#!/bin/bash

# Swagger Documentation Verification Script
# This script verifies that the Swagger documentation is properly generated and accessible

echo "🔍 Verifying Swagger Documentation for Promotion Web API..."
echo "=================================================="

# Check if swag tool is installed
echo "1. Checking swag tool installation..."
if command -v ~/go/bin/swag &> /dev/null; then
    echo "✅ swag tool is installed"
    ~/go/bin/swag --version
else
    echo "❌ swag tool not found. Installing..."
    go install github.com/swaggo/swag/cmd/swag@latest
    echo "✅ swag tool installed"
fi

echo ""

# Check if documentation files exist
echo "2. Checking documentation files..."
if [ -f "docs/swagger.yaml" ]; then
    echo "✅ swagger.yaml exists"
    echo "   Size: $(wc -l < docs/swagger.yaml) lines"
else
    echo "❌ swagger.yaml not found"
fi

if [ -f "docs/swagger.json" ]; then
    echo "✅ swagger.json exists"
    echo "   Size: $(wc -l < docs/swagger.json) lines"
else
    echo "❌ swagger.json not found"
fi

if [ -f "docs/docs.go" ]; then
    echo "✅ docs.go exists"
    echo "   Size: $(wc -l < docs/docs.go) lines"
else
    echo "❌ docs.go not found"
fi

echo ""

# Check for promotion web endpoints in documentation
echo "3. Verifying promotion web endpoints..."
if grep -q "promotion-web" docs/swagger.yaml; then
    echo "✅ Promotion web endpoints found in documentation"
    echo "   Endpoints found:"
    grep -o "/promotion-web[^:]*" docs/swagger.yaml | sort | uniq | head -10
else
    echo "❌ Promotion web endpoints not found in documentation"
fi

echo ""

# Check for promotion web models
echo "4. Verifying promotion web models..."
if grep -q "promotion_web\." docs/swagger.yaml; then
    echo "✅ Promotion web models found in documentation"
    echo "   Models found:"
    grep -o "promotion_web\.[A-Za-z]*" docs/swagger.yaml | sort | uniq | head -10
else
    echo "❌ Promotion web models not found in documentation"
fi

echo ""

# Check for authentication configuration
echo "5. Verifying authentication configuration..."
if grep -q "BearerAuth" docs/swagger.yaml; then
    echo "✅ Bearer authentication configured"
else
    echo "❌ Bearer authentication not configured"
fi

echo ""

# Check for tags
echo "6. Verifying endpoint tags..."
if grep -q "Admin - Promotion Management" docs/swagger.yaml; then
    echo "✅ Admin promotion management tags found"
else
    echo "❌ Admin promotion management tags not found"
fi

if grep -q "User - Promotion Collection" docs/swagger.yaml; then
    echo "✅ User promotion collection tags found"
else
    echo "❌ User promotion collection tags not found"
fi

if grep -q "Public - Promotions" docs/swagger.yaml; then
    echo "✅ Public promotion tags found"
else
    echo "❌ Public promotion tags not found"
fi

echo ""

# Regenerate documentation to ensure it's up to date
echo "7. Regenerating documentation..."
~/go/bin/swag init -g cmd/server/main.go -o docs
if [ $? -eq 0 ]; then
    echo "✅ Documentation regenerated successfully"
else
    echo "❌ Documentation regeneration failed"
fi

echo ""

# Validate JSON format
echo "8. Validating JSON format..."
if python3 -m json.tool docs/swagger.json > /dev/null 2>&1; then
    echo "✅ swagger.json is valid JSON"
else
    echo "❌ swagger.json is invalid JSON"
fi

echo ""

# Count endpoints and models
echo "9. Documentation statistics..."
TOTAL_PATHS=$(grep -c "^  /" docs/swagger.yaml)
PROMOTION_PATHS=$(grep -c "/promotion-web" docs/swagger.yaml)
TOTAL_DEFINITIONS=$(grep -c "^  [a-zA-Z].*:$" docs/swagger.yaml | head -1)

echo "   Total API paths: $TOTAL_PATHS"
echo "   Promotion web paths: $PROMOTION_PATHS"
echo "   Total model definitions: $TOTAL_DEFINITIONS"

echo ""

# Final verification
echo "10. Final verification..."
echo "=================================================="

if [ -f "docs/swagger.yaml" ] && [ -f "docs/swagger.json" ] && [ -f "docs/docs.go" ]; then
    if grep -q "promotion-web" docs/swagger.yaml && grep -q "BearerAuth" docs/swagger.yaml; then
        echo "🎉 SUCCESS: Swagger documentation is complete and ready!"
        echo ""
        echo "📋 Access Information:"
        echo "   Swagger UI: http://localhost:8080/swagger-ui/index.html"
        echo "   YAML Doc:   docs/swagger.yaml"
        echo "   JSON Doc:   docs/swagger.json"
        echo ""
        echo "🔧 Features Available:"
        echo "   ✅ Interactive API testing"
        echo "   ✅ JWT Bearer authentication"
        echo "   ✅ Request/response examples"
        echo "   ✅ Model schema definitions"
        echo "   ✅ Endpoint categorization"
        echo "   ✅ Complete promotion web coverage"
        echo ""
        echo "🚀 Ready for production use!"
    else
        echo "⚠️  WARNING: Documentation generated but promotion web content incomplete"
    fi
else
    echo "❌ FAILED: Documentation files missing or incomplete"
fi

echo ""
echo "Verification complete."
