#!/bin/sh

set -e

echo "==================================="
echo "🚀 Starting Blacking API Server"
echo "==================================="

# Default environment if not set
ENV=${1:-development}

echo "📝 Environment: $ENV"
echo "🗄️  Database Schema: ${DATABASE_SCHEMA:-public}"

# Wait for database to be ready (optional)
if [ -n "$DATABASE_HOST" ] && [ -n "$DATABASE_PORT" ]; then
    echo "⏳ Waiting for database connection..."
    
    # Simple connection test with timeout
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if timeout 5 /app/migrate $ENV validate 2>/dev/null; then
            echo "✅ Database connection successful"
            break
        fi
        
        echo "   Attempt $attempt/$max_attempts - waiting for database..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        echo "❌ Failed to connect to database after $max_attempts attempts"
        echo "   Please check database configuration and connectivity"
        exit 1
    fi
fi

# Run database migrations
echo "🔄 Running database migrations..."
if /app/migrate $ENV up; then
    echo "✅ Database migrations completed successfully"
else
    echo "❌ Database migrations failed"
    exit 1
fi

# Run database seeders (enabled by default, can be disabled with ENABLE_SEEDERS=false)
if [ "$ENABLE_SEEDERS" != "false" ]; then
    echo "🌱 Running database seeders..."
    if /app/migrate $ENV seed; then
        echo "✅ Database seeders completed successfully"
    else
        echo "⚠️  Database seeders failed (continuing anyway)"
    fi
else
    echo "⏭️  Skipping database seeders (ENABLE_SEEDERS=false)"
fi

# Start the main application
echo "🌟 Starting main application..."
echo "==================================="

exec /app/main -env $ENV