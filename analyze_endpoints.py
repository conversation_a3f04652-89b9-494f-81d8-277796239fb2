#!/usr/bin/env python3
"""
Script to analyze API endpoints from router files and compare with Swagger documentation.
"""
import os
import re
import json
import sys
from collections import defaultdict

def extract_endpoints_from_go_files():
    """Extract all API endpoints from Go router files"""
    router_dir = "internal/router"
    endpoints = defaultdict(list)
    
    # Pattern to match router method calls
    # Matches: router.GET("/path", handler) or group.POST("/path", handler)
    pattern = re.compile(r'(\w+)\.(GET|POST|PUT|DELETE|PATCH)\("([^"]+)"')
    
    for filename in os.listdir(router_dir):
        if filename.endswith('.go') and filename != 'router.go':
            filepath = os.path.join(router_dir, filename)
            category = filename.replace('.go', '').replace('_', '-')
            
            with open(filepath, 'r') as f:
                content = f.read()
                
            # Find all matches
            matches = pattern.findall(content)
            
            for group_name, method, path in matches:
                # <PERSON><PERSON> commented lines
                if '//' in path:
                    continue
                    
                # Build full endpoint
                endpoint = f"{method} {path}"
                endpoints[category].append(endpoint)
    
    return endpoints

def load_swagger_endpoints():
    """Load endpoints from Swagger JSON documentation"""
    swagger_file = "docs/swagger.json"
    
    if not os.path.exists(swagger_file):
        print(f"Swagger file not found: {swagger_file}")
        return set()
    
    with open(swagger_file, 'r') as f:
        swagger_data = json.load(f)
    
    swagger_endpoints = set()
    
    # Extract all paths and methods from swagger
    for path, methods in swagger_data.get('paths', {}).items():
        for method in methods.keys():
            if method.upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
                # Normalize the path - add /api/v1 prefix if not present and not a root path
                normalized_path = path
                if not path.startswith('/api/') and not path.startswith('/health') and not path.startswith('/auth') and not path.startswith('/system'):
                    normalized_path = f"/api/v1{path}"
                
                endpoint = f"{method.upper()} {normalized_path}"
                swagger_endpoints.add(endpoint)
    
    return swagger_endpoints

def normalize_endpoint(endpoint, router_file):
    """Normalize endpoint paths for comparison"""
    method, path = endpoint.split(' ', 1)
    
    # Handle common router patterns
    if router_file == 'member-auth':
        if not path.startswith('/api/'):
            path = f"/api/v1/member-auth{path}"
    elif router_file == 'auth-2fa':
        if not path.startswith('/api/') and not path.startswith('/auth/'):
            path = f"/api/v1/auth/2fa{path}"
    elif router_file in ['user', 'member']:
        if not path.startswith('/api/'):
            path = f"/api/v1{path}"
    elif router_file == 'system':
        if not path.startswith('/api/') and not path.startswith('/system'):
            if path.startswith('/allowed-ips'):
                path = f"/api/v1{path}"
            elif path.startswith('/system'):
                pass  # Keep as is
            else:
                path = f"/api/v1/system{path}"
    elif router_file == 'jaijaipay':
        if not path.startswith('/api/'):
            path = f"/api/v1/jaijai{path}"
    elif router_file == 'crypto-deposit':
        if not path.startswith('/api/'):
            path = f"/api/v1/payment-gateway/crypto{path}"
    elif router_file == 'payment-gateway':
        if not path.startswith('/api/'):
            path = f"/api/v1/payment-gateway{path}"
    elif router_file not in ['router']:
        if not path.startswith('/api/') and not path.startswith('/health') and not path.startswith('/auth') and not path.startswith('/system'):
            path = f"/api/v1{path}"
    
    return f"{method} {path}"

def main():
    print("🔍 Analyzing API endpoints from router files vs Swagger documentation...")
    print("=" * 80)
    
    # Extract endpoints from Go files
    router_endpoints = extract_endpoints_from_go_files()
    
    # Load Swagger endpoints
    swagger_endpoints = load_swagger_endpoints()
    
    # Normalize and collect all endpoints from routers
    all_router_endpoints = set()
    router_endpoint_map = {}
    
    for router_file, endpoints in router_endpoints.items():
        for endpoint in endpoints:
            normalized = normalize_endpoint(endpoint, router_file)
            all_router_endpoints.add(normalized)
            router_endpoint_map[normalized] = (router_file, endpoint)
    
    # Find missing endpoints
    missing_endpoints = all_router_endpoints - swagger_endpoints
    
    # Organize missing endpoints by router file
    missing_by_router = defaultdict(list)
    for endpoint in missing_endpoints:
        if endpoint in router_endpoint_map:
            router_file, original = router_endpoint_map[endpoint]
            missing_by_router[router_file].append((endpoint, original))
    
    # Print summary
    print(f"📊 SUMMARY:")
    print(f"   Total endpoints in router files: {len(all_router_endpoints)}")
    print(f"   Total endpoints in Swagger: {len(swagger_endpoints)}")
    print(f"   Missing from Swagger: {len(missing_endpoints)}")
    print()
    
    # Print missing endpoints by category
    print("🚨 MISSING ENDPOINTS FROM SWAGGER DOCUMENTATION:")
    print("=" * 80)
    
    total_missing = 0
    for router_file in sorted(missing_by_router.keys()):
        endpoints = missing_by_router[router_file]
        if endpoints:
            print(f"\n📁 {router_file.upper()} ({len(endpoints)} missing):")
            print("-" * 50)
            for normalized, original in sorted(endpoints):
                print(f"   {normalized}")
                total_missing += 1
    
    if total_missing == 0:
        print("✅ All endpoints are documented in Swagger!")
    
    print(f"\n📈 TOTAL MISSING: {total_missing} endpoints")

if __name__ == "__main__":
    main()