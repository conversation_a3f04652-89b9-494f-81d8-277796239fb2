package http

import (
	"net/http"
	"strconv"
	"time"

	"blacking-api/internal/domain/member_audit_log"
	"blacking-api/internal/service"

	"github.com/gin-gonic/gin"
)

type MemberAuditLogHandler struct {
	memberAuditLogService service.MemberAuditLogService
}

func NewMemberAuditLogHandler(memberAuditLogService service.MemberAuditLogService) *MemberAuditLogHandler {
	return &MemberAuditLogHandler{
		memberAuditLogService: memberAuditLogService,
	}
}

func (h *MemberAuditLogHandler) ListMemberAuditLogs(c *gin.Context) {
	// Parse query parameters
	limitStr := c.<PERSON>("limit", "10")
	offsetStr := c.<PERSON>("offset", "0")

	// Parse filter parameters
	username := c.Query("username")
	action := c.Query("action")
	phone := c.Query("phone")
	fullname := c.Query("fullname")
	dateFrom := c.Query("date_from")
	dateTo := c.Query("date_to")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100 // Max limit
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// Set default date to today if no date filter is provided
	if dateFrom == "" && dateTo == "" {
		today := time.Now().Format("2006-01-02")
		dateFrom = today
		dateTo = today
	}

	// Build filter
	filter := &member_audit_log.MemberAuditLogFilter{
		Username: username,
		Action:   action,
		Phone:    phone,
		FullName: fullname,
	}

	if dateFrom != "" {
		filter.DateFrom = &dateFrom
	}
	if dateTo != "" {
		filter.DateTo = &dateTo
	}

	// Get audit logs
	auditLogs, err := h.memberAuditLogService.ListAuditLogs(c.Request.Context(), limit, offset, filter)
	if err != nil {
		c.Error(err)
		return
	}

	// Get total count
	total, err := h.memberAuditLogService.GetAuditLogsCount(c.Request.Context(), filter)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"audit_logs": auditLogs,
			"total":      total,
			"limit":      limit,
			"offset":     offset,
		},
	})
}
