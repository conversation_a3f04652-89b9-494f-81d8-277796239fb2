package http

import (
	"net/http"

	"blacking-api/internal/domain/refresh_token"
	"blacking-api/internal/service"

	jwt "github.com/appleboy/gin-jwt/v2"
	"github.com/gin-gonic/gin"
)

type RefreshTokenHandler struct {
	refreshTokenService service.RefreshTokenService
	userJwtMiddleware   *jwt.GinJWTMiddleware
	memberJwtMiddleware *jwt.GinJWTMiddleware
}

func NewRefreshTokenHandler(refreshTokenService service.RefreshTokenService, userJwtMiddleware *jwt.GinJWTMiddleware, memberJwtMiddleware *jwt.GinJWTMiddleware) *RefreshTokenHandler {
	return &RefreshTokenHandler{
		refreshTokenService: refreshTokenService,
		userJwtMiddleware:   userJwtMiddleware,
		memberJwtMiddleware: memberJwtMiddleware,
	}
}

type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
	UserType     string `json:"user_type" binding:"required,oneof=admin member"`
}

type RefreshTokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int    `json:"expires_in"` // seconds
	TokenType    string `json:"token_type"`
}

// RefreshToken handles token refresh for both admin and member
// @Summary Refresh authentication token
// @Description Refresh expired JWT access token using refresh token for both admin and member users
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body RefreshTokenRequest true "Refresh token request"
// @Success 200 {object} object{status=string,data=RefreshTokenResponse} "Token refreshed successfully"
// @Failure 400 {object} object{status=string,message=string,error=string} "Bad request"
// @Failure 401 {object} object{status=string,message=string} "Unauthorized"
// @Failure 500 {object} object{status=string,message=string} "Internal server error"
// @Router /api/v1/member-auth/refresh-token [post]
func (h *RefreshTokenHandler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  "error",
			"message": "Invalid request format",
			"error":   err.Error(),
		})
		return
	}

	// Validate refresh token
	rt, err := h.refreshTokenService.ValidateRefreshToken(c.Request.Context(), req.RefreshToken)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"status":  "error",
			"message": "Invalid or expired refresh token",
		})
		return
	}

	// Check if user type matches
	expectedUserType := refresh_token.UserType(req.UserType)
	if rt.UserType != expectedUserType {
		c.JSON(http.StatusUnauthorized, gin.H{
			"status":  "error",
			"message": "Invalid user type for refresh token",
		})
		return
	}

	// Generate new access token based on user type
	var newAccessToken string
	var expiresIn int

	if rt.UserType == refresh_token.UserTypeAdmin {
		// Generate admin access token
		newAccessToken, err = h.generateAdminAccessToken(c, rt)
		expiresIn = 15 * 60 // 15 minutes
	} else {
		// Generate member access token
		newAccessToken, err = h.generateMemberAccessToken(c, rt)
		expiresIn = 15 * 60 // 15 minutes
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status":  "error",
			"message": "Failed to generate access token",
		})
		return
	}

	// Create new refresh token (rotate refresh token for better security)
	var newRefreshTokenResp *refresh_token.RefreshTokenResponse
	if rt.UserType == refresh_token.UserTypeAdmin {
		newRefreshTokenResp, err = h.refreshTokenService.CreateRefreshToken(c.Request.Context(), *rt.UserID, rt.UserType)
	} else {
		newRefreshTokenResp, err = h.refreshTokenService.CreateRefreshToken(c.Request.Context(), *rt.MemberID, rt.UserType)
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status":  "error",
			"message": "Failed to create new refresh token",
		})
		return
	}

	// Invalidate old refresh token
	h.refreshTokenService.InvalidateRefreshToken(c.Request.Context(), req.RefreshToken)

	// Return response
	c.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": RefreshTokenResponse{
			AccessToken:  newAccessToken,
			RefreshToken: newRefreshTokenResp.Token,
			ExpiresIn:    expiresIn,
			TokenType:    "Bearer",
		},
	})
}

func (h *RefreshTokenHandler) generateAdminAccessToken(c *gin.Context, rt *refresh_token.RefreshToken) (string, error) {
	// Create temporary context for JWT generation
	claims := gin.H{
		"id":       *rt.UserID,
		"username": "admin", // You might want to fetch actual username from database
	}

	token, _, err := h.userJwtMiddleware.TokenGenerator(claims)
	return token, err
}

func (h *RefreshTokenHandler) generateMemberAccessToken(c *gin.Context, rt *refresh_token.RefreshToken) (string, error) {
	// Create temporary context for JWT generation
	claims := gin.H{
		"id":       *rt.MemberID,
		"username": "member", // You might want to fetch actual username from database
	}

	token, _, err := h.memberJwtMiddleware.TokenGenerator(claims)
	return token, err
}

// InvalidateAllTokens handles logout by invalidating all refresh tokens for a user
// @Summary Invalidate all tokens (logout)
// @Description Invalidate all refresh tokens for the authenticated user, effectively logging them out from all devices
// @Tags Authentication
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} object{status=string,message=string} "All tokens invalidated successfully"
// @Failure 401 {object} object{status=string,message=string} "Unauthorized"
// @Failure 500 {object} object{status=string,message=string} "Internal server error"
// @Router /api/v1/member-auth/invalidate-tokens [post]
func (h *RefreshTokenHandler) InvalidateAllTokens(c *gin.Context) {
	// Extract JWT claims to get user info
	claims := jwt.ExtractClaims(c)
	if claims == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"status":  "error",
			"message": "Authentication required",
		})
		return
	}

	// Get user ID from claims
	userIDFloat, exists := claims["id"].(float64)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"status":  "error",
			"message": "Invalid token claims",
		})
		return
	}

	userID := int(userIDFloat)

	// Determine user type - this is a simplified approach
	// In a real implementation, you might want to store user type in JWT claims
	// or determine it from the middleware used
	var userType refresh_token.UserType
	if c.Request.URL.Path == "/api/v1/member-auth/invalidate-tokens" {
		userType = refresh_token.UserTypeMember
	} else {
		userType = refresh_token.UserTypeAdmin
	}

	// Invalidate all refresh tokens for this user
	err := h.refreshTokenService.InvalidateAllUserTokens(c.Request.Context(), userID, userType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status":  "error",
			"message": "Failed to invalidate tokens",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "All tokens invalidated successfully",
	})
}
