package http

import (
	"blacking-api/internal/domain/banking"
	"blacking-api/internal/service"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"net/http"
)

type BankingHandler struct {
	bankingService service.BankingService
	logger         logger.Logger
}

func NewBankingHandler(bankingService service.BankingService, logger logger.Logger) *BankingHandler {
	return &BankingHandler{
		bankingService: bankingService,
		logger:         logger,
	}
}

// ListBankings lists banking accounts with search functionality
// @Summary List banking accounts
// @Description Get a list of banking accounts with optional search and pagination
// @Tags Banking Management
// @Produce json
// @Security BearerAuth
// @Param search query string false "Search term for bank name or code"
// @Param limit query int false "Limit" default(50)
// @Param offset query int false "Offset" default(0)
// @Success 200 {object} object{success=bool,data=[]object} "Banking accounts retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /banking [get]
func (h *BankingHandler) ListBankings(c *gin.Context) {
	var req banking.BankingSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	bankings, err := h.bankingService.ListBankings(c.Request.Context(), &req)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    bankings,
	})
}
