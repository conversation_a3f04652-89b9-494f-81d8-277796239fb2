package http

import (
	"net/http"
	"strconv"

	"blacking-api/internal/domain/member"
	"blacking-api/internal/service"
	"blacking-api/pkg/auth"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

// PartnerHandler handles partner-related HTTP requests
type PartnerHandler struct {
	partnerService service.PartnerService
	logger         logger.Logger
}

// NewPartnerHandler creates a new partner handler
func NewPartnerHandler(partnerService service.PartnerService, logger logger.Logger) *PartnerHandler {
	return &PartnerHandler{
		partnerService: partnerService,
		logger:         logger,
	}
}

// CreatePartner handles POST /api/v1/partners
func (h *PartnerHandler) CreatePartner(c *gin.Context) {
	var req member.CreatePartnerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	// Get admin ID and client IP
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)
	clientIP := c.ClientIP()

	partnerResp, err := h.partnerService.CreatePartner(c.Request.Context(), req, clientIP, adminId, adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    partnerResp,
	})
}

// GetPartnerByID handles GET /api/v1/partners/:id
func (h *PartnerHandler) GetPartnerByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("partner ID is required"))
		return
	}

	partner, err := h.partnerService.GetPartnerByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    partner,
	})
}

// ListPartners handles GET /api/v1/partners
func (h *PartnerHandler) ListPartners(c *gin.Context) {
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")
	search := c.Query("search") // Search by first_name

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100 // Max limit
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	partners, total, err := h.partnerService.ListPartners(c.Request.Context(), limit, offset, search)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"partners": partners,
			"pagination": gin.H{
				"total":  total,
				"limit":  limit,
				"offset": offset,
			},
		},
	})
}

// ListPartnersForDropdown handles GET /api/v1/partners/dropdown
// @Summary Get partners dropdown
// @Description Get list of active partners for dropdown/select options with page-specific logic
// @Tags Partners
// @Produce json
// @Security BearerAuth
// @Param page query string false "Page name (e.g., customer-followup) - adds special ID=0 option for customer-followup"
// @Success 200 {object} object{success=bool,data=object{partners=[]member.PartnerDropdownResponse}} "Partners dropdown retrieved successfully"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/partners/dropdown [get]
func (h *PartnerHandler) ListPartnersForDropdown(c *gin.Context) {
	page := c.Query("page")

	var partners []*member.PartnerDropdownResponse
	var err error

	if page != "" {
		// Use page-specific logic
		partners, err = h.partnerService.ListPartnersForDropdownByPage(c.Request.Context(), page)
	} else {
		// Use default logic
		partners, err = h.partnerService.ListPartnersForDropdown(c.Request.Context())
	}

	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"partners": partners,
		},
	})
}

// UpdatePartner handles PUT /api/v1/partners/:id
func (h *PartnerHandler) UpdatePartner(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("partner ID is required"))
		return
	}

	var req member.UpdatePartnerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	partnerResp, err := h.partnerService.UpdatePartner(c.Request.Context(), id, req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    partnerResp,
	})
}

// ChangePartnerPassword handles PUT /api/v1/partners/:id/password
func (h *PartnerHandler) ChangePartnerPassword(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("partner ID is required"))
		return
	}

	var req member.ChangePartnerPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	err := h.partnerService.ChangePartnerPassword(c.Request.Context(), id, req, strconv.Itoa(adminId), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "partner password changed successfully",
	})
}

// SuspendPartner handles PATCH /api/v1/partners/:id/suspend
func (h *PartnerHandler) SuspendPartner(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("partner ID is required"))
		return
	}

	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	err := h.partnerService.SuspendPartner(c.Request.Context(), id, strconv.Itoa(adminId), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "partner suspended successfully",
	})
}

// DeletePartner handles POST /api/v1/partners/:id/delete
func (h *PartnerHandler) DeletePartner(c *gin.Context) {
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("partner ID is required"))
		return
	}
	var req member.DeletePartnerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}
	err := h.partnerService.DeletePartner(c.Request.Context(), id, req, strconv.Itoa(adminId), adminName)
	if err != nil {
		c.Error(err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Partner deleted successfully",
	})
}
