package http

import (
	"blacking-api/internal/domain/sms_provider"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"strconv"
)

type SMSProviderHandler struct {
	smsProviderService service.SMSProviderService
	logger             logger.Logger
}

func NewSMSProviderHandler(smsProviderService service.SMSProviderService, logger logger.Logger) *SMSProviderHandler {
	return &SMSProviderHandler{
		smsProviderService: smsProviderService,
		logger:             logger,
	}
}

// @Summary Create SMS provider
// @Description Create a new SMS provider for sending SMS messages
// @Tags Communication Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body object true "SMS provider creation request"
// @Success 201 {object} object{success=bool,message=string} "SMS provider created successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /sms-provider [post]
func (h *SMSProviderHandler) CreateSMSProvider(c *gin.Context) {
	var req *sms_provider.SMSProviderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.smsProviderService.CreateSMSProvider(c.Request.Context(), req); err != nil {
		h.logger.Error("Failed to create SMS provider", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(201, gin.H{
		"success": true,
		"message": "SMS provider created successfully",
	})
}

// @Summary List SMS providers
// @Description Get a list of SMS providers with optional search and pagination
// @Tags Communication Management
// @Produce json
// @Security BearerAuth
// @Param limit query int false "Limit"
// @Param offset query int false "Offset"
// @Param search query string false "Search term"
// @Success 200 {object} object{success=bool,data=[]object} "SMS providers retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /sms-provider [get]
func (h *SMSProviderHandler) GetSMSProviders(c *gin.Context) {
	var req *sms_provider.SMSProviderSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	providers, err := h.smsProviderService.FindAllSMSProviders(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("Failed to get SMS providers", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    providers,
	})
}

// @Summary Get SMS provider by ID
// @Description Get a specific SMS provider by its ID
// @Tags Communication Management
// @Produce json
// @Security BearerAuth
// @Param id path string true "SMS Provider ID"
// @Success 200 {object} object{success=bool,data=object} "SMS provider retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "SMS provider not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /sms-provider/{id} [get]
func (h *SMSProviderHandler) GetSMSProviderByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("SMS provider ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	provider, err := h.smsProviderService.FindSMSProviderByID(c.Request.Context(), idInt)
	if err != nil {
		h.logger.Error("Failed to get SMS provider by ID", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    provider,
	})
}

func (h *SMSProviderHandler) UpdateSMSProvider(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("SMS provider ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *sms_provider.SMSProviderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.smsProviderService.UpdateSMSProvider(c.Request.Context(), idInt, req); err != nil {
		h.logger.Error("Failed to update SMS provider", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "SMS provider updated successfully",
	})
}

func (h *SMSProviderHandler) UpdateSMSProviderStatus(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("SMS provider ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *sms_provider.UpdateStatusRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.smsProviderService.UpdateSMSProviderStatus(c.Request.Context(), idInt, req); err != nil {
		h.logger.Error("Failed to update SMS provider status", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "SMS provider status updated successfully",
	})
}

func (h *SMSProviderHandler) DeleteSMSProvider(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("SMS provider ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.smsProviderService.DeleteSMSProvider(c.Request.Context(), idInt); err != nil {
		h.logger.Error("Failed to delete SMS provider", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "SMS provider deleted successfully",
	})
}
