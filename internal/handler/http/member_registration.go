package http

import (
	"net/http"

	"blacking-api/internal/domain/member"
	"blacking-api/internal/domain/system_setting"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"

	"github.com/gin-gonic/gin"
)

type MemberRegistrationHandler struct {
	memberService        service.MemberService
	otpService           service.OTPService
	systemSettingService service.SystemSettingService
	referralService      service.ReferralService
}

func NewMemberRegistrationHandler(memberService service.MemberService, otpService service.OTPService, systemSettingService service.SystemSettingService, referralService service.ReferralService) *MemberRegistrationHandler {
	return &MemberRegistrationHandler{
		memberService:        memberService,
		otpService:           otpService,
		systemSettingService: systemSettingService,
		referralService:      referralService,
	}
}

// SendRegistrationOTP sends OTP for member registration
// @Summary Send registration OTP
// @Description Send OTP code to phone for member registration verification
// @Tags Member Registration
// @Accept json
// @Produce json
// @Param request body member.VerifySendOTPRequest true "OTP request"
// @Success 200 {object} object{success=bool,data=member.RegistrationOTPResponse,message=string} "OTP sent successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/member-auth/register/send-otp [post]
func (h *MemberRegistrationHandler) SendRegistrationOTP(c *gin.Context) {
	var req member.VerifySendOTPRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	// Check if OTP is required (mode != none)
	//otpOptionSetting, err := h.systemSettingService.GetSystemSetting(c.Request.Context(), system_setting.KeyOTPOption)
	//if err != nil {
	//	c.Error(err)
	//	return
	//}

	// Reject if mode is "none" - OTP not required
	//if otpOptionSetting.Value == "none" {
	//	c.Error(errors.NewValidationError("OTP verification not required, use direct registration instead"))
	//	return
	//}

	response, err := h.memberService.SendRegistrationOTP(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
		"message": "OTP sent successfully",
	})
}

// VerifyRegistrationOTP verifies OTP for member registration
// @Summary Verify registration OTP
// @Description Verify OTP code for member registration
// @Tags Member Registration
// @Accept json
// @Produce json
// @Param request body member.VerifyRegistrationOTPRequest true "OTP verification request"
// @Success 201 {object} object{success=bool,message=string} "OTP verified successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/member-auth/register/verify-otp [post]
func (h *MemberRegistrationHandler) VerifyRegistrationOTP(c *gin.Context) {
	var req member.VerifyRegistrationOTPRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	// Check if OTP is required (mode != none)
	otpOptionSetting, err := h.systemSettingService.GetSystemSetting(c.Request.Context(), system_setting.KeyOTPOption)
	if err != nil {
		c.Error(err)
		return
	}

	// Reject if mode is "none" - OTP not required
	if otpOptionSetting.Value == "none" {
		c.Error(errors.NewValidationError("OTP verification not required, use direct registration instead"))
		return
	}

	// Get client IP
	clientIP := c.ClientIP()

	_, err = h.memberService.VerifyRegistrationOTP(c.Request.Context(), req, clientIP)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "OTP verified successfully",
	})
}

// CompleteRegistration handles OTP-based registration completion (legacy endpoint - deprecated)
// @Summary Complete OTP-based registration (deprecated)
// @Description Complete member registration after OTP verification - use /register instead
// @Tags Member Registration
// @Accept json
// @Produce json
// @Param request body member.CompleteRegistrationRequest true "Registration request"
// @Success 201 {object} object{success=bool,data=member.MemberResponse,message=string} "Registration completed successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /member-auth/register/complete [post]
// @Deprecated
func (h *MemberRegistrationHandler) CompleteRegistration(c *gin.Context) {
	var req member.CompleteRegistrationRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	// Check if OTP flow is required (mode != none)
	otpOptionSetting, err := h.systemSettingService.GetSystemSetting(c.Request.Context(), system_setting.KeyOTPOption)
	if err != nil {
		c.Error(err)
		return
	}

	// Reject if mode is "none" - should use direct registration instead
	if otpOptionSetting.Value == "none" {
		c.Error(errors.NewValidationError("OTP verification not required, use direct registration instead"))
		return
	}

	// Get client IP
	clientIP := c.ClientIP()

	memberResp, err := h.memberService.CompleteRegistration(c.Request.Context(), req, clientIP)
	if err != nil {
		c.Error(err)
		return
	}

	// If member has a referrer, increment the referrer's downline count
	// We need to get the referrer ID from the created member response
	if memberResp != nil && memberResp.ReferUserID != nil && *memberResp.ReferUserID > 0 {
		referrerID := *memberResp.ReferUserID
		err = h.referralService.IncrementDownlineCount(c.Request.Context(), referrerID)
		if err != nil {
			// Log error but don't fail the registration
			_ = err // Log this in production
		}
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    memberResp,
		"message": "Registration completed successfully",
	})
}

// GetRegistrationMode gets current registration mode settings
// @Summary Get registration mode
// @Description Get current registration mode (OTP required or direct registration)
// @Tags Member Registration
// @Accept json
// @Produce json
// @Success 200 {object} object{success=bool,data=object{current_mode=string},message=string} "Registration mode retrieved successfully"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/member-auth/register/mode [get]
func (h *MemberRegistrationHandler) GetRegistrationMode(c *gin.Context) {
	// Get OTP option from system setting
	otpOptionSetting, err := h.systemSettingService.GetSystemSetting(c.Request.Context(), system_setting.KeyOTPOption)
	if err != nil {
		c.Error(err)
		return
	}

	// Add current selected mode
	responseData := gin.H{
		"current_mode": otpOptionSetting.Value,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    responseData,
		"message": "Registration mode retrieved successfully",
	})
}

// Register handles both direct and OTP-based registration based on system settings
// @Summary Register member
// @Description Register a new member account (supports both direct and OTP-based registration based on system settings)
// @Tags Member Registration
// @Accept json
// @Produce json
// @Param request body member.CompleteRegistrationRequest true "Registration request"
// @Success 201 {object} object{success=bool,data=member.MemberResponse,message=string} "Registration completed successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /member-auth/register [post]
func (h *MemberRegistrationHandler) Register(c *gin.Context) {
	var req member.CompleteRegistrationRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	// Get OTP option from system setting to determine registration flow
	otpOptionSetting, err := h.systemSettingService.GetSystemSetting(c.Request.Context(), system_setting.KeyOTPOption)
	if err != nil {
		c.Error(err)
		return
	}

	// Get client IP
	clientIP := c.ClientIP()

	var memberResp *member.MemberResponse

	// Handle registration based on OTP mode
	if otpOptionSetting.Value == "none" {
		// Direct registration (no OTP required)
		memberResp, err = h.memberService.DirectRegistration(c.Request.Context(), req, clientIP)
	} else {
		// OTP-based registration (OTP must be verified first)
		memberResp, err = h.memberService.CompleteRegistration(c.Request.Context(), req, clientIP)
	}

	if err != nil {
		c.Error(err)
		return
	}

	// If member has a referrer, increment the referrer's downline count
	// We need to get the referrer ID from the created member response
	if memberResp != nil && memberResp.ReferUserID != nil && *memberResp.ReferUserID > 0 {
		referrerID := *memberResp.ReferUserID
		err = h.referralService.IncrementDownlineCount(c.Request.Context(), referrerID)
		if err != nil {
			// Log error but don't fail the registration
			// The member is already created successfully
			// We can handle this in background job if needed
			// For now, just log the error
			_ = err // Log this in production
		}
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    memberResp,
		"message": "Registration completed successfully",
	})
}

// DirectRegistration handles direct registration (legacy endpoint - deprecated)
// @Summary Direct registration (deprecated)
// @Description Direct member registration without OTP - use /register instead
// @Tags Member Registration
// @Accept json
// @Produce json
// @Param request body member.CompleteRegistrationRequest true "Registration request"
// @Success 201 {object} object{success=bool,data=member.MemberResponse,message=string} "Registration completed successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /member-auth/register/direct [post]
// @Deprecated
func (h *MemberRegistrationHandler) DirectRegistration(c *gin.Context) {
	var req member.CompleteRegistrationRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	// Check if direct registration is allowed (mode = none)
	otpOptionSetting, err := h.systemSettingService.GetSystemSetting(c.Request.Context(), system_setting.KeyOTPOption)
	if err != nil {
		c.Error(err)
		return
	}

	// Check if current mode allows direct registration
	if otpOptionSetting.Value != "none" {
		c.Error(errors.NewValidationError("direct registration not allowed, OTP verification required"))
		return
	}

	// Get client IP
	clientIP := c.ClientIP()

	memberResp, err := h.memberService.DirectRegistration(c.Request.Context(), req, clientIP)
	if err != nil {
		c.Error(err)
		return
	}

	// If member has a referrer, increment the referrer's downline count
	// We need to get the referrer ID from the created member response
	if memberResp != nil && memberResp.ReferUserID != nil && *memberResp.ReferUserID > 0 {
		referrerID := *memberResp.ReferUserID
		err = h.referralService.IncrementDownlineCount(c.Request.Context(), referrerID)
		if err != nil {
			// Log error but don't fail the registration
			_ = err // Log this in production
		}
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    memberResp,
		"message": "Registration completed successfully",
	})
}
