package http

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"blacking-api/internal/config"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

type WebSocketTestHandler struct {
	logger logger.Logger
	config *config.Config
}

func NewWebSocketTestHandler(logger logger.Logger, cfg *config.Config) *WebSocketTestHandler {
	return &WebSocketTestHandler{
		logger: logger,
		config: cfg,
	}
}

// Simple WebSocket test that just tries to connect to Socket.IO
func (h *WebSocketTestHandler) TestWebSocketConnectionSimple(c *gin.Context) {
	action := c.Query("action")
	if action == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "action parameter is required"})
		return
	}

	// Get WebSocket URL from config and convert to Socket.IO format
	baseURL := h.config.WebSocket.URL
	websocketURL := baseURL + "/socket.io/?EIO=4&transport=websocket"

	h.logger.Infof("Connecting to Socket.IO server: %s", websocketURL)

	// Create dialer
	dialer := websocket.Dialer{
		HandshakeTimeout: 10 * time.Second,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: false,
		},
	}

	// Connect
	conn, _, err := dialer.Dial(websocketURL, nil)
	if err != nil {
		h.logger.WithError(err).Error("Failed to connect to Socket.IO server")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to connect to WebSocket server",
			"details": err.Error(),
		})
		return
	}
	defer conn.Close()

	// Prepare message based on action
	var event string
	var data interface{}

	switch action {
	case "join":
		channelID := c.Query("channel_id")
		if channelID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "channel_id is required for join action"})
			return
		}
		event = "join"
		data = map[string]interface{}{"channel_id": parseChannelID(channelID)}

	case "leave":
		channelID := c.Query("channel_id")
		if channelID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "channel_id is required for leave action"})
			return
		}
		event = "leave"
		data = map[string]interface{}{"channel_id": parseChannelID(channelID)}

	case "deposit":
		channelID := c.Query("channel_id")
		if channelID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "channel_id is required for deposit action"})
			return
		}
		status := c.DefaultQuery("status", "true") == "true"
		event = "deposit"
		data = map[string]interface{}{
			"channel_id": parseChannelID(channelID),
			"status":     status,
		}

	case "withdraw":
		channelID := c.Query("channel_id")
		if channelID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "channel_id is required for withdraw action"})
			return
		}
		status := c.DefaultQuery("status", "true") == "true"
		event = "withdraw"
		data = map[string]interface{}{
			"channel_id": parseChannelID(channelID),
			"status":     status,
		}

	case "ping":
		event = "ping"
		data = map[string]interface{}{}

	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"error":             "Invalid action",
			"supported_actions": []string{"join", "leave", "deposit", "withdraw", "ping"},
		})
		return
	}

	// Simple test: try to read handshake and send message
	conn.SetReadDeadline(time.Now().Add(10 * time.Second))

	// Read handshake messages
	responses := []string{}
	for i := 0; i < 3; i++ {
		_, messageBytes, err := conn.ReadMessage()
		if err != nil {
			break
		}
		messageStr := string(messageBytes)
		responses = append(responses, messageStr)
		h.logger.Infof("Socket.IO message %d: %s", i+1, messageStr)

		// If we get "0" (connect), send "40" (namespace connect)
		if messageStr == "0" {
			conn.WriteMessage(websocket.TextMessage, []byte("40"))
		}
		// If we get "40" (namespace connected), send our event
		if messageStr == "40" {
			socketIOData := []interface{}{event, data}
			dataJSON, _ := json.Marshal(socketIOData)
			socketIOMessage := "42" + string(dataJSON)
			h.logger.Infof("Sending: %s", socketIOMessage)
			conn.WriteMessage(websocket.TextMessage, []byte(socketIOMessage))
		}
	}

	// Return success with debug info
	c.JSON(http.StatusOK, gin.H{
		"status":        "success",
		"action":        action,
		"websocket_url": websocketURL,
		"sent_event":    event,
		"sent_data":     data,
		"responses":     responses,
		"message":       "Socket.IO connection test completed",
	})
}

func (h *WebSocketTestHandler) GetWebSocketInfo(c *gin.Context) {
	websocketURL := h.config.WebSocket.URL

	info := gin.H{
		"websocket_info": gin.H{
			"websocket_url": websocketURL,
			"emit_events": gin.H{
				"join": gin.H{
					"description": "Join a channel",
					"data_format": gin.H{
						"channel_id": "integer",
					},
				},
				"leave": gin.H{
					"description": "Leave a channel",
					"data_format": gin.H{
						"channel_id": "integer",
					},
				},
				"deposit": gin.H{
					"description": "Send deposit transaction status",
					"data_format": gin.H{
						"channel_id": "integer",
						"status":     "boolean",
					},
				},
				"withdraw": gin.H{
					"description": "Send withdraw transaction status",
					"data_format": gin.H{
						"channel_id": "integer",
						"status":     "boolean",
					},
				},
			},
			"listen_events": gin.H{
				"user_joined":       gin.H{"description": "Notification when user joins channel"},
				"user_left":         gin.H{"description": "Notification when user leaves channel"},
				"deposit_response":  gin.H{"description": "Response to deposit transaction"},
				"withdraw_response": gin.H{"description": "Response to withdraw transaction"},
			},
		},
	}

	c.JSON(http.StatusOK, info)
}

func parseChannelID(channelIDStr string) int {
	var channelID int
	fmt.Sscanf(channelIDStr, "%d", &channelID)
	return channelID
}
