package http

import (
	"encoding/json"
	"io"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"blacking-api/internal/service"
	"blacking-api/pkg/jaijaipay"
	"blacking-api/pkg/logger"
)

// JaiJaiPayHandler handles HTTP requests for JaiJaiPay API operations
type JaiJaiPayHandler struct {
	service *service.JaiJaiPayService
	logger  logger.Logger
}

// NewJaiJaiPayHandler creates a new JaiJaiPay handler instance
func NewJaiJaiPayHandler(service *service.JaiJaiPayService, logger logger.Logger) *JaiJaiPayHandler {
	return &JaiJaiPayHandler{
		service: service,
		logger:  logger,
	}
}

// Deposits Handlers

// CreateDeposit handles POST /api/jaijaipay/deposits
// @Summary Create JaiJaiPay deposit
// @Description Create a new deposit transaction through JaiJaiPay payment gateway
// @Tags JaiJaiPay
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body jaijaipay.CreateDepositRequest true "Create deposit request"
// @Success 201 {object} object{id=int} "Deposit created successfully"
// @Failure 400 {object} object{error=string,message=string} "Bad request"
// @Failure 500 {object} object{error=string,message=string} "Internal server error"
// @Router /api/v1/jaijai/deposits [post]
func (h *JaiJaiPayHandler) CreateDeposit(c *gin.Context) {
	var req jaijaipay.CreateDepositRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("invalid request body for create deposit")
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"message": err.Error(),
		})
		return
	}

	result, err := h.service.CreateDeposit(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("failed to create deposit")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create deposit",
			"message": err.Error(),
		})
		return
	}

	// Return database ID instead of transaction_id
	c.JSON(http.StatusCreated, gin.H{
		"id": result.ID,
	})
}

// ListDeposits handles GET /api/jaijaipay/deposits
// @Summary List JaiJaiPay deposits
// @Description Get a paginated list of JaiJaiPay deposit transactions with optional filters
// @Tags JaiJaiPay
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param status query string false "Filter by status"
// @Param startDate query string false "Start date filter (YYYY-MM-DD)"
// @Param endDate query string false "End date filter (YYYY-MM-DD)"
// @Param currency query string false "Filter by currency"
// @Param orderId query string false "Filter by order ID"
// @Param customerReferenceId query string false "Filter by customer reference ID"
// @Success 200 {object} object{data=[]object,metadata=object} "Deposits retrieved successfully"
// @Failure 500 {object} object{error=string,message=string} "Internal server error"
// @Router /api/v1/jaijai/deposits [get]
func (h *JaiJaiPayHandler) ListDeposits(c *gin.Context) {
	req := &jaijaipay.ListDepositsRequest{}

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}
	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			req.Limit = l
		}
	}
	req.Status = c.Query("status")
	req.StartDate = c.Query("startDate")
	req.EndDate = c.Query("endDate")
	req.Currency = c.Query("currency")
	req.OrderID = c.Query("orderId")
	req.CustomerReferenceID = c.Query("customerReferenceId")

	result, err := h.service.ListDeposits(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("failed to list deposits")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to list deposits",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetDepositByID handles GET /api/jaijaipay/deposits/:transactionId
// @Summary Get JaiJaiPay deposit by ID
// @Description Get a specific JaiJaiPay deposit transaction by transaction ID
// @Tags JaiJaiPay
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param transactionId path string true "Transaction ID"
// @Success 200 {object} object{transaction_id=string,amount=number,status=string} "Deposit retrieved successfully"
// @Failure 400 {object} object{error=string} "Bad request"
// @Failure 404 {object} object{error=string,message=string} "Deposit not found"
// @Failure 500 {object} object{error=string,message=string} "Internal server error"
// @Router /api/v1/jaijai/deposits/{transactionId} [get]
func (h *JaiJaiPayHandler) GetDepositByID(c *gin.Context) {
	transactionID := c.Param("transactionId")
	if transactionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Transaction ID is required",
		})
		return
	}

	result, err := h.service.GetDepositByID(c.Request.Context(), transactionID)
	if err != nil {
		h.logger.WithError(err).Error("failed to get deposit by ID")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get deposit",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// CancelDeposit handles POST /api/jaijaipay/deposits/cancel
// @Summary Cancel JaiJaiPay deposit
// @Description Cancel a JaiJaiPay deposit transaction
// @Tags JaiJaiPay
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body jaijaipay.CancelDepositRequest true "Cancel deposit request"
// @Success 200 {object} object{transaction_id=string,status=string} "Deposit cancelled successfully"
// @Failure 400 {object} object{error=string,message=string} "Bad request"
// @Failure 500 {object} object{error=string,message=string} "Internal server error"
// @Router /api/v1/jaijai/deposits/cancel [post]
func (h *JaiJaiPayHandler) CancelDeposit(c *gin.Context) {
	var req jaijaipay.CancelDepositRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("invalid request body for cancel deposit")
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"message": err.Error(),
		})
		return
	}

	result, err := h.service.CancelDeposit(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("failed to cancel deposit")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to cancel deposit",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// Withdrawals Handlers

// CreateWithdrawal handles POST /api/jaijaipay/withdrawals
// @Summary Create JaiJaiPay withdrawal
// @Description Create a new withdrawal transaction through JaiJaiPay payment gateway
// @Tags JaiJaiPay
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body jaijaipay.CreateWithdrawalRequest true "Create withdrawal request"
// @Success 201 {object} object{transaction_id=string,amount=number} "Withdrawal created successfully"
// @Failure 400 {object} object{error=string,message=string} "Bad request"
// @Failure 500 {object} object{error=string,message=string} "Internal server error"
// @Router /api/v1/jaijai/withdrawals [post]
func (h *JaiJaiPayHandler) CreateWithdrawal(c *gin.Context) {
	var req jaijaipay.CreateWithdrawalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("invalid request body for create withdrawal")
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"message": err.Error(),
		})
		return
	}

	result, err := h.service.CreateWithdrawal(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("failed to create withdrawal")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create withdrawal",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, result)
}

// ListWithdrawals handles GET /api/jaijaipay/withdrawals
func (h *JaiJaiPayHandler) ListWithdrawals(c *gin.Context) {
	req := &jaijaipay.ListWithdrawalsRequest{}

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}
	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			req.Limit = l
		}
	}
	req.Status = c.Query("status")
	req.StartDate = c.Query("startDate")
	req.EndDate = c.Query("endDate")
	req.Currency = c.Query("currency")
	req.CustomerReferenceID = c.Query("customerReferenceId")

	result, err := h.service.ListWithdrawals(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("failed to list withdrawals")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to list withdrawals",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetWithdrawalByID handles GET /api/jaijaipay/withdrawals/:transactionId
func (h *JaiJaiPayHandler) GetWithdrawalByID(c *gin.Context) {
	transactionID := c.Param("transactionId")
	if transactionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Transaction ID is required",
		})
		return
	}

	result, err := h.service.GetWithdrawalByID(c.Request.Context(), transactionID)
	if err != nil {
		h.logger.WithError(err).Error("failed to get withdrawal by ID")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get withdrawal",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// Balance Handlers

// GetBalance handles GET /api/jaijaipay/balance
func (h *JaiJaiPayHandler) GetBalance(c *gin.Context) {
	result, err := h.service.GetBalance(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("failed to get balance")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get balance",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// Fees Handlers

// GetFeePreview handles GET /api/jaijaipay/fees/preview
func (h *JaiJaiPayHandler) GetFeePreview(c *gin.Context) {
	req := &jaijaipay.FeePreviewRequest{}

	// Parse query parameters
	if amount := c.Query("amount"); amount != "" {
		if a, err := strconv.ParseFloat(amount, 64); err == nil {
			req.Amount = a
		}
	}
	req.TransactionType = c.Query("transactionType")
	req.Currency = c.Query("currency")

	// Validate required parameters
	if req.Amount <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Amount is required and must be greater than 0",
		})
		return
	}
	if req.TransactionType == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Transaction type is required (DEPOSIT or WITHDRAWAL)",
		})
		return
	}

	result, err := h.service.GetFeePreview(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("failed to get fee preview")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get fee preview",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// Analytics Handlers

// GetTransactionAnalytics handles GET /api/jaijaipay/analytics/transactions
func (h *JaiJaiPayHandler) GetTransactionAnalytics(c *gin.Context) {
	req := &jaijaipay.AnalyticsRequest{}

	req.StartDate = c.Query("startDate")
	req.EndDate = c.Query("endDate")
	req.Currency = c.Query("currency")
	req.TransactionType = c.Query("transactionType")
	req.Status = c.Query("status")

	// Validate required parameters
	if req.StartDate == "" || req.EndDate == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Start date and end date are required (YYYY-MM-DD format)",
		})
		return
	}

	result, err := h.service.GetTransactionAnalytics(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("failed to get transaction analytics")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get transaction analytics",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// Webhooks Handlers

// ResendWebhook handles POST /api/jaijaipay/webhooks/resend
func (h *JaiJaiPayHandler) ResendWebhook(c *gin.Context) {
	var req jaijaipay.ResendWebhookRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("invalid request body for resend webhook")
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"message": err.Error(),
		})
		return
	}

	result, err := h.service.ResendWebhook(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("failed to resend webhook")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to resend webhook",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// API Logs Handlers

// GetAPILogsByOrderID handles GET /api/jaijaipay/logs/order/:orderId
func (h *JaiJaiPayHandler) GetAPILogsByOrderID(c *gin.Context) {
	orderID := c.Param("orderId")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Order ID is required",
		})
		return
	}

	result, err := h.service.GetAPILogsByOrderID(c.Request.Context(), orderID)
	if err != nil {
		h.logger.WithError(err).Error("failed to get API logs by order ID")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get API logs",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"order_id": orderID,
		"logs":     result,
	})
}

// GetAPILogsByTransactionID handles GET /api/jaijaipay/logs/transaction/:transactionId
func (h *JaiJaiPayHandler) GetAPILogsByTransactionID(c *gin.Context) {
	transactionID := c.Param("transactionId")
	if transactionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Transaction ID is required",
		})
		return
	}

	result, err := h.service.GetAPILogsByTransactionID(c.Request.Context(), transactionID)
	if err != nil {
		h.logger.WithError(err).Error("failed to get API logs by transaction ID")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get API logs",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"transaction_id": transactionID,
		"logs":           result,
	})
}

// HandleWebhook handles POST /api/v1/jaijai/webhook
// This endpoint receives webhook notifications from JaiJaiPay
func (h *JaiJaiPayHandler) HandleWebhook(c *gin.Context) {
	h.logger.Info("HANDLER DEBUG: Webhook received")

	// Read raw body
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		h.logger.WithError(err).Error("failed to read webhook request body")
		// Still return success as JaiJaiPay team only wants 200 OK
		c.JSON(http.StatusOK, gin.H{
			"success": true,
		})
		return
	}

	h.logger.WithField("body_length", len(body)).Info("HANDLER DEBUG: Body read successfully")

	// Get webhook signature from header (optional - no validation required)
	signature := c.GetHeader("X-JaiJaiPay-Signature")

	// Log the incoming webhook for debugging
	h.logger.WithFields(map[string]interface{}{
		"signature":   signature,
		"body_length": len(body),
		"user_agent":  c.GetHeader("User-Agent"),
		"ip":          c.ClientIP(),
	}).Info("received JaiJaiPay webhook")

	// Parse webhook payload from the body we already read
	var payload jaijaipay.WebhookPayload
	if err := json.Unmarshal(body, &payload); err != nil {
		h.logger.WithError(err).Error("invalid webhook payload format")
		// Still return success as JaiJaiPay team only wants 200 OK
		c.JSON(http.StatusOK, gin.H{
			"success": true,
		})
		return
	}

	// Process webhook through service layer (no signature verification)
	err = h.service.ProcessWebhook(c.Request.Context(), &payload, signature, body)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"transaction_id": payload.TransactionID,
			"event":          payload.Event,
		}).Error("failed to process webhook")
		// Still return success as JaiJaiPay team only wants 200 OK
	}

	// Always return success response as requested by JaiJaiPay team
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
