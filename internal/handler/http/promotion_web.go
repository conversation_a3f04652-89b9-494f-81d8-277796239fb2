package http

import (
	"blacking-api/pkg/auth"
	"net/http"
	"strconv"
	"time"

	"blacking-api/internal/domain/promotion_web"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

type PromotionWebHandler struct {
	promotionWebService service.PromotionWebService
	logger              logger.Logger
}

func NewPromotionWebHandler(promotionWebService service.PromotionWebService, logger logger.Logger) *PromotionWebHandler {
	return &PromotionWebHandler{
		promotionWebService: promotionWebService,
		logger:              logger,
	}
}

// CreatePromotionWeb handles POST /api/v1/promotion-webs
// @Summary Create a new promotion
// @Description Create a new promotion web campaign with specified parameters
// @Tags Admin - Promotion Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body promotion_web.PromotionWebCreateRequest true "Promotion creation data"
// @Success 201 {object} map[string]interface{} "Promotion created successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web [post]
func (h *PromotionWebHandler) CreatePromotionWeb(c *gin.Context) {
	var req promotion_web.PromotionWebCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	adminId := auth.GetUserIDFromContext(c)
	req.CreatedByAdminId = int64(adminId)

	id, err := h.promotionWebService.CreatePromotionWeb(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data": gin.H{
			"id": id,
		},
		"message": "Promotion web created successfully",
	})
}

// GetPromotionWebList handles GET /api/v1/promotion-webs
// @Summary Get promotion list
// @Description Get a paginated list of promotions with optional filtering
// @Tags Admin - Promotion Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param search query string false "Search term"
// @Param promotionWebStatusId query int false "Filter by status ID"
// @Success 200 {object} map[string]interface{} "Promotion list retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Invalid query parameters"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web [get]
func (h *PromotionWebHandler) GetPromotionWebList(c *gin.Context) {
	var req promotion_web.PromotionWebGetListRequest

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			req.Limit = l
		}
	}

	req.Search = c.Query("search")
	// Fix parameter names to match URL query parameters
	req.StartDate = c.Query("startDate") // Changed from "start_date"
	req.EndDate = c.Query("endDate")     // Changed from "end_date"

	// Fix parameter name to match URL query parameter
	if statusId := c.Query("promotionWebStatusId"); statusId != "" { // Changed from "promotion_web_status_id"
		if s, err := strconv.ParseInt(statusId, 10, 64); err == nil {
			req.PromotionWebStatusId = &s
		}
	}

	// Set default values if not provided
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}

	list, total, err := h.promotionWebService.GetPromotionWebList(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"items": list,
			"total": total,
			"page":  req.Page,
			"limit": req.Limit,
		},
		"message": "Promotion web list retrieved successfully",
	})
}

// GetPromotionWebById handles GET /api/v1/promotion-webs/:id
// @Summary Get promotion by ID
// @Description Get detailed information about a specific promotion
// @Tags Admin - Promotion Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Promotion ID"
// @Success 200 {object} map[string]interface{} "Promotion retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Invalid promotion ID"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 404 {object} map[string]interface{} "Promotion not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/{id} [get]
func (h *PromotionWebHandler) GetPromotionWebById(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion web ID"))
		return
	}

	result, err := h.promotionWebService.GetPromotionWebById(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		"message": "Promotion web retrieved successfully",
	})
}

// UpdatePromotionWeb handles PUT /api/v1/promotion-webs/:id
// @Summary Update promotion
// @Description Update an existing promotion with new data
// @Tags Admin - Promotion Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Promotion ID"
// @Param request body promotion_web.PromotionWebUpdateRequest true "Promotion update data"
// @Success 200 {object} map[string]interface{} "Promotion updated successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 404 {object} map[string]interface{} "Promotion not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/{id} [put]
func (h *PromotionWebHandler) UpdatePromotionWeb(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion web ID"))
		return
	}

	var req promotion_web.PromotionWebUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	req.Id = id
	adminId := auth.GetUserIDFromContext(c)
	req.UpdatedByAdminId = int64(adminId)

	err = h.promotionWebService.UpdatePromotionWeb(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Promotion web updated successfully",
	})
}

// DeletePromotionWeb handles DELETE /api/v1/promotion-webs/:id
// @Summary Delete promotion
// @Description Delete a promotion by ID
// @Tags Admin - Promotion Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Promotion ID"
// @Success 200 {object} map[string]interface{} "Promotion deleted successfully"
// @Failure 400 {object} map[string]interface{} "Invalid promotion ID"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 404 {object} map[string]interface{} "Promotion not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/{id} [delete]
func (h *PromotionWebHandler) DeletePromotionWeb(c *gin.Context) {
	adminId := auth.GetUserIDFromContext(c)

	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion web ID"))
		return
	}

	var req promotion_web.DeletePromotionWebRequest
	req.Id = id
	req.DeletedByAdminId = int64(adminId)
	req.DeletedAt = time.Now().UTC()

	err = h.promotionWebService.DeletePromotionWeb(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "delete successfully",
	})
}

// CancelPromotionWeb handles POST /api/v1/promotion-webs/:id/cancel
// @Summary Cancel a promotion
// @Description Cancel an existing promotion web campaign
// @Tags Admin - Promotion Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Promotion ID"
// @Param request body promotion_web.CancelPromotionWebRequest true "Cancel promotion data"
// @Success 200 {object} map[string]interface{} "Promotion canceled successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 404 {object} map[string]interface{} "Promotion not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/{id}/cancel [post]
func (h *PromotionWebHandler) CancelPromotionWeb(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion web ID"))
		return
	}

	var req promotion_web.CancelPromotionWebRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	req.Id = id
	req.PromotionWebStatusId = promotion_web.PROMOTION_WEB_STATUS_CANCELED
	req.CanceledAt = time.Now().UTC()

	adminId := auth.GetUserIDFromContext(c)
	req.CanceledByAdminId = int64(adminId)

	err = h.promotionWebService.CancelPromotionWeb(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Promotion web canceled successfully",
	})
}

// GetPromotionWebUserToCancel handles GET /api/v1/promotion-webs/:id/users-to-cancel
func (h *PromotionWebHandler) GetPromotionWebUserToCancel(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion web ID"))
		return
	}

	result, err := h.promotionWebService.GetPromotionWebUserToCancel(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		"message": "Promotion web users to cancel retrieved successfully",
	})
}

// GetUserPromotionWebList handles GET /api/v1/promotion-web-users
// @Summary Get user promotion list
// @Description Get a paginated list of user promotions with filtering options
// @Tags Admin - User Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param promotionWebId query int false "Filter by promotion ID"
// @Param promotionWebUserStatusId query int false "Filter by user promotion status"
// @Param search query string false "Search by user details"
// @Success 200 {object} map[string]interface{} "User promotion list retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Invalid query parameters"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/users [get]
func (h *PromotionWebHandler) GetUserPromotionWebList(c *gin.Context) {
	var req promotion_web.PromotionWebUserGetListRequest

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			req.Limit = l
		}
	}

	req.Search = c.Query("search")
	req.StartDate = c.Query("start_date")
	req.EndDate = c.Query("end_date")

	if promotionWebId := c.Query("promotion_web_id"); promotionWebId != "" {
		if p, err := strconv.ParseInt(promotionWebId, 10, 64); err == nil {
			req.PromotionWebId = &p
		}
	}

	if statusId := c.Query("promotion_web_user_status_id"); statusId != "" {
		if s, err := strconv.ParseInt(statusId, 10, 64); err == nil {
			req.PromotionWebUserStatusId = &s
		}
	}

	list, total, err := h.promotionWebService.GetUserPromotionWebList(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"items": list,
			"total": total,
			"page":  req.Page,
			"limit": req.Limit,
		},
		"message": "User promotion web list retrieved successfully",
	})
}

// GetPromotionWebUserById handles GET /api/v1/promotion-web-users/:id
func (h *PromotionWebHandler) GetPromotionWebUserById(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion web user ID"))
		return
	}

	req := promotion_web.GetPromotionWebUserById{Id: id}
	result, err := h.promotionWebService.GetPromotionWebUserById(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		"message": "Promotion web user retrieved successfully",
	})
}

// GetUserPromotionWebByUserId handles GET /api/v1/users/:user_id/promotion-web
func (h *PromotionWebHandler) GetUserPromotionWebByUserId(c *gin.Context) {
	memberId := auth.GetUserIDFromContext(c)
	result, err := h.promotionWebService.GetUserPromotionWebByUserId(c.Request.Context(), int64(memberId))
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		"message": "User promotion web retrieved successfully",
	})
}

// PromotionWebUserGetListByUserId handles GET /api/v1/users/:user_id/promotion-web-users
func (h *PromotionWebHandler) PromotionWebUserGetListByUserId(c *gin.Context) {
	var req promotion_web.PromotionWebUserGetListByUserIdRequest

	memberId := auth.GetUserIDFromContext(c)
	req.UserId = int64(memberId)

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			req.Limit = l
		}
	}

	req.Search = c.Query("search")
	req.OfDate = c.Query("of_date")
	req.DateType = c.Query("date_type")
	req.FromDate = c.Query("from_date")
	req.ToDate = c.Query("to_date")
	req.SortCol = c.Query("sort_col")
	req.SortAsc = c.Query("sort_asc")

	if statusId := c.Query("promotion_web_user_status_id"); statusId != "" {
		if s, err := strconv.ParseInt(statusId, 10, 64); err == nil {
			req.PromotionWebUserStatusId = &s
		}
	}

	list, total, err := h.promotionWebService.PromotionWebUserGetListByUserId(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"items": list,
			"total": total,
			"page":  req.Page,
			"limit": req.Limit,
		},
		"message": "Promotion web user list by user ID retrieved successfully",
	})
}

// CancelPromotionWebUserById handles POST /api/v1/promotion-web-users/:id/cancel
func (h *PromotionWebHandler) CancelPromotionWebUserById(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion web user ID"))
		return
	}

	var req promotion_web.CancelPromotionWebUserById
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	req.Id = id

	err = h.promotionWebService.CancelPromotionWebUserById(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Promotion web user canceled successfully",
	})
}

// GetActivePromotionWebSlides handles GET /api/v1/promotion-webs/slides/active
// @Summary Get active promotion slides
// @Description Get a list of active promotions for display as slides (public access)
// @Tags Public - Promotions
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "Active promotion slides retrieved successfully"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/public/slides/active [get]
func (h *PromotionWebHandler) GetActivePromotionWebSlides(c *gin.Context) {
	result, err := h.promotionWebService.PromotionWebGetSildeListOnlyActive(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		"message": "Active promotion web slides retrieved successfully",
	})
}

// UpdatePromotionWebPriorityOrder handles PUT /api/v1/promotion-webs/:id/priority-order
func (h *PromotionWebHandler) UpdatePromotionWebPriorityOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion web ID"))
		return
	}

	err = h.promotionWebService.UpdatePromotionWebPriorityOrderCreate(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Promotion web priority order updated successfully",
	})
}

// SortPromotionWebPriorityOrder handles POST /api/v1/promotion-webs/sort-priority
func (h *PromotionWebHandler) SortPromotionWebPriorityOrder(c *gin.Context) {
	var req promotion_web.DragSortRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	err := h.promotionWebService.SortPromotionWebPriorityOrder(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Promotion web priority order sorted successfully",
	})
}

// UploadImageToCloudflare handles POST /api/v1/promotion-webs/upload-image
func (h *PromotionWebHandler) UploadImageToCloudflare(c *gin.Context) {
	fileUrl, err := h.promotionWebService.UploadImageToCloudflare(c.Request.Context(), c.Request)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Image uploaded to Cloudflare successfully",
		"data":    fileUrl,
	})
}

// Option/lookup endpoints
// @Summary Get promotion types
// @Description Get all available promotion types for configuration
// @Tags Admin - Options & Lookups
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Promotion types retrieved successfully"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/options/types [get]
func (h *PromotionWebHandler) GetPromotionTypes(c *gin.Context) {
	types, err := h.promotionWebService.GetPromotionTypes(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    types,
		"message": "Promotion types retrieved successfully",
	})
}

// @Summary Get promotion statuses
// @Description Get all available promotion statuses for configuration
// @Tags Admin - Options & Lookups
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Promotion statuses retrieved successfully"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/options/statuses [get]
func (h *PromotionWebHandler) GetPromotionStatuses(c *gin.Context) {
	statuses, err := h.promotionWebService.GetPromotionStatuses(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    statuses,
		"message": "Promotion statuses retrieved successfully",
	})
}

// @Summary Get promotion bonus conditions
// @Description Get all available bonus conditions for promotion configuration
// @Tags Admin - Options & Lookups
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Promotion bonus conditions retrieved successfully"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/options/bonus-conditions [get]
func (h *PromotionWebHandler) GetPromotionBonusConditions(c *gin.Context) {
	conditions, err := h.promotionWebService.GetPromotionBonusConditions(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    conditions,
		"message": "Promotion bonus conditions retrieved successfully",
	})
}

// @Summary Get promotion bonus types
// @Description Get all available bonus types for promotion configuration
// @Tags Admin - Options & Lookups
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Promotion bonus types retrieved successfully"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/options/bonus-types [get]
func (h *PromotionWebHandler) GetPromotionBonusTypes(c *gin.Context) {
	types, err := h.promotionWebService.GetPromotionBonusTypes(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    types,
		"message": "Promotion bonus types retrieved successfully",
	})
}

// @Summary Get promotion turnover types
// @Description Get all available turnover types for promotion configuration
// @Tags Admin - Options & Lookups
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Promotion turnover types retrieved successfully"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/options/turnover-types [get]
func (h *PromotionWebHandler) GetPromotionTurnoverTypes(c *gin.Context) {
	types, err := h.promotionWebService.GetPromotionTurnoverTypes(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    types,
		"message": "Promotion turnover types retrieved successfully",
	})
}

// @Summary Get promotion date types
// @Description Get all available date types for promotion configuration
// @Tags Admin - Options & Lookups
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Promotion date types retrieved successfully"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/options/date-types [get]
func (h *PromotionWebHandler) GetPromotionDateTypes(c *gin.Context) {
	types, err := h.promotionWebService.GetPromotionDateTypes(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    types,
		"message": "Promotion date types retrieved successfully",
	})
}

// User promotion endpoints
// @Summary Collect a promotion
// @Description Allow a user to collect/claim a promotion
// @Tags User - Promotion Collection
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body promotion_web.CollectPromotionRequest true "Promotion collection data"
// @Success 201 {object} map[string]interface{} "Promotion collected successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data or promotion not eligible"
// @Failure 401 {object} map[string]interface{} "User authentication required"
// @Failure 403 {object} map[string]interface{} "User access required"
// @Failure 404 {object} map[string]interface{} "Promotion not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/user/collect [post]
func (h *PromotionWebHandler) CollectPromotion(c *gin.Context) {
	var req promotion_web.CollectPromotionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.Error(errors.NewUnauthorizedError("user not authenticated"))
		return
	}
	req.UserID = userID.(int64)

	id, err := h.promotionWebService.CollectPromotion(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data": gin.H{
			"id": id,
		},
		"message": "Promotion collected successfully",
	})
}

// @Summary Get user's promotions
// @Description Get all promotions available to the authenticated user with their status
// @Tags User - Promotion Collection
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "User promotions retrieved successfully"
// @Failure 401 {object} map[string]interface{} "User authentication required"
// @Failure 403 {object} map[string]interface{} "User access required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/user/my-promotions [get]
func (h *PromotionWebHandler) GetUserPromotions(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.Error(errors.NewUnauthorizedError("user not authenticated"))
		return
	}

	promotions, err := h.promotionWebService.GetUserPromotions(c.Request.Context(), userID.(int64))
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    promotions,
		"message": "User promotions retrieved successfully",
	})
}

// Public promotion endpoints
// @Summary Get public promotions
// @Description Get a list of all publicly available promotions (no authentication required)
// @Tags Public - Promotions
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "Public promotions retrieved successfully"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/public [get]
func (h *PromotionWebHandler) GetPublicPromotions(c *gin.Context) {
	promotions, err := h.promotionWebService.GetPublicPromotions(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    promotions,
		"message": "Public promotions retrieved successfully",
	})
}

// @Summary Get public promotion by ID
// @Description Get detailed information about a specific public promotion
// @Tags Public - Promotions
// @Accept json
// @Produce json
// @Param id path int true "Promotion ID"
// @Success 200 {object} map[string]interface{} "Public promotion retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Invalid promotion ID"
// @Failure 404 {object} map[string]interface{} "Promotion not found or not publicly available"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/public/{id} [get]
func (h *PromotionWebHandler) GetPublicPromotionByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion ID"))
		return
	}

	promotion, err := h.promotionWebService.GetPublicPromotionByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    promotion,
		"message": "Public promotion retrieved successfully",
	})
}

// Lock credit endpoints
// @Summary Create lock credit
// @Description Create a new credit lock for a user's promotion
// @Tags Admin - Lock Credit
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body promotion_web.LockCreditPromotionCreateRequest true "Lock credit data"
// @Success 201 {object} map[string]interface{} "Lock credit created successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /lock-credit [post]
func (h *PromotionWebHandler) CreateLockCredit(c *gin.Context) {
	var req promotion_web.LockCreditPromotionCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	id, err := h.promotionWebService.CreateLockCredit(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data": gin.H{
			"id": id,
		},
		"message": "Lock credit created successfully",
	})
}

// @Summary Get lock credit withdraw list
// @Description Get a paginated list of credit withdraw locks with filtering options
// @Tags Admin - Lock Credit
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param userId query int false "Filter by user ID"
// @Param search query string false "Search by member code, name, or phone"
// @Param startDate query string false "Start date filter (YYYY-MM-DD)"
// @Param endDate query string false "End date filter (YYYY-MM-DD)"
// @Success 200 {object} map[string]interface{} "Lock credit withdraw list retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Invalid query parameters"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /lock-credit/withdraw-list [get]
func (h *PromotionWebHandler) GetLockCreditWithdrawList(c *gin.Context) {
	var req promotion_web.GetLockCreditWithdrawListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.Error(errors.NewValidationError("invalid query parameters: " + err.Error()))
		return
	}

	response, err := h.promotionWebService.GetLockCreditWithdrawList(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
		"message": "Lock credit withdraw list retrieved successfully",
	})
}

func (h *PromotionWebHandler) UnlockCreditWithdraw(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid lock credit withdraw ID"))
		return
	}

	// Get admin ID from context (set by auth middleware)
	adminID, exists := c.Get("user_id")
	if !exists {
		c.Error(errors.NewUnauthorizedError("admin not authenticated"))
		return
	}

	err = h.promotionWebService.UnlockCreditWithdraw(c.Request.Context(), id, adminID.(int64))
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Credit withdraw unlocked successfully",
	})
}

// @Summary Check lock credit withdraw
// @Description Check if the authenticated user has any locked credit that prevents withdrawal
// @Tags User - Lock Credit
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Lock credit status checked successfully"
// @Failure 401 {object} map[string]interface{} "User authentication required"
// @Failure 403 {object} map[string]interface{} "User access required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /user/lock-credit/withdraw-check [get]
func (h *PromotionWebHandler) CheckLockCreditWithdraw(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.Error(errors.NewUnauthorizedError("user not authenticated"))
		return
	}

	isLocked, err := h.promotionWebService.CheckLockedCredit(c.Request.Context(), userID.(int64))
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"isLocked": isLocked,
		},
		"message": "Lock credit status checked successfully",
	})
}

// Additional handler methods from migration document
// @Summary Get promotion slide list
// @Description Get a list of active promotions for display as slides
// @Tags Admin - Promotion Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Promotion slide list retrieved successfully"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/slide-list [get]
func (h *PromotionWebHandler) GetPromotionSlideList(c *gin.Context) {
	slides, err := h.promotionWebService.GetPromotionSlideList(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    slides,
		"message": "Promotion slide list retrieved successfully",
	})
}

// @Summary Sort promotion priority order
// @Description Update the priority order of promotions by dragging and dropping
// @Tags Admin - Promotion Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body promotion_web.DragSortRequest true "Sort request data"
// @Success 200 {object} map[string]interface{} "Priority order updated successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/sort-priority-order [put]
func (h *PromotionWebHandler) SortPromotionPriorityOrder(c *gin.Context) {
	var req promotion_web.DragSortRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Get admin ID from context (set by auth middleware)
	adminID, exists := c.Get("admin_id")
	if !exists {
		c.Error(errors.NewUnauthorizedError("admin not authenticated"))
		return
	}

	err := h.promotionWebService.SortPromotionPriorityOrder(c.Request.Context(), req, adminID.(int64))
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Priority order updated successfully",
	})
}

func (h *PromotionWebHandler) GetPromotionSummary(c *gin.Context) {
	var req promotion_web.PromotionSummaryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.Error(errors.NewValidationError("invalid query parameters: " + err.Error()))
		return
	}

	summary, err := h.promotionWebService.GetPromotionSummary(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    summary,
		"message": "Promotion summary retrieved successfully",
	})
}

func (h *PromotionWebHandler) GetUserPromotionSummary(c *gin.Context) {
	var req promotion_web.UserPromotionSummaryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.Error(errors.NewValidationError("invalid query parameters: " + err.Error()))
		return
	}

	summary, err := h.promotionWebService.GetUserPromotionSummary(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    summary,
		"message": "User promotion summary retrieved successfully",
	})
}

func (h *PromotionWebHandler) GetPromotionByHiddenURL(c *gin.Context) {
	hiddenURL := c.Param("hiddenUrlLink")
	if hiddenURL == "" {
		c.Error(errors.NewValidationError("hidden URL link is required"))
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.Error(errors.NewUnauthorizedError("user not authenticated"))
		return
	}

	promotion, err := h.promotionWebService.GetPromotionByHiddenURL(c.Request.Context(), userID.(int64), hiddenURL)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    promotion,
		"message": "Promotion retrieved successfully",
	})
}

func (h *PromotionWebHandler) GetUserTurnoverSummary(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid user promotion ID"))
		return
	}

	summary, err := h.promotionWebService.GetUserTurnoverSummary(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    summary,
		"message": "User turnover summary retrieved successfully",
	})
}

func (h *PromotionWebHandler) CheckUserPromotionForDeposit(c *gin.Context) {
	var req promotion_web.CheckUserPromotionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	result, err := h.promotionWebService.CheckUserPromotionForDeposit(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"result": result,
		},
		"message": "User promotion check completed",
	})
}

func (h *PromotionWebHandler) CheckPromotionWithdraw(c *gin.Context) {
	var req promotion_web.CheckPromotionWithdrawRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	result, err := h.promotionWebService.CheckPromotionWithdraw(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		"message": "Promotion withdraw check completed",
	})
}

func (h *PromotionWebHandler) GetUserCollectedPromotions(c *gin.Context) {
	var req promotion_web.GetUserCollectedPromotionsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.Error(errors.NewValidationError("invalid query parameters: " + err.Error()))
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.Error(errors.NewUnauthorizedError("user not authenticated"))
		return
	}

	response, err := h.promotionWebService.GetUserCollectedPromotions(c.Request.Context(), userID.(int64), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
		"message": "User collected promotions retrieved successfully",
	})
}

func (h *PromotionWebHandler) GetUserPromotionByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion ID"))
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.Error(errors.NewUnauthorizedError("user not authenticated"))
		return
	}

	promotion, err := h.promotionWebService.GetUserPromotionByID(c.Request.Context(), userID.(int64), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    promotion,
		"message": "User promotion retrieved successfully",
	})
}

// @Summary Upload promotion cover
// @Description Upload a cover image for a promotion
// @Tags Admin - Promotion Management
// @Accept multipart/form-data
// @Produce json
// @Security BearerAuth
// @Param file formData file true "Cover image file"
// @Success 200 {object} map[string]interface{} "Promotion cover uploaded successfully"
// @Failure 400 {object} map[string]interface{} "Invalid file or file validation failed"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Admin access required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /promotion-web/upload/cover [post]
func (h *PromotionWebHandler) UploadPromotionCover(c *gin.Context) {
	result, err := h.promotionWebService.UploadPromotionCover(c.Request.Context(), c.Request)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		"message": "Promotion cover uploaded successfully",
	})
}
