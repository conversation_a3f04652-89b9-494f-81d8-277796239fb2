package http

import (
	"blacking-api/internal/domain/holding_account"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"net/http"
	"strconv"
)

type HoldingAccountHandler struct {
	holdingAccountService service.HodlingAccountService
	logger                logger.Logger
}

func NewHoldingAccountHandler(holdingAccountService service.HodlingAccountService, logger logger.Logger) *HoldingAccountHandler {
	return &HoldingAccountHandler{
		holdingAccountService: holdingAccountService,
		logger:                logger,
	}
}

// @Summary Create holding account
// @Description Create a new holding account for managing funds
// @Tags Account Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body object true "Holding account creation request"
// @Success 201 {object} object{success=bool,message=string} "Holding account created successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /holding-account [post]
func (h *HoldingAccountHandler) CreateHoldingAccount(c *gin.Context) {
	var req *holding_account.HoldingAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.holdingAccountService.CreateHoldingAccount(c.Request.Context(), req); err != nil {
		h.logger.Error("Failed to create holding account", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(201, gin.H{
		"success": true,
		"message": "Holding account created successfully",
	})
}

// @Summary List holding accounts
// @Description Get a list of holding accounts with optional search and pagination
// @Tags Account Management
// @Produce json
// @Security BearerAuth
// @Param limit query int false "Limit"
// @Param offset query int false "Offset"
// @Param search query string false "Search term"
// @Success 200 {object} object{success=bool,holding_accounts=[]object} "Holding accounts retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /holding-account [get]
func (h *HoldingAccountHandler) GetHoldingAccounts(c *gin.Context) {
	var req *holding_account.HoldingAccountSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	holdingAccounts, err := h.holdingAccountService.FindAllHoldingAccount(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("Failed to get holding accounts", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success":          true,
		"holding_accounts": holdingAccounts,
	})
}

// @Summary Get holding account by ID
// @Description Get a specific holding account by its ID
// @Tags Account Management
// @Produce json
// @Security BearerAuth
// @Param id path string true "Holding Account ID"
// @Success 200 {object} object{success=bool,holding_account=object} "Holding account retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Holding account not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /holding-account/{id} [get]
func (h *HoldingAccountHandler) GetHoldingAccountByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("holding account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	holdingAccount, err := h.holdingAccountService.FindHoldingAccountByID(c.Request.Context(), idInt)
	if err != nil {
		h.logger.Error("Failed to get holding account by ID", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success":         true,
		"holding_account": holdingAccount,
	})
}

func (h *HoldingAccountHandler) UpdateHoldingAccount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("withdraw account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *holding_account.HoldingAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.holdingAccountService.UpdateHoldingAccount(c.Request.Context(), idInt, req); err != nil {
		h.logger.Error("Failed to update holding account", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Holding account updated successfully",
	})
}

func (h *HoldingAccountHandler) ActiveHoldingAccount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("withdraw account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	statusStr := c.Query("status")
	if statusStr == "" {
		HandleSingleError(c, errors.NewValidationError("status is required"))
		return
	}

	status, err := strconv.ParseBool(statusStr)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	err = h.holdingAccountService.ActiveHoldingAccount(c.Request.Context(), idInt, status)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"Message": "Holding account status updated successfully",
	})
}

func (h *HoldingAccountHandler) DeleteHoldingAccount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("holding account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.holdingAccountService.DeleteHoldingAccount(c.Request.Context(), idInt); err != nil {
		h.logger.Error("Failed to delete holding account", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Holding account deleted successfully",
	})
}
