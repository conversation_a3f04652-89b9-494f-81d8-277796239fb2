package http

import (
	"net/http"
	"time"

	"blacking-api/internal/domain/dashboard"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"

	"github.com/gin-gonic/gin"
)

type DashboardHandler struct {
	dashboardService *service.DashboardService
}

func NewDashboardHandler(dashboardService *service.DashboardService) *DashboardHandler {
	return &DashboardHandler{
		dashboardService: dashboardService,
	}
}

// GetDashboard godoc
// @Summary Get dashboard data
// @Description รวมข้อมูลทั้งหมดสำหรับหน้า dashboard พร้อม join ข้อมูล game types และ providers
// @Tags Dashboard
// @Accept json
// @Produce json
// @Param date_from query string true "Start date (YYYY-MM-DD)" example(2025-08-01)
// @Param date_to query string true "End date (YYYY-MM-DD)" example(2025-08-19)
// @Param type query string true "Dashboard type" Enums(daily) example(daily)
// @Success 200 {object} dashboard.DashboardResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/dashboard [get]
func (h *DashboardHandler) GetDashboard(c *gin.Context) {
	ctx := c.Request.Context()

	// Validate request parameters
	var req dashboard.DashboardRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Message: "Invalid request parameters",
			Data:    err.Error(),
		})
		return
	}

	// Validate date format and range
	if err := h.validateDashboardRequest(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Message: "Invalid request data",
			Data:    err.Error(),
		})
		return
	}

	dashboardData, err := h.dashboardService.GetDashboardData(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Message: "Failed to get dashboard data",
			Data:    err.Error(),
		})
		return
	}

	response := dashboard.DashboardResponse{
		Code:    http.StatusOK,
		Success: true,
		Message: "Dashboard data retrieved successfully",
		Data:    *dashboardData,
	}

	c.JSON(http.StatusOK, response)
}

// GetBotSuccess godoc
// @Summary Get bot success data
// @Description ข้อมูลความสำเร็จของ bot สำหรับหน้า dashboard
// @Tags Dashboard
// @Accept json
// @Produce json
// @Param round_date query string true "Round date (YYYY-MM-DD)" example(2025-07-16)
// @Success 200 {object} dashboard.BotSuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/dashboard/bot [get]
func (h *DashboardHandler) GetBotSuccess(c *gin.Context) {
	ctx := c.Request.Context()

	// Validate request parameters
	var req dashboard.BotSuccessRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Message: "Invalid request parameters",
			Data:    err.Error(),
		})
		return
	}

	// Validate date format
	if err := h.validateBotSuccessRequest(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Message: "Invalid request data",
			Data:    err.Error(),
		})
		return
	}

	botSuccessData, err := h.dashboardService.GetBotSuccessData(ctx, req.RoundDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Message: "Failed to get bot success data",
			Data:    err.Error(),
		})
		return
	}

	response := dashboard.BotSuccessResponse{
		Code:    http.StatusOK,
		Success: true,
		Message: "Bot success data retrieved successfully",
		Data:    *botSuccessData,
	}

	c.JSON(http.StatusOK, response)
}

// validateDashboardRequest validates the dashboard request parameters
func (h *DashboardHandler) validateDashboardRequest(req *dashboard.DashboardRequest) error {
	// Parse dates
	dateFrom, err := time.Parse("2006-01-02", req.DateFrom)
	if err != nil {
		return errors.NewValidationError("date_from must be in YYYY-MM-DD format")
	}

	dateTo, err := time.Parse("2006-01-02", req.DateTo)
	if err != nil {
		return errors.NewValidationError("date_to must be in YYYY-MM-DD format")
	}

	// Validate date range
	if dateFrom.After(dateTo) {
		return errors.NewValidationError("date_from cannot be after date_to")
	}

	// Validate type (จะมีแค่ daily ตอนนี้)
	if req.Type != "daily" {
		return errors.NewValidationError("type must be 'daily'")
	}

	return nil
}

// validateBotSuccessRequest validates the bot success request parameters
func (h *DashboardHandler) validateBotSuccessRequest(req *dashboard.BotSuccessRequest) error {
	// Parse date
	_, err := time.Parse("2006-01-02", req.RoundDate)
	if err != nil {
		return errors.NewValidationError("round_date must be in YYYY-MM-DD format")
	}

	return nil
}
