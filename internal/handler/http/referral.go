package http

import (
	"net/http"
	"strconv"

	"blacking-api/internal/domain/referral"
	"blacking-api/internal/service"
	"blacking-api/pkg/auth"
	"blacking-api/pkg/errors"

	"github.com/gin-gonic/gin"
)

type ReferralHandler struct {
	referralService service.ReferralService
}

func NewReferralHandler(referralService service.ReferralService) *ReferralHandler {
	return &ReferralHandler{
		referralService: referralService,
	}
}

// GetOverview returns referral overview statistics
// @Summary Get referral overview
// @Description Get referral overview statistics for the current user
// @Tags Referral
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} object{success=bool,data=referral.OverviewResponse} "Overview retrieved successfully"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /referrals/overview [get]
func (h *ReferralHandler) GetOverview(c *gin.Context) {
	memberID := auth.GetMemberIDFromContext(c)
	if memberID == 0 {
		c.Error(errors.NewUnauthorizedError("member not authenticated"))
		return
	}

	overview, err := h.referralService.GetOverview(c.Request.Context(), memberID)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    overview,
	})
}

// GetReferralMembers returns comprehensive referral data including downlines and analytics
// @Summary Get referral members data
// @Description Get downlines list with today's commission analytics and chart data with pagination support
// @Tags Referral
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number (default: 1)" minimum(1)
// @Param limit query int false "Records per page (default: 20, max: 100)" minimum(1) maximum(100)
// @Success 200 {object} object{success=bool,data=referral.ReferralMembersResponse} "Members data retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /referrals/members [get]
func (h *ReferralHandler) GetReferralMembers(c *gin.Context) {
	memberID := auth.GetMemberIDFromContext(c)
	if memberID == 0 {
		c.Error(errors.NewUnauthorizedError("member not authenticated"))
		return
	}

	// Parse pagination parameters
	var req referral.ReferralMembersRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.Error(errors.NewValidationError("invalid query parameters"))
		return
	}

	response, err := h.referralService.GetReferralMembers(c.Request.Context(), memberID, req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// GetIncomeData returns referral income statistics and history with pagination
// @Summary Get referral income
// @Description Get referral income statistics and history for the current user with pagination for recent transactions
// @Tags Referral
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param months query int false "Number of months for history (default: 12, max: 24)" minimum(1) maximum(24)
// @Param page query int false "Page number for recent transactions (default: 1)" minimum(1)
// @Param limit query int false "Records per page for recent transactions (default: 10, max: 100)" minimum(1) maximum(100)
// @Success 200 {object} object{success=bool,data=referral.IncomeResponse} "Income data retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /referrals/income [get]
func (h *ReferralHandler) GetIncomeData(c *gin.Context) {
	memberID := auth.GetMemberIDFromContext(c)
	if memberID == 0 {
		c.Error(errors.NewUnauthorizedError("member not authenticated"))
		return
	}

	// Parse request parameters
	var req referral.IncomeRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.Error(errors.NewValidationError("invalid query parameters"))
		return
	}

	incomeData, err := h.referralService.GetIncomeData(c.Request.Context(), memberID, req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    incomeData,
	})
}

// GetTutorialAndFAQ returns tutorials and FAQs for referral system
// @Summary Get referral tutorials and FAQs
// @Description Get tutorials and FAQs for the referral system
// @Tags Referral
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} object{success=bool,data=referral.TutorialFAQResponse} "Tutorial and FAQ data retrieved successfully"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /referrals/tutorial-faq [get]
func (h *ReferralHandler) GetTutorialAndFAQ(c *gin.Context) {
	data, err := h.referralService.GetTutorialAndFAQ(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
	})
}

// IncrementReferralView increments referral view count for a referral code
// @Summary Increment referral view count
// @Description Increment referral view count when someone visits referral link
// @Tags Referral
// @Accept json
// @Produce json
// @Param referral_code path string true "Referral code to increment view count"
// @Success 200 {object} object{success=bool,message=string} "View count incremented successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Referral code not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /referrals/view/{referral_code} [post]
func (h *ReferralHandler) IncrementReferralView(c *gin.Context) {
	referralCode := c.Param("referral_code")
	if referralCode == "" {
		c.Error(errors.NewValidationError("referral code is required"))
		return
	}

	err := h.referralService.IncrementReferralViewByCode(c.Request.Context(), referralCode)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Referral view count incremented successfully",
	})
}

// WithdrawCommission processes commission withdrawal request
// @Summary Withdraw commission balance
// @Description Withdraw commission balance for the current member
// @Tags Referral
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body referral.WithdrawCommissionRequest true "Withdraw request"
// @Success 200 {object} object{success=bool,data=referral.WithdrawCommissionResponse} "Withdraw request processed successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /referrals/withdraw [post]
func (h *ReferralHandler) WithdrawCommission(c *gin.Context) {
	memberID := auth.GetMemberIDFromContext(c)
	if memberID == 0 {
		c.Error(errors.NewUnauthorizedError("member not authenticated"))
		return
	}

	var req referral.WithdrawCommissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request format"))
		return
	}

	// Validate amount
	if req.Amount <= 0 {
		c.Error(errors.NewValidationError("withdraw amount must be greater than 0"))
		return
	}

	response, err := h.referralService.WithdrawCommission(c.Request.Context(), memberID, req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// GetPendingCommissions returns pending commission transactions with commission rates
// @Summary Get pending commissions
// @Description Get pending commission transactions with commission percent by category
// @Tags Referral
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Records per page (default: 20)"
// @Success 200 {object} object{success=bool,data=referral.PendingCommissionResponse} "Pending commissions retrieved successfully"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /referrals/pending-commissions [get]
func (h *ReferralHandler) GetPendingCommissions(c *gin.Context) {
	memberID := auth.GetMemberIDFromContext(c)
	if memberID == 0 {
		c.Error(errors.NewUnauthorizedError("member not authenticated"))
		return
	}

	// Parse query parameters
	page := 1
	if pageParam := c.Query("page"); pageParam != "" {
		if parsed, err := strconv.Atoi(pageParam); err == nil && parsed > 0 {
			page = parsed
		}
	}

	limit := 20
	if limitParam := c.Query("limit"); limitParam != "" {
		if parsed, err := strconv.Atoi(limitParam); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	response, err := h.referralService.GetPendingCommissions(c.Request.Context(), memberID, page, limit)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// ApproveCommissions approves pending commission transactions
// @Summary Approve pending commissions
// @Description Approve pending commission transactions and update member balances
// @Tags Referral
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body referral.ApproveCommissionRequest false "Commission approval request (empty body)"
// @Success 200 {object} object{success=bool,data=referral.ApproveCommissionResponse} "Commissions approved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /referrals/approve-commissions [post]
func (h *ReferralHandler) ApproveCommissions(c *gin.Context) {
	memberID := auth.GetMemberIDFromContext(c)
	if memberID == 0 {
		c.Error(errors.NewUnauthorizedError("member not authenticated"))
		return
	}

	response, err := h.referralService.ApproveCommissions(c.Request.Context(), memberID)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}
