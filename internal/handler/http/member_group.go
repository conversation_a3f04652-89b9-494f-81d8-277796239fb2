package http

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"blacking-api/internal/domain/member_group"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

// MemberGroupHandler handles member group-related HTTP requests
type MemberGroupHandler struct {
	memberGroupService service.MemberGroupService
	logger             logger.Logger
}

// NewMemberGroupHandler creates a new member group handler
func NewMemberGroupHandler(memberGroupService service.MemberGroupService, logger logger.Logger) *MemberGroupHandler {
	return &MemberGroupHandler{
		memberGroupService: memberGroupService,
		logger:             logger,
	}
}

// CreateMemberGroup handles POST /api/v1/member-groups
// @Summary Create member group
// @Description Create a new member group with optional image upload
// @Tags Member Group Management
// @Accept multipart/form-data
// @Produce json
// @Security BearerAuth
// @Param name formData string true "Member group name"
// @Param description formData string false "Member group description"
// @Param image_file formData file false "Member group image"
// @Success 201 {object} object{success=bool,data=member_group.MemberGroupResponse} "Member group created successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/member-groups [post]
func (h *MemberGroupHandler) CreateMemberGroup(c *gin.Context) {
	req, err := parseCreateFormData(c)
	if err != nil {
		c.Error(errors.NewValidationError("invalid form data"))
		return
	}

	// Handle image file upload if provided
	if _, _, err := c.Request.FormFile("image_file"); err == nil {
		fileUploadResp, err := h.memberGroupService.FileUpload(c.Request.Context(), c.Request, "image_file")
		if err != nil {
			c.Error(err)
			return
		}
		req.Image = &fileUploadResp.FileUrl
	}

	memberGroupResp, err := h.memberGroupService.CreateMemberGroup(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    memberGroupResp,
	})
}

// GetMemberGroup handles GET /api/v1/member-groups/:id
func (h *MemberGroupHandler) GetMemberGroup(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid member group ID"))
		return
	}

	memberGroupResp, err := h.memberGroupService.GetMemberGroupByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    memberGroupResp,
	})
}

// GetMemberGroupByCode handles GET /api/v1/member-groups/code/:code
func (h *MemberGroupHandler) GetMemberGroupByCode(c *gin.Context) {
	code := c.Param("code")
	if code == "" {
		c.Error(errors.NewValidationError("member group code is required"))
		return
	}

	memberGroupResp, err := h.memberGroupService.GetMemberGroupByCode(c.Request.Context(), code)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    memberGroupResp,
	})
}

// GetDefaultMemberGroup handles GET /api/v1/member-groups/default
func (h *MemberGroupHandler) GetDefaultMemberGroup(c *gin.Context) {
	memberGroupResp, err := h.memberGroupService.GetDefaultMemberGroup(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    memberGroupResp,
	})
}

// UpdateMemberGroup handles PUT /api/v1/member-groups/:id
func (h *MemberGroupHandler) UpdateMemberGroup(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid member group ID"))
		return
	}

	req, err := parseUpdateFormData(c)
	if err != nil {
		c.Error(errors.NewValidationError("invalid form data"))
		return
	}

	// Handle image file upload if provided
	var memberGroupResp *member_group.MemberGroupResponse
	if _, _, err := c.Request.FormFile("image_file"); err == nil {
		// Get current member group to check for existing image
		currentMemberGroup, err := h.memberGroupService.GetMemberGroupByID(c.Request.Context(), id)
		if err != nil {
			c.Error(err)
			return
		}

		// Delete old image file if exists
		if currentMemberGroup.Image != nil {
			if err := h.memberGroupService.DeleteFile(c.Request.Context(), &member_group.DeleteFileRequest{
				FileUrl: *currentMemberGroup.Image,
			}); err != nil {
				// Log error but don't fail the request
				// The new file upload was successful, so we continue
			}
		}

		fileUploadResp, err := h.memberGroupService.FileUpload(c.Request.Context(), c.Request, "image_file")
		if err != nil {
			c.Error(err)
			return
		}

		req.Image = &fileUploadResp.FileUrl

		// Use method that handles new file upload
		memberGroupResp, err = h.memberGroupService.UpdateMemberGroupWithNewFile(c.Request.Context(), id, req)
		if err != nil {
			c.Error(err)
			return
		}
	} else {
		// No new file uploaded, use regular update
		memberGroupResp, err = h.memberGroupService.UpdateMemberGroup(c.Request.Context(), id, req)
		if err != nil {
			c.Error(err)
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    memberGroupResp,
	})
}

// DeleteMemberGroup handles DELETE /api/v1/member-groups/:id
func (h *MemberGroupHandler) DeleteMemberGroup(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid member group ID"))
		return
	}

	if err := h.memberGroupService.DeleteMemberGroup(c.Request.Context(), id); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "member group deleted successfully",
	})
}

// ListMemberGroups handles GET /api/v1/member-groups
func (h *MemberGroupHandler) ListMemberGroups(c *gin.Context) {
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")
	search := c.Query("search")        // Search by code or name
	sortBy := c.Query("sort_by")       // Sort column (id, code, name, is_default, is_vip, created_at, updated_at)
	sortOrder := c.Query("sort_order") // Sort order (asc, desc)

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100 // Max limit
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// Validate sort parameters
	validSortColumns := map[string]bool{
		"id":         true,
		"code":       true,
		"name":       true,
		"is_default": true,
		"is_vip":     true,
		"created_at": true,
		"updated_at": true,
	}

	if sortBy != "" && !validSortColumns[sortBy] {
		sortBy = "is_default" // Default sort column (default first)
	}

	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = "desc" // Default sort order
	}

	memberGroups, total, err := h.memberGroupService.ListMemberGroups(c.Request.Context(), limit, offset, search, sortBy, sortOrder)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"member_groups": memberGroups,
			"pagination": gin.H{
				"total":      total,
				"limit":      limit,
				"offset":     offset,
				"sort_by":    sortBy,
				"sort_order": sortOrder,
			},
		},
	})
}

// ListActiveMemberGroups handles GET /api/v1/member-groups/active
func (h *MemberGroupHandler) ListActiveMemberGroups(c *gin.Context) {
	memberGroups, err := h.memberGroupService.ListActiveMemberGroups(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"member_groups": memberGroups,
		},
	})
}

// ListMemberGroupsForDropdown handles GET /api/v1/member-groups/dropdown
func (h *MemberGroupHandler) ListMemberGroupsForDropdown(c *gin.Context) {
	names, err := h.memberGroupService.ListMemberGroupsForDropdown(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"names": names,
		},
	})
}

// SetDefaultMemberGroup handles PUT /api/v1/member-groups/:id/set-default
func (h *MemberGroupHandler) SetDefaultMemberGroup(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid member group ID"))
		return
	}

	if err := h.memberGroupService.SetDefaultMemberGroup(c.Request.Context(), id); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "member group set as default successfully",
	})
}

// parseCreateFormData parses form data for CreateMemberGroupRequest
func parseCreateFormData(c *gin.Context) (member_group.CreateMemberGroupRequest, error) {
	var req member_group.CreateMemberGroupRequest

	// Basic fields
	req.Code = c.PostForm("code")
	req.Name = c.PostForm("name")

	// Member group type ID
	if memberGroupTypeIDStr := c.PostForm("member_group_type_id"); memberGroupTypeIDStr != "" {
		if memberGroupTypeID, err := strconv.Atoi(memberGroupTypeIDStr); err == nil {
			req.MemberGroupTypeID = &memberGroupTypeID
		}
	}

	// Numeric fields
	if minDepositStr := c.PostForm("min_deposit"); minDepositStr != "" {
		if minDeposit, err := strconv.ParseFloat(minDepositStr, 64); err == nil {
			req.MinDeposit = minDeposit
		}
	}

	if minWithdrawStr := c.PostForm("min_withdraw"); minWithdrawStr != "" {
		if minWithdraw, err := strconv.ParseFloat(minWithdrawStr, 64); err == nil {
			req.MinWithdraw = minWithdraw
		}
	}

	if maxDepositStr := c.PostForm("max_deposit"); maxDepositStr != "" {
		if maxDeposit, err := strconv.ParseFloat(maxDepositStr, 64); err == nil {
			req.MaxDeposit = maxDeposit
		}
	}

	// Boolean fields
	if isDefaultStr := c.PostForm("is_default"); isDefaultStr != "" {
		req.IsDefault = isDefaultStr == "true"
	}

	if isVIPStr := c.PostForm("is_vip"); isVIPStr != "" {
		req.IsVIP = isVIPStr == "true"
	}

	// Optional numeric fields
	if dailyWithdrawLimitStr := c.PostForm("daily_withdraw_limit"); dailyWithdrawLimitStr != "" {
		if dailyWithdrawLimit, err := strconv.Atoi(dailyWithdrawLimitStr); err == nil {
			req.DailyWithdrawLimit = &dailyWithdrawLimit
		}
	}

	if dailyWithdrawAmountLimitStr := c.PostForm("daily_withdraw_amount_limit"); dailyWithdrawAmountLimitStr != "" {
		if dailyWithdrawAmountLimit, err := strconv.ParseFloat(dailyWithdrawAmountLimitStr, 64); err == nil {
			req.DailyWithdrawAmountLimit = &dailyWithdrawAmountLimit
		}
	}

	// Commission group ID
	if commissionGroupIDStr := c.PostForm("commission_group_id"); commissionGroupIDStr != "" {
		if commissionGroupID, err := strconv.Atoi(commissionGroupIDStr); err == nil {
			req.CommissionGroupID = commissionGroupID
		}
	}

	// Deposit turnover fields
	req.DepositTurnoverType = member_group.DepositTurnoverType(c.PostForm("deposit_turnover_type"))

	if depositTurnoverAmountStr := c.PostForm("deposit_turnover_amount"); depositTurnoverAmountStr != "" {
		if depositTurnoverAmount, err := strconv.ParseFloat(depositTurnoverAmountStr, 64); err == nil {
			req.DepositTurnoverAmount = depositTurnoverAmount
		}
	}

	req.DepositTurnoverReleaseType = member_group.DepositTurnoverReleaseType(c.PostForm("deposit_turnover_release_type"))

	if depositTurnoverReleaseAmountStr := c.PostForm("deposit_turnover_release_amount"); depositTurnoverReleaseAmountStr != "" {
		if depositTurnoverReleaseAmount, err := strconv.ParseFloat(depositTurnoverReleaseAmountStr, 64); err == nil {
			req.DepositTurnoverReleaseAmount = depositTurnoverReleaseAmount
		}
	}

	if calculateMinDepositStr := c.PostForm("calculate_min_deposit"); calculateMinDepositStr != "" {
		if calculateMinDeposit, err := strconv.ParseFloat(calculateMinDepositStr, 64); err == nil && calculateMinDeposit >= 0 {
			req.CalculateMinDeposit = calculateMinDeposit
		}
	}

	// Image field
	if image := c.PostForm("image"); image != "" {
		req.Image = &image
	}

	// Parse withdrawal approvals array
	req.WithdrawalApprovals = parseWithdrawalApprovals(c)

	// Parse deposit account IDs array
	req.DepositAccountIDs = parseDepositAccountIDs(c)

	return req, nil
}

// parseWithdrawalApprovals parses withdrawal_approvals array from form data
func parseWithdrawalApprovals(c *gin.Context) []member_group.CreateWithdrawalApprovalRequest {
	var approvals []member_group.CreateWithdrawalApprovalRequest

	// Parse indexed form fields: withdrawal_approvals[0][min_amount], withdrawal_approvals[0][max_amount], etc.
	for i := 0; ; i++ {
		minAmountKey := fmt.Sprintf("withdrawal_approvals[%d][min_amount]", i)
		maxAmountKey := fmt.Sprintf("withdrawal_approvals[%d][max_amount]", i)
		userRoleIDKey := fmt.Sprintf("withdrawal_approvals[%d][user_role_id]", i)
		userRoleIDsKey := fmt.Sprintf("withdrawal_approvals[%d][user_role_ids]", i)

		minAmountStr := c.PostForm(minAmountKey)
		maxAmountStr := c.PostForm(maxAmountKey)
		userRoleIDStr := c.PostForm(userRoleIDKey)
		userRoleIDsStr := c.PostForm(userRoleIDsKey)

		// If no more entries, break
		if minAmountStr == "" && maxAmountStr == "" && userRoleIDStr == "" && userRoleIDsStr == "" {
			break
		}

		var approval member_group.CreateWithdrawalApprovalRequest

		if minAmount, err := strconv.ParseFloat(minAmountStr, 64); err == nil {
			approval.MinAmount = minAmount
		}

		if maxAmount, err := strconv.ParseFloat(maxAmountStr, 64); err == nil {
			approval.MaxAmount = maxAmount
		}

		// Handle user_role_ids array (preferred)
		if userRoleIDsStr != "" {
			// Parse comma-separated values or JSON array
			userRoleIDsStr = strings.Trim(userRoleIDsStr, "[]")
			if userRoleIDsStr != "" {
				userRoleIDParts := strings.Split(userRoleIDsStr, ",")
				for _, part := range userRoleIDParts {
					if userRoleID, err := strconv.Atoi(strings.TrimSpace(part)); err == nil {
						approval.UserRoleIDs = append(approval.UserRoleIDs, userRoleID)
					}
				}
			}
		}

		// Backward compatibility: handle single user_role_id if user_role_ids is not provided
		if len(approval.UserRoleIDs) == 0 && userRoleIDStr != "" {
			if userRoleID, err := strconv.Atoi(userRoleIDStr); err == nil {
				approval.UserRoleIDs = []int{userRoleID}
			}
		}

		approvals = append(approvals, approval)
	}

	return approvals
}

// parseDepositAccountIDs parses deposit_account_ids array from form data
func parseDepositAccountIDs(c *gin.Context) []int {
	var accountIDs []int

	// Parse indexed form fields: deposit_account_ids[0], deposit_account_ids[1], etc.
	for i := 0; ; i++ {
		key := fmt.Sprintf("deposit_account_ids[%d]", i)
		valueStr := c.PostForm(key)

		// If no more entries, break
		if valueStr == "" {
			break
		}

		if accountID, err := strconv.Atoi(valueStr); err == nil {
			accountIDs = append(accountIDs, accountID)
		}
	}

	return accountIDs
}

// parseUpdateFormData parses form data for UpdateMemberGroupRequest
func parseUpdateFormData(c *gin.Context) (member_group.UpdateMemberGroupRequest, error) {
	var req member_group.UpdateMemberGroupRequest

	// Basic fields
	req.Code = c.PostForm("code")
	req.Name = c.PostForm("name")

	// Member group type ID
	if memberGroupTypeIDStr := c.PostForm("member_group_type_id"); memberGroupTypeIDStr != "" {
		if memberGroupTypeID, err := strconv.Atoi(memberGroupTypeIDStr); err == nil {
			req.MemberGroupTypeID = &memberGroupTypeID
		}
	}

	// Numeric fields
	if minDepositStr := c.PostForm("min_deposit"); minDepositStr != "" {
		if minDeposit, err := strconv.ParseFloat(minDepositStr, 64); err == nil {
			req.MinDeposit = minDeposit
		}
	}

	if minWithdrawStr := c.PostForm("min_withdraw"); minWithdrawStr != "" {
		if minWithdraw, err := strconv.ParseFloat(minWithdrawStr, 64); err == nil {
			req.MinWithdraw = minWithdraw
		}
	}

	if maxDepositStr := c.PostForm("max_deposit"); maxDepositStr != "" {
		if maxDeposit, err := strconv.ParseFloat(maxDepositStr, 64); err == nil {
			req.MaxDeposit = maxDeposit
		}
	}

	// Boolean fields
	if isDefaultStr := c.PostForm("is_default"); isDefaultStr != "" {
		req.IsDefault = isDefaultStr == "true"
	}

	if isVIPStr := c.PostForm("is_vip"); isVIPStr != "" {
		req.IsVIP = isVIPStr == "true"
	}

	// Optional numeric fields
	if dailyWithdrawLimitStr := c.PostForm("daily_withdraw_limit"); dailyWithdrawLimitStr != "" {
		if dailyWithdrawLimit, err := strconv.Atoi(dailyWithdrawLimitStr); err == nil {
			req.DailyWithdrawLimit = &dailyWithdrawLimit
		}
	}

	if dailyWithdrawAmountLimitStr := c.PostForm("daily_withdraw_amount_limit"); dailyWithdrawAmountLimitStr != "" {
		if dailyWithdrawAmountLimit, err := strconv.ParseFloat(dailyWithdrawAmountLimitStr, 64); err == nil {
			req.DailyWithdrawAmountLimit = &dailyWithdrawAmountLimit
		}
	}

	// Commission group ID
	if commissionGroupIDStr := c.PostForm("commission_group_id"); commissionGroupIDStr != "" {
		if commissionGroupID, err := strconv.Atoi(commissionGroupIDStr); err == nil {
			req.CommissionGroupID = commissionGroupID
		}
	}

	// Deposit turnover fields
	req.DepositTurnoverType = member_group.DepositTurnoverType(c.PostForm("deposit_turnover_type"))

	if depositTurnoverAmountStr := c.PostForm("deposit_turnover_amount"); depositTurnoverAmountStr != "" {
		if depositTurnoverAmount, err := strconv.ParseFloat(depositTurnoverAmountStr, 64); err == nil {
			req.DepositTurnoverAmount = depositTurnoverAmount
		}
	}

	req.DepositTurnoverReleaseType = member_group.DepositTurnoverReleaseType(c.PostForm("deposit_turnover_release_type"))

	if depositTurnoverReleaseAmountStr := c.PostForm("deposit_turnover_release_amount"); depositTurnoverReleaseAmountStr != "" {
		if depositTurnoverReleaseAmount, err := strconv.ParseFloat(depositTurnoverReleaseAmountStr, 64); err == nil {
			req.DepositTurnoverReleaseAmount = depositTurnoverReleaseAmount
		}
	}

	if calculateMinDepositStr := c.PostForm("calculate_min_deposit"); calculateMinDepositStr != "" {
		if calculateMinDeposit, err := strconv.ParseFloat(calculateMinDepositStr, 64); err == nil && calculateMinDeposit >= 0 {
			req.CalculateMinDeposit = calculateMinDeposit
		}
	}

	// Image field
	if image := c.PostForm("image"); image != "" {
		req.Image = &image
	}

	// Image delete field
	if imageDeleteStr := c.PostForm("image_delete"); imageDeleteStr != "" {
		req.ImageDelete = imageDeleteStr == "true" || imageDeleteStr == "1"
	}

	// Parse withdrawal approvals array
	req.WithdrawalApprovals = parseWithdrawalApprovals(c)

	// Parse deposit account IDs array
	req.DepositAccountIDs = parseDepositAccountIDs(c)

	return req, nil
}
