package http

import (
	"blacking-api/internal/service"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"net/http"
)

type AutoBotHandler struct {
	autoBotService service.AutoBotService
	logger         logger.Logger
}

func NewAutoBotHandler(autoBotService service.AutoBotService, logger logger.Logger) *AutoBotHandler {
	return &AutoBotHandler{
		autoBotService: autoBotService,
		logger:         logger,
	}
}

// ListAutoBots lists all available auto bots
// @Summary List auto bots
// @Description Get a list of all available auto bots for banking operations
// @Tags Banking Management
// @Produce json
// @Security BearerAuth
// @Success 200 {object} object{success=bool,data=[]object} "Auto bots retrieved successfully"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /auto-bot [get]
func (h *AutoBotHandler) ListAutoBots(c *gin.Context) {
	autoBots, err := h.autoBotService.ListAutoBots(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    autoBots,
	})
}
