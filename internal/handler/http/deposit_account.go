package http

import (
	"blacking-api/internal/domain/deposit_account"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"net/http"
	"strconv"
)

type DepositAccountHandler struct {
	depositAccountService service.DepositAccountService
	logger                logger.Logger
}

func NewDepositAccountHandler(depositAccountService service.DepositAccountService, logger logger.Logger) *DepositAccountHandler {
	return &DepositAccountHandler{
		depositAccountService: depositAccountService,
		logger:                logger,
	}
}

// @Summary Create deposit account
// @Description Create a new deposit account for processing deposits
// @Tags Account Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body object true "Deposit account creation request"
// @Success 201 {object} object{success=bool,Message=string} "Deposit account created successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /deposit-account [post]
func (h *DepositAccountHandler) CreateDepositAccount(c *gin.Context) {
	var req *deposit_account.DepositAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	// Custom validation for AutoTransfer fields
	if err := req.ValidateAutoTransferFields(); err != nil {
		HandleSingleError(c, err)
		return
	}

	err := h.depositAccountService.CreateDepositAccount(c.Request.Context(), req)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"Message": "Deposit account created successfully",
	})
}

// @Summary List deposit accounts
// @Description Get a list of deposit accounts with optional search and pagination
// @Tags Account Management
// @Produce json
// @Security BearerAuth
// @Param limit query int false "Limit"
// @Param offset query int false "Offset"
// @Param search query string false "Search term"
// @Success 200 {object} object{success=bool,data=[]object} "Deposit accounts retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /deposit-account [get]
func (h *DepositAccountHandler) GetDepositAccounts(c *gin.Context) {
	var req *deposit_account.DepositAccountSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	depositAccount, err := h.depositAccountService.FindAllDepositAccounts(c.Request.Context(), req)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    depositAccount,
	})
}

// @Summary Get deposit account by ID
// @Description Get a specific deposit account by its ID
// @Tags Account Management
// @Produce json
// @Security BearerAuth
// @Param id path string true "Deposit Account ID"
// @Success 200 {object} object{success=bool,data=object} "Deposit account retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Deposit account not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /deposit-account/{id} [get]
func (h *DepositAccountHandler) GetDepositAccountByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("deposit account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	depositAccount, err := h.depositAccountService.FindDepositAccountByID(c.Request.Context(), idInt)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    depositAccount,
	})
}

func (h *DepositAccountHandler) GetDepositAccountSettingAlgorithmByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("deposit account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	depositAccount, err := h.depositAccountService.FindDepositAccountSettingAlgorithmByID(c.Request.Context(), idInt)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    depositAccount,
	})
}

// @Summary Update deposit account
// @Description Update an existing deposit account
// @Tags Account Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Deposit Account ID"
// @Param request body object true "Deposit account update request"
// @Success 200 {object} object{success=bool,Message=string} "Deposit account updated successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Deposit account not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /deposit-account/{id} [put]
func (h *DepositAccountHandler) UpdateDepositAccount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("deposit account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *deposit_account.DepositAccountUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, errors.NewValidationError("invalid request body"))
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	// Custom validation for AutoTransfer fields
	if err := req.ValidateAutoTransferFields(); err != nil {
		HandleSingleError(c, err)
		return
	}

	err = h.depositAccountService.UpdateDepositAccount(c.Request.Context(), idInt, req)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"Message": "Deposit account updated successfully",
	})
}

func (h *DepositAccountHandler) UpdateDepositAccountAutoBot(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("deposit account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	autoBotIDStr := c.Query("bot")
	if autoBotIDStr == "" {
		HandleSingleError(c, errors.NewValidationError("bot ID is required"))
		return
	}

	autoBotID, err := strconv.ParseInt(autoBotIDStr, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	err = h.depositAccountService.UpdateDepositAccountAutoBot(c.Request.Context(), idInt, autoBotID)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"Message": "Deposit account auto bot updated successfully",
	})
}

func (h *DepositAccountHandler) UpdateDepositAccountAlgorithm(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("deposit account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *deposit_account.DepositAccountSettingAlgorithm
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, errors.NewValidationError("invalid request body"))
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	err = h.depositAccountService.UpdateDepositAccountAlgorithm(c.Request.Context(), idInt, req)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"Message": "Deposit account algorithm updated successfully",
	})
}

func (h *DepositAccountHandler) ActiveDepositAccount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("deposit account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	statusStr := c.Query("status")
	if statusStr == "" {
		HandleSingleError(c, errors.NewValidationError("status is required"))
		return
	}

	status, err := strconv.ParseBool(statusStr)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	err = h.depositAccountService.ActiveDepositAccount(c.Request.Context(), idInt, status)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"Message": "Deposit account status updated successfully",
	})
}

// @Summary Delete deposit account
// @Description Delete a deposit account by ID
// @Tags Account Management
// @Produce json
// @Security BearerAuth
// @Param id path string true "Deposit Account ID"
// @Success 200 {object} object{success=bool,Message=string} "Deposit account deleted successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Deposit account not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /deposit-account/{id} [delete]
func (h *DepositAccountHandler) DeleteDepositAccount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("deposit account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	err = h.depositAccountService.DeleteDepositAccount(c.Request.Context(), idInt)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"Message": "Deposit account deleted successfully",
	})
}

// @Summary Get account transfer types
// @Description Get list of all account transfer types
// @Tags Account Management
// @Produce json
// @Security BearerAuth
// @Success 200 {object} object{success=bool,message=string,data=[]deposit_account.AccountTransferTypeResponse} "Account transfer types retrieved successfully"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /deposit-account/account-transfer-types [get]
func (h *DepositAccountHandler) GetAccountTransferTypes(c *gin.Context) {
	accountTransferTypes, err := h.depositAccountService.GetAccountTransferTypes(c.Request.Context())
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Account transfer types retrieved successfully",
		"data":    accountTransferTypes,
	})
}
