package http

import (
	"net/http"
	"strconv"

	"blacking-api/internal/domain/summary_bet_by_member"
	"blacking-api/internal/service"

	"github.com/gin-gonic/gin"
)

type SummaryBetByMemberHandler struct {
	summaryBetByMemberService *service.SummaryBetByMemberService
}

func NewSummaryBetByMemberHandler(summaryBetByMemberService *service.SummaryBetByMemberService) *SummaryBetByMemberHandler {
	return &SummaryBetByMemberHandler{
		summaryBetByMemberService: summaryBetByMemberService,
	}
}

// GetSummaryBetByMemberWithFilter godoc
// @Summary Get summary bet by member reports with filters
// @Description ข้อมูลรายงานสรุปเดิมพันแยกตาม Member พร้อมฟิลเตอร์ (รองรับทุก parameters)
// @Tags Reports
// @Accept json
// @Produce json
// @Param date_register query string false "Registration date" example(2025-01-01)
// @Param date_search query string false "Date search range" example(2025-08-01 to 2025-08-18)
// @Param first_deposit query string false "First deposit filter" example(yes)
// @Param game_type_id query int false "Game type ID" example(1)
// @Param partner_id query int false "Partner ID" example(1)
// @Param phone query string false "Phone number" example(**********)
// @Param provider_code query string false "Provider code" example(PGT)
// @Param showAll query int false "Show all records" example(1)
// @Param user_code query string false "User code" example(ZAB1E1PM15362)
// @Param user_groups query string false "User groups" example(vip)
// @Param username query string false "Username" example(testuser)
// @Param sort query string false "Sort order" example(desc)
// @Param sortBy query string false "Sort by field" example(created_at)
// @Success 200 {object} summary_bet_by_member.SummaryBetByMemberResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/reports/summary-bet-by-member [get]
func (h *SummaryBetByMemberHandler) GetSummaryBetByMemberWithFilter(c *gin.Context) {
	ctx := c.Request.Context()

	// Validate request parameters
	var req summary_bet_by_member.SummaryBetByMemberFilterRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Message: "Invalid request parameters",
			Data:    err.Error(),
		})
		return
	}

	// No validation needed as per user request - allow empty values

	summaryBetByMemberData, err := h.summaryBetByMemberService.GetSummaryBetByMember(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Message: "Failed to get summary bet by member reports",
			Data:    err.Error(),
		})
		return
	}

	response := summary_bet_by_member.SummaryBetByMemberResponse{
		Code:    http.StatusOK,
		Success: true,
		Message: "Summary bet by member reports retrieved successfully",
		Data:    *summaryBetByMemberData,
	}

	c.JSON(http.StatusOK, response)
}

// GetSummaryBetByMemberDetail godoc
// @Summary Get summary bet by member report detail
// @Description ข้อมูลรายละเอียดรายงานสรุปเดิมพันแยกตาม Member
// @Tags Reports
// @Accept json
// @Produce json
// @Param member_id path int true "Member ID" example(15362)
// @Success 200 {object} summary_bet_by_member.SummaryBetByMemberDetailResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/reports/summary-bet-by-member/{member_id} [get]
func (h *SummaryBetByMemberHandler) GetSummaryBetByMemberDetail(c *gin.Context) {
	ctx := c.Request.Context()

	// Get member_id from URL parameter
	memberIDStr := c.Param("member_id")
	memberID, err := strconv.Atoi(memberIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Message: "Invalid member ID",
			Data:    "Member ID must be a valid integer",
		})
		return
	}

	// Validate request parameters
	var req summary_bet_by_member.SummaryBetByMemberDetailRequest
	req.MemberID = memberID
	if err := c.ShouldBindUri(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Message: "Invalid request parameters",
			Data:    err.Error(),
		})
		return
	}

	summaryBetByMemberDetailData, err := h.summaryBetByMemberService.GetSummaryBetByMemberDetail(ctx, req.MemberID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Message: "Failed to get summary bet by member detail",
			Data:    err.Error(),
		})
		return
	}

	response := summary_bet_by_member.SummaryBetByMemberDetailResponse{
		Code:    http.StatusOK,
		Success: true,
		Message: "Summary bet by member detail retrieved successfully",
		Data:    *summaryBetByMemberDetailData,
	}

	c.JSON(http.StatusOK, response)
}
