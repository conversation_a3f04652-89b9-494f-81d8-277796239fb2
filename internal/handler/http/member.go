package http

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"

	"blacking-api/internal/config"
	"blacking-api/internal/domain/member"
	"blacking-api/internal/middleware"
	"blacking-api/internal/service"
	"blacking-api/pkg/auth"
	"blacking-api/pkg/errors"

	"github.com/gin-gonic/gin"
)

// Helper function to get string value from *string
func getUsernameString(username *string) string {
	if username != nil {
		return *username
	}
	return ""
}

type MemberHandler struct {
	memberService service.MemberService
	config        *config.Config
}

func NewMemberHandler(memberService service.MemberService, config *config.Config) *MemberHandler {
	return &MemberHandler{
		memberService: memberService,
		config:        config,
	}
}

// CreateMember creates a new member account
// @Summary Create member
// @Description Create a new member account by admin
// @Tags Member Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body member.CreateMemberRequest true "Create member request"
// @Success 201 {object} object{success=bool,data=member.MemberResponse} "Member created successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /members [post]
func (h *MemberHandler) CreateMember(c *gin.Context) {
	var req member.CreateMemberRequest
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	memberResp, err := h.memberService.CreateMember(c.Request.Context(), req, strconv.Itoa(adminId), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    memberResp,
	})
}

// GetMember gets a member by ID
// @Summary Get member by ID
// @Description Get a specific member by their ID
// @Tags Member Management
// @Produce json
// @Security BearerAuth
// @Param id path string true "Member ID"
// @Success 200 {object} object{success=bool,data=member.MemberResponse} "Member retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Member not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /members/{id} [get]
func (h *MemberHandler) GetMember(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("member ID is required"))
		return
	}

	memberResp, err := h.memberService.GetMemberByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    memberResp,
	})
}

// GetMemberByUsername gets a member by username
// @Summary Get member by username
// @Description Get a member by their username
// @Tags Member Management
// @Produce json
// @Security BearerAuth
// @Param username path string true "Member username"
// @Success 200 {object} object{success=bool,data=member.MemberResponse} "Member retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Member not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/members/username/{username} [get]
func (h *MemberHandler) GetMemberByUsername(c *gin.Context) {
	username := c.Param("username")
	if username == "" {
		c.Error(errors.NewValidationError("username is required"))
		return
	}

	memberResp, err := h.memberService.GetMemberByUsername(c.Request.Context(), username)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    memberResp,
	})
}

// GetMemberByGameUsername gets a member by game username
// @Summary Get member by game username
// @Description Get a member by their game username
// @Tags Member Management
// @Produce json
// @Security BearerAuth
// @Param game_username path string true "Member game username"
// @Success 200 {object} object{success=bool,data=member.MemberResponse} "Member retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Member not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/members/game-username/{game_username} [get]
func (h *MemberHandler) GetMemberByGameUsername(c *gin.Context) {
	gameUsername := c.Param("game_username")
	if gameUsername == "" {
		c.Error(errors.NewValidationError("game username is required"))
		return
	}

	memberResp, err := h.memberService.GetMemberByGameUsername(c.Request.Context(), gameUsername)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    memberResp,
	})
}

// UpdateMember updates an existing member
// @Summary Update member
// @Description Update an existing member account by ID
// @Tags Member Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Member ID"
// @Param request body member.UpdateMemberRequest true "Update member request"
// @Success 200 {object} object{success=bool,data=member.MemberResponse} "Member updated successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Member not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/members/{id} [put]
func (h *MemberHandler) UpdateMember(c *gin.Context) {
	id := c.Param("id")
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if id == "" {
		c.Error(errors.NewValidationError("member ID is required"))
		return
	}

	var req member.UpdateMemberRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	memberResp, err := h.memberService.UpdateMember(c.Request.Context(), id, req, strconv.Itoa(adminId), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    memberResp,
	})
}

// DeleteMember deletes a member account
// @Summary Delete member
// @Description Delete a member account by ID (soft delete)
// @Tags Member Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Member ID"
// @Param request body member.DeleteMemberRequest true "Delete member request"
// @Success 200 {object} object{success=bool,message=string} "Member deleted successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Member not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/members/{id}/delete [post]
func (h *MemberHandler) DeleteMember(c *gin.Context) {
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("member ID is required"))
		return
	}

	var req member.DeleteMemberRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.memberService.DeleteMember(c.Request.Context(), id, req, strconv.Itoa(adminId), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Member deleted successfully",
	})
}

func (h *MemberHandler) ListMembers(c *gin.Context) {
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")
	search := c.Query("search")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100 // Max limit
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	members, total, err := h.memberService.ListMembers(c.Request.Context(), limit, offset, search)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"members": members,
			"total":   total,
			"limit":   limit,
			"offset":  offset,
		},
	})
}

func (h *MemberHandler) ListMembersWithFilter(c *gin.Context) {
	// Parse pagination parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// Parse filter parameters
	filter := &member.MemberFilter{
		Search:        c.Query("search"),
		Username:      c.Query("username"),
		FullName:      c.Query("full_name"),
		BankNumber:    c.Query("bank_number"),
		ReferUserName: c.Query("refer_user_name"),
		CreatedAt:     getStringPtr(c.Query("created_at")),
		OrderBy:       c.DefaultQuery("order_by", "id"),
		OrderDir:      c.DefaultQuery("order_dir", "desc"),
	}

	// Parse integer filters
	if memberGroupIDStr := c.Query("member_group_id"); memberGroupIDStr != "" {
		if memberGroupID, err := strconv.Atoi(memberGroupIDStr); err == nil {
			filter.MemberGroupID = &memberGroupID
		}
	}

	if referUserIDStr := c.Query("refer_user_id"); referUserIDStr != "" {
		if referUserID, err := strconv.Atoi(referUserIDStr); err == nil {
			filter.ReferUserID = &referUserID
		}
	}

	if partnerIDStr := c.Query("partner_id"); partnerIDStr != "" {
		if partnerID, err := strconv.Atoi(partnerIDStr); err == nil {
			filter.PartnerID = &partnerID
		}
	}

	if createdByStr := c.Query("created_by"); createdByStr != "" {
		if createdBy, err := strconv.Atoi(createdByStr); err == nil {
			filter.CreatedBy = &createdBy
		}
	}

	members, total, err := h.memberService.ListMembersWithFilter(c.Request.Context(), limit, offset, filter)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"members": members,
			"total":   total,
			"limit":   limit,
			"offset":  offset,
		},
	})
}

// GetCurrentUser gets the current authenticated member from JWT token
// @Summary Get current user
// @Description Get current authenticated member information from JWT token in cookie
// @Tags Member Area
// @Produce json
// @Security MemberAuth
// @Success 200 {object} object{success=bool,data=member.MemberResponse} "Current user retrieved successfully"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /member/me [get]
func (h *MemberHandler) GetCurrentUser(c *gin.Context) {
	// Get member info from JWT context
	authMember, exists := c.Get("auth-member")
	if !exists {
		c.Error(errors.NewUnauthorizedError("member not authenticated"))
		return
	}

	memberClaims, ok := authMember.(*middleware.MemberLoginClaims)
	if !ok {
		c.Error(errors.NewUnauthorizedError("invalid member authentication"))
		return
	}

	// Log member ID from JWT for debugging
	memberID := strconv.Itoa(memberClaims.ID)
	log.Printf("GetCurrentUser: member ID from JWT: %d (string: %s), phone: %s, username: %s",
		memberClaims.ID, memberID, getUsernameString(memberClaims.Phone), getUsernameString(memberClaims.Username))

	// Get member profile with synced game data (wallet and providers)
	memberResp, err := h.memberService.GetMemberProfileWithGameData(c.Request.Context(), memberID)
	if err != nil {
		log.Printf("GetCurrentUser: failed to get member profile for ID %s: %v", memberID, err)
		c.Error(err)
		return
	}

	// Fallback: Check if member has valid game_username, if not try to backfill
	gameUsernameStr := "nil"
	if memberResp.GameUsername != nil {
		gameUsernameStr = *memberResp.GameUsername
	}

	// Only check fallback if game_username is missing or doesn't start with "bk"
	if memberResp.GameUsername == nil || *memberResp.GameUsername == "" || !strings.HasPrefix(*memberResp.GameUsername, "bk") {
		log.Printf("GetCurrentUser: member %s has invalid game_username '%s', attempting backfill", memberID, gameUsernameStr)

		// Try to backfill game_username
		gameUsername, backfillErr := h.memberService.BackfillGameUsernameV2(c.Request.Context(), memberID)
		if backfillErr != nil {
			log.Printf("GetCurrentUser: backfill failed for member %s: %v", memberID, backfillErr)
			// Don't fail the /me request, just log the warning
		} else {
			log.Printf("GetCurrentUser: successfully backfilled game_username %s for member %s", gameUsername, memberID)
			// Update response with new game_username
			memberResp.GameUsername = &gameUsername
		}
	} else {
		log.Printf("GetCurrentUser: member %s has valid game_username '%s', skipping backfill", memberID, gameUsernameStr)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    memberResp,
	})
}

// UpdateMemberBankInfo updates member's bank information by admin
// @Summary Update member bank information by admin
// @Description Update member's bank information (bank code, bank number, names, TrueMoney username) by admin with audit logging
// @Tags Member Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Member ID"
// @Param request body member.UpdateBankInfoRequest true "Bank info request"
// @Success 200 {object} object{success=bool,message=string} "Bank info updated successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Member not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /members/{id}/update-bank-info [post]
func (h *MemberHandler) UpdateMemberBankInfo(c *gin.Context) {
	memberID := c.Param("id")
	if memberID == "" {
		c.Error(errors.NewValidationError("member ID is required"))
		return
	}

	adminID := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	var req member.UpdateBankInfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.memberService.UpdateMemberBankInfo(c.Request.Context(), memberID, req, strconv.Itoa(adminID), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Member bank information updated successfully",
	})
}

func getStringPtr(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

// GetMemberStatusCounts returns count of members by status
// @Summary Get member status counts
// @Description Get count of members grouped by status (active, suspended, banned) and total count
// @Tags Member Management
// @Produce json
// @Success 200 {object} object{success=bool,data=member.MemberStatusCountsResponse,message=string} "Member status counts retrieved successfully"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /members/status-counts [get]
// @Security BearerAuth
func (h *MemberHandler) GetMemberStatusCounts(c *gin.Context) {
	statusCounts, err := h.memberService.GetMemberStatusCounts(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    statusCounts,
		"message": "Member status counts retrieved successfully",
	})
}

// ChangeMemberPassword changes member password by admin
// @Summary Change member password by admin
// @Description Change member password by admin with audit logging
// @Tags Member Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Member ID"
// @Param request body member.ChangeMemberPasswordRequest true "Change password request"
// @Success 200 {object} object{success=bool,message=string} "Password changed successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Member not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /members/{id}/change-password [post]
func (h *MemberHandler) ChangeMemberPassword(c *gin.Context) {
	memberID := c.Param("id")
	if memberID == "" {
		c.Error(errors.NewValidationError("member ID is required"))
		return
	}

	adminID := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	var req member.ChangeMemberPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.memberService.ChangeMemberPassword(c.Request.Context(), memberID, req, strconv.Itoa(adminID), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Member password changed successfully",
	})
}

// ChangeMemberPartner changes member partner by admin
// @Summary Change member partner by admin
// @Description Change member partner (refer_user_id) by admin with audit logging and action remark
// @Tags Member Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Member ID"
// @Param request body member.ChangeMemberPartnerRequest true "Change partner request"
// @Success 200 {object} object{success=bool,message=string} "Partner changed successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Member not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /members/{id}/change-partner [post]
func (h *MemberHandler) ChangeMemberPartner(c *gin.Context) {
	memberID := c.Param("id")
	if memberID == "" {
		c.Error(errors.NewValidationError("member ID is required"))
		return
	}

	adminID := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	var req member.ChangeMemberPartnerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.memberService.ChangeMemberPartner(c.Request.Context(), memberID, req, strconv.Itoa(adminID), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Member partner changed successfully",
	})
}

// SuspendMember toggles member suspension status by admin
// @Summary Toggle member suspension status by admin
// @Description Toggle member suspension status (suspend if active, unsuspend if suspended) by admin with audit logging and action remark preservation
// @Tags Member Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Member ID"
// @Param request body member.SuspendMemberRequest true "Suspend/Unsuspend member request"
// @Success 200 {object} object{success=bool,message=string} "Member status toggled successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Member not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /members/{id}/suspend [patch]
func (h *MemberHandler) SuspendMember(c *gin.Context) {
	memberID := c.Param("id")
	if memberID == "" {
		c.Error(errors.NewValidationError("member ID is required"))
		return
	}

	adminID := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	var req member.SuspendMemberRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.memberService.SuspendMember(c.Request.Context(), memberID, req, strconv.Itoa(adminID), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Member status updated successfully",
	})
}

// GetGameProviderData gets provider member code by phone and provider
// @Summary Get game provider data
// @Description Get provider member code by fetching member data from external API and filtering by provider
// @Tags Member Auth
// @Accept json
// @Produce json
// @Param request body member.GameProviderRequest true "Game provider request"
// @Success 200 {object} object{success=bool,data=string} "Provider member code retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Provider not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /member-auth/game [post]
func (h *MemberHandler) GetGameProviderData(c *gin.Context) {
	var req member.GameProviderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	// Call external API to get member data
	url := fmt.Sprintf("%s/members/by-phone/%s", h.config.Agent.BaseURL, req.Phone)
	httpReq, err := http.NewRequestWithContext(c.Request.Context(), "GET", url, nil)
	if err != nil {
		c.Error(errors.NewInternalError("failed to create request"))
		return
	}

	// Add Basic Auth header using config
	auth := fmt.Sprintf("%s:%s", h.config.Agent.LineCode, h.config.Agent.SecretKey)
	authHeader := "Basic " + base64.StdEncoding.EncodeToString([]byte(auth))
	httpReq.Header.Set("Authorization", authHeader)

	client := &http.Client{}
	resp, err := client.Do(httpReq)
	if err != nil {
		c.Error(errors.NewInternalError("failed to call external API"))
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		c.Error(errors.NewNotFoundError("member not found"))
		return
	}

	var apiResponse member.ExternalAPIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		c.Error(errors.NewInternalError("failed to decode response"))
		return
	}

	// Find the provider in the array
	//var providerMemberCode string
	//for _, provider := range apiResponse.Data.Providers {
	//	if provider.ProviderCode == req.Provider {
	//		// Use ProviderMemberCode if available, otherwise use ProviderUsername
	//		if provider.ProviderMemberCode != "" {
	//			providerMemberCode = provider.ProviderMemberCode
	//		} else {
	//			providerMemberCode = provider.ProviderUsername
	//		}
	//		break
	//	}
	//}
	//
	//if providerMemberCode == "" {
	//	c.Error(errors.NewNotFoundError("provider not found"))
	//	return
	//}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    "bk0m33",
	})
}

// GetGameProviders handles getting game providers for a member
// @Summary Get game providers for member
// @Description Get available game providers for a specific member from Game API
// @Tags Member
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Member ID"
// @Success 200 {object} object{success=bool,data=interface{},message=string} "Game providers retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Member not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/members/{id}/game-providers [get]
func (h *MemberHandler) GetGameProviders(c *gin.Context) {
	memberID := c.Param("id")
	if memberID == "" {
		c.Error(errors.NewValidationError("member ID is required"))
		return
	}

	gameProviders, err := h.memberService.GetGameProviders(c.Request.Context(), memberID)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    gameProviders,
		"message": "Game providers retrieved successfully",
	})
}

// GetGameProviderGroups handles getting game provider groups
// @Summary Get game provider groups
// @Description Get available game provider groups from game API
// @Tags Member Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} object{success=bool,data=interface{},message=string} "Game provider groups retrieved successfully"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/game-provider-groups [get]
func (h *MemberHandler) GetGameProviderGroups(c *gin.Context) {
	gameProviderGroups, err := h.memberService.GetGameProviderGroups(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    gameProviderGroups,
		"message": "Game provider groups retrieved successfully",
	})
}

// GetGameLists handles getting game lists for a member by provider
// @Summary Get game lists for member by provider
// @Description Get available game lists for a specific member and provider from Game API
// @Tags Member
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Member ID"
// @Param provider path string true "Provider code (e.g., JILI, PRAGMATIC)"
// @Success 200 {object} object{success=bool,data=interface{},message=string} "Game lists retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Member not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/members/{id}/game-lists/{provider} [get]
func (h *MemberHandler) GetGameLists(c *gin.Context) {
	memberID := c.Param("id")
	if memberID == "" {
		c.Error(errors.NewValidationError("member ID is required"))
		return
	}

	provider := c.Param("provider")
	if provider == "" {
		c.Error(errors.NewValidationError("provider is required"))
		return
	}

	gameLists, err := h.memberService.GetGameLists(c.Request.Context(), memberID, provider)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    gameLists,
		"message": "Game lists retrieved successfully",
	})
}

// GetGameProviderInfo handles getting specific game provider info for a member
// @Summary Get game provider info for member
// @Description Get specific game provider information for a member from Game API
// @Tags Member
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Member ID"
// @Param provider path string true "Provider code (e.g., JILI, PRAGMATIC)"
// @Success 200 {object} object{success=bool,data=interface{},message=string} "Game provider info retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Member not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/members/{id}/game-providers/{provider} [get]
func (h *MemberHandler) GetGameProviderInfo(c *gin.Context) {
	memberID := c.Param("id")
	if memberID == "" {
		c.Error(errors.NewValidationError("member ID is required"))
		return
	}

	provider := c.Param("provider")
	if provider == "" {
		c.Error(errors.NewValidationError("provider is required"))
		return
	}

	gameProviderInfo, err := h.memberService.GetGameProviderInfo(c.Request.Context(), memberID, provider)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    gameProviderInfo,
		"message": "Game provider info retrieved successfully",
	})
}

// BackfillGameUsername handles backfilling game_username for members without it
// @Summary Backfill game_username for member
// @Description Backfills game_username by calling AG API to create user and get game_username
// @Tags Member
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Member ID"
// @Success 200 {object} object{success=bool,data=object{game_username=string},message=string} "Game username backfilled successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Member not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/members/{id}/backfill-game-username [post]
func (h *MemberHandler) BackfillGameUsername(c *gin.Context) {
	memberID := c.Param("id")
	if memberID == "" {
		c.Error(errors.NewValidationError("member ID is required"))
		return
	}

	gameUsername, err := h.memberService.BackfillGameUsernameV2(c.Request.Context(), memberID)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"game_username": gameUsername,
		},
		"message": "Game username backfilled successfully",
	})
}
