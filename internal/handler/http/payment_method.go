package http

import (
	"blacking-api/internal/domain/payment_method"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"strconv"
)

type PaymentMethodHandler struct {
	paymentMethodService service.PaymentMethodService
	logger               logger.Logger
}

func NewPaymentMethodHandler(paymentMethodService service.PaymentMethodService, logger logger.Logger) *PaymentMethodHandler {
	return &PaymentMethodHandler{
		paymentMethodService: paymentMethodService,
		logger:               logger,
	}
}

// @Summary Create payment method
// @Description Create a new payment method for processing transactions
// @Tags Payment Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body object true "Payment method creation request"
// @Success 201 {object} object{success=bool,message=string} "Payment method created successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /payment-method [post]
func (h *PaymentMethodHandler) CreatePaymentMethod(c *gin.Context) {
	var req *payment_method.PaymentMethodRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.paymentMethodService.CreatePaymentMethod(c.Request.Context(), req); err != nil {
		h.logger.Error("Failed to create payment method", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(201, gin.H{
		"success": true,
		"message": "Payment method created successfully",
	})
}

// @Summary List payment methods
// @Description Get a list of all available payment methods
// @Tags Payment Management
// @Produce json
// @Security BearerAuth
// @Success 200 {object} object{success=bool,data=[]object} "Payment methods retrieved successfully"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /payment-method [get]
func (h *PaymentMethodHandler) ListPaymentMethods(c *gin.Context) {
	paymentMethods, err := h.paymentMethodService.ListPaymentMethods(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to list payment methods", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    paymentMethods,
	})
}

// @Summary Get payment method by ID
// @Description Get a specific payment method by its ID
// @Tags Payment Management
// @Produce json
// @Security BearerAuth
// @Param id path string true "Payment Method ID"
// @Success 200 {object} object{success=bool,data=object} "Payment method retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Payment method not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /payment-method/{id} [get]
func (h *PaymentMethodHandler) GetPaymentMethodByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("payment method ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	paymentMethod, err := h.paymentMethodService.FindPaymentMethodByID(c.Request.Context(), idInt)
	if err != nil {
		h.logger.Error("Failed to find payment method by ID", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    paymentMethod,
	})
}

// @Summary Update payment method
// @Description Update an existing payment method
// @Tags Payment Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Payment Method ID"
// @Param request body object true "Payment method update request"
// @Success 200 {object} object{success=bool,message=string} "Payment method updated successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Payment method not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /payment-method/{id} [put]
func (h *PaymentMethodHandler) UpdatePaymentMethod(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("payment method ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *payment_method.PaymentMethodRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.paymentMethodService.UpdatePaymentMethod(c.Request.Context(), idInt, req); err != nil {
		h.logger.Error("Failed to update payment method", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Payment method updated successfully",
	})
}

// @Summary Update payment method status
// @Description Update the status of a payment method (enable/disable)
// @Tags Payment Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Payment Method ID"
// @Param request body object true "Status update request"
// @Success 200 {object} object{success=bool,message=string} "Payment method status updated successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Payment method not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /payment-method/{id}/status [put]
func (h *PaymentMethodHandler) UpdatePaymentMethodStatus(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("payment method ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *payment_method.UpdateStatusRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.paymentMethodService.UpdatePaymentMethodStatus(c.Request.Context(), idInt, req); err != nil {
		h.logger.Error("Failed to update payment method status", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Payment method status updated successfully",
	})
}

// @Summary Delete payment method
// @Description Delete a payment method by ID
// @Tags Payment Management
// @Produce json
// @Security BearerAuth
// @Param id path string true "Payment Method ID"
// @Success 200 {object} object{success=bool,message=string} "Payment method deleted successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Payment method not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /payment-method/{id} [delete]
func (h *PaymentMethodHandler) DeletePaymentMethod(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("payment method ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.paymentMethodService.DeletePaymentMethod(c.Request.Context(), idInt); err != nil {
		h.logger.Error("Failed to delete payment method", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Payment method deleted successfully",
	})
}
