package http

import (
	"net/http"
	"strconv"
	"strings"
	"time"

	"blacking-api/internal/domain/customer_followup"
	"blacking-api/internal/domain/member"
	"blacking-api/internal/service"
	"blacking-api/pkg/auth"
	"blacking-api/pkg/errors"

	"github.com/gin-gonic/gin"
)

type CustomerFollowUpHandler struct {
	customerFollowUpService service.CustomerFollowUpService
}

func NewCustomerFollowUpHandler(customerFollowUpService service.CustomerFollowUpService) *CustomerFollowUpHandler {
	return &CustomerFollowUpHandler{
		customerFollowUpService: customerFollowUpService,
	}
}

// ListCustomerFollowUp lists customers for follow-up with comprehensive filters
// @Summary List customers for follow-up
// @Description Get list of customers for follow-up with various filters including tag, status, date range, etc.
// @Tags Customer Follow-Up
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param limit query int false "Limit (default 10, max 100)"
// @Param offset query int false "Offset (default 0)"
// @Param search query string false "Search in member info"
// @Param tag query string false "Follow-up tag filter"
// @Param status query string false "Follow-up status filter"
// @Param start_datetime query string false "Start date filter (YYYY-MM-DD HH:MM:SS)"
// @Param end_datetime query string false "End date filter (YYYY-MM-DD HH:MM:SS)"
// @Param partner_id query int false "Partner ID filter"
// @Param channel_id query int false "Channel ID filter"
// @Param platform_id query string false "Platform ID filter"
// @Param contacted_by query int false "Admin who contacted filter"
// @Param member_status query string false "Member status filter (active, inactive, suspended, banned)"
// @Param order_by query string false "Sort field: id, created_at, last_contact_at (default: id)"
// @Param order_dir query string false "Sort direction: asc, desc (default: desc)"
// @Success 200 {object} object{success=bool,data=object{customers=[]customer_followup.CustomerFollowUpResponse,total=int64,limit=int,offset=int}} "Customers retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /admin/customer-followup [get]
func (h *CustomerFollowUpHandler) ListCustomerFollowUp(c *gin.Context) {
	// Parse pagination parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}
	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// Parse filter parameters
	filter := &customer_followup.CustomerFollowUpFilter{
		Search:   c.Query("search"),
		Tag:      c.Query("tag"),
		Status:   c.Query("status"),
		OrderBy:  c.DefaultQuery("order_by", "id"),
		OrderDir: c.DefaultQuery("order_dir", "desc"),
	}

	// Parse date filters
	if startDateStr := c.Query("start_datetime"); startDateStr != "" {
		if startDate, err := time.Parse("2006-01-02 15:04:05", startDateStr); err == nil {
			filter.StartDateTime = &startDate
		}
	}
	if endDateStr := c.Query("end_datetime"); endDateStr != "" {
		if endDate, err := time.Parse("2006-01-02 15:04:05", endDateStr); err == nil {
			filter.EndDateTime = &endDate
		}
	}

	// Parse integer filters
	if partnerIDStr := c.Query("partner_id"); partnerIDStr != "" {
		if partnerID, err := strconv.Atoi(partnerIDStr); err == nil {
			filter.PartnerID = &partnerID
		}
	}
	if channelIDStr := c.Query("channel_id"); channelIDStr != "" {
		if channelID, err := strconv.Atoi(channelIDStr); err == nil {
			filter.ChannelID = &channelID
		}
	}
	if contactedByStr := c.Query("contacted_by"); contactedByStr != "" {
		if contactedBy, err := strconv.Atoi(contactedByStr); err == nil {
			filter.ContactedBy = &contactedBy
		}
	}

	// Parse platform ID
	if platformID := c.Query("platform_id"); platformID != "" {
		filter.PlatformID = &platformID
	}

	// Parse member status
	if memberStatusStr := c.Query("member_status"); memberStatusStr != "" {
		// Parse comma-separated member status values
		statusValues := strings.Split(memberStatusStr, ",")
		memberStatuses := make([]member.Status, 0, len(statusValues))
		for _, statusValue := range statusValues {
			statusValue = strings.TrimSpace(statusValue)
			if statusValue != "" {
				memberStatuses = append(memberStatuses, member.Status(statusValue))
			}
		}
		if len(memberStatuses) > 0 {
			filter.MemberStatus = memberStatuses
		}
	}

	// Get customers for follow-up
	customers, total, err := h.customerFollowUpService.ListCustomerFollowUp(c.Request.Context(), limit, offset, filter)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"customers": customers,
			"total":     total,
			"limit":     limit,
			"offset":    offset,
		},
	})
}

// ListOldCustomerFollowUp lists old customers for follow-up based on last_online filter
// @Summary List old customers for follow-up
// @Description Get list of old customers for follow-up with various filters including tag, status, last_online date range, etc.
// @Tags Customer Follow-Up
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param limit query int false "Limit (default 10, max 100)"
// @Param offset query int false "Offset (default 0)"
// @Param search query string false "Search in member info"
// @Param tag query string false "Follow-up tag filter"
// @Param status query string false "Follow-up status filter"
// @Param start_datetime query string false "Last online start date filter (YYYY-MM-DD HH:MM:SS)"
// @Param end_datetime query string false "Last online end date filter (YYYY-MM-DD HH:MM:SS)"
// @Param partner_id query int false "Partner ID filter"
// @Param channel_id query int false "Channel ID filter"
// @Param platform_id query string false "Platform ID filter"
// @Param contacted_by query int false "Admin who contacted filter"
// @Param member_status query string false "Member status filter (active, inactive, suspended, banned)"
// @Param order_by query string false "Sort field: id, last_online, last_contact_at (default: id)"
// @Param order_dir query string false "Sort direction: asc, desc (default: desc)"
// @Success 200 {object} object{success=bool,data=object{customers=[]customer_followup.CustomerFollowUpResponse,total=int64,limit=int,offset=int}} "Old customers retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /admin/customer-followup/search-old-customers [get]
func (h *CustomerFollowUpHandler) ListOldCustomerFollowUp(c *gin.Context) {
	// Parse pagination parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}
	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// Parse filter parameters
	filter := &customer_followup.CustomerFollowUpOldFilter{
		Search:   c.Query("search"),
		Tag:      c.Query("tag"),
		Status:   c.Query("status"),
		OrderBy:  c.DefaultQuery("order_by", "id"),
		OrderDir: c.DefaultQuery("order_dir", "desc"),
	}

	// Parse date filters (uses same parameters but filters last_online instead of created_at)
	if startDateStr := c.Query("start_datetime"); startDateStr != "" {
		if startDate, err := time.Parse("2006-01-02 15:04:05", startDateStr); err == nil {
			filter.StartDateTime = &startDate
		}
	}
	if endDateStr := c.Query("end_datetime"); endDateStr != "" {
		if endDate, err := time.Parse("2006-01-02 15:04:05", endDateStr); err == nil {
			filter.EndDateTime = &endDate
		}
	}

	// Parse integer filters
	if partnerIDStr := c.Query("partner_id"); partnerIDStr != "" {
		if partnerID, err := strconv.Atoi(partnerIDStr); err == nil {
			filter.PartnerID = &partnerID
		}
	}
	if channelIDStr := c.Query("channel_id"); channelIDStr != "" {
		if channelID, err := strconv.Atoi(channelIDStr); err == nil {
			filter.ChannelID = &channelID
		}
	}
	if contactedByStr := c.Query("contacted_by"); contactedByStr != "" {
		if contactedBy, err := strconv.Atoi(contactedByStr); err == nil {
			filter.ContactedBy = &contactedBy
		}
	}

	// Parse platform ID
	if platformID := c.Query("platform_id"); platformID != "" {
		filter.PlatformID = &platformID
	}

	// Parse member status
	if memberStatusStr := c.Query("member_status"); memberStatusStr != "" {
		// Parse comma-separated member status values
		statusValues := strings.Split(memberStatusStr, ",")
		memberStatuses := make([]member.Status, 0, len(statusValues))
		for _, statusValue := range statusValues {
			statusValue = strings.TrimSpace(statusValue)
			if statusValue != "" {
				memberStatuses = append(memberStatuses, member.Status(statusValue))
			}
		}
		if len(memberStatuses) > 0 {
			filter.MemberStatus = memberStatuses
		}
	}

	// Get old customers for follow-up
	customers, total, err := h.customerFollowUpService.ListOldCustomerFollowUp(c.Request.Context(), limit, offset, filter)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"customers": customers,
			"total":     total,
			"limit":     limit,
			"offset":    offset,
		},
	})
}

// TrackCustomerByCall tracks customer by phone call
// @Summary Track customer by call
// @Description Record a phone call to track customer follow-up
// @Tags Customer Follow-Up
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body customer_followup.TrackCustomerByCallRequest true "Track customer by call request"
// @Success 200 {object} object{success=bool,message=string} "Call tracked successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /admin/customer-followup/track-call [post]
func (h *CustomerFollowUpHandler) TrackCustomerByCall(c *gin.Context) {
	var req customer_followup.TrackCustomerByCallRequest
	adminID := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	// Validate admin ID
	if adminID == 0 {
		c.Error(errors.NewValidationError("invalid admin authentication: please login again"))
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.customerFollowUpService.TrackCustomerByCall(c.Request.Context(), req, adminID, adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Customer call tracked successfully",
	})
}

// TrackCustomerBySMS tracks customer by SMS
// @Summary Track customer by SMS
// @Description Record an SMS to track customer follow-up
// @Tags Customer Follow-Up
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body customer_followup.TrackCustomerBySMSRequest true "Track customer by SMS request"
// @Success 200 {object} object{success=bool,message=string} "SMS tracked successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /admin/customer-followup/track-sms [post]
func (h *CustomerFollowUpHandler) TrackCustomerBySMS(c *gin.Context) {
	var req customer_followup.TrackCustomerBySMSRequest
	adminID := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.customerFollowUpService.TrackCustomerBySMS(c.Request.Context(), req, adminID, adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Customer SMS tracked successfully",
	})
}

// UpdateFollowUpStatus updates member follow-up status
// @Summary Update follow-up status
// @Description Update member follow-up status (contacted, unreachable, not_contacted)
// @Tags Customer Follow-Up
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body customer_followup.UpdateFollowUpStatusRequest true "Update follow-up status request"
// @Success 200 {object} object{success=bool,message=string} "Follow-up status updated successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /admin/customer-followup/update-status [patch]
func (h *CustomerFollowUpHandler) UpdateFollowUpStatus(c *gin.Context) {
	var req customer_followup.UpdateFollowUpStatusRequest
	adminID := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.customerFollowUpService.UpdateFollowUpStatus(c.Request.Context(), req, adminID, adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Follow-up status updated successfully",
	})
}

// UpdateFollowUpTag sets tag for member
// @Summary Set follow-up tag
// @Description Set follow-up tag for member (cut_off, call_later, etc.)
// @Tags Customer Follow-Up
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body customer_followup.UpdateFollowUpTagRequest true "Update follow-up tag request"
// @Success 200 {object} object{success=bool,message=string} "Follow-up tag updated successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /admin/customer-followup/update-tag [patch]
func (h *CustomerFollowUpHandler) UpdateFollowUpTag(c *gin.Context) {
	var req customer_followup.UpdateFollowUpTagRequest
	adminID := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.customerFollowUpService.UpdateFollowUpTag(c.Request.Context(), req, adminID, adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Follow-up tag updated successfully",
	})
}

// UpdateMemberRemark updates member remark
// @Summary Update member remark
// @Description Update member remark field
// @Tags Customer Follow-Up
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body customer_followup.UpdateMemberRemarkRequest true "Update member remark request"
// @Success 200 {object} object{success=bool,message=string} "Member remark updated successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /admin/customer-followup/update-remark [patch]
func (h *CustomerFollowUpHandler) UpdateMemberRemark(c *gin.Context) {
	var req customer_followup.UpdateMemberRemarkRequest
	adminID := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.customerFollowUpService.UpdateMemberRemark(c.Request.Context(), req, adminID, adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Member remark updated successfully",
	})
}

// GetFollowUpOptions gets follow-up tags and statuses options
// @Summary Get follow-up options
// @Description Get available follow-up tags and statuses for dropdown/select options
// @Tags Customer Follow-Up
// @Produce json
// @Security BearerAuth
// @Success 200 {object} object{success=bool,data=object{tags=[]object,statuses=[]object}} "Options retrieved successfully"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /admin/customer-followup/options [get]
func (h *CustomerFollowUpHandler) GetFollowUpOptions(c *gin.Context) {
	tags := h.customerFollowUpService.GetFollowUpTags(c.Request.Context())
	statuses := h.customerFollowUpService.GetFollowUpStatuses(c.Request.Context())

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"tags":     tags,
			"statuses": statuses,
		},
	})
}

// ExportCustomerFollowUpCSV downloads CSV of current customer follow-up list
// @Summary Export customer follow-up as CSV
// @Description Download CSV file of customer follow-up list with current filters
// @Tags Customer Follow-Up
// @Produce text/csv
// @Security BearerAuth
// @Param search query string false "Search in member info"
// @Param tag query string false "Follow-up tag filter"
// @Param status query string false "Follow-up status filter"
// @Param start_datetime query string false "Start date filter (YYYY-MM-DD HH:MM:SS)"
// @Param end_datetime query string false "End date filter (YYYY-MM-DD HH:MM:SS)"
// @Param partner_id query int false "Partner ID filter"
// @Param channel_id query int false "Channel ID filter"
// @Param platform_id query string false "Platform ID filter"
// @Param contacted_by query int false "Admin who contacted filter"
// @Success 200 {file} file "CSV file download"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /admin/customer-followup/export-csv [get]
func (h *CustomerFollowUpHandler) ExportCustomerFollowUpCSV(c *gin.Context) {
	// Parse filter parameters (same as ListCustomerFollowUp)
	filter := &customer_followup.CustomerFollowUpFilter{
		Search:   c.Query("search"),
		Tag:      c.Query("tag"),
		Status:   c.Query("status"),
		OrderBy:  c.DefaultQuery("order_by", "id"),
		OrderDir: c.DefaultQuery("order_dir", "desc"),
	}

	// Parse date filters
	if startDateStr := c.Query("start_datetime"); startDateStr != "" {
		if startDate, err := time.Parse("2006-01-02 15:04:05", startDateStr); err == nil {
			filter.StartDateTime = &startDate
		}
	}
	if endDateStr := c.Query("end_datetime"); endDateStr != "" {
		if endDate, err := time.Parse("2006-01-02 15:04:05", endDateStr); err == nil {
			filter.EndDateTime = &endDate
		}
	}

	// Parse integer filters
	if partnerIDStr := c.Query("partner_id"); partnerIDStr != "" {
		if partnerID, err := strconv.Atoi(partnerIDStr); err == nil {
			filter.PartnerID = &partnerID
		}
	}
	if channelIDStr := c.Query("channel_id"); channelIDStr != "" {
		if channelID, err := strconv.Atoi(channelIDStr); err == nil {
			filter.ChannelID = &channelID
		}
	}
	if contactedByStr := c.Query("contacted_by"); contactedByStr != "" {
		if contactedBy, err := strconv.Atoi(contactedByStr); err == nil {
			filter.ContactedBy = &contactedBy
		}
	}

	// Parse platform ID
	if platformID := c.Query("platform_id"); platformID != "" {
		filter.PlatformID = &platformID
	}

	// Parse member status
	if memberStatusStr := c.Query("member_status"); memberStatusStr != "" {
		// Parse comma-separated member status values
		statusValues := strings.Split(memberStatusStr, ",")
		memberStatuses := make([]member.Status, 0, len(statusValues))
		for _, statusValue := range statusValues {
			statusValue = strings.TrimSpace(statusValue)
			if statusValue != "" {
				memberStatuses = append(memberStatuses, member.Status(statusValue))
			}
		}
		if len(memberStatuses) > 0 {
			filter.MemberStatus = memberStatuses
		}
	}

	// Get all data for CSV (use high limit)
	customers, _, err := h.customerFollowUpService.ListCustomerFollowUp(c.Request.Context(), 10000, 0, filter)
	if err != nil {
		c.Error(err)
		return
	}

	// Generate CSV content
	csvContent := h.generateCustomerFollowUpCSV(customers)

	// Set headers for file download
	filename := "customer_followup_" + time.Now().Format("20060102_150405") + ".csv"
	c.Header("Content-Type", "text/csv; charset=utf-8")
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Length", strconv.Itoa(len(csvContent)))

	// Write CSV content
	c.String(http.StatusOK, csvContent)
}

// ExportOldCustomerFollowUpCSV downloads CSV of old customers based on last_online filter
// @Summary Export old customer follow-up as CSV
// @Description Download CSV file of old customer follow-up list with current filters (based on last_online)
// @Tags Customer Follow-Up
// @Produce text/csv
// @Security BearerAuth
// @Param search query string false "Search in member info"
// @Param tag query string false "Follow-up tag filter"
// @Param status query string false "Follow-up status filter"
// @Param start_datetime query string false "Last online start date filter (YYYY-MM-DD HH:MM:SS)"
// @Param end_datetime query string false "Last online end date filter (YYYY-MM-DD HH:MM:SS)"
// @Param partner_id query int false "Partner ID filter"
// @Param channel_id query int false "Channel ID filter"
// @Param platform_id query string false "Platform ID filter"
// @Param contacted_by query int false "Admin who contacted filter"
// @Param member_status query string false "Member status filter (active, inactive, suspended, banned)"
// @Success 200 {file} file "CSV file download"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /admin/customer-followup/export-old-customers-csv [get]
func (h *CustomerFollowUpHandler) ExportOldCustomerFollowUpCSV(c *gin.Context) {
	// Parse filter parameters (same as ListOldCustomerFollowUp)
	filter := &customer_followup.CustomerFollowUpOldFilter{
		Search:   c.Query("search"),
		Tag:      c.Query("tag"),
		Status:   c.Query("status"),
		OrderBy:  c.DefaultQuery("order_by", "id"),
		OrderDir: c.DefaultQuery("order_dir", "desc"),
	}

	// Parse date filters (uses same parameters but filters last_online instead of created_at)
	if startDateStr := c.Query("start_datetime"); startDateStr != "" {
		if startDate, err := time.Parse("2006-01-02 15:04:05", startDateStr); err == nil {
			filter.StartDateTime = &startDate
		}
	}
	if endDateStr := c.Query("end_datetime"); endDateStr != "" {
		if endDate, err := time.Parse("2006-01-02 15:04:05", endDateStr); err == nil {
			filter.EndDateTime = &endDate
		}
	}

	// Parse integer filters
	if partnerIDStr := c.Query("partner_id"); partnerIDStr != "" {
		if partnerID, err := strconv.Atoi(partnerIDStr); err == nil {
			filter.PartnerID = &partnerID
		}
	}
	if channelIDStr := c.Query("channel_id"); channelIDStr != "" {
		if channelID, err := strconv.Atoi(channelIDStr); err == nil {
			filter.ChannelID = &channelID
		}
	}
	if contactedByStr := c.Query("contacted_by"); contactedByStr != "" {
		if contactedBy, err := strconv.Atoi(contactedByStr); err == nil {
			filter.ContactedBy = &contactedBy
		}
	}

	// Parse platform ID
	if platformID := c.Query("platform_id"); platformID != "" {
		filter.PlatformID = &platformID
	}

	// Parse member status
	if memberStatusStr := c.Query("member_status"); memberStatusStr != "" {
		// Parse comma-separated member status values
		statusValues := strings.Split(memberStatusStr, ",")
		memberStatuses := make([]member.Status, 0, len(statusValues))
		for _, statusValue := range statusValues {
			statusValue = strings.TrimSpace(statusValue)
			if statusValue != "" {
				memberStatuses = append(memberStatuses, member.Status(statusValue))
			}
		}
		if len(memberStatuses) > 0 {
			filter.MemberStatus = memberStatuses
		}
	}

	// Get all old customers data for CSV (use high limit)
	customers, _, err := h.customerFollowUpService.ListOldCustomerFollowUp(c.Request.Context(), 10000, 0, filter)
	if err != nil {
		c.Error(err)
		return
	}

	// Generate CSV content
	csvContent := h.generateOldCustomerFollowUpCSV(customers)

	// Set headers for file download
	filename := "old_customer_followup_" + time.Now().Format("20060102_150405") + ".csv"
	c.Header("Content-Type", "text/csv; charset=utf-8")
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Length", strconv.Itoa(len(csvContent)))

	// Write CSV content
	c.String(http.StatusOK, csvContent)
}

// Helper method to generate CSV content for old customers
func (h *CustomerFollowUpHandler) generateOldCustomerFollowUpCSV(customers []*customer_followup.CustomerFollowUpResponse) string {
	// CSV header (same as regular export but indicates this is for old customers)
	csv := "ID,Username,FirstName,LastName,Phone,Balance,Status,CreatedAt,LastOnline,FollowUpTag,FollowUpStatus,ContactedBy,LastContactAt,MemberGroupName,CallCount,SMSCount,Remark\n"

	// CSV data
	for _, customer := range customers {
		csv += strconv.Itoa(customer.ID) + ","
		csv += getStringValue(customer.Username) + ","
		csv += getStringValue(customer.FirstName) + ","
		csv += getStringValue(customer.LastName) + ","
		csv += getStringValue(customer.Phone) + ","
		csv += strconv.FormatFloat(customer.Balance, 'f', 2, 64) + ","
		csv += customer.Status + ","
		csv += customer.CreatedAt.Format("2006-01-02 15:04:05") + ","
		// Add last_online column (this is the key difference)
		if customer.LastOnline != nil {
			csv += customer.LastOnline.Format("2006-01-02 15:04:05") + ","
		} else {
			csv += ","
		}
		csv += getStringValue(customer.FollowUpTag) + ","
		csv += getStringValue(customer.FollowUpStatus) + ","
		csv += getIntValue(customer.ContactedBy) + ","
		csv += getTimeValue(customer.LastContactAt) + ","
		csv += getStringValue(customer.MemberGroupName) + ","
		csv += strconv.FormatInt(customer.CallCount, 10) + ","
		csv += strconv.FormatInt(customer.SMSCount, 10) + ","
		csv += getStringValue(customer.Remark) + "\n"
	}

	return csv
}

// Helper method to generate CSV content
func (h *CustomerFollowUpHandler) generateCustomerFollowUpCSV(customers []*customer_followup.CustomerFollowUpResponse) string {
	// CSV header
	csv := "ID,Username,FirstName,LastName,Phone,Balance,Status,CreatedAt,FollowUpTag,FollowUpStatus,ContactedBy,LastContactAt,MemberGroupName,CallCount,SMSCount,Remark\n"

	// CSV data
	for _, customer := range customers {
		csv += strconv.Itoa(customer.ID) + ","
		csv += getStringValue(customer.Username) + ","
		csv += getStringValue(customer.FirstName) + ","
		csv += getStringValue(customer.LastName) + ","
		csv += getStringValue(customer.Phone) + ","
		csv += strconv.FormatFloat(customer.Balance, 'f', 2, 64) + ","
		csv += customer.Status + ","
		csv += customer.CreatedAt.Format("2006-01-02 15:04:05") + ","
		csv += getStringValue(customer.FollowUpTag) + ","
		csv += getStringValue(customer.FollowUpStatus) + ","
		csv += getIntValue(customer.ContactedBy) + ","
		csv += getTimeValue(customer.LastContactAt) + ","
		csv += getStringValue(customer.MemberGroupName) + ","
		csv += strconv.FormatInt(customer.CallCount, 10) + ","
		csv += strconv.FormatInt(customer.SMSCount, 10) + ","
		csv += getStringValue(customer.Remark) + "\n"
	}

	return csv
}

// Helper functions for CSV generation
func getStringValue(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

func getIntValue(i *int) string {
	if i == nil {
		return ""
	}
	return strconv.Itoa(*i)
}

func getTimeValue(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}
