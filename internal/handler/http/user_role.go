package http

import (
	"fmt"
	"net/http"
	"strconv"

	userrole "blacking-api/internal/domain/user_role"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

type UserRoleHandler struct {
	userRoleService service.UserRoleService
	logger          logger.Logger
}

func NewUserRoleHandler(userRoleService service.UserRoleService, logger logger.Logger) *UserRoleHandler {
	return &UserRoleHandler{
		userRoleService: userRoleService,
		logger:          logger,
	}
}

// CreateUserRole creates a new user role
// @Summary Create user role
// @Description Create a new user role with permissions
// @Tags User Role Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body userrole.CreateUserRoleRequest true "Create user role request"
// @Success 201 {object} object{success=bool,data=userrole.UserRoleResponse} "User role created successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /user-roles [post]
func (h *UserRoleHandler) CreateUserRole(c *gin.Context) {
	var req userrole.CreateUserRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	userResp, err := h.userRoleService.CreateUserRole(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    userResp,
	})
}

// GetUserRole gets a user role by ID
// @Summary Get user role by ID
// @Description Get a specific user role by its ID
// @Tags User Role Management
// @Produce json
// @Security BearerAuth
// @Param id path string true "User Role ID"
// @Success 200 {object} object{success=bool,data=userrole.UserRoleResponse} "User role retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 404 {object} object{success=bool,error=string,message=string} "User role not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /user-roles/{id} [get]
func (h *UserRoleHandler) GetUserRole(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("user role ID is required"))
		return
	}

	userResp, err := h.userRoleService.GetUserRoleByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    userResp,
	})
}

// UpdateUserRole updates a user role
// @Summary Update user role
// @Description Update an existing user role
// @Tags User Role Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User Role ID"
// @Param request body userrole.UpdateUserRoleRequest true "Update user role request"
// @Success 200 {object} object{success=bool,data=userrole.UserRoleResponse} "User role updated successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 404 {object} object{success=bool,error=string,message=string} "User role not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /user-roles/{id} [put]
func (h *UserRoleHandler) UpdateUserRole(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("user role ID is required"))
		return
	}

	var req userrole.UpdateUserRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	userResp, err := h.userRoleService.UpdateUserRole(c.Request.Context(), id, req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    userResp,
	})
}

// DeleteUserRole deletes a user role
// @Summary Delete user role
// @Description Delete a user role by ID
// @Tags User Role Management
// @Produce json
// @Security BearerAuth
// @Param id path string true "User Role ID"
// @Success 200 {object} object{success=bool,message=string} "User role deleted successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 404 {object} object{success=bool,error=string,message=string} "User role not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /user-roles/{id} [delete]
func (h *UserRoleHandler) DeleteUserRole(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("user role ID is required"))
		return
	}

	if err := h.userRoleService.DeleteUserRole(c.Request.Context(), id); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "user role deleted successfully",
	})
}

// ListUserRoles lists user roles with pagination
// @Summary List user roles
// @Description Get a paginated list of user roles with optional search
// @Tags User Role Management
// @Produce json
// @Security BearerAuth
// @Param limit query int false "Limit" default(10)
// @Param offset query int false "Offset" default(0)
// @Param search query string false "Search term"
// @Success 200 {object} object{success=bool,data=object,pagination=object} "User roles retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /user-roles [get]
func (h *UserRoleHandler) ListUserRoles(c *gin.Context) {
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")
	search := c.Query("search") // รับพารามิเตอร์ search

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid limit parameter"))
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid offset parameter"))
		return
	}

	users, err := h.userRoleService.ListUserRoles(c.Request.Context(), limit, offset, search)
	if err != nil {
		c.Error(err)
		return
	}

	count, err := h.userRoleService.GetUserRolesCount(c.Request.Context(), search)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"users": users,
			"pagination": gin.H{
				"total":  count,
				"limit":  limit,
				"offset": offset,
			},
		},
	})
}

// ListUserRolesForDropdown handles GET /api/v1/user-roles/dropdown
// ListUserRolesForDropdown lists user roles for dropdown selection
// @Summary List user roles for dropdown
// @Description Get a list of user roles formatted for dropdown selection
// @Tags User Role Management
// @Produce json
// @Security BearerAuth
// @Success 200 {object} object{success=bool,data=[]object} "User roles for dropdown retrieved successfully"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /user-roles/dropdown [get]
func (h *UserRoleHandler) ListUserRolesForDropdown(c *gin.Context) {
	userRoles, err := h.userRoleService.ListUserRolesForDropdown(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    userRoles,
	})
}

func (h *UserRoleHandler) ActivateUserRole(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("user role ID is required"))
		return
	}

	if err := h.userRoleService.ActivateUserRole(c.Request.Context(), id); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "user activated successfully",
	})
}

func (h *UserRoleHandler) DeactivateUserRole(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("user role ID is required"))
		return
	}

	if err := h.userRoleService.DeactivateUserRole(c.Request.Context(), id); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "user deactivated successfully",
	})
}

func (h *UserRoleHandler) BulkToggleLockIP(c *gin.Context) {
	var req userrole.BulkToggleLockIPRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.userRoleService.BulkToggleLockIP(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("lock IP updated for %d user roles successfully", len(req.Toggles)),
		"data": gin.H{
			"toggles": req.Toggles,
		},
	})
}

func (h *UserRoleHandler) BulkToggle2FA(c *gin.Context) {
	var req userrole.BulkToggle2FARequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.userRoleService.BulkToggle2FA(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("2FA updated for %d user roles successfully", len(req.Toggles)),
		"data": gin.H{
			"toggles": req.Toggles,
		},
	})
}

func (h *UserRoleHandler) BulkListLockIP(c *gin.Context) {
	userRoles, err := h.userRoleService.BulkListLockIP(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    userRoles,
	})
}

func (h *UserRoleHandler) BulkList2FA(c *gin.Context) {
	userRoles, err := h.userRoleService.BulkList2FA(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    userRoles,
	})
}

func (h *UserRoleHandler) ReorderUserRoles(c *gin.Context) {
	var req userrole.ReorderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.userRoleService.ReorderUserRoles(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("reordered %d user roles successfully", len(req.UserRoleIDs)),
		"data": gin.H{
			"user_role_ids": req.UserRoleIDs,
		},
	})
}

func (h *UserRoleHandler) CloneUserRole(c *gin.Context) {
	var req userrole.CreateUserRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("user role ID is required"))
		return
	}

	data, err := h.userRoleService.CloneUserRole(c.Request.Context(), id, req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
	})
}
