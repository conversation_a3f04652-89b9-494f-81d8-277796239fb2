package http

import (
	"blacking-api/internal/domain/return_turn"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

type ReturnTurnHandler struct {
	returnTurnService service.ReturnTurnService
	validator         *validator.Validate
}

// NewReturnTurnHandler creates a new return turn handler
func NewReturnTurnHandler(
	returnTurnService service.ReturnTurnService,
) *ReturnTurnHandler {
	return &ReturnTurnHandler{
		returnTurnService: returnTurnService,
		validator:         validator.New(),
	}
}

// Admin APIs

// GetReturnSetting godoc
// @Summary Get return turn settings
// @Description Get the current return turn settings configuration
// @Tags Return Turn
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "data contains ReturnTurnSettingResponse with creditExpireDays field"
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /admin/return-turn/setting [get]
// @Security BearerAuth
func (h *ReturnTurnHandler) GetReturnSetting(c *gin.Context) {
	ctx := c.Request.Context()

	setting, err := h.returnTurnService.GetReturnSetting(ctx)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Return setting retrieved successfully",
		"data":    setting,
	})
}

// UpdateReturnSetting godoc
// @Summary Update return turn settings
// @Description Update the return turn settings configuration including creditExpireDays
// @Tags Return Turn
// @Accept json
// @Produce json
// @Param request body return_turn.ReturnTurnSettingUpdateRequest true "Update request with creditExpireDays field"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /admin/return-turn/setting [patch]
// @Security BearerAuth
func (h *ReturnTurnHandler) UpdateReturnSetting(c *gin.Context) {
	ctx := c.Request.Context()

	var req return_turn.ReturnTurnSettingUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, errors.NewValidationError(err.Error()))
		return
	}

	// Get admin ID from context (set by auth middleware)
	adminId, exists := c.Get("admin_id")
	if exists {
		if id, ok := adminId.(int64); ok {
			req.UpdatedByID = id
		}
	}

	err := h.returnTurnService.UpdateReturnSetting(ctx, req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Return setting updated successfully",
		"data":    nil,
	})
}

// GetCustomerPromotionList handles GET /admin/customer-promotion/list
func (h *ReturnTurnHandler) GetCustomerPromotionList(c *gin.Context) {
	ctx := c.Request.Context()

	var req return_turn.CustomerPromotionListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleError(c, errors.NewValidationError(err.Error()))
		return
	}

	// Set defaults if not provided
	if req.Page == 0 {
		req.Page = 1
	}
	if req.Limit == 0 {
		req.Limit = 10
	}

	result, err := h.returnTurnService.GetCustomerPromotionList(ctx, req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Customer promotion list retrieved successfully",
		"data":    result,
	})
}

// CancelCustomerPromotion handles POST /admin/customer-promotion/cancel
func (h *ReturnTurnHandler) CancelCustomerPromotion(c *gin.Context) {
	ctx := c.Request.Context()

	var req return_turn.CustomerPromotionCancelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleError(c, errors.NewValidationError(err.Error()))
		return
	}

	if err := h.validator.Struct(req); err != nil {
		HandleError(c, errors.NewValidationError(err.Error()))
		return
	}

	err := h.returnTurnService.CancelCustomerPromotion(ctx, req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Customer promotion canceled successfully",
		"data":    nil,
	})
}

// GetReturnHistoryMemberList handles GET /admin/return-turn/history/member-list
func (h *ReturnTurnHandler) GetReturnHistoryMemberList(c *gin.Context) {
	ctx := c.Request.Context()

	var req return_turn.ReturnTurnHistoryUserListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleError(c, errors.NewValidationError(err.Error()))
		return
	}

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.Limit == 0 {
		req.Limit = 10
	}

	result, err := h.returnTurnService.GetReturnHistoryMemberList(ctx, req)
	if err != nil {
		HandleError(c, err)
		return
	}

	// Calculate pagination values
	totalPages := int((result.Total + int64(req.Limit) - 1) / int64(req.Limit))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Get return history member list successfully",
		"data": gin.H{
			"success": true,
			"data":    result.List,
			"pagination": return_turn.ReturnTurnPaginationResponse{
				Page:       req.Page,
				Limit:      req.Limit,
				Total:      result.Total,
				TotalPages: totalPages,
			},
		},
	})
}

// GetReturnHistoryMemberSummary handles GET /admin/return-turn/history/member-summary
func (h *ReturnTurnHandler) GetReturnHistoryMemberSummary(c *gin.Context) {
	ctx := c.Request.Context()

	var req return_turn.ReturnTurnHistoryUserListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleError(c, errors.NewValidationError(err.Error()))
		return
	}

	result, err := h.returnTurnService.GetReturnHistoryMemberSummary(ctx, req)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Get return history member summary successfully",
		"data":    result,
	})
}

// GetReturnHistoryLogList handles GET /admin/return-turn/history/log-list
func (h *ReturnTurnHandler) GetReturnHistoryLogList(c *gin.Context) {
	ctx := c.Request.Context()

	var req return_turn.ReturnTurnHistoryListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleError(c, errors.NewValidationError(err.Error()))
		return
	}

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.Limit == 0 {
		req.Limit = 10
	}

	result, err := h.returnTurnService.GetReturnHistoryLogList(ctx, req)
	if err != nil {
		HandleError(c, err)
		return
	}

	// Calculate pagination values
	totalPages := int((result.Total + int64(req.Limit) - 1) / int64(req.Limit))

	// Build response with new format
	var data []return_turn.ReturnTurnLoserResponse
	if result.List != nil {
		if list, ok := result.List.([]return_turn.ReturnTurnLoserResponse); ok {
			data = list
		}
	}
	if data == nil {
		data = []return_turn.ReturnTurnLoserResponse{}
	}

	response := return_turn.ReturnTurnWebListResponse{
		Success: true,
		Data:    data,
		Pagination: return_turn.ReturnTurnPaginationResponse{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      result.Total,
			TotalPages: totalPages,
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Get return history log list successfully",
		"data":    response,
	})
}

// Web/User APIs

// GetUserCurrentReturnDetail handles GET /web/return-turn-loser/current
func (h *ReturnTurnHandler) GetUserCurrentReturnDetail(c *gin.Context) {
	ctx := c.Request.Context()

	// Get member code from context (set by auth middleware)
	memberCode, exists := c.Get("member_code")
	var memberCodeStr string
	if exists {
		if code, ok := memberCode.(string); ok {
			memberCodeStr = code
		} else if codePtr, ok := memberCode.(*string); ok && codePtr != nil {
			memberCodeStr = *codePtr
		}
	}

	if memberCodeStr == "" {
		HandleError(c, errors.NewUnauthorizedError("user not authenticated"))
		return
	}

	detail, err := h.returnTurnService.GetUserCurrentReturnDetailByCode(ctx, memberCodeStr)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Current return detail retrieved successfully",
		"data":    detail,
	})
}

// TakeUserReturnAmount handles POST /web/return-turn-loser/take
func (h *ReturnTurnHandler) TakeUserReturnAmount(c *gin.Context) {
	ctx := c.Request.Context()

	// Get member code from context (set by auth middleware)
	memberCode, exists := c.Get("member_code")
	var memberCodeStr string
	if exists {
		if code, ok := memberCode.(string); ok {
			memberCodeStr = code
		} else if codePtr, ok := memberCode.(*string); ok && codePtr != nil {
			memberCodeStr = *codePtr
		}
	}

	if memberCodeStr == "" {
		HandleError(c, errors.NewUnauthorizedError("user not authenticated"))
		return
	}

	err := h.returnTurnService.TakeUserReturnAmountByCode(ctx, memberCodeStr)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Updated success",
		"data":    nil,
	})
}

// GetUserReturnHistoryList handles GET /web/return-turn-loser/list
func (h *ReturnTurnHandler) GetUserReturnHistoryList(c *gin.Context) {
	ctx := c.Request.Context()

	var req return_turn.ReturnTurnTransactionListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleError(c, errors.NewValidationError(err.Error()))
		return
	}

	// Get member code from context (set by auth middleware)
	memberCode, exists := c.Get("member_code")
	var memberCodeStr string
	if exists {
		if code, ok := memberCode.(string); ok {
			memberCodeStr = code
		} else if codePtr, ok := memberCode.(*string); ok && codePtr != nil {
			memberCodeStr = *codePtr
		}
	}

	if memberCodeStr == "" {
		HandleError(c, errors.NewUnauthorizedError("user not authenticated"))
		return
	}

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.Limit == 0 {
		req.Limit = 10
	}

	result, err := h.returnTurnService.GetUserReturnHistoryListByCode(ctx, memberCodeStr, req)
	if err != nil {
		HandleError(c, err)
		return
	}

	// Calculate pagination values
	totalPages := int((result.Total + int64(req.Limit) - 1) / int64(req.Limit))

	// Build response with new format
	var data []return_turn.ReturnTurnLoserResponse
	if result.List != nil {
		if list, ok := result.List.([]return_turn.ReturnTurnLoserResponse); ok {
			data = list
		}
	}
	if data == nil {
		data = []return_turn.ReturnTurnLoserResponse{}
	}

	response := return_turn.ReturnTurnWebListResponse{
		Success: true,
		Data:    data,
		Pagination: return_turn.ReturnTurnPaginationResponse{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      result.Total,
			TotalPages: totalPages,
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetPromotionOpenList handles GET /web/promotions/open-list/promotion-list
func (h *ReturnTurnHandler) GetPromotionOpenList(c *gin.Context) {
	ctx := c.Request.Context()

	// Get return setting to check if enabled
	setting, err := h.returnTurnService.GetReturnSetting(ctx)
	if err != nil {
		HandleError(c, err)
		return
	}

	promotions := []map[string]interface{}{}

	if setting.IsEnabled {
		promotions = append(promotions, map[string]interface{}{
			"key":         "RETURN_LOSS",
			"name":        "คืนยอดเสีย",
			"description": "รับเงินคืนจากยอดเสียของคุณ",
			"imageUrl":    "/images/promotions/return-loss.png",
		})
	}

	// Add other promotions here if needed
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    promotions,
	})
}

// Cron APIs

// CronCutReturnLossDaily handles GET /cron/return-turn-loser/cut-daily
func (h *ReturnTurnHandler) CronCutReturnLossDaily(c *gin.Context) {
	ctx := c.Request.Context()

	err := h.returnTurnService.CronCutReturnLossDaily(ctx)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(http.StatusOK, return_turn.SuccessResponse{
		Message: "Daily return loss calculation completed",
	})
}

// CronCutReturnLossByDate handles GET /cron/return-turn-loser/cut-date
func (h *ReturnTurnHandler) CronCutReturnLossByDate(c *gin.Context) {
	ctx := c.Request.Context()

	// Get ofDate from query parameter
	ofDate := c.Query("ofDate")
	if ofDate == "" {
		HandleError(c, errors.NewValidationError("ofDate query parameter is required"))
		return
	}

	err := h.returnTurnService.CronCutReturnLossByDate(ctx, ofDate)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(http.StatusOK, return_turn.SuccessResponse{
		Message: "Return loss calculation completed for date: " + ofDate,
	})
}

// Reference Data APIs

// GetReturnTurnCutTypes handles GET /admin/return-turn/cut-types
func (h *ReturnTurnHandler) GetReturnTurnCutTypes(c *gin.Context) {
	ctx := c.Request.Context()

	types, err := h.returnTurnService.GetReturnTurnCutTypes(ctx)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Return turn cut types retrieved successfully",
		"data":    types,
	})
}

// GetReturnTurnLoserTypes handles GET /admin/return-turn/loser-types
func (h *ReturnTurnHandler) GetReturnTurnLoserTypes(c *gin.Context) {
	ctx := c.Request.Context()

	types, err := h.returnTurnService.GetReturnTurnLoserTypes(ctx)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Return turn loser types retrieved successfully",
		"data":    types,
	})
}

// GetCalculatePlayTypes handles GET /admin/return-turn/calculate-play-types
func (h *ReturnTurnHandler) GetCalculatePlayTypes(c *gin.Context) {
	ctx := c.Request.Context()

	types, err := h.returnTurnService.GetCalculatePlayTypes(ctx)
	if err != nil {
		HandleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Calculate play types retrieved successfully",
		"data":    types,
	})
}
