package http

import (
	"blacking-api/internal/domain/user_transaction"
	"blacking-api/internal/service"
	"blacking-api/pkg/auth"
	"blacking-api/pkg/errors"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type UserTransactionHandler struct {
	userTransactionService service.UserTransactionService
}

func NewUserTransactionHandler(userTransactionService service.UserTransactionService) *UserTransactionHandler {
	return &UserTransactionHandler{
		userTransactionService: userTransactionService,
	}
}

// CreateDeposit handles POST /user-transaction/deposit
// @Summary Create deposit transaction
// @Description Create a new deposit transaction
// @Tags UserTransaction
// @Accept json
// @Produce json
// @Param request body user_transaction.CreateUserTransactionAdminDepositRequest true "Deposit request"
// @Success 201 {object} object{success=bool,message=string} "Deposit created successfully"
// @Failure 400 {object} object{success=bool,error=object{code=string,message=string}} "Bad request"
// @Failure 500 {object} object{success=bool,error=object{code=string,message=string}} "Internal server error"
// @Router /user-transaction/deposit [post]
func (h *UserTransactionHandler) CreateDeposit(c *gin.Context) {
	var req user_transaction.CreateUserTransactionAdminDepositRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	err := h.userTransactionService.CreateDeposit(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Deposit success",
	})
}

// CreateWithdraw handles POST /user-transaction/withdraw
// @Summary Create withdraw transaction
// @Description Create a new withdraw transaction
// @Tags UserTransaction
// @Accept json
// @Produce json
// @Param request body user_transaction.CreateUserTransactionWithdrawRequest true "Withdraw request"
// @Success 201 {object} object{success=bool,data=user_transaction.UserTransactionResponse} "Withdraw created successfully"
// @Failure 400 {object} object{success=bool,error=object{code=string,message=string}} "Bad request"
// @Failure 500 {object} object{success=bool,error=object{code=string,message=string}} "Internal server error"
// @Router /user-transaction/withdraw [post]
func (h *UserTransactionHandler) CreateWithdraw(c *gin.Context) {
	var req user_transaction.CreateUserTransactionWithdrawRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	response, err := h.userTransactionService.CreateWithdraw(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    response,
	})
}

// CreateTransfer handles POST /user-transaction/transfer
// @Summary Create transfer transaction
// @Description Create a new transfer transaction
// @Tags UserTransaction
// @Accept json
// @Produce json
// @Param request body user_transaction.CreateUserTransactionTransferRequest true "Transfer request"
// @Success 201 {object} object{success=bool,data=user_transaction.UserTransactionResponse} "Transfer created successfully"
// @Failure 400 {object} object{success=bool,error=object{code=string,message=string}} "Bad request"
// @Failure 500 {object} object{success=bool,error=object{code=string,message=string}} "Internal server error"
// @Router /user-transaction/transfer [post]
func (h *UserTransactionHandler) CreateTransfer(c *gin.Context) {
	var req user_transaction.CreateUserTransactionTransferRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	response, err := h.userTransactionService.CreateTransfer(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    response,
	})
}

// CreateWebWithdraw handles POST /user-transaction/web/withdraw
// @Summary Create web withdraw transaction
// @Description Create a new web withdraw transaction
// @Tags UserTransaction
// @Accept json
// @Produce json
// @Param request body user_transaction.CreateUserTransactionWebWithdrawRequest true "Web withdraw request"
// @Success 201 {object} object{success=bool,data=user_transaction.UserTransactionResponse} "Web withdraw created successfully"
// @Failure 400 {object} object{success=bool,error=object{code=string,message=string}} "Bad request"
// @Failure 500 {object} object{success=bool,error=object{code=string,message=string}} "Internal server error"
// @Router /user-transaction/web/withdraw [post]
func (h *UserTransactionHandler) CreateWebWithdraw(c *gin.Context) {
	var req user_transaction.CreateUserTransactionWebWithdrawRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Get Member ID from auth context
	memberID := auth.GetMemberIDFromContext(c)
	if memberID == 0 {
		c.Error(errors.NewUnauthorizedError("member authentication required"))
		return
	}

	response, err := h.userTransactionService.CreateWebWithdraw(c.Request.Context(), int64(memberID), &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    response,
	})
}

// CreateWebDeposit handles POST /user-transaction/web/deposit
// @Summary Create web deposit transaction
// @Description Create a new web deposit transaction
// @Tags UserTransaction
// @Accept json
// @Produce json
// @Param request body user_transaction.CreateUserTransactionWebDepositRequest true "Web deposit request"
// @Success 201 {object} object{success=bool,data=user_transaction.UserTransactionResponse} "Web deposit created successfully"
// @Failure 400 {object} object{success=bool,error=object{code=string,message=string}} "Bad request"
// @Failure 500 {object} object{success=bool,error=object{code=string,message=string}} "Internal server error"
// @Router /user-transaction/web/deposit [post]
func (h *UserTransactionHandler) CreateWebDeposit(c *gin.Context) {
	var req user_transaction.CreateUserTransactionWebDepositRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Get Member ID from auth context
	memberID := auth.GetMemberIDFromContext(c)
	if memberID == 0 {
		c.Error(errors.NewUnauthorizedError("member authentication required"))
		return
	}

	response, err := h.userTransactionService.CreateWebDeposit(c.Request.Context(), int64(memberID), &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    response,
	})
}

// GetDepositPage handles GET /user-transaction/deposit/page
// @Summary Get paginated deposit transactions
// @Description Retrieve paginated deposit transactions with filters
// @Tags UserTransaction
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param statusId query int false "Status ID filter"
// @Param date query string false "Date filter (YYYY-MM-DD)"
// @Param username query string false "Username filter"
// @Param membercode query string false "Member code filter"
// @Param userbankid query int false "User bank ID filter"
// @Param bankingid query int false "Banking ID filter"
// @Param startamount query number false "Start amount filter"
// @Param endamount query number false "End amount filter"
// @Param amount query number false "Exact amount filter"
// @Param admin query string false "Admin filter"
// @Success 200 {object} response.SuccessWithPagination "Deposit transactions retrieved successfully"
// @Failure 400 {object} object{success=bool,error=object{code=string,message=string}} "Bad request"
// @Failure 500 {object} object{success=bool,error=object{code=string,message=string}} "Internal server error"
// @Router /user-transaction/deposit/page [get]
func (h *UserTransactionHandler) GetDepositPage(c *gin.Context) {
	var req user_transaction.UserTransactionDepositPageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.Error(errors.NewValidationError("invalid query parameters: " + err.Error()))
		return
	}

	response, err := h.userTransactionService.GetDepositPage(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"data":       response.Content,
		"page":       response.Page,
		"limit":      response.Limit,
		"total":      response.TotalItems,
		"totalPages": response.TotalPages,
	})
}

// GetWithdrawPage handles GET /user-transaction/withdraw/page
// @Summary Get paginated withdraw transactions
// @Description Retrieve paginated withdraw transactions with filters
// @Tags UserTransaction
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param statusId query int false "Status ID filter"
// @Param date query string false "Date filter (YYYY-MM-DD)"
// @Param username query string false "Username filter"
// @Param membercode query string false "Member code filter"
// @Param userbankid query int false "User bank ID filter"
// @Param bankingid query int false "Banking ID filter"
// @Param startamount query number false "Start amount filter"
// @Param endamount query number false "End amount filter"
// @Param amount query number false "Exact amount filter"
// @Param ip query string false "IP address filter"
// @Success 200 {object} response.SuccessWithPagination "Withdraw transactions retrieved successfully"
// @Failure 400 {object} object{success=bool,error=object{code=string,message=string}} "Bad request"
// @Failure 500 {object} object{success=bool,error=object{code=string,message=string}} "Internal server error"
// @Router /user-transaction/withdraw/page [get]
func (h *UserTransactionHandler) GetWithdrawPage(c *gin.Context) {
	var req user_transaction.UserTransactionWithdrawPageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.Error(errors.NewValidationError("invalid query parameters: " + err.Error()))
		return
	}

	response, err := h.userTransactionService.GetWithdrawPage(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"data":       response.Content,
		"page":       response.Page,
		"limit":      response.Limit,
		"total":      response.TotalItems,
		"totalPages": response.TotalPages,
	})
}

// GetTransferPage handles GET /user-transaction/transfer/page
// @Summary Get paginated transfer transactions
// @Description Retrieve paginated transfer transactions with filters
// @Tags UserTransaction
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param statusId query int false "Status ID filter"
// @Param date query string false "Date filter (YYYY-MM-DD)"
// @Param amount query number false "Exact amount filter"
// @Success 200 {object} response.SuccessWithPagination "Transfer transactions retrieved successfully"
// @Failure 400 {object} object{success=bool,error=object{code=string,message=string}} "Bad request"
// @Failure 500 {object} object{success=bool,error=object{code=string,message=string}} "Internal server error"
// @Router /user-transaction/transfer/page [get]
func (h *UserTransactionHandler) GetTransferPage(c *gin.Context) {
	var req user_transaction.UserTransactionTransferPageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.Error(errors.NewValidationError("invalid query parameters: " + err.Error()))
		return
	}

	response, err := h.userTransactionService.GetTransferPage(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"data":       response.Content,
		"page":       response.Page,
		"limit":      response.Limit,
		"total":      response.TotalItems,
		"totalPages": response.TotalPages,
	})
}

// GetDepositByID handles GET /user-transaction/deposit/:id
// @Summary Get deposit transaction by ID
// @Description Retrieve a specific deposit transaction by its ID
// @Tags UserTransaction
// @Accept json
// @Produce json
// @Param id path int true "Transaction ID"
// @Success 200 {object} object{success=bool,data=user_transaction.UserTransactionResponse} "Deposit transaction retrieved successfully"
// @Failure 400 {object} object{success=bool,error=object{code=string,message=string}} "Bad request"
// @Failure 404 {object} object{success=bool,error=object{code=string,message=string}} "Transaction not found"
// @Failure 500 {object} object{success=bool,error=object{code=string,message=string}} "Internal server error"
// @Router /user-transaction/deposit/{id} [get]
func (h *UserTransactionHandler) GetDepositByID(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid transaction ID"))
		return
	}

	response, err := h.userTransactionService.GetDepositByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// GetWithdrawByID handles GET /user-transaction/withdraw/:id
// @Summary Get withdraw transaction by ID
// @Description Retrieve a specific withdraw transaction by its ID
// @Tags UserTransaction
// @Accept json
// @Produce json
// @Param id path int true "Transaction ID"
// @Success 200 {object} object{success=bool,data=user_transaction.UserTransactionResponse} "Withdraw transaction retrieved successfully"
// @Failure 400 {object} object{success=bool,error=object{code=string,message=string}} "Bad request"
// @Failure 404 {object} object{success=bool,error=object{code=string,message=string}} "Transaction not found"
// @Failure 500 {object} object{success=bool,error=object{code=string,message=string}} "Internal server error"
// @Router /user-transaction/withdraw/{id} [get]
func (h *UserTransactionHandler) GetWithdrawByID(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid transaction ID"))
		return
	}

	response, err := h.userTransactionService.GetWithdrawByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// GetTransferByID handles GET /user-transaction/transfer/:id
// @Summary Get transfer transaction by ID
// @Description Retrieve a specific transfer transaction by its ID
// @Tags UserTransaction
// @Accept json
// @Produce json
// @Param id path int true "Transaction ID"
// @Success 200 {object} object{success=bool,data=user_transaction.UserTransactionResponse} "Transfer transaction retrieved successfully"
// @Failure 400 {object} object{success=bool,error=object{code=string,message=string}} "Bad request"
// @Failure 404 {object} object{success=bool,error=object{code=string,message=string}} "Transaction not found"
// @Failure 500 {object} object{success=bool,error=object{code=string,message=string}} "Internal server error"
// @Router /user-transaction/transfer/{id} [get]
func (h *UserTransactionHandler) GetTransferByID(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid transaction ID"))
		return
	}

	response, err := h.userTransactionService.GetTransferByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// GetDepositsByMemberID handles GET /user-transaction/deposit/member/:memberId
// @Summary Get deposit transactions by user ID
// @Description Retrieve paginated deposit transactions for a specific user
// @Tags UserTransaction
// @Accept json
// @Produce json
// @Param userId path int true "User ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param statusId query int false "Status ID filter"
// @Param date query string false "Date filter (YYYY-MM-DD)"
// @Param bankingId query int false "Banking ID filter"
// @Param startAmount query number false "Start amount filter"
// @Param endAmount query number false "End amount filter"
// @Param amount query number false "Exact amount filter"
// @Success 200 {object} response.SuccessWithPagination "User deposit transactions retrieved successfully"
// @Failure 400 {object} object{success=bool,error=object{code=string,message=string}} "Bad request"
// @Failure 404 {object} object{success=bool,error=object{code=string,message=string}} "User not found"
// @Failure 500 {object} object{success=bool,error=object{code=string,message=string}} "Internal server error"
// @Router /user-transaction/deposit/member/{memberId}/page [get]
func (h *UserTransactionHandler) GetDepositsByMemberID(c *gin.Context) {
	memberIDParam := c.Param("memberId")
	memberID, err := strconv.Atoi(memberIDParam)
	if err != nil {
		c.Error(errors.NewValidationError("invalid member ID"))
		return
	}

	var req user_transaction.UserTransactionDepositByUserRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.Error(errors.NewValidationError("invalid query parameters: " + err.Error()))
		return
	}

	response, err := h.userTransactionService.GetDepositsByMemberID(c.Request.Context(), memberID, &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"data":       response.Content,
		"page":       response.Page,
		"limit":      response.Limit,
		"total":      response.TotalItems,
		"totalPages": response.TotalPages,
	})
}

// GetWithdrawsByMemberID handles GET /user-transaction/withdraw/member/:memberId
// @Summary Get withdraw transactions by user ID
// @Description Retrieve paginated withdraw transactions for a specific user
// @Tags UserTransaction
// @Accept json
// @Produce json
// @Param userId path int true "User ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param statusId query int false "Status ID filter"
// @Param date query string false "Date filter (YYYY-MM-DD)"
// @Param bankingId query int false "Banking ID filter"
// @Param startAmount query number false "Start amount filter"
// @Param endAmount query number false "End amount filter"
// @Param amount query number false "Exact amount filter"
// @Success 200 {object} response.SuccessWithPagination "User withdraw transactions retrieved successfully"
// @Failure 400 {object} object{success=bool,error=object{code=string,message=string}} "Bad request"
// @Failure 404 {object} object{success=bool,error=object{code=string,message=string}} "User not found"
// @Failure 500 {object} object{success=bool,error=object{code=string,message=string}} "Internal server error"
// @Router /user-transaction/withdraw/member/{memberId}/page [get]
func (h *UserTransactionHandler) GetWithdrawsByMemberID(c *gin.Context) {
	memberIDParam := c.Param("memberId")
	memberID, err := strconv.Atoi(memberIDParam)
	if err != nil {
		c.Error(errors.NewValidationError("invalid member ID"))
		return
	}

	var req user_transaction.UserTransactionWithdrawByUserRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.Error(errors.NewValidationError("invalid query parameters: " + err.Error()))
		return
	}

	response, err := h.userTransactionService.GetWithdrawsByMemberID(c.Request.Context(), memberID, &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"data":       response.Content,
		"page":       response.Page,
		"limit":      response.Limit,
		"total":      response.TotalItems,
		"totalPages": response.TotalPages,
	})
}

// UpdateDepositStatus handles PUT /user-transaction/deposit/status/:id
// @Summary Update deposit transaction status
// @Description Update the status of a deposit transaction
// @Tags UserTransaction
// @Accept json
// @Produce json
// @Param id path int true "Transaction ID"
// @Param request body user_transaction.UpdateUserTransactionStatusRequest true "Status update request"
// @Success 200 {object} object{success=bool,message=string} "Status updated successfully"
// @Failure 400 {object} object{success=bool,error=object{code=string,message=string}} "Bad request"
// @Failure 404 {object} object{success=bool,error=object{code=string,message=string}} "Transaction not found"
// @Failure 500 {object} object{success=bool,error=object{code=string,message=string}} "Internal server error"
// @Router /user-transaction/deposit/status/{id} [put]
func (h *UserTransactionHandler) UpdateDepositStatus(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid transaction ID"))
		return
	}

	var req user_transaction.UpdateUserTransactionStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	err = h.userTransactionService.UpdateDepositStatus(c.Request.Context(), id, &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Deposit status updated successfully",
	})
}

// UpdateWithdrawStatus handles PUT /user-transaction/withdraw/status/:id
// @Summary Update withdraw transaction status
// @Description Update the status of a withdraw transaction
// @Tags UserTransaction
// @Accept json
// @Produce json
// @Param id path int true "Transaction ID"
// @Param request body user_transaction.UpdateUserTransactionStatusRequest true "Status update request"
// @Success 200 {object} object{success=bool,message=string} "Status updated successfully"
// @Failure 400 {object} object{success=bool,error=object{code=string,message=string}} "Bad request"
// @Failure 404 {object} object{success=bool,error=object{code=string,message=string}} "Transaction not found"
// @Failure 500 {object} object{success=bool,error=object{code=string,message=string}} "Internal server error"
// @Router /user-transaction/withdraw/status/{id} [put]
func (h *UserTransactionHandler) UpdateWithdrawStatus(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid transaction ID"))
		return
	}

	var req user_transaction.UpdateUserTransactionStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	err = h.userTransactionService.UpdateWithdrawStatus(c.Request.Context(), id, &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Withdraw status updated successfully",
	})
}

// UpdateTransferStatus handles PUT /user-transaction/transfer/status/:id
// @Summary Update transfer transaction status
// @Description Update the status of a transfer transaction
// @Tags UserTransaction
// @Accept json
// @Produce json
// @Param id path int true "Transaction ID"
// @Param request body user_transaction.UpdateUserTransactionStatusRequest true "Status update request"
// @Success 200 {object} object{success=bool,message=string} "Status updated successfully"
// @Failure 400 {object} object{success=bool,error=object{code=string,message=string}} "Bad request"
// @Failure 404 {object} object{success=bool,error=object{code=string,message=string}} "Transaction not found"
// @Failure 500 {object} object{success=bool,error=object{code=string,message=string}} "Internal server error"
// @Router /user-transaction/transfer/status/{id} [put]
func (h *UserTransactionHandler) UpdateTransferStatus(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid transaction ID"))
		return
	}

	var req user_transaction.UpdateUserTransactionStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	err = h.userTransactionService.UpdateTransferStatus(c.Request.Context(), id, &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Transfer status updated successfully",
	})
}

// UploadSlip handles POST /user-transaction/upload-slip
// @Summary Upload transaction slip
// @Description Upload a transaction slip image file
// @Tags UserTransaction
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "Slip image file"
// @Success 200 {object} object{success=bool,message=string,data=string} "Slip uploaded successfully"
// @Failure 400 {object} object{success=bool,error=object{code=string,message=string}} "Bad request"
// @Failure 500 {object} object{success=bool,error=object{code=string,message=string}} "Internal server error"
// @Security BearerAuth
// @Router /user-transaction/upload-slip [post]
func (h *UserTransactionHandler) UploadSlip(c *gin.Context) {
	fileUrl, err := h.userTransactionService.UploadSlip(c.Request.Context(), c.Request)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Slip uploaded successfully",
		"data":    fileUrl,
	})
}
