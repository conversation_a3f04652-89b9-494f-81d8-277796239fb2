package http

import (
	"blacking-api/internal/domain/theme_setting"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"net/http"

	"github.com/gin-gonic/gin"
)

type ThemeSettingHandler struct {
	themeSettingService service.ThemeSettingService
	logger              logger.Logger
}

// NewThemeSettingHandler creates a new theme setting handler
func NewThemeSettingHandler(themeSettingService service.ThemeSettingService, logger logger.Logger) *ThemeSettingHandler {
	return &ThemeSettingHandler{
		themeSettingService: themeSettingService,
		logger:              logger,
	}
}

// GetTheme handles GET /api/theme
// @Summary Get theme settings
// @Description Get the current theme settings configuration
// @Tags System Settings
// @Produce json
// @Security BearerAuth
// @Success 200 {object} object{success=bool,data=object} "Theme settings retrieved successfully"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /system/theme [get]
func (h *ThemeSettingHandler) GetTheme(c *gin.Context) {
	theme, err := h.themeSettingService.GetTheme(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    theme,
	})
}

// SaveTheme handles POST /api/theme
// @Summary Save theme settings
// @Description Save or update theme settings configuration
// @Tags System Settings
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body theme_setting.SaveThemeRequest true "Save theme request"
// @Success 200 {object} object{success=bool,message=string} "Theme settings saved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /system/theme [post]
func (h *ThemeSettingHandler) SaveTheme(c *gin.Context) {
	var req theme_setting.SaveThemeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Get user ID from context (assuming it's set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.Error(errors.NewUnauthorizedError("user not authenticated"))
		return
	}

	updatedBy, ok := userID.(int)
	if !ok {
		c.Error(errors.NewInternalError("invalid user ID format"))
		return
	}

	theme, err := h.themeSettingService.SaveTheme(c.Request.Context(), req, updatedBy)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Theme saved successfully",
		"data":    theme,
	})
}
