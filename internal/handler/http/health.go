package http

import (
	"net/http"
	"time"

	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

type HealthHandler struct {
	logger logger.Logger
}

func NewHealthHandler(logger logger.Logger) *HealthHandler {
	return &HealthHandler{
		logger: logger,
	}
}

// Health checks the overall health of the API service
// @Summary Health check
// @Description Get the health status of the API service
// @Tags Health
// @Produce json
// @Success 200 {object} object{status=string,service=string,version=string,timestamp=string} "Service is healthy"
// @Router /health [get]
func (h *HealthHandler) Health(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"service":   "blacking-api",
		"version":   "1.0.0",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// Ready checks if the service is ready to handle requests
// @Summary Readiness check
// @Description Check if the service is ready to handle requests
// @Tags Health
// @Produce json
// @Success 200 {object} object{status=string,timestamp=string} "Service is ready"
// @Router /health/ready [get]
func (h *HealthHandler) Ready(c *gin.Context) {
	// TODO: Add database connectivity check
	c.JSON(http.StatusOK, gin.H{
		"status":    "ready",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// Live checks if the service is alive
// @Summary Liveness check
// @Description Check if the service is alive and running
// @Tags Health
// @Produce json
// @Success 200 {object} object{status=string,timestamp=string} "Service is alive"
// @Router /health/live [get]
func (h *HealthHandler) Live(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "alive",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}
