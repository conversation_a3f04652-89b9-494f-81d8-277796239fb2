package http

import (
	"blacking-api/internal/domain/crypto_deposit"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type CryptoDepositHandler struct {
	cryptoDepositService *service.CryptoDepositService
	logger               logger.Logger
}

func NewCryptoDepositHandler(cryptoDepositService *service.CryptoDepositService, logger logger.Logger) *CryptoDepositHandler {
	return &CryptoDepositHandler{
		cryptoDepositService: cryptoDepositService,
		logger:               logger,
	}
}

// GetConfiguration handles GET /api/v1/payment-gateway/crypto/config
// @Summary Get crypto deposit configuration
// @Description Retrieves configuration needed for crypto deposits including wallet addresses and supported networks
// @Tags CryptoDeposits
// @Accept json
// @Produce json
// @Success 200 {object} crypto_deposit.CryptoDepositConfigResponse
// @Failure 500 {object} object{error=string,message=string}
// @Security Bearer
// @Router /api/v1/payment-gateway/crypto/config [get]
func (h *CryptoDepositHandler) GetConfiguration(c *gin.Context) {
	h.logger.Info("GetConfiguration called")

	config, err := h.cryptoDepositService.GetConfiguration(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("failed to get crypto deposit configuration")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Configuration retrieval failed",
			"message": "Failed to retrieve crypto deposit configuration",
			"details": err.Error(), // Add error details for debugging
		})
		return
	}

	h.logger.Info("GetConfiguration successful")
	c.JSON(http.StatusOK, config)
}

// InitiateDeposit handles POST /api/v1/payment-gateway/crypto/initiate
// @Summary Initiate crypto deposit
// @Description Creates a new crypto deposit transaction record and returns transaction ID for tracking
// @Tags CryptoDeposits
// @Accept json
// @Produce json
// @Param request body crypto_deposit.InitiateCryptoDepositRequest true "Initiate deposit request"
// @Success 201 {object} crypto_deposit.InitiateDepositResponse
// @Failure 400 {object} object{error=string,message=string}
// @Failure 409 {object} object{error=string,message=string}
// @Failure 500 {object} object{error=string,message=string}
// @Security Bearer
// @Router /api/v1/payment-gateway/crypto/initiate [post]
func (h *CryptoDepositHandler) InitiateDeposit(c *gin.Context) {
	var req crypto_deposit.InitiateCryptoDepositRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("invalid initiate deposit request")
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "Request validation failed: " + err.Error(),
		})
		return
	}

	response, err := h.cryptoDepositService.InitiateDeposit(c.Request.Context(), &req)
	if err != nil {
		h.handleServiceError(c, err, "failed to initiate crypto deposit")
		return
	}

	h.logger.WithField("transaction_id", response.TransactionID).Info("crypto deposit initiated successfully")
	c.JSON(http.StatusCreated, response)
}

// UpdateStep1 handles PUT /api/v1/payment-gateway/crypto/{transactionId}/step1
// @Summary Update Step 1 status
// @Description Reports Step 1 transaction results (User wallet → Backend wallet)
// @Tags CryptoDeposits
// @Accept json
// @Produce json
// @Param transactionId path string true "Transaction ID from initiation"
// @Param request body crypto_deposit.UpdateStep1Request true "Step 1 update request"
// @Success 200 {object} crypto_deposit.UpdateStepResponse
// @Failure 400 {object} object{error=string,message=string}
// @Failure 404 {object} object{error=string,message=string}
// @Failure 409 {object} object{error=string,message=string}
// @Failure 500 {object} object{error=string,message=string}
// @Security Bearer
// @Router /api/v1/payment-gateway/crypto/{transactionId}/step1 [put]
func (h *CryptoDepositHandler) UpdateStep1(c *gin.Context) {
	transactionID := c.Param("transactionId")
	if transactionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "Transaction ID is required",
		})
		return
	}

	var req crypto_deposit.UpdateStep1Request
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("invalid update step1 request")
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "Request validation failed: " + err.Error(),
		})
		return
	}

	response, err := h.cryptoDepositService.UpdateStep1(c.Request.Context(), transactionID, &req)
	if err != nil {
		h.handleServiceError(c, err, "failed to update crypto deposit step1")
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"transaction_id": transactionID,
		"step1_status":   req.Status,
	}).Info("crypto deposit step1 updated successfully")
	c.JSON(http.StatusOK, response)
}

// UpdateStep2 handles PUT /api/v1/payment-gateway/crypto/{transactionId}/step2
// @Summary Update Step 2 status
// @Description Reports Step 2 transaction results (Backend wallet → Final recipient)
// @Tags CryptoDeposits
// @Accept json
// @Produce json
// @Param transactionId path string true "Transaction ID from initiation"
// @Param request body crypto_deposit.UpdateStep2Request true "Step 2 update request"
// @Success 200 {object} crypto_deposit.UpdateStepResponse
// @Failure 400 {object} object{error=string,message=string}
// @Failure 404 {object} object{error=string,message=string}
// @Failure 409 {object} object{error=string,message=string}
// @Failure 500 {object} object{error=string,message=string}
// @Security Bearer
// @Router /api/v1/payment-gateway/crypto/{transactionId}/step2 [put]
func (h *CryptoDepositHandler) UpdateStep2(c *gin.Context) {
	transactionID := c.Param("transactionId")
	if transactionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "Transaction ID is required",
		})
		return
	}

	var req crypto_deposit.UpdateStep2Request
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("invalid update step2 request")
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "Request validation failed: " + err.Error(),
		})
		return
	}

	response, err := h.cryptoDepositService.UpdateStep2(c.Request.Context(), transactionID, &req)
	if err != nil {
		h.handleServiceError(c, err, "failed to update crypto deposit step2")
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"transaction_id": transactionID,
		"step2_status":   req.Status,
		"overall_status": response.OverallStatus,
	}).Info("crypto deposit step2 updated successfully")
	c.JSON(http.StatusOK, response)
}

// GetDepositStatus handles GET /api/v1/payment-gateway/crypto/{transactionId}
// @Summary Get deposit status
// @Description Retrieves current status and details of a crypto deposit transaction
// @Tags CryptoDeposits
// @Accept json
// @Produce json
// @Param transactionId path string true "Transaction ID from initiation"
// @Success 200 {object} crypto_deposit.CryptoDepositResponse
// @Failure 404 {object} object{error=string,message=string}
// @Failure 500 {object} object{error=string,message=string}
// @Security Bearer
// @Router /api/v1/payment-gateway/crypto/{transactionId} [get]
func (h *CryptoDepositHandler) GetDepositStatus(c *gin.Context) {
	transactionID := c.Param("transactionId")
	if transactionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "Transaction ID is required",
		})
		return
	}

	deposit, err := h.cryptoDepositService.GetDepositByTransactionID(c.Request.Context(), transactionID)
	if err != nil {
		h.handleServiceError(c, err, "failed to get crypto deposit status")
		return
	}

	c.JSON(http.StatusOK, deposit)
}

// ListDeposits handles GET /api/v1/payment-gateway/crypto/deposits
// @Summary List crypto deposits
// @Description Retrieves a paginated list of crypto deposit transactions
// @Tags CryptoDeposits
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param page_size query int false "Items per page" default(20)
// @Param status query string false "Filter by overall status"
// @Param step1Status query string false "Filter by Step 1 status"
// @Param step2Status query string false "Filter by Step 2 status"
// @Param chainId query int false "Filter by blockchain network"
// @Param currency query string false "Filter by token currency"
// @Param customerUsername query string false "Filter by customer username"
// @Param startDate query string false "Start date filter (YYYY-MM-DD)"
// @Param endDate query string false "End date filter (YYYY-MM-DD)"
// @Param sort_by query string false "Sort field" default(created_at)
// @Param sort_order query string false "Sort order (asc/desc)" default(desc)
// @Success 200 {object} crypto_deposit.CryptoDepositListResponse
// @Failure 400 {object} object{error=string,message=string}
// @Failure 500 {object} object{error=string,message=string}
// @Security Bearer
// @Router /api/v1/payment-gateway/crypto/deposits [get]
func (h *CryptoDepositHandler) ListDeposits(c *gin.Context) {
	var filters crypto_deposit.CryptoDepositFilters

	// Parse pagination
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			filters.Page = page
		}
	}
	if filters.Page == 0 {
		filters.Page = 1
	}

	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 && pageSize <= 100 {
			filters.PageSize = pageSize
		}
	}
	if filters.PageSize == 0 {
		filters.PageSize = 20
	}

	// Parse filters
	filters.Status = c.Query("status")
	filters.Step1Status = c.Query("step1Status")
	filters.Step2Status = c.Query("step2Status")
	filters.Currency = c.Query("currency")
	filters.CustomerUsername = c.Query("customerUsername")

	if chainIDStr := c.Query("chainId"); chainIDStr != "" {
		if chainID, err := strconv.Atoi(chainIDStr); err == nil {
			filters.ChainID = &chainID
		}
	}

	// Parse dates
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if startDate, err := time.Parse("2006-01-02", startDateStr); err == nil {
			filters.StartDate = &startDate
		}
	}

	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if endDate, err := time.Parse("2006-01-02", endDateStr); err == nil {
			// Set to end of day
			endDate = endDate.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
			filters.EndDate = &endDate
		}
	}

	// Parse sort options
	filters.SortBy = c.DefaultQuery("sort_by", "created_at")
	filters.SortOrder = c.DefaultQuery("sort_order", "desc")

	// Validate sort fields
	validSortFields := map[string]bool{
		"created_at": true,
		"updated_at": true,
		"amount":     true,
		"status":     true,
	}
	if !validSortFields[filters.SortBy] {
		filters.SortBy = "created_at"
	}

	response, err := h.cryptoDepositService.ListDeposits(c.Request.Context(), &filters)
	if err != nil {
		h.handleServiceError(c, err, "failed to list crypto deposits")
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetDepositLogs handles GET /api/v1/payment-gateway/crypto/{transactionId}/logs
// @Summary Get deposit logs
// @Description Retrieves detailed logs for a specific crypto deposit transaction
// @Tags CryptoDeposits
// @Accept json
// @Produce json
// @Param transactionId path string true "Transaction ID"
// @Success 200 {array} crypto_deposit.CryptoDepositLog
// @Failure 404 {object} object{error=string,message=string}
// @Failure 500 {object} object{error=string,message=string}
// @Security Bearer
// @Router /api/v1/payment-gateway/crypto/{transactionId}/logs [get]
func (h *CryptoDepositHandler) GetDepositLogs(c *gin.Context) {
	transactionID := c.Param("transactionId")
	if transactionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "Transaction ID is required",
		})
		return
	}

	logs, err := h.cryptoDepositService.GetDepositLogs(c.Request.Context(), transactionID)
	if err != nil {
		h.handleServiceError(c, err, "failed to get crypto deposit logs")
		return
	}

	c.JSON(http.StatusOK, logs)
}

// UpdateBackendWalletBalance handles PUT /api/v1/payment-gateway/crypto/wallets/{walletAddress}/balance
// @Summary Update backend wallet balance
// @Description Updates the balance of a backend wallet for monitoring purposes
// @Tags CryptoDeposits
// @Accept json
// @Produce json
// @Param walletAddress path string true "Wallet address"
// @Param request body object{ethBalance=number,tokenBalance=number} true "Balance update"
// @Success 200 {object} object{message=string}
// @Failure 400 {object} object{error=string,message=string}
// @Failure 500 {object} object{error=string,message=string}
// @Security Bearer
// @Router /api/v1/payment-gateway/crypto/wallets/{walletAddress}/balance [put]
func (h *CryptoDepositHandler) UpdateBackendWalletBalance(c *gin.Context) {
	walletAddress := c.Param("walletAddress")
	if walletAddress == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "Wallet address is required",
		})
		return
	}

	var req struct {
		ETHBalance   float64 `json:"ethBalance" binding:"min=0"`
		TokenBalance float64 `json:"tokenBalance" binding:"min=0"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("invalid balance update request")
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "Request validation failed: " + err.Error(),
		})
		return
	}

	err := h.cryptoDepositService.UpdateBackendWalletBalance(c.Request.Context(), walletAddress, req.ETHBalance, req.TokenBalance)
	if err != nil {
		h.handleServiceError(c, err, "failed to update backend wallet balance")
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"wallet_address": walletAddress,
		"eth_balance":    req.ETHBalance,
		"token_balance":  req.TokenBalance,
	}).Info("backend wallet balance updated successfully")

	c.JSON(http.StatusOK, gin.H{
		"message": "Backend wallet balance updated successfully",
	})
}

// CalculateConversion handles GET /api/v1/payment-gateway/crypto/convert
// @Summary Calculate token to THB conversion
// @Description Calculate how much THB for given token amount using stored conversion rates
// @Tags CryptoDeposits
// @Accept json
// @Produce json
// @Param chainId query int true "Chain ID" example(84532)
// @Param tokenContract query string true "Token contract address" example("******************************************")
// @Param amount query number true "Token amount" example(10.0)
// @Success 200 {object} crypto_deposit.ConversionCalculationResponse
// @Failure 400 {object} object{error=string,message=string}
// @Failure 404 {object} object{error=string,message=string}
// @Failure 500 {object} object{error=string,message=string}
// @Security Bearer
// @Router /api/v1/payment-gateway/crypto/convert [get]
func (h *CryptoDepositHandler) CalculateConversion(c *gin.Context) {
	chainIDStr := c.Query("chainId")
	tokenContract := c.Query("tokenContract")
	amountStr := c.Query("amount")

	if chainIDStr == "" || tokenContract == "" || amountStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Missing parameters",
			"message": "chainId, tokenContract, and amount query parameters are required",
		})
		return
	}

	chainID, err := strconv.Atoi(chainIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid chainId",
			"message": "chainId must be a valid integer",
		})
		return
	}

	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid amount",
			"message": "amount must be a valid number",
		})
		return
	}

	if amount <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid amount",
			"message": "amount must be greater than 0",
		})
		return
	}

	response, err := h.cryptoDepositService.CalculateConversion(c.Request.Context(), chainID, tokenContract, amount)
	if err != nil {
		h.handleServiceError(c, err, "failed to calculate conversion")
		return
	}

	c.JSON(http.StatusOK, response)
}

// ListChainTokens handles GET /api/v1/payment-gateway/crypto/tokens
// @Summary List available chain tokens
// @Description Retrieves all available tokens with their conversion rates
// @Tags CryptoDeposits
// @Accept json
// @Produce json
// @Success 200 {array} crypto_deposit.ChainToken
// @Failure 500 {object} object{error=string,message=string}
// @Security Bearer
// @Router /api/v1/payment-gateway/crypto/tokens [get]
func (h *CryptoDepositHandler) ListChainTokens(c *gin.Context) {
	tokens, err := h.cryptoDepositService.ListChainTokens(c.Request.Context())
	if err != nil {
		h.handleServiceError(c, err, "failed to list chain tokens")
		return
	}

	c.JSON(http.StatusOK, tokens)
}

// UpdateChainTokenRate handles PUT /api/v1/payment-gateway/crypto/tokens/{tokenId}/rate
// @Summary Update token conversion rate
// @Description Updates the conversion rate for a specific token (admin only)
// @Tags CryptoDeposits
// @Accept json
// @Produce json
// @Param tokenId path int true "Token ID"
// @Param request body object{rate=number} true "New rate" example({"rate": 33.5})
// @Success 200 {object} object{message=string}
// @Failure 400 {object} object{error=string,message=string}
// @Failure 404 {object} object{error=string,message=string}
// @Failure 500 {object} object{error=string,message=string}
// @Security Bearer
// @Router /api/v1/payment-gateway/crypto/tokens/{tokenId}/rate [put]
func (h *CryptoDepositHandler) UpdateChainTokenRate(c *gin.Context) {
	tokenIDStr := c.Param("tokenId")
	if tokenIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "Token ID is required",
		})
		return
	}

	tokenID, err := strconv.ParseInt(tokenIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid token ID",
			"message": "Token ID must be a valid integer",
		})
		return
	}

	var req struct {
		Rate float64 `json:"rate" binding:"required,gt=0"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("invalid update token rate request")
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "Request validation failed: " + err.Error(),
		})
		return
	}

	err = h.cryptoDepositService.UpdateChainTokenRate(c.Request.Context(), tokenID, req.Rate)
	if err != nil {
		h.handleServiceError(c, err, "failed to update chain token rate")
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"token_id": tokenID,
		"new_rate": req.Rate,
	}).Info("chain token rate updated successfully")

	c.JSON(http.StatusOK, gin.H{
		"message": "Token conversion rate updated successfully",
	})
}

// Helper method to handle service errors consistently
func (h *CryptoDepositHandler) handleServiceError(c *gin.Context, err error, logMessage string) {
	h.logger.WithError(err).Error(logMessage)

	if appErr, ok := err.(*errors.AppError); ok {
		switch appErr.Type {
		case errors.ValidationError:
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Validation failed",
				"message": appErr.Message,
			})
		case errors.NotFoundError:
			c.JSON(http.StatusNotFound, gin.H{
				"error":   "Not found",
				"message": appErr.Message,
			})
		case errors.ConflictError:
			c.JSON(http.StatusConflict, gin.H{
				"error":   "Conflict",
				"message": appErr.Message,
			})
		case errors.DatabaseError:
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Database error",
				"message": "An internal database error occurred",
			})
		case errors.InternalError:
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Internal server error",
				"message": appErr.Message,
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Internal server error",
				"message": "An unexpected error occurred",
			})
		}
	} else {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal server error",
			"message": "An unexpected error occurred",
		})
	}
}
