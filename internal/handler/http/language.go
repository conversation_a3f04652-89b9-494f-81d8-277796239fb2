package http

import (
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"net/http"

	"github.com/gin-gonic/gin"
)

type LanguageHandler struct {
	languageService service.LanguageService
	logger          logger.Logger
}

// NewLanguageHandler creates a new language handler
func NewLanguageHandler(languageService service.LanguageService, logger logger.Logger) *LanguageHandler {
	return &LanguageHandler{
		languageService: languageService,
		logger:          logger,
	}
}

// GetSupportedLanguages handles GET /api/languages
// @Summary Get supported languages
// @Description Get a list of all supported languages in the system
// @Tags Language Management
// @Produce json
// @Success 200 {object} object{success=bool,data=[]object,message=string} "Languages retrieved successfully"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /languages [get]
func (h *LanguageHandler) GetSupportedLanguages(c *gin.Context) {
	// Get supported languages using service
	languages, err := h.languageService.GetSupportedLanguages(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    languages,
		"message": "Supported languages retrieved successfully",
	})
}

// GetActiveLanguages handles GET /api/languages/active
// @Summary Get active languages
// @Description Get a list of currently active languages
// @Tags Language Management
// @Produce json
// @Success 200 {object} object{success=bool,data=[]object,message=string} "Active languages retrieved successfully"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /languages/active [get]
func (h *LanguageHandler) GetActiveLanguages(c *gin.Context) {
	// Get active languages using service
	languages, err := h.languageService.GetActiveLanguages(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    languages,
		"message": "ActiveWithdrawAccount languages retrieved successfully",
	})
}

// GetLanguageByCode handles GET /api/languages/:code
// @Summary Get language by code
// @Description Get language information by language code
// @Tags Language Management
// @Produce json
// @Param code path string true "Language Code"
// @Success 200 {object} object{success=bool,data=object,message=string} "Language retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 404 {object} object{success=bool,error=string,message=string} "Language not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /languages/{code} [get]
func (h *LanguageHandler) GetLanguageByCode(c *gin.Context) {
	code := c.Param("code")
	if code == "" {
		c.Error(errors.NewValidationError("language code is required"))
		return
	}

	// Get language by code using service
	language, err := h.languageService.GetLanguageByCode(c.Request.Context(), code)
	if err != nil {
		c.Error(err)
		return
	}

	if language == nil {
		c.Error(errors.NewNotFoundError("language not found"))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    language,
		"message": "Language retrieved successfully",
	})
}

// ValidateLanguageCode handles POST /api/languages/validate
func (h *LanguageHandler) ValidateLanguageCode(c *gin.Context) {
	var req struct {
		Code string `json:"code" validate:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Validate language code using service
	isValid := h.languageService.ValidateLanguageCode(c.Request.Context(), req.Code)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"code":     req.Code,
			"is_valid": isValid,
		},
		"message": "Language code validation completed",
	})
}
