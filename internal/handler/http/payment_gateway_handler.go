package http

import (
	"blacking-api/internal/domain/payment_gateway_account"
	"blacking-api/internal/domain/payment_gateway_transaction"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/jaijaipay"
	"blacking-api/pkg/logger"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type PaymentGatewayHandler struct {
	paymentGatewayService *service.PaymentGatewayService
	transactionRepo       interfaces.PaymentGatewayTransactionRepository
	logger                logger.Logger
}

func NewPaymentGatewayHandler(paymentGatewayService *service.PaymentGatewayService, transactionRepo interfaces.PaymentGatewayTransactionRepository, logger logger.Logger) *PaymentGatewayHandler {
	return &PaymentGatewayHandler{
		paymentGatewayService: paymentGatewayService,
		transactionRepo:       transactionRepo,
		logger:                logger,
	}
}

// @Summary Get payment gateway providers
// @Description Get list of all payment gateway providers
// @Tags Payment Gateway
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /payment-gateway/providers [get]
func (h *PaymentGatewayHandler) GetProviders(c *gin.Context) {
	providers, err := h.paymentGatewayService.GetProviderList(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to get payment gateway providers",
			"error":   err.Error(),
		})
		return
	}

	var response []map[string]interface{}
	for _, provider := range providers {
		response = append(response, map[string]interface{}{
			"id":       provider.ID,
			"code":     provider.Code,
			"provider": provider.Provider,
			"name":     provider.AccountName,
			"active":   provider.Active,
			"deposit":  provider.IsDeposit,
			"withdraw": provider.IsWithdraw,
			"transfer": provider.IsTransfer,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// @Summary Get active payment gateway providers
// @Description Get list of active payment gateway providers
// @Tags Payment Gateway
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /payment-gateway/providers/active [get]
func (h *PaymentGatewayHandler) GetActiveProviders(c *gin.Context) {
	providers, err := h.paymentGatewayService.GetActiveProviders(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to get active payment gateway providers",
			"error":   err.Error(),
		})
		return
	}

	var response []map[string]interface{}
	for _, provider := range providers {
		response = append(response, map[string]interface{}{
			"id":       provider.ID,
			"code":     provider.Code,
			"provider": provider.Provider,
			"name":     provider.AccountName,
			"active":   provider.Active,
			"deposit":  provider.IsDeposit,
			"withdraw": provider.IsWithdraw,
			"transfer": provider.IsTransfer,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// @Summary Update payment gateway provider status
// @Description Update the active status of a payment gateway provider
// @Tags Payment Gateway
// @Accept json
// @Produce json
// @Param id path int true "Provider ID"
// @Param request body map[string]interface{} true "Status update request"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /payment-gateway/providers/{id}/status [put]
func (h *PaymentGatewayHandler) UpdateProviderStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid provider ID",
		})
		return
	}

	var request struct {
		Active bool `json:"active" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	err = h.paymentGatewayService.UpdateProviderStatus(c.Request.Context(), id, request.Active)
	if err != nil {
		if errors.IsNotFoundError(err) {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "Payment gateway provider not found",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to update provider status",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Provider status updated successfully",
	})
}

// @Summary Create payment gateway provider
// @Description Create a new payment gateway provider
// @Tags Payment Gateway
// @Accept json
// @Produce json
// @Param request body payment_gateway_account.PaymentGatewayAccountRequest true "Create provider request"
// @Success 201 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /payment-gateway/providers [post]
func (h *PaymentGatewayHandler) CreateProvider(c *gin.Context) {
	var request payment_gateway_account.PaymentGatewayAccountRequest

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	provider, err := h.paymentGatewayService.CreateProvider(c.Request.Context(), &request)
	if err != nil {
		if errors.IsValidationError(err) {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to create payment gateway provider",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Provider created successfully",
		"data": map[string]interface{}{
			"id":       provider.ID,
			"code":     provider.Code,
			"provider": provider.Provider,
			"name":     provider.AccountName,
			"active":   provider.Active,
		},
	})
}

// JaiJaiPay specific endpoints

// @Summary Create JaiJaiPay client
// @Description Test JaiJaiPay connection using database configuration
// @Tags Payment Gateway
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /payment-gateway/jaijaipay/test [post]
func (h *PaymentGatewayHandler) TestJaiJaiPayConnection(c *gin.Context) {
	_, err := h.paymentGatewayService.CreateJaiJaiPayClient(c.Request.Context())
	if err != nil {
		if errors.IsNotFoundError(err) {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "No active JaiJaiPay configuration found",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to create JaiJaiPay client",
			"error":   err.Error(),
		})
		return
	}

	// Test basic client functionality (you might want to add a ping/health check method to JaiJaiPay client)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "JaiJaiPay client created successfully from database configuration",
		"data": map[string]interface{}{
			"configured":  true,
			"provider":    "jaijaipay",
			"description": "Connected using database configuration instead of environment variables",
		},
	})
}

// @Summary Get JaiJaiPay balance
// @Description Get balance from JaiJaiPay using database configuration
// @Tags Payment Gateway
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /payment-gateway/jaijaipay/balance [get]
func (h *PaymentGatewayHandler) GetJaiJaiPayBalance(c *gin.Context) {
	client, err := h.paymentGatewayService.CreateJaiJaiPayClient(c.Request.Context())
	if err != nil {
		if errors.IsNotFoundError(err) {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "No active JaiJaiPay configuration found",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to create JaiJaiPay client",
			"error":   err.Error(),
		})
		return
	}

	balance, err := client.Balance.Get(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to get JaiJaiPay balance",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Balance retrieved successfully",
		"data":    balance,
		"source":  "Database configuration",
	})
}

// @Summary Create JaiJaiPay deposit
// @Description Create a deposit transaction with JaiJaiPay using database configuration
// @Tags Payment Gateway
// @Accept json
// @Produce json
// @Param request body map[string]interface{} true "Deposit request"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /payment-gateway/jaijaipay/deposits [post]
func (h *PaymentGatewayHandler) CreateJaiJaiPayDeposit(c *gin.Context) {
	_, err := h.paymentGatewayService.CreateJaiJaiPayClient(c.Request.Context())
	if err != nil {
		if errors.IsNotFoundError(err) {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "No active JaiJaiPay configuration found",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to create JaiJaiPay client",
			"error":   err.Error(),
		})
		return
	}

	var request map[string]interface{}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	// Note: You'll need to implement the CreateDeposit method in your JaiJaiPay client
	// For now, this is a placeholder showing the flow
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Deposit request processed via database configuration",
		"data":    request,
		"source":  "Database configuration (JaiJaiPay gateway)",
		"note":    "This endpoint needs to be implemented with actual JaiJaiPay deposit logic",
	})
}

// @Summary Resend webhook
// @Description Resend webhook for a specific transaction using database configuration
// @Tags Payment Gateway
// @Accept json
// @Produce json
// @Param request body map[string]interface{} true "Resend webhook request"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /payment-gateway/jaijaipay/webhooks/resend [post]
func (h *PaymentGatewayHandler) ResendJaiJaiPayWebhook(c *gin.Context) {
	var request struct {
		TransactionID string `json:"transactionId" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	// Get JaiJaiPay client from database configuration
	client, err := h.paymentGatewayService.CreateJaiJaiPayClient(c.Request.Context())
	if err != nil {
		if errors.IsNotFoundError(err) {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "No active JaiJaiPay configuration found",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to create JaiJaiPay client",
			"error":   err.Error(),
		})
		return
	}

	// Create resend webhook request
	resendReq := &jaijaipay.ResendWebhookRequest{
		TransactionID: request.TransactionID,
	}

	result, err := client.Webhooks.Resend(c.Request.Context(), resendReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to resend webhook",
			"error":   err.Error(),
		})
		return
	}

	// Log resend action to transaction table (don't duplicate if exists)
	exists, existsErr := h.transactionRepo.ExistsByTransactionID(c.Request.Context(), request.TransactionID)
	if existsErr == nil && !exists {
		// Create transaction record for the resend action
		transaction := &payment_gateway_transaction.PaymentGatewayTransaction{
			TransactionID:           request.TransactionID,
			InternalReference:       "WEBHOOK_RESEND",
			PaymentGatewayAccountID: 1, // Will be validated
			Provider:                "jaijaipay",
			TransactionType:         "TRANSFER", // Use valid enum value instead of WEBHOOK_RESEND
			Amount:                  0,
			Currency:                "THB",
			Status:                  "COMPLETED",
			Description:             "Webhook resend action via database configuration",
			Metadata: map[string]interface{}{
				"resend_action": true,
				"resent_at":     result.ResentAt,
				"resend_count":  result.PreviousResendCount + 1,
				"source":        "database_configuration",
				"endpoint":      "/api/v1/payment-gateway/jaijaipay/webhooks/resend",
			},
			ProviderResponse: map[string]interface{}{
				"success":               result.Success,
				"message":               result.Message,
				"transaction_id":        result.TransactionID,
				"resent_at":             result.ResentAt,
				"previous_resend_count": result.PreviousResendCount,
				"warning":               result.Warning,
			},
			InitiatedAt: &[]time.Time{time.Now()}[0],
			CreatedBy:   "webhook_resend_api",
		}

		if createErr := h.transactionRepo.Create(c.Request.Context(), transaction); createErr != nil {
			// Log error but don't fail the response since resend was successful
			h.logger.WithError(createErr).WithField("transaction_id", request.TransactionID).
				Warn("failed to log resend action to transaction table")
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Webhook resent successfully",
		"data":    result,
		"source":  "Database configuration (JaiJaiPay gateway)",
	})
}

// @Summary Get payment gateway transactions
// @Description Get list of payment gateway transactions with filtering and pagination
// @Tags Payment Gateway
// @Accept json
// @Produce json
// @Param transaction_id query string false "Transaction ID"
// @Param internal_reference query string false "Internal Reference"
// @Param provider query string false "Provider"
// @Param status query string false "Status"
// @Param transaction_type query string false "Transaction Type"
// @Param customer_username query string false "Customer Username"
// @Param customer_reference query string false "Customer Reference"
// @Param customer_bank_account query string false "Customer Bank Account"
// @Param customer_bank_name query string false "Customer Bank Name"
// @Param payment_gateway_account_id query int false "Payment Gateway Account ID"
// @Param min_amount query number false "Minimum Amount"
// @Param max_amount query number false "Maximum Amount"
// @Param created_from_date query string false "Created From Date (YYYY-MM-DD)"
// @Param created_to_date query string false "Created To Date (YYYY-MM-DD)"
// @Param initiated_from_date query string false "Initiated From Date (YYYY-MM-DD)"
// @Param initiated_to_date query string false "Initiated To Date (YYYY-MM-DD)"
// @Param completed_from_date query string false "Completed From Date (YYYY-MM-DD)"
// @Param completed_to_date query string false "Completed To Date (YYYY-MM-DD)"
// @Param has_error query bool false "Has Error"
// @Param sort_by query string false "Sort By (id, transaction_id, provider, status, transaction_type, amount, created_at, updated_at, initiated_at, completed_at, customer_username)"
// @Param sort_order query string false "Sort Order (asc, desc)"
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 10, max: 100)"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /payment-gateway/transactions [get]
func (h *PaymentGatewayHandler) GetTransactions(c *gin.Context) {
	var filters payment_gateway_transaction.TransactionFilters

	// Bind query parameters to filters struct
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid query parameters",
			"error":   err.Error(),
		})
		return
	}

	// Parse date strings if provided
	if dateFrom := c.Query("created_from_date"); dateFrom != "" {
		if parsedDate, err := time.Parse("2006-01-02", dateFrom); err == nil {
			filters.CreatedFromDate = &parsedDate
		}
	}
	if dateTo := c.Query("created_to_date"); dateTo != "" {
		if parsedDate, err := time.Parse("2006-01-02", dateTo); err == nil {
			filters.CreatedToDate = &parsedDate
		}
	}
	if dateFrom := c.Query("initiated_from_date"); dateFrom != "" {
		if parsedDate, err := time.Parse("2006-01-02", dateFrom); err == nil {
			filters.InitiatedFromDate = &parsedDate
		}
	}
	if dateTo := c.Query("initiated_to_date"); dateTo != "" {
		if parsedDate, err := time.Parse("2006-01-02", dateTo); err == nil {
			filters.InitiatedToDate = &parsedDate
		}
	}
	if dateFrom := c.Query("completed_from_date"); dateFrom != "" {
		if parsedDate, err := time.Parse("2006-01-02", dateFrom); err == nil {
			filters.CompletedFromDate = &parsedDate
		}
	}
	if dateTo := c.Query("completed_to_date"); dateTo != "" {
		if parsedDate, err := time.Parse("2006-01-02", dateTo); err == nil {
			filters.CompletedToDate = &parsedDate
		}
	}

	// Get transactions using the repository
	result, err := h.transactionRepo.GetTransactions(c.Request.Context(), &filters)
	if err != nil {
		h.logger.WithError(err).Error("failed to get transactions")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to get transactions",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"message":    "Transactions retrieved successfully",
		"data":       result.Transactions,
		"pagination": result.Pagination,
	})
}

// @Summary Get payment gateway transaction by ID
// @Description Get a specific payment gateway transaction by transaction ID
// @Tags Payment Gateway
// @Accept json
// @Produce json
// @Param id path string true "Transaction ID"
// @Success 200 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /payment-gateway/transactions/{id} [get]
func (h *PaymentGatewayHandler) GetTransactionByID(c *gin.Context) {
	transactionID := c.Param("id")
	if transactionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Transaction ID is required",
		})
		return
	}

	transaction, err := h.transactionRepo.GetByTransactionID(c.Request.Context(), transactionID)
	if err != nil {
		h.logger.WithError(err).WithField("transaction_id", transactionID).Error("failed to get transaction by ID")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to get transaction",
			"error":   err.Error(),
		})
		return
	}

	if transaction == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Transaction not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Transaction retrieved successfully",
		"data":    transaction,
	})
}
