package http

import (
	"net/http"
	"strconv"

	"blacking-api/internal/domain/user"
	"blacking-api/internal/service"
	"blacking-api/pkg/auth"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

type UserHandler struct {
	userService service.UserService
	logger      logger.Logger
}

func NewUserHandler(userService service.UserService, logger logger.Logger) *UserHandler {
	return &UserHandler{
		userService: userService,
		logger:      logger,
	}
}

// Create<PERSON>ser creates a new user account
// @Summary Create user
// @Description Create a new user account with admin privileges
// @Tags User Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body user.CreateUserRequest true "Create user request"
// @Success 201 {object} object{success=bool,data=user.UserResponse} "User created successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /users [post]
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req user.CreateUserRequest
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	userResp, err := h.userService.CreateUser(c.Request.Context(), req, strconv.Itoa(adminId), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    userResp,
	})
}

// GetUser gets a user by ID
// @Summary Get user by ID
// @Description Get a specific user by their ID
// @Tags User Management
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Success 200 {object} object{success=bool,data=user.UserResponse} "User retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 404 {object} object{success=bool,error=string,message=string} "User not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /users/{id} [get]
func (h *UserHandler) GetUser(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("user ID is required"))
		return
	}

	userResp, err := h.userService.GetUserByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    userResp,
	})
}

// UpdateUser updates an existing user
// @Summary Update user
// @Description Update an existing user account by ID
// @Tags User Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Param request body user.UpdateUserRequest true "Update user request"
// @Success 200 {object} object{success=bool,data=user.UserResponse} "User updated successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "User not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/users/{id} [put]
func (h *UserHandler) UpdateUser(c *gin.Context) {
	id := c.Param("id")
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if id == "" {
		c.Error(errors.NewValidationError("user ID is required"))
		return
	}

	var req user.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	userResp, err := h.userService.UpdateUser(c.Request.Context(), id, req, strconv.Itoa(adminId), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    userResp,
	})
}

// DeleteUser deletes a user account
// @Summary Delete user
// @Description Delete a user account by ID
// @Tags User Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Success 200 {object} object{success=bool,message=string} "User deleted successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "User not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/users/{id} [delete]
func (h *UserHandler) DeleteUser(c *gin.Context) {
	id := c.Param("id")
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)
	if id == "" {
		c.Error(errors.NewValidationError("user ID is required"))
		return
	}

	if err := h.userService.DeleteUser(c.Request.Context(), id, strconv.Itoa(adminId), adminName); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "user deleted successfully",
	})
}

// ListUsers retrieves a paginated list of users
// @Summary List users
// @Description Get a paginated list of users with optional filtering
// @Tags User Management
// @Produce json
// @Security BearerAuth
// @Param limit query int false "Number of items per page" default(10)
// @Param offset query int false "Number of items to skip" default(0)
// @Param search query string false "Search term for filtering users"
// @Param user_role_id query int false "Filter by user role ID"
// @Success 200 {object} object{success=bool,data=object{users=[]user.UserResponse,pagination=object{total=int,limit=int,offset=int}}} "Users retrieved successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/users [get]
func (h *UserHandler) ListUsers(c *gin.Context) {
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")
	search := c.Query("search")              // รับพารามิเตอร์ search
	userRoleIDStr := c.Query("user_role_id") // รับพารามิเตอร์ user_role_id

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid limit parameter"))
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid offset parameter"))
		return
	}

	// Convert user_role_id to integer if provided
	var userRoleID *int
	if userRoleIDStr != "" {
		roleID, err := strconv.Atoi(userRoleIDStr)
		if err != nil {
			c.Error(errors.NewValidationError("invalid user_role_id parameter"))
			return
		}
		userRoleID = &roleID
	}

	users, err := h.userService.ListUsers(c.Request.Context(), limit, offset, search, userRoleID)
	if err != nil {
		c.Error(err)
		return
	}

	count, err := h.userService.GetUsersCount(c.Request.Context(), search, userRoleID)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"users": users,
			"pagination": gin.H{
				"total":  count,
				"limit":  limit,
				"offset": offset,
			},
		},
	})
}

// ActivateUser activates a user account
// @Summary Activate user
// @Description Activate a user account by ID
// @Tags User Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Success 200 {object} object{success=bool,message=string} "User activated successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "User not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/users/{id}/activate [patch]
func (h *UserHandler) ActivateUser(c *gin.Context) {
	id := c.Param("id")
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if id == "" {
		c.Error(errors.NewValidationError("user ID is required"))
		return
	}

	if err := h.userService.ActivateUser(c.Request.Context(), id, strconv.Itoa(adminId), adminName); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "user activated successfully",
	})
}

// DeactivateUser deactivates a user account
// @Summary Deactivate user
// @Description Deactivate a user account by ID
// @Tags User Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Success 200 {object} object{success=bool,message=string} "User deactivated successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "User not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/users/{id}/deactivate [patch]
func (h *UserHandler) DeactivateUser(c *gin.Context) {
	id := c.Param("id")
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if id == "" {
		c.Error(errors.NewValidationError("user ID is required"))
		return
	}

	if err := h.userService.DeactivateUser(c.Request.Context(), id, strconv.Itoa(adminId), adminName); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "user deactivated successfully",
	})
}

// SuspendUser suspends a user account
// @Summary Suspend user
// @Description Suspend a user account by ID
// @Tags User Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Success 200 {object} object{success=bool,message=string} "User suspended successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "User not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/users/{id}/suspend [patch]
func (h *UserHandler) SuspendUser(c *gin.Context) {
	id := c.Param("id")
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if id == "" {
		c.Error(errors.NewValidationError("user ID is required"))
		return
	}

	if err := h.userService.SuspendUser(c.Request.Context(), id, strconv.Itoa(adminId), adminName); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "user suspended successfully",
	})
}

type ChangePasswordRequest struct {
	NewPassword     string `json:"new_password" validate:"required,min=8"`
	ConfirmPassword string `json:"confirm_password" validate:"required,min=8"`
}

// ChangePassword changes a user's password
// @Summary Change user password
// @Description Change a user's password by ID
// @Tags User Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Param request body ChangePasswordRequest true "Change password request"
// @Success 200 {object} object{success=bool,message=string} "Password changed successfully"
// @Failure 400 {object} object{success=bool,error=string,message=string} "Bad request"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 404 {object} object{success=bool,error=string,message=string} "User not found"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /api/v1/users/{id}/password [patch]
func (h *UserHandler) ChangePassword(c *gin.Context) {
	id := c.Param("id")
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if id == "" {
		c.Error(errors.NewValidationError("user ID is required"))
		return
	}

	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	if req.NewPassword != req.ConfirmPassword {
		c.Error(errors.NewValidationError("new password and confirm password do not match"))
		return
	}

	if err := h.userService.ChangePassword(c.Request.Context(), id, req.NewPassword, strconv.Itoa(adminId), adminName); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "password changed successfully",
	})
}

// GetUsersForDropdown returns users formatted for dropdown selection
// @Summary Get users for dropdown
// @Description Get list of users formatted for dropdown selection (id, name). If page=member, adds special entry "ลูกค้าสมัครเอง" with id=0
// @Tags User Management
// @Produce json
// @Param page query string false "Page context (e.g., 'member' to add special entry)"
// @Success 200 {object} object{success=bool,data=user.UserDropdownResponse,message=string} "Users for dropdown retrieved successfully"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /users/dropdown [get]
// @Security BearerAuth
func (h *UserHandler) GetUsersForDropdown(c *gin.Context) {
	page := c.Query("page")

	users, err := h.userService.GetUsersForDropdown(c.Request.Context(), page)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    users,
		"message": "Users for dropdown retrieved successfully",
	})
}

// GetCurrentAdmin returns the current authenticated user
// @Summary Get current user
// @Description Get the currently authenticated user information
// @Tags User Management
// @Produce json
// @Success 200 {object} object{success=bool,data=user.UserResponse} "Current user retrieved successfully"
// @Failure 401 {object} object{success=bool,error=string,message=string} "Unauthorized"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /users/me [get]
// @Security BearerAuth
func (h *UserHandler) GetCurrentAdmin(c *gin.Context) {
	userID := auth.GetUserIDFromContext(c)
	if userID == 0 {
		c.Error(errors.NewUnauthorizedError("user not authenticated"))
		return
	}

	userResp, err := h.userService.GetUserByID(c.Request.Context(), strconv.Itoa(userID))
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    userResp,
	})
}
