package http

import (
	"blacking-api/internal/service"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"net/http"
)

type Algorithm<PERSON>andler struct {
	algorithmService service.AlgorithmService
	logger           logger.Logger
}

func NewAlgorithmHandler(algorithmService service.AlgorithmService, logger logger.Logger) *AlgorithmHandler {
	return &AlgorithmHandler{
		algorithmService: algorithmService,
		logger:           logger,
	}
}

// ListAlgorithms lists all available algorithms
// @Summary List algorithms
// @Description Get a list of all available algorithms for banking operations
// @Tags Banking Management
// @Produce json
// @Security BearerAuth
// @Success 200 {object} object{success=bool,data=[]object} "Algorithms retrieved successfully"
// @Failure 500 {object} object{success=bool,error=string,message=string} "Internal server error"
// @Router /algorithm [get]
func (handler *AlgorithmHandler) ListAlgorithms(c *gin.Context) {
	algorithms, err := handler.algorithmService.ListAlgorithms(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSO<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    algorithms,
	})
}
