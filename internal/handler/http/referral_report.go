package http

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	_ "blacking-api/internal/domain/common" // For Swagger documentation
	"blacking-api/internal/domain/report"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"

	"github.com/gin-gonic/gin"
)

type ReferralReportHandler struct {
	service *service.ReferralReportService
}

func NewReferralReportHandler(service *service.ReferralReportService) *ReferralReportHandler {
	return &ReferralReportHandler{
		service: service,
	}
}

// GetReferralReportSummary handles GET /api/admin/reports/referral/summary
// @Summary Get referral report summary
// @Description Get referral report summary grouped by member with date filtering, search, and pagination
// @Tags Referral Reports
// @Accept json
// @Produce json
// @Param start_date query string false "Start date (YYYY-MM-DD)" format(date)
// @Param end_date query string false "End date (YYYY-MM-DD)" format(date)
// @Param search query string false "Search by username or phone"
// @Param phone query string false "Exact phone match"
// @Param username query string false "Exact username match"
// @Param status query []string false "Status filter" collectionFormat(multi) Enums(pending,success,cancel)
// @Param type query []string false "Type filter" collectionFormat(multi) Enums(commission,withdraw,adjustment,bonus)
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Records per page" default(20)
// @Param order_by query string false "Sort field" default(member_id) Enums(member_id,total_commission_amount,total_withdraw_amount,net_amount,transaction_count,created_at)
// @Param order_dir query string false "Sort direction" default(desc) Enums(asc,desc)
// @Success 200 {object} report.ReferralReportSummaryResponse
// @Failure 400 {object} common.ErrorResponse
// @Failure 500 {object} common.ErrorResponse
// @Router /api/admin/reports/referral/summary [get]
func (h *ReferralReportHandler) GetReferralReportSummary(c *gin.Context) {
	filter, err := h.parseReportFilter(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid parameters",
			"message": err.Error(),
		})
		return
	}

	// Validate filter
	if err := h.service.ValidateReportFilter(&filter); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation error",
			"message": err.Error(),
		})
		return
	}

	response, err := h.service.GetReferralReportSummary(c.Request.Context(), filter)
	if err != nil {
		if errors.IsValidationError(err) {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "validation error",
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "internal server error",
			"message": "failed to get referral report summary",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetReferralReportDetail handles GET /api/admin/reports/referral/detail/:member_id
// @Summary Get referral report detail grouped by downline
// @Description Get detailed referral transactions for a specific member grouped by downline_member_id with sum
// @Tags Referral Reports
// @Accept json
// @Produce json
// @Param member_id path int true "Member ID"
// @Param start_date query string false "Start date (YYYY-MM-DD)" format(date)
// @Param end_date query string false "End date (YYYY-MM-DD)" format(date)
// @Param search query string false "Search by username or phone"
// @Param phone query string false "Exact phone match"
// @Param username query string false "Exact username match"
// @Param status query []string false "Status filter" collectionFormat(multi) Enums(pending,success,cancel)
// @Param type query []string false "Type filter" collectionFormat(multi) Enums(commission,withdraw,adjustment,bonus)
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Records per page" default(20)
// @Param order_dir query string false "Sort direction" default(desc) Enums(asc,desc)
// @Success 200 {object} report.ReferralReportDetailGroupedResponse
// @Failure 400 {object} common.ErrorResponse
// @Failure 500 {object} common.ErrorResponse
// @Router /api/admin/reports/referral/detail/{member_id} [get]
func (h *ReferralReportHandler) GetReferralReportDetail(c *gin.Context) {
	memberIDStr := c.Param("member_id")
	memberID, err := strconv.Atoi(memberIDStr)
	if err != nil || memberID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid parameter",
			"message": "invalid member_id",
		})
		return
	}

	filter, err := h.parseReportFilter(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid parameters",
			"message": err.Error(),
		})
		return
	}

	// Validate filter
	if err := h.service.ValidateReportFilter(&filter); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation error",
			"message": err.Error(),
		})
		return
	}

	// Use grouped detail by default now
	response, err := h.service.GetReferralReportDetailGrouped(c.Request.Context(), memberID, filter)
	if err != nil {
		if errors.IsValidationError(err) {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "validation error",
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "internal server error",
			"message": "failed to get referral report grouped detail",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ExportReferralReportSummaryCSV handles GET /api/admin/reports/referral/summary/export
// @Summary Export referral report summary as CSV
// @Description Export referral report summary to CSV file with date filtering and search
// @Tags Referral Reports
// @Accept json
// @Produce text/csv
// @Param start_date query string false "Start date (YYYY-MM-DD)" format(date)
// @Param end_date query string false "End date (YYYY-MM-DD)" format(date)
// @Param search query string false "Search by username or phone"
// @Param phone query string false "Exact phone match"
// @Param username query string false "Exact username match"
// @Param status query []string false "Status filter" collectionFormat(multi) Enums(pending,success,cancel)
// @Param type query []string false "Type filter" collectionFormat(multi) Enums(commission,withdraw,adjustment,bonus)
// @Success 200 {file} csv
// @Failure 400 {object} common.ErrorResponse
// @Failure 500 {object} common.ErrorResponse
// @Router /api/admin/reports/referral/summary/export [get]
func (h *ReferralReportHandler) ExportReferralReportSummaryCSV(c *gin.Context) {
	filter, err := h.parseReportFilter(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid parameters",
			"message": err.Error(),
		})
		return
	}

	// Validate filter
	if err := h.service.ValidateReportFilter(&filter); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation error",
			"message": err.Error(),
		})
		return
	}

	csvData, err := h.service.ExportReferralReportSummaryCSV(c.Request.Context(), filter)
	if err != nil {
		if errors.IsValidationError(err) {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "validation error",
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "internal server error",
			"message": "failed to export referral report summary",
		})
		return
	}

	// Generate filename with date range
	filename := h.generateCSVFilename("referral_summary", filter.StartDate, filter.EndDate)

	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	c.Data(http.StatusOK, "text/csv", csvData)
}

// ExportReferralReportDetailCSV handles GET /api/admin/reports/referral/detail/:member_id/export
// @Summary Export referral report grouped detail as CSV
// @Description Export detailed referral transactions grouped by downline for a specific member to CSV file
// @Tags Referral Reports
// @Accept json
// @Produce text/csv
// @Param member_id path int true "Member ID"
// @Param start_date query string false "Start date (YYYY-MM-DD)" format(date)
// @Param end_date query string false "End date (YYYY-MM-DD)" format(date)
// @Param search query string false "Search by username or phone"
// @Param phone query string false "Exact phone match"
// @Param username query string false "Exact username match"
// @Param status query []string false "Status filter" collectionFormat(multi) Enums(pending,success,cancel)
// @Param type query []string false "Type filter" collectionFormat(multi) Enums(commission,withdraw,adjustment,bonus)
// @Success 200 {file} csv
// @Failure 400 {object} common.ErrorResponse
// @Failure 500 {object} common.ErrorResponse
// @Router /api/admin/reports/referral/detail/{member_id}/export [get]
func (h *ReferralReportHandler) ExportReferralReportDetailCSV(c *gin.Context) {
	memberIDStr := c.Param("member_id")
	memberID, err := strconv.Atoi(memberIDStr)
	if err != nil || memberID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid parameter",
			"message": "invalid member_id",
		})
		return
	}

	filter, err := h.parseReportFilter(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid parameters",
			"message": err.Error(),
		})
		return
	}

	// Validate filter
	if err := h.service.ValidateReportFilter(&filter); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation error",
			"message": err.Error(),
		})
		return
	}

	// Use grouped detail CSV export
	csvData, err := h.service.ExportReferralReportDetailGroupedCSV(c.Request.Context(), memberID, filter)
	if err != nil {
		if errors.IsValidationError(err) {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "validation error",
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "internal server error",
			"message": "failed to export referral report grouped detail",
		})
		return
	}

	// Generate filename with member ID and date range
	filename := h.generateCSVFilename(fmt.Sprintf("referral_grouped_detail_member_%d", memberID), filter.StartDate, filter.EndDate)

	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	c.Data(http.StatusOK, "text/csv", csvData)
}

// parseReportFilter parses query parameters into ReferralReportFilter
func (h *ReferralReportHandler) parseReportFilter(c *gin.Context) (report.ReferralReportFilter, error) {
	filter := report.ReferralReportFilter{}

	// Parse dates
	if startDateStr := c.Query("start_date"); startDateStr != "" {
		startDate, err := time.Parse("2006-01-02", startDateStr)
		if err != nil {
			return filter, fmt.Errorf("invalid start_date format, expected YYYY-MM-DD")
		}
		// Set to start of day
		startOfDay := time.Date(startDate.Year(), startDate.Month(), startDate.Day(), 0, 0, 0, 0, startDate.Location())
		filter.StartDate = &startOfDay
	}

	if endDateStr := c.Query("end_date"); endDateStr != "" {
		endDate, err := time.Parse("2006-01-02", endDateStr)
		if err != nil {
			return filter, fmt.Errorf("invalid end_date format, expected YYYY-MM-DD")
		}
		// Set to end of day
		endOfDay := time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 59, 999999999, endDate.Location())
		filter.EndDate = &endOfDay
	}

	// Parse search parameters
	filter.Search = strings.TrimSpace(c.Query("search"))
	filter.Phone = strings.TrimSpace(c.Query("phone"))
	filter.Username = strings.TrimSpace(c.Query("username"))

	// Parse member_id from query (for filtering)
	if memberIDStr := c.Query("member_id"); memberIDStr != "" {
		if memberID, err := strconv.Atoi(memberIDStr); err == nil && memberID > 0 {
			filter.MemberID = &memberID
		}
	}

	// Parse status filter (multi-value)
	if statusValues := c.QueryArray("status"); len(statusValues) > 0 {
		var statuses []string
		for _, status := range statusValues {
			if status = strings.TrimSpace(status); status != "" {
				statuses = append(statuses, status)
			}
		}
		filter.Status = statuses
	}

	// Parse type filter (multi-value)
	if typeValues := c.QueryArray("type"); len(typeValues) > 0 {
		var types []string
		for _, transactionType := range typeValues {
			if transactionType = strings.TrimSpace(transactionType); transactionType != "" {
				types = append(types, transactionType)
			}
		}
		filter.Type = types
	}

	// Parse pagination
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			filter.Page = page
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 {
			filter.Limit = limit
		}
	}

	// Parse sorting
	filter.OrderBy = strings.TrimSpace(c.Query("order_by"))
	filter.OrderDir = strings.TrimSpace(c.Query("order_dir"))

	return filter, nil
}

// generateCSVFilename generates a filename for CSV export
func (h *ReferralReportHandler) generateCSVFilename(prefix string, startDate, endDate *time.Time) string {
	now := time.Now()
	dateStr := now.Format("20060102_150405")

	if startDate != nil && endDate != nil {
		dateRange := fmt.Sprintf("%s_to_%s",
			startDate.Format("20060102"),
			endDate.Format("20060102"))
		return fmt.Sprintf("%s_%s_%s.csv", prefix, dateRange, dateStr)
	}

	return fmt.Sprintf("%s_%s.csv", prefix, dateStr)
}

// GetCommissionByGameCategory handles GET /api/admin/reports/referral/commission-by-game-category
// @Summary Get commission transactions by game category
// @Description Get commission transactions filtered by game category with date range and pagination
// @Tags Referral Reports
// @Accept json
// @Produce json
// @Param game_category query string true "Game category" Enums(Slot,Live-Casino,Sport)
// @Param start_date query string false "Start date (YYYY-MM-DD)" format(date)
// @Param end_date query string false "End date (YYYY-MM-DD)" format(date)
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Records per page" default(20)
// @Success 200 {object} report.CommissionByGameCategoryResponse
// @Failure 400 {object} common.ErrorResponse
// @Failure 500 {object} common.ErrorResponse
// @Router /api/admin/reports/referral/commission-by-game-category [get]
func (h *ReferralReportHandler) GetCommissionByGameCategory(c *gin.Context) {
	filter, err := h.parseCommissionByGameCategoryFilter(c)
	if err != nil {
		c.Error(err)
		return
	}

	response, err := h.service.GetCommissionByGameCategory(c.Request.Context(), filter)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response.Data,
		"pagination": response.Pagination,
		"summary": response.Summary,
		"message": "Commission by game category report retrieved successfully",
	})
}

// parseCommissionByGameCategoryFilter parses query parameters into CommissionByGameCategoryFilter
func (h *ReferralReportHandler) parseCommissionByGameCategoryFilter(c *gin.Context) (report.CommissionByGameCategoryFilter, error) {
	var filter report.CommissionByGameCategoryFilter

	// Parse game category (required)
	filter.GameCategory = strings.TrimSpace(c.Query("game_category"))

	// Parse date range
	if startDateStr := c.Query("start_date"); startDateStr != "" {
		if startDate, err := time.Parse("2006-01-02", startDateStr); err == nil {
			filter.StartDate = &startDate
		} else {
			return filter, errors.NewValidationError("invalid start_date format: use YYYY-MM-DD")
		}
	}

	if endDateStr := c.Query("end_date"); endDateStr != "" {
		if endDate, err := time.Parse("2006-01-02", endDateStr); err == nil {
			// Set to end of day
			endOfDay := time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 59, 999999999, endDate.Location())
			filter.EndDate = &endOfDay
		} else {
			return filter, errors.NewValidationError("invalid end_date format: use YYYY-MM-DD")
		}
	}

	// Parse pagination
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			filter.Page = page
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 {
			filter.Limit = limit
		}
	}

	return filter, nil
}
