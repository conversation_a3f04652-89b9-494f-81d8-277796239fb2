package http

import (
	"net/http"
	"strconv"
	"time"

	"blacking-api/internal/domain/summary_report"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"

	"github.com/gin-gonic/gin"
)

type SummaryReportHandler struct {
	summaryReportService *service.SummaryReportService
}

func NewSummaryReportHandler(summaryReportService *service.SummaryReportService) *SummaryReportHandler {
	return &SummaryReportHandler{
		summaryReportService: summaryReportService,
	}
}

// GetSummaryReports godoc (DEPRECATED - use GetSummaryReportsWithFilter instead)
// @Summary Get summary reports (deprecated)
// @Description ใช้ GetSummaryReportsWithFilter แทน - ข้อมูลรายงานสรุปหน้าหลัก
// @Tags Reports
// @Accept json
// @Produce json
// @Param date_from query string true "Start date (YYYY-MM-DD)" example(2025-01-01)
// @Param date_to query string true "End date (YYYY-MM-DD)" example(2025-01-31)
// @Param category query string false "Report category" Enums(deposit,withdraw,transfer,commission)
// @Success 200 {object} summary_report.SummaryReportResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/reports/summary-bet/legacy [get]
func (h *SummaryReportHandler) GetSummaryReports(c *gin.Context) {
	ctx := c.Request.Context()

	// Validate request parameters
	var req summary_report.SummaryReportRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Message: "Invalid request parameters",
			Data:    err.Error(),
		})
		return
	}

	// Validate date format and range
	if err := h.validateSummaryReportRequest(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Message: "Invalid request data",
			Data:    err.Error(),
		})
		return
	}

	summaryReportsData, err := h.summaryReportService.GetSummaryReports(ctx, req.DateFrom, req.DateTo, req.Category)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Message: "Failed to get summary reports",
			Data:    err.Error(),
		})
		return
	}

	response := summary_report.SummaryReportResponse{
		Code:    http.StatusOK,
		Success: true,
		Message: "Summary reports retrieved successfully",
		Data:    *summaryReportsData,
	}

	c.JSON(http.StatusOK, response)
}

// GetSummaryReportDetail godoc
// @Summary Get summary report detail
// @Description ข้อมูลรายละเอียดรายงานสรุป
// @Tags Reports
// @Accept json
// @Produce json
// @Param id path int true "Report ID" example(1)
// @Param date_from query string true "Start date (YYYY-MM-DD)" example(2025-01-01)
// @Param date_to query string true "End date (YYYY-MM-DD)" example(2025-01-31)
// @Success 200 {object} summary_report.SummaryReportDetailResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/reports/summary-bet/{id} [get]
func (h *SummaryReportHandler) GetSummaryReportDetail(c *gin.Context) {
	ctx := c.Request.Context()

	// Get ID from URL parameter
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Message: "Invalid report ID",
			Data:    "ID must be a valid integer",
		})
		return
	}

	// Validate request parameters
	var req summary_report.SummaryReportDetailRequest
	req.ID = id
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Message: "Invalid request parameters",
			Data:    err.Error(),
		})
		return
	}

	// Validate date format and range
	if err := h.validateSummaryReportDetailRequest(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Message: "Invalid request data",
			Data:    err.Error(),
		})
		return
	}

	summaryReportDetailData, err := h.summaryReportService.GetSummaryReportDetail(ctx, req.ID, req.DateFrom, req.DateTo)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Message: "Failed to get summary report detail",
			Data:    err.Error(),
		})
		return
	}

	response := summary_report.SummaryReportDetailResponse{
		Code:    http.StatusOK,
		Success: true,
		Message: "Summary report detail retrieved successfully",
		Data:    *summaryReportDetailData,
	}

	c.JSON(http.StatusOK, response)
}

// validateSummaryReportRequest validates the summary report request parameters
func (h *SummaryReportHandler) validateSummaryReportRequest(req *summary_report.SummaryReportRequest) error {
	// Parse dates
	dateFrom, err := time.Parse("2006-01-02", req.DateFrom)
	if err != nil {
		return errors.NewValidationError("date_from must be in YYYY-MM-DD format")
	}

	dateTo, err := time.Parse("2006-01-02", req.DateTo)
	if err != nil {
		return errors.NewValidationError("date_to must be in YYYY-MM-DD format")
	}

	// Validate date range
	if dateFrom.After(dateTo) {
		return errors.NewValidationError("date_from cannot be after date_to")
	}

	// Validate category (optional)
	if req.Category != "" {
		validCategories := map[string]bool{
			"deposit":    true,
			"withdraw":   true,
			"transfer":   true,
			"commission": true,
		}
		if !validCategories[req.Category] {
			return errors.NewValidationError("category must be one of: deposit, withdraw, transfer, commission")
		}
	}

	return nil
}

// validateSummaryReportDetailRequest validates the summary report detail request parameters
func (h *SummaryReportHandler) validateSummaryReportDetailRequest(req *summary_report.SummaryReportDetailRequest) error {
	// Validate ID
	if req.ID <= 0 {
		return errors.NewValidationError("id must be a positive integer")
	}

	// Parse dates
	dateFrom, err := time.Parse("2006-01-02", req.DateFrom)
	if err != nil {
		return errors.NewValidationError("date_from must be in YYYY-MM-DD format")
	}

	dateTo, err := time.Parse("2006-01-02", req.DateTo)
	if err != nil {
		return errors.NewValidationError("date_to must be in YYYY-MM-DD format")
	}

	// Validate date range
	if dateFrom.After(dateTo) {
		return errors.NewValidationError("date_from cannot be after date_to")
	}

	return nil
}

// GetSummaryReportsWithFilter godoc
// @Summary Get summary reports with filters
// @Description ข้อมูลรายงานสรุปพร้อมฟิลเตอร์ (รองรับทุก parameters)
// @Tags Reports
// @Accept json
// @Produce json
// @Param date_register query string false "Registration date (YYYY-MM-DD)" example(2025-01-01)
// @Param date_from query string false "Start date (YYYY-MM-DD)" example(2025-01-01)
// @Param date_to query string false "End date (YYYY-MM-DD)" example(2025-01-31)
// @Param end_time query string false "End time (HH:MM:SS)" example(23:59:59)
// @Param first_deposit query string false "First deposit filter" example(yes)
// @Param game_type_id query int false "Game type ID" example(1)
// @Param is_include_partner query boolean false "Include partner data" example(true)
// @Param partner_id query int false "Partner ID" example(1)
// @Param phone query string false "Phone number" example(**********)
// @Param provider_code query string false "Provider code" example(PG)
// @Param start_time query string false "Start time (HH:MM:SS)" example(00:00:00)
// @Param user_code query string false "User code" example(USER001)
// @Param username query string false "Username" example(testuser)
// @Param sort query string false "Sort order" example(asc)
// @Param sortBy query string false "Sort by field" example(name)
// @Success 200 {object} summary_report.SummaryReportResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/reports/summary-bet [get]
func (h *SummaryReportHandler) GetSummaryReportsWithFilter(c *gin.Context) {
	ctx := c.Request.Context()

	// Validate request parameters
	var req summary_report.SummaryReportFilterRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Message: "Invalid request parameters",
			Data:    err.Error(),
		})
		return
	}

	// Validate filter request
	if err := h.validateSummaryReportFilterRequest(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Message: "Invalid request data",
			Data:    err.Error(),
		})
		return
	}

	// For now, use the basic service but in future this can be extended
	// to handle the advanced filters
	summaryReportsData, err := h.summaryReportService.GetSummaryReports(ctx, req.DateFrom, req.DateTo, "")
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Message: "Failed to get summary reports",
			Data:    err.Error(),
		})
		return
	}

	response := summary_report.SummaryReportResponse{
		Code:    http.StatusOK,
		Success: true,
		Message: "Summary reports with filters retrieved successfully",
		Data:    *summaryReportsData,
	}

	c.JSON(http.StatusOK, response)
}

// validateSummaryReportFilterRequest validates the summary report filter request parameters
func (h *SummaryReportHandler) validateSummaryReportFilterRequest(req *summary_report.SummaryReportFilterRequest) error {
	// Only validate required date_from and date_to if they are not empty
	if req.DateFrom != "" {
		dateFrom, err := time.Parse("2006-01-02", req.DateFrom)
		if err != nil {
			return errors.NewValidationError("date_from must be in YYYY-MM-DD format")
		}

		if req.DateTo != "" {
			dateTo, err := time.Parse("2006-01-02", req.DateTo)
			if err != nil {
				return errors.NewValidationError("date_to must be in YYYY-MM-DD format")
			}

			// Validate date range only if both dates are provided
			if dateFrom.After(dateTo) {
				return errors.NewValidationError("date_from cannot be after date_to")
			}
		}
	}

	// All other validations are now bypassed - accept empty strings and any values
	return nil
}
