package http

import (
	"blacking-api/internal/domain/bank_transaction_slip"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// BankTransactionSlipHandler handles HTTP requests for bank transaction slips
type BankTransactionSlipHandler struct {
	service service.BankTransactionSlipService
}

// NewBankTransactionSlipHandler creates a new bank transaction slip handler
func NewBankTransactionSlipHandler(service service.BankTransactionSlipService) *BankTransactionSlipHandler {
	return &BankTransactionSlipHandler{
		service: service,
	}
}

// Create handles POST /bank-transaction-slip
// @Summary Create a new bank transaction slip
// @Description Create a new bank transaction slip for a member
// @Tags Bank Transaction Slip
// @Accept json
// @Produce json
// @Param request body bank_transaction_slip.CreateRequest true "Create request"
// @Success 201 {object} response.Success{data=bank_transaction_slip.Response}
// @Failure 400 {object} response.Error
// @Failure 404 {object} response.Error
// @Failure 500 {object} response.Error
// @Router /bank-transaction-slip [post]
func (h *BankTransactionSlipHandler) Create(c *gin.Context) {
	var req bank_transaction_slip.CreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError(err.Error()))
		return
	}

	result, err := h.service.Create(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    result,
	})
}

// GetByID handles GET /bank-transaction-slip/:id
// @Summary Get bank transaction slip by ID
// @Description Get a bank transaction slip by its ID
// @Tags Bank Transaction Slip
// @Accept json
// @Produce json
// @Param id path int true "Slip ID"
// @Success 200 {object} response.Success{data=bank_transaction_slip.Response}
// @Failure 404 {object} response.Error
// @Failure 500 {object} response.Error
// @Router /bank-transaction-slip/{id} [get]
func (h *BankTransactionSlipHandler) GetByID(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid ID"))
		return
	}

	result, err := h.service.GetByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// GetByTransactionID handles GET /bank-transaction-slip/transaction/:transactionId
// @Summary Get bank transaction slip by transaction ID
// @Description Get a bank transaction slip by its transaction ID
// @Tags Bank Transaction Slip
// @Accept json
// @Produce json
// @Param transactionId path int true "Transaction ID"
// @Success 200 {object} response.Success{data=bank_transaction_slip.Response}
// @Failure 404 {object} response.Error
// @Failure 500 {object} response.Error
// @Router /bank-transaction-slip/transaction/{transactionId} [get]
func (h *BankTransactionSlipHandler) GetByTransactionID(c *gin.Context) {
	idParam := c.Param("transactionId")
	transactionID, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid transaction ID"))
		return
	}

	result, err := h.service.GetByTransactionID(c.Request.Context(), transactionID)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// GetList handles GET /bank-transaction-slip
// @Summary Get paginated list of bank transaction slips
// @Description Get a paginated list of bank transaction slips with filters
// @Tags Bank Transaction Slip
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param memberId query int false "Filter by member ID"
// @Param status query int false "Filter by status (1=Pending, 2=Approved, 3=Rejected, 4=Cancelled)"
// @Param transactionId query int false "Filter by transaction ID"
// @Param fromDate query string false "Filter from date (YYYY-MM-DD)"
// @Param toDate query string false "Filter to date (YYYY-MM-DD)"
// @Param search query string false "Search in member username, fullname, account names"
// @Success 200 {object} response.SuccessWithPagination{data=[]bank_transaction_slip.Response}
// @Failure 400 {object} response.Error
// @Failure 500 {object} response.Error
// @Router /bank-transaction-slip [get]
func (h *BankTransactionSlipHandler) GetList(c *gin.Context) {
	var filter bank_transaction_slip.FilterRequest
	if err := c.ShouldBindQuery(&filter); err != nil {
		c.Error(errors.NewValidationError(err.Error()))
		return
	}

	result, err := h.service.GetList(c.Request.Context(), &filter)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetByMemberID handles GET /bank-transaction-slip/member/:memberId
// @Summary Get bank transaction slips by member ID
// @Description Get bank transaction slips for a specific member
// @Tags Bank Transaction Slip
// @Accept json
// @Produce json
// @Param memberId path int true "Member ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param status query int false "Filter by status"
// @Param fromDate query string false "Filter from date (YYYY-MM-DD)"
// @Param toDate query string false "Filter to date (YYYY-MM-DD)"
// @Success 200 {object} response.SuccessWithPagination{data=[]bank_transaction_slip.Response}
// @Failure 400 {object} response.Error
// @Failure 500 {object} response.Error
// @Router /bank-transaction-slip/member/{memberId} [get]
func (h *BankTransactionSlipHandler) GetByMemberID(c *gin.Context) {
	idParam := c.Param("memberId")
	memberID, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid member ID"))
		return
	}

	var filter bank_transaction_slip.FilterRequest
	if err := c.ShouldBindQuery(&filter); err != nil {
		c.Error(errors.NewValidationError(err.Error()))
		return
	}

	result, err := h.service.GetByMemberID(c.Request.Context(), memberID, &filter)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, result)
}

// Update handles PUT /bank-transaction-slip/:id
// @Summary Update a bank transaction slip
// @Description Update a bank transaction slip (only if status is pending)
// @Tags Bank Transaction Slip
// @Accept json
// @Produce json
// @Param id path int true "Slip ID"
// @Param request body bank_transaction_slip.UpdateRequest true "Update request"
// @Success 200 {object} response.Success{data=bank_transaction_slip.Response}
// @Failure 400 {object} response.Error
// @Failure 404 {object} response.Error
// @Failure 500 {object} response.Error
// @Router /bank-transaction-slip/{id} [put]
func (h *BankTransactionSlipHandler) Update(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid ID"))
		return
	}

	var req bank_transaction_slip.UpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError(err.Error()))
		return
	}

	result, err := h.service.Update(c.Request.Context(), id, &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// UpdateStatus handles PATCH /bank-transaction-slip/:id/status
// @Summary Update bank transaction slip status
// @Description Update the status of a bank transaction slip
// @Tags Bank Transaction Slip
// @Accept json
// @Produce json
// @Param id path int true "Slip ID"
// @Param request body bank_transaction_slip.UpdateStatusRequest true "Status update request"
// @Success 200 {object} response.Success
// @Failure 400 {object} response.Error
// @Failure 404 {object} response.Error
// @Failure 500 {object} response.Error
// @Router /bank-transaction-slip/{id}/status [patch]
func (h *BankTransactionSlipHandler) UpdateStatus(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid ID"))
		return
	}

	var req bank_transaction_slip.UpdateStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError(err.Error()))
		return
	}

	err = h.service.UpdateStatus(c.Request.Context(), id, &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Status updated successfully",
	})
}

// Delete handles DELETE /bank-transaction-slip/:id
// @Summary Delete a bank transaction slip
// @Description Delete a bank transaction slip (only if pending or cancelled)
// @Tags Bank Transaction Slip
// @Accept json
// @Produce json
// @Param id path int true "Slip ID"
// @Success 200 {object} response.Success
// @Failure 400 {object} response.Error
// @Failure 404 {object} response.Error
// @Failure 500 {object} response.Error
// @Router /bank-transaction-slip/{id} [delete]
func (h *BankTransactionSlipHandler) Delete(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid ID"))
		return
	}

	err = h.service.Delete(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Bank transaction slip deleted successfully",
	})
}

// GetStatusCounts handles GET /bank-transaction-slip/status-counts
// @Summary Get bank transaction slip status counts
// @Description Get count of bank transaction slips by status
// @Tags Bank Transaction Slip
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} response.Error
// @Router /bank-transaction-slip/status-counts [get]
func (h *BankTransactionSlipHandler) GetStatusCounts(c *gin.Context) {
	counts, err := h.service.GetStatusCounts(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    counts,
	})
}
