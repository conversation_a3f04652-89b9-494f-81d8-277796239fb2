package language

// Language represents a supported language
type Language struct {
	Code       string `json:"code"`
	Name       string `json:"name"`
	NativeName string `json:"native_name"`
	Flag       string `json:"flag"`
	IsActive   bool   `json:"is_active"`
}

// LanguageResponse represents the response for supported languages
type LanguageResponse struct {
	Languages []Language `json:"languages"`
	Total     int        `json:"total"`
}

// GetSupportedLanguages returns the list of supported languages
func GetSupportedLanguages() []Language {
	return []Language{
		{
			Code:       "th",
			Name:       "Thai",
			NativeName: "ไทย",
			Flag:       "🇹🇭",
			IsActive:   true,
		},
		{
			Code:       "en",
			Name:       "English",
			NativeName: "English",
			Flag:       "🇺🇸",
			IsActive:   true,
		},
		{
			Code:       "zh",
			Name:       "Chinese",
			NativeName: "中文",
			Flag:       "🇨🇳",
			IsActive:   true,
		},
		{
			Code:       "vi",
			Name:       "Vietnamese",
			NativeName: "Tiếng Việt",
			Flag:       "🇻🇳",
			IsActive:   true,
		},
		{
			Code:       "ja",
			Name:       "Japanese",
			NativeName: "日本語",
			Flag:       "🇯🇵",
			IsActive:   true,
		},
		{
			Code:       "lo",
			Name:       "Lao",
			NativeName: "ລາວ",
			Flag:       "🇱🇦",
			IsActive:   true,
		},
	}
}

// GetLanguageByCode returns a language by its code
func GetLanguageByCode(code string) *Language {
	languages := GetSupportedLanguages()
	for _, lang := range languages {
		if lang.Code == code {
			return &lang
		}
	}
	return nil
}

// GetActiveLanguages returns only active languages
func GetActiveLanguages() []Language {
	languages := GetSupportedLanguages()
	var activeLanguages []Language

	for _, lang := range languages {
		if lang.IsActive {
			activeLanguages = append(activeLanguages, lang)
		}
	}

	return activeLanguages
}

// ValidateLanguageCode validates if a language code is supported
func ValidateLanguageCode(code string) bool {
	return GetLanguageByCode(code) != nil
}
