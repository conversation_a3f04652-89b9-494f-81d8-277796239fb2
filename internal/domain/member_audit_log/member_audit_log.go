package member_audit_log

import (
	"blacking-api/pkg/errors"
	"time"
)

// ActionType represents the type of action performed on member
type ActionType string

const (
	ActionCreate          ActionType = "create"
	ActionUpdate          ActionType = "update"
	ActionDelete          ActionType = "delete"
	ActionActivate        ActionType = "activate"
	ActionDeactivate      ActionType = "deactivate"
	ActionSuspend         ActionType = "suspend"
	ActionUnsuspend       ActionType = "unsuspend"
	ActionPassword        ActionType = "password_change"
	ActionGameCredentials ActionType = "game_credentials_change"
	ActionBalanceUpdate   ActionType = "balance_update"
	ActionChangePartner   ActionType = "change_partner"
	ActionUpdateBankInfo  ActionType = "update_bank_info"
)

// MemberAuditLog represents an audit log entry for member operations
type MemberAuditLog struct {
	ID            int        `json:"id" db:"id"`
	MemberID      int        `json:"member_id" db:"member_id"`
	Username      string     `json:"username" db:"username"`
	Action        ActionType `json:"action" db:"action"`
	OldValues     *string    `json:"old_values,omitempty" db:"old_values"`
	NewValues     *string    `json:"new_values,omitempty" db:"new_values"`
	ChangedBy     int        `json:"changed_by" db:"changed_by"`
	ChangedByName string     `json:"changed_by_name" db:"changed_by_name"`
	ChangedAt     time.Time  `json:"changed_at" db:"changed_at"`
}

// CreateAuditLogRequest represents the request to create an audit log
type CreateAuditLogRequest struct {
	MemberID      int        `json:"member_id" validate:"required"`
	Username      string     `json:"username" validate:"required"`
	Action        ActionType `json:"action" validate:"required"`
	OldValues     *string    `json:"old_values,omitempty"`
	NewValues     *string    `json:"new_values,omitempty"`
	ChangedBy     int        `json:"changed_by" validate:"required"`
	ChangedByName string     `json:"changed_by_name" validate:"required"`
}

// AuditFieldChange represents a single field change in the audit log
type AuditFieldChange struct {
	Title     string `json:"title"`      // Display name of the field
	Before    string `json:"before"`     // Value before change
	After     string `json:"after"`      // Value after change
	HasChange bool   `json:"has_change"` // Flag indicating if values are different
}

// MemberAuditLogResponse represents the response structure for audit log
type MemberAuditLogResponse struct {
	ID        int                `json:"id"`
	MemberID  int                `json:"member_id"`
	Username  string             `json:"username"`
	Action    ActionType         `json:"action"`
	OldValues *string            `json:"old_values,omitempty"` // Keep for backward compatibility
	NewValues *string            `json:"new_values,omitempty"` // Keep for backward compatibility
	Changes   []AuditFieldChange `json:"changes"`              // New detailed changes format
	// Separate arrays for easier table rendering on frontend
	Titles        []string  `json:"titles"`                  // Field titles array
	Befores       []string  `json:"befores"`                 // Before values array
	Afters        []string  `json:"afters"`                  // After values array
	HasChanges    []bool    `json:"has_changes"`             // Change flags array
	ActionRemark  *string   `json:"action_remark,omitempty"` // Action-specific remark
	ChangedBy     int       `json:"changed_by"`
	ChangedByName string    `json:"changed_by_name"`
	ChangedAt     time.Time `json:"changed_at"`
}

// NewMemberAuditLog creates a new member audit log entry
func NewMemberAuditLog(req CreateAuditLogRequest) (*MemberAuditLog, error) {
	if err := validateCreateRequest(req); err != nil {
		return nil, err
	}

	now := time.Now()
	auditLog := &MemberAuditLog{
		MemberID:      req.MemberID,
		Username:      req.Username,
		Action:        req.Action,
		OldValues:     req.OldValues,
		NewValues:     req.NewValues,
		ChangedBy:     req.ChangedBy,
		ChangedByName: req.ChangedByName,
		ChangedAt:     now,
	}

	return auditLog, nil
}

// ToResponse converts MemberAuditLog to MemberAuditLogResponse
func (m *MemberAuditLog) ToResponse() MemberAuditLogResponse {
	return MemberAuditLogResponse{
		ID:            m.ID,
		MemberID:      m.MemberID,
		Username:      m.Username,
		Action:        m.Action,
		OldValues:     m.OldValues,
		NewValues:     m.NewValues,
		Changes:       []AuditFieldChange{}, // Will be populated by service layer
		Titles:        []string{},           // Will be populated by service layer
		Befores:       []string{},           // Will be populated by service layer
		Afters:        []string{},           // Will be populated by service layer
		HasChanges:    []bool{},             // Will be populated by service layer
		ActionRemark:  nil,                  // Will be populated by service layer from NewValues
		ChangedBy:     m.ChangedBy,
		ChangedByName: m.ChangedByName,
		ChangedAt:     m.ChangedAt,
	}
}

// CreateAuditFieldChange creates an audit field change entry
func CreateAuditFieldChange(title, before, after string) AuditFieldChange {
	return AuditFieldChange{
		Title:     title,
		Before:    before,
		After:     after,
		HasChange: before != after,
	}
}

// MemberAuditLogFilter contains filter parameters for audit log queries
type MemberAuditLogFilter struct {
	Username string  `json:"username,omitempty"`  // Filter by member username
	Action   string  `json:"action,omitempty"`    // Filter by action type
	Phone    string  `json:"phone,omitempty"`     // Filter by member phone
	FullName string  `json:"fullname,omitempty"`  // Filter by member fullname
	DateFrom *string `json:"date_from,omitempty"` // Filter from date (YYYY-MM-DD)
	DateTo   *string `json:"date_to,omitempty"`   // Filter to date (YYYY-MM-DD)
}

// MemberFieldTitles contains Thai display names for member fields
var MemberFieldTitles = map[string]string{
	"fullname":          "ชื่อ-นามสกุล",
	"first_name":        "ชื่อ",
	"last_name":         "นามสกุล",
	"gender":            "เพศ",
	"birth_date":        "วันเกิด",
	"phone":             "เบอร์โทร",
	"tw_username":       "เลขบัญชี ทรูมันนี่",
	"line_id":           "LINE ID",
	"member_group":      "กลุ่มผู้ใช้งาน",
	"referral_group":    "กลุ่มเพื่อนชวนเพื่อน",
	"commission_group":  "กลุ่มคอมิชชั่น",
	"remark":            "หมายเหตุ",
	"bank_code":         "รหัสธนาคาร",
	"bank":              "ธนาคาร",
	"bank_account_name": "ชื่อบัญชีธนาคาร",
	"bank_number":       "เลขบัญชีธนาคาร",
	"address_detail":    "รายละเอียดที่อยู่",
	"address_province":  "จังหวัด",
	"address_amphoe":    "อำเภอ",
	"address_district":  "ตำบล",
	"password":          "รหัสผ่าน",
	"partner_name":      "ชื่อ Partner",
	"refer_user_id":     "รหัส Partner",
	"title":             "หัวข้อ",
}

// IsValidAction checks if the action type is valid
func IsValidAction(action ActionType) bool {
	switch action {
	case ActionCreate, ActionUpdate, ActionDelete, ActionActivate, ActionDeactivate, ActionSuspend, ActionUnsuspend, ActionPassword, ActionGameCredentials, ActionBalanceUpdate, ActionChangePartner, ActionUpdateBankInfo:
		return true
	default:
		return false
	}
}

// GetActionDescription returns a human-readable description for the action
func (a ActionType) GetActionDescription() string {
	switch a {
	case ActionCreate:
		return "Member created"
	case ActionUpdate:
		return "Member updated"
	case ActionDelete:
		return "Member deleted"
	case ActionActivate:
		return "Member activated"
	case ActionDeactivate:
		return "Member deactivated"
	case ActionSuspend:
		return "Member suspended"
	case ActionUnsuspend:
		return "Member unsuspended"
	case ActionPassword:
		return "Member password changed"
	case ActionGameCredentials:
		return "Member game credentials changed"
	case ActionBalanceUpdate:
		return "Member balance updated"
	case ActionChangePartner:
		return "Member partner changed"
	case ActionUpdateBankInfo:
		return "Member bank information updated"
	default:
		return "Unknown action"
	}
}

// validateCreateRequest validates the create audit log request
func validateCreateRequest(req CreateAuditLogRequest) error {
	if req.MemberID == 0 {
		return errors.NewValidationError("member_id is required")
	}
	if req.Username == "" {
		return errors.NewValidationError("username is required")
	}
	if req.Action == "" {
		return errors.NewValidationError("action is required")
	}
	if !IsValidAction(req.Action) {
		return errors.NewValidationError("invalid action type")
	}
	// Allow ChangedBy = 0 for system actions
	if req.ChangedBy == 0 && req.ChangedByName != "System" {
		return errors.NewValidationError("changed_by is required for non-system actions")
	}
	if req.ChangedBy != 0 && req.ChangedByName == "" {
		return errors.NewValidationError("changed_by_name is required")
	}
	if req.ChangedByName == "" {
		return errors.NewValidationError("changed_by_name is required")
	}
	return nil
}
