package bank_transaction_slip

import (
	"time"
)

// Status constants for bank transaction slip
const (
	StatusPending   = 1
	StatusApproved  = 2
	StatusRejected  = 3
	StatusCancelled = 4
)

// BankTransactionSlip represents a bank transaction slip entity
type BankTransactionSlip struct {
	ID                int64      `json:"id" db:"id"`
	MemberID          int64      `json:"memberId" db:"member_id"`
	Status            int        `json:"status" db:"status"`
	TransactionID     int64      `json:"transactionId" db:"transaction_id"`
	SlipUrl           *string    `json:"slipUrl" db:"slip_url"`
	RawQrCode         *string    `json:"rawQrCode" db:"raw_qr_code"`
	FromAccountNumber string     `json:"fromAccountNumber" db:"from_account_number"`
	FromAccountName   string     `json:"fromAccountName" db:"from_account_name"`
	FromBankName      string     `json:"fromBankName" db:"from_bank_name"`
	ToAccountNumber   string     `json:"toAccountNumber" db:"to_account_number"`
	ToAccountName     string     `json:"toAccountName" db:"to_account_name"`
	Amount            float64    `json:"amount" db:"amount"`
	TransactionDate   time.Time  `json:"transactionDate" db:"transaction_date"`
	Remark            string     `json:"remark" db:"remark"`
	CreatedAt         time.Time  `json:"createdAt" db:"created_at"`
	UpdatedAt         *time.Time `json:"updatedAt" db:"updated_at"`

	// Joined fields
	MemberUsername *string `json:"memberUsername,omitempty" db:"member_username"`
	MemberFullname *string `json:"memberFullname,omitempty" db:"member_fullname"`
}

// CreateRequest represents the request to create a bank transaction slip
type CreateRequest struct {
	MemberID          int64     `json:"memberId" validate:"required,min=1"`
	TransactionID     int64     `json:"transactionId" validate:"required,min=1"`
	RawQrCode         *string   `json:"rawQrCode"`
	FromAccountNumber string    `json:"fromAccountNumber" validate:"required"`
	FromAccountName   string    `json:"fromAccountName" validate:"required"`
	FromBankName      string    `json:"fromBankName" validate:"required"`
	ToAccountNumber   string    `json:"toAccountNumber" validate:"required"`
	ToAccountName     string    `json:"toAccountName" validate:"required"`
	Amount            float64   `json:"amount" validate:"required,gt=0"`
	TransactionDate   time.Time `json:"transactionDate" validate:"required"`
	Remark            string    `json:"remark"`
}

// UpdateRequest represents the request to update a bank transaction slip
type UpdateRequest struct {
	RawQrCode         *string   `json:"rawQrCode"`
	FromAccountNumber string    `json:"fromAccountNumber" validate:"required"`
	FromAccountName   string    `json:"fromAccountName" validate:"required"`
	FromBankName      string    `json:"fromBankName" validate:"required"`
	ToAccountNumber   string    `json:"toAccountNumber" validate:"required"`
	ToAccountName     string    `json:"toAccountName" validate:"required"`
	Amount            float64   `json:"amount" validate:"required,gt=0"`
	TransactionDate   time.Time `json:"transactionDate" validate:"required"`
	Remark            string    `json:"remark"`
}

// UpdateStatusRequest represents the request to update status
type UpdateStatusRequest struct {
	Status int    `json:"status" validate:"required,min=1,max=4"`
	Remark string `json:"remark"`
}

// FilterRequest represents filter parameters for listing
type FilterRequest struct {
	Page          int     `form:"page" validate:"min=1"`
	Limit         int     `form:"limit" validate:"min=1,max=100"`
	MemberID      *int64  `form:"memberId"`
	Status        *int    `form:"status"`
	TransactionID *int64  `form:"transactionId"`
	FromDate      *string `form:"fromDate"`
	ToDate        *string `form:"toDate"`
	Search        *string `form:"search"`
}

// Response represents the API response for a bank transaction slip
type Response struct {
	ID                int64      `json:"id"`
	MemberID          int64      `json:"memberId"`
	MemberUsername    string     `json:"memberUsername"`
	MemberFullname    string     `json:"memberFullname"`
	Status            int        `json:"status"`
	StatusName        string     `json:"statusName"`
	TransactionID     int64      `json:"transactionId"`
	RawQrCode         *string    `json:"rawQrCode"`
	FromAccountNumber string     `json:"fromAccountNumber"`
	FromAccountName   string     `json:"fromAccountName"`
	FromBankName      string     `json:"fromBankName"`
	ToAccountNumber   string     `json:"toAccountNumber"`
	ToAccountName     string     `json:"toAccountName"`
	Amount            float64    `json:"amount"`
	TransactionDate   time.Time  `json:"transactionDate"`
	Remark            string     `json:"remark"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         *time.Time `json:"updatedAt"`
}

// ToResponse converts BankTransactionSlip to Response
func (b *BankTransactionSlip) ToResponse() Response {
	resp := Response{
		ID:                b.ID,
		MemberID:          b.MemberID,
		Status:            b.Status,
		StatusName:        GetStatusName(b.Status),
		TransactionID:     b.TransactionID,
		RawQrCode:         b.RawQrCode,
		FromAccountNumber: b.FromAccountNumber,
		FromAccountName:   b.FromAccountName,
		FromBankName:      b.FromBankName,
		ToAccountNumber:   b.ToAccountNumber,
		ToAccountName:     b.ToAccountName,
		Amount:            b.Amount,
		TransactionDate:   b.TransactionDate,
		Remark:            b.Remark,
		CreatedAt:         b.CreatedAt,
		UpdatedAt:         b.UpdatedAt,
	}

	if b.MemberUsername != nil {
		resp.MemberUsername = *b.MemberUsername
	}
	if b.MemberFullname != nil {
		resp.MemberFullname = *b.MemberFullname
	}

	return resp
}

// GetStatusName returns the name of the status
func GetStatusName(status int) string {
	switch status {
	case StatusPending:
		return "Pending"
	case StatusApproved:
		return "Approved"
	case StatusRejected:
		return "Rejected"
	case StatusCancelled:
		return "Cancelled"
	default:
		return "Unknown"
	}
}
