package report

import (
	"fmt"
	"time"
)

// ReferralReportSummary represents summarized referral data grouped by member_id
type ReferralReportSummary struct {
	MemberID               int       `json:"member_id" db:"member_id"`
	MemberUsername         *string   `json:"member_username" db:"member_username"`
	MemberPhone            *string   `json:"member_phone" db:"member_phone"`
	MemberFirstName        *string   `json:"member_first_name" db:"member_first_name"`
	MemberLastName         *string   `json:"member_last_name" db:"member_last_name"`
	ReferCode              string    `json:"refer_code" db:"refer_code"`
	TotalCommissionAmount  float64   `json:"total_commission_amount" db:"total_commission_amount"`
	TotalWithdrawAmount    float64   `json:"total_withdraw_amount" db:"total_withdraw_amount"`
	NetAmount              float64   `json:"net_amount" db:"net_amount"` // commission - withdraw
	TransactionCount       int       `json:"transaction_count" db:"transaction_count"`
	DownlineCount          int       `json:"downline_count" db:"downline_count"` // distinct count of downline_member_id
	FirstTransactionDate   time.Time `json:"first_transaction_date" db:"first_transaction_date"`
	LastTransactionDate    time.Time `json:"last_transaction_date" db:"last_transaction_date"`
	CurrentCommissionBalance float64 `json:"current_commission_balance" db:"current_commission_balance"` // from members table
}

// ReferralReportDetail represents detailed referral transaction data
type ReferralReportDetail struct {
	ID                    int        `json:"id" db:"id"`
	MemberID              int        `json:"member_id" db:"member_id"`
	MemberUsername        *string    `json:"member_username" db:"member_username"`
	MemberPhone           *string    `json:"member_phone" db:"member_phone"`
	Type                  string     `json:"type" db:"type"`
	Amount                float64    `json:"amount" db:"amount"`
	GameCategory          *string    `json:"game_category" db:"game_category"`
	DownlineMemberID      *int       `json:"downline_member_id" db:"downline_member_id"`
	DownlineMemberUsername *string   `json:"downline_member_username" db:"downline_member_username"`
	DownlineMemberPhone   *string    `json:"downline_member_phone" db:"downline_member_phone"`
	BalanceBefore         float64    `json:"balance_before" db:"balance_before"`
	BalanceAfter          float64    `json:"balance_after" db:"balance_after"`
	ReferralStartDate     *time.Time `json:"referral_start_date" db:"referral_start_date"`
	ReferralEndDate       *time.Time `json:"referral_end_date" db:"referral_end_date"`
	CreatedByAdmin        *int       `json:"created_by_admin" db:"created_by_admin"`
	CreatedByMember       *int       `json:"created_by_member" db:"created_by_member"`
	Status                string     `json:"status" db:"status"`
	Remark                *string    `json:"remark" db:"remark"`
	CreatedAt             time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt             time.Time  `json:"updated_at" db:"updated_at"`
}

// ReferralReportDetailGrouped represents grouped detail by downline_member_id with sum
type ReferralReportDetailGrouped struct {
	MemberID                 int       `json:"member_id" db:"member_id"`
	MemberUsername           *string   `json:"member_username" db:"member_username"`
	MemberPhone              *string   `json:"member_phone" db:"member_phone"`
	DownlineMemberID         *int      `json:"downline_member_id" db:"downline_member_id"`
	DownlineMemberUsername   *string   `json:"downline_member_username" db:"downline_member_username"`
	DownlineMemberPhone      *string   `json:"downline_member_phone" db:"downline_member_phone"`
	TotalCommissionAmount    float64   `json:"total_commission_amount" db:"total_commission_amount"`
	TotalWithdrawAmount      float64   `json:"total_withdraw_amount" db:"total_withdraw_amount"`
	TotalAdjustmentAmount    float64   `json:"total_adjustment_amount" db:"total_adjustment_amount"`
	TotalBonusAmount         float64   `json:"total_bonus_amount" db:"total_bonus_amount"`
	NetAmount                float64   `json:"net_amount" db:"net_amount"`
	TransactionCount         int       `json:"transaction_count" db:"transaction_count"`
	CommissionCount          int       `json:"commission_count" db:"commission_count"`
	WithdrawCount            int       `json:"withdraw_count" db:"withdraw_count"`
	AdjustmentCount          int       `json:"adjustment_count" db:"adjustment_count"`
	BonusCount               int       `json:"bonus_count" db:"bonus_count"`
	FirstTransactionDate     time.Time `json:"first_transaction_date" db:"first_transaction_date"`
	LastTransactionDate      time.Time `json:"last_transaction_date" db:"last_transaction_date"`
}

// ReferralReportFilter represents filter criteria for referral reports
type ReferralReportFilter struct {
	StartDate    *time.Time `json:"start_date" form:"start_date"`
	EndDate      *time.Time `json:"end_date" form:"end_date"`
	Search       string     `json:"search" form:"search"`        // search by member username or phone
	MemberID     *int       `json:"member_id" form:"member_id"`  // exact match
	Phone        string     `json:"phone" form:"phone"`          // exact match
	Username     string     `json:"username" form:"username"`    // exact match
	Status       []string   `json:"status" form:"status"`        // status filter (success, pending, cancel)
	Type         []string   `json:"type" form:"type"`            // type filter (commission, withdraw, adjustment, bonus)
	GameCategory *string    `json:"game_category" form:"game_category"` // game category filter (Slot, Live-Casino, Sport)
	OrderBy      string     `json:"order_by" form:"order_by"`    // sort field: "member_id", "total_amount", "created_at", etc.
	OrderDir     string     `json:"order_dir" form:"order_dir"`  // sort direction: "desc" (default), "asc"
	Page         int        `json:"page" form:"page"`            // page number (1-based)
	Limit        int        `json:"limit" form:"limit"`          // records per page
}

// CommissionByGameCategoryFilter represents filter for commission by game category report
type CommissionByGameCategoryFilter struct {
	StartDate    *time.Time `json:"start_date" form:"start_date"`
	EndDate      *time.Time `json:"end_date" form:"end_date"`
	GameCategory string     `json:"game_category" form:"game_category"` // required: Slot, Live-Casino, Sport
	Page         int        `json:"page" form:"page"`
	Limit        int        `json:"limit" form:"limit"`
}

// CommissionByGameCategoryResponse represents API response for commission by game category
type CommissionByGameCategoryResponse struct {
	Data       []ReferralReportDetail `json:"data"`
	Pagination PaginationResponse     `json:"pagination"`
	Summary    CategoryCommissionSummary `json:"summary"`
}

// CategoryCommissionSummary represents summary for commission by game category
type CategoryCommissionSummary struct {
	GameCategory          string  `json:"game_category"`
	TotalCommissionAmount float64 `json:"total_commission_amount"`
	TransactionCount      int     `json:"transaction_count"`
	UniqueMembers         int     `json:"unique_members"`
}

// ReferralReportSummaryResponse represents API response for summary report
type ReferralReportSummaryResponse struct {
	Data       []ReferralReportSummary `json:"data"`
	Pagination PaginationResponse      `json:"pagination"`
	Summary    ReportSummary           `json:"summary"`
}

// ReferralReportDetailResponse represents API response for detail report
type ReferralReportDetailResponse struct {
	Data       []ReferralReportDetail `json:"data"`
	Pagination PaginationResponse     `json:"pagination"`
	Summary    DetailReportSummary    `json:"summary"`
}

// ReferralReportDetailGroupedResponse represents API response for grouped detail report
type ReferralReportDetailGroupedResponse struct {
	Data       []ReferralReportDetailGrouped `json:"data"`
	Pagination PaginationResponse            `json:"pagination"`
	Summary    DetailReportSummary           `json:"summary"`
}

// ReportSummary represents summary statistics for the report
type ReportSummary struct {
	TotalMembers              int     `json:"total_members"`
	TotalCommissionAmount     float64 `json:"total_commission_amount"`
	TotalWithdrawAmount       float64 `json:"total_withdraw_amount"`
	NetAmount                 float64 `json:"net_amount"`
	TotalTransactionCount     int     `json:"total_transaction_count"`
	TotalDownlineCount        int     `json:"total_downline_count"`
	AverageCommissionPerMember float64 `json:"average_commission_per_member"`
}

// DetailReportSummary represents summary statistics for detail report
type DetailReportSummary struct {
	TotalRecords          int     `json:"total_records"`
	TotalCommissionAmount float64 `json:"total_commission_amount"`
	TotalWithdrawAmount   float64 `json:"total_withdraw_amount"`
	NetAmount             float64 `json:"net_amount"`
	CommissionCount       int     `json:"commission_count"`
	WithdrawCount         int     `json:"withdraw_count"`
}

// PaginationResponse represents pagination information
type PaginationResponse struct {
	Page       int  `json:"page"`
	Limit      int  `json:"limit"`
	TotalPages int  `json:"total_pages"`
	TotalCount int  `json:"total_count"`
	HasNext    bool `json:"has_next"`
	HasPrev    bool `json:"has_prev"`
}

// CSVExportRequest represents request for CSV export
type CSVExportRequest struct {
	ReportType string                `json:"report_type" form:"report_type"` // "summary" or "detail"
	Filter     ReferralReportFilter  `json:"filter"`
	MemberID   *int                  `json:"member_id" form:"member_id"` // for detail report
}

// DefaultFilter returns default filter with today's date range
func DefaultFilter() ReferralReportFilter {
	now := time.Now()
	startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999999999, now.Location())

	return ReferralReportFilter{
		StartDate: &startOfDay,
		EndDate:   &endOfDay,
		OrderBy:   "member_id",
		OrderDir:  "desc",
		Page:      1,
		Limit:     20,
		Status:    []string{"success"}, // default to successful transactions only
		Type:      []string{"commission"}, // default to commission type only
	}
}

// Validate validates the filter parameters
func (f *ReferralReportFilter) Validate() error {
	// Set defaults if not provided
	if f.Page <= 0 {
		f.Page = 1
	}
	if f.Limit <= 0 {
		f.Limit = 20
	}
	if f.Limit > 1000 {
		f.Limit = 1000 // max limit
	}
	if f.OrderBy == "" {
		f.OrderBy = "member_id"
	}
	if f.OrderDir == "" {
		f.OrderDir = "desc"
	}

	// Validate order by fields
	validOrderBy := []string{"member_id", "total_commission_amount", "total_withdraw_amount", "net_amount", "transaction_count", "created_at", "updated_at", "first_transaction_date", "last_transaction_date"}
	isValidOrderBy := false
	for _, field := range validOrderBy {
		if f.OrderBy == field {
			isValidOrderBy = true
			break
		}
	}
	if !isValidOrderBy {
		f.OrderBy = "member_id"
	}

	// Validate order direction
	if f.OrderDir != "asc" && f.OrderDir != "desc" {
		f.OrderDir = "desc"
	}

	// Set default date range if not provided (today)
	if f.StartDate == nil || f.EndDate == nil {
		defaultFilter := DefaultFilter()
		if f.StartDate == nil {
			f.StartDate = defaultFilter.StartDate
		}
		if f.EndDate == nil {
			f.EndDate = defaultFilter.EndDate
		}
	}

	return nil
}

// GetOffset calculates the offset for database query
func (f *ReferralReportFilter) GetOffset() int {
	return (f.Page - 1) * f.Limit
}

// Validate validates the commission by game category filter
func (f *CommissionByGameCategoryFilter) Validate() error {
	// Set defaults if not provided
	if f.Page <= 0 {
		f.Page = 1
	}
	if f.Limit <= 0 {
		f.Limit = 20
	}
	if f.Limit > 1000 {
		f.Limit = 1000 // max limit
	}

	// Validate game category
	if f.GameCategory == "" {
		return fmt.Errorf("game_category is required")
	}
	validCategories := []string{"Slot", "Live-Casino", "Sport"}
	isValid := false
	for _, category := range validCategories {
		if f.GameCategory == category {
			isValid = true
			break
		}
	}
	if !isValid {
		return fmt.Errorf("invalid game_category: must be one of %v", validCategories)
	}

	// Set default date range if not provided (today)
	if f.StartDate == nil || f.EndDate == nil {
		now := time.Now()
		startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999999999, now.Location())
		
		if f.StartDate == nil {
			f.StartDate = &startOfDay
		}
		if f.EndDate == nil {
			f.EndDate = &endOfDay
		}
	}

	return nil
}

// GetOffset calculates the offset for database query
func (f *CommissionByGameCategoryFilter) GetOffset() int {
	return (f.Page - 1) * f.Limit
}

// ToCSVHeaders returns CSV headers for summary report
func (r *ReferralReportSummary) ToCSVHeaders() []string {
	return []string{
		"Member ID",
		"Username", 
		"Phone",
		"First Name",
		"Last Name",
		"Refer Code",
		"Total Commission Amount",
		"Total Withdraw Amount", 
		"Net Amount",
		"Transaction Count",
		"Downline Count",
		"First Transaction Date",
		"Last Transaction Date",
		"Current Commission Balance",
	}
}

// ToCSVRow converts summary record to CSV row
func (r *ReferralReportSummary) ToCSVRow() []string {
	username := ""
	if r.MemberUsername != nil {
		username = *r.MemberUsername
	}
	
	phone := ""
	if r.MemberPhone != nil {
		phone = *r.MemberPhone
	}
	
	firstName := ""
	if r.MemberFirstName != nil {
		firstName = *r.MemberFirstName
	}
	
	lastName := ""
	if r.MemberLastName != nil {
		lastName = *r.MemberLastName
	}

	return []string{
		fmt.Sprintf("%d", r.MemberID),
		username,
		phone,
		firstName,
		lastName,
		r.ReferCode,
		fmt.Sprintf("%.2f", r.TotalCommissionAmount),
		fmt.Sprintf("%.2f", r.TotalWithdrawAmount),
		fmt.Sprintf("%.2f", r.NetAmount),
		fmt.Sprintf("%d", r.TransactionCount),
		fmt.Sprintf("%d", r.DownlineCount),
		r.FirstTransactionDate.Format("2006-01-02 15:04:05"),
		r.LastTransactionDate.Format("2006-01-02 15:04:05"),
		fmt.Sprintf("%.2f", r.CurrentCommissionBalance),
	}
}

// ToCSVHeaders returns CSV headers for detail report
func (r *ReferralReportDetail) ToCSVHeaders() []string {
	return []string{
		"ID",
		"Member ID",
		"Member Username",
		"Member Phone", 
		"Type",
		"Amount",
		"Game Category",
		"Downline Member ID",
		"Downline Username",
		"Downline Phone",
		"Balance Before",
		"Balance After",
		"Referral Start Date",
		"Referral End Date",
		"Created By Admin",
		"Created By Member",
		"Status",
		"Remark",
		"Created At",
		"Updated At",
	}
}

// ToCSVHeaders returns CSV headers for grouped detail report
func (r *ReferralReportDetailGrouped) ToCSVHeaders() []string {
	return []string{
		"Member ID",
		"Member Username",
		"Member Phone",
		"Downline Member ID",
		"Downline Username", 
		"Downline Phone",
		"Total Commission Amount",
		"Total Withdraw Amount",
		"Total Adjustment Amount",
		"Total Bonus Amount",
		"Net Amount",
		"Transaction Count",
		"Commission Count",
		"Withdraw Count",
		"Adjustment Count",
		"Bonus Count",
		"First Transaction Date",
		"Last Transaction Date",
	}
}

// ToCSVRow converts grouped detail record to CSV row
func (r *ReferralReportDetailGrouped) ToCSVRow() []string {
	memberUsername := ""
	if r.MemberUsername != nil {
		memberUsername = *r.MemberUsername
	}
	
	memberPhone := ""
	if r.MemberPhone != nil {
		memberPhone = *r.MemberPhone
	}
	
	downlineMemberID := ""
	if r.DownlineMemberID != nil {
		downlineMemberID = fmt.Sprintf("%d", *r.DownlineMemberID)
	}
	
	downlineUsername := ""
	if r.DownlineMemberUsername != nil {
		downlineUsername = *r.DownlineMemberUsername
	}
	
	downlinePhone := ""
	if r.DownlineMemberPhone != nil {
		downlinePhone = *r.DownlineMemberPhone
	}

	return []string{
		fmt.Sprintf("%d", r.MemberID),
		memberUsername,
		memberPhone,
		downlineMemberID,
		downlineUsername,
		downlinePhone,
		fmt.Sprintf("%.2f", r.TotalCommissionAmount),
		fmt.Sprintf("%.2f", r.TotalWithdrawAmount),
		fmt.Sprintf("%.2f", r.TotalAdjustmentAmount),
		fmt.Sprintf("%.2f", r.TotalBonusAmount),
		fmt.Sprintf("%.2f", r.NetAmount),
		fmt.Sprintf("%d", r.TransactionCount),
		fmt.Sprintf("%d", r.CommissionCount),
		fmt.Sprintf("%d", r.WithdrawCount),
		fmt.Sprintf("%d", r.AdjustmentCount),
		fmt.Sprintf("%d", r.BonusCount),
		r.FirstTransactionDate.Format("2006-01-02 15:04:05"),
		r.LastTransactionDate.Format("2006-01-02 15:04:05"),
	}
}

// ToCSVRow converts detail record to CSV row
func (r *ReferralReportDetail) ToCSVRow() []string {
	memberUsername := ""
	if r.MemberUsername != nil {
		memberUsername = *r.MemberUsername
	}
	
	memberPhone := ""
	if r.MemberPhone != nil {
		memberPhone = *r.MemberPhone
	}
	
	downlineMemberID := ""
	if r.DownlineMemberID != nil {
		downlineMemberID = fmt.Sprintf("%d", *r.DownlineMemberID)
	}
	
	downlineUsername := ""
	if r.DownlineMemberUsername != nil {
		downlineUsername = *r.DownlineMemberUsername
	}
	
	downlinePhone := ""
	if r.DownlineMemberPhone != nil {
		downlinePhone = *r.DownlineMemberPhone
	}
	
	referralStartDate := ""
	if r.ReferralStartDate != nil {
		referralStartDate = r.ReferralStartDate.Format("2006-01-02 15:04:05")
	}
	
	referralEndDate := ""
	if r.ReferralEndDate != nil {
		referralEndDate = r.ReferralEndDate.Format("2006-01-02 15:04:05")
	}
	
	createdByAdmin := ""
	if r.CreatedByAdmin != nil {
		createdByAdmin = fmt.Sprintf("%d", *r.CreatedByAdmin)
	}
	
	createdByMember := ""
	if r.CreatedByMember != nil {
		createdByMember = fmt.Sprintf("%d", *r.CreatedByMember)
	}
	
	gameCategory := ""
	if r.GameCategory != nil {
		gameCategory = *r.GameCategory
	}
	
	remark := ""
	if r.Remark != nil {
		remark = *r.Remark
	}

	return []string{
		fmt.Sprintf("%d", r.ID),
		fmt.Sprintf("%d", r.MemberID),
		memberUsername,
		memberPhone,
		r.Type,
		fmt.Sprintf("%.2f", r.Amount),
		gameCategory,
		downlineMemberID,
		downlineUsername,
		downlinePhone,
		fmt.Sprintf("%.2f", r.BalanceBefore),
		fmt.Sprintf("%.2f", r.BalanceAfter),
		referralStartDate,
		referralEndDate,
		createdByAdmin,
		createdByMember,
		r.Status,
		remark,
		r.CreatedAt.Format("2006-01-02 15:04:05"),
		r.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}