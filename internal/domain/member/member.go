package member

import (
	"context"
	"crypto/rand"
	"fmt"
	"regexp"
	"strconv"
	"time"

	"blacking-api/pkg/errors"

	"golang.org/x/crypto/bcrypt"
)

type Member struct {
	ID                 int        `json:"id"`
	Username           *string    `json:"username"`
	Password           string     `json:"password,omitempty"`
	GameUsername       *string    `json:"game_username"`
	GamePassword       *string    `json:"game_password,omitempty"`
	FirstName          *string    `json:"first_name"`
	LastName           *string    `json:"last_name"`
	Phone              *string    `json:"phone"`
	Gender             *string    `json:"gender"`
	TwUsername         *string    `json:"tw_username"`
	LineID             *string    `json:"line_id"`
	BankCode           *string    `json:"bank_code"`
	BankNumber         *string    `json:"bank_number"`
	BankShortName      *string    `json:"bank_short_name,omitempty"` // Bank short name from JOIN
	Remark             *string    `json:"remark"`
	CreatedBy          *int       `json:"created_by"`
	Avatar             *string    `json:"avatar"`
	LoginStatus        bool       `json:"login_status"`
	OperateStatus      bool       `json:"operate_status"`
	RegisterStatus     bool       `json:"register_status"`
	Balance            float64    `json:"balance"`
	ReferUserID        *int       `json:"refer_user_id"`
	ReferCode          string     `json:"refer_code"`
	RegisterReferCode  *string    `json:"register_refer_code"`
	LastOnline         *time.Time `json:"last_online"`
	IsEnable           bool       `json:"is_enable"`
	RegisterIP         *string    `json:"register_ip"`
	LastLoginIP        *string    `json:"last_login_ip"`
	LastLoginUserAgent *string    `json:"last_login_user_agent"`
	LastLoginDevice    *string    `json:"last_login_device"`
	SessionID          *string    `json:"session_id"`
	TwofaSecret        *string    `json:"twofa_secret,omitempty"`
	TwofaStatus        *int       `json:"twofa_status"`
	TwofaVerifyCount   *int       `json:"twofa_verify_count"`
	BirthDate          *string    `json:"birth_date"` // date YYYY-MM-DD

	// Customer follow-up fields
	FollowUpTag    *string    `json:"follow_up_tag"`    // cut_off, call_later, not_convenient, not_interested, no_money, no_answer, waiting_transfer
	FollowUpStatus *string    `json:"follow_up_status"` // contacted, unreachable, not_contacted
	ContactedBy    *int       `json:"contacted_by"`     // Admin ID who last contacted
	LastContactAt  *time.Time `json:"last_contact_at"`  // Last contact timestamp

	// Partner-related fields
	IsPartner       bool    `json:"is_partner"`
	MemberGroupID   *int    `json:"member_group_id"`
	ReferralGroupID *int    `json:"referral_group_id"`
	ShowPartnerInfo bool    `json:"show_partner_info"`
	ChannelID       *int    `json:"channel_id"`
	ChannelName     *string `json:"channel_name,omitempty"` // Added for JOIN with channels table
	PlatformID      *string `json:"platform_id"`            // Changed from *int to *string to match VARCHAR(50) in database
	PartnerRemark   *string `json:"partner_remark"`

	// Group names from JOIN
	MemberGroupName     *string `json:"member_group_name,omitempty"`
	CommissionGroupName *string `json:"commission_group_name,omitempty"`
	ReferralGroupName   *string `json:"referral_group_name,omitempty"`
	CreatedByUsername   *string `json:"created_by_username,omitempty"`

	// Game data from external API
	GameProviders map[string]string `json:"game_providers,omitempty"` // Providers from Game API

	Status    Status    `json:"status"`
	DeleteBy  *int      `json:"delete_by"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type Status string

const (
	StatusActive    Status = "active"
	StatusInactive  Status = "inactive"
	StatusSuspended Status = "suspended"
	StatusBanned    Status = "banned"
	StatusAll       Status = "all" // for total count display
)

type CreateMemberRequest struct {
	Phone           string  `json:"phone" validate:"required,min=10,max=15"`
	Gender          string  `json:"gender" validate:"required,oneof=male female other"`
	FirstName       *string `json:"first_name" validate:"omitempty,max=100"`
	LastName        *string `json:"last_name" validate:"omitempty,max=100"`
	Password        string  `json:"password" validate:"required,min=6"`
	MemberGroupID   int     `json:"member_group_id" validate:"required,min=1"`
	PartnerID       int     `json:"partner_id" validate:"omitempty,min=1"`
	ReferralGroupID int     `json:"referral_group_id" validate:"omitempty,min=1"`
	BankCode        string  `json:"bank_code" validate:"required,numeric,max=10"`
	BankNumber      string  `json:"bank_number" validate:"required,min=1,max=50"`
	Remark          *string `json:"remark" validate:"omitempty,max=500"`
}

// RegisterMemberRequest for public member registration (no admin required)
type RegisterMemberRequest struct {
	Username          string  `json:"username" validate:"required,min=3,max=50"`
	Password          string  `json:"password" validate:"required,min=6"`
	FirstName         *string `json:"first_name" validate:"omitempty,max=100"`
	LastName          *string `json:"last_name" validate:"omitempty,max=100"`
	Phone             *string `json:"phone" validate:"omitempty,max=50"`
	Gender            *string `json:"gender" validate:"omitempty,oneof=male female other"`
	ReferUserID       *int    `json:"refer_user_id" validate:"omitempty"`
	RegisterReferCode *string `json:"register_refer_code" validate:"omitempty,len=8"`
	RegisterIP        *string `json:"register_ip" validate:"omitempty,max=100"`
}

type UpdateMemberRequest struct {
	// Username      *string `json:"username" validate:"omitempty,min=3,max=50"`
	Password        *string `json:"password" validate:"omitempty,min=6"`
	GameUsername    *string `json:"game_username" validate:"omitempty,min=3,max=50"`
	GamePassword    *string `json:"game_password" validate:"omitempty,min=6"`
	FirstName       *string `json:"first_name" validate:"omitempty,max=100"`
	LastName        *string `json:"last_name" validate:"omitempty,max=100"`
	Phone           *string `json:"phone" validate:"omitempty,max=50"`
	Gender          *string `json:"gender" validate:"omitempty,oneof=male female other"`
	TwUsername      *string `json:"tw_username" validate:"omitempty,max=50"`
	LineID          *string `json:"line_id" validate:"omitempty,max=50"`
	BankCode        *string `json:"bank_code" validate:"omitempty,numeric,max=10"`
	BankNumber      *string `json:"bank_number" validate:"omitempty,max=50"`
	Avatar          *string `json:"avatar" validate:"omitempty,max=255"`
	LoginStatus     *bool   `json:"login_status"`
	OperateStatus   *bool   `json:"operate_status"`
	IsEnable        *bool   `json:"is_enable"`
	MemberGroupID   *int    `json:"member_group_id" validate:"required,min=1"`
	ReferralGroupID *int    `json:"referral_group_id" validate:"required,min=1"`
	PartnerID       *int    `json:"partner_id" validate:"omitempty,min=1"`
}

// RegisterMemberRequest for public member registration (no admin required)
type VerifySendOTPRequest struct {
	Phone string `json:"phone" validate:"omitempty,min=10,max=10"`
}

// VerifyRegistrationOTPRequest for verifying OTP and completing registration
type VerifyRegistrationOTPRequest struct {
	Phone     string `json:"phone" validate:"required"`
	Code      string `json:"code" validate:"required,len=6"`
	Reference string `json:"reference" validate:"required,len=6"`
}

// CompleteRegistrationRequest for completing registration with full member data
type CompleteRegistrationRequest struct {
	Phone string `json:"phone" validate:"required"`

	// Registration data (username will be validated but phone will be used as actual username)
	Username          string  `json:"username" validate:"required,min=3,max=50"`
	Password          string  `json:"password" validate:"required,min=6"`
	FirstName         string  `json:"first_name" validate:"required,min=1,max=100"`
	LastName          string  `json:"last_name" validate:"required,min=1,max=100"`
	Gender            string  `json:"gender" validate:"omitempty,oneof=male female other"`
	BankCode          string  `json:"bank_code" validate:"omitempty,numeric,max=10"`
	BankNumber        string  `json:"bank_number" validate:"omitempty,max=50"`
	RegisterReferCode *string `json:"register_refer_code" validate:"omitempty,len=8"`
	AcceptTerms       bool    `json:"accept_terms" validate:"required"`
}

// AgentRegistrationRequest for agent-based registration (matches agent's structure)
type AgentRegistrationRequest struct {
	Username  string  `json:"username" validate:"required,min=4,max=20"`
	Email     string  `json:"email" validate:"required,email"`
	FirstName string  `json:"firstName" validate:"required,min=1,max=100"`
	LastName  string  `json:"lastName" validate:"required,min=1,max=100"`
	Phone     *string `json:"phone" validate:"omitempty,min=10,max=20"`
	Password  string  `json:"password" validate:"required,min=6"`
}

// CreatePartnerRequest for creating partner member
type CreatePartnerRequest struct {
	Phone           string  `json:"phone" validate:"required,min=10,max=15"`
	Gender          string  `json:"gender" validate:"required,oneof=male female other"`
	FirstName       string  `json:"first_name" validate:"required,min=1,max=100"`
	MemberGroupID   int     `json:"member_group_id" validate:"required,min=1"`
	ReferralGroupID *int    `json:"referral_group_id" validate:"omitempty,min=1"`
	Password        string  `json:"password" validate:"required,min=6,max=50"`
	ShowPartnerInfo bool    `json:"show_partner_info" validate:"required"`
	ChannelID       int     `json:"channel_id" validate:"required,min=1"`
	PlatformID      string  `json:"platform_id" validate:"required,min=1,max=50"`
	PartnerRemark   *string `json:"partner_remark" validate:"omitempty,max=500"`
}

// UpdatePartnerRequest for updating partner member
type UpdatePartnerRequest struct {
	Phone           *string `json:"phone" validate:"omitempty,min=10,max=15"`
	Gender          *string `json:"gender" validate:"omitempty,oneof=male female other"`
	MemberGroupID   *int    `json:"member_group_id" validate:"omitempty,min=1"`
	ReferralGroupID *int    `json:"referral_group_id" validate:"omitempty,min=1"`
	ShowPartnerInfo *bool   `json:"show_partner_info"`
	ChannelID       *int    `json:"channel_id" validate:"omitempty,min=1"`
	PlatformID      *string `json:"platform_id" validate:"omitempty"`
	PartnerRemark   *string `json:"partner_remark" validate:"omitempty,max=500"`
}

// ChangePartnerPasswordRequest for changing partner password
type ChangePartnerPasswordRequest struct {
	NewPassword string `json:"new_password" validate:"required,min=6,max=50"`
}

// DeletePartnerRequest for deleting partner
type DeletePartnerRequest struct {
	ActionRemark *string `json:"action_remark" validate:"omitempty,max=500"`
}

// ChangeMemberPasswordRequest for changing member password by admin
type ChangeMemberPasswordRequest struct {
	NewPassword string `json:"new_password" validate:"required,min=6,max=50"`
}

// ChangeMemberPartnerRequest for changing member partner by admin
type ChangeMemberPartnerRequest struct {
	PartnerID    *int    `json:"partner_id" validate:"omitempty,min=1"`
	ActionRemark *string `json:"action_remark" validate:"omitempty,max=500"`
}

// UpdateBankInfoRequest for updating member bank information
type UpdateBankInfoRequest struct {
	BankCode   *string `json:"bank_code" validate:"omitempty,numeric,max=10"`
	BankNumber *string `json:"bank_number" validate:"omitempty,max=50"`
	FirstName  *string `json:"first_name" validate:"omitempty,max=100"`
	LastName   *string `json:"last_name" validate:"omitempty,max=100"`
	TwUsername *string `json:"tw_username" validate:"omitempty,max=50"`
	Title      *string `json:"title" validate:"omitempty,max=200"`
}

// DeleteMemberRequest for deleting member
type DeleteMemberRequest struct {
	ActionRemark *string `json:"action_remark" validate:"omitempty,max=500"`
}

// SuspendMemberRequest for suspending member
type SuspendMemberRequest struct {
	ActionRemark *string `json:"action_remark" validate:"omitempty,max=500"`
}

// RegistrationOTPResponse for OTP sending response
type RegistrationOTPResponse struct {
	Message   string `json:"message"`
	Phone     string `json:"phone"`
	Reference string `json:"reference"`
	ExpiresIn int    `json:"expires_in"` // seconds
}

type MemberResponse struct {
	ID                 int        `json:"id"`
	Username           *string    `json:"username"`
	GameUsername       *string    `json:"game_username"`
	FirstName          *string    `json:"first_name"`
	LastName           *string    `json:"last_name"`
	Phone              *string    `json:"phone"`
	Gender             *string    `json:"gender"`
	TwUsername         *string    `json:"tw_username"`
	LineID             *string    `json:"line_id"`
	BankCode           *string    `json:"bank_code"`
	BankNumber         *string    `json:"bank_number"`
	BankShortName      *string    `json:"bank_short_name,omitempty"` // Bank short name from JOIN
	Remark             *string    `json:"remark"`
	Avatar             *string    `json:"avatar"`
	LoginStatus        bool       `json:"login_status"`
	OperateStatus      bool       `json:"operate_status"`
	RegisterStatus     bool       `json:"register_status"`
	Balance            float64    `json:"balance"`
	ReferUserID        *int       `json:"refer_user_id"`
	ReferCode          string     `json:"refer_code"`
	RegisterReferCode  *string    `json:"register_refer_code"`
	LastOnline         *time.Time `json:"last_online"`
	IsEnable           bool       `json:"is_enable"`
	RegisterIP         *string    `json:"register_ip"`
	LastLoginIP        *string    `json:"last_login_ip"`
	LastLoginUserAgent *string    `json:"last_login_user_agent"`
	LastLoginDevice    *string    `json:"last_login_device"`
	TwofaStatus        *int       `json:"twofa_status"`
	TwofaVerifyCount   *int       `json:"twofa_verify_count"`
	BirthDate          *string    `json:"birth_date"` // date YYYY-MM-DD

	// Customer follow-up fields
	FollowUpTag    *string    `json:"follow_up_tag"`    // cut_off, call_later, not_convenient, not_interested, no_money, no_answer, waiting_transfer
	FollowUpStatus *string    `json:"follow_up_status"` // contacted, unreachable, not_contacted
	ContactedBy    *int       `json:"contacted_by"`     // Admin ID who last contacted
	LastContactAt  *time.Time `json:"last_contact_at"`  // Last contact timestamp

	// Partner-related fields
	IsPartner       bool    `json:"is_partner"`
	MemberGroupID   *int    `json:"member_group_id"`
	ReferralGroupID *int    `json:"referral_group_id"`
	ShowPartnerInfo bool    `json:"show_partner_info"`
	ChannelID       *int    `json:"channel_id"`
	ChannelName     *string `json:"channel_name,omitempty"`
	PlatformID      *string `json:"platform_id"`
	PartnerRemark   *string `json:"partner_remark"`

	// Group names from JOIN
	MemberGroupName     *string `json:"member_group_name,omitempty"`
	CommissionGroupName *string `json:"commission_group_name,omitempty"`
	ReferralGroupName   *string `json:"referral_group_name,omitempty"`
	CreatedByUsername   *string `json:"created_by_username,omitempty"`

	// Game data from external API
	GameProviders map[string]string `json:"game_providers,omitempty"` // Providers from Game API

	Status    Status    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// PartnerDropdownResponse represents partner data for dropdown usage
type PartnerDropdownResponse struct {
	ID        int    `json:"id"`
	Name      string `json:"name"` // Combined first_name + last_name
	ReferCode string `json:"refer_code"`
}

// NewMemberFromRegistration creates a new member from public registration
func NewMemberFromRegistration(req RegisterMemberRequest, clientIP string) (*Member, error) {
	if err := validateRegisterRequest(req); err != nil {
		return nil, err
	}

	hashedPassword, err := hashPassword(req.Password)
	if err != nil {
		return nil, errors.NewInternalError("failed to hash password")
	}

	// Set register IP from client if not provided
	registerIP := req.RegisterIP
	if registerIP == nil && clientIP != "" {
		registerIP = &clientIP
	}

	member := &Member{
		// ID will be generated by database
		Username:          &req.Username,
		Password:          hashedPassword,
		GameUsername:      nil, // Game credentials not set during registration
		GamePassword:      nil,
		FirstName:         req.FirstName,
		LastName:          req.LastName,
		Phone:             req.Phone,
		Gender:            req.Gender,
		LoginStatus:       false,
		OperateStatus:     false,
		RegisterStatus:    true,
		Balance:           0.0,
		ReferUserID:       req.ReferUserID,
		ReferCode:         generateReferCode(), // Generate unique refer code
		RegisterReferCode: req.RegisterReferCode,
		IsEnable:          true, // Auto-enable new registrations
		RegisterIP:        registerIP,
		Status:            StatusActive,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	return member, nil
}

// NewMemberFromAgentRegistration creates a new member from agent registration request
func NewMemberFromAgentRegistration(req AgentRegistrationRequest, clientIP string) (*Member, error) {
	if err := validateAgentRegistrationRequest(req); err != nil {
		return nil, err
	}

	hashedPassword, err := hashPassword(req.Password)
	if err != nil {
		return nil, errors.NewInternalError("failed to hash password")
	}

	registerIP := &clientIP

	// Use phone as username if provided, otherwise use username from request
	username := req.Username
	if req.Phone != nil && *req.Phone != "" {
		username = *req.Phone
	}

	member := &Member{
		// ID will be generated by database
		Username:          &username,
		Password:          hashedPassword,
		GameUsername:      nil, // Game credentials not set during registration
		GamePassword:      nil,
		FirstName:         &req.FirstName,
		LastName:          &req.LastName,
		Phone:             req.Phone,
		Gender:            nil, // Set default gender or make it configurable
		LoginStatus:       false,
		OperateStatus:     false,
		RegisterStatus:    true,
		Balance:           0.0,
		ReferUserID:       nil,
		ReferCode:         generateReferCode(), // Generate unique refer code
		RegisterReferCode: nil,
		IsEnable:          true, // Auto-enable new registrations
		RegisterIP:        registerIP,
		Status:            StatusActive,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	return member, nil
}

// NewMemberFromCompleteRegistration creates a new member from complete registration request
func NewMemberFromCompleteRegistration(req CompleteRegistrationRequest, clientIP string, memberRepo interface {
	GetByReferCode(ctx context.Context, referCode string) (*Member, error)
}) (*Member, error) {
	if err := validateCompleteRegistrationRequest(req); err != nil {
		return nil, err
	}

	hashedPassword, err := hashPassword(req.Password)
	if err != nil {
		return nil, errors.NewInternalError("failed to hash password")
	}

	// Validate and resolve refer code to member ID
	var referUserID *int
	if req.RegisterReferCode != nil && *req.RegisterReferCode != "" {
		referrer, err := memberRepo.GetByReferCode(context.Background(), *req.RegisterReferCode)
		if err != nil {
			return nil, errors.NewValidationError("invalid refer code")
		}
		referUserID = &referrer.ID
	}

	registerIP := &clientIP

	member := &Member{
		// ID will be generated by database
		Username:          &req.Phone, // Use phone as username
		Password:          hashedPassword,
		GameUsername:      nil, // Game credentials not set during registration
		GamePassword:      nil,
		FirstName:         &req.FirstName,
		LastName:          &req.LastName,
		Phone:             &req.Phone,
		Gender:            &req.Gender,
		BankCode:          &req.BankCode,
		BankNumber:        &req.BankNumber,
		LoginStatus:       false,
		OperateStatus:     false,
		RegisterStatus:    true,
		Balance:           0.0,
		ReferUserID:       referUserID,         // Set from resolved refer code
		ReferCode:         generateReferCode(), // Generate unique refer code
		RegisterReferCode: req.RegisterReferCode,
		IsEnable:          true, // Auto-enable new registrations
		RegisterIP:        registerIP,
		Status:            StatusActive,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	return member, nil
}

// NewPartner creates a new partner member
func NewPartner(req CreatePartnerRequest, clientIP string, adminID int) (*Member, error) {
	if err := validatePartnerRequest(req); err != nil {
		return nil, err
	}

	hashedPassword, err := hashPassword(req.Password)
	if err != nil {
		return nil, errors.NewInternalError("failed to hash password")
	}

	// Set register IP from client
	var registerIP *string
	if clientIP != "" {
		registerIP = &clientIP
	}

	gender := &req.Gender
	firstName := &req.FirstName

	now := time.Now()
	member := &Member{
		// ID will be generated by database
		Username:       &req.Phone, // Use phone as username for partners
		Password:       hashedPassword,
		GameUsername:   nil, // Game credentials not set during partner creation
		GamePassword:   nil,
		FirstName:      firstName,
		Phone:          &req.Phone,
		Gender:         gender,
		LoginStatus:    false,
		OperateStatus:  false,
		RegisterStatus: true,
		Balance:        0.0,
		ReferCode:      generateReferCode(), // Generate unique refer code
		IsEnable:       true,                // Auto-enable new partners
		RegisterIP:     registerIP,          // Set from clientIP parameter
		CreatedBy:      &adminID,            // Set admin who created the partner

		// Partner-specific fields
		IsPartner:       true, // Mark as partner
		MemberGroupID:   &req.MemberGroupID,
		ReferralGroupID: req.ReferralGroupID,
		ShowPartnerInfo: req.ShowPartnerInfo,
		ChannelID:       &req.ChannelID,
		PlatformID:      &req.PlatformID, // Now both are *string
		PartnerRemark:   req.PartnerRemark,

		Status:    StatusActive,
		CreatedAt: now,
		UpdatedAt: now,
	}

	return member, nil
}

func NewMember(req CreateMemberRequest, adminID int, memberRepo interface {
	GetByID(ctx context.Context, id string) (*Member, error)
}) (*Member, error) {
	if err := validateCreateRequest(req); err != nil {
		return nil, err
	}

	hashedPassword, err := hashPassword(req.Password)
	if err != nil {
		return nil, errors.NewInternalError("failed to hash password")
	}

	// Validate and resolve partner_id to refer_user_id and refer_code
	var referUserID *int
	var registerReferCode *string
	if req.PartnerID != 0 {
		partner, err := memberRepo.GetByID(context.Background(), strconv.Itoa(req.PartnerID))
		if err != nil {
			return nil, errors.NewValidationError("invalid partner ID")
		}
		if !partner.IsPartner {
			return nil, errors.NewValidationError("specified user is not a partner")
		}
		referUserID = &req.PartnerID
		registerReferCode = &partner.ReferCode
	}

	now := time.Now()
	member := &Member{
		// Username will be generated after getting ID from database
		Password:          hashedPassword,
		Phone:             &req.Phone,
		Gender:            &req.Gender, // Add this line
		FirstName:         req.FirstName,
		LastName:          req.LastName,
		BankCode:          &req.BankCode,
		BankNumber:        &req.BankNumber,
		Remark:            req.Remark,
		CreatedBy:         &adminID,
		LoginStatus:       false,
		OperateStatus:     false,
		RegisterStatus:    true,
		Balance:           0.0,
		ReferUserID:       referUserID,
		ReferCode:         generateReferCode(),
		RegisterReferCode: registerReferCode,
		IsEnable:          true,
		MemberGroupID:     &req.MemberGroupID,
		ReferralGroupID:   &req.ReferralGroupID,
		Status:            StatusActive,
		DeleteBy:          nil,
		CreatedAt:         now,
		UpdatedAt:         now,
	}

	return member, nil
}

func (m *Member) Update(req UpdateMemberRequest) error {
	if err := validateUpdateRequest(req); err != nil {
		return err
	}

	if req.Password != nil {
		m.SetPassword(*req.Password)
	}
	if req.GameUsername != nil {
		m.GameUsername = req.GameUsername
	}
	if req.GamePassword != nil {
		m.GamePassword = req.GamePassword
	}
	if req.FirstName != nil {
		m.FirstName = req.FirstName
	}
	if req.LastName != nil {
		m.LastName = req.LastName
	}
	if req.Phone != nil {
		m.Phone = req.Phone
	}
	if req.Gender != nil {
		m.Gender = req.Gender
	}
	if req.TwUsername != nil {
		m.TwUsername = req.TwUsername
	}
	if req.LineID != nil {
		m.LineID = req.LineID
	}
	if req.BankCode != nil {
		m.BankCode = req.BankCode
	}
	if req.BankNumber != nil {
		m.BankNumber = req.BankNumber
	}
	if req.Avatar != nil {
		m.Avatar = req.Avatar
	}
	if req.LoginStatus != nil {
		m.LoginStatus = *req.LoginStatus
	}
	if req.OperateStatus != nil {
		m.OperateStatus = *req.OperateStatus
	}
	if req.IsEnable != nil {
		m.IsEnable = *req.IsEnable
	}
	if req.MemberGroupID != nil {
		m.MemberGroupID = req.MemberGroupID
	}
	if req.ReferralGroupID != nil {
		m.ReferralGroupID = req.ReferralGroupID
	}
	if req.PartnerID != nil {
		m.ReferUserID = req.PartnerID
	}

	m.UpdatedAt = time.Now()
	return nil
}

func (m *Member) IsActive() bool {
	return m.Status == StatusActive
}

func (m *Member) Deactivate() {
	m.Status = StatusInactive
	m.UpdatedAt = time.Now()
}

func (m *Member) Activate() {
	m.Status = StatusActive
	m.UpdatedAt = time.Now()
}

func (m *Member) ToResponse() MemberResponse {
	return MemberResponse{
		ID:                 m.ID,
		Username:           m.Username,
		GameUsername:       m.GameUsername,
		FirstName:          m.FirstName,
		LastName:           m.LastName,
		Phone:              m.Phone,
		Gender:             m.Gender,
		TwUsername:         m.TwUsername,
		LineID:             m.LineID,
		BankCode:           m.BankCode,
		BankNumber:         m.BankNumber,
		BankShortName:      m.BankShortName,
		Remark:             m.Remark,
		Avatar:             m.Avatar,
		LoginStatus:        m.LoginStatus,
		OperateStatus:      m.OperateStatus,
		RegisterStatus:     m.RegisterStatus,
		Balance:            m.Balance,
		ReferUserID:        m.ReferUserID,
		ReferCode:          m.ReferCode,
		RegisterReferCode:  m.RegisterReferCode,
		LastOnline:         m.LastOnline,
		IsEnable:           m.IsEnable,
		RegisterIP:         m.RegisterIP,
		LastLoginIP:        m.LastLoginIP,
		LastLoginUserAgent: m.LastLoginUserAgent,
		LastLoginDevice:    m.LastLoginDevice,
		TwofaStatus:        m.TwofaStatus,
		TwofaVerifyCount:   m.TwofaVerifyCount,
		BirthDate:          m.BirthDate,

		// Customer follow-up fields
		FollowUpTag:    m.FollowUpTag,
		FollowUpStatus: m.FollowUpStatus,
		ContactedBy:    m.ContactedBy,
		LastContactAt:  m.LastContactAt,

		// Partner-related fields
		IsPartner:       m.IsPartner,
		MemberGroupID:   m.MemberGroupID,
		ReferralGroupID: m.ReferralGroupID,
		ShowPartnerInfo: m.ShowPartnerInfo,
		ChannelID:       m.ChannelID,
		ChannelName:     m.ChannelName,
		PlatformID:      m.PlatformID,
		PartnerRemark:   m.PartnerRemark,

		// Group names from JOIN
		MemberGroupName:     m.MemberGroupName,
		CommissionGroupName: m.CommissionGroupName,
		ReferralGroupName:   m.ReferralGroupName,
		CreatedByUsername:   m.CreatedByUsername,

		// Game data from external API
		GameProviders: m.GameProviders,

		Status:    m.Status,
		CreatedAt: m.CreatedAt,
		UpdatedAt: m.UpdatedAt,
	}
}

// ToDropdownResponse converts Member to PartnerDropdownResponse
func (m *Member) ToDropdownResponse() PartnerDropdownResponse {
	// Combine first_name and last_name
	name := ""
	if m.FirstName != nil && m.LastName != nil {
		name = *m.FirstName + " " + *m.LastName
	} else if m.FirstName != nil {
		name = *m.FirstName
	} else if m.LastName != nil {
		name = *m.LastName
	} else {
		if m.Username != nil {
			name = *m.Username // Fallback to username if no names
		} else {
			name = "Unnamed" // Fallback if no username available
		}
	}

	return PartnerDropdownResponse{
		ID:        m.ID,
		Name:      name,
		ReferCode: m.ReferCode,
	}
}

func (u *Member) SetPassword(password string) error {
	if len(password) < 8 {
		return errors.NewValidationError("password must be at least 8 characters long")
	}

	hashedPassword, err := hashPassword(password)
	if err != nil {
		return errors.NewInternalError("failed to hash password")
	}

	u.Password = hashedPassword
	u.UpdatedAt = time.Now()
	return nil
}

func (u *Member) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(password))
	return err == nil
}

func hashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}

// generateReferCode generates a unique refer code for member
func generateReferCode() string {
	// Generate random bytes using crypto/rand for better security
	bytes := make([]byte, 8)
	rand.Read(bytes)

	// Convert to uppercase alphanumeric string
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, 8)

	for i := range result {
		result[i] = charset[bytes[i]%byte(len(charset))]
	}

	return string(result)
}

func validateRegisterRequest(req RegisterMemberRequest) error {
	if req.Username == "" {
		return errors.NewValidationError("username is required")
	}
	if req.Password == "" {
		return errors.NewValidationError("password is required")
	}
	if len(req.Password) < 6 {
		return errors.NewValidationError("password must be at least 6 characters")
	}
	if len(req.Username) < 3 {
		return errors.NewValidationError("username must be at least 3 characters")
	}
	return nil
}

func validateCreateRequest(req CreateMemberRequest) error {
	if req.Phone == "" {
		return errors.NewValidationError("phone is required")
	}

	// Validate Thai phone number format (10 digits starting with 0)
	if !isValidThaiPhoneNumber(req.Phone) {
		return errors.NewValidationError("phone must be a valid Thai phone number (10 digits starting with 0)")
	}
	if req.Password == "" {
		return errors.NewValidationError("password is required")
	}
	if len(req.Password) < 6 {
		return errors.NewValidationError("password must be at least 6 characters")
	}
	if req.MemberGroupID <= 0 {
		return errors.NewValidationError("member_group_id is required and must be greater than 0")
	}
	if req.BankCode == "" {
		return errors.NewValidationError("bank_code is required")
	}
	if req.BankNumber == "" {
		return errors.NewValidationError("bank_number is required")
	}
	if req.PartnerID < 0 {
		return errors.NewValidationError("partner_id must be equal or greater than 0")
	}
	if req.ReferralGroupID <= 0 {
		return errors.NewValidationError("referral_group_id must be greater than 0")
	}
	if req.Remark != nil && len(*req.Remark) > 500 {
		return errors.NewValidationError("remark cannot exceed 500 characters")
	}
	if req.Gender != "male" && req.Gender != "female" && req.Gender != "other" {
		return errors.NewValidationError("gender must be male, female, or other")
	}
	return nil
}

func validateUpdateRequest(req UpdateMemberRequest) error {
	if req.GameUsername != nil && *req.GameUsername == "" {
		return errors.NewValidationError("game_username cannot be empty")
	}
	return nil
}

// validatePartnerRequest validates partner creation request
func validatePartnerRequest(req CreatePartnerRequest) error {
	if req.Phone == "" {
		return errors.NewValidationError("phone is required")
	}

	// Validate Thai phone number format
	if !isValidThaiPhoneNumber(req.Phone) {
		return errors.NewValidationError("phone must be a valid Thai phone number (10 digits starting with 0)")
	}
	if req.FirstName == "" {
		return errors.NewValidationError("first_name is required")
	}
	if req.Password == "" {
		return errors.NewValidationError("password is required")
	}
	if req.MemberGroupID <= 0 {
		return errors.NewValidationError("member_group_id must be greater than 0")
	}
	if req.ChannelID <= 0 {
		return errors.NewValidationError("channel_id must be greater than 0")
	}
	return nil
}

func validateCompleteRegistrationRequest(req CompleteRegistrationRequest) error {
	if req.Password == "" {
		return errors.NewValidationError("password is required")
	}
	if req.Phone == "" {
		return errors.NewValidationError("phone is required")
	}

	// Validate Thai phone number format
	if !isValidThaiPhoneNumber(req.Phone) {
		return errors.NewValidationError("phone must be a valid Thai phone number (10 digits starting with 0)")
	}
	if len(req.Password) < 6 {
		return errors.NewValidationError("password must be at least 6 characters")
	}
	if req.BankCode == "" {
		return errors.NewValidationError("bank code is required")
	}
	if req.BankNumber == "" {
		return errors.NewValidationError("bank number is required")
	}
	if req.Gender != "male" && req.Gender != "female" && req.Gender != "other" {
		return errors.NewValidationError("gender is not correct")
	}
	if !req.AcceptTerms {
		return errors.NewValidationError("you must accept terms and conditions")
	}
	return nil
}

func validateAgentRegistrationRequest(req AgentRegistrationRequest) error {
	if req.Username == "" {
		return errors.NewValidationError("username is required")
	}
	if len(req.Username) < 4 || len(req.Username) > 20 {
		return errors.NewValidationError("username must be between 4 and 20 characters")
	}
	if req.Email == "" {
		return errors.NewValidationError("email is required")
	}
	// Basic email validation
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(req.Email) {
		return errors.NewValidationError("email format is invalid")
	}
	if req.FirstName == "" {
		return errors.NewValidationError("firstName is required")
	}
	if len(req.FirstName) < 1 || len(req.FirstName) > 100 {
		return errors.NewValidationError("firstName must be between 1 and 100 characters")
	}
	if req.LastName == "" {
		return errors.NewValidationError("lastName is required")
	}
	if len(req.LastName) < 1 || len(req.LastName) > 100 {
		return errors.NewValidationError("lastName must be between 1 and 100 characters")
	}
	if req.Password == "" {
		return errors.NewValidationError("password is required")
	}
	if len(req.Password) < 6 {
		return errors.NewValidationError("password must be at least 6 characters")
	}
	// Validate phone if provided
	if req.Phone != nil {
		if len(*req.Phone) < 10 || len(*req.Phone) > 20 {
			return errors.NewValidationError("phone must be between 10 and 20 characters")
		}
		// For Thai numbers, validate format if it looks like a Thai number
		if len(*req.Phone) == 10 && (*req.Phone)[0] == '0' {
			if !isValidThaiPhoneNumber(*req.Phone) {
				return errors.NewValidationError("phone must be a valid Thai phone number (10 digits starting with 0)")
			}
		}
	}
	return nil
}

// UpdatePartner updates partner-specific fields
func (m *Member) UpdatePartner(req UpdatePartnerRequest) error {
	if err := validateUpdatePartnerRequest(req); err != nil {
		return err
	}

	if req.Phone != nil {
		m.Phone = req.Phone
	}
	if req.Gender != nil {
		m.Gender = req.Gender
	}
	if req.MemberGroupID != nil {
		m.MemberGroupID = req.MemberGroupID
	}
	if req.ReferralGroupID != nil {
		m.ReferralGroupID = req.ReferralGroupID
	}
	if req.ShowPartnerInfo != nil {
		m.ShowPartnerInfo = *req.ShowPartnerInfo
	}
	if req.ChannelID != nil {
		m.ChannelID = req.ChannelID
	}
	if req.PlatformID != nil {
		m.PlatformID = req.PlatformID
	}
	if req.PartnerRemark != nil {
		m.PartnerRemark = req.PartnerRemark
	}

	m.UpdatedAt = time.Now()
	return nil
}

// validateUpdatePartnerRequest validates partner update request
func validateUpdatePartnerRequest(req UpdatePartnerRequest) error {
	if req.Phone != nil && *req.Phone == "" {
		return errors.NewValidationError("phone cannot be empty")
	}
	if req.Gender != nil && (*req.Gender != "male" && *req.Gender != "female" && *req.Gender != "other") {
		return errors.NewValidationError("gender must be male, female, or other")
	}
	return nil
}

// ChangePassword changes the member's password
func (m *Member) ChangePassword(newPassword string) error {
	if newPassword == "" {
		return errors.NewValidationError("new password is required")
	}
	if len(newPassword) < 6 {
		return errors.NewValidationError("password must be at least 6 characters")
	}

	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return errors.NewInternalError("failed to hash password")
	}

	m.Password = string(hashedPassword)
	m.UpdatedAt = time.Now()
	return nil
}

// ChangePasswordByAdmin allows admin to change member's password
func (m *Member) ChangePasswordByAdmin(newPassword, adminID, adminName string) error {
	if newPassword == "" {
		return errors.NewValidationError("new password is required")
	}
	if len(newPassword) < 6 {
		return errors.NewValidationError("password must be at least 6 characters")
	}

	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return errors.NewInternalError("failed to hash password")
	}

	m.Password = string(hashedPassword)
	m.UpdatedAt = time.Now()

	return nil
}

// GenerateUsername generates username with prefix and formatted ID
func (m *Member) GenerateUsername(prefix string) {
	formattedID := fmt.Sprintf("%06d", m.ID) // Format as 000001
	username := prefix + formattedID
	m.Username = &username
}

// MemberFilter represents filter criteria for member queries
type MemberFilter struct {
	Search        string  `json:"search"`          // full_name (first_name + last_name), username, phone contain
	Username      string  `json:"username"`        // username contain
	FullName      string  `json:"full_name"`       // full name (first_name + last_name) contain
	BankNumber    string  `json:"bank_number"`     // bank number contain
	MemberGroupID *int    `json:"member_group_id"` // exact match
	ReferUserID   *int    `json:"refer_user_id"`   // refer_user_id exact match
	PartnerID     *int    `json:"partner_id"`      // refer_user_id exact match (alias for backward compatibility)
	ReferUserName string  `json:"refer_user_name"` // refer user name contain
	CreatedBy     *int    `json:"created_by"`      // created_by exact match
	CreatedAt     *string `json:"created_at"`      // date YYYY-MM-DD
	BirthDate     *string `json:"birth_date"`      // date YYYY-MM-DD
	// Date range filters for created_at
	StartDateTime *time.Time `json:"start_datetime"` // created_at >= start_datetime
	EndDateTime   *time.Time `json:"end_datetime"`   // created_at <= end_datetime
	// Date range filters for last_online
	LastOnlineStartDateTime *time.Time `json:"last_online_start_datetime"` // last_online >= start_datetime
	LastOnlineEndDateTime   *time.Time `json:"last_online_end_datetime"`   // last_online <= end_datetime
	// Channel and platform filters
	ChannelID  *int     `json:"channel_id"`  // channel_id exact match
	PlatformID *string  `json:"platform_id"` // platform_id exact match
	Status     []Status `json:"status"`      // status filter (active, inactive, suspended, banned) - default: [active, suspended, banned]
	// Follow-up filters
	FollowUpTag    string `json:"follow_up_tag"`    // follow_up_tag exact match
	FollowUpStatus string `json:"follow_up_status"` // follow_up_status exact match
	ContactedBy    *int   `json:"contacted_by"`     // contacted_by exact match
	OrderBy        string `json:"order_by"`         // sort field: "id" (default), "balance", "created_at", etc.
	OrderDir       string `json:"order_dir"`        // sort direction: "desc" (default), "asc"
}

// MemberStatusCount represents count of members by status
type MemberStatusCount struct {
	Status Status `json:"status"`
	Label  string `json:"label"` // Thai label for status
	Count  int64  `json:"count"`
}

// MemberStatusCountsResponse represents response for member status counts
type MemberStatusCountsResponse struct {
	StatusCounts []MemberStatusCount `json:"status_counts"`
	TotalCount   int64               `json:"total_count"` // sum of active + suspended + banned
}

// GetStatusLabel returns Thai label for status
func GetStatusLabel(status Status) string {
	switch status {
	case StatusAll:
		return "ทั้งหมด"
	case StatusActive:
		return "ปกติ"
	case StatusSuspended:
		return "ถูกล็อก"
	case StatusBanned:
		return "ห้าม"
	case StatusInactive:
		return "ไม่ใช้งาน"
	default:
		return string(status)
	}
}

// ConvertAgentToCompleteRegistration converts agent registration request to complete registration request
func ConvertAgentToCompleteRegistration(req AgentRegistrationRequest) CompleteRegistrationRequest {
	phone := ""
	if req.Phone != nil {
		phone = *req.Phone
	}

	// Set default values for required fields that agent doesn't provide
	return CompleteRegistrationRequest{
		Phone:             phone,
		Username:          req.Username,
		Password:          req.Password,
		Gender:            "other",      // Default gender
		BankCode:          "001",        // Default bank code
		BankNumber:        "**********", // Default bank number
		RegisterReferCode: nil,
		AcceptTerms:       true, // Auto-accept for agent registrations
	}
}

// isValidThaiPhoneNumber validates Thai phone number format
// Thai phone numbers are 10 digits starting with 0 (e.g., **********)
func isValidThaiPhoneNumber(phone string) bool {
	// Check if phone matches Thai format: starts with 0, followed by 9 digits
	phoneRegex := regexp.MustCompile(`^0[0-9]{9}$`)
	return phoneRegex.MatchString(phone)
}

// GameProviderRequest represents request for game provider data
type GameProviderRequest struct {
	Phone    string `json:"phone" validate:"required"`
	Provider string `json:"provider" validate:"required"`
}

// ExternalAPIResponse represents response from external member API
type ExternalAPIResponse struct {
	Data    ExternalMemberData `json:"data"`
	Message string             `json:"message"`
	Success bool               `json:"success"`
}

// ExternalMemberData represents member data from external API
type ExternalMemberData struct {
	Member        ExternalMember     `json:"member"`
	Providers     []ExternalProvider `json:"providers"`
	ProviderCount int                `json:"providerCount"`
	GameUsername  string             `json:"gameUsername"`
}

// ExternalMember represents member data from external API
type ExternalMember struct {
	ID         int    `json:"id"`
	MemberCode string `json:"memberCode"`
	Username   string `json:"username"`
	Email      string `json:"email"`
	FirstName  string `json:"firstName"`
	LastName   string `json:"lastName"`
	Phone      string `json:"phone"`
	AgentCode  string `json:"agentCode"`
	Status     string `json:"status"`
	CreatedAt  string `json:"createdAt"`
	UpdatedAt  string `json:"updatedAt"`
}

// ExternalProvider represents provider data from external API
type ExternalProvider struct {
	ID                 int     `json:"id"`
	MemberCode         string  `json:"memberCode"`
	ProviderCode       string  `json:"providerCode"`
	ProviderMemberCode string  `json:"providerMemberCode"`
	ProviderUsername   string  `json:"providerUsername"`
	Status             string  `json:"status"`
	RegistrationDate   string  `json:"registrationDate"`
	LastSyncDate       string  `json:"lastSyncDate"`
	ErrorMessage       *string `json:"errorMessage"`
	CreatedAt          string  `json:"createdAt"`
	UpdatedAt          string  `json:"updatedAt"`
}
