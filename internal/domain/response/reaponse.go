package response

type Success struct {
	Message string      `json:"message" validate:"required,min=1,max=255"`
	Data    interface{} `json:"data"`
}

type Error struct {
	Message string `json:"message"`
	Error   string `json:"error,omitempty"`
	Code    int    `json:"code,omitempty"`
}

type SuccessWithPagination struct {
	Message    string      `json:"message" validate:"required,min=1,max=255"`
	Content    interface{} `json:"content"`
	Page       int64       `json:"page"`
	Limit      int64       `json:"limit"`
	TotalPages int64       `json:"totalPages"`
	TotalItems int64       `json:"totalItems"`
}
