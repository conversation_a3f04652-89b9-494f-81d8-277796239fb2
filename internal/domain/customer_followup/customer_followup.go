package customer_followup

import (
	"time"

	"blacking-api/internal/domain/member"
)

// FollowUpTag constants - Fixed tags as requested
const (
	TagCutOff          = "cut_off"          // ตัดสาย
	TagCallLater       = "call_later"       // ติดต่อภายหลัง
	TagNotConvenient   = "not_convenient"   // ไม่สะดวกคุย
	TagNotInterested   = "not_interested"   // ไม่สนใจโปรโมชั่น
	TagNoMoney         = "no_money"         // ไม่มีเงิน
	TagNoAnswer        = "no_answer"        // โทรไม่ติด
	TagWaitingTransfer = "waiting_transfer" // รอโอน
)

// FollowUpStatus constants
const (
	StatusContacted    = "contacted"     // เคยติดต่อแล้ว
	StatusUnreachable  = "unreachable"   // ติดต่อไม่ได้
	StatusNotContacted = "not_contacted" // ยังไม่ได้ติดต่อ (default)
)

// CustomerFollowUpFilter represents comprehensive filter for customer follow-up list
type CustomerFollowUpFilter struct {
	// Basic filters
	Search string `json:"search"` // search in member info
	Tag    string `json:"tag"`    // follow_up_tag filter
	Status string `json:"status"` // follow_up_status filter

	// Date range filters
	StartDateTime *time.Time `json:"start_datetime"` // created_at >= start_datetime
	EndDateTime   *time.Time `json:"end_datetime"`   // created_at <= end_datetime

	// Relationship filters
	PartnerID  *int    `json:"partner_id"`  // refer_user_id exact match
	ChannelID  *int    `json:"channel_id"`  // channel_id exact match
	PlatformID *string `json:"platform_id"` // platform_id exact match

	// Admin filter
	ContactedBy *int `json:"contacted_by"` // admin who contacted (contacted_by field)

	// Member status filters (from existing member filter)
	MemberStatus []member.Status `json:"member_status"` // member status filter

	// Pagination and sorting
	OrderBy  string `json:"order_by"`  // sort field: "id", "created_at", "last_contact_at"
	OrderDir string `json:"order_dir"` // sort direction: "desc" (default), "asc"
}

// CustomerFollowUpOldFilter represents filter for searching old customers based on last_online
type CustomerFollowUpOldFilter struct {
	// Basic filters (same as regular filter)
	Search string `json:"search"` // search in member info
	Tag    string `json:"tag"`    // follow_up_tag filter
	Status string `json:"status"` // follow_up_status filter

	// Date range filters - uses same parameter names but filters last_online instead of created_at
	StartDateTime *time.Time `json:"start_datetime"` // last_online >= start_datetime
	EndDateTime   *time.Time `json:"end_datetime"`   // last_online <= end_datetime

	// Relationship filters
	PartnerID  *int    `json:"partner_id"`  // refer_user_id exact match
	ChannelID  *int    `json:"channel_id"`  // channel_id exact match
	PlatformID *string `json:"platform_id"` // platform_id exact match

	// Admin filter
	ContactedBy *int `json:"contacted_by"` // admin who contacted (contacted_by field)

	// Member status filters (from existing member filter)
	MemberStatus []member.Status `json:"member_status"` // member status filter

	// Pagination and sorting
	OrderBy  string `json:"order_by"`  // sort field: "id", "last_online", "last_contact_at"
	OrderDir string `json:"order_dir"` // sort direction: "desc" (default), "asc"
}

// CustomerFollowUpResponse represents the response for customer follow-up list
type CustomerFollowUpResponse struct {
	// Member basic info
	ID         int        `json:"id"`
	Username   *string    `json:"username"`
	FirstName  *string    `json:"first_name"`
	LastName   *string    `json:"last_name"`
	Phone      *string    `json:"phone"`
	Balance    float64    `json:"balance"`
	Status     string     `json:"status"`
	CreatedAt  time.Time  `json:"created_at"`
	LastOnline *time.Time `json:"last_online"`

	// Follow-up specific info
	FollowUpTag     *string    `json:"follow_up_tag"`
	FollowUpStatus  *string    `json:"follow_up_status"`
	ContactedBy     *int       `json:"contacted_by"`
	ContactedByName *string    `json:"contacted_by_name"`
	LastContactAt   *time.Time `json:"last_contact_at"`

	// Joined info
	MemberGroupName *string `json:"member_group_name"`
	PlatformName    *string `json:"platform_name"`
	ChannelName     *string `json:"channel_name"`
	PartnerName     *string `json:"partner_name"`

	// Call and SMS counts
	CallCount int64 `json:"call_count"`
	SMSCount  int64 `json:"sms_count"`

	// Additional info
	Remark *string `json:"remark"`
}

// UpdateFollowUpStatusRequest for updating follow-up status
type UpdateFollowUpStatusRequest struct {
	MemberID       int    `json:"member_id" validate:"required,min=1"`
	FollowUpStatus string `json:"follow_up_status" validate:"required,oneof=contacted unreachable not_contacted"`
}

// UpdateFollowUpTagRequest for setting tag to member
type UpdateFollowUpTagRequest struct {
	MemberID    int    `json:"member_id" validate:"required,min=1"`
	FollowUpTag string `json:"follow_up_tag" validate:"required,oneof=cut_off call_later not_convenient not_interested no_money no_answer waiting_transfer"`
}

// UpdateMemberRemarkRequest for updating member remark
type UpdateMemberRemarkRequest struct {
	MemberID int     `json:"member_id" validate:"required,min=1"`
	Remark   *string `json:"remark"`
}

// TrackCustomerByCallRequest for tracking customer by phone call
type TrackCustomerByCallRequest struct {
	MemberID     int     `json:"member_id" validate:"required,min=1"`
	Notes        *string `json:"notes"`
	CallDuration *int    `json:"call_duration" validate:"omitempty,min=0"`
	CallStatus   string  `json:"call_status" validate:"required,oneof=completed missed busy no_answer"`
}

// TrackCustomerBySMSRequest for tracking customer by SMS
type TrackCustomerBySMSRequest struct {
	MemberID       int    `json:"member_id" validate:"required,min=1"`
	MessageContent string `json:"message_content" validate:"required,min=1,max=1000"`
}

// GetAllFollowUpTags returns all available follow-up tags
func GetAllFollowUpTags() []map[string]string {
	return []map[string]string{
		{"value": "all", "label": "ทั้งหมด"},
		{"value": TagCutOff, "label": "ตัดสาย"},
		{"value": TagCallLater, "label": "ติดต่อภายหลัง"},
		{"value": TagNotConvenient, "label": "ไม่สะดวกคุย"},
		{"value": TagNotInterested, "label": "ไม่สนใจโปรโมชั่น"},
		{"value": TagNoMoney, "label": "ไม่มีเงิน"},
		{"value": TagNoAnswer, "label": "โทรไม่ติด"},
		{"value": TagWaitingTransfer, "label": "รอโอน"},
	}
}

// GetAllFollowUpStatuses returns all available follow-up statuses
func GetAllFollowUpStatuses() []map[string]string {
	return []map[string]string{
		{"value": "all", "label": "ทั้งหมด"},
		{"value": StatusContacted, "label": "เคยติดต่อแล้ว"},
		{"value": StatusUnreachable, "label": "ติดต่อไม่ได้"},
		{"value": StatusNotContacted, "label": "ยังไม่ได้ติดต่อ"},
	}
}

// GetFollowUpTagLabel returns Thai label for follow-up tag
func GetFollowUpTagLabel(tag string) string {
	switch tag {
	case TagCutOff:
		return "ตัดสาย"
	case TagCallLater:
		return "ติดต่อภายหลัง"
	case TagNotConvenient:
		return "ไม่สะดวกคุย"
	case TagNotInterested:
		return "ไม่สนใจโปรโมชั่น"
	case TagNoMoney:
		return "ไม่มีเงิน"
	case TagNoAnswer:
		return "โทรไม่ติด"
	case TagWaitingTransfer:
		return "รอโอน"
	default:
		return ""
	}
}

// GetFollowUpStatusLabel returns Thai label for follow-up status
func GetFollowUpStatusLabel(status string) string {
	switch status {
	case StatusContacted:
		return "เคยติดต่อแล้ว"
	case StatusUnreachable:
		return "ติดต่อไม่ได้"
	case StatusNotContacted:
		return "ยังไม่ได้ติดต่อ"
	default:
		return "ยังไม่ได้ติดต่อ"
	}
}

// ValidateFollowUpTag validates if the provided tag is valid
func ValidateFollowUpTag(tag string) bool {
	validTags := []string{
		TagCutOff, TagCallLater, TagNotConvenient,
		TagNotInterested, TagNoMoney, TagNoAnswer, TagWaitingTransfer,
	}

	for _, validTag := range validTags {
		if tag == validTag {
			return true
		}
	}
	return false
}

// ValidateFollowUpStatus validates if the provided status is valid
func ValidateFollowUpStatus(status string) bool {
	validStatuses := []string{StatusContacted, StatusUnreachable, StatusNotContacted}

	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}
