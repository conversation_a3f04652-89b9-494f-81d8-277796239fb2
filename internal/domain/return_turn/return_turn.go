package return_turn

import (
	"time"
)

// Constants for Return Type
const (
	RETURN_TURN_TYPE_LOSER = int64(1)
)

// Constants for Cut Type
const (
	RETURN_TURN_CUT_TYPE_DAILY  = int64(1)
	RETURN_TURN_CUT_TYPE_WEEKLY = int64(2)
)

// Constants for Status
const (
	RETURN_TURN_STATUS_PENDING = int64(1) // รอคำนวณ
	RETURN_TURN_STATUS_READY   = int64(2) // พร้อมรับ
	RETURN_TURN_STATUS_TAKEN   = int64(3) // รับแล้ว
	RETURN_TURN_STATUS_EXPIRED = int64(4) // หมดอายุ
)

// Constants for Calculate Play Type
const (
	CALCULATE_PLAY_TYPE_LIVE_CASINO = int64(1)
	CALCULATE_PLAY_TYPE_SLOT        = int64(2)
	CALCULATE_PLAY_TYPE_SPORT       = int64(3)
)

// Common response structures
type SuccessResponse struct {
	Message string `json:"message"`
}

type SuccessWithPagination struct {
	List  interface{} `json:"list"`
	Total int64       `json:"total"`
}

// ReturnTurnWebListResponse for web API with pagination
type ReturnTurnWebListResponse struct {
	Success    bool                         `json:"success"`
	Data       []ReturnTurnLoserResponse    `json:"data"`
	Pagination ReturnTurnPaginationResponse `json:"pagination"`
}

// ReturnTurnAdminListResponse for admin API with pagination
type ReturnTurnAdminListResponse struct {
	Success    bool                         `json:"success"`
	Data       []ReturnTurnLoserResponse    `json:"data"`
	Pagination ReturnTurnPaginationResponse `json:"pagination"`
}

// ReturnTurnPaginationResponse for web API pagination
type ReturnTurnPaginationResponse struct {
	Page       int   `json:"page"`
	Limit      int   `json:"limit"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"totalPages"`
}

// ReturnTurnSetting main settings model
type ReturnTurnSetting struct {
	ID               int64      `json:"id" db:"id"`
	ReturnPercent    float64    `json:"returnPercent" db:"return_percent"`
	ReturnTypeID     int64      `json:"returnTypeId" db:"return_type_id"`
	CutTypeID        int64      `json:"cutTypeId" db:"cut_type_id"`
	MinLossPrice     float64    `json:"minLossPrice" db:"min_loss_price"`
	MaxReturnPrice   float64    `json:"maxReturnPrice" db:"max_return_price"`
	Detail           string     `json:"detail" db:"detail"`
	IsEnabled        bool       `json:"isEnabled" db:"is_enabled"`
	CreditExpireDays int        `json:"creditExpireDays" db:"credit_expire_days"`
	CreatedAt        time.Time  `json:"createdAt" db:"created_at"`
	UpdatedAt        *time.Time `json:"updatedAt,omitempty" db:"updated_at"`
}

// ReturnTurnSettingResponse with calculate types
type ReturnTurnSettingResponse struct {
	ID               int64      `json:"id" db:"id"`
	ReturnPercent    float64    `json:"returnPercent" db:"return_percent"`
	ReturnTypeID     int64      `json:"returnTypeId" db:"return_type_id"`
	CutTypeID        int64      `json:"cutTypeId" db:"cut_type_id"`
	MinLossPrice     float64    `json:"minLossPrice" db:"min_loss_price"`
	MaxReturnPrice   float64    `json:"maxReturnPrice" db:"max_return_price"`
	Detail           string     `json:"detail" db:"detail"`
	IsEnabled        bool       `json:"isEnabled" db:"is_enabled"`
	CreditExpireDays int        `json:"creditExpireDays" db:"credit_expire_days"`
	CalculateTypes   []int64    `json:"calculateTypes"` // [1,2,3]
	CreatedAt        time.Time  `json:"createdAt" db:"created_at"`
	UpdatedAt        *time.Time `json:"updatedAt,omitempty" db:"updated_at"`
	CacheExpiredAt   time.Time  `json:"cacheExpiredAt"`
}

// ReturnTurnSettingUpdateRequest for updating settings
type ReturnTurnSettingUpdateRequest struct {
	ReturnPercent    *float64 `json:"returnPercent"`
	ReturnTypeID     *int64   `json:"returnTypeId"`
	CutTypeID        *int64   `json:"cutTypeId"`
	MinLossPrice     *float64 `json:"minLossPrice"`
	MaxReturnPrice   *float64 `json:"maxReturnPrice"`
	Detail           *string  `json:"detail"`
	IsEnabled        *bool    `json:"isEnabled"`
	CreditExpireDays *int     `json:"creditExpireDays"`
	CalculateTypes   []int64  `json:"calculateTypes"` // [1,2,3]
	UpdatedByID      int64    `json:"-"`
}

// CalculatePlayType model
type CalculatePlayType struct {
	ID        int64     `json:"id" db:"id"`
	Name      string    `json:"name" db:"name"`
	CreatedAt time.Time `json:"createdAt" db:"created_at"`
}

// ReturnTurnCalculateType junction table
type ReturnTurnCalculateType struct {
	ID                 int64     `json:"id" db:"id"`
	PromotionSettingID int64     `json:"promotionSettingId" db:"promotion_setting_id"`
	CalculateTypeID    int64     `json:"calculateTypeId" db:"calculate_type_id"`
	CreatedAt          time.Time `json:"createdAt" db:"created_at"`
}

// ReturnTurnLoser transaction model
type ReturnTurnLoser struct {
	ID                    int64      `json:"id" db:"id"`
	MemberCode            string     `json:"memberCode" db:"member_code"`
	StatusID              int64      `json:"statusId" db:"status_id"`
	DailyKey              string     `json:"dailyKey" db:"daily_key"`
	OfDate                string     `json:"ofDate" db:"of_date"`
	TotalLossAmount       float64    `json:"totalLossAmount" db:"total_loss_amount"`
	TotalLossLiveCasino   float64    `json:"totalLossLiveCasino" db:"total_loss_live_casino"`
	TotalLossSlot         float64    `json:"totalLossSlot" db:"total_loss_slot"`
	TotalLossSport        float64    `json:"totalLossSport" db:"total_loss_sport"`
	ReturnPercent         float64    `json:"returnPercent" db:"return_percent"`
	GameDetail            string     `json:"gameDetail" db:"game_detail"`
	ReturnTypeID          int64      `json:"returnTypeId" db:"return_type_id"`
	CutTypeID             int64      `json:"cutTypeId" db:"cut_type_id"`
	MinLossPrice          float64    `json:"minLossPrice" db:"min_loss_price"`
	MaxReturnPrice        float64    `json:"maxReturnPrice" db:"max_return_price"`
	ReturnPriceLiveCasino float64    `json:"returnPriceLiveCasino" db:"return_price_live_casino"`
	TakeAtLiveCasino      *time.Time `json:"takeAtLiveCasino,omitempty" db:"take_at_live_casino"`
	TakenPriceLiveCasino  float64    `json:"takenPriceLiveCasino" db:"taken_price_live_casino"`
	ReturnPriceSlot       float64    `json:"returnPriceSlot" db:"return_price_slot"`
	TakeAtSlot            *time.Time `json:"takeAtSlot,omitempty" db:"take_at_slot"`
	TakenPriceSlot        float64    `json:"takenPriceSlot" db:"taken_price_slot"`
	ReturnPriceSport      float64    `json:"returnPriceSport" db:"return_price_sport"`
	TakenAtSport          *time.Time `json:"takenAtSport,omitempty" db:"taken_at_sport"`
	TakenPriceSport       float64    `json:"takenPriceSport" db:"taken_price_sport"`
	CalcAt                *time.Time `json:"calcAt,omitempty" db:"calc_at"`
	CreatedAt             time.Time  `json:"createdAt" db:"created_at"`
	UpdatedAt             *time.Time `json:"updatedAt,omitempty" db:"updated_at"`
}

// ReturnTurnLoserResponse with additional fields
type ReturnTurnLoserResponse struct {
	ID                    int64      `json:"id" db:"id"`
	MemberCode            string     `json:"memberCode" db:"member_code"`
	Username              string     `json:"username" db:"username"`
	Fullname              string     `json:"fullname" db:"fullname"`
	StatusID              int64      `json:"statusId" db:"status_id"`
	StatusName            string     `json:"statusName" db:"status_name"`
	DailyKey              string     `json:"dailyKey" db:"daily_key"`
	OfDate                string     `json:"ofDate" db:"of_date"`
	TotalLossAmount       float64    `json:"totalLossAmount" db:"total_loss_amount"`
	TotalLossLiveCasino   float64    `json:"totalLossLiveCasino" db:"total_loss_live_casino"`
	TotalLossSlot         float64    `json:"totalLossSlot" db:"total_loss_slot"`
	TotalLossSport        float64    `json:"totalLossSport" db:"total_loss_sport"`
	ReturnPercent         float64    `json:"returnPercent" db:"return_percent"`
	GameDetail            string     `json:"gameDetail" db:"game_detail"`
	ReturnTypeID          int64      `json:"returnTypeId" db:"return_type_id"`
	CutTypeID             int64      `json:"cutTypeId" db:"cut_type_id"`
	CutTypeName           string     `json:"cutTypeName" db:"cut_type_name"`
	MinLossPrice          float64    `json:"minLossPrice" db:"min_loss_price"`
	MaxReturnPrice        float64    `json:"maxReturnPrice" db:"max_return_price"`
	ReturnPriceLiveCasino float64    `json:"returnPriceLiveCasino" db:"return_price_live_casino"`
	TakeAtLiveCasino      *time.Time `json:"takeAtLiveCasino,omitempty" db:"take_at_live_casino"`
	TakenPriceLiveCasino  float64    `json:"takenPriceLiveCasino" db:"taken_price_live_casino"`
	ReturnPriceSlot       float64    `json:"returnPriceSlot" db:"return_price_slot"`
	TakeAtSlot            *time.Time `json:"takeAtSlot,omitempty" db:"take_at_slot"`
	TakenPriceSlot        float64    `json:"takenPriceSlot" db:"taken_price_slot"`
	ReturnPriceSport      float64    `json:"returnPriceSport" db:"return_price_sport"`
	TakenAtSport          *time.Time `json:"takenAtSport,omitempty" db:"taken_at_sport"`
	TakenPriceSport       float64    `json:"takenPriceSport" db:"taken_price_sport"`
	CreditExpireAt        string     `json:"creditExpireAt" db:"credit_expire_at"`
	LogStatus             string     `json:"logStatus"`
	CalcAt                *time.Time `json:"calcAt,omitempty" db:"calc_at"`
	CreatedAt             time.Time  `json:"createdAt" db:"created_at"`
	UpdatedAt             *time.Time `json:"updatedAt,omitempty" db:"updated_at"`
}

// CustomerPromotion for listing users with active promotions
type CustomerPromotionListRequest struct {
	FromDate      string `form:"fromDate"`
	ToDate        string `form:"toDate"`
	PromotionName string `form:"promotionName"`
	StatusID      *int64 `form:"statusId"`
	Search        string `form:"search"`
	Page          int    `form:"page" default:"1"`
	Limit         int    `form:"limit" default:"10"`
	SortCol       string `form:"sortCol"`
	SortAsc       string `form:"sortAsc"`
}

type CustomerPromotionListResponse struct {
	ID              int64      `json:"id" db:"id"`
	MemberID        int64      `json:"memberId" db:"member_id"`
	Username        string     `json:"username" db:"username"`
	Fullname        string     `json:"fullname" db:"fullname"`
	TypeID          int64      `json:"typeId" db:"type_id"`
	TypeName        string     `json:"typeName" db:"type_name"`
	StatusID        int64      `json:"statusId" db:"status_id"`
	StatusName      string     `json:"statusName" db:"status_name"`
	StartTurnAmount float64    `json:"startTurnAmount" db:"start_turn_amount"`
	TotalTurnAmount float64    `json:"totalTurnAmount" db:"total_turn_amount"`
	PromotionName   string     `json:"promotionName" db:"promotion_name"`
	BonusAmount     float64    `json:"bonusAmount" db:"bonus_amount"`
	StartTurnAt     time.Time  `json:"startTurnAt" db:"start_turn_at"`
	EndTurnAt       *time.Time `json:"endTurnAt,omitempty" db:"end_turn_at"`
	CreatedAt       time.Time  `json:"createdAt" db:"created_at"`
	UpdatedAt       *time.Time `json:"updatedAt,omitempty" db:"updated_at"`
}

type CustomerPromotionCancelRequest struct {
	StatementID int64 `json:"statementId" binding:"required"`
}

// History Report Models
type ReturnTurnHistoryUserListRequest struct {
	DateType string `form:"dateType"` // all, today, yesterday, last_week, this_month, last_month
	FromDate string `form:"fromDate"`
	ToDate   string `form:"toDate"`
	Search   string `form:"search"`
	Page     int    `form:"page" default:"1"`
	Limit    int    `form:"limit" default:"10"`
	SortCol  string `form:"sortCol"`
	SortAsc  string `form:"sortAsc"`
}

type ReturnTurnHistoryUserListResponse struct {
	ID              int64   `json:"id" db:"id"`
	MemberID        int64   `json:"memberId" db:"member_id"`
	MemberCode      string  `json:"memberCode" db:"member_code"`
	Username        string  `json:"username" db:"username"`
	Fullname        string  `json:"fullname" db:"fullname"`
	TotalLossAmount float64 `json:"totalLossAmount" db:"total_loss_amount"`
	TotalTakenPrice float64 `json:"totalTakenPrice" db:"total_taken_price"`
}

type ReturnTurnHistoryUserSummaryResponse struct {
	DateType        string  `json:"dateType"`
	FromDate        string  `json:"fromDate"`
	ToDate          string  `json:"toDate"`
	TotalLossAmount float64 `json:"totalLossAmount"`
	TotalTakenPrice float64 `json:"totalTakenPrice"`
}

type ReturnTurnHistoryListRequest struct {
	MemberID *int64 `form:"memberId"`
	FromDate string `form:"fromDate"`
	ToDate   string `form:"toDate"`
	StatusID *int64 `form:"statusId"`
	Page     int    `form:"page" default:"1"`
	Limit    int    `form:"limit" default:"10"`
}

// User Return Detail
type ReturnTurnUserDetail struct {
	MemberCode      string                    `json:"memberCode"`
	StatusID        int64                     `json:"statusId"`
	StatusName      string                    `json:"statusName"`
	ReturnPercent   float64                   `json:"returnPercent"`
	ReturnPrice     float64                   `json:"returnPrice"`
	Detail          string                    `json:"detail"`
	RelatedItemList []ReturnTurnLoserResponse `json:"relatedItemList"`
}

// Transaction List Request
type ReturnTurnTransactionListRequest struct {
	MemberID int64  `json:"memberId"`
	StatusID *int64 `form:"statusId"`
	Page     int    `form:"page" default:"1"`
	Limit    int    `form:"limit" default:"10"`
}

// Create/Update Bodies
type ReturnTurnLoserCreateBody struct {
	MemberCode            string  `json:"memberCode"`
	StatusID              int64   `json:"statusId"`
	DailyKey              string  `json:"dailyKey"`
	OfDate                string  `json:"ofDate"`
	TotalLossAmount       float64 `json:"totalLossAmount"`
	TotalLossLiveCasino   float64 `json:"totalLossLiveCasino"`
	TotalLossSlot         float64 `json:"totalLossSlot"`
	TotalLossSport        float64 `json:"totalLossSport"`
	ReturnPercent         float64 `json:"returnPercent"`
	GameDetail            string  `json:"gameDetail"`
	ReturnTypeID          int64   `json:"returnTypeId"`
	CutTypeID             int64   `json:"cutTypeId"`
	MinLossPrice          float64 `json:"minLossPrice"`
	MaxReturnPrice        float64 `json:"maxReturnPrice"`
	ReturnPriceLiveCasino float64 `json:"returnPriceLiveCasino"`
	ReturnPriceSlot       float64 `json:"returnPriceSlot"`
	ReturnPriceSport      float64 `json:"returnPriceSport"`
}

type ReturnTurnLoserCalcBody struct {
	StatusID              int64     `json:"statusId"`
	ReturnPriceLiveCasino float64   `json:"returnPriceLiveCasino"`
	ReturnPriceSlot       float64   `json:"returnPriceSlot"`
	ReturnPriceSport      float64   `json:"returnPriceSport"`
	CalcAt                time.Time `json:"calcAt"`
}

type ReturnTurnLoserUpdateBody struct {
	StatusID             int64      `json:"statusId"`
	TakeAt               *time.Time `json:"takeAt,omitempty"`
	TakenPriceLiveCasino float64    `json:"takenPriceLiveCasino"`
	TakenPriceSlot       float64    `json:"takenPriceSlot"`
	TakenPriceSport      float64    `json:"takenPriceSport"`
	UpdatedAt            time.Time  `json:"updatedAt"`
}

// Play Log Models
type PlaylogTotalAmount struct {
	MemberCode          string  `json:"memberCode" db:"member_code"`
	MemberUsername      string  `json:"memberUsername" db:"member_username"`
	TotalLossAmount     float64 `json:"totalLossAmount" db:"total_loss_amount"`
	TotalLossLiveCasino float64 `json:"totalLossLiveCasino" db:"total_loss_live_casino"`
	TotalLossSlot       float64 `json:"totalLossSlot" db:"total_loss_slot"`
	TotalLossSport      float64 `json:"totalLossSport" db:"total_loss_sport"`
}

type CronPlayLogCheckResponse struct {
	HasData bool   `json:"hasData"`
	Count   int64  `json:"count"`
	MinDate string `json:"minDate"`
	MaxDate string `json:"maxDate"`
}

// Cron Request
type CronCutByDateRequest struct {
	OfDate string `form:"ofDate" binding:"required"`
}

// User Transaction Models (for credit operations)
type UserTransactionCreateRequest struct {
	MemberID                int64   `json:"memberId"`
	UserTransactionTypeID   int64   `json:"userTransactionTypeId"`
	UserTransactionStatusID int64   `json:"userTransactionStatusId"`
	Amount                  float64 `json:"amount"`
	Detail                  string  `json:"detail"`
	CreatedByAdminID        int64   `json:"createdByAdminId"`
}

type UserTransactionCreateResponse struct {
	ID            int64     `json:"id"`
	MemberID      int64     `json:"memberId"`
	Amount        float64   `json:"amount"`
	BalanceBefore float64   `json:"balanceBefore"`
	BalanceAfter  float64   `json:"balanceAfter"`
	CreatedAt     time.Time `json:"createdAt"`
}

// Status and Type Reference Models
type ReturnTurnLoserStatus struct {
	ID   int64  `json:"id" db:"id"`
	Name string `json:"name" db:"name"`
}

type ReturnTurnCutType struct {
	ID   int64  `json:"id" db:"id"`
	Name string `json:"name" db:"name"`
}

type ReturnTurnLoserType struct {
	ID   int64  `json:"id" db:"id"`
	Name string `json:"name" db:"name"`
}
