package channel

import (
	"time"

	"blacking-api/internal/domain/platform"
	"blacking-api/pkg/errors"
)

// Status represents the channel status
type Status string

const (
	StatusActive   Status = "active"
	StatusInactive Status = "inactive"
)

// Channel represents a marketing channel
type Channel struct {
	ID          int       `json:"id"`
	Name        string    `json:"name"`
	PlatformID  string    `json:"platform_id"`
	Description *string   `json:"description"`
	Status      Status    `json:"status"`
	CreatedBy   string    `json:"created_by"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// CreateChannelRequest for creating channel
type CreateChannelRequest struct {
	Name        string  `json:"name" validate:"required,min=1,max=100"`
	PlatformID  string  `json:"platform_id" validate:"required"`
	Description *string `json:"description" validate:"omitempty,max=500"`
}

// UpdateChannelRequest for updating channel
type UpdateChannelRequest struct {
	Name        string  `json:"name" validate:"required,min=1,max=100"`
	PlatformID  string  `json:"platform_id" validate:"required"`
	Description *string `json:"description" validate:"omitempty,max=500"`
}

// ChannelResponse represents channel API response
type ChannelResponse struct {
	ID          int                        `json:"id"`
	Name        string                     `json:"name"`
	PlatformID  string                     `json:"platform_id"`
	Platform    *platform.PlatformResponse `json:"platform,omitempty"`
	Description *string                    `json:"description"`
	Status      Status                     `json:"status"`
	CreatedBy   string                     `json:"created_by"`
	CreatedAt   time.Time                  `json:"created_at"`
	UpdatedAt   time.Time                  `json:"updated_at"`
}

// ChannelDropdownResponse represents channel dropdown API response
type ChannelDropdownResponse struct {
	ID           int    `json:"id"`
	Name         string `json:"name"`
	PlatformID   string `json:"platform_id"`
	PlatformName string `json:"platform_name"`
}

// NewChannel creates a new channel
func NewChannel(req CreateChannelRequest, createdBy string) (*Channel, error) {
	if err := validateChannelRequest(req); err != nil {
		return nil, err
	}

	now := time.Now()
	channel := &Channel{
		// ID will be generated by database
		Name:        req.Name,
		PlatformID:  req.PlatformID,
		Description: req.Description,
		Status:      StatusActive,
		CreatedBy:   createdBy,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	return channel, nil
}

// Update updates channel information
func (c *Channel) Update(req UpdateChannelRequest) error {
	if err := validateUpdateChannelRequest(req); err != nil {
		return err
	}

	c.Name = req.Name
	c.PlatformID = req.PlatformID
	c.Description = req.Description
	c.UpdatedAt = time.Now()

	return nil
}

// ToResponse converts channel to response format
func (c *Channel) ToResponse() ChannelResponse {
	response := ChannelResponse{
		ID:          c.ID,
		Name:        c.Name,
		PlatformID:  c.PlatformID,
		Description: c.Description,
		Status:      c.Status,
		CreatedBy:   c.CreatedBy,
		CreatedAt:   c.CreatedAt,
		UpdatedAt:   c.UpdatedAt,
	}

	// Get platform information
	if platformData, err := platform.GetPlatformByID(c.PlatformID); err == nil {
		platformResponse := platformData.ToResponse()
		response.Platform = &platformResponse
	}

	return response
}

// ToDropdownResponse converts channel to dropdown response format
func (c *Channel) ToDropdownResponse() ChannelDropdownResponse {
	response := ChannelDropdownResponse{
		ID:         c.ID,
		Name:       c.Name,
		PlatformID: c.PlatformID,
	}

	// Get platform name
	if platformData, err := platform.GetPlatformByID(c.PlatformID); err == nil {
		response.PlatformName = platformData.Name
	}

	return response
}

// Activate activates the channel
func (c *Channel) Activate() {
	c.Status = StatusActive
	c.UpdatedAt = time.Now()
}

// Deactivate deactivates the channel
func (c *Channel) Deactivate() {
	c.Status = StatusInactive
	c.UpdatedAt = time.Now()
}

// validateChannelRequest validates channel creation request
func validateChannelRequest(req CreateChannelRequest) error {
	if req.Name == "" {
		return errors.NewValidationError("name is required")
	}
	if req.PlatformID == "" {
		return errors.NewValidationError("platform_id is required")
	}

	// Validate platform exists
	if _, err := platform.GetPlatformByID(req.PlatformID); err != nil {
		return errors.NewValidationError("invalid platform_id")
	}

	return nil
}

// validateUpdateChannelRequest validates channel update request
func validateUpdateChannelRequest(req UpdateChannelRequest) error {
	if req.Name == "" {
		return errors.NewValidationError("name is required")
	}
	if req.PlatformID == "" {
		return errors.NewValidationError("platform_id is required")
	}

	// Validate platform exists
	if _, err := platform.GetPlatformByID(req.PlatformID); err != nil {
		return errors.NewValidationError("invalid platform_id")
	}

	return nil
}
