package referral

import (
	"time"

	"github.com/google/uuid"
)

// ReferralTransaction represents a referral transaction record
type ReferralTransaction struct {
	ID                int        `json:"id" db:"id"`
	MemberID          int        `json:"member_id" db:"member_id"`
	Type              string     `json:"type" db:"type"`                             // Transaction type: commission, withdraw
	Amount            float64    `json:"amount" db:"amount"`                         // Transaction amount (positive for income, negative for withdraw)
	DownlineMemberID  *int       `json:"downline_member_id" db:"downline_member_id"` // NULL for withdraw transactions
	BalanceBefore     float64    `json:"balance_before" db:"balance_before"`         // Balance before this transaction
	BalanceAfter      float64    `json:"balance_after" db:"balance_after"`           // Balance after this transaction
	ReferralStartDate *time.Time `json:"referral_start_date" db:"referral_start_date"`
	ReferralEndDate   *time.Time `json:"referral_end_date" db:"referral_end_date"`
	CreatedByAdmin    *int       `json:"created_by_admin" db:"created_by_admin"`   // NULL if not created by admin
	CreatedByMember   *int       `json:"created_by_member" db:"created_by_member"` // NULL if not created by member
	Status            string     `json:"status" db:"status"`
	Remark            *string    `json:"remark" db:"remark"` // Optional remark for the transaction
	CreatedAt         time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at" db:"updated_at"`
}

// ReferralTransactionStatus constants
const (
	StatusPending = "pending"
	StatusSuccess = "success"
	StatusCancel  = "cancel"
)

// ReferralTransactionType constants
const (
	TypeCommission = "commission" // Income from referral commission
	TypeWithdraw   = "withdraw"   // Withdraw commission balance
	TypeAdjustment = "adjustment" // Admin manual adjustment
	TypeBonus      = "bonus"      // Special bonus/promotion
)

// Helper functions to identify creator type
func IsCreatedBySystem(rt *ReferralTransaction) bool {
	return rt.CreatedByAdmin == nil && rt.CreatedByMember == nil
}

func IsCreatedByAdmin(rt *ReferralTransaction) bool {
	return rt.CreatedByAdmin != nil
}

func IsCreatedByMember(rt *ReferralTransaction) bool {
	return rt.CreatedByMember != nil
}

// Get creator type as string for display
func GetCreatorType(rt *ReferralTransaction) string {
	if IsCreatedByAdmin(rt) {
		return "admin"
	} else if IsCreatedByMember(rt) {
		return "member"
	} else if IsCreatedBySystem(rt) {
		return "system"
	}
	return "unknown"
}

// Get creator ID (returns the actual admin or member ID)
func GetCreatorID(rt *ReferralTransaction) int {
	if IsCreatedByAdmin(rt) {
		return *rt.CreatedByAdmin
	} else if IsCreatedByMember(rt) {
		return *rt.CreatedByMember
	}
	return 0 // System has no specific ID
}

// Helper functions to create transactions
func NewCommissionTransaction(memberID int, downlineMemberID *int, amount, balanceBefore, balanceAfter float64, startDate, endDate time.Time) *ReferralTransaction {
	return &ReferralTransaction{
		MemberID:          memberID,
		Type:              TypeCommission,
		Amount:            amount,
		DownlineMemberID:  downlineMemberID, // Required for commission transactions
		BalanceBefore:     balanceBefore,
		BalanceAfter:      balanceAfter,
		ReferralStartDate: &startDate,
		ReferralEndDate:   &endDate,
		CreatedByAdmin:    nil, // System created
		CreatedByMember:   nil, // System created
		Status:            StatusSuccess,
		Remark:            nil, // No remark by default
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}
}

func NewWithdrawTransaction(memberID int, amount, balanceBefore, balanceAfter float64) *ReferralTransaction {
	now := time.Now()
	return &ReferralTransaction{
		MemberID:          memberID,
		Type:              TypeWithdraw,
		Amount:            -amount, // Negative for withdraw
		DownlineMemberID:  nil,     // NULL for withdraw transactions
		BalanceBefore:     balanceBefore,
		BalanceAfter:      balanceAfter,
		ReferralStartDate: &now, // Use current time for withdraw
		ReferralEndDate:   &now, // Use current time for withdraw
		CreatedByAdmin:    nil,  // System created
		CreatedByMember:   nil,  // System created
		Status:            StatusSuccess,
		Remark:            nil, // No remark by default
		CreatedAt:         now,
		UpdatedAt:         now,
	}
}

func NewCommissionTransactionWithRemark(memberID int, downlineMemberID *int, amount, balanceBefore, balanceAfter float64, startDate, endDate time.Time, remark *string) *ReferralTransaction {
	return &ReferralTransaction{
		MemberID:          memberID,
		Type:              TypeCommission,
		Amount:            amount,
		DownlineMemberID:  downlineMemberID, // Required for commission transactions
		BalanceBefore:     balanceBefore,
		BalanceAfter:      balanceAfter,
		ReferralStartDate: &startDate,
		ReferralEndDate:   &endDate,
		CreatedByAdmin:    nil, // System created
		CreatedByMember:   nil, // System created
		Status:            StatusSuccess,
		Remark:            remark,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}
}

func NewWithdrawTransactionWithRemark(memberID int, amount, balanceBefore, balanceAfter float64, remark *string) *ReferralTransaction {
	now := time.Now()
	return &ReferralTransaction{
		MemberID:          memberID,
		Type:              TypeWithdraw,
		Amount:            -amount, // Negative for withdraw
		DownlineMemberID:  nil,     // NULL for withdraw transactions
		BalanceBefore:     balanceBefore,
		BalanceAfter:      balanceAfter,
		ReferralStartDate: nil, // NULL for withdraw transactions
		ReferralEndDate:   nil, // NULL for withdraw transactions
		CreatedByAdmin:    nil, // System created
		CreatedByMember:   nil, // System created
		Status:            StatusSuccess,
		Remark:            remark,
		CreatedAt:         now,
		UpdatedAt:         now,
	}
}

func NewAdminCommissionTransaction(adminID, memberID int, downlineMemberID *int, amount, balanceBefore, balanceAfter float64, startDate, endDate time.Time) *ReferralTransaction {
	return &ReferralTransaction{
		MemberID:          memberID,
		Type:              TypeCommission,
		Amount:            amount,
		DownlineMemberID:  downlineMemberID, // Required for commission transactions
		BalanceBefore:     balanceBefore,
		BalanceAfter:      balanceAfter,
		ReferralStartDate: &startDate,
		ReferralEndDate:   &endDate,
		CreatedByAdmin:    &adminID, // Created by admin
		CreatedByMember:   nil,
		Status:            StatusPending,
		Remark:            nil, // No remark by default
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}
}

func NewAdminAdjustmentTransaction(adminID, memberID int, amount, balanceBefore, balanceAfter float64) *ReferralTransaction {
	now := time.Now()
	return &ReferralTransaction{
		MemberID:          memberID,
		Type:              TypeAdjustment,
		Amount:            amount, // Can be positive or negative
		DownlineMemberID:  nil,    // NULL for adjustment transactions
		BalanceBefore:     balanceBefore,
		BalanceAfter:      balanceAfter,
		ReferralStartDate: nil,      // NULL for adjustment transactions
		ReferralEndDate:   nil,      // NULL for adjustment transactions
		CreatedByAdmin:    &adminID, // Created by admin
		CreatedByMember:   nil,
		Status:            StatusPending,
		Remark:            nil, // No remark by default
		CreatedAt:         now,
		UpdatedAt:         now,
	}
}

func NewAdminCommissionTransactionWithRemark(adminID, memberID int, downlineMemberID *int, amount, balanceBefore, balanceAfter float64, startDate, endDate time.Time, remark *string) *ReferralTransaction {
	return &ReferralTransaction{
		MemberID:          memberID,
		Type:              TypeCommission,
		Amount:            amount,
		DownlineMemberID:  downlineMemberID, // Required for commission transactions
		BalanceBefore:     balanceBefore,
		BalanceAfter:      balanceAfter,
		ReferralStartDate: &startDate,
		ReferralEndDate:   &endDate,
		CreatedByAdmin:    &adminID, // Created by admin
		CreatedByMember:   nil,
		Status:            StatusPending,
		Remark:            remark,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}
}

func NewAdminAdjustmentTransactionWithRemark(adminID, memberID int, amount, balanceBefore, balanceAfter float64, remark *string) *ReferralTransaction {
	now := time.Now()
	return &ReferralTransaction{
		MemberID:          memberID,
		Type:              TypeAdjustment,
		Amount:            amount, // Can be positive or negative
		DownlineMemberID:  nil,    // NULL for adjustment transactions
		BalanceBefore:     balanceBefore,
		BalanceAfter:      balanceAfter,
		ReferralStartDate: nil,      // NULL for adjustment transactions
		ReferralEndDate:   nil,      // NULL for adjustment transactions
		CreatedByAdmin:    &adminID, // Created by admin
		CreatedByMember:   nil,
		Status:            StatusPending,
		Remark:            remark,
		CreatedAt:         now,
		UpdatedAt:         now,
	}
}

// ValidateStatus validates if the status is valid
func ValidateStatus(status string) bool {
	validStatuses := []string{StatusPending, StatusSuccess, StatusCancel}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// ValidateType validates if the transaction type is valid
func ValidateType(transactionType string) bool {
	validTypes := []string{TypeCommission, TypeWithdraw, TypeAdjustment, TypeBonus}
	for _, validType := range validTypes {
		if transactionType == validType {
			return true
		}
	}
	return false
}

// SetRemark sets the remark for the transaction
func (rt *ReferralTransaction) SetRemark(remark string) {
	if remark == "" {
		rt.Remark = nil
	} else {
		rt.Remark = &remark
	}
	rt.UpdatedAt = time.Now()
}

// GetRemark returns the remark string or empty string if nil
func (rt *ReferralTransaction) GetRemark() string {
	if rt.Remark == nil {
		return ""
	}
	return *rt.Remark
}

// HasRemark returns true if the transaction has a remark
func (rt *ReferralTransaction) HasRemark() bool {
	return rt.Remark != nil && *rt.Remark != ""
}

// IsCommission returns true if the transaction is a commission type
func (rt *ReferralTransaction) IsCommission() bool {
	return rt.Type == TypeCommission
}

// IsWithdraw returns true if the transaction is a withdraw type
func (rt *ReferralTransaction) IsWithdraw() bool {
	return rt.Type == TypeWithdraw
}

// IsAdjustment returns true if the transaction is an adjustment type
func (rt *ReferralTransaction) IsAdjustment() bool {
	return rt.Type == TypeAdjustment
}

// IsBonus returns true if the transaction is a bonus type
func (rt *ReferralTransaction) IsBonus() bool {
	return rt.Type == TypeBonus
}

// IsIncome returns true if the transaction adds to the balance (positive amount)
func (rt *ReferralTransaction) IsIncome() bool {
	return rt.Amount > 0
}

// IsExpense returns true if the transaction reduces the balance (negative amount)
func (rt *ReferralTransaction) IsExpense() bool {
	return rt.Amount < 0
}

// GetAbsoluteAmount returns the absolute value of the amount
func (rt *ReferralTransaction) GetAbsoluteAmount() float64 {
	if rt.Amount < 0 {
		return -rt.Amount
	}
	return rt.Amount
}

// SetType sets the transaction type and updates the timestamp
func (rt *ReferralTransaction) SetType(transactionType string) {
	if ValidateType(transactionType) {
		rt.Type = transactionType
		rt.UpdatedAt = time.Now()
	}
}

type Register struct {
	ID                 uuid.UUID `json:"id" db:"id"`
	ReferUserID        uuid.UUID `json:"refer_user_id" db:"refer_user_id"`
	RegisterReferCode  string    `json:"register_refer_code" db:"register_refer_code"`
	Commission         float64   `json:"commission" db:"commission"`
	MemberID           uuid.UUID `json:"member_id" db:"member_id"`
	ReferDownLineCount int       `json:"refer_down_line_count" db:"refer_down_line_count"`
	CreatedAt          time.Time `json:"created_at" db:"created_at"`
	UpdatedAt          time.Time `json:"updated_at" db:"updated_at"`
}

// Response DTOs for API endpoints

type OverviewResponse struct {
	// Required fields based on new requirements
	TotalReferrals             int     `json:"total_referrals"`               // member -> downline_count
	ReferralViewCount          int     `json:"referral_view_count"`           // member -> referral_view_count
	CommissionBalance          float64 `json:"commission_balance"`            // member -> commission_balance
	CommissionGrowthPercentage float64 `json:"commission_growth_percentage"`  // % growth month over month from referral_transactions
	TodayDownlineRegisterCount int     `json:"today_downline_register_count"` // today's new registrations
}

type ReferralMember struct {
	MemberID      int        `json:"member_id"`
	Username      string     `json:"username"`
	Phone         string     `json:"phone"`
	DownlineCount int        `json:"downline_count"`
	JoinedAt      time.Time  `json:"joined_at"`
	Status        string     `json:"status"`
}

// New response structure for referrals/members API
type ReferralMembersResponse struct {
	Downlines            []ReferralMember     `json:"downlines"`
	HourlyChart          *HourlyChartData     `json:"hourly_chart"`
	TodayCommission      float64              `json:"today_commission"`
	GameCategoryStats    []GameCategoryStat   `json:"game_category_stats"`
	GrowthPercent        float64              `json:"growth_percent"`
	Pagination           *PaginationMeta      `json:"pagination,omitempty"`
}

// PaginationMeta represents pagination metadata
type PaginationMeta struct {
	Page       int  `json:"page"`
	Limit      int  `json:"limit"`
	TotalPages int  `json:"total_pages"`
	TotalCount int  `json:"total_count"`
	HasNext    bool `json:"has_next"`
	HasPrev    bool `json:"has_prev"`
}

// ReferralMembersRequest represents request parameters for referrals/members API
type ReferralMembersRequest struct {
	Page  int `json:"page" form:"page"`     // page number (1-based)
	Limit int `json:"limit" form:"limit"`   // records per page
}

// Hourly chart data for Chart.js
type HourlyChartData struct {
	Labels   []string  `json:"labels"`   // ["00:00", "01:00", "02:00", ...]
	Datasets []Dataset `json:"datasets"` // Chart.js dataset format
}

type Dataset struct {
	Label           string    `json:"label"`
	Data            []float64 `json:"data"`
	BackgroundColor string    `json:"backgroundColor"`
	BorderColor     string    `json:"borderColor"`
}

// Game category statistics
type GameCategoryStat struct {
	Category      string  `json:"category"`       // "Slot", "Casino", etc.
	TotalWinloss  float64 `json:"total_winloss"`  // sum(upline_winloss) for this category
	Commission    float64 `json:"commission"`     // total_winloss * commission_percent
}

type IncomeResponse struct {
	TotalIncome                float64             `json:"total_income"`
	CommissionGrowthPercentage float64             `json:"commission_growth_percentage"`
	RecentTransactions         []IncomeTransaction `json:"recent_transactions"`
	Pagination                 *PaginationMeta     `json:"pagination,omitempty"`
}

// IncomeRequest represents request parameters for income API
type IncomeRequest struct {
	Months int `json:"months" form:"months"` // Number of months for history (default: 12, max: 24)
	Page   int `json:"page" form:"page"`     // page number (1-based) for recent_transactions
	Limit  int `json:"limit" form:"limit"`   // records per page for recent_transactions
}

type MonthlyIncome struct {
	Month  string  `json:"month"`
	Year   int     `json:"year"`
	Amount float64 `json:"amount"`
	Count  int     `json:"count"`
}

type IncomeTransaction struct {
	ID              int       `json:"id"`
	MemberUsername  string    `json:"member_username"`
	ReferCode       string    `json:"refer_code"`
	Type            string    `json:"type"`   // Transaction type: commission, withdraw, adjustment, bonus
	Amount          float64   `json:"amount"` // Transaction amount (positive for income, negative for expense)
	GameCategory    *string   `json:"game_category,omitempty"` // Game category for commission transactions
	TransactionDate time.Time `json:"transaction_date"`
	Status          string    `json:"status"`
	Remark          *string   `json:"remark,omitempty"`  // Optional remark
	CreatedByAdmin  *int      `json:"created_by_admin"`  // NULL if not created by admin
	CreatedByMember *int      `json:"created_by_member"` // NULL if not created by member
	CreatorType     string    `json:"creator_type"`      // "system", "admin", "member"
}

type TutorialFAQResponse struct {
	FAQs                          []*FAQ `json:"faqs"`
	NetworkTutorialImage          string `json:"network_tutorial_image"`
	NetworkTutorialText           string `json:"network_tutorial_text"`
	NetworkTutorialMakeMoneyImage string `json:"network_tutorial_make_money_image"`
}

// PendingCommissionResponse represents pending commission data
type PendingCommissionResponse struct {
	PendingCommissions        []PendingCommissionTransaction `json:"pending_commissions"`
	CommissionPercentByCategory map[string]float64           `json:"commission_percent_by_category"`
	TotalAmount               float64                        `json:"total_amount"`
	TotalByCategory           map[string]float64             `json:"total_by_category"`
	Pagination                *PaginationMeta                `json:"pagination,omitempty"`
}

// PendingCommissionTransaction represents a pending commission transaction
type PendingCommissionTransaction struct {
	ID                int       `json:"id"`
	MemberID          int       `json:"member_id"`
	MemberUsername    string    `json:"member_username"`
	DownlineMemberID  *int      `json:"downline_member_id"`
	DownlineUsername  *string   `json:"downline_username"`
	Type              string    `json:"type"`
	Amount            float64   `json:"amount"`
	GameCategory      *string   `json:"game_category"`
	ReferralStartDate *time.Time `json:"referral_start_date"`
	ReferralEndDate   *time.Time `json:"referral_end_date"`
	Status            string    `json:"status"`
	Remark            *string   `json:"remark"`
	CreatedAt         time.Time `json:"created_at"`
}

// ApproveCommissionRequest represents request to approve commission
type ApproveCommissionRequest struct {
}

// ApproveCommissionResponse represents response after approving commission
type ApproveCommissionResponse struct {
	ApprovedCount     int     `json:"approved_count"`
	TotalAmount       float64 `json:"total_amount"`
	UpdatedMemberIDs  []int   `json:"updated_member_ids"`
}

// Commission Withdraw Request/Response
type WithdrawCommissionRequest struct {
	Amount float64 `json:"amount" validate:"required,gt=0"`
}

type WithdrawCommissionResponse struct {
	TransactionID  int     `json:"transaction_id"`
	MemberID       int     `json:"member_id"`
	WithdrawAmount float64 `json:"withdraw_amount"`
	BalanceBefore  float64 `json:"balance_before"`
	BalanceAfter   float64 `json:"balance_after"`
	Status         string  `json:"status"`
	Remark         string  `json:"remark"`
	Message        string  `json:"message"`
	CreatedAt      string  `json:"created_at"`
}

type FAQ struct {
	ID       int     `json:"id"`
	Position *int    `json:"position"`
	Title    *string `json:"title"`
	Answer   *string `json:"answer"`
	Status   string  `json:"status"`
}

// External API Integration - Winloss Data Structures
type WinlossDataItem struct {
	ActionType      string    `json:"action_type"`
	AmountDeposit   float64   `json:"amount_deposit"`
	AmountWithdraw  float64   `json:"amount_withdraw"`
	BalanceAfter    float64   `json:"balance_after"`
	BalanceBefore   float64   `json:"balance_before"`
	BetAmount       float64   `json:"bet_amount"`
	BetWinloss      float64   `json:"bet_winloss"`
	Created         string    `json:"created"`     // "20250824"
	CreatedAt       time.Time `json:"created_at"`  // "2025-08-24T12:03:33.93Z"
	CreatedISO      time.Time `json:"created_iso"` // "2025-08-24T12:03:33.924Z"
	GameCategory    string    `json:"game_category"`
	GameName        string    `json:"game_name"`
	PlayDate        time.Time `json:"play_date"`
	ProductID       string    `json:"product_id"`
	Provider        string    `json:"provider"`
	ProviderWinloss float64   `json:"provider_winloss"`
	RoundID         string    `json:"round_id"`
	TxnID           string    `json:"txn_id"`
	Upline          string    `json:"upline"`
	UplineWinloss   float64   `json:"upline_winloss"` // This is the commission amount!
	Username        string    `json:"username"`
}

type WinlossResponse struct {
	Data struct {
		Data []WinlossDataItem `json:"data"`
		Meta struct {
			Limit     int `json:"limit"`
			Page      int `json:"page"`
			Total     int `json:"total"`
			TotalPage int `json:"total_page"`
		} `json:"meta"`
		Request struct {
			Upline       string  `json:"upline"`
			GameProvider *string `json:"game_provider"`
			Username     string  `json:"username"`
			StartTime    string  `json:"start_time"`
			EndTime      string  `json:"end_time"`
			Limit        int     `json:"limit"`
			Page         int     `json:"page"`
		} `json:"request"`
		Sum struct {
			BetAmount       float64 `json:"bet_amount"`
			BetCount        int     `json:"bet_count"`
			BetWinloss      float64 `json:"bet_winloss"`
			ProviderWinloss float64 `json:"provider_winloss"`
			UplineWinloss   float64 `json:"upline_winloss"` // Total commission for the period
		} `json:"sum"`
	} `json:"data"`
	Message string `json:"message"`
	Status  string `json:"status"`
}

// Graph Data Structures
type GraphData struct {
	Title    string           `json:"title"`
	Dataset1 []GraphDataPoint `json:"dataset1"`
}

type GraphDataPoint struct {
	Date             string  `json:"date"`              // "2025-08-24"
	CommissionEarned float64 `json:"commission_earned"` // upline_winloss * commission_percent
	BetCount         int     `json:"bet_count"`
	TotalBetAmount   float64 `json:"total_bet_amount"`
}

// Game Play Summary Structures
type GamePlaySummary struct {
	TotalBetAmount   float64               `json:"total_bet_amount"`  // รวมยอดเล่นทั้งหมด
	TotalWinLoss     float64               `json:"total_win_loss"`    // รวม win/loss ทั้งหมด
	TotalBetCount    int                   `json:"total_bet_count"`   // จำนวนเกมทั้งหมด
	GameCategories   []GameCategorySummary `json:"game_categories"`   // สรุปตาม category
	TopGames         []GameSummary         `json:"top_games"`         // เกมที่เล่นมากสุด
	PlayingFrequency map[string]int        `json:"playing_frequency"` // ความถี่การเล่นตามวัน
}

type GameCategorySummary struct {
	Category         string  `json:"category"`          // "Slot", "Casino", etc.
	BetAmount        float64 `json:"bet_amount"`        // ยอดเล่นใน category นี้
	WinLoss          float64 `json:"win_loss"`          // win/loss ใน category นี้
	BetCount         int     `json:"bet_count"`         // จำนวนเกมใน category นี้
	WinRate          float64 `json:"win_rate"`          // เปอร์เซ็นต์การชนะ
	CommissionEarned float64 `json:"commission_earned"` // commission ที่ได้จาก category นี้
}

type GameSummary struct {
	GameName   string  `json:"game_name"`   // "Win Win Won"
	Provider   string  `json:"provider"`    // "AMB"
	Category   string  `json:"category"`    // "Slot"
	BetAmount  float64 `json:"bet_amount"`  // ยอดเล่นใน game นี้
	WinLoss    float64 `json:"win_loss"`    // win/loss ใน game นี้
	BetCount   int     `json:"bet_count"`   // จำนวนครั้งที่เล่น
	LastPlayed string  `json:"last_played"` // วันที่เล่นล่าสุด
}

// Request parameters for Members API
type MembersWithGraphRequest struct {
	Limit           int     `json:"limit" form:"limit"`
	Offset          int     `json:"offset" form:"offset"`
	IncludeGraph    bool    `json:"include_graph" form:"include_graph"`         // Whether to include graph data
	IncludeGamePlay bool    `json:"include_game_play" form:"include_game_play"` // Whether to include game play summary
	StartDate       *string `json:"start_date" form:"start_date"`               // For data filtering
	EndDate         *string `json:"end_date" form:"end_date"`                   // For data filtering
}

// Repository interfaces
type RegisterRepository interface {
	GetOverview(userID uuid.UUID) (*OverviewResponse, error)
	GetReferralMembers(userID uuid.UUID, req MembersWithGraphRequest) ([]ReferralMember, int, error) // Updated signature
	GetIncomeData(userID uuid.UUID, months int) (*IncomeResponse, error)
	GetByUserID(userID uuid.UUID) ([]Register, error)
	Create(register *Register) error
	GetByMemberID(memberID uuid.UUID) (*Register, error)

	// New methods for additional data
	GetMemberCommissionData(memberID int) (commission_balance, downline_count, referral_view_count float64, err error) // Get cached data from members table
	GetTodayDownlineRegistrations(memberID int) (int, error)                                                           // Count today's registrations
}

// External API interface for winloss data
type ExternalWinlossRepository interface {
	GetWinlossData(upline string, startDate, endDate time.Time, page, limit int) (*WinlossResponse, error)
}

type FAQRepository interface {
	GetActiveFAQsByCategory(category string) ([]FAQ, error)
}
