package statement_type

import (
	"time"
)

// Statement type constants
const (
	TRANSFER_IN  = "TRANSFER_IN"
	TRANSFER_OUT = "TRANSFER_OUT"
)

type StatementType struct {
	ID        int64      `json:"id" db:"id"`
	Name      string     `json:"name" db:"name"`
	LabelTH   *string    `json:"label_th,omitempty" db:"label_th"`
	LabelEN   *string    `json:"label_en,omitempty" db:"label_en"`
	CreatedAt time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at,omitempty" db:"deleted_at"`
}

type CreateStatementTypeRequest struct {
	Name    string  `json:"name" validate:"required,max=255"`
	LabelTH *string `json:"label_th,omitempty" validate:"omitempty,max=255"`
	LabelEN *string `json:"label_en,omitempty" validate:"omitempty,max=255"`
}

type UpdateStatementTypeRequest struct {
	Name    *string `json:"name,omitempty" validate:"omitempty,max=255"`
	LabelTH *string `json:"label_th,omitempty" validate:"omitempty,max=255"`
	LabelEN *string `json:"label_en,omitempty" validate:"omitempty,max=255"`
}

type StatementTypeFilter struct {
	Name           *string `json:"name,omitempty"`
	IncludeDeleted bool    `json:"include_deleted,omitempty"`
}

type StatementTypeListResponse struct {
	Data       []StatementType `json:"data"`
	TotalCount int64           `json:"total_count"`
	Page       int             `json:"page"`
	PerPage    int             `json:"per_page"`
}
