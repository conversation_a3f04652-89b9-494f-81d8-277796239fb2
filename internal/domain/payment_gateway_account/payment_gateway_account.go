package payment_gateway_account

import (
	"blacking-api/internal/domain/request"
	"time"
)

type PaymentGatewayAccount struct {
	ID                                 int64     `json:"id" db:"id"`
	Account<PERSON>ame                        string    `json:"account_name" db:"account_name"`
	Code                               string    `json:"code" db:"code"`
	Provider                           string    `json:"provider" db:"provider"`
	MerchantCode                       string    `json:"merchant_code" db:"merchant_code"`
	SecretKey                          string    `json:"secret_key" db:"secret_key"`
	SecretKeyTwo                       string    `json:"secret_key_two" db:"secret_key_two"`
	APIKey                             string    `json:"api_key" db:"api_key"`
	BaseURL                            string    `json:"base_url" db:"base_url"`
	FirstUsername                      string    `json:"first_username" db:"first_username"`
	SecondUsername                     string    `json:"second_username" db:"second_username"`
	FirstPassword                      string    `json:"first_password" db:"first_password"`
	SecondPassword                     string    `json:"second_password" db:"second_password"`
	MinimumWithdraw                    float64   `json:"minimum_withdraw" db:"minimum_withdraw"`
	MaximumWithdraw                    float64   `json:"maximum_withdraw" db:"maximum_withdraw"`
	WithdrawSplit                      bool      `json:"withdraw_split" db:"withdraw_splitting"`
	MaximumWithdrawPerTransaction      float64   `json:"maximum_withdraw_per_transaction" db:"maximum_withdraw_per_transaction"`
	MaximumSplitWithdrawPerTransaction float64   `json:"maximum_split_withdraw_per_transaction" db:"maximum_split_withdraw_per_transaction"`
	TimeoutSeconds                     int       `json:"timeout_seconds" db:"timeout_seconds"`
	MaxRetries                         int       `json:"max_retries" db:"max_retries"`
	RetryDelaySeconds                  int       `json:"retry_delay_seconds" db:"retry_delay_seconds"`
	EnableRequestLog                   bool      `json:"enable_request_log" db:"enable_request_log"`
	LogResponseBody                    bool      `json:"log_response_body" db:"log_response_body"`
	EnableDebug                        bool      `json:"enable_debug" db:"enable_debug"`
	IsDeposit                          bool      `json:"is_deposit" db:"is_deposit"`
	IsWithdraw                         bool      `json:"is_withdraw" db:"is_withdraw"`
	IsTransfer                         bool      `json:"is_transfer" db:"is_transfer"`
	Active                             bool      `json:"active" db:"active"`
	Inactive                           bool      `json:"inactive" db:"inactive"`
	CreatedAt                          time.Time `json:"created_at" db:"created_at"`
	UpdatedAt                          time.Time `json:"updated_at" db:"updated_at"`
}

type PaymentGatewayAccountRequest struct {
	AccountName                        string  `json:"accountName" validate:"required,min=1,max=255"`
	Code                               string  `json:"code" validate:"required,min=1,max=255"`
	Provider                           string  `json:"provider" validate:"required,min=1,max=255"`
	MerchantCode                       string  `json:"merchantCode" validate:"required,min=1,max=255"`
	SecretKey                          string  `json:"secretKey" validate:"required,min=1,max=255"`
	SecretKeyTwo                       string  `json:"secretKeyTwo"`
	APIKey                             string  `json:"apiKey"`
	BaseURL                            string  `json:"baseUrl" validate:"required,url"`
	FirstUsername                      string  `json:"firstUsername"`
	SecondUsername                     string  `json:"secondUsername"`
	FirstPassword                      string  `json:"firstPassword"`
	SecondPassword                     string  `json:"secondPassword"`
	MinimumWithdraw                    float64 `json:"minimumWithdraw" validate:"required"`
	MaximumWithdraw                    float64 `json:"maximumWithdraw" validate:"required"`
	WithdrawSplit                      bool    `json:"withdrawSplit" default:"false"`
	MaximumWithdrawPerTransaction      float64 `json:"maximumWithdrawPerTransaction" validate:"required"`
	MaximumSplitWithdrawPerTransaction float64 `json:"maximumSplitWithdrawPerTransaction" validate:"required"`
	TimeoutSeconds                     int     `json:"timeoutSeconds" default:"30" validate:"min=5,max=300"`
	MaxRetries                         int     `json:"maxRetries" default:"3" validate:"min=0,max=10"`
	RetryDelaySeconds                  int     `json:"retryDelaySeconds" default:"1" validate:"min=1,max=60"`
	EnableRequestLog                   bool    `json:"enableRequestLog" default:"true"`
	LogResponseBody                    bool    `json:"logResponseBody" default:"false"`
	EnableDebug                        bool    `json:"enableDebug" default:"false"`
	IsDeposit                          bool    `json:"isDeposit" default:"false"`
	IsWithdraw                         bool    `json:"isWithdraw" default:"false"`
	IsTransfer                         bool    `json:"isTransfer" default:"false"`
}

type PaymentGatewayAccountListResponse struct {
	ID              int64     `json:"id"`
	AccountName     string    `json:"accountName"`
	MerchantCode    string    `json:"merchantCode"`
	Provider        string    `json:"provider"`
	MinimumWithdraw float64   `json:"minimumWithdraw"`
	MaximumWithdraw float64   `json:"maximumWithdraw"`
	IsDeposit       bool      `json:"isDeposit"`
	IsWithdraw      bool      `json:"isWithdraw"`
	IsTransfer      bool      `json:"isTransfer"`
	Active          bool      `json:"active"`
	CreatedAt       time.Time `json:"createdAt"`
	UpdatedAt       time.Time `json:"updatedAt"`
}

type PaymentGatewayAccountByIdResponse struct {
	ID                                 int64   `json:"id"`
	AccountName                        string  `json:"accountName"`
	Code                               string  `json:"code"`
	Provider                           string  `json:"provider"`
	MerchantCode                       string  `json:"merchantCode"`
	SecretKey                          string  `json:"secretKey"`
	SecretKeyTwo                       string  `json:"secretKeyTwo"`
	APIKey                             string  `json:"apiKey"`
	BaseURL                            string  `json:"baseUrl"`
	FirstUsername                      string  `json:"firstUsername"`
	SecondUsername                     string  `json:"secondUsername"`
	FirstPassword                      string  `json:"firstPassword"`
	SecondPassword                     string  `json:"secondPassword"`
	MinimumWithdraw                    float64 `json:"minimumWithdraw"`
	MaximumWithdraw                    float64 `json:"maximumWithdraw"`
	WithdrawSplit                      bool    `json:"withdrawSplit"`
	MaximumWithdrawPerTransaction      float64 `json:"maximumWithdrawPerTransaction"`
	MaximumSplitWithdrawPerTransaction float64 `json:"maximumSplitWithdrawPerTransaction"`
	TimeoutSeconds                     int     `json:"timeoutSeconds"`
	MaxRetries                         int     `json:"maxRetries"`
	RetryDelaySeconds                  int     `json:"retryDelaySeconds"`
	EnableRequestLog                   bool    `json:"enableRequestLog"`
	LogResponseBody                    bool    `json:"logResponseBody"`
	EnableDebug                        bool    `json:"enableDebug"`
	IsDeposit                          bool    `json:"isDeposit"`
	IsWithdraw                         bool    `json:"isWithdraw"`
	IsTransfer                         bool    `json:"isTransfer"`
}

type PaymentGatewayAccountSearchRequest struct {
	request.RequestPagination
	AccountName *string `form:"accountName"`
}

type UpdateStatusRequest struct {
	Name   string `form:"name" validate:"required,min=1,max=255"`
	Status bool   `form:"status" default:"false"`
}

func (p *PaymentGatewayAccount) ToListResponse() *PaymentGatewayAccountListResponse {
	return &PaymentGatewayAccountListResponse{
		ID:              p.ID,
		AccountName:     p.AccountName,
		MerchantCode:    p.MerchantCode,
		Provider:        p.Provider,
		MinimumWithdraw: p.MinimumWithdraw,
		MaximumWithdraw: p.MaximumWithdraw,
		IsDeposit:       p.IsDeposit,
		IsWithdraw:      p.IsWithdraw,
		IsTransfer:      p.IsTransfer,
		Active:          p.Active,
		CreatedAt:       p.CreatedAt,
		UpdatedAt:       p.UpdatedAt,
	}
}

func (p *PaymentGatewayAccount) ToByIdResponse() *PaymentGatewayAccountByIdResponse {
	return &PaymentGatewayAccountByIdResponse{
		ID:                                 p.ID,
		AccountName:                        p.AccountName,
		Code:                               p.Code,
		Provider:                           p.Provider,
		MerchantCode:                       p.MerchantCode,
		SecretKey:                          p.SecretKey,
		SecretKeyTwo:                       p.SecretKeyTwo,
		APIKey:                             p.APIKey,
		BaseURL:                            p.BaseURL,
		FirstUsername:                      p.FirstUsername,
		SecondUsername:                     p.SecondUsername,
		FirstPassword:                      p.FirstPassword,
		SecondPassword:                     p.SecretKeyTwo,
		MinimumWithdraw:                    p.MinimumWithdraw,
		MaximumWithdraw:                    p.MaximumWithdraw,
		WithdrawSplit:                      p.WithdrawSplit,
		MaximumWithdrawPerTransaction:      p.MaximumWithdrawPerTransaction,
		MaximumSplitWithdrawPerTransaction: p.MaximumSplitWithdrawPerTransaction,
		TimeoutSeconds:                     p.TimeoutSeconds,
		MaxRetries:                         p.MaxRetries,
		RetryDelaySeconds:                  p.RetryDelaySeconds,
		EnableRequestLog:                   p.EnableRequestLog,
		LogResponseBody:                    p.LogResponseBody,
		EnableDebug:                        p.EnableDebug,
		IsDeposit:                          p.IsDeposit,
		IsWithdraw:                         p.IsWithdraw,
		IsTransfer:                         p.IsTransfer,
	}
}

type JaiJaiPayConfig struct {
	BaseURL          string        `json:"baseUrl"`
	APIKey           string        `json:"apiKey"`
	SecretKey        string        `json:"secretKey"`
	Timeout          time.Duration `json:"timeout"`
	MaxRetries       int           `json:"maxRetries"`
	RetryDelay       time.Duration `json:"retryDelay"`
	EnableRequestLog bool          `json:"enableRequestLog"`
	LogResponseBody  bool          `json:"logResponseBody"`
	EnableDebug      bool          `json:"enableDebug"`
}

func (p *PaymentGatewayAccount) ToJaiJaiPayConfig() *JaiJaiPayConfig {
	return &JaiJaiPayConfig{
		BaseURL:          p.BaseURL,
		APIKey:           p.APIKey,
		SecretKey:        p.SecretKey,
		Timeout:          time.Duration(p.TimeoutSeconds) * time.Second,
		MaxRetries:       p.MaxRetries,
		RetryDelay:       time.Duration(p.RetryDelaySeconds) * time.Second,
		EnableRequestLog: p.EnableRequestLog,
		LogResponseBody:  p.LogResponseBody,
		EnableDebug:      p.EnableDebug,
	}
}
