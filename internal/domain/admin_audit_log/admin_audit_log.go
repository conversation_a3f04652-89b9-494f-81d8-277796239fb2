package admin_audit_log

import (
	"blacking-api/pkg/errors"
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

// AdminAuditLog represents an audit log entry for admin API operations
type AdminAuditLog struct {
	ID             int       `json:"id" db:"id"`
	UserID         int       `json:"user_id" db:"user_id"`
	Username       string    `json:"username" db:"username"`
	Method         string    `json:"method" db:"method"`
	Path           string    `json:"path" db:"path"`
	RequestBody    *string   `json:"request_body,omitempty" db:"request_body"`
	ResponseStatus int       `json:"response_status" db:"response_status"`
	IPAddress      *string   `json:"ip_address,omitempty" db:"ip_address"`
	UserAgent      *string   `json:"user_agent,omitempty" db:"user_agent"`
	CreatedAt      time.Time `json:"created_at" db:"created_at"`
}

// CreateAdminAuditLogRequest represents the request to create an admin audit log
type CreateAdminAuditLogRequest struct {
	UserID         int     `json:"user_id" validate:"required"`
	Username       string  `json:"username" validate:"required"`
	Method         string  `json:"method" validate:"required"`
	Path           string  `json:"path" validate:"required"`
	RequestBody    *string `json:"request_body,omitempty"`
	ResponseStatus int     `json:"response_status"`
	IPAddress      *string `json:"ip_address,omitempty"`
	UserAgent      *string `json:"user_agent,omitempty"`
}

// AdminAuditLogResponse represents the response structure for admin audit log
type AdminAuditLogResponse struct {
	ID             int       `json:"id"`
	UserID         int       `json:"user_id"`
	Username       string    `json:"username"`
	Method         string    `json:"method"`
	Path           string    `json:"path"`
	RequestBody    *string   `json:"request_body,omitempty"`
	ResponseStatus int       `json:"response_status"`
	IPAddress      *string   `json:"ip_address,omitempty"`
	UserAgent      *string   `json:"user_agent,omitempty"`
	CreatedAt      time.Time `json:"created_at"`
}

// AdminAuditLogFilter represents filter parameters for listing admin audit logs
type AdminAuditLogFilter struct {
	UserID   *int    `json:"user_id,omitempty"`
	Username *string `json:"username,omitempty"`
	Method   *string `json:"method,omitempty"`
	Path     *string `json:"path,omitempty"`
	DateFrom *string `json:"date_from,omitempty"` // Format: YYYY-MM-DD
	DateTo   *string `json:"date_to,omitempty"`   // Format: YYYY-MM-DD
}

// NewAdminAuditLog creates a new admin audit log entry
func NewAdminAuditLog(req CreateAdminAuditLogRequest) (*AdminAuditLog, error) {
	if err := validateCreateRequest(req); err != nil {
		return nil, err
	}

	// Sanitize request body for security
	sanitizedRequestBody := sanitizeRequestBody(req.RequestBody)

	now := time.Now()
	auditLog := &AdminAuditLog{
		UserID:         req.UserID,
		Username:       req.Username,
		Method:         req.Method,
		Path:           req.Path,
		RequestBody:    sanitizedRequestBody,
		ResponseStatus: req.ResponseStatus,
		IPAddress:      req.IPAddress,
		UserAgent:      req.UserAgent,
		CreatedAt:      now,
	}

	return auditLog, nil
}

// ToResponse converts AdminAuditLog to AdminAuditLogResponse
func (a *AdminAuditLog) ToResponse() AdminAuditLogResponse {
	return AdminAuditLogResponse{
		ID:             a.ID,
		UserID:         a.UserID,
		Username:       a.Username,
		Method:         a.Method,
		Path:           a.Path,
		RequestBody:    a.RequestBody,
		ResponseStatus: a.ResponseStatus,
		IPAddress:      a.IPAddress,
		UserAgent:      a.UserAgent,
		CreatedAt:      a.CreatedAt,
	}
}

// validateCreateRequest validates the create admin audit log request
func validateCreateRequest(req CreateAdminAuditLogRequest) error {
	if req.UserID == 0 {
		return errors.NewValidationError("user_id is required")
	}
	if req.Username == "" {
		return errors.NewValidationError("username is required")
	}
	if req.Method == "" {
		return errors.NewValidationError("method is required")
	}
	if req.Path == "" {
		return errors.NewValidationError("path is required")
	}
	
	// Validate HTTP method
	validMethods := []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "HEAD"}
	isValidMethod := false
	for _, method := range validMethods {
		if req.Method == method {
			isValidMethod = true
			break
		}
	}
	if !isValidMethod {
		return errors.NewValidationError("invalid HTTP method")
	}

	return nil
}

// sanitizeRequestBody sanitizes request body to remove sensitive information
func sanitizeRequestBody(requestBody *string) *string {
	if requestBody == nil || *requestBody == "" {
		return requestBody
	}

	body := *requestBody

	// Check if it's multipart form data and extract meaningful fields
	if isMultipartFormData(body) {
		summary := extractMultipartSummary(body)
		return &summary
	}

	// Check if it's a file upload (binary data)
	if len(body) > 100 && containsBinaryData(body) {
		fileInfo := "Binary file upload detected - content not logged for security"
		return &fileInfo
	}

	// Limit body size and mask sensitive fields
	if len(body) > 2000 { // Reduce from 5KB to 2KB for better readability
		body = body[:2000] + "... [TRUNCATED - " + formatSize(len(*requestBody)) + " total]"
	}

	// Mask password fields
	body = maskSensitiveFields(body)

	return &body
}

// containsBinaryData checks if the string contains binary data
func containsBinaryData(data string) bool {
	// Check for null bytes or other binary indicators
	for _, b := range data {
		if b == 0 || b > 127 {
			return true
		}
	}
	return false
}

// isFileUpload checks if the request body indicates a file upload
func isFileUpload(body string) bool {
	// Check for common file upload indicators
	indicators := []string{
		"Content-Disposition: form-data",
		"filename=",
		"multipart/form-data",
		"boundary=",
	}
	
	for _, indicator := range indicators {
		if len(body) > 1000 && contains(body, indicator) {
			return true
		}
	}
	return false
}

// maskSensitiveFields masks password and other sensitive fields in JSON
func maskSensitiveFields(body string) string {
	// Simple approach to mask common sensitive fields
	sensitiveFields := []string{
		"password",
		"Password", 
		"PASSWORD",
		"secret",
		"Secret",
		"SECRET",
		"token",
		"Token",
		"TOKEN",
		"key",
		"Key",
		"KEY",
	}

	result := body
	for _, field := range sensitiveFields {
		// Replace field values with asterisks (simple regex-like replacement)
		result = maskFieldValue(result, field)
	}

	return result
}

// maskFieldValue masks a specific field value in JSON-like string
func maskFieldValue(body, fieldName string) string {
	// Simple string replacement for JSON field values
	// This is a basic implementation - for production, consider using proper JSON parsing
	patterns := []string{
		`"` + fieldName + `":"`,
		`"` + fieldName + `": "`,
		`'` + fieldName + `':'`,
		`'` + fieldName + `': '`,
	}

	for _, pattern := range patterns {
		if contains(body, pattern) {
			// Find the field and replace its value with asterisks
			start := indexOf(body, pattern)
			if start != -1 {
				start += len(pattern)
				end := findValueEnd(body, start)
				if end > start {
					value := body[start:end]
					masked := generateAsterisks(len(value))
					body = body[:start] + masked + body[end:]
				}
			}
		}
	}

	return body
}

// Helper functions
func contains(s, substr string) bool {
	return indexOf(s, substr) != -1
}

func indexOf(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

func findValueEnd(s string, start int) int {
	if start >= len(s) {
		return start
	}

	quote := s[start-1] // Get the quote character (" or ')
	for i := start; i < len(s); i++ {
		if s[i] == quote {
			return i
		}
	}
	return len(s)
}

func generateAsterisks(length int) string {
	if length == 0 {
		return ""
	}
	if length > 20 {
		length = 8 // Limit asterisks length
	}
	asterisks := make([]byte, length)
	for i := range asterisks {
		asterisks[i] = '*'
	}
	return string(asterisks)
}

// isMultipartFormData checks if the body is multipart form data
func isMultipartFormData(body string) bool {
	return contains(body, "Content-Disposition: form-data")
}

// extractMultipartSummary extracts field names and values from multipart data as JSON
func extractMultipartSummary(body string) string {
	lines := strings.Split(body, "\n")
	fields := make(map[string]interface{})
	var currentField string
	var hasFiles bool
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		
		// Extract field name
		if contains(line, "Content-Disposition: form-data; name=") {
			start := indexOf(line, `name="`) + 6
			end := indexOf(line[start:], `"`)
			if end > 0 {
				currentField = line[start : start+end]
			}
			
			// Check if this is a file field
			if contains(line, "filename=") {
				hasFiles = true
				filenameStart := indexOf(line, `filename="`) + 10
				filenameEnd := indexOf(line[filenameStart:], `"`)
				if filenameEnd > 0 {
					filename := line[filenameStart : filenameStart+filenameEnd]
					fields[currentField] = map[string]string{
						"type": "file",
						"filename": filename,
					}
					currentField = ""
				}
			}
		} else if line != "" && !contains(line, "Content-Disposition") && !contains(line, "---") && !contains(line, "Content-Type") && currentField != "" {
			// This is likely the field value
			var currentValue string
			if len(line) > 200 {
				currentValue = line[:200] + "..."
			} else {
				currentValue = line
			}
			
			// Mask sensitive fields
			if isSensitiveField(currentField) {
				currentValue = "***"
			}
			
			fields[currentField] = currentValue
			currentField = ""
		}
	}
	
	// Create summary object
	summary := map[string]interface{}{
		"type": "multipart_form",
		"fields": fields,
		"total_size": formatSize(len(body)),
	}
	
	if hasFiles {
		summary["has_files"] = true
	}
	
	// Convert to JSON string
	result, err := jsonMarshal(summary)
	if err != nil {
		return fmt.Sprintf(`{"type":"multipart_form","error":"failed_to_parse","size":"%s"}`, formatSize(len(body)))
	}
	
	// Limit JSON size
	if len(result) > 1000 {
		truncatedSummary := map[string]interface{}{
			"type": "multipart_form",
			"total_size": formatSize(len(body)),
			"field_count": len(fields),
			"truncated": true,
		}
		if hasFiles {
			truncatedSummary["has_files"] = true
		}
		result, _ = jsonMarshal(truncatedSummary)
	}
	
	return result
}

// formatSize formats byte size to human readable format
func formatSize(bytes int) string {
	if bytes < 1024 {
		return fmt.Sprintf("%d bytes", bytes)
	} else if bytes < 1024*1024 {
		return fmt.Sprintf("%.1f KB", float64(bytes)/1024)
	} else {
		return fmt.Sprintf("%.1f MB", float64(bytes)/(1024*1024))
	}
}

// isSensitiveField checks if a field name is sensitive
func isSensitiveField(fieldName string) bool {
	sensitiveFields := []string{
		"password", "Password", "PASSWORD",
		"secret", "Secret", "SECRET",
		"token", "Token", "TOKEN",
		"key", "Key", "KEY",
	}
	
	fieldLower := strings.ToLower(fieldName)
	for _, sensitive := range sensitiveFields {
		if contains(fieldLower, strings.ToLower(sensitive)) {
			return true
		}
	}
	return false
}

// jsonMarshal marshals interface to JSON string with error handling
func jsonMarshal(v interface{}) (string, error) {
	bytes, err := json.Marshal(v)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

