package domain

import (
	"encoding/json"
	"time"
)

// JaiJaiPayAPILog represents a log entry for JaiJaiPay API calls
type JaiJaiPayAPILog struct {
	ID                 int64           `json:"id" db:"id"`
	RequestID          string          `json:"request_id" db:"request_id"`
	APICategory        string          `json:"api_category" db:"api_category"`
	Method             string          `json:"method" db:"method"`
	Endpoint           string          `json:"endpoint" db:"endpoint"`
	RequestBody        json.RawMessage `json:"request_body" db:"request_body"`
	RequestHeaders     json.RawMessage `json:"request_headers" db:"request_headers"`
	ResponseStatusCode *int            `json:"response_status_code" db:"response_status_code"`
	ResponseBody       json.RawMessage `json:"response_body" db:"response_body"`
	ResponseTimeMs     *int            `json:"response_time_ms" db:"response_time_ms"`
	Success            bool            `json:"success" db:"success"`
	ErrorMessage       *string         `json:"error_message" db:"error_message"`
	BusinessData       json.RawMessage `json:"business_data" db:"business_data"`
	CreatedAt          time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt          time.Time       `json:"updated_at" db:"updated_at"`
}

// BusinessContext represents flexible business context data
type BusinessContext struct {
	OrderID           *string  `json:"order_id,omitempty"`
	TransactionID     *string  `json:"transaction_id,omitempty"`
	CustomerReference *string  `json:"customer_reference,omitempty"`
	Amount            *float64 `json:"amount,omitempty"`
	Currency          *string  `json:"currency,omitempty"`
	BankCode          *string  `json:"bank_code,omitempty"`
	BankAccount       *string  `json:"bank_account,omitempty"`
	AccountHolder     *string  `json:"account_holder,omitempty"`
	// Add more fields as needed for different API types
}

// SetBusinessData converts BusinessContext to JSON and stores it
func (log *JaiJaiPayAPILog) SetBusinessData(ctx BusinessContext) error {
	data, err := json.Marshal(ctx)
	if err != nil {
		return err
	}
	log.BusinessData = data
	return nil
}

// GetBusinessData retrieves and unmarshals business context
func (log *JaiJaiPayAPILog) GetBusinessData() (*BusinessContext, error) {
	if len(log.BusinessData) == 0 {
		return &BusinessContext{}, nil
	}

	var ctx BusinessContext
	err := json.Unmarshal(log.BusinessData, &ctx)
	if err != nil {
		return nil, err
	}

	return &ctx, nil
}

// APICategory constants
var APICategories = struct {
	Deposits    string
	Withdrawals string
	Transfers   string
	Payments    string
}{
	Deposits:    "deposits",
	Withdrawals: "withdrawals",
	Transfers:   "transfers",
	Payments:    "payments",
}
