package member_group_type

import (
	"time"

	"blacking-api/pkg/errors"
)

// parseBoolString converts interface{} values to bool
// Accepts: "true", "1", "yes", true -> true
// Accepts: "false", "0", "no", false, nil -> false
func parseBoolString(v interface{}) bool {
	if v == nil {
		return false
	}

	switch val := v.(type) {
	case bool:
		return val
	case string:
		switch val {
		case "true", "1", "yes", "True", "TRUE", "YES":
			return true
		default:
			return false
		}
	case *string:
		if val == nil {
			return false
		}
		switch *val {
		case "true", "1", "yes", "True", "TRUE", "YES":
			return true
		default:
			return false
		}
	default:
		return false
	}
}

// Status represents the member group type status
type Status string

const (
	StatusActive   Status = "active"
	StatusInactive Status = "inactive"
)

// CalculationType represents calculation type for level conditions
type CalculationType string

const (
	CalculationTypeDay   CalculationType = "day"
	CalculationTypeMonth CalculationType = "month"
)

// ConditionType represents condition type for level upgrade/downgrade
type ConditionType string

const (
	ConditionTypeBetting           ConditionType = "betting_amount"      // ยอดแทงสะสม
	ConditionTypeDeposit           ConditionType = "deposit_amount"      // ยอดฝากสะสม
	ConditionTypeBettingAndDeposit ConditionType = "betting_and_deposit" // ยอดแทงสะสมและยอดฝากสะสม
	ConditionTypeBettingOrDeposit  ConditionType = "betting_or_deposit"  // ยอดแทงสะสมหรือยอดฝากสะสม
)

// MemberGroupType represents a member group type
type MemberGroupType struct {
	ID               int     `json:"id"`
	Name             string  `json:"name"`               // ชื่อ(เลเวล)
	Position         int     `json:"position"`           // ลำดับการแสดงผล
	ShowInLobby      bool    `json:"show_in_lobby"`      // แสดงที่ lobby
	BadgeBgColor     string  `json:"badge_bg_color"`     // สีพื้นหลัง badge
	BadgeBorderColor string  `json:"badge_border_color"` // สีขอบ badge
	Image1           *string `json:"image1"`             // รูปภาพ 1 (jpg, png, 165x165px)
	Image2           *string `json:"image2"`             // รูปภาพ 2 (gif, 165x165px)
	BgImage          *string `json:"bg_image"`           // รูปภาพพื้นหลัง (jpg, png, 400x220px)

	// VIP Benefits
	VipEnabled                 bool `json:"vip_enabled"`                   // เปิดใช้งาน
	VipPersonalCustomerService bool `json:"vip_personal_customer_service"` // ฝ่ายบริการลูกค้าพิเศษ รายบุคคล
	VipMaxDailyWithdraw        bool `json:"vip_max_daily_withdraw"`        // ยอดถอนสูงสุดต่อวัน
	VipEventParticipation      bool `json:"vip_event_participation"`       // สิทธิเข้าร่วมกิจกรรมต่างๆ

	// Bonus Benefits
	BonusEnabled          bool `json:"bonus_enabled"`           // เปิดใช้งาน
	BonusBirthday         bool `json:"bonus_birthday"`          // โบนัสพิเศษวันเกิด
	BonusLevelMaintenance bool `json:"bonus_level_maintenance"` // โบนัสการรักษาระดับ
	BonusLevelUpgrade     bool `json:"bonus_level_upgrade"`     // โบนัสพิเศษเลื่อนระดับ
	BonusFestival         bool `json:"bonus_festival"`          // โบนัสพิเศษตามเทศกาล

	// Cashback Benefits
	CashbackEnabled bool `json:"cashback_enabled"` // เปิดใช้งาน
	CashbackSports  bool `json:"cashback_sports"`  // กีฬา
	CashbackCasino  bool `json:"cashback_casino"`  // คาสิโน
	CashbackFishing bool `json:"cashback_fishing"` // ยิงปลา
	CashbackSlot    bool `json:"cashback_slot"`    // สล็อต
	CashbackLottery bool `json:"cashback_lottery"` // หวย
	CashbackCard    bool `json:"cashback_card"`    // ไพ่
	CashbackOther   bool `json:"cashback_other"`   // อื่นๆ

	// Level Upgrade Conditions
	UpgradeEnable          bool            `json:"upgrade_enable"`           // เปิดใช้งานการเลื่อนระดับ
	UpgradeBettingAmount   float64         `json:"upgrade_betting_amount"`   // ยอดแทงสะสม
	UpgradeDepositAmount   float64         `json:"upgrade_deposit_amount"`   // ยอดฝากสะสม
	UpgradeCalculationType CalculationType `json:"upgrade_calculation_type"` // คำนวนจาก (day/month)
	UpgradeDays            int             `json:"upgrade_days"`             // จำนวนวัน (1-90)
	UpgradeConditionType   ConditionType   `json:"upgrade_condition_type"`   // คำนวนจาก

	// Level Downgrade Conditions
	DowngradeEnable          bool            `json:"downgrade_enable"`           // เปิดใช้งานการลดระดับ
	DowngradeBettingAmount   float64         `json:"downgrade_betting_amount"`   // ยอดแทงสะสม
	DowngradeDepositAmount   float64         `json:"downgrade_deposit_amount"`   // ยอดฝากสะสม
	DowngradeCalculationType CalculationType `json:"downgrade_calculation_type"` // คำนวนจาก (day/month)
	DowngradeDays            int             `json:"downgrade_days"`             // จำนวนวัน (1-90)
	DowngradeConditionType   ConditionType   `json:"downgrade_condition_type"`   // คำนวนจาก

	Status    Status    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// CreateMemberGroupTypeRequest for creating member group type
type CreateMemberGroupTypeRequest struct {
	Name             string  `json:"name" validate:"required,min=1,max=50"`
	Position         int     `json:"position"`
	ShowInLobby      bool    `json:"show_in_lobby"`
	BadgeBgColor     string  `json:"badge_bg_color" validate:"required,hexcolor"`
	BadgeBorderColor string  `json:"badge_border_color" validate:"required,hexcolor"`
	Image1           *string `json:"image1"`
	Image2           *string `json:"image2"`
	BgImage          *string `json:"bg_image"`

	// VIP Benefits
	VipEnabled                 bool `json:"vip_enabled"`
	VipPersonalCustomerService bool `json:"vip_personal_customer_service"`
	VipMaxDailyWithdraw        bool `json:"vip_max_daily_withdraw"`
	VipEventParticipation      bool `json:"vip_event_participation"`

	// Bonus Benefits
	BonusEnabled          bool `json:"bonus_enabled"`
	BonusBirthday         bool `json:"bonus_birthday"`
	BonusLevelMaintenance bool `json:"bonus_level_maintenance"`
	BonusLevelUpgrade     bool `json:"bonus_level_upgrade"`
	BonusFestival         bool `json:"bonus_festival"`

	// Cashback Benefits
	CashbackEnabled bool `json:"cashback_enabled"`
	CashbackSports  bool `json:"cashback_sports"`
	CashbackCasino  bool `json:"cashback_casino"`
	CashbackFishing bool `json:"cashback_fishing"`
	CashbackSlot    bool `json:"cashback_slot"`
	CashbackLottery bool `json:"cashback_lottery"`
	CashbackCard    bool `json:"cashback_card"`
	CashbackOther   bool `json:"cashback_other"`

	// Level Upgrade Conditions
	UpgradeEnable          bool            `json:"upgrade_enable"` // เปิดใช้งานการเลื่อนระดับ
	UpgradeBettingAmount   float64         `json:"upgrade_betting_amount" validate:"min=0"`
	UpgradeDepositAmount   float64         `json:"upgrade_deposit_amount" validate:"min=0"`
	UpgradeCalculationType CalculationType `json:"upgrade_calculation_type" validate:"required,oneof=day month"`
	UpgradeDays            int             `json:"upgrade_days" validate:"min=1,max=90"`
	UpgradeConditionType   ConditionType   `json:"upgrade_condition_type" validate:"required"`

	// Level Downgrade Conditions
	DowngradeEnable          bool            `json:"downgrade_enable"` // เปิดใช้งานการลดระดับ
	DowngradeBettingAmount   float64         `json:"downgrade_betting_amount" validate:"min=0"`
	DowngradeDepositAmount   float64         `json:"downgrade_deposit_amount" validate:"min=0"`
	DowngradeCalculationType CalculationType `json:"downgrade_calculation_type" validate:"required,oneof=day month"`
	DowngradeDays            int             `json:"downgrade_days" validate:"min=1,max=90"`
	DowngradeConditionType   ConditionType   `json:"downgrade_condition_type" validate:"required"`
}

// UpdateMemberGroupTypeRequest for updating member group type
type UpdateMemberGroupTypeRequest struct {
	Name             string      `json:"name" validate:"required,min=1,max=50"`
	Position         int         `json:"position"`
	ShowInLobby      bool        `json:"show_in_lobby"`
	BadgeBgColor     string      `json:"badge_bg_color" validate:"required,hexcolor"`
	BadgeBorderColor string      `json:"badge_border_color" validate:"required,hexcolor"`
	Image1           *string     `json:"image1"`
	Image2           *string     `json:"image2"`
	BgImage          *string     `json:"bg_image"`
	Image1Delete     interface{} `json:"image1_delete"`   // Flag to delete image1 (accepts "true", "false", "1", "0", true, false)
	Image2Delete     interface{} `json:"image2_delete"`   // Flag to delete image2 (accepts "true", "false", "1", "0", true, false)
	BgImageDelete    interface{} `json:"bg_image_delete"` // Flag to delete bg_image (accepts "true", "false", "1", "0", true, false)

	// VIP Benefits
	VipEnabled                 bool `json:"vip_enabled"`
	VipPersonalCustomerService bool `json:"vip_personal_customer_service"`
	VipMaxDailyWithdraw        bool `json:"vip_max_daily_withdraw"`
	VipEventParticipation      bool `json:"vip_event_participation"`

	// Bonus Benefits
	BonusEnabled          bool `json:"bonus_enabled"`
	BonusBirthday         bool `json:"bonus_birthday"`
	BonusLevelMaintenance bool `json:"bonus_level_maintenance"`
	BonusLevelUpgrade     bool `json:"bonus_level_upgrade"`
	BonusFestival         bool `json:"bonus_festival"`

	// Cashback Benefits
	CashbackEnabled bool `json:"cashback_enabled"`
	CashbackSports  bool `json:"cashback_sports"`
	CashbackCasino  bool `json:"cashback_casino"`
	CashbackFishing bool `json:"cashback_fishing"`
	CashbackSlot    bool `json:"cashback_slot"`
	CashbackLottery bool `json:"cashback_lottery"`
	CashbackCard    bool `json:"cashback_card"`
	CashbackOther   bool `json:"cashback_other"`

	// Level Upgrade Conditions
	UpgradeEnable          bool            `json:"upgrade_enable"` // เปิดใช้งานการเลื่อนระดับ
	UpgradeBettingAmount   float64         `json:"upgrade_betting_amount" validate:"min=0"`
	UpgradeDepositAmount   float64         `json:"upgrade_deposit_amount" validate:"min=0"`
	UpgradeCalculationType CalculationType `json:"upgrade_calculation_type" validate:"required,oneof=day month"`
	UpgradeDays            int             `json:"upgrade_days" validate:"min=1,max=90"`
	UpgradeConditionType   ConditionType   `json:"upgrade_condition_type" validate:"required"`

	// Level Downgrade Conditions
	DowngradeEnable          bool            `json:"downgrade_enable"` // เปิดใช้งานการลดระดับ
	DowngradeBettingAmount   float64         `json:"downgrade_betting_amount" validate:"min=0"`
	DowngradeDepositAmount   float64         `json:"downgrade_deposit_amount" validate:"min=0"`
	DowngradeCalculationType CalculationType `json:"downgrade_calculation_type" validate:"required,oneof=day month"`
	DowngradeDays            int             `json:"downgrade_days" validate:"min=1,max=90"`
	DowngradeConditionType   ConditionType   `json:"downgrade_condition_type" validate:"required"`
}

// MemberGroupTypeResponse represents member group type API response
type MemberGroupTypeResponse struct {
	ID               int     `json:"id"`
	Name             string  `json:"name"`
	Position         int     `json:"position"`
	ShowInLobby      bool    `json:"show_in_lobby"`
	BadgeBgColor     string  `json:"badge_bg_color"`
	BadgeBorderColor string  `json:"badge_border_color"`
	Image1           *string `json:"image1"`
	Image2           *string `json:"image2"`
	BgImage          *string `json:"bg_image"`

	// VIP Benefits
	VipEnabled                 bool `json:"vip_enabled"`
	VipPersonalCustomerService bool `json:"vip_personal_customer_service"`
	VipMaxDailyWithdraw        bool `json:"vip_max_daily_withdraw"`
	VipEventParticipation      bool `json:"vip_event_participation"`

	// Bonus Benefits
	BonusEnabled          bool `json:"bonus_enabled"`
	BonusBirthday         bool `json:"bonus_birthday"`
	BonusLevelMaintenance bool `json:"bonus_level_maintenance"`
	BonusLevelUpgrade     bool `json:"bonus_level_upgrade"`
	BonusFestival         bool `json:"bonus_festival"`

	// Cashback Benefits
	CashbackEnabled bool `json:"cashback_enabled"`
	CashbackSports  bool `json:"cashback_sports"`
	CashbackCasino  bool `json:"cashback_casino"`
	CashbackFishing bool `json:"cashback_fishing"`
	CashbackSlot    bool `json:"cashback_slot"`
	CashbackLottery bool `json:"cashback_lottery"`
	CashbackCard    bool `json:"cashback_card"`
	CashbackOther   bool `json:"cashback_other"`

	// Level Upgrade Conditions
	UpgradeEnable          bool            `json:"upgrade_enable"`
	UpgradeBettingAmount   float64         `json:"upgrade_betting_amount"`
	UpgradeDepositAmount   float64         `json:"upgrade_deposit_amount"`
	UpgradeCalculationType CalculationType `json:"upgrade_calculation_type"`
	UpgradeDays            int             `json:"upgrade_days"`
	UpgradeConditionType   ConditionType   `json:"upgrade_condition_type"`

	// Level Downgrade Conditions
	DowngradeEnable          bool            `json:"downgrade_enable"`
	DowngradeBettingAmount   float64         `json:"downgrade_betting_amount"`
	DowngradeDepositAmount   float64         `json:"downgrade_deposit_amount"`
	DowngradeCalculationType CalculationType `json:"downgrade_calculation_type"`
	DowngradeDays            int             `json:"downgrade_days"`
	DowngradeConditionType   ConditionType   `json:"downgrade_condition_type"`

	Status    Status    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// MemberGroupTypeDropdownResponse represents member group type dropdown API response
type MemberGroupTypeDropdownResponse struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

// NewMemberGroupType creates a new member group type
func NewMemberGroupType(req CreateMemberGroupTypeRequest) (*MemberGroupType, error) {
	if err := validateMemberGroupTypeRequest(req); err != nil {
		return nil, err
	}

	now := time.Now()
	memberGroupType := &MemberGroupType{
		// ID will be generated by database
		Name:             req.Name,
		Position:         req.Position,
		ShowInLobby:      req.ShowInLobby,
		BadgeBgColor:     req.BadgeBgColor,
		BadgeBorderColor: req.BadgeBorderColor,
		Image1:           req.Image1,
		Image2:           req.Image2,
		BgImage:          req.BgImage,

		// VIP Benefits
		VipEnabled:                 req.VipEnabled,
		VipPersonalCustomerService: req.VipPersonalCustomerService,
		VipMaxDailyWithdraw:        req.VipMaxDailyWithdraw,
		VipEventParticipation:      req.VipEventParticipation,

		// Bonus Benefits
		BonusEnabled:          req.BonusEnabled,
		BonusBirthday:         req.BonusBirthday,
		BonusLevelMaintenance: req.BonusLevelMaintenance,
		BonusLevelUpgrade:     req.BonusLevelUpgrade,
		BonusFestival:         req.BonusFestival,

		// Cashback Benefits
		CashbackEnabled: req.CashbackEnabled,
		CashbackSports:  req.CashbackSports,
		CashbackCasino:  req.CashbackCasino,
		CashbackFishing: req.CashbackFishing,
		CashbackSlot:    req.CashbackSlot,
		CashbackLottery: req.CashbackLottery,
		CashbackCard:    req.CashbackCard,
		CashbackOther:   req.CashbackOther,

		// Level Upgrade Conditions
		UpgradeEnable:          req.UpgradeEnable,
		UpgradeBettingAmount:   req.UpgradeBettingAmount,
		UpgradeDepositAmount:   req.UpgradeDepositAmount,
		UpgradeCalculationType: req.UpgradeCalculationType,
		UpgradeDays:            req.UpgradeDays,
		UpgradeConditionType:   req.UpgradeConditionType,

		// Level Downgrade Conditions
		DowngradeEnable:          req.DowngradeEnable,
		DowngradeBettingAmount:   req.DowngradeBettingAmount,
		DowngradeDepositAmount:   req.DowngradeDepositAmount,
		DowngradeCalculationType: req.DowngradeCalculationType,
		DowngradeDays:            req.DowngradeDays,
		DowngradeConditionType:   req.DowngradeConditionType,

		Status:    StatusActive,
		CreatedAt: now,
		UpdatedAt: now,
	}

	return memberGroupType, nil
}

// Update updates member group type information
func (mgt *MemberGroupType) Update(req UpdateMemberGroupTypeRequest) error {
	if err := validateUpdateMemberGroupTypeRequest(req); err != nil {
		return err
	}

	mgt.Name = req.Name
	mgt.Position = req.Position
	mgt.ShowInLobby = req.ShowInLobby
	mgt.BadgeBgColor = req.BadgeBgColor
	mgt.BadgeBorderColor = req.BadgeBorderColor

	// Handle image deletion flags
	if parseBoolString(req.Image1Delete) {
		// Debug: Image1 will be deleted
		mgt.Image1 = nil
	} else if req.Image1 != nil {
		mgt.Image1 = req.Image1
	}

	if parseBoolString(req.Image2Delete) {
		mgt.Image2 = nil
	} else if req.Image2 != nil {
		mgt.Image2 = req.Image2
	}

	if parseBoolString(req.BgImageDelete) {
		mgt.BgImage = nil
	} else if req.BgImage != nil {
		mgt.BgImage = req.BgImage
	}

	// VIP Benefits
	mgt.VipEnabled = req.VipEnabled
	mgt.VipPersonalCustomerService = req.VipPersonalCustomerService
	mgt.VipMaxDailyWithdraw = req.VipMaxDailyWithdraw
	mgt.VipEventParticipation = req.VipEventParticipation

	// Bonus Benefits
	mgt.BonusEnabled = req.BonusEnabled
	mgt.BonusBirthday = req.BonusBirthday
	mgt.BonusLevelMaintenance = req.BonusLevelMaintenance
	mgt.BonusLevelUpgrade = req.BonusLevelUpgrade
	mgt.BonusFestival = req.BonusFestival

	// Cashback Benefits
	mgt.CashbackEnabled = req.CashbackEnabled
	mgt.CashbackSports = req.CashbackSports
	mgt.CashbackCasino = req.CashbackCasino
	mgt.CashbackFishing = req.CashbackFishing
	mgt.CashbackSlot = req.CashbackSlot
	mgt.CashbackLottery = req.CashbackLottery
	mgt.CashbackCard = req.CashbackCard
	mgt.CashbackOther = req.CashbackOther

	// Level Upgrade Conditions
	mgt.UpgradeEnable = req.UpgradeEnable
	mgt.UpgradeBettingAmount = req.UpgradeBettingAmount
	mgt.UpgradeDepositAmount = req.UpgradeDepositAmount
	mgt.UpgradeCalculationType = req.UpgradeCalculationType
	mgt.UpgradeDays = req.UpgradeDays
	mgt.UpgradeConditionType = req.UpgradeConditionType

	// Level Downgrade Conditions
	mgt.DowngradeEnable = req.DowngradeEnable
	mgt.DowngradeBettingAmount = req.DowngradeBettingAmount
	mgt.DowngradeDepositAmount = req.DowngradeDepositAmount
	mgt.DowngradeCalculationType = req.DowngradeCalculationType
	mgt.DowngradeDays = req.DowngradeDays
	mgt.DowngradeConditionType = req.DowngradeConditionType

	mgt.UpdatedAt = time.Now()

	return nil
}

// ToResponse converts member group type to response format
func (mgt *MemberGroupType) ToResponse() MemberGroupTypeResponse {
	return MemberGroupTypeResponse{
		ID:               mgt.ID,
		Name:             mgt.Name,
		Position:         mgt.Position,
		ShowInLobby:      mgt.ShowInLobby,
		BadgeBgColor:     mgt.BadgeBgColor,
		BadgeBorderColor: mgt.BadgeBorderColor,
		Image1:           mgt.Image1,
		Image2:           mgt.Image2,
		BgImage:          mgt.BgImage,

		// VIP Benefits
		VipEnabled:                 mgt.VipEnabled,
		VipPersonalCustomerService: mgt.VipPersonalCustomerService,
		VipMaxDailyWithdraw:        mgt.VipMaxDailyWithdraw,
		VipEventParticipation:      mgt.VipEventParticipation,

		// Bonus Benefits
		BonusEnabled:          mgt.BonusEnabled,
		BonusBirthday:         mgt.BonusBirthday,
		BonusLevelMaintenance: mgt.BonusLevelMaintenance,
		BonusLevelUpgrade:     mgt.BonusLevelUpgrade,
		BonusFestival:         mgt.BonusFestival,

		// Cashback Benefits
		CashbackEnabled: mgt.CashbackEnabled,
		CashbackSports:  mgt.CashbackSports,
		CashbackCasino:  mgt.CashbackCasino,
		CashbackFishing: mgt.CashbackFishing,
		CashbackSlot:    mgt.CashbackSlot,
		CashbackLottery: mgt.CashbackLottery,
		CashbackCard:    mgt.CashbackCard,
		CashbackOther:   mgt.CashbackOther,

		// Level Upgrade Conditions
		UpgradeEnable:          mgt.UpgradeEnable,
		UpgradeBettingAmount:   mgt.UpgradeBettingAmount,
		UpgradeDepositAmount:   mgt.UpgradeDepositAmount,
		UpgradeCalculationType: mgt.UpgradeCalculationType,
		UpgradeDays:            mgt.UpgradeDays,
		UpgradeConditionType:   mgt.UpgradeConditionType,

		// Level Downgrade Conditions
		DowngradeEnable:          mgt.DowngradeEnable,
		DowngradeBettingAmount:   mgt.DowngradeBettingAmount,
		DowngradeDepositAmount:   mgt.DowngradeDepositAmount,
		DowngradeCalculationType: mgt.DowngradeCalculationType,
		DowngradeDays:            mgt.DowngradeDays,
		DowngradeConditionType:   mgt.DowngradeConditionType,

		Status:    mgt.Status,
		CreatedAt: mgt.CreatedAt,
		UpdatedAt: mgt.UpdatedAt,
	}
}

// validateMemberGroupTypeRequest validates member group type creation request
func validateMemberGroupTypeRequest(req CreateMemberGroupTypeRequest) error {
	if req.Name == "" {
		return errors.NewValidationError("name is required")
	}
	if req.BadgeBgColor == "" {
		return errors.NewValidationError("badge_bg_color is required")
	}
	if req.BadgeBorderColor == "" {
		return errors.NewValidationError("badge_border_color is required")
	}
	if req.UpgradeDays < 1 || req.UpgradeDays > 90 {
		return errors.NewValidationError("upgrade_days must be between 1 and 90")
	}
	if req.DowngradeDays < 1 || req.DowngradeDays > 90 {
		return errors.NewValidationError("downgrade_days must be between 1 and 90")
	}
	return nil
}

// validateUpdateMemberGroupTypeRequest validates member group type update request
func validateUpdateMemberGroupTypeRequest(req UpdateMemberGroupTypeRequest) error {
	if req.Name == "" {
		return errors.NewValidationError("name is required")
	}
	if req.BadgeBgColor == "" {
		return errors.NewValidationError("badge_bg_color is required")
	}
	if req.BadgeBorderColor == "" {
		return errors.NewValidationError("badge_border_color is required")
	}
	if req.UpgradeDays < 1 || req.UpgradeDays > 90 {
		return errors.NewValidationError("upgrade_days must be between 1 and 90")
	}
	if req.DowngradeDays < 1 || req.DowngradeDays > 90 {
		return errors.NewValidationError("downgrade_days must be between 1 and 90")
	}
	return nil
}

// FileUploadResponse represents file upload response for member group type
type FileUploadResponse struct {
	FileUrl string `json:"fileUrl"`
}

// DeleteFileRequest represents file deletion request for member group type
type DeleteFileRequest struct {
	FileUrl string `json:"fileUrl" validate:"required"`
}
