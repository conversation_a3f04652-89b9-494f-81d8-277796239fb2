package dashboard

import "time"

// Dashboard รวมข้อมูลทั้งหมดสำหรับหน้า dashboard
type Dashboard struct {
	Daily                []DailyData         `json:"daily"`
	Transactions         Transaction         `json:"transactions"`
	ActiveBets           ActiveBet           `json:"active_bets"`
	PartnerGroups        []PartnerGroup      `json:"partner_groups"`
	Turnovers            Turnover            `json:"turnovers"`
	NetTurns             NetTurn             `json:"net_turns"`
	GameTypes            GameTypeData        `json:"game_types"`
	CommissionAffiliates CommissionAffiliate `json:"commission_affiliates"`
}

// DailyData ข้อมูลสรุปรายวัน
type DailyData struct {
	NewMember                    int     `json:"new_member"`
	FirstDeposit                 int     `json:"first_deposit"`
	ActiveMember                 int     `json:"active_member"`
	ActiveBet                    int     `json:"active_bet"`
	MemberBalance                string  `json:"member_balance"`
	Bonus                        string  `json:"bonus"`
	Expense                      string  `json:"expense"`
	Deposit                      string  `json:"deposit"`
	Withdraw                     string  `json:"withdraw"`
	Profit                       string  `json:"profit"`
	Jackpot                      string  `json:"jackpot"`
	WinLose                      string  `json:"win_lose"`
	Revenue                      string  `json:"revenue"`
	Balance                      string  `json:"balance"`
	Note                         string  `json:"note"`
	Commission                   string  `json:"commission"`
	Affiliate                    string  `json:"affiliate"`
	DepositCredit                string  `json:"deposit_credit"`
	WithdrawCredit               string  `json:"withdraw_credit"`
	WithdrawSma                  string  `json:"withdraw_sma"`
	WithdrawFinance              string  `json:"withdraw_finance"`
	NewMemberPercent             string  `json:"new_member_percent"`
	FirstDepositPercent          string  `json:"first_deposit_percent"`
	ActiveMemberPercent          float64 `json:"active_member_percent"`
	ActiveBetPercent             int     `json:"active_bet_percent"`
	MemberBalancePercent         string  `json:"member_balance_percent"`
	BonusPercent                 string  `json:"bonus_percent"`
	ExpensePercent               string  `json:"expense_percent"`
	DepositPercent               string  `json:"deposit_percent"`
	WithdrawPercent              string  `json:"withdraw_percent"`
	ProfitPercent                string  `json:"profit_percent"`
	JackpotPercent               string  `json:"jackpot_percent"`
	WinLosePercent               string  `json:"win_lose_percent"`
	RevenuePercent               string  `json:"revenue_percent"`
	BalancePercent               string  `json:"balance_percent"`
	NotePercent                  string  `json:"note_percent"`
	CommissionPercent            string  `json:"commission_percent"`
	AffiliatePercent             string  `json:"affiliate_percent"`
	DepositCreditPercent         string  `json:"deposit_credit_percent"`
	WithdrawCreditPercent        string  `json:"withdraw_credit_percent"`
	WithdrawSmaPercent           string  `json:"withdraw_sma_percent"`
	WithdrawFinancePercent       string  `json:"withdraw_finance_percent"`
	AgentStatsAppDownload        int     `json:"agent_stats_app_download"`
	AgentStatsAppDownloadPercent int     `json:"agent_stats_app_download_percen"`
	AgentStatsAppActive          int     `json:"agent_stats_app_active"`
	AgentStatsAppActivePercent   int     `json:"agent_stats_app_active_percen"`
}

// Transaction ข้อมูลธุรกรรม (เป็น object ที่มี array ข้างใน)
type Transaction struct {
	Deposit                []int     `json:"deposit"`
	Withdraw               []int     `json:"withdraw"`
	DepositAmount          []float64 `json:"deposit_amount"`
	WithdrawAmount         []float64 `json:"withdraw_amount"`
	DepositTotal           float64   `json:"deposit_total"`
	WithdrawTotal          float64   `json:"withdraw_total"`
	DepositCount           int       `json:"deposit_count"`
	WithdrawCount          int       `json:"withdraw_count"`
	RegisterDeposit        []int     `json:"register_deposit"`
	RegisterDepositAverage int       `json:"register_deposit_average"`
	Title                  []string  `json:"title"`
}

// ActiveBet ข้อมูลการเดิมพันที่ยังใช้งาน (เป็น object ที่มี array ข้างใน)
type ActiveBet struct {
	ActiveBet              []int    `json:"active_bet"`
	ActiveBetAverage       int      `json:"active_bet_average"`
	Register               []int    `json:"register"`
	RegisterAverage        int      `json:"register_average"`
	RegisterDeposit        []int    `json:"register_deposit"`
	RegisterDepositAverage int      `json:"register_deposit_average"`
	Title                  []string `json:"title"`
}

// PartnerGroup ข้อมูลกลุ่มพาร์ทเนอร์
type PartnerGroup struct {
	ID           *int   `json:"id"`
	Title        string `json:"title,omitempty"`
	Name         string `json:"name"`
	Qty          string `json:"qty"`
	FirstDeposit string `json:"first_deposit"`
	TotalDeposit string `json:"total_deposit"`
}

// Turnover ข้อมูลยอดหมุนเวียน (เป็น object ที่มี array ข้างใน)
type Turnover struct {
	Turnover     []float64 `json:"turnover"`
	Percent      []float64 `json:"percent"`
	Title        []string  `json:"title"`
	Total        float64   `json:"total"`
	ProviderID   []int     `json:"provider_id"`
	ProviderName []string  `json:"provider_name"`
}

// NetTurn ข้อมูลยอดสุทธิ (เป็น object ที่มี array ข้างใน)
type NetTurn struct {
	NetTurn      []float64 `json:"netturn"`
	Percent      []float64 `json:"percent"`
	Title        []string  `json:"title"`
	Total        float64   `json:"total"`
	ProviderID   []int     `json:"provider_id"`
	ProviderName []string  `json:"provider_name"`
}

// BotSuccess ข้อมูลรวมทั้งหมดของ bot
type BotSuccess struct {
	BotSuccessData   BotSuccessData `json:"bot_success"`
	AdminApprove     AdminApprove   `json:"admin_approve"`
	BotDepositHour   BotDepositData `json:"bot_deposit_hour"`
	BotDepositMinute BotDepositData `json:"bot_deposit_minute"`
}

// BotSuccessData ข้อมูลความสำเร็จของ bot
type BotSuccessData struct {
	Data  []BotSuccessItem `json:"data"`
	Title []string         `json:"title"`
}

// BotSuccessItem รายการข้อมูลความสำเร็จของ bot
type BotSuccessItem struct {
	BankID  int    `json:"bank_id"`
	Minute  int    `json:"minute"`
	RoundNo int    `json:"round_no"`
	Count   int    `json:"count"`
	Bank    string `json:"bank"`
}

// AdminApprove ข้อมูลการอนุมัติของแอดมิน
type AdminApprove struct {
	Data  []interface{} `json:"data"` // ตอนนี้เป็น array ว่าง
	Title []string      `json:"title"`
}

// BotDepositData ข้อมูลการฝากเงินของ bot (ทั้ง hour และ minute)
type BotDepositData struct {
	Title     []string  `json:"title"`
	RoundNo   []int     `json:"round_no"`
	Count     []int     `json:"count"`
	Amount    []float64 `json:"amount"`
	SumCount  int       `json:"sum_count"`
	SumAmount float64   `json:"sum_amount"`
}

// GameTypeData ข้อมูลประเภทเกม (สำหรับ dashboard)
type GameTypeData struct {
	GroupedBySlug map[string]GameTypeGroup `json:"grouped_by_slug"`
	WinLostTotal  float64                  `json:"winLost_total"`
	TurnoverTotal float64                  `json:"turnover_total"`
}

// GameTypeItem รายการข้อมูลประเภทเกมแต่ละรายการ
type GameTypeItem struct {
	GameTypeID int       `json:"game_type_id"`
	ProviderID int       `json:"provider_id"`
	WinLose    string    `json:"win_lose"`
	Turnover   string    `json:"turnover"`
	NetTurn    string    `json:"net_turn"`
	GameType   *GameType `json:"game_type,omitempty"`
	Provider   *Provider `json:"provider,omitempty"`
}

// GameTypeGroup ข้อมูลประเภทเกมที่ถูกจัดกลุ่มตาม slug
type GameTypeGroup struct {
	Slug          string         `json:"slug"`
	Name          string         `json:"name"`
	TotalWinLose  float64        `json:"total_win_lose"`
	TotalTurnover float64        `json:"total_turnover"`
	TotalNetTurn  float64        `json:"total_net_turn"`
	Items         []GameTypeItem `json:"items"`
}

// CommissionAffiliate ข้อมูลค่าคอมมิชชั่นแอฟฟิเลียท
type CommissionAffiliate struct {
	GroupedBySlug        map[string]CommissionAffiliateGroup `json:"grouped_by_slug"`
	CommissionTotal      float64                             `json:"commission_total"`
	AffiliateLevel1Total float64                             `json:"affiliate_level_1_total"`
	AffiliateLevel2Total float64                             `json:"affiliate_level_2_total"`
}

// CommissionAffiliateGroup ข้อมูลค่าคอมมิชชั่นแอฟฟิเลียทที่ถูกจัดกลุ่มตาม slug
type CommissionAffiliateGroup struct {
	Slug                 string                    `json:"slug"`
	Name                 string                    `json:"name"`
	TotalCommission      float64                   `json:"total_commission"`
	TotalAffiliateLevel1 float64                   `json:"total_affiliate_level_1"`
	TotalAffiliateLevel2 float64                   `json:"total_affiliate_level_2"`
	Items                []CommissionAffiliateItem `json:"items"`
}

// CommissionAffiliateItem รายการข้อมูลค่าคอมมิชชั่นแอฟฟิเลียท
type CommissionAffiliateItem struct {
	GameTypeID      int       `json:"game_type_id"`
	ProviderID      int       `json:"provider_id"`
	Commission      string    `json:"commission"`
	AffiliateLevel1 string    `json:"affiliate_level_1"`
	AffiliateLevel2 string    `json:"affiliate_level_2"`
	GameType        *GameType `json:"game_type,omitempty"`
	Provider        *Provider `json:"provider,omitempty"`
}

// GameType ข้อมูลประเภทเกม (สำหรับ join)
type GameType struct {
	ID        int        `json:"id"`
	Name      string     `json:"name"`
	Slug      string     `json:"slug"`
	DeletedAt *time.Time `json:"deleted_at"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
}

// Provider ข้อมูลผู้ให้บริการเกม (สำหรับ join)
type Provider struct {
	ID         int        `json:"id"`
	Logo       string     `json:"logo"`
	Code       string     `json:"code"`
	Name       string     `json:"name"`
	Status     string     `json:"status"`
	GameTypeID int        `json:"game_type_id"`
	DeletedAt  *time.Time `json:"deleted_at"`
	CreatedAt  time.Time  `json:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at"`
}

// DashboardRequest request structure สำหรับ API
type DashboardRequest struct {
	DateFrom string `form:"date_from" json:"date_from" binding:"required" validate:"required,datetime=2006-01-02"`
	DateTo   string `form:"date_to" json:"date_to" binding:"required" validate:"required,datetime=2006-01-02"`
	Type     string `form:"type" json:"type" binding:"required" validate:"required,oneof=daily"`
}

// DashboardResponse response structure สำหรับ API
type DashboardResponse struct {
	Code    int       `json:"code"`
	Success bool      `json:"success"`
	Message string    `json:"message"`
	Data    Dashboard `json:"data"`
}

// BotSuccessRequest request structure สำหรับ Bot Success API
type BotSuccessRequest struct {
	RoundDate string `form:"round_date" json:"round_date" binding:"required" validate:"required,datetime=2006-01-02"`
}

// BotSuccessResponse response structure สำหรับ Bot Success API
type BotSuccessResponse struct {
	Code    int        `json:"code"`
	Success bool       `json:"success"`
	Message string     `json:"message"`
	Data    BotSuccess `json:"data"`
}
