package user_transaction

import (
	"blacking-api/internal/domain/request"
	"time"
)

// Transaction Direction Constants
const (
	USER_TRANSACTION_DIRECTION_DEPOSIT  = 1 // ฝาก (Deposit)
	USER_TRANSACTION_DIRECTION_WITHDRAW = 2 // ถอน (Withdraw)
)

// Transaction Status Constants
const (
	USER_TRANSACTION_STATUS_WEBSITE_DEPOSIT      = 1  // ฝากเว็บ (Website Deposit)
	USER_TRANSACTION_STATUS_WAITING_FOR_TRANSFER = 2  // รอโอน (Waiting for Transfer)
	USER_TRANSACTION_STATUS_WAITING_VERIFICATION = 3  // รอตรวจสอบ (Waiting for Verification)
	USER_TRANSACTION_STATUS_SLIP_PENDING         = 4  // สลิปรอดำเนินการ (Slip Pending)
	USER_TRANSACTION_STATUS_IN_PROGRESS          = 5  // กำลังดำเนินการ (In Progress)
	USER_TRANSACTION_STATUS_OVER_LIMIT           = 6  // เกินวงเงิน (Over Limit)
	USER_TRANSACTION_STATUS_VIP_APPROVED         = 7  // อนุมัติ VIP (VIP Approved)
	USER_TRANSACTION_STATUS_WAITING_FOR_MATCHING = 8  // รอจับคู่ (Waiting for Matching)
	USER_TRANSACTION_STATUS_IN_QUEUE             = 9  // รอคิว (In Queue)
	USER_TRANSACTION_STATUS_DUPLICATE            = 10 // ทำซ้ำ (Duplicate)
	USER_TRANSACTION_STATUS_SUCCESS              = 11 // สำเร็จ (Success)
	USER_TRANSACTION_STATUS_CANCELLED            = 12 // ยกเลิก (Cancelled)
)

// Transaction Type Constants
const (
	USER_TRANSACTION_TYPE_DEPOSIT               = 1  // ฝาก (Deposit)
	USER_TRANSACTION_TYPE_WITHDRAW              = 2  // ถอน (Withdraw)
	USER_TRANSACTION_TYPE_BONUS                 = 3  // โบนัส (Bonus)
	USER_TRANSACTION_TYPE_PROMOTION_RETURN_LOSS = 4  // แจกโบนัสฟรี คืนยอดเสีย (Promotion Return Loss)
	USER_TRANSACTION_TYPE_AFFILIATE_INCOME      = 5  // โบนัสรายได้แนะนำเพื่อน (Affiliate Income)
	USER_TRANSACTION_TYPE_ALLIANCE_INCOME       = 6  // โบนัสรายได้พันธมิตร (Alliance Income)
	USER_TRANSACTION_TYPE_TAKE_CREDIT_BACK      = 7  // ดึงเครดิตกลับ (Take Credit Back)
	USER_TRANSACTION_TYPE_DAILY_ACTIVITY_BONUS  = 8  // โบนัสกิจกรรมรายวัน (Daily Activity Bonus)
	USER_TRANSACTION_TYPE_LUCKY_WHEEL           = 9  // เครดิตจากกิจกรรมกงล้อนำโชค (Lucky Wheel Credit)
	USER_TRANSACTION_TYPE_PROMOTION_WEB         = 10 // โปรโมชั่นเว็บ (Web Promotion)
	USER_TRANSACTION_TYPE_COUPON_CASH           = 11 // คูปองเงินสด (Cash Coupon)
	USER_TRANSACTION_TYPE_LOTTERY               = 12 // เครดิตลอตเตอรี่ (Lottery Credit)
	USER_TRANSACTION_TYPE_PROMOTION_RETURN_TURN = 13 // แจกโบนัสฟรี คืนยอด commission (Promotion Return Commission)
	USER_TRANSACTION_TYPE_CANCEL_CREDIT         = 14 // ยกเลิกเติมเครดิต (Cancel Credit)
)

// UserTransaction represents the main user transaction entity
type UserTransaction struct {
	ID                int64      `json:"id" db:"id"`
	DirectionID       int        `json:"directionId" db:"direction_id"`
	TypeID            int        `json:"typeId" db:"type_id"`
	MemberID          int        `json:"memberId" db:"member_id"`
	BankingID         int        `json:"bankingId" db:"banking_id"`
	TransferBankingID *int       `json:"transferBankingId" db:"transfer_banking_id"`
	RefID             string     `json:"refId" db:"ref_id"`
	Detail            *string    `json:"detail" db:"detail"`
	Date              time.Time  `json:"date" db:"date"`
	CreditAmount      float64    `json:"creditAmount" db:"credit_amount"`
	CreditBefore      float64    `json:"creditBefore" db:"credit_before"`
	CreditAfter       float64    `json:"creditAfter" db:"credit_after"`
	CreditBack        float64    `json:"creditBack" db:"credit_back"`
	BonusAmount       float64    `json:"bonusAmount" db:"bonus_amount"`
	TransferAt        *time.Time `json:"transferAt" db:"transfer_at"`
	ConfirmAdminID    *int       `json:"confirmAdminId" db:"confirm_admin_id"`
	PromotionID       *int64     `json:"promotionId" db:"promotion_id"`
	StatusID          int        `json:"statusId" db:"status_id"`
	CreatedAt         time.Time  `json:"createdAt" db:"created_at"`
	UpdatedAt         time.Time  `json:"updatedAt" db:"updated_at"`

	// Joined fields for responses
	DirectionName       *string `json:"directionName" db:"direction_name"`
	TypeName            *string `json:"typeName" db:"type_name"`
	StatusName          *string `json:"statusName" db:"status_name"`
	StatusDetail        *string `json:"statusDetail" db:"status_detail"`
	Username            *string `json:"username" db:"username"`
	MemberCode          *string `json:"memberCode" db:"-"` // Note: member_code column does not exist in database
	BankingName         *string `json:"bankingName" db:"banking_name"`
	TransferBankingName *string `json:"transferBankingName" db:"transfer_banking_name"`
	ConfirmAdminName    *string `json:"confirmAdminName" db:"confirm_admin_name"`
}

// Request DTOs
type CreateUserTransactionAdminDepositRequest struct {
	PhoneOrUsername  string  `json:"phoneOrUsername" validate:"required"`
	DepositAmount    float64 `json:"amount" validate:"required,gt=0"`
	DepositAccountId int     `json:"depositAccountId" validate:"required,gt=0"`
	Date             string  `json:"date" validate:"required"`
	Time             string  `json:"time" validate:"required"`
	PromotionID      *int64  `json:"promotionId"`
	Description      string  `json:"description"`
	SlipUrl          string  `json:"slipUrl" validate:"required,url"` // Deposit slip image URL
	CreatedByAdminID int64   `json:"-"`                               // Set internally from context
}

// CreateUserTransactionMemberDepositRequest is used when members create their own deposits
type CreateUserTransactionMemberDepositRequest struct {
	MemberID         int64   `json:"-"`                                         // Set internally from auth context, not from request
	DepositAmount    float64 `json:"amount" validate:"required,gt=0"`           // Deposit amount
	DepositAccountId int     `json:"depositAccountId" validate:"required,gt=0"` // Bank account ID for deposit
	Date             string  `json:"date" validate:"required"`                  // Deposit date (YYYY-MM-DD)
	Time             string  `json:"time" validate:"required"`                  // Deposit time (HH:MM:SS)
	PromotionID      *int64  `json:"promotionId,omitempty"`                     // Optional promotion ID
	Description      string  `json:"description,omitempty"`                     // Optional description/notes
	SlipUrl          string  `json:"slipUrl" validate:"required,url"`           // Deposit slip image URL
	Reference        string  `json:"reference,omitempty"`                       // Optional transaction reference
}

type CreateUserTransactionWithdrawRequest struct {
	PhoneOrUsername string  `json:"phoneOrUsername" validate:"required"`
	CreditAmount    float64 `json:"credit_amount" validate:"required,gt=0"`
	Description     string  `json:"description"`
}

type CreateUserTransactionWebWithdrawRequest struct {
	CreditAmount   float64 `json:"amount" validate:"required,gt=0"`
	WithdrawBankId int     `json:"withdrawBankId" validate:"required,gt=0"`
	AccountNumber  string  `json:"accountNumber" validate:"required"`
	AccountName    string  `json:"accountName" validate:"required"`
	Description    string  `json:"description"`
}

type CreateUserTransactionTransferRequest struct {
	BankingID         int     `json:"banking_id" validate:"required,gt=0"`
	TransferBankingID int     `json:"transfer_banking_id" validate:"required,gt=0"`
	CreditAmount      float64 `json:"credit_amount" validate:"required,gt=0"`
	Description       string  `json:"description"`
}

type CreateUserTransactionWebDepositRequest struct {
	DepositAmount    float64 `json:"amount" validate:"required,gt=0"`
	DepositAccountId int     `json:"depositAccountId" validate:"required,gt=0"`
	Date             string  `json:"date" validate:"required"`
	Time             string  `json:"time" validate:"required"`
	PromotionID      *int64  `json:"promotionId"`
	Description      string  `json:"description"`
	SlipUrl          string  `json:"slipUrl" validate:"required,url"`
}

type UpdateUserTransactionStatusRequest struct {
	StatusID       int    `json:"status_id" validate:"required,gt=0"`
	ConfirmAdminID *int   `json:"confirm_admin_id"`
	Detail         string `json:"detail"`
}

// Pagination and Filter DTOs
type UserTransactionDepositPageRequest struct {
	request.RequestPagination
	StatusID    *int     `form:"statusId"`
	Date        *string  `form:"date"`
	Username    *string  `form:"username"`
	MemberCode  *string  `form:"membercode"`
	UserBankID  *int     `form:"userbankid"`
	BankingID   *int     `form:"bankingid"`
	StartAmount *float64 `form:"startamount"`
	EndAmount   *float64 `form:"endamount"`
	Amount      *float64 `form:"amount"`
	Admin       *string  `form:"admin"`
}

type UserTransactionWithdrawPageRequest struct {
	request.RequestPagination
	StatusID    *int     `form:"statusId"`
	Date        *string  `form:"date"`
	Username    *string  `form:"username"`
	MemberCode  *string  `form:"membercode"`
	UserBankID  *int     `form:"userbankid"`
	BankingID   *int     `form:"bankingid"`
	StartAmount *float64 `form:"startamount"`
	EndAmount   *float64 `form:"endamount"`
	Amount      *float64 `form:"amount"`
	IP          *string  `form:"ip"`
}

type UserTransactionTransferPageRequest struct {
	request.RequestPagination
	StatusID *int     `form:"statusId"`
	Date     *string  `form:"date"`
	Amount   *float64 `form:"amount"`
}

// User-specific transaction list DTOs
type UserTransactionDepositByUserRequest struct {
	request.RequestPagination
	StatusID    *int     `form:"statusId"`
	Date        *string  `form:"date"`
	BankingID   *int     `form:"bankingId"`
	StartAmount *float64 `form:"startAmount"`
	EndAmount   *float64 `form:"endAmount"`
	Amount      *float64 `form:"amount"`
}

type UserTransactionWithdrawByUserRequest struct {
	request.RequestPagination
	StatusID    *int     `form:"statusId"`
	Date        *string  `form:"date"`
	BankingID   *int     `form:"bankingId"`
	StartAmount *float64 `form:"startAmount"`
	EndAmount   *float64 `form:"endAmount"`
	Amount      *float64 `form:"amount"`
}

// Response DTOs
type UserTransactionResponse struct {
	ID                  int64      `json:"id"`
	DirectionID         int        `json:"directionId"`
	DirectionName       string     `json:"directionName"`
	TypeID              int        `json:"typeId"`
	TypeName            string     `json:"typeName"`
	MemberID            int        `json:"memberId"`
	Username            string     `json:"username"`
	MemberCode          string     `json:"memberCode"`
	BankingID           int        `json:"bankingId"`
	BankingName         string     `json:"bankingName"`
	TransferBankingID   *int       `json:"transferBankingId"`
	TransferBankingName *string    `json:"transferBankingName"`
	RefID               string     `json:"refId"`
	Detail              *string    `json:"detail"`
	Date                time.Time  `json:"date"`
	CreditAmount        float64    `json:"creditAmount"`
	CreditBefore        float64    `json:"creditBefore"`
	CreditAfter         float64    `json:"creditAfter"`
	CreditBack          float64    `json:"creditBack"`
	BonusAmount         float64    `json:"bonusAmount"`
	TransferAt          *time.Time `json:"transferAt"`
	ConfirmAdminID      *int       `json:"confirmAdminId"`
	ConfirmAdminName    *string    `json:"confirmAdminName"`
	PromotionID         *int64     `json:"promotionId"`
	StatusID            int        `json:"statusId"`
	StatusName          string     `json:"statusName"`
	StatusDetail        string     `json:"statusDetail"`
	CreatedAt           time.Time  `json:"createdAt"`
	UpdatedAt           time.Time  `json:"updatedAt"`
}

// ToResponse converts UserTransaction to UserTransactionResponse
func (u *UserTransaction) ToResponse() UserTransactionResponse {
	response := UserTransactionResponse{
		ID:                u.ID,
		DirectionID:       u.DirectionID,
		TypeID:            u.TypeID,
		MemberID:          u.MemberID,
		BankingID:         u.BankingID,
		TransferBankingID: u.TransferBankingID,
		RefID:             u.RefID,
		Detail:            u.Detail,
		Date:              u.Date,
		CreditAmount:      u.CreditAmount,
		CreditBefore:      u.CreditBefore,
		CreditAfter:       u.CreditAfter,
		CreditBack:        u.CreditBack,
		BonusAmount:       u.BonusAmount,
		TransferAt:        u.TransferAt,
		ConfirmAdminID:    u.ConfirmAdminID,
		PromotionID:       u.PromotionID,
		StatusID:          u.StatusID,
		CreatedAt:         u.CreatedAt,
		UpdatedAt:         u.UpdatedAt,
	}

	// Handle nullable joined fields
	if u.DirectionName != nil {
		response.DirectionName = *u.DirectionName
	}
	if u.TypeName != nil {
		response.TypeName = *u.TypeName
	}
	if u.Username != nil {
		response.Username = *u.Username
	}
	if u.MemberCode != nil {
		response.MemberCode = *u.MemberCode
	}
	if u.BankingName != nil {
		response.BankingName = *u.BankingName
	}
	if u.TransferBankingName != nil {
		response.TransferBankingName = u.TransferBankingName
	}
	if u.ConfirmAdminName != nil {
		response.ConfirmAdminName = u.ConfirmAdminName
	}
	if u.StatusName != nil {
		response.StatusName = *u.StatusName
	}
	if u.StatusDetail != nil {
		response.StatusDetail = *u.StatusDetail
	}

	return response
}
