package summary_bet_by_member

// SummaryBetByMember ข้อมูลรายงานสรุปเดิมพันแยกตาม Member
type SummaryBetByMember struct {
	Data       []SummaryBetByMemberItem `json:"data"`
	Pagination Pagination               `json:"pagination"`
}

// SummaryBetByMemberItem รายการข้อมูลรายงานสรุปเดิมพันแยกตาม Member
type SummaryBetByMemberItem struct {
	ID struct {
		UserID int    `json:"user_id"`
		Phone  string `json:"phone"`
	} `json:"_id"`
	CommissionAmount         float64 `json:"commission_amount"`
	CommissionNetTurnover    float64 `json:"commission_net_turnover"`
	CountUser                int     `json:"count_user"`
	NetTurnOver              float64 `json:"net_turn_over"`
	TurnOver                 float64 `json:"turn_over"`
	WinLoseNotIncludeJackpot float64 `json:"win_lose_not_include_jackpot"`
	Jackpot                  float64 `json:"jackpot"`
	WinLose                  float64 `json:"win_lose"`
	Balance                  string  `json:"balance"`
	UserCode                 string  `json:"user_code"`
}

// SummaryBetByMemberDetail ข้อมูลรายละเอียดรายงานสรุปเดิมพันแยกตาม Member
type SummaryBetByMemberDetail struct {
	Data       []SummaryBetByMemberDetailItem `json:"data"`
	Pagination Pagination                     `json:"pagination"`
}

// SummaryBetByMemberDetailItem รายการย่อยในรายละเอียด
type SummaryBetByMemberDetailItem struct {
	ID struct {
		UserID             int    `json:"user_id"`
		Phone              string `json:"phone"`
		GamingProviderCode string `json:"gaming_provider_code"`
		GameTypeID         int    `json:"game_type_id"`
	} `json:"_id"`
	TurnOver                 float64 `json:"turn_over"`
	NetTurnOver              float64 `json:"net_turn_over"`
	Jackpot                  float64 `json:"jackpot"`
	WinLoseNotIncludeJackpot float64 `json:"win_lose_not_include_jackpot"`
	WinLose                  float64 `json:"win_lose"`
	CommissionNetTurnover    float64 `json:"commission_net_turnover"`
	CommissionAmount         float64 `json:"commission_amount"`
}

// Pagination ข้อมูล pagination
type Pagination struct {
	PerPage     string  `json:"perPage"`
	CurrentPage string  `json:"currentPage"`
	From        int     `json:"from"`
	To          int     `json:"to"`
	Total       int     `json:"total"`
	LastPage    int     `json:"lastPage"`
	PrevPage    *string `json:"prevPage"`
	NextPage    *string `json:"nextPage"`
}

// SummaryBetByMemberFilterRequest request structure สำหรับ filter ข้อมูล
type SummaryBetByMemberFilterRequest struct {
	DateRegister *string `form:"date_register" json:"date_register"`
	DateSearch   *string `form:"date_search" json:"date_search"`
	FirstDeposit *string `form:"first_deposit" json:"first_deposit"`
	GameTypeID   *int    `form:"game_type_id" json:"game_type_id"`
	PartnerID    *int    `form:"partner_id" json:"partner_id"`
	Phone        *string `form:"phone" json:"phone"`
	ProviderCode *string `form:"provider_code" json:"provider_code"`
	ShowAll      *int    `form:"showAll" json:"showAll"`
	UserCode     *string `form:"user_code" json:"user_code"`
	UserGroups   *string `form:"user_groups" json:"user_groups"`
	Username     *string `form:"username" json:"username"`
	Sort         string  `form:"sort" json:"sort"`
	SortBy       string  `form:"sortBy" json:"sortBy"`
}

// SummaryBetByMemberDetailRequest request structure สำหรับ Detail API
type SummaryBetByMemberDetailRequest struct {
	MemberID int `uri:"member_id" json:"member_id" binding:"required"`
}

// SummaryBetByMemberResponse response structure สำหรับ API
type SummaryBetByMemberResponse struct {
	Code    int                `json:"code"`
	Success bool               `json:"success"`
	Message string             `json:"message"`
	Data    SummaryBetByMember `json:"data"`
}

// SummaryBetByMemberDetailResponse response structure สำหรับ Detail API
type SummaryBetByMemberDetailResponse struct {
	Code    int                      `json:"code"`
	Success bool                     `json:"success"`
	Message string                   `json:"message"`
	Data    SummaryBetByMemberDetail `json:"data"`
}
