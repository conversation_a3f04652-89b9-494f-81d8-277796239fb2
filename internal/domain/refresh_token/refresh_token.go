package refresh_token

import (
	"crypto/rand"
	"encoding/hex"
	"time"

	"blacking-api/pkg/errors"
)

// UserType represents the type of user (admin or member)
type UserType string

const (
	UserTypeAdmin  UserType = "admin"
	UserTypeMember UserType = "member"
)

// RefreshToken represents a refresh token entity
type RefreshToken struct {
	ID        int       `json:"id" db:"id"`
	Token     string    `json:"token" db:"token"`
	UserID    *int      `json:"user_id" db:"user_id"`
	MemberID  *int      `json:"member_id" db:"member_id"`
	UserType  UserType  `json:"user_type" db:"user_type"`
	ExpiresAt time.Time `json:"expires_at" db:"expires_at"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// CreateRefreshTokenRequest represents the request to create a refresh token
type CreateRefreshTokenRequest struct {
	UserID   *int     `json:"user_id"`
	MemberID *int     `json:"member_id"`
	UserType UserType `json:"user_type" validate:"required,oneof=admin member"`
}

// RefreshTokenResponse represents the response structure for refresh token
type RefreshTokenResponse struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
}

// NewRefreshToken creates a new refresh token
func NewRefreshToken(req CreateRefreshTokenRequest) (*RefreshToken, error) {
	if err := validateCreateRequest(req); err != nil {
		return nil, err
	}

	// Generate secure random token
	token, err := generateSecureToken()
	if err != nil {
		return nil, errors.NewInternalError("failed to generate refresh token")
	}

	now := time.Now()
	expiresAt := now.Add(7 * 24 * time.Hour) // 7 days

	refreshToken := &RefreshToken{
		Token:     token,
		UserID:    req.UserID,
		MemberID:  req.MemberID,
		UserType:  req.UserType,
		ExpiresAt: expiresAt,
		CreatedAt: now,
		UpdatedAt: now,
	}

	return refreshToken, nil
}

// ToResponse converts RefreshToken to RefreshTokenResponse
func (rt *RefreshToken) ToResponse() RefreshTokenResponse {
	return RefreshTokenResponse{
		Token:     rt.Token,
		ExpiresAt: rt.ExpiresAt,
	}
}

// IsExpired checks if the refresh token is expired
func (rt *RefreshToken) IsExpired() bool {
	return time.Now().After(rt.ExpiresAt)
}

// IsValid checks if the refresh token is valid (not expired)
func (rt *RefreshToken) IsValid() bool {
	return !rt.IsExpired()
}

// validateCreateRequest validates the create refresh token request
func validateCreateRequest(req CreateRefreshTokenRequest) error {
	if req.UserType == "" {
		return errors.NewValidationError("user_type is required")
	}

	if req.UserType != UserTypeAdmin && req.UserType != UserTypeMember {
		return errors.NewValidationError("user_type must be 'admin' or 'member'")
	}

	if req.UserType == UserTypeAdmin {
		if req.UserID == nil {
			return errors.NewValidationError("user_id is required for admin type")
		}
		if req.MemberID != nil {
			return errors.NewValidationError("member_id must be null for admin type")
		}
	}

	if req.UserType == UserTypeMember {
		if req.MemberID == nil {
			return errors.NewValidationError("member_id is required for member type")
		}
		if req.UserID != nil {
			return errors.NewValidationError("user_id must be null for member type")
		}
	}

	return nil
}

// generateSecureToken generates a cryptographically secure random token
func generateSecureToken() (string, error) {
	bytes := make([]byte, 32) // 256 bits
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}
