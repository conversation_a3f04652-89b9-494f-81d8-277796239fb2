package deposit_account

import (
	"blacking-api/internal/domain/request"
	"blacking-api/pkg/errors"
	"time"
)

type DepositAccount struct {
	ID                    int64     `json:"id" db:"id"`
	BankingID             int64     `json:"banking_id" db:"banking_id"`
	BankingName           string    `json:"banking_name" db:"banking_name"`
	BankingImageUrl       string    `json:"banking_image_url" db:"banking_image_url"`
	PaymentMethodID       int64     `json:"payment_method_id" db:"payment_method_id"`
	AlgorithmID           *int64    `json:"algorithm_id" db:"algorithm_id"`
	AlgorithmName         *string   `json:"algorithm_name" db:"algorithm_name"`
	AutoBotID             int64     `json:"auto_bot_id" db:"auto_bot_id"`
	AccountName           string    `json:"account_name" db:"account_name"`
	AccountNameDisplay    string    `json:"account_name_display" db:"account_name_display"`
	AccountNumber         string    `json:"account_number" db:"account_number"`
	PhoneNumber           string    `json:"phone_number" db:"phone_number"`
	AutoTransfer          bool      `json:"auto_transfer" db:"auto_transfer"`
	PushBulletNickname    *string   `json:"push_bullet_nickname" db:"push_bullet_nickname"`
	PushBulletToken       *string   `json:"push_bullet_token" db:"push_bullet_token"`
	Active                bool      `json:"active" db:"active"`
	Inactive              bool      `json:"inactive" db:"inactive"`
	IsQRPayment           bool      `json:"is_qr_payment" db:"is_qr_payment"`
	AmountTriggerTransfer *float64  `json:"amount_trigger_transfer" db:"amount_trigger_transfer"`
	AmountTransfer        *float64  `json:"amount_transfer" db:"amount_transfer"`
	AccountTransferID     *int64    `json:"account_transfer_id" db:"account_transfer_id"`
	AccountTransferTypeID *int64    `json:"account_transfer_type_id" db:"account_transfer_type_id"`
	CreatedAt             time.Time `json:"created_at" db:"created_at"`
	UpdatedAt             time.Time `json:"updated_at" db:"updated_at"`
}

type DepositAccountRequest struct {
	AccountName           string   `json:"accountName" validate:"required,min=1,max=255"`
	AccountNameDisplay    string   `json:"accountNameDisplay" validate:"required,min=1,max=255"`
	AccountNumber         string   `json:"accountNumber" validate:"required,min=13,max=13"`
	BankingID             uint     `json:"bankingId" validate:"required,min=1"`
	PhoneNumber           string   `json:"phoneNumber" validate:"required,min=10,max=10"`
	AutoTransfer          bool     `json:"autoTransfer" default:"false"`
	PaymentMethodID       uint     `json:"paymentMethodId" validate:"required,min=1"`
	QRCodePayment         bool     `json:"qrCodePayment" default:"false"`
	AmountTriggerTransfer *float64 `json:"amountTriggerTransfer"`
	AmountTransfer        *float64 `json:"amountTransfer"`
	AccountTransferID     *uint    `json:"accountTransferId"`
	AccountTransferTypeID *uint    `json:"accountTransferTypeId"`
}

type DepositAccountUpdateRequest struct {
	AccountName           string   `json:"accountName" validate:"required,min=1,max=255"`
	AccountNameDisplay    string   `json:"accountNameDisplay" validate:"required,min=1,max=255"`
	AccountNumber         string   `json:"accountNumber" validate:"required,min=13,max=13"`
	BankingID             uint     `json:"bankingId" validate:"required,min=1"`
	PhoneNumber           string   `json:"phoneNumber" validate:"required,min=10,max=10"`
	AutoTransfer          bool     `json:"autoTransfer" default:"false"`
	PaymentMethodID       uint     `json:"paymentMethodId" validate:"required,min=1"`
	QRCodePayment         bool     `json:"qrCodePayment" default:"false"`
	AmountTriggerTransfer *float64 `json:"amountTriggerTransfer"`
	AmountTransfer        *float64 `json:"amountTransfer"`
	AccountTransferID     *uint    `json:"accountTransferId"`
	AccountTransferTypeID *uint    `json:"accountTransferTypeId"`
}

type DepositAccountSettingAlgorithm struct {
	AlgorithmID        uint   `json:"algorithmId" validate:"required,min=1"`
	PushBulletNickname string `json:"pushBulletNickname" validate:"required,min=1,max=255"`
	PushBulletToken    string `json:"pushBulletToken" validate:"required,min=1,max=255"`
}

type DepositAccountListResponse struct {
	ID                    int64     `json:"id"`
	BankingID             int64     `json:"bankingId"`
	BankingName           string    `json:"bankingName"`
	BankingImageUrl       string    `json:"bankingImageUrl"`
	AlgorithmID           *int64    `json:"algorithmId"`
	AlgorithmName         *string   `json:"algorithm_name"`
	AutoBotID             int64     `json:"autoBotId"`
	AccountName           string    `json:"accountName"`
	AccountNameDisplay    string    `json:"accountNameDisplay"`
	AccountNumber         string    `json:"accountNumber"`
	AutoTransfer          bool      `json:"autoTransfer"`
	QRCodePayment         bool      `json:"qrCodePayment"`
	AmountTriggerTransfer *float64  `json:"amountTriggerTransfer"`
	AmountTransfer        *float64  `json:"amountTransfer"`
	AccountTransferID     *int64    `json:"accountTransferId"`
	AccountTransferTypeID *int64    `json:"accountTransferTypeId"`
	CreatedAt             time.Time `json:"createdAt"`
	UpdatedAt             time.Time `json:"updatedAt"`
}

type DepositAccountByIdResponse struct {
	ID                    int64    `json:"id"`
	AccountName           string   `json:"accountName"`
	AccountNameDisplay    string   `json:"accountNameDisplay"`
	AccountNumber         string   `json:"accountNumber"`
	BankingID             int64    `json:"bankingId"`
	PhoneNumber           string   `json:"phoneNumber"`
	AutoTransfer          bool     `json:"autoTransfer"`
	PaymentMethodID       int64    `json:"paymentMethodId"`
	QRCodePayment         bool     `json:"qrCodePayment"`
	AmountTriggerTransfer *float64 `json:"amountTriggerTransfer"`
	AmountTransfer        *float64 `json:"amountTransfer"`
	AccountTransferID     *int64   `json:"accountTransferId"`
	AccountTransferTypeID *int64   `json:"accountTransferTypeId"`
}

type DepositAccountSettingAlgorithmResponse struct {
	ID                 int64  `json:"id"`
	AlgorithmID        uint   `json:"algorithmId"`
	PushBulletNickname string `json:"pushBulletNickname"`
	PushBulletToken    string `json:"pushBulletToken"`
}

type DepositAccountSearchRequest struct {
	request.RequestPagination
	AccountName *string `form:"name"`
	BankingID   *int64  `form:"banking"`
	AlgorithmID *int64  `form:"algorithm"`
	AutoBotID   *int64  `form:"bot"`
}

type AccountTransferType struct {
	ID   int64  `json:"id" db:"id"`
	Name string `json:"name" db:"name"`
}

type AccountTransferTypeResponse struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}

func (a *AccountTransferType) ToResponse() *AccountTransferTypeResponse {
	return &AccountTransferTypeResponse{
		ID:   a.ID,
		Name: a.Name,
	}
}

func (d *DepositAccount) ToListResponse() *DepositAccountListResponse {
	return &DepositAccountListResponse{
		ID:                    d.ID,
		BankingID:             d.BankingID,
		BankingName:           d.BankingName,
		BankingImageUrl:       d.BankingImageUrl,
		AlgorithmID:           d.AlgorithmID,
		AlgorithmName:         d.AlgorithmName,
		AutoBotID:             d.AutoBotID,
		AccountName:           d.AccountName,
		AccountNameDisplay:    d.AccountNameDisplay,
		AccountNumber:         d.AccountNumber,
		AutoTransfer:          d.AutoTransfer,
		QRCodePayment:         d.IsQRPayment,
		AmountTriggerTransfer: d.AmountTriggerTransfer,
		AmountTransfer:        d.AmountTransfer,
		AccountTransferID:     d.AccountTransferID,
		AccountTransferTypeID: d.AccountTransferTypeID,
		CreatedAt:             d.CreatedAt,
		UpdatedAt:             d.UpdatedAt,
	}
}

func (d *DepositAccount) ToByIdResponse() *DepositAccountByIdResponse {
	return &DepositAccountByIdResponse{
		ID:                    d.ID,
		AccountName:           d.AccountName,
		AccountNameDisplay:    d.AccountNameDisplay,
		AccountNumber:         d.AccountNumber,
		BankingID:             d.BankingID,
		PhoneNumber:           d.PhoneNumber,
		AutoTransfer:          d.AutoTransfer,
		PaymentMethodID:       d.PaymentMethodID,
		QRCodePayment:         d.IsQRPayment,
		AmountTriggerTransfer: d.AmountTriggerTransfer,
		AmountTransfer:        d.AmountTransfer,
		AccountTransferID:     d.AccountTransferID,
		AccountTransferTypeID: d.AccountTransferTypeID,
	}
}

func (d *DepositAccount) ToResponseSettingAlgorithm() *DepositAccountSettingAlgorithmResponse {
	return &DepositAccountSettingAlgorithmResponse{
		ID:                 d.ID,
		AlgorithmID:        uint(*d.AlgorithmID),
		PushBulletNickname: *d.PushBulletNickname,
		PushBulletToken:    *d.PushBulletToken,
	}
}

// ValidateAutoTransferFields validates required fields when AutoTransfer is true
func (r *DepositAccountRequest) ValidateAutoTransferFields() error {
	if r.AutoTransfer {
		if r.AmountTriggerTransfer == nil {
			return errors.NewValidationError("amountTriggerTransfer is required when autoTransfer is true")
		}
		if r.AmountTransfer == nil {
			return errors.NewValidationError("amountTransfer is required when autoTransfer is true")
		}
		if r.AccountTransferTypeID == nil {
			return errors.NewValidationError("accountTransferTypeId is required when autoTransfer is true")
		}
		if r.AccountTransferID == nil {
			return errors.NewValidationError("accountTransferId is required when autoTransfer is true")
		}

		// Validate positive amounts
		if *r.AmountTriggerTransfer <= 0 {
			return errors.NewValidationError("amountTriggerTransfer must be greater than 0")
		}
		if *r.AmountTransfer <= 0 {
			return errors.NewValidationError("amountTransfer must be greater than 0")
		}
	}
	return nil
}

// ValidateAutoTransferFields validates required fields when AutoTransfer is true for update request
func (r *DepositAccountUpdateRequest) ValidateAutoTransferFields() error {
	if r.AutoTransfer {
		if r.AmountTriggerTransfer == nil {
			return errors.NewValidationError("amountTriggerTransfer is required when autoTransfer is true")
		}
		if r.AmountTransfer == nil {
			return errors.NewValidationError("amountTransfer is required when autoTransfer is true")
		}
		if r.AccountTransferTypeID == nil {
			return errors.NewValidationError("accountTransferTypeId is required when autoTransfer is true")
		}
		if r.AccountTransferID == nil {
			return errors.NewValidationError("accountTransferId is required when autoTransfer is true")
		}

		// Validate positive amounts
		if *r.AmountTriggerTransfer <= 0 {
			return errors.NewValidationError("amountTriggerTransfer must be greater than 0")
		}
		if *r.AmountTransfer <= 0 {
			return errors.NewValidationError("amountTransfer must be greater than 0")
		}
	}
	return nil
}
