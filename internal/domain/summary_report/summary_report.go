package summary_report

// SummaryReport ข้อมูลรายงานสรุป
type SummaryReport struct {
	Data []SummaryReportItem `json:"data"`
}

// SummaryReportItem รายการข้อมูลรายงานสรุป
type SummaryReportItem struct {
	ID struct {
		GamingProviderCode string `json:"gaming_provider_code"`
		GameTypeID         int    `json:"game_type_id"`
	} `json:"_id"`
	CommissionAmount         float64 `json:"commission_amount"`
	CommissionNetTurnover    float64 `json:"commission_net_turnover"`
	CountUser                int     `json:"count_user"`
	NetTurnOver              float64 `json:"net_turn_over"`
	TurnOver                 float64 `json:"turn_over"`
	Jackpot                  float64 `json:"jackpot"`
	WinLoseNotIncludeJackpot float64 `json:"win_lose_not_include_jackpot"`
	WinLose                  float64 `json:"win_lose"`

	// Provider information (joined)
	Provider *Provider `json:"provider,omitempty"`
}

// Provider ข้อมูล provider สำหรับ join
type Provider struct {
	ID   int    `json:"id"`
	Logo string `json:"logo"`
	Code string `json:"code"`
	Name string `json:"name"`
}

// SummaryReportDetail ข้อมูลรายละเอียดรายงานสรุป
type SummaryReportDetail struct {
	Data []SummaryReportDetailItem `json:"data"`
}

// SummaryReportDetailItem รายการย่อยในรายละเอียด
type SummaryReportDetailItem struct {
	ID struct {
		GamingProviderCode string `json:"gaming_provider_code"`
		GameCode           string `json:"game_code"`
		GameName           string `json:"game_name"`
	} `json:"_id"`
	TurnOver                 float64 `json:"turn_over"`
	NetTurnOver              float64 `json:"net_turn_over"`
	Jackpot                  float64 `json:"jackpot"`
	WinLoseNotIncludeJackpot float64 `json:"win_lose_not_include_jackpot"`
	WinLose                  float64 `json:"win_lose"`
	CommissionNetTurnover    float64 `json:"commission_net_turnover"`
	CommissionAmount         float64 `json:"commission_amount"`

	// Provider information (joined)
	Provider *Provider `json:"provider,omitempty"`
}

// SummaryReportRequest request structure สำหรับ API
type SummaryReportRequest struct {
	DateFrom string `form:"date_from" json:"date_from" binding:"required" validate:"required,datetime=2006-01-02"`
	DateTo   string `form:"date_to" json:"date_to" binding:"required" validate:"required,datetime=2006-01-02"`
	Category string `form:"category" json:"category" validate:"omitempty,oneof=deposit withdraw transfer commission"`
}

// SummaryReportDetailRequest request structure สำหรับ Detail API
type SummaryReportDetailRequest struct {
	ID       int    `uri:"id" json:"id" binding:"required"`
	DateFrom string `form:"date_from" json:"date_from" binding:"required" validate:"required,datetime=2006-01-02"`
	DateTo   string `form:"date_to" json:"date_to" binding:"required" validate:"required,datetime=2006-01-02"`
}

// SummaryReportResponse response structure สำหรับ API
type SummaryReportResponse struct {
	Code    int           `json:"code"`
	Success bool          `json:"success"`
	Message string        `json:"message"`
	Data    SummaryReport `json:"data"`
}

// SummaryReportDetailResponse response structure สำหรับ Detail API
type SummaryReportDetailResponse struct {
	Code    int                 `json:"code"`
	Success bool                `json:"success"`
	Message string              `json:"message"`
	Data    SummaryReportDetail `json:"data"`
}

// SummaryReportFilterRequest request structure สำหรับ filter ข้อมูล
type SummaryReportFilterRequest struct {
	DateRegister     *string `form:"date_register" json:"date_register"`
	DateFrom         string  `form:"date_from" json:"date_from"`
	DateTo           string  `form:"date_to" json:"date_to"`
	EndTime          *string `form:"end_time" json:"end_time"`
	FirstDeposit     *string `form:"first_deposit" json:"first_deposit"`
	GameTypeID       *int    `form:"game_type_id" json:"game_type_id"`
	IsIncludePartner *bool   `form:"is_include_partner" json:"is_include_partner"`
	PartnerID        *int    `form:"partner_id" json:"partner_id"`
	Phone            *string `form:"phone" json:"phone"`
	ProviderCode     *string `form:"provider_code" json:"provider_code"`
	StartTime        *string `form:"start_time" json:"start_time"`
	UserCode         *string `form:"user_code" json:"user_code"`
	Username         *string `form:"username" json:"username"`
	Sort             string  `form:"sort" json:"sort"`
	SortBy           string  `form:"sortBy" json:"sortBy"`

	// Original validation tags (commented out for bypass):
	// DateRegister      *string `form:"date_register" json:"date_register" validate:"omitempty,datetime=2006-01-02"`
	// DateFrom          string  `form:"date_from" json:"date_from" binding:"required" validate:"required,datetime=2006-01-02"`
	// DateTo            string  `form:"date_to" json:"date_to" binding:"required" validate:"required,datetime=2006-01-02"`
	// EndTime           *string `form:"end_time" json:"end_time" validate:"omitempty,datetime=15:04:05"`
	// FirstDeposit      *string `form:"first_deposit" json:"first_deposit" validate:"omitempty,oneof=yes no"`
	// GameTypeID        *int    `form:"game_type_id" json:"game_type_id" validate:"omitempty,min=1"`
	// IsIncludePartner  *bool   `form:"is_include_partner" json:"is_include_partner"`
	// PartnerID         *int    `form:"partner_id" json:"partner_id" validate:"omitempty,min=1"`
	// Phone             *string `form:"phone" json:"phone" validate:"omitempty,min=10,max=15"`
	// ProviderCode      *string `form:"provider_code" json:"provider_code" validate:"omitempty,min=2,max=50"`
	// StartTime         *string `form:"start_time" json:"start_time" validate:"omitempty,datetime=15:04:05"`
	// UserCode          *string `form:"user_code" json:"user_code" validate:"omitempty,min=3,max=50"`
	// Username          *string `form:"username" json:"username" validate:"omitempty,min=3,max=50"`
	// Sort              string  `form:"sort" json:"sort" validate:"omitempty,oneof=asc desc"`
	// SortBy            string  `form:"sortBy" json:"sortBy" validate:"omitempty,oneof=name date amount count status created_at updated_at"`
}
