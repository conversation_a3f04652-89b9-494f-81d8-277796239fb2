package statement_status

import (
	"time"
)

// Statement status constants
const (
	PENDING   = "PENDING"
	CONFIRMED = "CONFIRMED"
	IGNORED   = "IGNORED"
)

type StatementStatus struct {
	ID        int64      `json:"id" db:"id"`
	Name      string     `json:"name" db:"name"`
	LabelTH   *string    `json:"label_th,omitempty" db:"label_th"`
	LabelEN   *string    `json:"label_en,omitempty" db:"label_en"`
	CreatedAt time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at,omitempty" db:"deleted_at"`
}

type CreateStatementStatusRequest struct {
	Name    string  `json:"name" validate:"required,max=255"`
	LabelTH *string `json:"label_th,omitempty" validate:"omitempty,max=255"`
	LabelEN *string `json:"label_en,omitempty" validate:"omitempty,max=255"`
}

type UpdateStatementStatusRequest struct {
	Name    *string `json:"name,omitempty" validate:"omitempty,max=255"`
	LabelTH *string `json:"label_th,omitempty" validate:"omitempty,max=255"`
	LabelEN *string `json:"label_en,omitempty" validate:"omitempty,max=255"`
}

type StatementStatusFilter struct {
	Name           *string `json:"name,omitempty"`
	IncludeDeleted bool    `json:"include_deleted,omitempty"`
}

type StatementStatusListResponse struct {
	Data       []StatementStatus `json:"data"`
	TotalCount int64             `json:"total_count"`
	Page       int               `json:"page"`
	PerPage    int               `json:"per_page"`
}
