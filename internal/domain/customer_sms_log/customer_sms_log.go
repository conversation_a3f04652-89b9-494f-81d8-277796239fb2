package customer_sms_log

import (
	"encoding/json"
	"time"
)

// CustomerSMSLog represents a customer SMS tracking record
type CustomerSMSLog struct {
	ID               int             `json:"id"`
	MemberID         int             `json:"member_id"`
	AdminID          int             `json:"admin_id"`
	AdminName        string          `json:"admin_name"`
	MessageContent   string          `json:"message_content"`
	SMSStatus        string          `json:"sms_status"`        // sent, failed, delivered, read
	ProviderResponse json.RawMessage `json:"provider_response"` // response from SMS provider
	CreatedAt        time.Time       `json:"created_at"`
	UpdatedAt        time.Time       `json:"updated_at"`
}

// CustomerSMSLogResponse represents the response structure for API
type CustomerSMSLogResponse struct {
	ID               int             `json:"id"`
	MemberID         int             `json:"member_id"`
	AdminID          int             `json:"admin_id"`
	AdminName        string          `json:"admin_name"`
	MessageContent   string          `json:"message_content"`
	SMSStatus        string          `json:"sms_status"`
	ProviderResponse json.RawMessage `json:"provider_response,omitempty"`
	CreatedAt        time.Time       `json:"created_at"`
	UpdatedAt        time.Time       `json:"updated_at"`
}

// CreateCustomerSMSLogRequest represents request for creating a new SMS log
type CreateCustomerSMSLogRequest struct {
	MemberID       int    `json:"member_id" validate:"required,min=1"`
	MessageContent string `json:"message_content" validate:"required,min=1,max=1000"`
}

// UpdateCustomerSMSLogRequest represents request for updating an SMS log
type UpdateCustomerSMSLogRequest struct {
	SMSStatus        string          `json:"sms_status" validate:"required,oneof=sent failed delivered read"`
	ProviderResponse json.RawMessage `json:"provider_response,omitempty"`
}

// CustomerSMSLogFilter represents filter criteria for SMS log queries
type CustomerSMSLogFilter struct {
	MemberID      *int       `json:"member_id"`      // exact match
	AdminID       *int       `json:"admin_id"`       // exact match
	SMSStatus     string     `json:"sms_status"`     // exact match
	StartDateTime *time.Time `json:"start_datetime"` // created_at >= start_datetime
	EndDateTime   *time.Time `json:"end_datetime"`   // created_at <= end_datetime
	Search        string     `json:"search"`         // message_content ILIKE search
}

// SMSStatus constants
const (
	SMSStatusSent      = "sent"
	SMSStatusFailed    = "failed"
	SMSStatusDelivered = "delivered"
	SMSStatusRead      = "read"
)

// GetAllSMSStatuses returns all available SMS statuses
func GetAllSMSStatuses() []string {
	return []string{
		SMSStatusSent,
		SMSStatusFailed,
		SMSStatusDelivered,
		SMSStatusRead,
	}
}

// GetSMSStatusLabel returns Thai label for SMS status
func GetSMSStatusLabel(status string) string {
	switch status {
	case SMSStatusSent:
		return "ส่งแล้ว"
	case SMSStatusFailed:
		return "ส่งไม่สำเร็จ"
	case SMSStatusDelivered:
		return "ถึงผู้รับแล้ว"
	case SMSStatusRead:
		return "อ่านแล้ว"
	default:
		return "ไม่ทราบ"
	}
}

// ToResponse converts CustomerSMSLog to CustomerSMSLogResponse
func (s *CustomerSMSLog) ToResponse() CustomerSMSLogResponse {
	return CustomerSMSLogResponse{
		ID:               s.ID,
		MemberID:         s.MemberID,
		AdminID:          s.AdminID,
		AdminName:        s.AdminName,
		MessageContent:   s.MessageContent,
		SMSStatus:        s.SMSStatus,
		ProviderResponse: s.ProviderResponse,
		CreatedAt:        s.CreatedAt,
		UpdatedAt:        s.UpdatedAt,
	}
}

// NewCustomerSMSLog creates a new CustomerSMSLog instance
func NewCustomerSMSLog(req CreateCustomerSMSLogRequest, adminID int, adminName string) *CustomerSMSLog {
	now := time.Now()

	return &CustomerSMSLog{
		MemberID:         req.MemberID,
		AdminID:          adminID,
		AdminName:        adminName,
		MessageContent:   req.MessageContent,
		SMSStatus:        SMSStatusSent,         // Default status
		ProviderResponse: json.RawMessage("{}"), // Default empty JSON
		CreatedAt:        now,
		UpdatedAt:        now,
	}
}

// Update updates the CustomerSMSLog with new data
func (s *CustomerSMSLog) Update(req UpdateCustomerSMSLogRequest) {
	s.SMSStatus = req.SMSStatus
	if req.ProviderResponse != nil {
		s.ProviderResponse = req.ProviderResponse
	}
	s.UpdatedAt = time.Now()
}

// SetProviderResponse sets the provider response
func (s *CustomerSMSLog) SetProviderResponse(response interface{}) error {
	responseBytes, err := json.Marshal(response)
	if err != nil {
		return err
	}
	s.ProviderResponse = responseBytes
	s.UpdatedAt = time.Now()
	return nil
}

// GetProviderResponseAsMap returns provider response as map
func (s *CustomerSMSLog) GetProviderResponseAsMap() (map[string]interface{}, error) {
	var response map[string]interface{}
	if len(s.ProviderResponse) == 0 {
		return response, nil
	}

	err := json.Unmarshal(s.ProviderResponse, &response)
	return response, err
}
