package bank_statement

import (
	"time"
)

type BankStatement struct {
	ID                int64      `json:"id" db:"id"`
	AccountID         *int64     `json:"account_id,omitempty" db:"account_id"`
	ExternalID        *int64     `json:"external_id,omitempty" db:"external_id"`
	Amount            *float64   `json:"amount,omitempty" db:"amount"`
	StatementTypeID   *int64     `json:"statement_type_id,omitempty" db:"statement_type_id"`
	StatementStatusID *int64     `json:"statement_status_id,omitempty" db:"statement_status_id"`
	FromBankID        *int64     `json:"from_bank_id,omitempty" db:"from_bank_id"`
	FromAccountNumber *string    `json:"from_account_number,omitempty" db:"from_account_number"`
	TransferAt        *time.Time `json:"transfer_at,omitempty" db:"transfer_at"`
	CreatedAt         time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at" db:"updated_at"`
}

type CreateBankStatementRequest struct {
	AccountID         *int64     `json:"account_id,omitempty" validate:"omitempty"`
	ExternalID        *int64     `json:"external_id,omitempty" validate:"omitempty"`
	Amount            *float64   `json:"amount,omitempty" validate:"omitempty"`
	StatementTypeID   *int64     `json:"statement_type_id,omitempty" validate:"omitempty"`
	StatementStatusID *int64     `json:"statement_status_id,omitempty" validate:"omitempty"`
	FromBankID        *int64     `json:"from_bank_id,omitempty" validate:"omitempty"`
	FromAccountNumber *string    `json:"from_account_number,omitempty" validate:"omitempty,max=255"`
	TransferAt        *time.Time `json:"transfer_at,omitempty" validate:"omitempty"`
}

type UpdateBankStatementRequest struct {
	AccountID         *int64     `json:"account_id,omitempty" validate:"omitempty"`
	ExternalID        *int64     `json:"external_id,omitempty" validate:"omitempty"`
	Amount            *float64   `json:"amount,omitempty" validate:"omitempty"`
	StatementTypeID   *int64     `json:"statement_type_id,omitempty" validate:"omitempty"`
	StatementStatusID *int64     `json:"statement_status_id,omitempty" validate:"omitempty"`
	FromBankID        *int64     `json:"from_bank_id,omitempty" validate:"omitempty"`
	FromAccountNumber *string    `json:"from_account_number,omitempty" validate:"omitempty,max=255"`
	TransferAt        *time.Time `json:"transfer_at,omitempty" validate:"omitempty"`
}

type BankStatementFilter struct {
	AccountID         *int64     `json:"account_id,omitempty"`
	ExternalID        *int64     `json:"external_id,omitempty"`
	StatementTypeID   *int64     `json:"statement_type_id,omitempty"`
	StatementStatusID *int64     `json:"statement_status_id,omitempty"`
	FromBankID        *int64     `json:"from_bank_id,omitempty"`
	FromAccountNumber *string    `json:"from_account_number,omitempty"`
	TransferAtFrom    *time.Time `json:"transfer_at_from,omitempty"`
	TransferAtTo      *time.Time `json:"transfer_at_to,omitempty"`
	AmountMin         *float64   `json:"amount_min,omitempty"`
	AmountMax         *float64   `json:"amount_max,omitempty"`
}

type BankStatementListResponse struct {
	Data       []BankStatement `json:"data"`
	TotalCount int64           `json:"total_count"`
	Page       int             `json:"page"`
	PerPage    int             `json:"per_page"`
}
