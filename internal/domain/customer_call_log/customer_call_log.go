package customer_call_log

import (
	"time"
)

// CustomerCallLog represents a customer call tracking record
type CustomerCallLog struct {
	ID           int       `json:"id"`
	MemberID     int       `json:"member_id"`
	AdminID      int       `json:"admin_id"`
	AdminName    string    `json:"admin_name"`
	Notes        *string   `json:"notes"`
	CallDuration *int      `json:"call_duration"` // duration in seconds
	CallStatus   string    `json:"call_status"`   // completed, missed, busy, no_answer
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// CustomerCallLogResponse represents the response structure for API
type CustomerCallLogResponse struct {
	ID           int       `json:"id"`
	MemberID     int       `json:"member_id"`
	AdminID      int       `json:"admin_id"`
	AdminName    string    `json:"admin_name"`
	Notes        *string   `json:"notes"`
	CallDuration *int      `json:"call_duration"`
	CallStatus   string    `json:"call_status"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// CreateCustomerCallLogRequest represents request for creating a new call log
type CreateCustomerCallLogRequest struct {
	MemberID     int     `json:"member_id" validate:"required,min=1"`
	Notes        *string `json:"notes"`
	CallDuration *int    `json:"call_duration" validate:"omitempty,min=0"`
	CallStatus   string  `json:"call_status" validate:"required,oneof=completed missed busy no_answer"`
}

// UpdateCustomerCallLogRequest represents request for updating a call log
type UpdateCustomerCallLogRequest struct {
	Notes        *string `json:"notes"`
	CallDuration *int    `json:"call_duration" validate:"omitempty,min=0"`
	CallStatus   string  `json:"call_status" validate:"required,oneof=completed missed busy no_answer"`
}

// CustomerCallLogFilter represents filter criteria for call log queries
type CustomerCallLogFilter struct {
	MemberID      *int       `json:"member_id"`      // exact match
	AdminID       *int       `json:"admin_id"`       // exact match
	CallStatus    string     `json:"call_status"`    // exact match
	StartDateTime *time.Time `json:"start_datetime"` // created_at >= start_datetime
	EndDateTime   *time.Time `json:"end_datetime"`   // created_at <= end_datetime
}

// CallStatus constants
const (
	CallStatusCompleted = "completed"
	CallStatusMissed    = "missed"
	CallStatusBusy      = "busy"
	CallStatusNoAnswer  = "no_answer"
)

// GetAllCallStatuses returns all available call statuses
func GetAllCallStatuses() []string {
	return []string{
		CallStatusCompleted,
		CallStatusMissed,
		CallStatusBusy,
		CallStatusNoAnswer,
	}
}

// GetCallStatusLabel returns Thai label for call status
func GetCallStatusLabel(status string) string {
	switch status {
	case CallStatusCompleted:
		return "สำเร็จ"
	case CallStatusMissed:
		return "ไม่ได้รับสาย"
	case CallStatusBusy:
		return "สายไม่ว่าง"
	case CallStatusNoAnswer:
		return "ไม่รับสาย"
	default:
		return "ไม่ทราบ"
	}
}

// ToResponse converts CustomerCallLog to CustomerCallLogResponse
func (c *CustomerCallLog) ToResponse() CustomerCallLogResponse {
	return CustomerCallLogResponse{
		ID:           c.ID,
		MemberID:     c.MemberID,
		AdminID:      c.AdminID,
		AdminName:    c.AdminName,
		Notes:        c.Notes,
		CallDuration: c.CallDuration,
		CallStatus:   c.CallStatus,
		CreatedAt:    c.CreatedAt,
		UpdatedAt:    c.UpdatedAt,
	}
}

// NewCustomerCallLog creates a new CustomerCallLog instance
func NewCustomerCallLog(req CreateCustomerCallLogRequest, adminID int, adminName string) *CustomerCallLog {
	now := time.Now()

	// Default status if not provided
	callStatus := req.CallStatus
	if callStatus == "" {
		callStatus = CallStatusCompleted
	}

	return &CustomerCallLog{
		MemberID:     req.MemberID,
		AdminID:      adminID,
		AdminName:    adminName,
		Notes:        req.Notes,
		CallDuration: req.CallDuration,
		CallStatus:   callStatus,
		CreatedAt:    now,
		UpdatedAt:    now,
	}
}

// Update updates the CustomerCallLog with new data
func (c *CustomerCallLog) Update(req UpdateCustomerCallLogRequest) {
	c.Notes = req.Notes
	c.CallDuration = req.CallDuration
	c.CallStatus = req.CallStatus
	c.UpdatedAt = time.Now()
}
