package crypto_deposit

import (
	"time"
)

// BlockchainNetwork represents supported blockchain networks
type BlockchainNetwork struct {
	ID                   int64     `json:"id" db:"id"`
	ChainID              int       `json:"chainId" db:"chain_id"`
	NetworkName          string    `json:"networkName" db:"network_name"`
	NetworkType          string    `json:"networkType" db:"network_type"`
	RPCUrl               string    `json:"rpcUrl" db:"rpc_url"`
	ExplorerUrl          string    `json:"explorerUrl" db:"explorer_url"`
	NativeCurrencySymbol string    `json:"nativeCurrencySymbol" db:"native_currency_symbol"`
	IsActive             bool      `json:"isActive" db:"is_active"`
	CreatedAt            time.Time `json:"createdAt" db:"created_at"`
	UpdatedAt            time.Time `json:"updatedAt" db:"updated_at"`
}

// BackendWallet represents backend wallets for crypto operations
type BackendWallet struct {
	ID                    int64      `json:"id" db:"id"`
	WalletAddress         string     `json:"walletAddress" db:"wallet_address"`
	ChainID               int        `json:"chainId" db:"chain_id"`
	WalletName            string     `json:"walletName" db:"wallet_name"`
	CurrentETHBalance     float64    `json:"currentEthBalance" db:"current_eth_balance"`
	CurrentTokenBalance   float64    `json:"currentTokenBalance" db:"current_token_balance"`
	MinimumETHThreshold   float64    `json:"minimumEthThreshold" db:"minimum_eth_threshold"`
	MaximumTokenThreshold float64    `json:"maximumTokenThreshold" db:"maximum_token_threshold"`
	LastBalanceCheck      *time.Time `json:"lastBalanceCheck" db:"last_balance_check"`
	IsActive              bool       `json:"isActive" db:"is_active"`
	CreatedAt             time.Time  `json:"createdAt" db:"created_at"`
	UpdatedAt             time.Time  `json:"updatedAt" db:"updated_at"`
}

// CryptoDepositLog represents detailed logging for crypto deposit steps
type CryptoDepositLog struct {
	ID              int64                  `json:"id" db:"id"`
	TransactionID   string                 `json:"transactionId" db:"transaction_id"`
	StepNumber      int                    `json:"stepNumber" db:"step_number"`
	EventType       string                 `json:"eventType" db:"event_type"`
	TransactionHash *string                `json:"transactionHash" db:"transaction_hash"`
	BlockNumber     *int64                 `json:"blockNumber" db:"block_number"`
	GasUsed         *int64                 `json:"gasUsed" db:"gas_used"`
	GasPriceGwei    *float64               `json:"gasPriceGwei" db:"gas_price_gwei"`
	EventData       map[string]interface{} `json:"eventData" db:"event_data"`
	ErrorDetails    *string                `json:"errorDetails" db:"error_details"`
	Timestamp       time.Time              `json:"timestamp" db:"timestamp"`
}

// Request structs for API endpoints

// InitiateCryptoDepositRequest represents the request to initiate a crypto deposit
type InitiateCryptoDepositRequest struct {
	TransactionID     string  `json:"transactionId" binding:"required" example:"crypto_deposit_uuid_123456789"`
	InternalReference string  `json:"internalReference" example:"member_deposit_001"`
	Amount            float64 `json:"amount" binding:"required,gt=0" example:"100.50"`
	Currency          string  `json:"currency" binding:"required" example:"USDC"`
	CustomerUsername  string  `json:"customerUsername" binding:"required" example:"member123"`
	UserWalletAddress string  `json:"userWalletAddress" binding:"required,len=42" example:"******************************************"`
	ChainID           int     `json:"chainId" binding:"required" example:"84532"`
	TokenContract     string  `json:"tokenContract" binding:"required,len=42" example:"******************************************"`
	FrontendSessionID string  `json:"frontendSessionId" example:"session_uuid_987654321"`
}

// UpdateStep1Request represents the request to update Step 1 status
type UpdateStep1Request struct {
	TransactionHash string     `json:"transactionHash" binding:"required,len=66" example:"0xabc123def456..."`
	BlockNumber     *int64     `json:"blockNumber" example:"12345"`
	GasUsed         *int64     `json:"gasUsed" example:"65000"`
	ActualAmount    float64    `json:"actualAmount" binding:"required,gt=0" example:"100.50"`
	Status          string     `json:"status" binding:"required,oneof=pending confirmed failed reverted" example:"confirmed"`
	InitiatedAt     time.Time  `json:"initiatedAt" binding:"required" example:"2025-08-29T10:30:15Z"`
	ConfirmedAt     *time.Time `json:"confirmedAt" example:"2025-08-29T10:30:45Z"`
	ErrorMessage    *string    `json:"errorMessage"`
}

// UpdateStep2Request represents the request to update Step 2 status
type UpdateStep2Request struct {
	TransactionHash       string               `json:"transactionHash" binding:"required,len=66" example:"0xdef456ghi789..."`
	BlockNumber           *int64               `json:"blockNumber" example:"12346"`
	GasUsed               *int64               `json:"gasUsed" example:"75000"`
	GasFeeETH             float64              `json:"gasFeeEth" example:"0.002"`
	ActualAmount          float64              `json:"actualAmount" binding:"required,gt=0" example:"100.50"`
	Status                string               `json:"status" binding:"required,oneof=pending confirmed failed reverted" example:"confirmed"`
	InitiatedAt           time.Time            `json:"initiatedAt" binding:"required" example:"2025-08-29T10:31:00Z"`
	ConfirmedAt           *time.Time           `json:"confirmedAt" example:"2025-08-29T10:31:30Z"`
	ErrorMessage          *string              `json:"errorMessage"`
	ThirdwebTransactionID *string              `json:"thirdwebTransactionId" example:"8fc4420d-b3d4-411f-968c-9466fa3f3491"`
	CreditUpdate          *CreditUpdateRequest `json:"creditUpdate,omitempty"`
}

// Response structs

// CryptoDepositConfigResponse represents the configuration response
type CryptoDepositConfigResponse struct {
	BackendWalletAddress  string          `json:"backendWalletAddress" example:"******************************************"`
	FinalRecipientAddress string          `json:"finalRecipientAddress" example:"******************************************"`
	SupportedNetworks     []NetworkConfig `json:"supportedNetworks"`
	TokenContracts        []TokenConfig   `json:"tokenContracts"`
}

// NetworkConfig represents network configuration
type NetworkConfig struct {
	ChainID              int           `json:"chainId" example:"84532"`
	NetworkName          string        `json:"networkName" example:"Base Sepolia"`
	NetworkType          string        `json:"networkType" example:"testnet"`
	RPCUrl               string        `json:"rpcUrl" example:"https://sepolia.base.org"`
	ExplorerUrl          string        `json:"explorerUrl" example:"https://sepolia-explorer.base.org"`
	NativeCurrencySymbol string        `json:"nativeCurrencySymbol" example:"ETH"`
	IsActive             bool          `json:"isActive" example:"true"`
	Tokens               []TokenConfig `json:"tokens"`
}

// TokenConfig represents token configuration
type TokenConfig struct {
	ID              int64   `json:"id" example:"1"`
	ContractAddress string  `json:"contractAddress" example:"******************************************"`
	Symbol          string  `json:"symbol" example:"USDC"`
	Decimals        int     `json:"decimals" example:"18"`
	ChainID         int     `json:"chainId" example:"84532"`
	RateToTHB       float64 `json:"rateToThb" example:"33.5"`
	IsActive        bool    `json:"isActive" example:"true"`
}

// InitiateDepositResponse represents the response after initiating a deposit
type InitiateDepositResponse struct {
	TransactionID         string    `json:"transactionId" example:"crypto_deposit_uuid_123456789"`
	Status                string    `json:"status" example:"INITIATED"`
	BackendWalletAddress  string    `json:"backendWalletAddress" example:"******************************************"`
	FinalRecipientAddress string    `json:"finalRecipientAddress" example:"******************************************"`
	ChainID               int       `json:"chainId" example:"84532"`
	TokenContract         string    `json:"tokenContract" example:"******************************************"`
	Amount                string    `json:"amount" example:"100.50"`
	Currency              string    `json:"currency" example:"USDC"`
	CreatedAt             time.Time `json:"createdAt" example:"2025-08-29T10:30:00Z"`
	ExpiresAt             time.Time `json:"expiresAt" example:"2025-08-29T11:00:00Z"`
}

// UpdateStepResponse represents the response after updating a step
type UpdateStepResponse struct {
	TransactionID         string     `json:"transactionId" example:"crypto_deposit_uuid_123456789"`
	Step1Status           *string    `json:"step1Status,omitempty" example:"confirmed"`
	Step2Status           *string    `json:"step2Status,omitempty" example:"confirmed"`
	OverallStatus         string     `json:"overallStatus" example:"STEP1_COMPLETED"`
	Step1TransactionHash  *string    `json:"step1TransactionHash,omitempty" example:"0xabc123def456..."`
	Step2TransactionHash  *string    `json:"step2TransactionHash,omitempty" example:"0xdef456ghi789..."`
	Step1CompletedAt      *time.Time `json:"step1CompletedAt,omitempty" example:"2025-08-29T10:30:45Z"`
	Step2CompletedAt      *time.Time `json:"step2CompletedAt,omitempty" example:"2025-08-29T10:31:30Z"`
	FinalReceivedAmount   *float64   `json:"finalReceivedAmount,omitempty" example:"100.50"`
	CompletionTimeSeconds *int       `json:"completionTimeSeconds,omitempty" example:"75"`
	CompletedAt           *time.Time `json:"completedAt,omitempty" example:"2025-08-29T10:31:30Z"`
	UpdatedAt             time.Time  `json:"updatedAt" example:"2025-08-29T10:31:35Z"`
}

// CryptoDepositResponse represents the detailed response for a crypto deposit
type CryptoDepositResponse struct {
	ID                int64                 `json:"id" example:"1001"`
	TransactionID     string                `json:"transactionId" example:"crypto_deposit_uuid_123456789"`
	InternalReference string                `json:"internalReference" example:"member_deposit_001"`
	Provider          string                `json:"provider" example:"BLOCKCHAIN"`
	TransactionType   string                `json:"transactionType" example:"DEPOSIT"`
	Amount            float64               `json:"amount" example:"100.50"`
	Currency          string                `json:"currency" example:"USDC"`
	OverallStatus     string                `json:"overallStatus" example:"COMPLETED"`
	CustomerUsername  string                `json:"customerUsername" example:"member123"`
	Wallets           WalletInfo            `json:"wallets"`
	Network           NetworkInfo           `json:"network"`
	Step1             *StepInfo             `json:"step1,omitempty"`
	Step2             *StepInfo             `json:"step2,omitempty"`
	Conversion        *ConversionInfo       `json:"conversion,omitempty"`
	Summary           TransactionSummary    `json:"summary"`
	Timestamps        TransactionTimestamps `json:"timestamps"`
}

// WalletInfo represents wallet information
type WalletInfo struct {
	UserWallet     string `json:"userWallet" example:"******************************************"`
	BackendWallet  string `json:"backendWallet" example:"******************************************"`
	FinalRecipient string `json:"finalRecipient" example:"******************************************"`
}

// NetworkInfo represents network information
type NetworkInfo struct {
	ChainID       int    `json:"chainId" example:"84532"`
	NetworkName   string `json:"networkName" example:"Base Sepolia"`
	TokenContract string `json:"tokenContract" example:"******************************************"`
	TokenSymbol   string `json:"tokenSymbol" example:"USDC"`
}

// StepInfo represents step information
type StepInfo struct {
	TransactionHash       *string    `json:"transactionHash,omitempty" example:"0xabc123def456..."`
	BlockNumber           *int64     `json:"blockNumber,omitempty" example:"12345"`
	GasUsed               *int64     `json:"gasUsed,omitempty" example:"65000"`
	GasFeeETH             *float64   `json:"gasFeeEth,omitempty" example:"0.002"`
	ActualAmount          *float64   `json:"actualAmount,omitempty" example:"100.50"`
	Status                *string    `json:"status,omitempty" example:"confirmed"`
	InitiatedAt           *time.Time `json:"initiatedAt,omitempty" example:"2025-08-29T10:30:15Z"`
	ConfirmedAt           *time.Time `json:"confirmedAt,omitempty" example:"2025-08-29T10:30:45Z"`
	ThirdwebTransactionID *string    `json:"thirdwebTransactionId,omitempty" example:"8fc4420d-b3d4-411f-968c-9466fa3f3491"`
}

// TransactionSummary represents transaction summary
type TransactionSummary struct {
	FinalReceivedAmount   *float64 `json:"finalReceivedAmount,omitempty" example:"100.50"`
	CompletionTimeSeconds *int     `json:"completionTimeSeconds,omitempty" example:"75"`
	TotalGasCostETH       *float64 `json:"totalGasCostEth,omitempty" example:"0.002"`
	EfficiencyPercentage  *float64 `json:"efficiencyPercentage,omitempty" example:"100.00"`
}

// TransactionTimestamps represents transaction timestamps
type TransactionTimestamps struct {
	CreatedAt   time.Time  `json:"createdAt" example:"2025-08-29T10:30:00Z"`
	UpdatedAt   time.Time  `json:"updatedAt" example:"2025-08-29T10:31:35Z"`
	CompletedAt *time.Time `json:"completedAt,omitempty" example:"2025-08-29T10:31:30Z"`
}

// ConversionInfo represents token to THB conversion information
type ConversionInfo struct {
	TokenAmount    float64 `json:"tokenAmount" example:"15.0"`
	ConversionRate float64 `json:"conversionRate" example:"33.5"`
	CreditAmount   float64 `json:"creditAmount" example:"502.5"`
	TokenSymbol    string  `json:"tokenSymbol" example:"USDT"`
	BaseCurrency   string  `json:"baseCurrency" example:"THB"`
}

// CryptoDepositListResponse represents paginated list response
type CryptoDepositListResponse struct {
	Data       []*CryptoDepositSummary `json:"data"`
	Pagination PaginationInfo          `json:"pagination"`
}

// CryptoDepositSummary represents summary information for list view
type CryptoDepositSummary struct {
	ID                    int64      `json:"id" example:"1001"`
	TransactionID         string     `json:"transactionId" example:"crypto_deposit_uuid_123456789"`
	Amount                float64    `json:"amount" example:"100.50"`
	Currency              string     `json:"currency" example:"USDC"`
	OverallStatus         string     `json:"overallStatus" example:"COMPLETED"`
	CustomerUsername      string     `json:"customerUsername" example:"member123"`
	ChainID               int        `json:"chainId" example:"84532"`
	NetworkName           string     `json:"networkName" example:"Base Sepolia"`
	Step1TransactionHash  *string    `json:"step1TransactionHash,omitempty" example:"0xabc123def456..."`
	Step2TransactionHash  *string    `json:"step2TransactionHash,omitempty" example:"0xdef456ghi789..."`
	FinalReceivedAmount   *float64   `json:"finalReceivedAmount,omitempty" example:"100.50"`
	CompletionTimeSeconds *int       `json:"completionTimeSeconds,omitempty" example:"75"`
	CreatedAt             time.Time  `json:"createdAt" example:"2025-08-29T10:30:00Z"`
	CompletedAt           *time.Time `json:"completedAt,omitempty" example:"2025-08-29T10:31:30Z"`
}

// PaginationInfo represents pagination information
type PaginationInfo struct {
	CurrentPage int   `json:"currentPage" example:"1"`
	PageSize    int   `json:"pageSize" example:"10"`
	Total       int64 `json:"total" example:"25"`
	TotalPages  int   `json:"totalPages" example:"3"`
}

// CryptoDepositFilters represents filters for listing deposits
type CryptoDepositFilters struct {
	// Pagination
	Page     int `form:"page,default=1" json:"page"`
	PageSize int `form:"page_size,default=20" json:"pageSize"`

	// Filters
	Status           string `form:"status" json:"status"`
	Step1Status      string `form:"step1Status" json:"step1Status"`
	Step2Status      string `form:"step2Status" json:"step2Status"`
	ChainID          *int   `form:"chainId" json:"chainId"`
	Currency         string `form:"currency" json:"currency"`
	CustomerUsername string `form:"customerUsername" json:"customerUsername"`

	// Date filters
	StartDate *time.Time `form:"startDate" json:"startDate"`
	EndDate   *time.Time `form:"endDate" json:"endDate"`

	// Sort options
	SortBy    string `form:"sort_by,default=created_at" json:"sortBy"`
	SortOrder string `form:"sort_order,default=desc" json:"sortOrder"`
}

// ChainToken represents token conversion rates
type ChainToken struct {
	ID                  int64     `json:"id" db:"id"`
	BlockchainNetworkID int64     `json:"blockchainNetworkId" db:"blockchain_network_id"`
	TokenContract       string    `json:"tokenContract" db:"token_contract"`
	TokenSymbol         string    `json:"tokenSymbol" db:"token_symbol"`
	TokenDecimals       int       `json:"tokenDecimals" db:"token_decimals"`
	RateToTHB           float64   `json:"rateToThb" db:"rate_to_thb"`
	IsActive            bool      `json:"isActive" db:"is_active"`
	CreatedAt           time.Time `json:"createdAt" db:"created_at"`
	UpdatedAt           time.Time `json:"updatedAt" db:"updated_at"`
}

// CreditUpdateRequest for processing member credit update
type CreditUpdateRequest struct {
	GameUsername string  `json:"gameUsername" binding:"required" example:"player123"`
	AmountTHB    float64 `json:"amountThb" binding:"required,gt=0" example:"330.00"`
}

// ConversionCalculationRequest for rate calculation
type ConversionCalculationRequest struct {
	ChainID       int     `json:"chainId" binding:"required" example:"84532"`
	TokenContract string  `json:"tokenContract" binding:"required" example:"******************************************"`
	TokenAmount   float64 `json:"tokenAmount" binding:"required,gt=0" example:"10.0"`
}

// ConversionCalculationResponse for rate calculation result
type ConversionCalculationResponse struct {
	TokenAmount   float64 `json:"tokenAmount" example:"10.0"`
	Rate          float64 `json:"rate" example:"33.5"`
	AmountTHB     float64 `json:"amountThb" example:"335.0"`
	TokenSymbol   string  `json:"tokenSymbol" example:"USDC"`
	ChainID       int     `json:"chainId" example:"84532"`
	TokenContract string  `json:"tokenContract" example:"******************************************"`
}

// Status constants
const (
	// Overall statuses
	StatusInitiated      = "INITIATED"
	StatusStep1Pending   = "STEP1_PENDING"
	StatusStep1Completed = "STEP1_COMPLETED"
	StatusStep2Pending   = "STEP2_PENDING"
	StatusCompleted      = "COMPLETED"
	StatusFailed         = "FAILED"
	StatusCancelled      = "CANCELLED"

	// Step statuses
	StepStatusPending   = "pending"
	StepStatusConfirmed = "confirmed"
	StepStatusFailed    = "failed"
	StepStatusReverted  = "reverted"

	// Event types for logs
	EventInitiated = "initiated"
	EventTxSent    = "tx_sent"
	EventConfirmed = "confirmed"
	EventFailed    = "failed"
	EventReverted  = "reverted"

	// Provider and transaction type
	ProviderBlockchain     = "BLOCKCHAIN"
	TransactionTypeDeposit = "DEPOSIT"
)

// Helper methods

// IsValidWalletAddress validates Ethereum wallet address format
func IsValidWalletAddress(address string) bool {
	return len(address) == 42 && address[:2] == "0x"
}

// IsValidTransactionHash validates Ethereum transaction hash format
func IsValidTransactionHash(hash string) bool {
	return len(hash) == 66 && hash[:2] == "0x"
}

// IsValidStatus validates overall status
func IsValidStatus(status string) bool {
	validStatuses := []string{
		StatusInitiated, StatusStep1Pending, StatusStep1Completed,
		StatusStep2Pending, StatusCompleted, StatusFailed, StatusCancelled,
	}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// IsValidStepStatus validates step status
func IsValidStepStatus(status string) bool {
	validStatuses := []string{
		StepStatusPending, StepStatusConfirmed, StepStatusFailed, StepStatusReverted,
	}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}
