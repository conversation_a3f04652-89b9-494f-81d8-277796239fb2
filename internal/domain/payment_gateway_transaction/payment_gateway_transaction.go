package payment_gateway_transaction

import (
	"time"
)

type PaymentGatewayTransaction struct {
	// Primary Information
	ID                int64  `json:"id" db:"id"`
	TransactionID     string `json:"transaction_id" db:"transaction_id"`
	InternalReference string `json:"internal_reference" db:"internal_reference"`

	// Gateway Information
	PaymentGatewayAccountID int64  `json:"payment_gateway_account_id" db:"payment_gateway_account_id"`
	Provider                string `json:"provider" db:"provider"`
	ProviderMerchantID      string `json:"provider_merchant_id" db:"provider_merchant_id"`
	ProviderOrderID         string `json:"provider_order_id" db:"provider_order_id"`

	// Transaction Details
	TransactionType string  `json:"transaction_type" db:"transaction_type"`
	Amount          float64 `json:"amount" db:"amount"`
	Currency        string  `json:"currency" db:"currency"`
	FeeAmount       float64 `json:"fee_amount" db:"fee_amount"`
	NetAmount       float64 `json:"net_amount" db:"net_amount"`

	// Status Tracking
	Status          string     `json:"status" db:"status"`
	PreviousStatus  string     `json:"previous_status" db:"previous_status"`
	StatusUpdatedAt *time.Time `json:"status_updated_at" db:"status_updated_at"`

	// Customer Information
	CustomerReference   string `json:"customer_reference" db:"customer_reference"`
	CustomerUsername    string `json:"customer_username" db:"customer_username"`
	CustomerBankAccount string `json:"customer_bank_account" db:"customer_bank_account"`
	CustomerBankName    string `json:"customer_bank_name" db:"customer_bank_name"`

	// URLs and Callbacks
	CallbackURL string `json:"callback_url" db:"callback_url"`
	ReturnURL   string `json:"return_url" db:"return_url"`
	PaymentURL  string `json:"payment_url" db:"payment_url"`

	// QR Code Information
	QRCode     string `json:"qr_code" db:"qr_code"`
	QRText     string `json:"qr_text" db:"qr_text"`
	QRImageURL string `json:"qr_image_url" db:"qr_image_url"`

	// Transaction Lifecycle
	InitiatedAt *time.Time `json:"initiated_at" db:"initiated_at"`
	ExpiresAt   *time.Time `json:"expires_at" db:"expires_at"`
	CompletedAt *time.Time `json:"completed_at" db:"completed_at"`
	FailedAt    *time.Time `json:"failed_at" db:"failed_at"`

	// Webhook Information
	WebhookReceivedAt *time.Time `json:"webhook_received_at" db:"webhook_received_at"`
	WebhookCount      int        `json:"webhook_count" db:"webhook_count"`
	LastWebhookAt     *time.Time `json:"last_webhook_at" db:"last_webhook_at"`
	WebhookStatus     string     `json:"webhook_status" db:"webhook_status"`

	// Metadata
	Description      string                 `json:"description" db:"description"`
	Metadata         map[string]interface{} `json:"metadata" db:"metadata"`
	ProviderResponse map[string]interface{} `json:"provider_response" db:"provider_response"`
	BlockchainData   map[string]interface{} `json:"blockchain_data" db:"blockchain_data"`
	ErrorCode        string                 `json:"error_code" db:"error_code"`
	ErrorMessage     string                 `json:"error_message" db:"error_message"`

	// Audit Fields
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
	CreatedBy string    `json:"created_by" db:"created_by"`
	UpdatedBy string    `json:"updated_by" db:"updated_by"`
}

type PaymentGatewayWebhook struct {
	// Primary Information
	ID            int64  `json:"id" db:"id"`
	TransactionID string `json:"transaction_id" db:"transaction_id"`

	// Webhook Details
	WebhookEvent     string                 `json:"webhook_event" db:"webhook_event"`
	WebhookPayload   map[string]interface{} `json:"webhook_payload" db:"webhook_payload"`
	WebhookSignature string                 `json:"webhook_signature" db:"webhook_signature"`
	WebhookHeaders   map[string]interface{} `json:"webhook_headers" db:"webhook_headers"`

	// Processing Information
	IsSignatureValid bool       `json:"is_signature_valid" db:"is_signature_valid"`
	ProcessingStatus string     `json:"processing_status" db:"processing_status"`
	ProcessingError  string     `json:"processing_error" db:"processing_error"`
	ProcessedAt      *time.Time `json:"processed_at" db:"processed_at"`

	// Provider Information
	Provider          string     `json:"provider" db:"provider"`
	ProviderRequestID string     `json:"provider_request_id" db:"provider_request_id"`
	ProviderTimestamp *time.Time `json:"provider_timestamp" db:"provider_timestamp"`

	// HTTP Information
	HTTPMethod     string `json:"http_method" db:"http_method"`
	HTTPStatusCode int    `json:"http_status_code" db:"http_status_code"`
	UserAgent      string `json:"user_agent" db:"user_agent"`
	IPAddress      string `json:"ip_address" db:"ip_address"`

	// Audit Fields
	ReceivedAt time.Time `json:"received_at" db:"received_at"`
}

// Request/Response structs for API

type CreateTransactionRequest struct {
	TransactionID     string                 `json:"transactionId" binding:"required"`
	InternalReference string                 `json:"internalReference"`
	Provider          string                 `json:"provider" binding:"required"`
	ProviderOrderID   string                 `json:"providerOrderId"`
	TransactionType   string                 `json:"transactionType" binding:"required"`
	Amount            float64                `json:"amount" binding:"required"`
	Currency          string                 `json:"currency"`
	CustomerReference string                 `json:"customerReference"`
	CustomerUsername  string                 `json:"customerUsername"`
	Description       string                 `json:"description"`
	CallbackURL       string                 `json:"callbackUrl"`
	ReturnURL         string                 `json:"returnUrl"`
	Metadata          map[string]interface{} `json:"metadata"`
}

type WebhookLogRequest struct {
	TransactionID     string                 `json:"transactionId" binding:"required"`
	WebhookEvent      string                 `json:"webhookEvent" binding:"required"`
	WebhookPayload    map[string]interface{} `json:"webhookPayload" binding:"required"`
	WebhookSignature  string                 `json:"webhookSignature"`
	IsSignatureValid  bool                   `json:"isSignatureValid"`
	ProcessingStatus  string                 `json:"processingStatus"`
	Provider          string                 `json:"provider" binding:"required"`
	ProviderRequestID string                 `json:"providerRequestId"`
	HTTPStatusCode    int                    `json:"httpStatusCode"`
	UserAgent         string                 `json:"userAgent"`
	IPAddress         string                 `json:"ipAddress"`
}

// Filter and pagination structs
type TransactionFilters struct {
	// Pagination
	Page     int `form:"page,default=1" json:"page"`
	PageSize int `form:"page_size,default=10" json:"pageSize"`

	// Primary filters
	TransactionID     string `form:"transaction_id" json:"transactionId"`
	InternalReference string `form:"internal_reference" json:"internalReference"`
	Provider          string `form:"provider" json:"provider"`
	Status            string `form:"status" json:"status"`
	TransactionType   string `form:"transaction_type" json:"transactionType"`

	// Customer filters
	CustomerUsername    string `form:"customer_username" json:"customerUsername"`
	CustomerReference   string `form:"customer_reference" json:"customerReference"`
	CustomerBankAccount string `form:"customer_bank_account" json:"customerBankAccount"`
	CustomerBankName    string `form:"customer_bank_name" json:"customerBankName"`

	// Amount filters
	MinAmount *float64 `form:"min_amount" json:"minAmount"`
	MaxAmount *float64 `form:"max_amount" json:"maxAmount"`

	// Date filters
	CreatedFromDate   *time.Time `form:"created_from_date" json:"createdFromDate"`
	CreatedToDate     *time.Time `form:"created_to_date" json:"createdToDate"`
	InitiatedFromDate *time.Time `form:"initiated_from_date" json:"initiatedFromDate"`
	InitiatedToDate   *time.Time `form:"initiated_to_date" json:"initiatedToDate"`
	CompletedFromDate *time.Time `form:"completed_from_date" json:"completedFromDate"`
	CompletedToDate   *time.Time `form:"completed_to_date" json:"completedToDate"`

	// Payment gateway account filter
	PaymentGatewayAccountID *int64 `form:"payment_gateway_account_id" json:"paymentGatewayAccountId"`

	// Error status filters
	HasError bool `form:"has_error" json:"hasError"`

	// Sort options
	SortBy    string `form:"sort_by,default=created_at" json:"sortBy"`
	SortOrder string `form:"sort_order,default=desc" json:"sortOrder"`
}

type TransactionListResponse struct {
	Transactions []*PaymentGatewayTransaction `json:"transactions"`
	Pagination   *PaginationInfo              `json:"pagination"`
}

type PaginationInfo struct {
	CurrentPage int   `json:"currentPage"`
	PageSize    int   `json:"pageSize"`
	Total       int64 `json:"total"`
	TotalPages  int   `json:"totalPages"`
}
