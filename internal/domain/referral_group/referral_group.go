package referral_group

import (
	"time"

	"blacking-api/pkg/errors"
)

// Status represents the referral group status
type Status string

const (
	StatusActive   Status = "active"
	StatusInactive Status = "inactive"
)

// ReferralGroup represents a referral group
type ReferralGroup struct {
	ID              int       `json:"id"`
	Name            string    `json:"name"`             // ชื่อกลุ่ม
	IsDefault       bool      `json:"is_default"`       // ค่าเริ่มต้น
	TurnoverSports  float64   `json:"turnover_sports"`  // เปอร์เซ็นต์ยอดเทิร์นโอเวอร์กีฬา (0-100)
	TurnoverCasino  float64   `json:"turnover_casino"`  // เปอร์เซ็นต์ยอดเทิร์นโอเวอร์คาสิโน (0-100)
	TurnoverFishing float64   `json:"turnover_fishing"` // เปอร์เซ็นต์ยอดเทิร์นโอเวอร์ตกปลา (0-100)
	TurnoverSlot    float64   `json:"turnover_slot"`    // เปอร์เซ็นต์ยอดเทิร์นโอเวอร์สล็อต (0-100)
	TurnoverLottery float64   `json:"turnover_lottery"` // เปอร์เซ็นต์ยอดเทิร์นโอเวอร์หวย (0-100)
	TurnoverCard    float64   `json:"turnover_card"`    // เปอร์เซ็นต์ยอดเทิร์นโอเวอร์ไพ่ (0-100)
	Status          Status    `json:"status"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// CreateReferralGroupRequest for creating referral group
type CreateReferralGroupRequest struct {
	Name            string  `json:"name" validate:"required,min=1,max=100"`
	IsDefault       bool    `json:"is_default"`
	TurnoverSports  float64 `json:"turnover_sports" validate:"min=0,max=100"`
	TurnoverCasino  float64 `json:"turnover_casino" validate:"min=0,max=100"`
	TurnoverFishing float64 `json:"turnover_fishing" validate:"min=0,max=100"`
	TurnoverSlot    float64 `json:"turnover_slot" validate:"min=0,max=100"`
	TurnoverLottery float64 `json:"turnover_lottery" validate:"min=0,max=100"`
	TurnoverCard    float64 `json:"turnover_card" validate:"min=0,max=100"`
}

// UpdateReferralGroupRequest for updating referral group
type UpdateReferralGroupRequest struct {
	Name            string  `json:"name" validate:"required,min=1,max=100"`
	IsDefault       bool    `json:"is_default"`
	TurnoverSports  float64 `json:"turnover_sports" validate:"min=0,max=100"`
	TurnoverCasino  float64 `json:"turnover_casino" validate:"min=0,max=100"`
	TurnoverFishing float64 `json:"turnover_fishing" validate:"min=0,max=100"`
	TurnoverSlot    float64 `json:"turnover_slot" validate:"min=0,max=100"`
	TurnoverLottery float64 `json:"turnover_lottery" validate:"min=0,max=100"`
	TurnoverCard    float64 `json:"turnover_card" validate:"min=0,max=100"`
}

// ReferralGroupResponse represents referral group API response
type ReferralGroupResponse struct {
	ID              int       `json:"id"`
	Name            string    `json:"name"`
	IsDefault       bool      `json:"is_default"`
	TurnoverSports  float64   `json:"turnover_sports"`
	TurnoverCasino  float64   `json:"turnover_casino"`
	TurnoverFishing float64   `json:"turnover_fishing"`
	TurnoverSlot    float64   `json:"turnover_slot"`
	TurnoverLottery float64   `json:"turnover_lottery"`
	TurnoverCard    float64   `json:"turnover_card"`
	Status          Status    `json:"status"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// DropdownItem represents a dropdown item with ID and name
type DropdownItem struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

// NewReferralGroup creates a new referral group
func NewReferralGroup(req CreateReferralGroupRequest) (*ReferralGroup, error) {
	if err := validateReferralGroupRequest(req); err != nil {
		return nil, err
	}

	now := time.Now()
	referralGroup := &ReferralGroup{
		// ID will be generated by database
		Name:            req.Name,
		IsDefault:       req.IsDefault,
		TurnoverSports:  req.TurnoverSports,
		TurnoverCasino:  req.TurnoverCasino,
		TurnoverFishing: req.TurnoverFishing,
		TurnoverSlot:    req.TurnoverSlot,
		TurnoverLottery: req.TurnoverLottery,
		TurnoverCard:    req.TurnoverCard,
		Status:          StatusActive,
		CreatedAt:       now,
		UpdatedAt:       now,
	}

	return referralGroup, nil
}

// Update updates referral group information
func (rg *ReferralGroup) Update(req UpdateReferralGroupRequest) error {
	if err := validateUpdateReferralGroupRequest(req); err != nil {
		return err
	}

	rg.Name = req.Name
	rg.IsDefault = req.IsDefault
	rg.TurnoverSports = req.TurnoverSports
	rg.TurnoverCasino = req.TurnoverCasino
	rg.TurnoverFishing = req.TurnoverFishing
	rg.TurnoverSlot = req.TurnoverSlot
	rg.TurnoverLottery = req.TurnoverLottery
	rg.TurnoverCard = req.TurnoverCard
	rg.UpdatedAt = time.Now()

	return nil
}

// ToResponse converts referral group to response format
func (rg *ReferralGroup) ToResponse() ReferralGroupResponse {
	return ReferralGroupResponse{
		ID:              rg.ID,
		Name:            rg.Name,
		IsDefault:       rg.IsDefault,
		TurnoverSports:  rg.TurnoverSports,
		TurnoverCasino:  rg.TurnoverCasino,
		TurnoverFishing: rg.TurnoverFishing,
		TurnoverSlot:    rg.TurnoverSlot,
		TurnoverLottery: rg.TurnoverLottery,
		TurnoverCard:    rg.TurnoverCard,
		Status:          rg.Status,
		CreatedAt:       rg.CreatedAt,
		UpdatedAt:       rg.UpdatedAt,
	}
}

// validateReferralGroupRequest validates referral group creation request
func validateReferralGroupRequest(req CreateReferralGroupRequest) error {
	if req.Name == "" {
		return errors.NewValidationError("name is required")
	}
	if len(req.Name) > 100 {
		return errors.NewValidationError("name must be at most 100 characters")
	}
	return nil
}

// validateUpdateReferralGroupRequest validates referral group update request
func validateUpdateReferralGroupRequest(req UpdateReferralGroupRequest) error {
	return validateReferralGroupRequest(CreateReferralGroupRequest{
		Name:      req.Name,
		IsDefault: req.IsDefault,
	})
}
