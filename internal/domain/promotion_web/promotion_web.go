package promotion_web

import (
	"mime/multipart"
	"time"
)

// PROMOTION_WEB_OPTION_MODEL
const (
	PROMOTION_WEB_USER_STATUS_ON_PROCESS  = int64(1)
	PROMOTION_WEB_USER_STATUS_SUCCESS     = int64(2)
	PROMOTION_WEB_USER_STATUS_CANCELED    = int64(3)
	PROMOTION_WEB_USER_STATUS_ON_WITHDRAW = int64(4)
)

const (
	PROMOTION_WEB_STATUS_DISABLE_WEB = int64(1)
	PROMOTION_WEB_STATUS_ACTIVE      = int64(2)
	PROMOTION_WEB_STATUS_CANCELED    = int64(3)
	PROMOTION_WEB_STATUS_ONLY_SHOW   = int64(4)
	PROMOTION_WEB_STATUS_ONLY_URL    = int64(5)
)

// Promotion Web Type Constants
const (
	PROMOTION_WEB_TYPE_NEW_MEMBER_FREE         = int64(1)
	PROMOTION_WEB_TYPE_NEW_MEMBER_CONDITION    = int64(2)
	PROMOTION_WEB_TYPE_DEPOSIT_MINIMUM_PER_DAY = int64(3)
	PROMOTION_WEB_TYPE_FIRST_DEPOSIT           = int64(4)
	PROMOTION_WEB_TYPE_DEPOSIT_PER_DAY         = int64(5)
	PROMOTION_WEB_TYPE_DEPOSIT_BY_TIME         = int64(6)
	PROMOTION_WEB_TYPE_FIRST_DEPOSIT_OF_DAY    = int64(7)
)

// Promotion Web Date Type Constants
const (
	PROMOTION_WEB_DATE_TYPE_FIXED_DATE     = int64(1)
	PROMOTION_WEB_DATE_TYPE_NON_FIXED_DATE = int64(2)
)

// Promotion Web Bonus Type Constants
const (
	PROMOTION_WEB_BONUS_TYPE_PERCENT    = int64(1)
	PROMOTION_WEB_BONUS_TYPE_FIXED_RATE = int64(2)
)

// Promotion Web Bonus Condition Constants
const (
	PROMOTION_WEB_BONUS_CONDITION_MORE_THAN_OR_EQUAL = int64(1)
	PROMOTION_WEB_BONUS_CONDITION_LESS_THAN_OR_EQUAL = int64(2)
)

// Promotion Web Turnover Type Constants
const (
	PROMOTION_WEB_TURN_OVER_TYPE_ALL       = int64(1)
	PROMOTION_WEB_TURN_OVER_TYPE_SPORT     = int64(2)
	PROMOTION_WEB_TURN_OVER_TYPE_CASINO    = int64(3)
	PROMOTION_WEB_TURN_OVER_TYPE_SLOT      = int64(4)
	PROMOTION_WEB_TURN_OVER_TYPE_P2P       = int64(5)
	PROMOTION_WEB_TURN_OVER_TYPE_LOTTERY   = int64(6)
	PROMOTION_WEB_TURN_OVER_TYPE_FINANCIAL = int64(7)
)

// User Withdraw Lock Credit Type Constants
const (
	USER_WITHDRAW_LOCK_CREDIT_TYPE_PROMOTION = int64(1)
)

// Business Logic Constants
const (
	NOT_PASS_PROMOTION   = "NOT_PASS_PROMOTION"
	PASS_PROMOTION       = "PASS_PROMOTION"
	PASS_TO_WITHDRAW     = "PASS_TO_WITHDRAW"
	CONTINUE_TO_WITHDRAW = "CONTINUE_TO_WITHDRAW"
	NOT_PASS_TO_WITHDRAW = "NOT_PASS_TO_WITHDRAW"
)

// Common response structures
type PaginatedResponse struct {
	List  interface{} `json:"list"`
	Total int64       `json:"total"`
	Page  int         `json:"page"`
	Limit int         `json:"limit"`
}

type ErrorResponse struct {
	Message string `json:"message"`
	Code    string `json:"code,omitempty"`
}

type SuccessResponse struct {
	Message string `json:"message"`
}

// Promotion Web Option Responses
type PromotionWebTypeResponse struct {
	ID      int64  `json:"id" db:"id"`
	Name    string `json:"name" db:"name"`
	LabelTh string `json:"labelTh" db:"label_th"`
	LabelEn string `json:"labelEn" db:"label_en"`
}

type PromotionWebStatusResponse struct {
	ID      int64  `json:"id" db:"id"`
	Name    string `json:"name" db:"name"`
	LabelTh string `json:"labelTh" db:"label_th"`
	LabelEn string `json:"labelEn" db:"label_en"`
}

type PromotionWebBonusConditionResponse struct {
	ID      int64  `json:"id" db:"id"`
	Syntax  string `json:"syntax" db:"syntax"`
	Name    string `json:"name" db:"name"`
	LabelTh string `json:"labelTh" db:"label_th"`
	LabelEn string `json:"labelEn" db:"label_en"`
}

type PromotionWebBonusTypeResponse struct {
	ID      int64  `json:"id" db:"id"`
	Name    string `json:"name" db:"name"`
	LabelTh string `json:"labelTh" db:"label_th"`
	LabelEn string `json:"labelEn" db:"label_en"`
}

type PromotionWebTurnoverTypeResponse struct {
	ID      int64  `json:"id" db:"id"`
	Name    string `json:"name" db:"name"`
	LabelTh string `json:"labelTh" db:"label_th"`
	LabelEn string `json:"labelEn" db:"label_en"`
}

type PromotionWebDateTypeResponse struct {
	ID      int64  `json:"id" db:"id"`
	Name    string `json:"name" db:"name"`
	LabelTh string `json:"labelTh" db:"label_th"`
	LabelEn string `json:"labelEn" db:"label_en"`
}

type PromotionWebGetListRequest struct {
	Page                 int    `form:"page" default:"1"`
	Limit                int    `form:"limit" default:"10"`
	StartDate            string `form:"startDate" time_format:"2006-01-02"`
	EndDate              string `form:"endDate" time_format:"2006-01-02"`
	Search               string `form:"search"`
	PromotionWebStatusId *int64 `form:"promotionWebStatusId"`
}

type PromotionWebGetListResponse struct {
	Id                     int64      `json:"id"`
	PromotionWebTypeId     int64      `json:"promotionWebTypeId"`
	PromotionWebTypeTh     string     `json:"promotionWebTypeTh"`
	PromotionWebStatusId   int64      `json:"promotionWebStatusId"`
	PromotionWebStatusTh   string     `json:"promotionWebStatusTh"`
	Name                   string     `json:"name"`
	PromotionWebDateTypeId int64      `json:"promotionWebDateTypeId"`
	PromotionWebDateTypeTh string     `json:"promotionWebDateTypeTh"`
	StartDate              *time.Time `json:"startDate"`
	EndDate                *time.Time `json:"endDate"`
	TimeStart              string     `json:"timeStart"`
	TimeEnd                string     `json:"timeEnd"`
	CreatedByAdminId       int64      `json:"createdByAdminId"`
	CreatedByAdminName     string     `json:"createdByAdminName"`
	UpdatedByAdminId       *int64     `json:"updatedByAdminId"`
	UpdatedByAdminName     string     `json:"updatedByAdminName"`
	CanceledByAdminId      *int64     `json:"canceledByAdminId"`
	CanceledByAdminName    *string    `json:"canceledByAdminName"`
	HiddenUrlLink          string     `json:"hiddenUrlLink"`
	UpdatedAt              time.Time  `json:"updatedAt"`
}

type PromotionWebCreateRequest struct {
	Id                           int64     `json:"-"`
	PromotionWebTypeId           int64     `json:"promotionWebTypeId" binding:"required"`
	PromotionWebStatusId         int64     `json:"promotionWebStatusId" binding:"required"`
	ConditionDetail              string    `json:"conditionDetail"`
	ImageUrl                     string    `json:"imageUrl"`
	Name                         string    `json:"name" binding:"required"`
	ShortDescription             string    `json:"shortDescription" binding:"required"`
	Description                  string    `json:"description" binding:"required"`
	PromotionWebDateTypeId       int64     `json:"promotionWebDateTypeId" binding:"required"`
	StartDate                    *string   `json:"startDate"`
	EndDate                      *string   `json:"endDate"`
	FreeBonusAmount              float64   `json:"freeBonusAmount"`
	PrivilegePerDay              int64     `json:"privilegePerDay"`
	AbleWithdrawMorethan         float64   `json:"ableWithdrawMorethan"`
	PromotionWebBonusConditionId *int64    `json:"promotionWebBonusConditionId"`
	BonusConditionAmount         float64   `json:"bonusConditionAmount"`
	PromotionWebBonusTypeId      *int64    `json:"promotionWebBonusTypeId"`
	BonusTypeAmount              float64   `json:"bonusTypeAmount"`
	BonusTypeAmountMax           float64   `json:"bonusTypeAmountMax"`
	AbleWithdrawPertime          float64   `json:"ableWithdrawPertime"`
	PromotionWebTurnoverTypeId   *int64    `json:"promotionWebTurnoverTypeId"`
	TurnoverAmount               float64   `json:"turnoverAmount"`
	Monday                       bool      `json:"monday"`
	Tuesday                      bool      `json:"tuesday"`
	Wednesday                    bool      `json:"wednesday"`
	Thursday                     bool      `json:"thursday"`
	Friday                       bool      `json:"friday"`
	Saturday                     bool      `json:"saturday"`
	Sunday                       bool      `json:"sunday"`
	TimeStart                    *string   `json:"timeStart"`
	TimeEnd                      *string   `json:"timeEnd"`
	HiddenUrlLink                string    `json:"hiddenUrlLink"`
	CreatedByAdminId             int64     `json:"-"`
	UpdatedAt                    time.Time `json:"-"`
}

type PromotionWebGetByIdResponse struct {
	Id                               int64      `json:"id"`
	PromotionWebTypeId               int64      `json:"promotionWebTypeId"`
	PromotionWebTypeTh               string     `json:"promotionWebTypeTh"`
	PromotionWebStatusId             int64      `json:"promotionWebStatusId"`
	PromotionWebStatusTh             string     `json:"promotionWebStatusTh"`
	ConditionDetail                  string     `json:"conditionDetail"`
	ImageUrl                         string     `json:"imageUrl"`
	Name                             string     `json:"name"`
	ShortDescription                 string     `json:"shortDescription"`
	Description                      string     `json:"description"`
	PromotionWebDateTypeId           int64      `json:"promotionWebDateTypeId"`
	PromotionWebDateTypeTh           string     `json:"promotionWebDateTypeTh"`
	StartDate                        *time.Time `json:"startDate"`
	EndDate                          *time.Time `json:"endDate"`
	FreeBonusAmount                  float64    `json:"freeBonusAmount"`
	PrivilegePerDay                  int64      `json:"privilegePerDay"`
	AbleWithdrawMorethan             float64    `json:"ableWithdrawMorethan"`
	PromotionWebBonusConditionId     int64      `json:"promotionWebBonusConditionId"`
	PromotionWebBonusConditionTh     string     `json:"promotionWebBonusConditionTh"`
	PromotionWebBonusConditionSyntax string     `json:"promotionWebBonusConditionSyntax"`
	BonusConditionAmount             float64    `json:"bonusConditionAmount"`
	PromotionWebBonusTypeId          int64      `json:"promotionWebBonusTypeId"`
	PromotionWebBonusTypeTh          string     `json:"promotionWebBonusTypeTh"`
	BonusTypeAmount                  float64    `json:"bonusTypeAmount"`
	BonusTypeAmountMax               float64    `json:"bonusTypeAmountMax"`
	AbleWithdrawPertime              float64    `json:"ableWithdrawPertime"`
	PromotionWebTurnoverTypeId       int64      `json:"promotionWebTurnoverTypeId"`
	PromotionWebTurnoverTypeTh       string     `json:"promotionWebTurnoverTypeTh"`
	TurnoverAmount                   float64    `json:"turnoverAmount"`
	Monday                           bool       `json:"monday"`
	Tuesday                          bool       `json:"tuesday"`
	Wednesday                        bool       `json:"wednesday"`
	Thursday                         bool       `json:"thursday"`
	Friday                           bool       `json:"friday"`
	Saturday                         bool       `json:"saturday"`
	Sunday                           bool       `json:"sunday"`
	TimeStart                        string     `json:"timeStart"`
	TimeEnd                          string     `json:"timeEnd"`
}

type PromotionWebUpdateRequest struct {
	Id                           int64    `json:"-"`
	PromotionWebTypeId           *int64   `json:"promotionWebTypeId"`
	PromotionWebStatusId         *int64   `json:"promotionWebStatusId"`
	ConditionDetail              *string  `json:"conditionDetail"`
	ImageUrl                     *string  `json:"imageUrl"`
	Name                         *string  `json:"name"`
	ShortDescription             *string  `json:"shortDescription"`
	Description                  *string  `json:"description"`
	PromotionWebDateTypeId       int64    `json:"promotionWebDateTypeId"`
	StartDate                    *string  `json:"startDate"`
	EndDate                      *string  `json:"endDate"`
	FreeBonusAmount              *float64 `json:"freeBonusAmount"`
	PrivilegePerDay              *int64   `json:"privilegePerDay"`
	AbleWithdrawMorethan         *float64 `json:"ableWithdrawMorethan"`
	PromotionWebBonusConditionId *int64   `json:"promotionWebBonusConditionId"`
	BonusConditionAmount         *float64 `json:"bonusConditionAmount"`
	PromotionWebBonusTypeId      *int64   `json:"promotionWebBonusTypeId"`
	BonusTypeAmount              *float64 `json:"bonusTypeAmount"`
	BonusTypeAmountMax           *float64 `json:"bonusTypeAmountMax"`
	AbleWithdrawPertime          *float64 `json:"ableWithdrawPertime"`
	PromotionWebTurnoverTypeId   *int64   `json:"promotionWebTurnoverTypeId"`
	TurnoverAmount               *float64 `json:"turnoverAmount"`
	Monday                       *bool    `json:"monday"`
	Tuesday                      *bool    `json:"tuesday"`
	Wednesday                    *bool    `json:"wednesday"`
	Thursday                     *bool    `json:"thursday"`
	Friday                       *bool    `json:"friday"`
	Saturday                     *bool    `json:"saturday"`
	Sunday                       *bool    `json:"sunday"`
	TimeStart                    *string  `json:"timeStart"`
	TimeEnd                      *string  `json:"timeEnd"`
	HiddenUrlLink                *string  `json:"hiddenUrlLink"`
	UpdatedByAdminId             int64    `json:"-"`
}

type GetPromotionWebIdToCancel struct {
	Id                       int64 `json:"id"`
	PromotionWebUserStatusId int64 `json:"promotionWebUserStatusId"`
}

type CancelPromotionWebRequest struct {
	Id                   int64     `json:"id"`
	CanceledByAdminId    int64     `json:"-"`
	CanceledAt           time.Time `json:"-"`
	PromotionWebStatusId int64     `json:"-"`
}

type DeletePromotionWebRequest struct {
	Id                   int64     `json:"id"`
	DeletedByAdminId     int64     `json:"-"`
	DeletedAt            time.Time `json:"-"`
	PromotionWebStatusId int64     `json:"-"`
}

type PromotionWebExpired struct {
	Id int64 `json:"id"`
}

type CancelPromotionWebUserById struct {
	Id                int64 `json:"-"`
	CanceledByAdminId int64 `json:"canceledByAdminId"`
}

type CancelPromotionWebUserByPromotionWebId struct {
	PromotionWebId    int64     `json:"promotionWebId"`
	CanceledByAdminId int64     `json:"canceledByAdminId"`
	CanceledAt        time.Time `json:"canceledAt"`
}

type PromotionWebUserByUserIdResponse struct {
	Id                       int64     `json:"id"`
	PromotionWebId           int64     `json:"promotionWebId"`
	PromotionName            string    `json:"promotionName"`
	UserId                   int64     `json:"userId"`
	MemberCode               string    `json:"memberCode"`
	FullName                 string    `json:"fullName"`
	Phone                    string    `json:"phone"`
	PromotionWebUserStatusId int64     `json:"promotionWebUserStatusId"`
	PromotionWebUserStatusTh string    `json:"promotionWebUserStatusTh"`
	TotalAmount              float64   `json:"totalAmount"`
	CreatedAt                time.Time `json:"createdAt"`
}

type PromotionWebUserGetListRequest struct {
	PromotionWebId           *int64 `form:"promotionWebId"`
	Page                     int    `form:"page" default:"1"`
	Limit                    int    `form:"limit" default:"10"`
	StartDate                string `form:"startDate" time_format:"2006-01-02"`
	EndDate                  string `form:"end" time_format:"2006-01-02"`
	PromotionWebUserStatusId *int64 `form:"promotionWebUserStatusId"`
	Search                   string `form:"search"`
	TypeList                 string `form:"typeList"`
}

type PromotionWebUserGetListResponse struct {
	Id                       int64      `json:"id"`
	PromotionWebId           int64      `json:"promotionWebId"`
	PromotionName            string     `json:"promotionName"`
	UserId                   int64      `json:"userId"`
	MemberCode               string     `json:"memberCode"`
	FullName                 string     `json:"fullName"`
	Phone                    string     `json:"phone"`
	PromotionWebUserStatusId int64      `json:"promotionWebUserStatusId"`
	PromotionWebUserStatusTh string     `json:"promotionWebUserStatusTh"`
	TotalAmount              float64    `json:"totalAmount"`
	IsLocked                 bool       `json:"isLocked"`
	AbleWithdrawPertime      float64    `json:"ableWithdrawPertime"`
	AbleWithdrawMorethan     float64    `json:"ableWithdrawMorethan"`
	CreatedAt                time.Time  `json:"createdAt"`
	CanceledByAdminId        *int64     `json:"canceledByAdminId"`
	CanceledByAdminName      *string    `json:"canceledByAdminName"`
	CanceledAt               *time.Time `json:"canceledAt"`
	ApproveCreditByAdminId   *int64     `json:"approveCreditByAdminId"`
	ApproveCreditByAdminName string     `json:"approveCreditByAdminName"`
	ApproveCreditAt          *time.Time `json:"approveCreditAt"`
}

type GetPromotionWebUserById struct {
	Id int64 `json:"id"`
}

type GetPromotionWebUserByIdResponse struct {
	Id                       int64     `json:"id"`
	PromotionWebId           int64     `json:"promotionWebId"`
	PromotionName            string    `json:"promotionName"`
	UserId                   int64     `json:"userId"`
	MemberCode               string    `json:"memberCode"`
	FullName                 string    `json:"fullName"`
	Phone                    string    `json:"phone"`
	PromotionWebUserStatusId int64     `json:"promotionWebUserStatusId"`
	PromotionWebUserStatusTh string    `json:"promotionWebUserStatusTh"`
	TotalAmount              float64   `json:"totalAmount"`
	CreatedAt                time.Time `json:"createdAt"`
}

type PromotionWebUserGetListByUserIdRequest struct {
	UserId                   int64  `form:"userId" binding:"required"`
	PromotionWebUserStatusId *int64 `form:"promotionWebUserStatusId"`
	OfDate                   string `form:"ofDate"`
	DateType                 string `form:"dateType"`
	FromDate                 string `form:"fromDate"`
	ToDate                   string `form:"toDate"`
	Search                   string `form:"search"`
	Page                     int    `form:"page" default:"1"`
	Limit                    int    `form:"limit" default:"10"`
	SortCol                  string `form:"sortCol"`
	SortAsc                  string `form:"sortAsc"`
}

type PromotionWebUserGetListByUserIdResponse struct {
	Id                       int64      `json:"id"`
	PromotionWebId           int64      `json:"promotionWebId"`
	PromotionName            string     `json:"promotionName"`
	UserId                   int64      `json:"userId"`
	MemberCode               string     `json:"memberCode"`
	FullName                 string     `json:"fullName"`
	Phone                    string     `json:"phone"`
	PromotionWebUserStatusId int64      `json:"promotionWebUserStatusId"`
	PromotionWebUserStatusTh string     `json:"promotionWebUserStatusTh"`
	TotalAmount              float64    `json:"totalAmount"`
	CreatedAt                time.Time  `json:"createdAt"`
	CanceledByAdminId        *int64     `json:"canceledByAdminId"`
	CanceledByAdminName      *string    `json:"canceledByAdminName"`
	CanceledAt               *time.Time `json:"canceledAt"`
}

type PromotionWebGetSildeListOnlyActive struct {
	Id                     int64  `json:"id"`
	PromotionWebTypeId     int64  `json:"promotionWebTypeId"`
	PromotionWebTypeTh     string `json:"promotionWebTypeTh"`
	PromotionWebStatusId   int64  `json:"promotionWebStatusId"`
	PromotionWebStatusTh   string `json:"promotionWebStatusTh"`
	Name                   string `json:"name"`
	PromotionWebDateTypeId int64  `json:"promotionWebDateTypeId"`
	StartDate              string `json:"startDate"`
	EndDate                string `json:"endDate"`
	TimeStart              string `json:"timeStart"`
	TimeEnd                string `json:"timeEnd"`
}

type CloudFlareResult struct {
	Id                string    `json:"id"`
	Filename          string    `json:"filename"`
	Uploaded          time.Time `json:"uploaded"`
	RequireSignedURLs bool      `json:"requireSignedURLs"`
	Variants          []string  `json:"variants"`
}

type CloudFlareUploadResponse struct {
	Result   CloudFlareResult `json:"result"`
	Success  bool             `json:"success"`
	Errors   []interface{}    `json:"errors"`
	Messages []interface{}    `json:"messages"`
}

type CloudFlareUploadCreateBody struct {
	FileTypeId        int64     `json:"fileTypeId"`
	ImageId           string    `json:"imageId"`
	Filename          string    `json:"filename"`
	Uploaded          time.Time `json:"uploaded"`
	RequireSignedURLs bool      `json:"requireSignedURLs"`
	FileUrl           string    `json:"fileUrl"`
	ImageDimensions   string    `json:"imageDimensions"`
	Kilobytes         int64     `json:"kilobytes"`
}

// DragSortRequest moved to line 629 to avoid duplication

type PrioritySortResponse struct {
	Id            int64 `json:"id"`
	PriorityOrder int64 `json:"priorityOrder"`
}

// Lock Credit Models
type LockCreditPromotionCreateRequest struct {
	UserID             int64   `json:"userId" validate:"required"`
	PromotionID        int64   `json:"promotionId" validate:"required"`
	PromotionWebUserID int64   `json:"promotionWebUserId" validate:"required"`
	BonusAmount        float64 `json:"bonusAmount" validate:"required"`
	IsLocked           bool    `json:"isLocked"`
}

type LockCreditPromotionUpdateRequest struct {
	ID        int64 `json:"-"`
	IsLocked  *bool `json:"isLocked"`
	UpdatedBy int64 `json:"-"`
}

type GetLockCreditWithdrawListRequest struct {
	UserID    int64  `form:"userId"`
	Search    string `form:"search"`
	StartDate string `form:"startDate" time_format:"2006-01-02"`
	EndDate   string `form:"endDate" time_format:"2006-01-02"`
	Page      int    `form:"page,default=1" validate:"min=1"`
	Limit     int    `form:"limit,default=10" validate:"min=1,max=100"`
}

type GetLockCreditWithdrawListResponse struct {
	ID                           int64      `json:"id" db:"id"`
	UserID                       int64      `json:"userId" db:"user_id"`
	MemberCode                   string     `json:"memberCode" db:"member_code"`
	Fullname                     string     `json:"fullname" db:"fullname"`
	Phone                        string     `json:"phone" db:"phone"`
	RefID                        int64      `json:"refId" db:"ref_id"`
	Detail                       string     `json:"detail" db:"detail"`
	UserWithdrawLockCreditTypeID int64      `json:"userWithdrawLockCreditTypeId" db:"user_withdraw_lock_credit_type_id"`
	UserWithdrawLockCreditTypeTh string     `json:"userWithdrawLockCreditTypeTh" db:"user_withdraw_lock_credit_type_th"`
	CreditMoreThan               float64    `json:"creditMoreThan" db:"credit_more_than"`
	AllowWithdrawAmount          float64    `json:"allowWithdrawAmount" db:"allow_withdraw_amount"`
	WithdrawAmount               float64    `json:"withdrawAmount" db:"withdraw_amount"`
	PullCreditAmount             float64    `json:"pullCreditAmount" db:"pull_credit_amount"`
	IsLocked                     bool       `json:"isLocked" db:"is_locked"`
	IsPullCredit                 bool       `json:"isPullCredit" db:"is_pull_credit"`
	CreatedAt                    time.Time  `json:"createdAt" db:"created_at"`
	ApprovedAt                   *time.Time `json:"approvedAt" db:"approved_at"`
	ApprovedByID                 *int64     `json:"approvedById" db:"approved_by_id"`
	ApprovedByName               *string    `json:"approvedByName" db:"approved_by_name"`
}

type UnlockCreditWithdrawRequest struct {
	ID         int64 `json:"-"`
	ApprovedBy int64 `json:"-"`
}

type CheckLockedCreditResponse struct {
	IsLocked bool    `json:"isLocked"`
	Amount   float64 `json:"amount,omitempty"`
}

// User Promotion Collection Models
type CollectPromotionRequest struct {
	PromotionWebID int64 `json:"promotionWebId" validate:"required"`
	UserID         int64 `json:"-"`
}

type ShowPromotionForUserResponse struct {
	ID                      int64  `json:"id" db:"id"`
	PromotionWebTypeID      int64  `json:"promotionWebTypeId" db:"promotion_web_type_id"`
	PromotionWebTypeTh      string `json:"promotionWebTypeTh" db:"promotion_web_type_th"`
	PromotionWebTypeEn      string `json:"promotionWebTypeEn" db:"promotion_web_type_en"`
	PromotionWebStatusID    int64  `json:"promotionWebStatusId" db:"promotion_web_status_id"`
	ConditionDetail         string `json:"conditionDetail" db:"condition_detail"`
	ImageURL                string `json:"imageUrl" db:"image_url"`
	Name                    string `json:"name" db:"name"`
	ShortDescription        string `json:"shortDescription" db:"short_description"`
	Description             string `json:"description" db:"description"`
	PromotionWebDateTypeID  int64  `json:"promotionWebDateTypeId" db:"promotion_web_date_type_id"`
	StartDate               string `json:"startDate" db:"start_date"`
	EndDate                 string `json:"endDate" db:"end_date"`
	Monday                  bool   `json:"monday" db:"monday"`
	Tuesday                 bool   `json:"tuesday" db:"tuesday"`
	Wednesday               bool   `json:"wednesday" db:"wednesday"`
	Thursday                bool   `json:"thursday" db:"thursday"`
	Friday                  bool   `json:"friday" db:"friday"`
	Saturday                bool   `json:"saturday" db:"saturday"`
	Sunday                  bool   `json:"sunday" db:"sunday"`
	TimeStart               string `json:"timeStart" db:"time_start"`
	TimeEnd                 string `json:"timeEnd" db:"time_end"`
	HiddenURLLink           string `json:"hiddenUrlLink" db:"hidden_url_link"`
	UserStatusWithPromotion string `json:"userStatusWithPromotion"` // "NOT_AVAILABLE", "AVAILABLE", "ON_PROCESS"
}

// Additional Models from Migration Document

// Promotion Slide Models
type PromotionSlideResponse struct {
	ID               int64  `json:"id" db:"id"`
	Name             string `json:"name" db:"name"`
	ImageURL         string `json:"imageUrl" db:"image_url"`
	ShortDescription string `json:"shortDescription" db:"short_description"`
	HiddenURLLink    string `json:"hiddenUrlLink" db:"hidden_url_link"`
}

// Summary Models
type PromotionSummaryRequest struct {
	StartDate string `form:"startDate" time_format:"2006-01-02"`
	EndDate   string `form:"endDate" time_format:"2006-01-02"`
}

type PromotionSummaryResponse struct {
	TotalPromotions    int64 `json:"totalPromotions" db:"total_promotions"`
	ActivePromotions   int64 `json:"activePromotions" db:"active_promotions"`
	InactivePromotions int64 `json:"inactivePromotions" db:"inactive_promotions"`
}

type UserPromotionSummaryRequest struct {
	UserID    int64  `form:"userId"`
	StartDate string `form:"startDate" time_format:"2006-01-02"`
	EndDate   string `form:"endDate" time_format:"2006-01-02"`
}

type UserPromotionSummaryResponse struct {
	TotalCollected int64   `json:"totalCollected" db:"total_collected"`
	TotalSuccess   int64   `json:"totalSuccess" db:"total_success"`
	TotalCanceled  int64   `json:"totalCanceled" db:"total_canceled"`
	TotalAmount    float64 `json:"totalAmount" db:"total_amount"`
}

// Turnover Models
type UserTurnoverSummaryResponse struct {
	UserPromotionID    int64   `json:"userPromotionId" db:"user_promotion_id"`
	RequiredTurnover   float64 `json:"requiredTurnover" db:"required_turnover"`
	CurrentTurnover    float64 `json:"currentTurnover" db:"current_turnover"`
	RemainingTurnover  float64 `json:"remainingTurnover" db:"remaining_turnover"`
	TurnoverPercentage float64 `json:"turnoverPercentage" db:"turnover_percentage"`
	IsCompleted        bool    `json:"isCompleted" db:"is_completed"`
}

// Business Logic Models
type CheckUserPromotionRequest struct {
	UserID      int64   `json:"userId" validate:"required"`
	PromotionID int64   `json:"promotionId" validate:"required"`
	Amount      float64 `json:"amount" validate:"required"`
}

type CheckPromotionWithdrawRequest struct {
	UserID int64   `json:"userId" validate:"required"`
	Amount float64 `json:"amount" validate:"required"`
}

type CheckPromotionWithdrawResponse struct {
	CanWithdraw  bool    `json:"canWithdraw"`
	LockedAmount float64 `json:"lockedAmount,omitempty"`
	Message      string  `json:"message,omitempty"`
}

// File Upload Models
type FileUploadResponse struct {
	URL string `json:"url"`
}

type UploadPromotionCoverRequest struct {
	File multipart.FileHeader `form:"file" binding:"required"`
}

// Drag Sort Models
type DragSortRequest struct {
	FromItemID int64 `json:"fromItemId" validate:"required"`
	ToItemID   int64 `json:"toItemId" validate:"required"`
}

// User Promotion Collection Models
type GetUserCollectedPromotionsRequest struct {
	Page  int `form:"page,default=1" validate:"min=1"`
	Limit int `form:"limit,default=10" validate:"min=1,max=100"`
}

type GetUserCollectedPromotionsResponse struct {
	ID                       int64     `json:"id" db:"id"`
	PromotionWebID           int64     `json:"promotionWebId" db:"promotion_web_id"`
	PromotionName            string    `json:"promotionName" db:"promotion_name"`
	PromotionWebUserStatusID int64     `json:"promotionWebUserStatusId" db:"promotion_web_user_status_id"`
	StatusName               string    `json:"statusName" db:"status_name"`
	TotalAmount              float64   `json:"totalAmount" db:"total_amount"`
	CreatedAt                time.Time `json:"createdAt" db:"created_at"`
}

// Hidden URL Models
type GetPromotionByHiddenURLResponse struct {
	ID                      int64  `json:"id" db:"id"`
	PromotionWebTypeID      int64  `json:"promotionWebTypeId" db:"promotion_web_type_id"`
	PromotionWebTypeTh      string `json:"promotionWebTypeTh" db:"promotion_web_type_th"`
	PromotionWebTypeEn      string `json:"promotionWebTypeEn" db:"promotion_web_type_en"`
	PromotionWebStatusID    int64  `json:"promotionWebStatusId" db:"promotion_web_status_id"`
	ConditionDetail         string `json:"conditionDetail" db:"condition_detail"`
	ImageURL                string `json:"imageUrl" db:"image_url"`
	Name                    string `json:"name" db:"name"`
	ShortDescription        string `json:"shortDescription" db:"short_description"`
	Description             string `json:"description" db:"description"`
	PromotionWebDateTypeID  int64  `json:"promotionWebDateTypeId" db:"promotion_web_date_type_id"`
	StartDate               string `json:"startDate" db:"start_date"`
	EndDate                 string `json:"endDate" db:"end_date"`
	Monday                  bool   `json:"monday" db:"monday"`
	Tuesday                 bool   `json:"tuesday" db:"tuesday"`
	Wednesday               bool   `json:"wednesday" db:"wednesday"`
	Thursday                bool   `json:"thursday" db:"thursday"`
	Friday                  bool   `json:"friday" db:"friday"`
	Saturday                bool   `json:"saturday" db:"saturday"`
	Sunday                  bool   `json:"sunday" db:"sunday"`
	TimeStart               string `json:"timeStart" db:"time_start"`
	TimeEnd                 string `json:"timeEnd" db:"time_end"`
	HiddenURLLink           string `json:"hiddenUrlLink" db:"hidden_url_link"`
	UserStatusWithPromotion string `json:"userStatusWithPromotion"` // "NOT_AVAILABLE", "AVAILABLE", "ON_PROCESS"
}
