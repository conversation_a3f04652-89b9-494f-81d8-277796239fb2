package service

import (
	"context"
	"fmt"

	"blacking-api/pkg/jaijaipay"
)

// updateGameUsernameBeforeCredit updates member's game_username before processing deposit credit
func (s *JaiJaiPayService) updateGameUsernameBeforeCredit(ctx context.Context, payload *jaijaipay.WebhookPayload) error {
	log := s.logger.WithContext(ctx).WithField("operation", "updateGameUsernameBeforeCredit").With<PERSON>ield("transaction_id", payload.TransactionID)

	// Get member by customer reference (member ID)
	member, err := s.paymentGatewayService.memberRepo.GetByID(ctx, payload.CustomerReference)
	if err != nil {
		log.WithError(err).WithField("customer_reference", payload.CustomerReference).Error("failed to get member for game username update")
		return fmt.Errorf("failed to get member: %w", err)
	}

	// Check if member has phone number
	if member.Phone == nil || *member.Phone == "" {
		log.Warn("member has no phone number, skipping game username update")
		return nil
	}

	// Check if member already has game_username
	if member.GameUsername != nil && *member.GameUsername != "" {
		log.WithField("existing_game_username", *member.GameUsername).Info("member already has game_username, skipping update")
		return nil
	}

	// Call UpdateGameUsernameFromAGAPI to get and update game_username
	if err := s.memberService.UpdateGameUsernameFromAGAPI(ctx, *member.Phone); err != nil {
		log.WithError(err).WithField("phone", *member.Phone).Error("failed to update game username from AG API")
		return fmt.Errorf("failed to update game username from AG API: %w", err)
	}

	log.WithField("phone", *member.Phone).Info("successfully updated game username from AG API before deposit credit")
	return nil
}
