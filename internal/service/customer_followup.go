package service

import (
	"context"
	"strconv"
	"time"

	"blacking-api/internal/domain/customer_call_log"
	"blacking-api/internal/domain/customer_followup"
	"blacking-api/internal/domain/customer_sms_log"
	"blacking-api/internal/domain/member"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
)

type CustomerFollowUpService interface {
	// List customer follow-up with comprehensive filters
	ListCustomerFollowUp(ctx context.Context, limit, offset int, filter *customer_followup.CustomerFollowUpFilter) ([]*customer_followup.CustomerFollowUpResponse, int64, error)

	// List old customers based on last_online filter
	ListOldCustomerFollowUp(ctx context.Context, limit, offset int, filter *customer_followup.CustomerFollowUpOldFilter) ([]*customer_followup.CustomerFollowUpResponse, int64, error)

	// Track customer actions
	TrackCustomerByCall(ctx context.Context, req customer_followup.TrackCustomerByCallRequest, adminID int, adminName string) error
	TrackCustomerBySMS(ctx context.Context, req customer_followup.TrackCustomerBySMSRequest, adminID int, adminName string) error

	// Update member follow-up info
	UpdateFollowUpStatus(ctx context.Context, req customer_followup.UpdateFollowUpStatusRequest, adminID int, adminName string) error
	UpdateFollowUpTag(ctx context.Context, req customer_followup.UpdateFollowUpTagRequest, adminID int, adminName string) error
	UpdateMemberRemark(ctx context.Context, req customer_followup.UpdateMemberRemarkRequest, adminID int, adminName string) error

	// Get follow-up options
	GetFollowUpTags(ctx context.Context) []map[string]string
	GetFollowUpStatuses(ctx context.Context) []map[string]string
}

type customerFollowUpService struct {
	memberRepo            interfaces.MemberRepository
	callLogRepo           interfaces.CustomerCallLogRepository
	smsLogRepo            interfaces.CustomerSMSLogRepository
	memberAuditLogService MemberAuditLogService
	smsService            SMSService
	logger                logger.Logger
}

func NewCustomerFollowUpService(
	memberRepo interfaces.MemberRepository,
	callLogRepo interfaces.CustomerCallLogRepository,
	smsLogRepo interfaces.CustomerSMSLogRepository,
	memberAuditLogService MemberAuditLogService,
	smsService SMSService,
	logger logger.Logger,
) CustomerFollowUpService {
	return &customerFollowUpService{
		memberRepo:            memberRepo,
		callLogRepo:           callLogRepo,
		smsLogRepo:            smsLogRepo,
		memberAuditLogService: memberAuditLogService,
		smsService:            smsService,
		logger:                logger,
	}
}

func (s *customerFollowUpService) ListCustomerFollowUp(ctx context.Context, limit, offset int, filter *customer_followup.CustomerFollowUpFilter) ([]*customer_followup.CustomerFollowUpResponse, int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListCustomerFollowUp")

	// Convert customer follow-up filter to member filter
	memberFilter := s.convertToMemberFilter(filter)

	// Get members with filter
	members, err := s.memberRepo.ListWithFilter(ctx, limit, offset, memberFilter)
	if err != nil {
		log.WithError(err).Error("failed to list members for follow-up")
		return nil, 0, err
	}

	// Get total count
	total, err := s.memberRepo.CountWithFilter(ctx, memberFilter)
	if err != nil {
		log.WithError(err).Error("failed to count members for follow-up")
		return nil, 0, err
	}

	// Convert to follow-up responses with additional data
	responses := make([]*customer_followup.CustomerFollowUpResponse, len(members))
	for i, m := range members {
		response, err := s.buildFollowUpResponse(ctx, m)
		if err != nil {
			log.WithError(err).WithField("member_id", m.ID).Error("failed to build follow-up response")
			continue
		}
		responses[i] = response
	}

	log.WithField("count", len(responses)).WithField("total", total).Info("customer follow-up list retrieved successfully")
	return responses, total, nil
}

func (s *customerFollowUpService) ListOldCustomerFollowUp(ctx context.Context, limit, offset int, filter *customer_followup.CustomerFollowUpOldFilter) ([]*customer_followup.CustomerFollowUpResponse, int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListOldCustomerFollowUp")

	// Convert old customer filter to member filter
	memberFilter := s.convertOldFilterToMemberFilter(filter)

	// Get members with filter
	members, err := s.memberRepo.ListWithFilter(ctx, limit, offset, memberFilter)
	if err != nil {
		log.WithError(err).Error("failed to list old customers for follow-up")
		return nil, 0, err
	}

	// Get total count
	total, err := s.memberRepo.CountWithFilter(ctx, memberFilter)
	if err != nil {
		log.WithError(err).Error("failed to count old customers for follow-up")
		return nil, 0, err
	}

	// Convert to follow-up responses with additional data
	responses := make([]*customer_followup.CustomerFollowUpResponse, len(members))
	for i, m := range members {
		response, err := s.buildFollowUpResponse(ctx, m)
		if err != nil {
			log.WithError(err).WithField("member_id", m.ID).Error("failed to build follow-up response")
			continue
		}
		responses[i] = response
	}

	log.WithField("count", len(responses)).WithField("total", total).Info("old customer follow-up list retrieved successfully")
	return responses, total, nil
}

func (s *customerFollowUpService) TrackCustomerByCall(ctx context.Context, req customer_followup.TrackCustomerByCallRequest, adminID int, adminName string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "TrackCustomerByCall").WithField("member_id", req.MemberID)

	// Validate member exists
	member, err := s.memberRepo.GetByID(ctx, strconv.Itoa(req.MemberID))
	if err != nil {
		log.WithError(err).Error("failed to get member for call tracking")
		return err
	}

	// Create call log entry
	callLogReq := customer_call_log.CreateCustomerCallLogRequest{
		MemberID:     req.MemberID,
		Notes:        req.Notes,
		CallDuration: req.CallDuration,
		CallStatus:   req.CallStatus,
	}

	callLog := customer_call_log.NewCustomerCallLog(callLogReq, adminID, adminName)
	if err := s.callLogRepo.Create(ctx, callLog); err != nil {
		log.WithError(err).Error("failed to create call log")
		return err
	}

	// Update member follow-up status to "contacted"
	contactedStatus := customer_followup.StatusContacted
	member.FollowUpStatus = &contactedStatus
	member.ContactedBy = &adminID
	now := time.Now()
	member.LastContactAt = &now

	if err := s.memberRepo.Update(ctx, member); err != nil {
		log.WithError(err).Error("failed to update member follow-up status after call")
		return err
	}

	// Log audit trail
	username := ""
	if member.Username != nil {
		username = *member.Username
	}
	s.logFollowUpAudit(ctx, member.ID, username, adminID, adminName, "call_tracking", map[string]interface{}{
		"call_status": req.CallStatus,
		"notes":       req.Notes,
	})

	log.Info("customer call tracking completed successfully")
	return nil
}

func (s *customerFollowUpService) TrackCustomerBySMS(ctx context.Context, req customer_followup.TrackCustomerBySMSRequest, adminID int, adminName string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "TrackCustomerBySMS").WithField("member_id", req.MemberID)

	// Validate member exists
	member, err := s.memberRepo.GetByID(ctx, strconv.Itoa(req.MemberID))
	if err != nil {
		log.WithError(err).Error("failed to get member for SMS tracking")
		return err
	}

	// Create SMS log entry
	smsLogReq := customer_sms_log.CreateCustomerSMSLogRequest{
		MemberID:       req.MemberID,
		MessageContent: req.MessageContent,
	}

	smsLog := customer_sms_log.NewCustomerSMSLog(smsLogReq, adminID, adminName)

	// Try to send SMS if member has phone
	if member.Phone != nil && *member.Phone != "" {
		// Send SMS using SMS service (implementation depends on your SMS service)
		// For now, we'll mark as sent - you can integrate with actual SMS provider
		smsLog.SMSStatus = customer_sms_log.SMSStatusSent
	} else {
		smsLog.SMSStatus = customer_sms_log.SMSStatusFailed
		log.Warn("member has no phone number for SMS")
	}

	if err := s.smsLogRepo.Create(ctx, smsLog); err != nil {
		log.WithError(err).Error("failed to create SMS log")
		return err
	}

	// Update member follow-up status to "contacted"
	contactedStatus := customer_followup.StatusContacted
	member.FollowUpStatus = &contactedStatus
	member.ContactedBy = &adminID
	now := time.Now()
	member.LastContactAt = &now

	if err := s.memberRepo.Update(ctx, member); err != nil {
		log.WithError(err).Error("failed to update member follow-up status after SMS")
		return err
	}

	// Log audit trail
	username := ""
	if member.Username != nil {
		username = *member.Username
	}
	s.logFollowUpAudit(ctx, member.ID, username, adminID, adminName, "sms_tracking", map[string]interface{}{
		"message_content": req.MessageContent,
		"sms_status":      smsLog.SMSStatus,
	})

	log.Info("customer SMS tracking completed successfully")
	return nil
}

func (s *customerFollowUpService) UpdateFollowUpStatus(ctx context.Context, req customer_followup.UpdateFollowUpStatusRequest, adminID int, adminName string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateFollowUpStatus").WithField("member_id", req.MemberID)

	// Validate status
	if !customer_followup.ValidateFollowUpStatus(req.FollowUpStatus) {
		return errors.NewValidationError("invalid follow-up status")
	}

	// Get member
	member, err := s.memberRepo.GetByID(ctx, strconv.Itoa(req.MemberID))
	if err != nil {
		log.WithError(err).Error("failed to get member for status update")
		return err
	}

	oldStatus := ""
	if member.FollowUpStatus != nil {
		oldStatus = *member.FollowUpStatus
	}

	// Update follow-up status
	log.WithField("old_status", oldStatus).WithField("new_status", req.FollowUpStatus).WithField("admin_id", adminID).Info("updating member follow-up status")

	member.FollowUpStatus = &req.FollowUpStatus
	member.ContactedBy = &adminID
	now := time.Now()
	member.LastContactAt = &now

	if err := s.memberRepo.Update(ctx, member); err != nil {
		log.WithError(err).Error("failed to update member follow-up status")
		return err
	}

	log.WithField("member_id", member.ID).WithField("updated_status", *member.FollowUpStatus).Info("member updated in repository")

	// Log audit trail
	username := ""
	if member.Username != nil {
		username = *member.Username
	}
	s.logFollowUpAudit(ctx, member.ID, username, adminID, adminName, "status_update", map[string]interface{}{
		"old_status": oldStatus,
		"new_status": req.FollowUpStatus,
	})

	log.Info("member follow-up status updated successfully")
	return nil
}

func (s *customerFollowUpService) UpdateFollowUpTag(ctx context.Context, req customer_followup.UpdateFollowUpTagRequest, adminID int, adminName string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateFollowUpTag").WithField("member_id", req.MemberID)

	// Validate tag
	if !customer_followup.ValidateFollowUpTag(req.FollowUpTag) {
		return errors.NewValidationError("invalid follow-up tag")
	}

	// Get member
	member, err := s.memberRepo.GetByID(ctx, strconv.Itoa(req.MemberID))
	if err != nil {
		log.WithError(err).Error("failed to get member for tag update")
		return err
	}

	oldTag := ""
	if member.FollowUpTag != nil {
		oldTag = *member.FollowUpTag
	}

	// Update follow-up tag
	log.WithField("old_tag", oldTag).WithField("new_tag", req.FollowUpTag).WithField("admin_id", adminID).Info("updating member follow-up tag")

	member.FollowUpTag = &req.FollowUpTag
	member.ContactedBy = &adminID
	now := time.Now()
	member.LastContactAt = &now

	if err := s.memberRepo.Update(ctx, member); err != nil {
		log.WithError(err).Error("failed to update member follow-up tag")
		return err
	}

	log.WithField("member_id", member.ID).WithField("updated_tag", *member.FollowUpTag).WithField("contacted_by", *member.ContactedBy).Info("member tag updated in repository")

	// Log audit trail
	username := ""
	if member.Username != nil {
		username = *member.Username
	}
	s.logFollowUpAudit(ctx, member.ID, username, adminID, adminName, "tag_update", map[string]interface{}{
		"old_tag": oldTag,
		"new_tag": req.FollowUpTag,
	})

	log.Info("member follow-up tag updated successfully")
	return nil
}

func (s *customerFollowUpService) UpdateMemberRemark(ctx context.Context, req customer_followup.UpdateMemberRemarkRequest, adminID int, adminName string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateMemberRemark").WithField("member_id", req.MemberID)

	// Get member
	member, err := s.memberRepo.GetByID(ctx, strconv.Itoa(req.MemberID))
	if err != nil {
		log.WithError(err).Error("failed to get member for remark update")
		return err
	}

	oldRemark := ""
	if member.Remark != nil {
		oldRemark = *member.Remark
	}

	// Update remark
	member.Remark = req.Remark
	member.ContactedBy = &adminID
	now := time.Now()
	member.LastContactAt = &now

	if err := s.memberRepo.Update(ctx, member); err != nil {
		log.WithError(err).Error("failed to update member remark")
		return err
	}

	// Log audit trail
	newRemark := ""
	if req.Remark != nil {
		newRemark = *req.Remark
	}
	username := ""
	if member.Username != nil {
		username = *member.Username
	}
	s.logFollowUpAudit(ctx, member.ID, username, adminID, adminName, "remark_update", map[string]interface{}{
		"old_remark": oldRemark,
		"new_remark": newRemark,
	})

	log.Info("member remark updated successfully")
	return nil
}

func (s *customerFollowUpService) GetFollowUpTags(ctx context.Context) []map[string]string {
	return customer_followup.GetAllFollowUpTags()
}

func (s *customerFollowUpService) GetFollowUpStatuses(ctx context.Context) []map[string]string {
	return customer_followup.GetAllFollowUpStatuses()
}

// Helper methods

func (s *customerFollowUpService) convertOldFilterToMemberFilter(filter *customer_followup.CustomerFollowUpOldFilter) *member.MemberFilter {
	memberFilter := &member.MemberFilter{
		Search:   filter.Search,
		OrderBy:  "id",
		OrderDir: "desc",
	}

	if filter.OrderBy != "" {
		memberFilter.OrderBy = filter.OrderBy
	}
	if filter.OrderDir != "" {
		memberFilter.OrderDir = filter.OrderDir
	}

	// Set member status filter
	if len(filter.MemberStatus) > 0 {
		memberFilter.Status = filter.MemberStatus
	} else {
		// Default: show active members for follow-up
		memberFilter.Status = []member.Status{member.StatusActive}
	}

	// Add relationship filters
	if filter.PartnerID != nil {
		memberFilter.ReferUserID = filter.PartnerID
	}
	if filter.ChannelID != nil {
		memberFilter.ChannelID = filter.ChannelID
	}
	if filter.PlatformID != nil {
		memberFilter.PlatformID = filter.PlatformID
	}

	// Add follow-up tag filter
	if filter.Tag != "" && filter.Tag != "all" {
		memberFilter.FollowUpTag = filter.Tag
	}

	// Add follow-up status filter
	if filter.Status != "" && filter.Status != "all" {
		memberFilter.FollowUpStatus = filter.Status
	}

	// Add contacted by filter
	if filter.ContactedBy != nil {
		memberFilter.ContactedBy = filter.ContactedBy
	}

	// Add last_online date range filters (instead of created_at)
	if filter.StartDateTime != nil {
		memberFilter.LastOnlineStartDateTime = filter.StartDateTime
	}
	if filter.EndDateTime != nil {
		memberFilter.LastOnlineEndDateTime = filter.EndDateTime
	}

	return memberFilter
}

func (s *customerFollowUpService) convertToMemberFilter(filter *customer_followup.CustomerFollowUpFilter) *member.MemberFilter {
	memberFilter := &member.MemberFilter{
		Search:   filter.Search,
		OrderBy:  "id",
		OrderDir: "desc",
	}

	if filter.OrderBy != "" {
		memberFilter.OrderBy = filter.OrderBy
	}
	if filter.OrderDir != "" {
		memberFilter.OrderDir = filter.OrderDir
	}

	// Set member status filter
	if len(filter.MemberStatus) > 0 {
		memberFilter.Status = filter.MemberStatus
	} else {
		// Default: show active members for follow-up
		memberFilter.Status = []member.Status{member.StatusActive}
	}

	// Add relationship filters
	if filter.PartnerID != nil {
		memberFilter.ReferUserID = filter.PartnerID
	}
	if filter.ChannelID != nil {
		memberFilter.ChannelID = filter.ChannelID
	}
	if filter.PlatformID != nil {
		memberFilter.PlatformID = filter.PlatformID
	}

	// Add follow-up tag filter
	if filter.Tag != "" && filter.Tag != "all" {
		memberFilter.FollowUpTag = filter.Tag
	}

	// Add follow-up status filter
	if filter.Status != "" && filter.Status != "all" {
		memberFilter.FollowUpStatus = filter.Status
	}

	// Add contacted by filter
	if filter.ContactedBy != nil {
		memberFilter.ContactedBy = filter.ContactedBy
	}

	// Add date range filters
	if filter.StartDateTime != nil {
		memberFilter.StartDateTime = filter.StartDateTime
	}
	if filter.EndDateTime != nil {
		memberFilter.EndDateTime = filter.EndDateTime
	}

	return memberFilter
}

func (s *customerFollowUpService) buildFollowUpResponse(ctx context.Context, m *member.Member) (*customer_followup.CustomerFollowUpResponse, error) {
	// Get call count
	callCount, err := s.callLogRepo.CountByMemberID(ctx, m.ID)
	if err != nil {
		s.logger.WithError(err).WithField("member_id", m.ID).Warn("failed to get call count")
		callCount = 0
	}

	// Get SMS count
	smsCount, err := s.smsLogRepo.CountByMemberID(ctx, m.ID)
	if err != nil {
		s.logger.WithError(err).WithField("member_id", m.ID).Warn("failed to get SMS count")
		smsCount = 0
	}

	// Build response
	response := &customer_followup.CustomerFollowUpResponse{
		ID:              m.ID,
		Username:        m.Username,
		FirstName:       m.FirstName,
		LastName:        m.LastName,
		Phone:           m.Phone,
		Balance:         m.Balance,
		Status:          string(m.Status),
		CreatedAt:       m.CreatedAt,
		LastOnline:      m.LastOnline,
		FollowUpTag:     m.FollowUpTag,
		FollowUpStatus:  m.FollowUpStatus,
		ContactedBy:     m.ContactedBy,
		LastContactAt:   m.LastContactAt,
		MemberGroupName: m.MemberGroupName,
		ChannelName:     m.ChannelName,
		CallCount:       callCount,
		SMSCount:        smsCount,
		Remark:          m.Remark,
	}

	// Add platform name and other joined info if available
	// These would be populated by extended repository queries

	return response, nil
}

func (s *customerFollowUpService) logFollowUpAudit(ctx context.Context, memberID int, memberUsername string, adminID int, adminName string, action string, details map[string]interface{}) {
	if s.memberAuditLogService != nil {
		// Create audit log entry for follow-up actions
		// Implementation depends on your existing audit log structure
		s.logger.WithField("member_id", memberID).WithField("action", action).WithField("details", details).Info("follow-up action logged")
	}
}
