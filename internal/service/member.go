package service

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"blacking-api/internal/config"
	"blacking-api/internal/domain/login_attempt"
	"blacking-api/internal/domain/member"
	"blacking-api/internal/domain/otp"
	"blacking-api/internal/domain/system_setting"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/agapi"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/gameapi"
	"blacking-api/pkg/logger"
	"blacking-api/pkg/useragent"

	"github.com/gin-gonic/gin"
)

type MemberService interface {
	CreateMember(ctx context.Context, req member.CreateMemberRequest, adminId string, adminName string) (*member.MemberResponse, error)
	GetMemberByID(ctx context.Context, id string) (*member.MemberResponse, error)
	GetMemberByUsername(ctx context.Context, username string) (*member.MemberResponse, error)
	GetMemberByGameUsername(ctx context.Context, gameUsername string) (*member.MemberResponse, error)
	UpdateMember(ctx context.Context, id string, req member.UpdateMemberRequest, adminId string, adminName string) (*member.MemberResponse, error)
	DeleteMember(ctx context.Context, id string, req member.DeleteMemberRequest, adminId string, adminName string) error
	ListMembers(ctx context.Context, limit, offset int, search string) ([]*member.MemberResponse, int64, error)
	ListMembersWithFilter(ctx context.Context, limit, offset int, filter *member.MemberFilter) ([]*member.MemberResponse, int64, error)
	ValidateMemberCredentials(ctx *gin.Context, username, password string) (*member.MemberResponse, error)

	// OTP-based registration methods
	SendRegistrationOTP(ctx context.Context, req member.VerifySendOTPRequest) (*member.RegistrationOTPResponse, error)
	VerifyRegistrationOTP(ctx context.Context, req member.VerifyRegistrationOTPRequest, clientIP string) (bool, error)
	CompleteRegistration(ctx context.Context, req member.CompleteRegistrationRequest, clientIP string) (*member.MemberResponse, error)

	// Direct registration (for none mode)
	DirectRegistration(ctx context.Context, req member.CompleteRegistrationRequest, clientIP string) (*member.MemberResponse, error)

	// Status counts
	GetMemberStatusCounts(ctx context.Context) (*member.MemberStatusCountsResponse, error)

	// Password management
	ChangeMemberPassword(ctx context.Context, memberID string, req member.ChangeMemberPasswordRequest, adminID string, adminName string) error

	// Partner management
	ChangeMemberPartner(ctx context.Context, memberID string, req member.ChangeMemberPartnerRequest, adminID string, adminName string) error

	// Bank info management
	UpdateMemberBankInfo(ctx context.Context, memberID string, req member.UpdateBankInfoRequest, adminID, adminName string) error

	// Member status management
	SuspendMember(ctx context.Context, memberID string, req member.SuspendMemberRequest, adminID string, adminName string) error

	// Game username update after first deposit
	UpdateGameUsernameFromAGAPI(ctx context.Context, phone string) error

	// Game providers
	GetGameProviders(ctx context.Context, memberID string) (interface{}, error)
	GetGameProviderGroups(ctx context.Context) (interface{}, error)
	GetGameLists(ctx context.Context, memberID string, provider string) (interface{}, error)
	GetGameProviderInfo(ctx context.Context, memberID string, provider string) (interface{}, error)

	// Member profile with game data
	GetMemberProfileWithGameData(ctx context.Context, memberID string) (*member.MemberResponse, error)

	// Game username backfill
	BackfillGameUsername(ctx context.Context, memberID string) (string, error)
	BackfillGameUsernameV2(ctx context.Context, memberID string) (string, error)
}

type memberService struct {
	memberRepo            interfaces.MemberRepository
	systemSettingService  SystemSettingService
	loginAttemptRepo      interfaces.LoginAttemptRepository
	memberAuditLogService MemberAuditLogService
	otpService            OTPService
	referralGroupRepo     interfaces.ReferralGroupRepository
	memberGroupRepo       interfaces.MemberGroupRepository
	bankingRepo           interfaces.BankingRepository
	agClient              *agapi.Client
	gameAPIClient         *gameapi.Client
	config                *config.Config
	logger                logger.Logger
}

func NewMemberService(memberRepo interfaces.MemberRepository, loginAttemptRepo interfaces.LoginAttemptRepository, systemSettingService SystemSettingService, memberAuditLogService MemberAuditLogService, otpService OTPService, referralGroupRepo interfaces.ReferralGroupRepository, memberGroupRepo interfaces.MemberGroupRepository, bankingRepo interfaces.BankingRepository, agClient *agapi.Client, config *config.Config, logger logger.Logger) MemberService {
	// Initialize game API client
	gameAPIClient := gameapi.NewClient(config.Game.BaseURL, "bk10", config.Game.WinLossMemberInfoEndpoint, config.Game.UserDepositEndpoint)

	return &memberService{
		memberRepo:            memberRepo,
		loginAttemptRepo:      loginAttemptRepo,
		systemSettingService:  systemSettingService,
		memberAuditLogService: memberAuditLogService,
		otpService:            otpService,
		referralGroupRepo:     referralGroupRepo,
		memberGroupRepo:       memberGroupRepo,
		bankingRepo:           bankingRepo,
		agClient:              agClient,
		gameAPIClient:         gameAPIClient,
		config:                config,
		logger:                logger,
	}
}

func (s *memberService) CreateMember(ctx context.Context, req member.CreateMemberRequest, adminId string, adminName string) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CreateMember")

	adminIdInt, err := strconv.Atoi(adminId)
	if err != nil {
		return nil, errors.NewValidationError("invalid admin ID")
	}

	// Check if phone already exists
	existingMember, err := s.memberRepo.GetByPhone(ctx, req.Phone)
	if err == nil && existingMember != nil {
		log.WithField("phone", req.Phone).Error("phone already exists")
		return nil, errors.NewValidationError("phone already exists")
	}

	// Validate member_group_id
	log.WithField("member_group_id", req.MemberGroupID).Info("validating member group ID")
	_, err = s.memberGroupRepo.GetByID(ctx, req.MemberGroupID)
	if err != nil {
		log.WithError(err).WithField("member_group_id", req.MemberGroupID).Error("invalid member group ID")
		return nil, errors.NewValidationError("invalid member group ID")
	}

	// Validate bank_code exists in banking table
	if req.BankCode != "" {
		_, err := s.bankingRepo.GetByCode(ctx, req.BankCode)
		if err != nil {
			log.WithError(err).WithField("bank_code", req.BankCode).Error("invalid bank_code - not found in banking table")
			return nil, errors.NewValidationError(fmt.Sprintf("bank_code '%s' not found in banking table", req.BankCode))
		}
	}

	// Validate referral_group_id if provided
	if req.ReferralGroupID > 0 {
		referralGroup, err := s.referralGroupRepo.GetByID(ctx, req.ReferralGroupID)
		if err != nil {
			log.WithError(err).WithField("referral_group_id", req.ReferralGroupID).Error("referral group validation failed - detailed error")
			// Log the actual error for debugging
			log.WithField("error_type", fmt.Sprintf("%T", err)).WithField("error_message", err.Error()).Error("referral group error details")
			return nil, errors.NewValidationError(fmt.Sprintf("referral group ID %d not found or inactive - check if ID exists and status is 'active'", req.ReferralGroupID))
		}
		log.WithField("referral_group_id", req.ReferralGroupID).WithField("referral_group_name", referralGroup.Name).Info("referral group validation passed")
	}

	// Create new member (without systemSettingRepo dependency)
	newMember, err := member.NewMember(req, adminIdInt, s.memberRepo)
	if err != nil {
		log.WithError(err).Error("failed to create member domain object")
		return nil, err
	}

	// Save to repository (this will set the ID and temp username)
	if err := s.memberRepo.Create(ctx, newMember, "admin"); err != nil {
		log.WithError(err).Error("failed to save member to repository")
		return nil, err
	}

	// Skip username generation - will be updated from AG API response

	// Log member creation to audit trail
	username := ""
	if newMember.Username != nil {
		username = *newMember.Username
	}
	auditReq := CreateMemberAuditLog(newMember.ID, username, adminIdInt, adminName, newMember)
	if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
		log.WithError(err).Warn("failed to log member creation audit")
	}

	response := newMember.ToResponse()
	responseUsername := ""
	if response.Username != nil {
		responseUsername = *response.Username
	}
	log.WithField("member_id", response.ID).WithField("username", responseUsername).Info("member created successfully")
	return &response, nil
}

// SendRegistrationOTP sends OTP for member registration verification
func (s *memberService) SendRegistrationOTP(ctx context.Context, req member.VerifySendOTPRequest) (*member.RegistrationOTPResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "SendRegistrationOTP")

	// Validate phone format
	if req.Phone == "" {
		return nil, errors.NewValidationError("phone is required")
	}
	if !isPhoneNumber(req.Phone) {
		return nil, errors.NewValidationError("phone must be a valid Thai phone number (10 digits starting with 0)")
	}

	// Check if username already exists
	existingMember, err := s.memberRepo.GetByPhone(ctx, req.Phone)
	if err == nil && existingMember != nil {
		return nil, errors.NewValidationError("username already exists")
	}

	lastOtpReq := otp.GetLastVerifyOTPRequest{
		Type:      otp.OTPTypePhone,
		Purpose:   otp.OTPPurposeMemberRegistration,
		Recipient: req.Phone,
	}

	oOtp, _ := s.otpService.GetLastVerifyOTP(ctx, lastOtpReq)
	if oOtp != nil {
		if oOtp.ExpiresAt.After(time.Now()) {
			now := time.Now()
			remainingTime := time.Until(oOtp.ExpiresAt)
			totalSeconds := int(remainingTime.Seconds())
			remainingMinutes := totalSeconds / 60
			remainingSeconds := totalSeconds % 60

			// Debug log to check timezone issues
			logger.WithField("now", now).
				WithField("expires_at", oOtp.ExpiresAt).
				WithField("remaining_seconds", totalSeconds).
				Info("OTP time debug")

			var timeMsg string
			if remainingMinutes > 0 {
				timeMsg = fmt.Sprintf("%d นา  %d  นา", remainingMinutes, remainingSeconds)
			} else {
				timeMsg = fmt.Sprintf("%d  นา", remainingSeconds)
			}

			return nil, errors.NewValidationError(fmt.Sprintf("OTP ref=%s already sent, please wait %s for it to expire", oOtp.Reference, timeMsg))
		}
	}

	// Send OTP
	otpReq := otp.SendOTPRequest{
		Type:      otp.OTPTypePhone,
		Purpose:   otp.OTPPurposeMemberRegistration,
		Recipient: req.Phone,
	}

	otpResponse, err := s.otpService.SendOTP(ctx, otpReq)
	if err != nil {
		log.WithError(err).Error("failed to send registration OTP")
		return nil, err
	}

	message := "OTP sent successfully to your phone number"
	if otpResponse.Message != "" {
		message = otpResponse.Message
	}

	response := &member.RegistrationOTPResponse{
		Message:   message,
		Phone:     maskPhone(req.Phone),
		Reference: otpResponse.Reference,
		ExpiresIn: 300, // 5 minutes
	}

	log.WithField("phone", req.Phone).Info("registration OTP sent successfully")
	return response, nil
}

// VerifyRegistrationOTP verifies OTP and creates member account
func (s *memberService) VerifyRegistrationOTP(ctx context.Context, req member.VerifyRegistrationOTPRequest, clientIP string) (bool, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "VerifyRegistrationOTP")

	// Verify OTP
	verifyReq := otp.VerifyOTPRequest{
		Type:      otp.OTPTypePhone,
		Purpose:   otp.OTPPurposeMemberRegistration,
		Recipient: req.Phone,
		Code:      req.Code,
	}

	otpRecord, err := s.otpService.VerifyOTP(ctx, verifyReq)
	if err != nil {
		log.WithError(err).Error("OTP verification failed")
		return false, err
	}

	// Verify reference matches
	if otpRecord.Reference != req.Reference {
		return false, errors.NewValidationError("invalid reference code")
	}

	return true, nil
}

// CompleteRegistration verifies OTP and creates member account with full registration data
func (s *memberService) CompleteRegistration(ctx context.Context, req member.CompleteRegistrationRequest, clientIP string) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CompleteRegistration")

	// Check if username already exists (validate the username field)
	existingMember, err := s.memberRepo.GetByUsername(ctx, req.Username)
	if err == nil && existingMember != nil {
		return nil, errors.NewValidationError("username already exists")
	}

	// Check if phone already exists
	existingMemberByPhone, err := s.memberRepo.GetByPhone(ctx, req.Phone)
	if err == nil && existingMemberByPhone != nil {
		return nil, errors.NewValidationError("phone number already registered")
	}

	lastOtpReq := otp.GetInUsedOTPRequest{
		Type:      otp.OTPTypePhone,
		Purpose:   otp.OTPPurposeMemberRegistration,
		Recipient: req.Phone,
	}

	otpRecord, err := s.otpService.GetInUsedOTP(ctx, lastOtpReq)
	if err != nil {
		log.WithError(err).Error("failed to get last OTP record")
		return nil, err
	}

	if !otpRecord.IsUsed {
		return nil, errors.NewValidationError("please vertify OTP first")
	}

	// Create new member from complete registration
	newMember, err := member.NewMemberFromCompleteRegistration(req, clientIP, s.memberRepo)
	if err != nil {
		log.WithError(err).Error("failed to create member from complete registration")
		return nil, err
	}

	// Set default member group ID
	defaultMemberGroup, err := s.memberGroupRepo.GetDefault(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get default member group")
		return nil, err
	}
	newMember.MemberGroupID = &defaultMemberGroup.ID

	// Set default referral group ID
	defaultReferralGroup, err := s.referralGroupRepo.GetDefault(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get default referral group")
		return nil, err
	}
	newMember.ReferralGroupID = &defaultReferralGroup.ID

	// Save to repository (this will set the ID)
	if err := s.memberRepo.Create(ctx, newMember, clientIP); err != nil {
		log.WithError(err).Error("failed to save registered member to repository")
		return nil, err
	}

	// Call AG API to register member without username (will be updated on first deposit)
	if err := s.callAgentAPIWithoutUsername(ctx, newMember); err != nil {
		log.WithError(err).Warn("failed to register member with AG API during registration, but member is registered locally")
		// Don't fail the registration if AG API call fails
	}

	// Log member registration to audit trail
	username := ""
	if newMember.Username != nil {
		username = *newMember.Username
	}
	auditReq := CreateMemberAuditLog(newMember.ID, username, 0, "System", newMember) // Use 0 for system
	if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
		log.WithError(err).Warn("failed to log member registration audit")
	}

	response := newMember.ToResponse()
	responseUsername := ""
	if response.Username != nil {
		responseUsername = *response.Username
	}
	log.WithField("member_id", response.ID).WithField("username", responseUsername).Info("member registration completed successfully")
	return &response, nil
}

// DirectRegistration handles direct member registration without OTP verification (for none mode)
func (s *memberService) DirectRegistration(ctx context.Context, req member.CompleteRegistrationRequest, clientIP string) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "DirectRegistration")

	// Check if username already exists (validate the username field)
	existingMember, err := s.memberRepo.GetByUsername(ctx, req.Username)
	if err == nil && existingMember != nil {
		return nil, errors.NewValidationError("username already exists")
	}

	// Check if phone already exists
	existingMemberByPhone, err := s.memberRepo.GetByPhone(ctx, req.Phone)
	if err == nil && existingMemberByPhone != nil {
		return nil, errors.NewValidationError("phone number already registered")
	}

	// Create new member from complete registration (no OTP verification required)
	newMember, err := member.NewMemberFromCompleteRegistration(req, clientIP, s.memberRepo)
	if err != nil {
		log.WithError(err).Error("failed to create member from direct registration")
		return nil, err
	}

	// Set default member group ID
	defaultMemberGroup, err := s.memberGroupRepo.GetDefault(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get default member group")
		return nil, err
	}
	newMember.MemberGroupID = &defaultMemberGroup.ID

	// Set default referral group ID
	defaultReferralGroup, err := s.referralGroupRepo.GetDefault(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get default referral group")
		return nil, err
	}
	newMember.ReferralGroupID = &defaultReferralGroup.ID

	// Save to repository (this will set the ID)
	if err := s.memberRepo.Create(ctx, newMember, clientIP); err != nil {
		log.WithError(err).Error("failed to save directly registered member to repository")
		return nil, err
	}

	// Call AG API to register member without username (will be updated on first deposit)
	if err := s.callAgentAPIWithoutUsername(ctx, newMember); err != nil {
		log.WithError(err).Warn("failed to register member with AG API during registration, but member is registered locally")
		// Don't fail the registration if AG API call fails
	}

	// Log member registration to audit trail
	username := ""
	if newMember.Username != nil {
		username = *newMember.Username
	}
	auditReq := CreateMemberAuditLog(newMember.ID, username, 0, "System", newMember) // Use 0 for system
	if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
		log.WithError(err).Warn("failed to log direct member registration audit")
	}

	response := newMember.ToResponse()
	responseUsername := ""
	if response.Username != nil {
		responseUsername = *response.Username
	}
	log.WithField("member_id", response.ID).WithField("username", responseUsername).Info("direct member registration completed successfully")
	return &response, nil
}

// GetMemberStatusCounts returns count of members by status
func (s *memberService) GetMemberStatusCounts(ctx context.Context) (*member.MemberStatusCountsResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetMemberStatusCounts")

	statusCounts, err := s.memberRepo.GetStatusCounts(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get member status counts")
		return nil, err
	}

	// Calculate total count (active + suspended + banned)
	var totalCount int64
	for _, statusCount := range statusCounts {
		totalCount += statusCount.Count
	}

	// Add "ทั้งหมด" entry at the beginning
	allStatusCounts := []member.MemberStatusCount{
		{
			Status: member.StatusAll,
			Label:  member.GetStatusLabel(member.StatusAll),
			Count:  totalCount,
		},
	}

	// Append individual status counts
	allStatusCounts = append(allStatusCounts, statusCounts...)

	response := &member.MemberStatusCountsResponse{
		StatusCounts: allStatusCounts,
		TotalCount:   totalCount,
	}

	log.WithField("total_count", totalCount).WithField("status_types", len(allStatusCounts)).Info("member status counts retrieved successfully")
	return response, nil
}

// maskPhone masks phone number for display
func maskPhone(phone string) string {
	if len(phone) >= 4 {
		return phone[:3] + "****" + phone[len(phone)-2:]
	}
	return "***"
}

func (s *memberService) GetMemberByID(ctx context.Context, id string) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetMemberByID").WithField("member_id", id)

	m, err := s.memberRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get member by ID")
		return nil, err
	}

	response := m.ToResponse()
	return &response, nil
}

func (s *memberService) GetMemberByUsername(ctx context.Context, username string) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetMemberByUsername").WithField("username", username)

	m, err := s.memberRepo.GetByUsername(ctx, username)
	if err != nil {
		log.WithError(err).Error("failed to get member by username")
		return nil, err
	}

	response := m.ToResponse()
	return &response, nil
}

func (s *memberService) GetMemberByGameUsername(ctx context.Context, gameUsername string) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetMemberByGameUsername").WithField("game_username", gameUsername)

	m, err := s.memberRepo.GetByGameUsername(ctx, gameUsername)
	if err != nil {
		log.WithError(err).Error("failed to get member by game username")
		return nil, err
	}

	response := m.ToResponse()
	return &response, nil
}

func (s *memberService) UpdateMember(ctx context.Context, id string, req member.UpdateMemberRequest, adminId string, adminName string) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateMember").WithField("member_id", id)

	// Get existing member
	existingMember, err := s.memberRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get existing member")
		return nil, err
	}

	// Validate member_group_id (required)
	if req.MemberGroupID == nil {
		log.Error("member_group_id is required")
		return nil, errors.NewValidationError("member_group_id is required")
	}
	log.WithField("member_group_id", *req.MemberGroupID).Info("validating member group ID")
	_, err = s.memberGroupRepo.GetByID(ctx, *req.MemberGroupID)
	if err != nil {
		log.WithError(err).WithField("member_group_id", *req.MemberGroupID).Error("invalid member group ID")
		return nil, errors.NewValidationError("invalid member group ID")
	}

	// Validate referral_group_id (required)
	if req.ReferralGroupID == nil {
		log.Error("referral_group_id is required")
		return nil, errors.NewValidationError("referral_group_id is required")
	}
	if *req.ReferralGroupID <= 0 {
		log.Error("referral_group_id must be greater than 0")
		return nil, errors.NewValidationError("referral_group_id must be greater than 0")
	}
	_, err = s.referralGroupRepo.GetByID(ctx, *req.ReferralGroupID)
	if err != nil {
		log.WithError(err).WithField("referral_group_id", *req.ReferralGroupID).Error("invalid referral group ID")
		return nil, errors.NewValidationError("invalid referral group ID")
	}

	// Validate bank_code exists in banking table if provided
	if req.BankCode != nil && *req.BankCode != "" {
		_, err := s.bankingRepo.GetByCode(ctx, *req.BankCode)
		if err != nil {
			log.WithError(err).WithField("bank_code", *req.BankCode).Error("invalid bank_code - not found in banking table")
			return nil, errors.NewValidationError(fmt.Sprintf("bank_code '%s' not found in banking table", *req.BankCode))
		}
	}

	// Validate partner_id if provided
	if req.PartnerID != nil && *req.PartnerID > 0 {
		partner, err := s.memberRepo.GetByID(ctx, strconv.Itoa(*req.PartnerID))
		if err != nil {
			log.WithError(err).WithField("partner_id", *req.PartnerID).Error("invalid partner ID")
			return nil, errors.NewValidationError("invalid partner ID")
		}
		if !partner.IsPartner {
			log.WithField("partner_id", *req.PartnerID).Error("specified user is not a partner")
			return nil, errors.NewValidationError("specified user is not a partner")
		}
	}

	// Keep old values for audit log
	oldValues := *existingMember

	// Update member
	if err := existingMember.Update(req); err != nil {
		log.WithError(err).Error("failed to update member domain object")
		return nil, err
	}

	// Save to repository
	if err := s.memberRepo.Update(ctx, existingMember); err != nil {
		log.WithError(err).Error("failed to save updated member to repository")
		return nil, err
	}

	// Log member update to audit trail
	adminIdInt, err := strconv.Atoi(adminId)
	if err != nil {
		log.WithError(err).Warn("failed to convert admin ID to integer for audit log")
		adminIdInt = 0 // Use 0 as fallback
	}
	username := ""
	if existingMember.Username != nil {
		username = *existingMember.Username
	}
	auditReq := UpdateMemberAuditLog(existingMember.ID, username, adminIdInt, adminName, oldValues, *existingMember)
	if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
		log.WithError(err).Warn("failed to log member update audit")
	}

	response := existingMember.ToResponse()
	log.WithField("member_id", response.ID).Info("member updated successfully")
	return &response, nil
}

func (s *memberService) DeleteMember(ctx context.Context, id string, req member.DeleteMemberRequest, adminId string, adminName string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "DeleteMember").WithField("member_id", id)

	// Check if member exists and get member data for audit log
	existingMember, err := s.memberRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get member for deletion")
		return err
	}

	// Soft delete member
	if err := s.memberRepo.Delete(ctx, id); err != nil {
		log.WithError(err).Error("failed to delete member")
		return err
	}

	// Log member deletion to audit trail
	adminIdInt, err := strconv.Atoi(adminId)
	if err != nil {
		log.WithError(err).Warn("failed to convert admin ID to integer for audit log")
		adminIdInt = 0 // Use 0 as fallback
	}
	// Get action remark from request
	var actionRemark string
	if req.ActionRemark != nil {
		actionRemark = *req.ActionRemark
	}
	username := ""
	if existingMember.Username != nil {
		username = *existingMember.Username
	}
	auditReq := DeleteMemberAuditLog(existingMember.ID, username, adminIdInt, adminName, *existingMember, actionRemark)
	if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
		log.WithError(err).Warn("failed to log member deletion audit")
	}

	log.WithField("member_id", id).Info("member deleted successfully")
	return nil
}

func (s *memberService) SuspendMember(ctx context.Context, memberID string, req member.SuspendMemberRequest, adminID string, adminName string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "SuspendMember").WithField("member_id", memberID)

	// Check if member exists and get member data for audit log
	existingMember, err := s.memberRepo.GetByID(ctx, memberID)
	if err != nil {
		log.WithError(err).Error("failed to get member for suspension/unsuspension")
		return err
	}

	// Store old status for audit
	oldStatus := existingMember.Status
	var action string

	// Toggle suspend/unsuspend based on current status
	if existingMember.Status == member.StatusSuspended {
		// If currently suspended, unsuspend (activate)
		existingMember.Status = member.StatusActive
		action = "unsuspend"
	} else {
		// If not suspended, suspend
		existingMember.Status = member.StatusSuspended
		action = "suspend"
	}

	existingMember.UpdatedAt = time.Now()

	// Save updated member
	if err := s.memberRepo.Update(ctx, existingMember); err != nil {
		log.WithError(err).Error("failed to update member status")
		return err
	}

	// Log member status change to audit trail
	adminIdInt, err := strconv.Atoi(adminID)
	if err != nil {
		log.WithError(err).Warn("failed to convert admin ID to integer for audit log")
		adminIdInt = 0 // Use 0 as fallback
	}

	// Get action remark from request (preserved across toggle operations)
	var actionRemark string
	if req.ActionRemark != nil {
		actionRemark = *req.ActionRemark
	}

	// Use appropriate audit log based on action
	if action == "suspend" {
		username := ""
		if existingMember.Username != nil {
			username = *existingMember.Username
		}
		auditReq := SuspendMemberAuditLog(existingMember.ID, username, adminIdInt, adminName, actionRemark)
		if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
			log.WithError(err).Warn("failed to log member suspension audit")
		}
	} else {
		username := ""
		if existingMember.Username != nil {
			username = *existingMember.Username
		}
		auditReq := UnsuspendMemberAuditLog(existingMember.ID, username, adminIdInt, adminName, actionRemark)
		if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
			log.WithError(err).Warn("failed to log member unsuspension audit")
		}
	}

	log.WithField("member_id", memberID).WithField("action", action).WithField("old_status", oldStatus).WithField("new_status", existingMember.Status).Info("member status updated successfully")
	return nil
}

func (s *memberService) ListMembers(ctx context.Context, limit, offset int, search string) ([]*member.MemberResponse, int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListMembers")

	// Get members
	members, err := s.memberRepo.List(ctx, limit, offset, search)
	if err != nil {
		log.WithError(err).Error("failed to list members")
		return nil, 0, err
	}

	// Get total count
	total, err := s.memberRepo.Count(ctx, search)
	if err != nil {
		log.WithError(err).Error("failed to count members")
		return nil, 0, err
	}

	// Convert to response
	responses := make([]*member.MemberResponse, len(members))
	for i, m := range members {
		response := m.ToResponse()
		responses[i] = &response
	}

	log.WithField("count", len(responses)).WithField("total", total).Info("members listed successfully")
	return responses, total, nil
}

func (s *memberService) ListMembersWithFilter(ctx context.Context, limit, offset int, filter *member.MemberFilter) ([]*member.MemberResponse, int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListMembersWithFilter")

	// Get members with filter
	members, err := s.memberRepo.ListWithFilter(ctx, limit, offset, filter)
	if err != nil {
		log.WithError(err).Error("failed to list members with filter")
		return nil, 0, err
	}

	// Get total count with filter
	total, err := s.memberRepo.CountWithFilter(ctx, filter)
	if err != nil {
		log.WithError(err).Error("failed to count members with filter")
		return nil, 0, err
	}

	// Convert to response
	responses := make([]*member.MemberResponse, len(members))
	for i, m := range members {
		response := m.ToResponse()
		responses[i] = &response
	}

	log.WithField("count", len(responses)).WithField("total", total).Info("members with filter listed successfully")
	return responses, total, nil
}

func (s *memberService) ValidateMemberCredentials(ctx *gin.Context, username, password string) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ValidateMemberCredentials").WithField("username", username)

	if username == "" || password == "" {
		return nil, errors.NewValidationError("username and password are required")
	}

	// Get login attempt limit from system settings
	loginLimit, err := s.systemSettingService.GetLoginAttemptLimit(ctx.Request.Context())
	if err != nil {
		log.WithError(err).Warn("failed to get login attempt limit, continuing without limit")
		loginLimit = &system_setting.LoginAttemptLimitResponse{MaxAttempts: 0} // No limit
	}

	// Try to get member by username or phone
	var u *member.Member
	var loginIdentifier string

	if isPhoneNumber(username) {
		log.WithField("login_type", "phone").Info("attempting login with phone number")
		loginIdentifier = username
		u, err = s.memberRepo.GetByPhone(ctx.Request.Context(), username)
	} else {
		log.WithField("login_type", "username").Info("attempting login with username")
		loginIdentifier = username
		u, err = s.memberRepo.GetByUsername(ctx.Request.Context(), username)
	}

	if err != nil {
		// Record failed login attempt
		s.recordLoginAttempt(ctx, loginIdentifier, false)

		if errors.GetAppError(err) != nil && errors.GetAppError(err).Type == errors.NotFoundError {
			return nil, errors.NewUnauthorizedError("ไม่พบสมาชิกในระบบ")
		}
		log.WithError(err).Error("failed to get member for credential validation")
		return nil, err
	}

	// Check if user is locked due to too many failed attempts
	if loginLimit.MaxAttempts > 0 && !u.IsEnable {
		summary, err := s.loginAttemptRepo.GetAttemptsSummary(ctx.Request.Context(), loginIdentifier, loginLimit.MaxAttempts)
		if err != nil {
			log.WithError(err).Warn("failed to get login attempts summary")
		} else if summary.IsLocked {
			log.WithField("failed_attempts", summary.FailedAttempts).Warn("user is locked due to too many failed login attempts")
			return nil, errors.NewUnauthorizedError("account is temporarily locked due to too many failed login attempts")
		}
	}

	// Validate password
	if !u.CheckPassword(password) {
		// Record failed login attempt
		s.recordLoginAttempt(ctx, loginIdentifier, false)

		// Check if this failed attempt should lock the account
		if loginLimit.MaxAttempts > 0 {
			failedCount, err := s.loginAttemptRepo.GetFailedAttemptsCount(ctx.Request.Context(), loginIdentifier, time.Now().Add(-24*time.Hour))
			if err == nil && failedCount >= loginLimit.MaxAttempts {
				// Disable user account by setting is_enable = false
				falseValue := false
				u.IsEnable = falseValue
				u.UpdatedAt = time.Now()
				if updateErr := s.memberRepo.Update(ctx.Request.Context(), u); updateErr != nil {
					log.WithError(updateErr).Error("failed to disable user account after max login attempts")
				} else {
					// Log audit trail for account lockout
					username := ""
					if u.Username != nil {
						username = *u.Username
					}
					s.logAccountLockout(ctx.Request.Context(), strconv.Itoa(u.ID), username, failedCount)
					log.WithField("failed_attempts", failedCount).Warn("user account disabled due to max login attempts exceeded")
				}
			}
		}

		return nil, errors.NewUnauthorizedError("รหัสผ่านไม่ถูกต้อง")
	}

	// Check if user account is active
	if !u.IsActive() {
		// Record failed login attempt (account not active)
		s.recordLoginAttempt(ctx, loginIdentifier, false)
		return nil, errors.NewUnauthorizedError("บัญชีสมาชิกไม่ได้เปิดใช้งาน")
	}

	// Record successful login attempt
	s.recordLoginAttempt(ctx, loginIdentifier, true)

	s.loginAttemptRepo.ClearFailedAttempts(ctx.Request.Context(), loginIdentifier)

	// Update login tracking information
	clientIP := ctx.ClientIP()
	userAgentHeader := ctx.GetHeader("User-Agent")

	// Parse user agent to get device type
	deviceInfo := useragent.ParseUserAgent(userAgentHeader)
	var deviceType string
	if deviceInfo.DeviceType != nil {
		deviceType = *deviceInfo.DeviceType
	} else {
		deviceType = "unknown"
	}

	// Update login info in database
	if err := s.memberRepo.UpdateLoginInfo(ctx.Request.Context(), u.ID, clientIP, userAgentHeader, deviceType); err != nil {
		log.WithError(err).Warn("failed to update login info, but login succeeded")
		// Don't fail the login if we can't update the tracking info
	}

	response := u.ToResponse()
	log.Info("user credentials validated successfully")
	return &response, nil
}

// recordLoginAttempt records a login attempt (success or failure)
func (s *memberService) recordLoginAttempt(ctx *gin.Context, username string, success bool) {
	// Extract IP address and user agent from gin context
	ipAddress := ctx.ClientIP()
	userAgent := ctx.Request.UserAgent()

	// For admin users, set isAdmin=true, isMember=false
	// This is for admin login attempts
	attempt := login_attempt.NewLoginAttempt(username, ipAddress, userAgent, success, false, true)
	if err := s.loginAttemptRepo.Create(ctx.Request.Context(), attempt); err != nil {
		s.logger.WithError(err).WithField("username", username).Error("failed to record login attempt")
	}
}

// logAccountLockout logs an audit trail when account is locked due to max login attempts
func (s *memberService) logAccountLockout(ctx context.Context, memberID, username string, failedAttempts int) {
	if s.memberAuditLogService != nil {
		memberIDInt, err := strconv.Atoi(memberID)
		if err != nil {
			s.logger.WithError(err).Warn("failed to convert member ID to integer for audit log")
			memberIDInt = 0 // Use 0 as fallback
		}
		changedBy := 0 // System action (use 0 for system)
		changedByName := "System"
		oldValues := map[string]interface{}{
			"is_enable": true,
		}
		newValues := map[string]interface{}{
			"is_enable":       false,
			"lockout_reason":  "max_login_attempts_exceeded",
			"failed_attempts": failedAttempts,
		}

		auditReq := UpdateMemberAuditLog(memberIDInt, username, changedBy, changedByName, oldValues, newValues)
		if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
			s.logger.WithError(err).Warn("failed to log member account lockout audit")
		}
	}
}

// ChangeMemberPassword changes member password by admin
func (s *memberService) ChangeMemberPassword(ctx context.Context, memberID string, req member.ChangeMemberPasswordRequest, adminID string, adminName string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "ChangeMemberPassword").WithField("member_id", memberID)

	// Get member
	memberEntity, err := s.memberRepo.GetByID(ctx, memberID)
	if err != nil {
		log.WithError(err).Error("failed to get member for password change")
		return err
	}

	// Change password
	if err := memberEntity.ChangePasswordByAdmin(req.NewPassword, adminID, adminName); err != nil {
		log.WithError(err).Error("failed to change member password")
		return err
	}

	// Update member in database
	if err := s.memberRepo.Update(ctx, memberEntity); err != nil {
		log.WithError(err).Error("failed to update member after password change")
		return err
	}

	// Log audit trail
	if s.memberAuditLogService != nil {
		memberIDInt, err := strconv.Atoi(memberID)
		if err != nil {
			log.WithError(err).Warn("failed to convert member ID to integer for audit log")
			memberIDInt = 0
		}
		adminIDInt, err := strconv.Atoi(adminID)
		if err != nil {
			log.WithError(err).Warn("failed to convert admin ID to integer for audit log")
			adminIDInt = 0
		}

		username := ""
		if memberEntity.Username != nil {
			username = *memberEntity.Username
		}
		auditReq := PasswordChangeMemberAuditLog(memberIDInt, username, adminIDInt, adminName)
		if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
			log.WithError(err).Warn("failed to log member password change audit")
		}
	}

	log.Info("member password changed successfully by admin")
	return nil
}

// ChangeMemberPartner changes member partner (refer_user_id) by admin
func (s *memberService) ChangeMemberPartner(ctx context.Context, memberID string, req member.ChangeMemberPartnerRequest, adminID string, adminName string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "ChangeMemberPartner").WithField("member_id", memberID)

	// Get member
	memberEntity, err := s.memberRepo.GetByID(ctx, memberID)
	if err != nil {
		log.WithError(err).Error("failed to get member for partner change")
		return err
	}

	// Store old values for audit
	oldPartnerID := memberEntity.ReferUserID
	var oldPartnerName *string
	var newPartnerName *string

	// Get old partner name if exists
	if oldPartnerID != nil {
		if oldPartner, err := s.memberRepo.GetByID(ctx, strconv.Itoa(*oldPartnerID)); err == nil {
			if oldPartner.FirstName != nil {
				oldPartnerName = oldPartner.FirstName
			} else {
				oldPartnerName = oldPartner.Username
			}
		}
	}

	// Validate new partner if provided
	if req.PartnerID != nil {
		newPartner, err := s.memberRepo.GetByID(ctx, strconv.Itoa(*req.PartnerID))
		if err != nil {
			log.WithError(err).Error("failed to get new partner for validation")
			return errors.NewValidationError("invalid partner ID")
		}
		if !newPartner.IsPartner {
			return errors.NewValidationError("specified user is not a partner")
		}

		// Get new partner name
		if newPartner.FirstName != nil {
			newPartnerName = newPartner.FirstName
		} else {
			newPartnerName = newPartner.Username
		}

		// Update refer code if changing to a partner
		memberEntity.RegisterReferCode = &newPartner.ReferCode
	} else {
		// Clear refer code if removing partner
		memberEntity.RegisterReferCode = nil
	}

	// Update partner
	memberEntity.ReferUserID = req.PartnerID
	memberEntity.UpdatedAt = time.Now()

	// Update member in database
	if err := s.memberRepo.Update(ctx, memberEntity); err != nil {
		log.WithError(err).Error("failed to update member after partner change")
		return err
	}

	// Log audit trail
	if s.memberAuditLogService != nil {
		memberIDInt, err := strconv.Atoi(memberID)
		if err != nil {
			log.WithError(err).Warn("failed to convert member ID to integer for audit log")
			memberIDInt = 0
		}
		adminIDInt, err := strconv.Atoi(adminID)
		if err != nil {
			log.WithError(err).Warn("failed to convert admin ID to integer for audit log")
			adminIDInt = 0
		}

		oldValues := map[string]interface{}{
			"refer_user_id": oldPartnerID,
			"partner_name":  oldPartnerName,
		}
		newValues := map[string]interface{}{
			"refer_user_id": req.PartnerID,
			"partner_name":  newPartnerName,
			"action_remark": req.ActionRemark,
		}

		username := ""
		if memberEntity.Username != nil {
			username = *memberEntity.Username
		}
		auditReq := ChangePartnerMemberAuditLog(memberIDInt, username, adminIDInt, adminName, oldValues, newValues)
		if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
			log.WithError(err).Warn("failed to log member partner change audit")
		}
	}

	log.Info("member partner changed successfully by admin")
	return nil
}

// UpdateMemberBankInfo updates member bank information by admin
func (s *memberService) UpdateMemberBankInfo(ctx context.Context, memberID string, req member.UpdateBankInfoRequest, adminID, adminName string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateMemberBankInfo").WithField("member_id", memberID).WithField("admin_id", adminID)

	// Get member
	memberEntity, err := s.memberRepo.GetByID(ctx, memberID)
	if err != nil {
		log.WithError(err).Error("failed to get member for bank info update")
		return err
	}

	// Store old values for audit
	oldValues := map[string]interface{}{
		"bank_code":   memberEntity.BankCode,
		"bank_number": memberEntity.BankNumber,
		"first_name":  memberEntity.FirstName,
		"last_name":   memberEntity.LastName,
		"tw_username": memberEntity.TwUsername,
	}

	// Validate bank_code exists in banking table if provided
	if req.BankCode != nil && *req.BankCode != "" {
		_, err := s.bankingRepo.GetByCode(ctx, *req.BankCode)
		if err != nil {
			log.WithError(err).WithField("bank_code", *req.BankCode).Error("invalid bank_code - not found in banking table")
			return errors.NewValidationError(fmt.Sprintf("bank_code '%s' not found in banking table", *req.BankCode))
		}
	}

	// Update bank information
	if req.BankCode != nil {
		memberEntity.BankCode = req.BankCode
	}
	if req.BankNumber != nil {
		memberEntity.BankNumber = req.BankNumber
	}
	if req.FirstName != nil {
		memberEntity.FirstName = req.FirstName
	}
	if req.LastName != nil {
		memberEntity.LastName = req.LastName
	}
	if req.TwUsername != nil {
		memberEntity.TwUsername = req.TwUsername
	}

	memberEntity.UpdatedAt = time.Now()

	// Update member in database
	if err := s.memberRepo.Update(ctx, memberEntity); err != nil {
		log.WithError(err).Error("failed to update member after bank info change")
		return err
	}

	// Prepare new values for audit
	newValues := map[string]interface{}{
		"bank_code":   memberEntity.BankCode,
		"bank_number": memberEntity.BankNumber,
		"first_name":  memberEntity.FirstName,
		"last_name":   memberEntity.LastName,
		"tw_username": memberEntity.TwUsername,
		"title":       req.Title, // Add title for audit log
	}

	// Log audit trail (admin updates member info)
	if s.memberAuditLogService != nil {
		memberIDInt, err := strconv.Atoi(memberID)
		if err != nil {
			log.WithError(err).Warn("failed to convert member ID to integer for audit log")
			memberIDInt = 0
		}

		adminIDInt, err := strconv.Atoi(adminID)
		if err != nil {
			log.WithError(err).Warn("failed to convert admin ID to integer for audit log")
			adminIDInt = 0
		}

		username := ""
		if memberEntity.Username != nil {
			username = *memberEntity.Username
		}
		auditReq := UpdateBankInfoMemberAuditLog(memberIDInt, username, adminIDInt, adminName, oldValues, newValues)
		if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
			log.WithError(err).Warn("failed to log member bank info update audit")
		}
	}

	log.Info("member bank information updated successfully by admin")
	return nil
}

// isPhoneNumber checks if the input string looks like a Thai phone number
// Thai phone numbers are typically 10 digits starting with 0 (e.g., **********)
func isPhoneNumber(input string) bool {
	// Check if input contains only digits and is exactly 10 characters long
	phoneRegex := regexp.MustCompile(`^0[0-9]{9}$`)
	return phoneRegex.MatchString(input)
}

// callAgentAPIWithoutUsername calls the AG API to register member without setting username (for initial registration)
func (s *memberService) callAgentAPIWithoutUsername(ctx context.Context, memberData *member.Member) error {
	log := s.logger.WithContext(ctx).WithField("operation", "callAgentAPIWithoutUsername").WithField("member_id", memberData.ID)

	if s.agClient == nil {
		log.Warn("AG API client not configured, skipping AG API call")
		return nil
	}

	log.Info("Starting AG API registration process without username")

	// Prepare AG API request
	var email, firstName, lastName, phone string

	// Handle optional fields with default values
	if memberData.FirstName != nil && *memberData.FirstName != "" {
		firstName = *memberData.FirstName
	} else {
		firstName = "Member" // Default first name
	}

	if memberData.LastName != nil && *memberData.LastName != "" {
		lastName = *memberData.LastName
	} else {
		lastName = "Member" // Default last name
	}

	if memberData.Phone != nil {
		phone = *memberData.Phone
	}

	// Use phone as email
	email = phone + "@example.com"

	agReq := agapi.MemberRegisterRequest{
		Username:  "", // Empty username - will be set later during first deposit
		Email:     email,
		FirstName: firstName,
		LastName:  lastName,
		Phone:     phone,
		Password:  "default_password", // Use a default password
	}

	// Call AG API
	_, err := s.agClient.RegisterMember(ctx, agReq)
	if err != nil {
		// Check if it's a connection issue (AG server not available)
		if isAGAPIUnavailable(err) {
			log.WithError(err).Warn("AG API server is not available, member registered locally only")
			return nil // Don't fail the registration
		}
		log.WithError(err).Error("failed to register member with AG API")
		return fmt.Errorf("failed to register member with AG API: %w", err)
	}

	log.WithField("phone", phone).Info("member successfully registered with AG API without username")
	return nil
}

// callAgentAPI calls the AG API to register member with providers
func (s *memberService) callAgentAPI(ctx context.Context, memberData *member.Member) error {
	username := ""
	if memberData.Username != nil {
		username = *memberData.Username
	}
	log := s.logger.WithContext(ctx).WithField("operation", "callAgentAPI").WithField("member_id", memberData.ID).WithField("username", username)

	if s.agClient == nil {
		log.Warn("AG API client not configured, skipping AG API call")
		return nil
	}

	log.Info("Starting AG API registration process")

	// Prepare AG API request
	var email, firstName, lastName, phone string

	// Handle optional fields with default values
	if memberData.FirstName != nil && *memberData.FirstName != "" {
		firstName = *memberData.FirstName
	} else {
		firstName = "Member" // Default first name
	}

	if memberData.LastName != nil && *memberData.LastName != "" {
		lastName = *memberData.LastName
	} else {
		if memberData.Username != nil {
			lastName = *memberData.Username // Use username as last name
		} else {
			lastName = "Member" // Default last name if no username
		}
	}

	if memberData.Phone != nil {
		phone = *memberData.Phone
	}

	// Use username as email if no separate email field, otherwise use phone
	if memberData.Username != nil {
		email = *memberData.Username + "@example.com"
	} else {
		email = phone + "@example.com"
	}

	// Use phone as username if memberData.Username is nil
	usernameForAPI := phone
	if memberData.Username != nil {
		usernameForAPI = *memberData.Username
	}

	agReq := agapi.MemberRegisterRequest{
		Username:  usernameForAPI,
		Email:     email,
		FirstName: firstName,
		LastName:  lastName,
		Phone:     phone,
		Password:  "default_password", // Use a default password or get from member
	}

	// Call AG API
	agResp, err := s.agClient.RegisterMember(ctx, agReq)
	if err != nil {
		// Check if it's a connection issue (AG server not available)
		if isAGAPIUnavailable(err) {
			log.WithError(err).Warn("AG API server is not available, member registered locally only")
			return nil // Don't fail the registration
		}
		log.WithError(err).Error("failed to register member with AG API")
		return fmt.Errorf("failed to register member with AG API: %w", err)
	}

	log.WithField("member_code", agResp.Data.MemberCode).WithField("username", agResp.Data.Username).Info("member successfully registered with AG API")

	// Update member with AG API response data
	if err := s.updateMemberWithAGData(ctx, int64(memberData.ID), agResp); err != nil {
		log.WithError(err).Error("failed to update member with AG API data")
		return fmt.Errorf("failed to update member with AG API data: %w", err)
	}

	log.WithField("updated_username", agResp.Data.MemberCode).WithField("updated_game_username", agResp.Data.Username).Info("member updated with AG API data")
	return nil
}

// updateMemberWithAGData updates member with data returned from AG API
func (s *memberService) updateMemberWithAGData(ctx context.Context, memberID int64, agResp *agapi.MemberRegisterResponse) error {
	log := s.logger.WithContext(ctx).WithField("operation", "updateMemberWithAGData").WithField("member_id", memberID)

	// Get current member data
	currentMember, err := s.memberRepo.GetByID(ctx, strconv.FormatInt(memberID, 10))
	if err != nil {
		log.WithError(err).Error("failed to get member by ID")
		return fmt.Errorf("failed to get member by ID: %w", err)
	}

	// Update username with memberCode and game_username with username from AG response
	// According to user: memberCode -> username column, username -> game_username column
	updatedMember := *currentMember
	updatedMember.Username = &agResp.Data.MemberCode   // memberCode goes to username column
	updatedMember.GameUsername = &agResp.Data.Username // username goes to game_username column

	// Update member in database
	if err := s.memberRepo.Update(ctx, &updatedMember); err != nil {
		oldUsername := ""
		if currentMember.Username != nil {
			oldUsername = *currentMember.Username
		}
		newUsername := ""
		if updatedMember.Username != nil {
			newUsername = *updatedMember.Username
		}

		log.WithError(err).WithFields(map[string]interface{}{
			"member_id":         currentMember.ID,
			"old_username":      oldUsername,
			"new_username":      newUsername,
			"old_game_username": currentMember.GameUsername,
			"new_game_username": updatedMember.GameUsername,
		}).Error("failed to update member with AG data")
		return fmt.Errorf("failed to update member with AG data: %w", err)
	}

	oldUsername := ""
	if currentMember.Username != nil {
		oldUsername = *currentMember.Username
	}
	newUsername := ""
	if updatedMember.Username != nil {
		newUsername = *updatedMember.Username
	}

	log.WithFields(map[string]interface{}{
		"old_username":      oldUsername,
		"new_username":      newUsername,
		"old_game_username": currentMember.GameUsername,
		"new_game_username": updatedMember.GameUsername,
	}).Info("member successfully updated with AG API data")

	return nil
}

// isAGAPIUnavailable checks if the error indicates AG API server is unavailable
func isAGAPIUnavailable(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	return strings.Contains(errStr, "connection refused") ||
		strings.Contains(errStr, "server is not available") ||
		strings.Contains(errStr, "dial tcp") ||
		strings.Contains(errStr, "no such host")
}

// UpdateGameUsernameFromAGAPI calls AG API to get member username and updates local game_username
func (s *memberService) UpdateGameUsernameFromAGAPI(ctx context.Context, phone string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateGameUsernameFromAGAPI").WithField("phone", phone)

	// Get member by phone
	member, err := s.memberRepo.GetByPhone(ctx, phone)
	if err != nil {
		log.WithError(err).Error("failed to get member by phone")
		return fmt.Errorf("failed to get member by phone: %w", err)
	}

	// Check if AG API client is configured
	if s.agClient == nil {
		log.Warn("AG API client not configured, skipping game username update")
		return nil
	}

	// Create AG API URL for getting member by phone
	agAPIURL := fmt.Sprintf("%s/members/by-phone/%s", strings.TrimSuffix(s.agClient.GetBaseURL(), "/"), phone)

	// Create HTTP GET request to retrieve member data
	req, err := http.NewRequestWithContext(ctx, "GET", agAPIURL, nil)
	if err != nil {
		log.WithError(err).Error("failed to create HTTP request")
		return fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Add Basic Auth header
	authHeader := s.agClient.CreateAuthHeader()
	req.Header.Set("Authorization", authHeader)
	req.Header.Set("Content-Type", "application/json")

	// Log request details
	log.WithFields(map[string]interface{}{
		"url":    agAPIURL,
		"phone":  phone,
		"method": "GET",
	}).Info("calling AG API to get member username")

	// Make HTTP request to AG API
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		log.WithError(err).Error("failed to call AG API")
		return fmt.Errorf("failed to call AG API: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.WithError(err).Error("failed to read AG API response")
		return fmt.Errorf("failed to read AG API response: %w", err)
	}

	// Log response details
	log.WithFields(map[string]interface{}{
		"status_code":   resp.StatusCode,
		"response_body": string(bodyBytes),
	}).Info("AG API response received")

	// Check if request was successful
	if resp.StatusCode != http.StatusOK {
		log.WithFields(map[string]interface{}{
			"status_code":   resp.StatusCode,
			"response_body": string(bodyBytes),
		}).Error("AG API returned non-OK status")
		return fmt.Errorf("AG API returned status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse response to get member with providers
	var agResponse struct {
		Success bool `json:"success"`
		Data    struct {
			Member struct {
				Username string `json:"username"`
			} `json:"member"`
		} `json:"data"`
	}

	if err := json.Unmarshal(bodyBytes, &agResponse); err != nil {
		log.WithError(err).WithField("response_body", string(bodyBytes)).Error("failed to parse AG API response")
		return fmt.Errorf("failed to parse AG API response: %w", err)
	}

	if !agResponse.Success {
		log.Error("AG API returned unsuccessful response")
		return fmt.Errorf("AG API returned unsuccessful response")
	}

	// Get username from AG API (this should be the game username from game service)
	gameUsername := agResponse.Data.Member.Username
	if gameUsername == "" {
		log.Warn("AG API member has empty username")
		return fmt.Errorf("AG API member has empty username")
	}

	// Update game_username with the username from AG API
	member.GameUsername = &gameUsername
	member.UpdatedAt = time.Now()

	// Save to database
	if err := s.memberRepo.Update(ctx, member); err != nil {
		log.WithError(err).Error("failed to update member game_username")
		return fmt.Errorf("failed to update member game_username: %w", err)
	}

	log.WithField("game_username", gameUsername).Info("successfully updated game_username from AG API")
	return nil
}

// GetGameProviders calls Game API to get available game providers for member
func (s *memberService) GetGameProviders(ctx context.Context, memberID string) (interface{}, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetGameProviders").WithField("member_id", memberID)

	// Get member by ID
	member, err := s.memberRepo.GetByID(ctx, memberID)
	if err != nil {
		log.WithError(err).Error("failed to get member by ID")
		return nil, fmt.Errorf("failed to get member: %w", err)
	}

	// Check if member has game_username
	if member.GameUsername == nil || *member.GameUsername == "" {
		log.Warn("member does not have game_username")
		return nil, fmt.Errorf("member does not have game_username")
	}

	// Get AG API URL from agClient config
	if s.agClient == nil {
		log.Warn("AG API client not configured, skipping game providers")
		return nil, fmt.Errorf("AG API client not configured")
	}

	agAPIURL := fmt.Sprintf("%s/providers", strings.TrimSuffix(s.agClient.GetBaseURL(), "/"))

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "GET", agAPIURL, nil)
	if err != nil {
		log.WithError(err).Error("failed to create AG API request")
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add Basic Auth header (same method as other AG API calls)
	authHeader := s.agClient.CreateAuthHeader()
	req.Header.Set("Authorization", authHeader)
	req.Header.Set("Content-Type", "application/json")

	// Log request details
	log.WithFields(map[string]interface{}{
		"url":           agAPIURL,
		"game_username": *member.GameUsername,
	}).Info("calling AG API for game providers")

	// Make HTTP request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		log.WithError(err).Error("failed to call AG API")
		return nil, fmt.Errorf("failed to call AG API: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.WithError(err).Error("failed to read AG API response")
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Log response details
	log.WithFields(map[string]interface{}{
		"status_code":   resp.StatusCode,
		"response_body": string(bodyBytes),
	}).Info("AG API providers response received")

	// Check if request was successful
	if resp.StatusCode != http.StatusOK {
		log.WithFields(map[string]interface{}{
			"status_code":   resp.StatusCode,
			"response_body": string(bodyBytes),
		}).Error("AG API returned non-OK status")
		return nil, fmt.Errorf("AG API returned status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse AG API response
	var agResponse struct {
		Success bool `json:"success"`
		Data    struct {
			Data       interface{} `json:"data"`
			Pagination interface{} `json:"pagination"`
		} `json:"data"`
		Message string `json:"message"`
	}
	if err := json.Unmarshal(bodyBytes, &agResponse); err != nil {
		log.WithError(err).Error("failed to parse AG API response")
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	if !agResponse.Success {
		log.Error("AG API returned unsuccessful response")
		return nil, fmt.Errorf("AG API returned unsuccessful response")
	}

	log.WithField("game_username", *member.GameUsername).Info("successfully retrieved game providers from AG API")
	// Return only the providers data array to flatten the structure
	return agResponse.Data.Data, nil
}

// GetGameProviderGroups calls Game API to get game provider groups
func (s *memberService) GetGameProviderGroups(ctx context.Context) (interface{}, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetGameProviderGroups")

	if s.gameAPIClient == nil {
		log.Error("Game API client not configured")
		return nil, fmt.Errorf("Game API client not configured")
	}

	log.Info("calling Game API for game provider groups")

	response, err := s.gameAPIClient.GetGameProviderGroups(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get game provider groups from Game API")
		return nil, fmt.Errorf("failed to get game provider groups: %w", err)
	}

	log.WithField("groups_count", len(response.Data)).Info("successfully retrieved game provider groups from Game API")

	return response.Data, nil
}

// GetGameLists calls Game API to get available game lists for member by provider
func (s *memberService) GetGameLists(ctx context.Context, memberID string, provider string) (interface{}, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetGameLists").WithField("member_id", memberID).WithField("provider", provider)

	// Get member by ID
	member, err := s.memberRepo.GetByID(ctx, memberID)
	if err != nil {
		log.WithError(err).Error("failed to get member by ID")
		return nil, fmt.Errorf("failed to get member: %w", err)
	}

	// Check if member has game_username
	if member.GameUsername == nil || *member.GameUsername == "" {
		log.Warn("member does not have game_username")
		return nil, fmt.Errorf("member does not have game_username")
	}

	// Get Game API URL from config
	if s.config.Game.BaseURL == "" {
		log.Error("Game API URL not configured")
		return nil, fmt.Errorf("Game API URL not configured")
	}

	gameAPIURL := fmt.Sprintf("%s/transfer/game-lists/%s", strings.TrimSuffix(s.config.Game.BaseURL, "/"), provider)

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "GET", gameAPIURL, nil)
	if err != nil {
		log.WithError(err).Error("failed to create Game API request")
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Create Basic Auth header using member's game_username and agent secret key
	credentials := fmt.Sprintf("%s:%s", *member.GameUsername, s.config.Agent.SecretKey)
	encoded := base64.StdEncoding.EncodeToString([]byte(credentials))
	req.Header.Set("Authorization", fmt.Sprintf("Basic %s", encoded))
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("Cache-Control", "no-cache")

	// Log request details
	log.WithFields(map[string]interface{}{
		"url":           gameAPIURL,
		"game_username": *member.GameUsername,
		"provider":      provider,
	}).Info("calling Game API for game lists")

	// Make HTTP request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		log.WithError(err).Error("failed to call Game API")
		return nil, fmt.Errorf("failed to call Game API: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.WithError(err).Error("failed to read Game API response")
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Log response details
	log.WithFields(map[string]interface{}{
		"status_code":   resp.StatusCode,
		"response_body": string(bodyBytes),
	}).Info("Game API response received")

	// Check if request was successful
	if resp.StatusCode != http.StatusOK {
		log.WithFields(map[string]interface{}{
			"status_code":   resp.StatusCode,
			"response_body": string(bodyBytes),
		}).Error("Game API returned non-OK status")
		return nil, fmt.Errorf("Game API returned status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse response and extract data field only
	var gameResponse struct {
		Data    interface{} `json:"data"`
		Message string      `json:"message"`
		Status  string      `json:"status"`
	}
	if err := json.Unmarshal(bodyBytes, &gameResponse); err != nil {
		log.WithError(err).Error("failed to parse Game API response")
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"game_username": *member.GameUsername,
		"provider":      provider,
	}).Info("successfully retrieved game lists from Game API")
	// Return only the data field to avoid nested data structure
	return gameResponse.Data, nil
}

// GetGameProviderInfo calls Game API to get specific game provider info for member
func (s *memberService) GetGameProviderInfo(ctx context.Context, memberID string, provider string) (interface{}, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetGameProviderInfo").WithField("member_id", memberID).WithField("provider", provider)

	// Get member by ID
	member, err := s.memberRepo.GetByID(ctx, memberID)
	if err != nil {
		log.WithError(err).Error("failed to get member by ID")
		return nil, fmt.Errorf("failed to get member: %w", err)
	}

	// Check if member has game_username
	if member.GameUsername == nil || *member.GameUsername == "" {
		log.Warn("member does not have game_username")
		return nil, fmt.Errorf("member does not have game_username")
	}

	// Get Game API URL from config
	if s.config.Game.BaseURL == "" {
		log.Error("Game API URL not configured")
		return nil, fmt.Errorf("Game API URL not configured")
	}

	gameAPIURL := fmt.Sprintf("%s/transfer/game-providers/%s", strings.TrimSuffix(s.config.Game.BaseURL, "/"), provider)

	// Prepare form data (as per curl example)
	formData := url.Values{}
	formData.Set("username", *member.GameUsername)
	formData.Set("amount", "10")
	formData.Set("reference_id", "")

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "GET", gameAPIURL, strings.NewReader(formData.Encode()))
	if err != nil {
		log.WithError(err).Error("failed to create Game API request")
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Create Basic Auth header using member's game_username and agent secret key
	credentials := fmt.Sprintf("%s:%s", *member.GameUsername, s.config.Agent.SecretKey)
	encoded := base64.StdEncoding.EncodeToString([]byte(credentials))
	req.Header.Set("Authorization", fmt.Sprintf("Basic %s", encoded))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("Cache-Control", "no-cache")

	// Log request details
	log.WithFields(map[string]interface{}{
		"url":           gameAPIURL,
		"game_username": *member.GameUsername,
		"provider":      provider,
	}).Info("calling Game API for game provider info")

	// Make HTTP request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		log.WithError(err).Error("failed to call Game API")
		return nil, fmt.Errorf("failed to call Game API: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.WithError(err).Error("failed to read Game API response")
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Log response details
	log.WithFields(map[string]interface{}{
		"status_code":   resp.StatusCode,
		"response_body": string(bodyBytes),
	}).Info("Game API response received")

	// Check if request was successful
	if resp.StatusCode != http.StatusOK {
		log.WithFields(map[string]interface{}{
			"status_code":   resp.StatusCode,
			"response_body": string(bodyBytes),
		}).Error("Game API returned non-OK status")
		return nil, fmt.Errorf("Game API returned status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse response and extract data field only
	var gameResponse struct {
		Data    interface{} `json:"data"`
		Message string      `json:"message"`
		Status  string      `json:"status"`
	}
	if err := json.Unmarshal(bodyBytes, &gameResponse); err != nil {
		log.WithError(err).Error("failed to parse Game API response")
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"game_username": *member.GameUsername,
		"provider":      provider,
	}).Info("successfully retrieved game provider info from Game API")
	// Return only the data field to avoid nested data structure
	return gameResponse.Data, nil
}

// GetMemberProfileWithGameData gets member profile and syncs wallet/providers from Game API
func (s *memberService) GetMemberProfileWithGameData(ctx context.Context, memberID string) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetMemberProfileWithGameData").WithField("member_id", memberID)

	// Get member by ID
	member, err := s.memberRepo.GetByID(ctx, memberID)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"member_id":    memberID,
			"error_type":   fmt.Sprintf("%T", err),
			"error_detail": err.Error(),
		}).Error("detailed error getting member by ID from GetMemberProfileWithGameData")
		return nil, fmt.Errorf("failed to get member: %w", err)
	}

	// If member has game_username, sync wallet and providers from Game API
	if member.GameUsername != nil && *member.GameUsername != "" {
		if err := s.syncMemberGameData(ctx, member); err != nil {
			log.WithError(err).Warn("failed to sync game data, returning member data without sync")
			// Continue without failing - return member data even if game sync fails
		}
	}

	response := member.ToResponse()
	log.WithField("member_id", memberID).Info("member profile retrieved")
	return &response, nil
}

// syncMemberGameData syncs member wallet and providers from Game API
func (s *memberService) syncMemberGameData(ctx context.Context, member *member.Member) error {
	log := s.logger.WithContext(ctx).WithField("operation", "syncMemberGameData").WithField("game_username", *member.GameUsername)

	// Get Agent API URL from config
	if s.config.Agent.BaseURL == "" {
		log.Error("Agent API URL not configured")
		return fmt.Errorf("Agent API URL not configured")
	}

	agentAPIURL := fmt.Sprintf("%s/users/update-data", strings.TrimSuffix(s.config.Agent.BaseURL, "/"))

	// Create request payload with username
	requestPayload := map[string]string{
		"username": *member.GameUsername,
	}

	payloadBytes, err := json.Marshal(requestPayload)
	if err != nil {
		log.WithError(err).Error("failed to marshal request payload")
		return fmt.Errorf("failed to marshal request payload: %w", err)
	}

	// Create HTTP request with JSON body
	req, err := http.NewRequestWithContext(ctx, "POST", agentAPIURL, bytes.NewBuffer(payloadBytes))
	if err != nil {
		log.WithError(err).Error("failed to create Game API request")
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	// Create Bearer token header using agent secret key
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", s.config.Agent.SecretKey))

	// Log request details
	log.WithFields(map[string]interface{}{
		"url":           agentAPIURL,
		"game_username": *member.GameUsername,
		"method":        "POST",
		"payload":       string(payloadBytes),
		"content_type":  "application/json",
		"auth_header":   fmt.Sprintf("Bearer %s", s.config.Agent.SecretKey),
	}).Info("calling Agent API for user profile")

	// Make HTTP request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		log.WithError(err).Error("failed to call Agent API")
		return fmt.Errorf("failed to call Agent API: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.WithError(err).Error("failed to read Agent API response")
		return fmt.Errorf("failed to read response: %w", err)
	}

	// Log response details
	log.WithFields(map[string]interface{}{
		"status_code":   resp.StatusCode,
		"response_body": string(bodyBytes),
	}).Info("Agent API profile response received")

	// Check if request was successful
	if resp.StatusCode != http.StatusOK {
		log.WithFields(map[string]interface{}{
			"status_code":   resp.StatusCode,
			"response_body": string(bodyBytes),
		}).Error("Agent API returned non-OK status")
		return fmt.Errorf("Agent API returned status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse response - check for error response first
	var errorResponse struct {
		Success bool `json:"success"`
		Error   struct {
			Code    string `json:"code"`
			Message string `json:"message"`
		} `json:"error"`
	}

	// Try to parse as error response first
	if err := json.Unmarshal(bodyBytes, &errorResponse); err == nil && !errorResponse.Success {
		log.WithFields(map[string]interface{}{
			"error_code":    errorResponse.Error.Code,
			"error_message": errorResponse.Error.Message,
		}).Error("Agent API returned error response")
		return fmt.Errorf("Agent API error: %s", errorResponse.Error.Message)
	}

	// Parse successful response
	var gameResponse struct {
		Success bool `json:"success"`
		Data    struct {
			Data struct {
				ID        string            `json:"id"`
				Phone     string            `json:"phone"`
				Username  string            `json:"username"`
				Providers map[string]string `json:"providers"`
				Upline    string            `json:"upline"`
				Wallet    float64           `json:"wallet"`
				Status    string            `json:"status"`
			} `json:"data"`
		} `json:"data"`
		Message string `json:"message"`
	}

	if err := json.Unmarshal(bodyBytes, &gameResponse); err != nil {
		log.WithError(err).Error("failed to parse Agent API response")
		return fmt.Errorf("failed to parse response: %w", err)
	}

	// Check if response indicates success
	if !gameResponse.Success {
		log.Error("Agent API response indicates failure")
		return fmt.Errorf("Agent API response indicates failure")
	}

	// Update member balance and providers with data from Agent API
	oldBalance := member.Balance
	member.Balance = gameResponse.Data.Data.Wallet
	member.GameProviders = gameResponse.Data.Data.Providers
	member.UpdatedAt = time.Now()

	if err := s.memberRepo.Update(ctx, member); err != nil {
		log.WithError(err).Error("failed to update member balance from game data")
		return fmt.Errorf("failed to update member balance: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"old_balance":   oldBalance,
		"new_balance":   gameResponse.Data.Data.Wallet,
		"game_username": *member.GameUsername,
		"providers":     gameResponse.Data.Data.Providers,
	}).Info("successfully synced member data from Agent API")

	return nil
}

// BackfillGameUsername backfills game_username for member by calling AG API to create user
func (s *memberService) BackfillGameUsername(ctx context.Context, memberID string) (string, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "BackfillGameUsername").WithField("member_id", memberID)

	// Get member by ID
	member, err := s.memberRepo.GetByID(ctx, memberID)
	if err != nil {
		log.WithError(err).Error("failed to get member by ID")
		return "", fmt.Errorf("failed to get member: %w", err)
	}

	// Check if member already has game_username
	if member.GameUsername != nil && *member.GameUsername != "" {
		log.WithField("existing_game_username", *member.GameUsername).Info("member already has game_username")
		return *member.GameUsername, nil
	}

	// Check if member has phone for AG API call
	if member.Phone == nil || *member.Phone == "" {
		log.Error("member has no phone number for AG API call")
		return "", fmt.Errorf("member has no phone number for AG API call")
	}

	// Call AG API to get member data (which should have username from game service)
	if s.agClient == nil {
		log.Error("AG API client not configured")
		return "", fmt.Errorf("AG API client not configured")
	}

	// Skip AG API and call Game Service directly to get proper game username
	log.Info("calling Game Service directly to create user and get game username")

	// Create registration request for AG API
	agRequest := map[string]interface{}{
		"username": "", // Empty username - will get game username from game service
		"email":    fmt.Sprintf("%<EMAIL>", *member.Phone),
		"firstName": func() string {
			if member.FirstName != nil {
				return *member.FirstName
			}
			return "Member"
		}(),
		"lastName": func() string {
			if member.LastName != nil {
				return *member.LastName
			}
			return "Member"
		}(),
		"phone":    *member.Phone,
		"password": "default_password",
	}

	requestBody, err := json.Marshal(agRequest)
	if err != nil {
		log.WithError(err).Error("failed to marshal AG API request")
		return "", fmt.Errorf("failed to marshal AG API request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", "", bytes.NewBuffer(requestBody))
	if err != nil {
		log.WithError(err).Error("failed to create HTTP request")
		return "", fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Add Basic Auth header
	authHeader := s.agClient.CreateAuthHeader()
	req.Header.Set("Authorization", authHeader)
	req.Header.Set("Content-Type", "application/json")

	// Log request details
	log.WithFields(map[string]interface{}{
		"url":    "",
		"phone":  *member.Phone,
		"method": "POST",
	}).Info("calling AG API to register member and get game username")

	// Make HTTP request to AG API
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		log.WithError(err).Error("failed to call AG API")
		return "", fmt.Errorf("failed to call AG API: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.WithError(err).Error("failed to read AG API response")
		return "", fmt.Errorf("failed to read AG API response: %w", err)
	}

	// Log response details
	log.WithFields(map[string]interface{}{
		"status_code":   resp.StatusCode,
		"response_body": string(bodyBytes),
	}).Info("AG API registration response received")

	// Check if request was successful (201 for creation)
	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK {
		log.WithFields(map[string]interface{}{
			"status_code":   resp.StatusCode,
			"response_body": string(bodyBytes),
		}).Error("AG API returned non-success status")
		return "", fmt.Errorf("AG API returned status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse response
	var agResponse struct {
		Success bool `json:"success"`
		Data    struct {
			Member struct {
				Username string `json:"username"`
			} `json:"member"`
		} `json:"data"`
	}

	if err := json.Unmarshal(bodyBytes, &agResponse); err != nil {
		log.WithError(err).WithField("response_body", string(bodyBytes)).Error("failed to parse AG API response")
		return "", fmt.Errorf("failed to parse AG API response: %w", err)
	}

	if !agResponse.Success {
		log.Error("AG API returned unsuccessful response")
		return "", fmt.Errorf("AG API returned unsuccessful response")
	}

	// Get username from AG API response
	existingUsername := agResponse.Data.Member.Username

	// Check if username is already a proper game username (starts with "bk")
	if existingUsername != "" && strings.HasPrefix(existingUsername, "bk") {
		log.WithField("existing_game_username", existingUsername).Info("member already has valid game username")

		// Update game_username in blacking-api database with existing valid username
		member.GameUsername = &existingUsername
		member.UpdatedAt = time.Now()

		if err := s.memberRepo.Update(ctx, member); err != nil {
			log.WithError(err).Error("failed to update member game_username")
			return "", fmt.Errorf("failed to update member game_username: %w", err)
		}

		log.WithField("game_username", existingUsername).Info("successfully updated game_username with existing valid username")
		return existingUsername, nil
	}

	// Username is empty or not a proper game username (like phone number)
	// Need to force call Game Service to create user and get proper game username
	log.WithField("invalid_username", existingUsername).Info("username is not valid game username, forcing Game Service call")

	// Force call Game Service directly to get proper game username
	gameUsername, err := s.forceCreateGameUser(ctx, member, existingUsername)
	if err != nil {
		log.WithError(err).Error("failed to force create game user")
		return "", fmt.Errorf("failed to force create game user: %w", err)
	}

	// Also update the username in AG API with the new game username
	if err := s.updateAGAPIUsername(ctx, member, gameUsername); err != nil {
		log.WithError(err).Warn("failed to update AG API username, but continuing with backfill")
	}

	// Update game_username in blacking-api database
	member.GameUsername = &gameUsername
	member.UpdatedAt = time.Now()

	if err := s.memberRepo.Update(ctx, member); err != nil {
		log.WithError(err).Error("failed to update member game_username")
		return "", fmt.Errorf("failed to update member game_username: %w", err)
	}

	log.WithField("game_username", gameUsername).Info("successfully backfilled game_username")
	return gameUsername, nil
}

// forceCreateGameUser calls Game Service directly to create user and get proper game username
func (s *memberService) forceCreateGameUser(ctx context.Context, member *member.Member, currentUsername string) (string, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "forceCreateGameUser").WithField("member_id", member.ID)

	log.WithFields(map[string]interface{}{
		"phone":            *member.Phone,
		"current_username": currentUsername,
	}).Info("calling Game Service directly to get proper game username")

	// Get Game API URL from config
	if s.config.Game.BaseURL == "" {
		log.Error("Game API URL not configured")
		return "", fmt.Errorf("Game API URL not configured")
	}

	// Get upline from config (agent code)
	upline := "bk10" // Default upline
	if s.config.Agent.SecretKey != "" {
		// Extract upline from agent secret or use default
		upline = "bk10"
	}

	gameAPIURL := fmt.Sprintf("%s/api/v1/transfer/user", strings.TrimSuffix(s.config.Game.BaseURL, "/"))

	// Create form data
	formData := url.Values{}
	formData.Set("phone", *member.Phone)
	formData.Set("upline", upline)

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", gameAPIURL, strings.NewReader(formData.Encode()))
	if err != nil {
		log.WithError(err).Error("failed to create Game API request")
		return "", fmt.Errorf("failed to create Game API request: %w", err)
	}

	// Set headers for form data
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// Log request details
	log.WithFields(map[string]interface{}{
		"url":    gameAPIURL,
		"phone":  *member.Phone,
		"upline": upline,
	}).Info("calling Game Service to create user")

	// Make HTTP request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		log.WithError(err).Error("failed to call Game API")
		return "", fmt.Errorf("failed to call Game API: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.WithError(err).Error("failed to read Game API response")
		return "", fmt.Errorf("failed to read Game API response: %w", err)
	}

	// Log response details
	log.WithFields(map[string]interface{}{
		"status_code":   resp.StatusCode,
		"response_body": string(bodyBytes),
	}).Info("Game API response received")

	// Check if request was successful
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		log.WithFields(map[string]interface{}{
			"status_code":   resp.StatusCode,
			"response_body": string(bodyBytes),
		}).Error("Game API returned non-success status")
		return "", fmt.Errorf("Game API returned status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse response
	var gameResponse struct {
		Data struct {
			Username string `json:"username"`
		} `json:"data"`
	}

	if err := json.Unmarshal(bodyBytes, &gameResponse); err != nil {
		log.WithError(err).WithField("response_body", string(bodyBytes)).Error("failed to parse Game API response")
		return "", fmt.Errorf("failed to parse Game API response: %w", err)
	}

	gameUsername := gameResponse.Data.Username
	if gameUsername == "" {
		log.Error("Game API returned empty username")
		return "", fmt.Errorf("Game API returned empty username")
	}

	log.WithField("game_username", gameUsername).Info("successfully created user in Game Service")
	return gameUsername, nil
}

// updateAGAPIUsername updates username in AG API with the new game username
func (s *memberService) updateAGAPIUsername(ctx context.Context, member *member.Member, gameUsername string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "updateAGAPIUsername").WithField("member_id", member.ID)

	if s.agClient == nil {
		log.Warn("AG API client not configured, skipping username update")
		return nil
	}

	// Update username in AG API using PATCH endpoint
	agAPIURL := fmt.Sprintf("%s/members/by-phone/%s", strings.TrimSuffix(s.agClient.GetBaseURL(), "/"), *member.Phone)

	updateData := map[string]interface{}{
		"username": gameUsername,
	}

	requestBody, err := json.Marshal(updateData)
	if err != nil {
		log.WithError(err).Error("failed to marshal AG API update request")
		return fmt.Errorf("failed to marshal AG API update request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "PATCH", agAPIURL, bytes.NewBuffer(requestBody))
	if err != nil {
		log.WithError(err).Error("failed to create AG API update request")
		return fmt.Errorf("failed to create AG API update request: %w", err)
	}

	// Add Basic Auth header
	authHeader := s.agClient.CreateAuthHeader()
	req.Header.Set("Authorization", authHeader)
	req.Header.Set("Content-Type", "application/json")

	// Log request details
	log.WithFields(map[string]interface{}{
		"url":           agAPIURL,
		"phone":         *member.Phone,
		"game_username": gameUsername,
	}).Info("updating AG API username with game username")

	// Make HTTP request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		log.WithError(err).Error("failed to call AG API")
		return fmt.Errorf("failed to call AG API: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.WithError(err).Error("failed to read AG API response")
		return fmt.Errorf("failed to read AG API response: %w", err)
	}

	// Log response details
	log.WithFields(map[string]interface{}{
		"status_code":   resp.StatusCode,
		"response_body": string(bodyBytes),
	}).Info("AG API username update response received")

	// Check if request was successful
	if resp.StatusCode != http.StatusOK {
		log.WithFields(map[string]interface{}{
			"status_code":   resp.StatusCode,
			"response_body": string(bodyBytes),
		}).Warn("AG API username update failed, but continuing")
		return fmt.Errorf("AG API returned status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	log.WithField("game_username", gameUsername).Info("successfully updated AG API username")
	return nil
}
