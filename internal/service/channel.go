package service

import (
	"context"

	"blacking-api/internal/domain/channel"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
)

// ChannelService defines the interface for channel service operations
type ChannelService interface {
	CreateChannel(ctx context.Context, req channel.CreateChannelRequest, createdBy string) (*channel.ChannelResponse, error)
	GetChannelByID(ctx context.Context, id int) (*channel.ChannelResponse, error)
	UpdateChannel(ctx context.Context, id int, req channel.UpdateChannelRequest) (*channel.ChannelResponse, error)
	DeleteChannel(ctx context.Context, id int) error
	ListChannels(ctx context.Context, limit, offset int, search string) ([]*channel.ChannelResponse, int64, error)
	ListActiveChannels(ctx context.Context) ([]*channel.ChannelResponse, error)
	ListChannelsByPlatform(ctx context.Context, platformID string) ([]*channel.ChannelResponse, error)
	GetChannelsForDropdown(ctx context.Context) ([]*channel.ChannelDropdownResponse, error)
}

type channelService struct {
	channelRepo interfaces.ChannelRepository
	logger      logger.Logger
}

// NewChannelService creates a new channel service
func NewChannelService(channelRepo interfaces.ChannelRepository, logger logger.Logger) ChannelService {
	return &channelService{
		channelRepo: channelRepo,
		logger:      logger,
	}
}

// CreateChannel creates a new channel
func (s *channelService) CreateChannel(ctx context.Context, req channel.CreateChannelRequest, createdBy string) (*channel.ChannelResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CreateChannel")

	// Check if channel name already exists
	existingChannel, err := s.channelRepo.GetByName(ctx, req.Name)
	if err == nil && existingChannel != nil {
		log.WithField("name", req.Name).Error("channel name already exists")
		return nil, errors.NewValidationError("channel name already exists")
	}

	// Create new channel
	newChannel, err := channel.NewChannel(req, createdBy)
	if err != nil {
		log.WithError(err).Error("failed to create channel domain object")
		return nil, err
	}

	// Save to repository
	if err := s.channelRepo.Create(ctx, newChannel); err != nil {
		log.WithError(err).Error("failed to save channel to repository")
		return nil, err
	}

	response := newChannel.ToResponse()
	log.WithField("channel_id", response.ID).WithField("name", req.Name).Info("channel created successfully")
	return &response, nil
}

// GetChannelByID retrieves channel by ID
func (s *channelService) GetChannelByID(ctx context.Context, id int) (*channel.ChannelResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetChannelByID")

	c, err := s.channelRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).WithField("channel_id", id).Error("failed to get channel by ID")
		return nil, err
	}

	response := c.ToResponse()
	return &response, nil
}

// UpdateChannel updates an existing channel
func (s *channelService) UpdateChannel(ctx context.Context, id int, req channel.UpdateChannelRequest) (*channel.ChannelResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateChannel")

	// Get existing channel
	c, err := s.channelRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).WithField("channel_id", id).Error("failed to get channel for update")
		return nil, err
	}

	// Check if new name conflicts with existing channel (excluding current channel)
	if c.Name != req.Name {
		existingChannel, err := s.channelRepo.GetByName(ctx, req.Name)
		if err == nil && existingChannel != nil && existingChannel.ID != id {
			log.WithField("name", req.Name).Error("channel name already exists")
			return nil, errors.NewValidationError("channel name already exists")
		}
	}

	// Update channel
	if err := c.Update(req); err != nil {
		log.WithError(err).Error("failed to update channel domain object")
		return nil, err
	}

	// Save to repository
	if err := s.channelRepo.Update(ctx, c); err != nil {
		log.WithError(err).Error("failed to save updated channel to repository")
		return nil, err
	}

	response := c.ToResponse()
	log.WithField("channel_id", response.ID).Info("channel updated successfully")
	return &response, nil
}

// DeleteChannel soft deletes a channel
func (s *channelService) DeleteChannel(ctx context.Context, id int) error {
	log := s.logger.WithContext(ctx).WithField("operation", "DeleteChannel")

	if err := s.channelRepo.Delete(ctx, id); err != nil {
		log.WithError(err).WithField("channel_id", id).Error("failed to delete channel")
		return err
	}

	log.WithField("channel_id", id).Info("channel deleted successfully")
	return nil
}

// ListChannels retrieves channels with pagination and search
func (s *channelService) ListChannels(ctx context.Context, limit, offset int, search string) ([]*channel.ChannelResponse, int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListChannels")

	// Get total count
	total, err := s.channelRepo.Count(ctx, search)
	if err != nil {
		log.WithError(err).Error("failed to count channels")
		return nil, 0, err
	}

	// Get channels
	channels, err := s.channelRepo.List(ctx, limit, offset, search)
	if err != nil {
		log.WithError(err).Error("failed to list channels")
		return nil, 0, err
	}

	// Convert to response format
	var responses []*channel.ChannelResponse
	for _, c := range channels {
		response := c.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("total", total).WithField("returned", len(responses)).Info("channels listed successfully")
	return responses, total, nil
}

// ListActiveChannels retrieves only active channels
func (s *channelService) ListActiveChannels(ctx context.Context) ([]*channel.ChannelResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListActiveChannels")

	channels, err := s.channelRepo.ListActive(ctx)
	if err != nil {
		log.WithError(err).Error("failed to list active channels")
		return nil, err
	}

	// Convert to response format
	var responses []*channel.ChannelResponse
	for _, c := range channels {
		response := c.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("count", len(responses)).Info("active channels listed successfully")
	return responses, nil
}

// ListChannelsByPlatform retrieves channels by platform ID
func (s *channelService) ListChannelsByPlatform(ctx context.Context, platformID string) ([]*channel.ChannelResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListChannelsByPlatform")

	channels, err := s.channelRepo.ListByPlatform(ctx, platformID)
	if err != nil {
		log.WithError(err).WithField("platform_id", platformID).Error("failed to list channels by platform")
		return nil, err
	}

	// Convert to response format
	var responses []*channel.ChannelResponse
	for _, c := range channels {
		response := c.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("platform_id", platformID).WithField("count", len(responses)).Info("channels by platform listed successfully")
	return responses, nil
}

// GetChannelsForDropdown retrieves active channels in dropdown format
func (s *channelService) GetChannelsForDropdown(ctx context.Context) ([]*channel.ChannelDropdownResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetChannelsForDropdown")

	channels, err := s.channelRepo.ListActive(ctx)
	if err != nil {
		log.WithError(err).Error("failed to list active channels for dropdown")
		return nil, err
	}

	// Convert to dropdown response format
	var responses []*channel.ChannelDropdownResponse
	for _, c := range channels {
		response := c.ToDropdownResponse()
		responses = append(responses, &response)
	}

	log.WithField("count", len(responses)).Info("channels for dropdown listed successfully")
	return responses, nil
}
