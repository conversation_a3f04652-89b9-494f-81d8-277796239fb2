package service

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"blacking-api/internal/domain/referral"
	"blacking-api/internal/domain/system_setting"
)

// SystemSettingGetter interface for graph processor (to avoid circular imports)
type SystemSettingGetter interface {
	GetSystemSetting(ctx context.Context, key string) (*system_setting.SystemSettingResponse, error)
}

// GraphProcessor processes winloss data into graph format
type GraphProcessor struct {
	systemSettingService SystemSettingGetter
}

// NewGraphProcessor creates a new graph processor
func NewGraphProcessor(systemSettingService SystemSettingGetter) *GraphProcessor {
	return &GraphProcessor{
		systemSettingService: systemSettingService,
	}
}

// ProcessWinlossToGraph converts winloss data to graph format
func (gp *GraphProcessor) ProcessWinlossToGraph(
	username string,
	winlossData []referral.WinlossDataItem,
	lastCommissionCalDate *time.Time,
) (*referral.GraphData, error) {

	// Filter data after last commission calculation date
	filteredData := gp.filterDataAfterDate(winlossData, lastCommissionCalDate)

	// Group data by date
	dailyData := gp.groupDataByDate(filteredData)

	// Convert to graph data points with category-specific commission
	dataPoints := gp.convertToGraphDataPointsWithCategoryCommission(dailyData)

	// Sort by date
	sort.Slice(dataPoints, func(i, j int) bool {
		return dataPoints[i].Date < dataPoints[j].Date
	})

	return &referral.GraphData{
		Title:    fmt.Sprintf("Commission Earnings for %s", username),
		Dataset1: dataPoints,
	}, nil
}

// getCommissionPercent retrieves commission percentage from system settings based on game category
func (gp *GraphProcessor) getCommissionPercent(gameCategory string) (float64, error) {
	var key string
	switch gameCategory {
	case system_setting.GameCategorySlot:
		key = system_setting.KeyReferralCommissionPercentSlot
	case system_setting.GameCategoryLiveCasino:
		key = system_setting.KeyReferralCommissionPercentLiveCasino
	case system_setting.GameCategorySport:
		key = system_setting.KeyReferralCommissionPercentSport
	default:
		// Default to Slot commission if category is unknown
		key = system_setting.KeyReferralCommissionPercentSlot
	}

	setting, err := gp.systemSettingService.GetSystemSetting(context.Background(), key)
	if err != nil {
		return 0, err
	}

	percent, err := strconv.ParseFloat(setting.Value, 64)
	if err != nil {
		return 0, fmt.Errorf("invalid commission percent format: %w", err)
	}

	return percent, nil
}

// filterDataAfterDate filters winloss data to only include records after the given date
func (gp *GraphProcessor) filterDataAfterDate(data []referral.WinlossDataItem, afterDate *time.Time) []referral.WinlossDataItem {
	if afterDate == nil {
		return data // Return all data if no filter date
	}

	filtered := make([]referral.WinlossDataItem, 0)
	for _, item := range data {
		if item.CreatedAt.After(*afterDate) {
			filtered = append(filtered, item)
		}
	}

	return filtered
}

// groupDataByDate groups winloss data by date (YYYY-MM-DD)
func (gp *GraphProcessor) groupDataByDate(data []referral.WinlossDataItem) map[string][]referral.WinlossDataItem {
	grouped := make(map[string][]referral.WinlossDataItem)

	for _, item := range data {
		dateKey := item.CreatedAt.Format("2006-01-02") // YYYY-MM-DD
		grouped[dateKey] = append(grouped[dateKey], item)
	}

	return grouped
}

// convertToGraphDataPoints converts grouped daily data to graph data points
func (gp *GraphProcessor) convertToGraphDataPoints(dailyData map[string][]referral.WinlossDataItem, commissionPercent float64) []referral.GraphDataPoint {
	dataPoints := make([]referral.GraphDataPoint, 0, len(dailyData))

	for date, dayData := range dailyData {
		// Calculate totals for the day
		var totalUplineWinloss float64
		var totalBetAmount float64
		betCount := len(dayData)

		for _, item := range dayData {
			totalUplineWinloss += item.UplineWinloss
			totalBetAmount += item.BetAmount
		}

		// Calculate commission earned (upline_winloss * commission_percent / 100)
		commissionEarned := totalUplineWinloss * (commissionPercent / 100.0)

		dataPoints = append(dataPoints, referral.GraphDataPoint{
			Date:             date,
			CommissionEarned: commissionEarned,
			BetCount:         betCount,
			TotalBetAmount:   totalBetAmount,
		})
	}

	return dataPoints
}

// convertToGraphDataPointsWithCategoryCommission converts grouped daily data to graph data points with category-specific commission
func (gp *GraphProcessor) convertToGraphDataPointsWithCategoryCommission(dailyData map[string][]referral.WinlossDataItem) []referral.GraphDataPoint {
	dataPoints := make([]referral.GraphDataPoint, 0, len(dailyData))

	for date, dayData := range dailyData {
		// Calculate totals for the day grouped by category
		var totalBetAmount float64
		var totalCommissionEarned float64
		betCount := len(dayData)

		for _, item := range dayData {
			// Get commission percentage for this game category
			commissionPercent, err := gp.getCommissionPercent(item.GameCategory)
			if err != nil {
				// Use default 1.0% if unable to get commission
				commissionPercent = 1.0
			}

			// Calculate commission for this specific item
			itemCommission := item.UplineWinloss * (commissionPercent / 100.0)
			totalCommissionEarned += itemCommission
			totalBetAmount += item.BetAmount
		}

		dataPoints = append(dataPoints, referral.GraphDataPoint{
			Date:             date,
			CommissionEarned: totalCommissionEarned,
			BetCount:         betCount,
			TotalBetAmount:   totalBetAmount,
		})
	}

	return dataPoints
}

// ProcessMultipleMembersGraph processes graph data for multiple members
func (gp *GraphProcessor) ProcessMultipleMembersGraph(
	members []referral.ReferralMember,
	winlossRepo referral.ExternalWinlossRepository,
	startDate, endDate *time.Time,
) error {

	// Default date range if not provided
	if startDate == nil {
		thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
		startDate = &thirtyDaysAgo
	}
	if endDate == nil {
		now := time.Now()
		endDate = &now
	}

	// Process each member
	for i := range members {
		member := &members[i]

		// Get winloss data for this member's upline
		// Note: In real implementation, we'd need to determine the upline from member data
		// For now, we'll use a placeholder
		upline := "bk0" // This should come from member data or be determined somehow

		winlossResp, err := winlossRepo.GetWinlossData(upline, *startDate, *endDate, 1, 1000) // Get all data
		if err != nil {
			// Log error but continue with other members
			continue
		}

		// Filter data for this specific member
		memberData := gp.filterDataByUsername(winlossResp.Data.Data, member.Username)

		// Process to graph - using nil for LastCommissionCalDate since field was removed
		graph, err := gp.ProcessWinlossToGraph(member.Username, memberData, nil)
		if err != nil {
			// Log error but continue
			continue
		}

		// Note: Graph field removed from ReferralMember struct in new API design
		_ = graph // Suppress unused variable warning
	}

	return nil
}

// filterDataByUsername filters winloss data for a specific username
func (gp *GraphProcessor) filterDataByUsername(data []referral.WinlossDataItem, username string) []referral.WinlossDataItem {
	filtered := make([]referral.WinlossDataItem, 0)
	for _, item := range data {
		if strings.EqualFold(item.Username, username) {
			filtered = append(filtered, item)
		}
	}
	return filtered
}

// ProcessGamePlaySummary creates a game play summary from winloss data
func (gp *GraphProcessor) ProcessGamePlaySummary(
	username string,
	winlossData []referral.WinlossDataItem,
	commissionPercent float64,
) *referral.GamePlaySummary {

	if len(winlossData) == 0 {
		return &referral.GamePlaySummary{
			GameCategories:   []referral.GameCategorySummary{},
			TopGames:         []referral.GameSummary{},
			PlayingFrequency: make(map[string]int),
		}
	}

	// Initialize summary
	summary := &referral.GamePlaySummary{
		PlayingFrequency: make(map[string]int),
	}

	// Group by game category
	categoryData := make(map[string][]referral.WinlossDataItem)
	gameData := make(map[string][]referral.WinlossDataItem)

	// Process each winloss item
	for _, item := range winlossData {
		// Overall totals
		summary.TotalBetAmount += item.BetAmount
		summary.TotalWinLoss += item.BetWinloss
		summary.TotalBetCount++

		// Group by category
		categoryData[item.GameCategory] = append(categoryData[item.GameCategory], item)

		// Group by game name
		gameKey := item.GameName + "|" + item.Provider + "|" + item.GameCategory
		gameData[gameKey] = append(gameData[gameKey], item)

		// Playing frequency by date
		dateKey := item.CreatedAt.Format("2006-01-02")
		summary.PlayingFrequency[dateKey]++
	}

	// Process category summaries
	for category, items := range categoryData {
		categorySummary := gp.processCategorySummary(category, items, commissionPercent)
		summary.GameCategories = append(summary.GameCategories, categorySummary)
	}

	// Process top games
	topGames := gp.processTopGames(gameData, 10) // Top 10 games
	summary.TopGames = topGames

	return summary
}

// processCategorySummary creates summary for a specific game category
func (gp *GraphProcessor) processCategorySummary(category string, items []referral.WinlossDataItem, commissionPercent float64) referral.GameCategorySummary {
	var betAmount, winLoss, uplineWinLoss float64
	betCount := len(items)
	winCount := 0

	for _, item := range items {
		betAmount += item.BetAmount
		winLoss += item.BetWinloss
		uplineWinLoss += item.UplineWinloss

		if item.BetWinloss > 0 {
			winCount++
		}
	}

	// Calculate win rate
	winRate := 0.0
	if betCount > 0 {
		winRate = float64(winCount) / float64(betCount) * 100
	}

	// Calculate commission earned
	commissionEarned := uplineWinLoss * (commissionPercent / 100.0)

	return referral.GameCategorySummary{
		Category:         category,
		BetAmount:        betAmount,
		WinLoss:          winLoss,
		BetCount:         betCount,
		WinRate:          winRate,
		CommissionEarned: commissionEarned,
	}
}

// processTopGames creates a list of top games by bet amount
func (gp *GraphProcessor) processTopGames(gameData map[string][]referral.WinlossDataItem, limit int) []referral.GameSummary {
	type gameTemp struct {
		key        string
		gameName   string
		provider   string
		category   string
		betAmount  float64
		winLoss    float64
		betCount   int
		lastPlayed time.Time
	}

	// Create temporary slice for sorting
	tempGames := make([]gameTemp, 0, len(gameData))

	for key, items := range gameData {
		// Parse key (gameName|provider|category)
		parts := strings.Split(key, "|")
		if len(parts) != 3 {
			continue
		}

		var betAmount, winLoss float64
		betCount := len(items)
		var lastPlayed time.Time

		for _, item := range items {
			betAmount += item.BetAmount
			winLoss += item.BetWinloss

			if item.CreatedAt.After(lastPlayed) {
				lastPlayed = item.CreatedAt
			}
		}

		tempGames = append(tempGames, gameTemp{
			key:        key,
			gameName:   parts[0],
			provider:   parts[1],
			category:   parts[2],
			betAmount:  betAmount,
			winLoss:    winLoss,
			betCount:   betCount,
			lastPlayed: lastPlayed,
		})
	}

	// Sort by bet amount (descending)
	sort.Slice(tempGames, func(i, j int) bool {
		return tempGames[i].betAmount > tempGames[j].betAmount
	})

	// Convert to result format
	result := make([]referral.GameSummary, 0, limit)
	maxItems := limit
	if len(tempGames) < limit {
		maxItems = len(tempGames)
	}

	for i := 0; i < maxItems; i++ {
		game := tempGames[i]
		result = append(result, referral.GameSummary{
			GameName:   game.gameName,
			Provider:   game.provider,
			Category:   game.category,
			BetAmount:  game.betAmount,
			WinLoss:    game.winLoss,
			BetCount:   game.betCount,
			LastPlayed: game.lastPlayed.Format("2006-01-02 15:04:05"),
		})
	}

	return result
}
