package service

import (
	"context"

	"blacking-api/internal/domain/refresh_token"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
)

type RefreshTokenService interface {
	CreateRefreshToken(ctx context.Context, userID int, userType refresh_token.UserType) (*refresh_token.RefreshTokenResponse, error)
	ValidateRefreshToken(ctx context.Context, token string) (*refresh_token.RefreshToken, error)
	InvalidateRefreshToken(ctx context.Context, token string) error
	InvalidateAllUserTokens(ctx context.Context, userID int, userType refresh_token.UserType) error
	CleanupExpiredTokens(ctx context.Context) error
}

type refreshTokenService struct {
	refreshTokenRepo interfaces.RefreshTokenRepository
	logger           logger.Logger
}

func NewRefreshTokenService(refreshTokenRepo interfaces.RefreshTokenRepository, logger logger.Logger) RefreshTokenService {
	return &refreshTokenService{
		refreshTokenRepo: refreshTokenRepo,
		logger:           logger,
	}
}

func (s *refreshTokenService) CreateRefreshToken(ctx context.Context, userID int, userType refresh_token.UserType) (*refresh_token.RefreshTokenResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CreateRefreshToken").WithField("user_id", userID).WithField("user_type", userType)

	var req refresh_token.CreateRefreshTokenRequest
	req.UserType = userType

	if userType == refresh_token.UserTypeAdmin {
		req.UserID = &userID
	} else {
		req.MemberID = &userID
	}

	// Create new refresh token
	rt, err := refresh_token.NewRefreshToken(req)
	if err != nil {
		log.WithError(err).Error("failed to create refresh token domain object")
		return nil, err
	}

	// Save to repository
	if err := s.refreshTokenRepo.Create(ctx, rt); err != nil {
		log.WithError(err).Error("failed to save refresh token to repository")
		return nil, err
	}

	response := rt.ToResponse()
	log.WithField("token_id", rt.ID).Info("refresh token created successfully")
	return &response, nil
}

func (s *refreshTokenService) ValidateRefreshToken(ctx context.Context, token string) (*refresh_token.RefreshToken, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ValidateRefreshToken")

	if token == "" {
		return nil, errors.NewValidationError("refresh token is required")
	}

	// Get refresh token from repository
	rt, err := s.refreshTokenRepo.GetByToken(ctx, token)
	if err != nil {
		log.WithError(err).Error("refresh token not found")
		return nil, errors.NewUnauthorizedError("invalid refresh token")
	}

	// Check if token is expired
	if rt.IsExpired() {
		log.WithField("token_id", rt.ID).WithField("expires_at", rt.ExpiresAt).Warn("refresh token expired")
		// Clean up expired token
		s.refreshTokenRepo.DeleteByToken(ctx, token)
		return nil, errors.NewUnauthorizedError("refresh token expired")
	}

	log.WithField("token_id", rt.ID).WithField("user_type", rt.UserType).Info("refresh token validated successfully")
	return rt, nil
}

func (s *refreshTokenService) InvalidateRefreshToken(ctx context.Context, token string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "InvalidateRefreshToken")

	if token == "" {
		return errors.NewValidationError("refresh token is required")
	}

	err := s.refreshTokenRepo.DeleteByToken(ctx, token)
	if err != nil {
		log.WithError(err).Error("failed to invalidate refresh token")
		return err
	}

	log.Info("refresh token invalidated successfully")
	return nil
}

func (s *refreshTokenService) InvalidateAllUserTokens(ctx context.Context, userID int, userType refresh_token.UserType) error {
	log := s.logger.WithContext(ctx).WithField("operation", "InvalidateAllUserTokens").WithField("user_id", userID).WithField("user_type", userType)

	err := s.refreshTokenRepo.DeleteByUserID(ctx, userID, userType)
	if err != nil {
		log.WithError(err).Error("failed to invalidate all user tokens")
		return err
	}

	log.Info("all user refresh tokens invalidated successfully")
	return nil
}

func (s *refreshTokenService) CleanupExpiredTokens(ctx context.Context) error {
	log := s.logger.WithContext(ctx).WithField("operation", "CleanupExpiredTokens")

	err := s.refreshTokenRepo.DeleteExpired(ctx)
	if err != nil {
		log.WithError(err).Error("failed to cleanup expired tokens")
		return err
	}

	log.Info("expired refresh tokens cleaned up successfully")
	return nil
}
