package service

import (
	"blacking-api/internal/domain/payment_gateway_account"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/jaijaipay"
	"blacking-api/pkg/logger"
	"context"
	"fmt"
	"time"
)

type PaymentGatewayService struct {
	paymentGatewayRepo  interfaces.PaymentGatewayAccountRepository
	jaiJaiPayAPILogRepo interfaces.JaiJaiPayAPILogRepository
	memberRepo          interfaces.MemberRepository
	logger              logger.Logger
}

func NewPaymentGatewayService(
	paymentGatewayRepo interfaces.PaymentGatewayAccountRepository,
	jaiJaiPayAPILogRepo interfaces.JaiJaiPayAPILogRepository,
	memberRepo interfaces.MemberRepository,
	logger logger.Logger,
) *PaymentGatewayService {
	return &PaymentGatewayService{
		paymentGatewayRepo:  paymentGatewayRepo,
		jaiJaiPayAPILogRepo: jaiJaiPayAPILogRepo,
		memberRepo:          memberRepo,
		logger:              logger,
	}
}

// GetActiveProvider gets the active payment gateway configuration by provider name
func (s *PaymentGatewayService) GetActiveProvider(ctx context.Context, provider string) (*payment_gateway_account.PaymentGatewayAccount, error) {
	// Get active payment gateway account for the provider
	gateway, err := s.paymentGatewayRepo.GetByProviderAndActive(ctx, provider, true)
	if err != nil {
		s.logger.WithError(err).Errorf("failed to get active payment gateway for provider: %s", provider)
		return nil, errors.NewDatabaseError("failed to get payment gateway configuration")
	}

	if gateway == nil {
		return nil, errors.NewNotFoundError(fmt.Sprintf("no active payment gateway found for provider: %s", provider))
	}

	return gateway, nil
}

// CreateJaiJaiPayClient creates a JaiJaiPay client from payment gateway configuration
func (s *PaymentGatewayService) CreateJaiJaiPayClient(ctx context.Context) (*jaijaipay.Client, error) {
	gateway, err := s.GetActiveProvider(ctx, "jaijaipay")
	if err != nil {
		return nil, err
	}

	// Convert to JaiJaiPay config
	config := gateway.ToJaiJaiPayConfig()

	// Create JaiJaiPay client with database configuration
	clientConfig := &jaijaipay.Config{
		BaseURL:          config.BaseURL,
		APIKey:           config.APIKey,
		SecretKey:        config.SecretKey,
		Timeout:          config.Timeout,
		MaxRetries:       config.MaxRetries,
		RetryDelay:       config.RetryDelay,
		EnableRequestLog: config.EnableRequestLog,
		LogResponseBody:  config.LogResponseBody,
		EnableDebug:      config.EnableDebug,
	}

	client, err := jaijaipay.NewClient(*clientConfig, s.logger, s.jaiJaiPayAPILogRepo)
	if err != nil {
		s.logger.WithError(err).Error("failed to create JaiJaiPay client")
		return nil, errors.NewInternalError("failed to create payment gateway client")
	}

	s.logger.WithFields(map[string]interface{}{
		"provider": "jaijaipay",
		"base_url": config.BaseURL,
		"timeout":  config.Timeout,
		"retries":  config.MaxRetries,
		"debug":    config.EnableDebug,
	}).Info("JaiJaiPay client created from database configuration")

	return client, nil
}

// GetProviderList gets all payment gateway providers
func (s *PaymentGatewayService) GetProviderList(ctx context.Context) ([]*payment_gateway_account.PaymentGatewayAccount, error) {
	gateways, err := s.paymentGatewayRepo.GetAll(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get payment gateway providers")
		return nil, errors.NewDatabaseError("failed to get payment gateway providers")
	}

	return gateways, nil
}

// GetActiveProviders gets all active payment gateway providers
func (s *PaymentGatewayService) GetActiveProviders(ctx context.Context) ([]*payment_gateway_account.PaymentGatewayAccount, error) {
	gateways, err := s.paymentGatewayRepo.GetByActive(ctx, true)
	if err != nil {
		s.logger.WithError(err).Error("failed to get active payment gateway providers")
		return nil, errors.NewDatabaseError("failed to get active payment gateway providers")
	}

	return gateways, nil
}

// UpdateProviderStatus updates the status of a payment gateway provider
func (s *PaymentGatewayService) UpdateProviderStatus(ctx context.Context, id int64, active bool) error {
	gateway, err := s.paymentGatewayRepo.GetByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Errorf("failed to get payment gateway with ID: %d", id)
		return errors.NewDatabaseError("failed to get payment gateway")
	}

	if gateway == nil {
		return errors.NewNotFoundError("payment gateway not found")
	}

	gateway.Active = active
	gateway.Inactive = !active
	gateway.UpdatedAt = time.Now()

	if err := s.paymentGatewayRepo.UpdateEntity(ctx, gateway); err != nil {
		s.logger.WithError(err).Errorf("failed to update payment gateway status for ID: %d", id)
		return errors.NewDatabaseError("failed to update payment gateway status")
	}

	s.logger.WithFields(map[string]interface{}{
		"id":       id,
		"provider": gateway.Provider,
		"active":   active,
	}).Info("payment gateway status updated")

	return nil
}

// ValidateProviderConfiguration validates payment gateway configuration
func (s *PaymentGatewayService) ValidateProviderConfiguration(gateway *payment_gateway_account.PaymentGatewayAccount) error {
	if gateway.Provider == "" {
		return errors.NewValidationError("provider is required")
	}

	switch gateway.Provider {
	case "jaijaipay":
		if gateway.APIKey == "" {
			return errors.NewValidationError("API key is required for JaiJaiPay")
		}
		if gateway.SecretKey == "" {
			return errors.NewValidationError("secret key is required for JaiJaiPay")
		}
		if gateway.BaseURL == "" {
			return errors.NewValidationError("base URL is required for JaiJaiPay")
		}
		if gateway.TimeoutSeconds < 5 || gateway.TimeoutSeconds > 300 {
			return errors.NewValidationError("timeout must be between 5 and 300 seconds")
		}
		if gateway.MaxRetries < 0 || gateway.MaxRetries > 10 {
			return errors.NewValidationError("max retries must be between 0 and 10")
		}
	default:
		return errors.NewValidationError(fmt.Sprintf("unsupported provider: %s", gateway.Provider))
	}

	return nil
}

// CreateProvider creates a new payment gateway provider
func (s *PaymentGatewayService) CreateProvider(ctx context.Context, req *payment_gateway_account.PaymentGatewayAccountRequest) (*payment_gateway_account.PaymentGatewayAccount, error) {
	// Check if provider with same code already exists
	existing, err := s.paymentGatewayRepo.GetByCode(ctx, req.Code)
	if err != nil && !errors.IsNotFoundError(err) {
		s.logger.WithError(err).Error("failed to check existing payment gateway")
		return nil, errors.NewDatabaseError("failed to check existing payment gateway")
	}

	if existing != nil {
		return nil, errors.NewValidationError("payment gateway with this code already exists")
	}

	// Create new payment gateway
	gateway := &payment_gateway_account.PaymentGatewayAccount{
		AccountName:                        req.AccountName,
		Code:                               req.Code,
		Provider:                           req.Provider,
		MerchantCode:                       req.MerchantCode,
		SecretKey:                          req.SecretKey,
		SecretKeyTwo:                       req.SecretKeyTwo,
		APIKey:                             req.APIKey,
		BaseURL:                            req.BaseURL,
		FirstUsername:                      req.FirstUsername,
		SecondUsername:                     req.SecondUsername,
		FirstPassword:                      req.FirstPassword,
		SecondPassword:                     req.SecondPassword,
		MinimumWithdraw:                    req.MinimumWithdraw,
		MaximumWithdraw:                    req.MaximumWithdraw,
		WithdrawSplit:                      req.WithdrawSplit,
		MaximumWithdrawPerTransaction:      req.MaximumWithdrawPerTransaction,
		MaximumSplitWithdrawPerTransaction: req.MaximumSplitWithdrawPerTransaction,
		TimeoutSeconds:                     req.TimeoutSeconds,
		MaxRetries:                         req.MaxRetries,
		RetryDelaySeconds:                  req.RetryDelaySeconds,
		EnableRequestLog:                   req.EnableRequestLog,
		LogResponseBody:                    req.LogResponseBody,
		EnableDebug:                        req.EnableDebug,
		IsDeposit:                          req.IsDeposit,
		IsWithdraw:                         req.IsWithdraw,
		IsTransfer:                         req.IsTransfer,
		Active:                             false, // Default inactive
		Inactive:                           true,
		CreatedAt:                          time.Now(),
		UpdatedAt:                          time.Now(),
	}

	// Validate configuration
	if err := s.ValidateProviderConfiguration(gateway); err != nil {
		return nil, err
	}

	// Create in database
	if err := s.paymentGatewayRepo.CreateEntity(ctx, gateway); err != nil {
		s.logger.WithError(err).Error("failed to create payment gateway")
		return nil, errors.NewDatabaseError("failed to create payment gateway")
	}

	s.logger.WithFields(map[string]interface{}{
		"id":       gateway.ID,
		"provider": gateway.Provider,
		"code":     gateway.Code,
	}).Info("payment gateway provider created")

	return gateway, nil
}
