package service

import (
	"context"
	"encoding/json"
	"os"
	"path/filepath"
	"strconv"

	"blacking-api/internal/domain/dashboard"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
)

type DashboardService struct {
	logger   logger.Logger
	dataPath string
}

func NewDashboardService(logger logger.Logger) *DashboardService {
	return &DashboardService{
		logger:   logger,
		dataPath: "test/testdata/dashboard",
	}
}

// GetDashboardData โหลดและ join ข้อมูลทั้งหมดสำหรับ dashboard
func (s *DashboardService) GetDashboardData(ctx context.Context) (*dashboard.Dashboard, error) {
	// โหลดข้อมูล lookup tables
	gameTypes, err := s.loadGameTypes()
	if err != nil {
		s.logger.WithError(err).Error("failed to load game types")
		return nil, errors.NewInternalError("failed to load game types")
	}

	providers, err := s.loadProviders()
	if err != nil {
		s.logger.WithError(err).Error("failed to load providers")
		return nil, errors.NewInternalError("failed to load providers")
	}

	// โหลดข้อมูลหลัก
	daily, err := s.loadDailyData()
	if err != nil {
		s.logger.WithError(err).Error("failed to load daily data")
		return nil, errors.NewInternalError("failed to load daily data")
	}

	transactions, err := s.loadTransactions(gameTypes, providers)
	if err != nil {
		s.logger.WithError(err).Error("failed to load transactions")
		return nil, errors.NewInternalError("failed to load transactions")
	}

	activeBets, err := s.loadActiveBets(gameTypes, providers)
	if err != nil {
		s.logger.WithError(err).Error("failed to load active bets")
		return nil, errors.NewInternalError("failed to load active bets")
	}

	partnerGroups, err := s.loadPartnerGroups()
	if err != nil {
		s.logger.WithError(err).Error("failed to load partner groups")
		return nil, errors.NewInternalError("failed to load partner groups")
	}

	turnovers, err := s.loadTurnovers(gameTypes, providers)
	if err != nil {
		s.logger.WithError(err).Error("failed to load turnovers")
		return nil, errors.NewInternalError("failed to load turnovers")
	}

	netTurns, err := s.loadNetTurns(gameTypes, providers)
	if err != nil {
		s.logger.WithError(err).Error("failed to load net turns")
		return nil, errors.NewInternalError("failed to load net turns")
	}

	gameTypesData, err := s.loadGameTypesData(gameTypes, providers)
	if err != nil {
		s.logger.WithError(err).Error("failed to load game types data")
		return nil, errors.NewInternalError("failed to load game types data")
	}

	commissionAffiliates, err := s.loadCommissionAffiliates(gameTypes, providers)
	if err != nil {
		s.logger.WithError(err).Error("failed to load commission affiliates")
		return nil, errors.NewInternalError("failed to load commission affiliates")
	}

	return &dashboard.Dashboard{
		Daily:                daily,
		Transactions:         transactions,
		ActiveBets:           activeBets,
		PartnerGroups:        partnerGroups,
		Turnovers:            turnovers,
		NetTurns:             netTurns,
		GameTypes:            gameTypesData,
		CommissionAffiliates: commissionAffiliates,
	}, nil
}

// loadGameTypes โหลดข้อมูล game types สำหรับ join
func (s *DashboardService) loadGameTypes() (map[int]*dashboard.GameType, error) {
	filePath := filepath.Join(s.dataPath, "game-type.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	var response struct {
		Code    int                  `json:"code"`
		Success bool                 `json:"success"`
		Data    []dashboard.GameType `json:"data"`
	}

	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}

	gameTypesMap := make(map[int]*dashboard.GameType)
	for i := range response.Data {
		gameType := &response.Data[i]
		gameTypesMap[gameType.ID] = gameType
	}

	return gameTypesMap, nil
}

// loadProviders โหลดข้อมูล providers สำหรับ join
func (s *DashboardService) loadProviders() (map[int]*dashboard.Provider, error) {
	filePath := filepath.Join(s.dataPath, "provider.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	var response struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			Providers []dashboard.Provider `json:"providers"`
		} `json:"data"`
	}

	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}

	providersMap := make(map[int]*dashboard.Provider)
	for i := range response.Data.Providers {
		provider := &response.Data.Providers[i]
		providersMap[provider.ID] = provider
	}

	return providersMap, nil
}

// loadDailyData โหลดข้อมูลสรุปรายวัน
func (s *DashboardService) loadDailyData() ([]dashboard.DailyData, error) {
	filePath := filepath.Join(s.dataPath, "1 [634] daily.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	var response struct {
		Code    int                   `json:"code"`
		Success bool                  `json:"success"`
		Data    []dashboard.DailyData `json:"data"`
	}

	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}

	return response.Data, nil
}

// loadTransactions โหลดข้อมูลธุรกรรม
func (s *DashboardService) loadTransactions(gameTypes map[int]*dashboard.GameType, providers map[int]*dashboard.Provider) (dashboard.Transaction, error) {
	filePath := filepath.Join(s.dataPath, "2 [632] transactions.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return dashboard.Transaction{}, err
	}

	var response struct {
		Code    int                   `json:"code"`
		Success bool                  `json:"success"`
		Data    dashboard.Transaction `json:"data"`
	}

	if err := json.Unmarshal(data, &response); err != nil {
		return dashboard.Transaction{}, err
	}

	return response.Data, nil
}

// loadActiveBets โหลดข้อมูลการเดิมพันที่ยังใช้งาน
func (s *DashboardService) loadActiveBets(gameTypes map[int]*dashboard.GameType, providers map[int]*dashboard.Provider) (dashboard.ActiveBet, error) {
	filePath := filepath.Join(s.dataPath, "3 [634] activeBets.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return dashboard.ActiveBet{}, err
	}

	var response struct {
		Code    int                 `json:"code"`
		Success bool                `json:"success"`
		Data    dashboard.ActiveBet `json:"data"`
	}

	if err := json.Unmarshal(data, &response); err != nil {
		return dashboard.ActiveBet{}, err
	}

	return response.Data, nil
}

// loadPartnerGroups โหลดข้อมูลกลุ่มพาร์ทเนอร์
func (s *DashboardService) loadPartnerGroups() ([]dashboard.PartnerGroup, error) {
	filePath := filepath.Join(s.dataPath, "4 [633] partnerGroups.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	var response struct {
		Code    int                      `json:"code"`
		Success bool                     `json:"success"`
		Data    []dashboard.PartnerGroup `json:"data"`
	}

	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}

	return response.Data, nil
}

// loadTurnovers โหลดข้อมูลยอดหมุนเวียน และ join กับ provider names
func (s *DashboardService) loadTurnovers(gameTypes map[int]*dashboard.GameType, providers map[int]*dashboard.Provider) (dashboard.Turnover, error) {
	filePath := filepath.Join(s.dataPath, "5 [635] turnovers.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return dashboard.Turnover{}, err
	}

	var rawResponse struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			Turnover   []float64 `json:"turnover"`
			Percent    []float64 `json:"percent"`
			Title      []*string `json:"title"`
			Total      float64   `json:"total"`
			ProviderID []int     `json:"provider_id"`
		} `json:"data"`
	}

	if err := json.Unmarshal(data, &rawResponse); err != nil {
		return dashboard.Turnover{}, err
	}

	// Join provider names ตาม provider_id
	providerNames := make([]string, len(rawResponse.Data.ProviderID))
	titles := make([]string, len(rawResponse.Data.ProviderID))

	for i, providerID := range rawResponse.Data.ProviderID {
		if provider, exists := providers[providerID]; exists {
			providerNames[i] = provider.Name
			titles[i] = provider.Name
		} else {
			providerNames[i] = "Unknown Provider"
			titles[i] = "Unknown Provider"
		}
	}

	return dashboard.Turnover{
		Turnover:     rawResponse.Data.Turnover,
		Percent:      rawResponse.Data.Percent,
		Title:        titles,
		Total:        rawResponse.Data.Total,
		ProviderID:   rawResponse.Data.ProviderID,
		ProviderName: providerNames,
	}, nil
}

// loadNetTurns โหลดข้อมูลยอดสุทธิ และ join กับ provider names
func (s *DashboardService) loadNetTurns(gameTypes map[int]*dashboard.GameType, providers map[int]*dashboard.Provider) (dashboard.NetTurn, error) {
	filePath := filepath.Join(s.dataPath, "5.1 netturn.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return dashboard.NetTurn{}, err
	}

	var rawResponse struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			NetTurn    []float64 `json:"netturn"`
			Percent    []float64 `json:"percent"`
			Title      []*string `json:"title"`
			Total      float64   `json:"total"`
			ProviderID []int     `json:"provider_id"`
		} `json:"data"`
	}

	if err := json.Unmarshal(data, &rawResponse); err != nil {
		return dashboard.NetTurn{}, err
	}

	// Join provider names ตาม provider_id
	providerNames := make([]string, len(rawResponse.Data.ProviderID))
	titles := make([]string, len(rawResponse.Data.ProviderID))

	for i, providerID := range rawResponse.Data.ProviderID {
		if provider, exists := providers[providerID]; exists {
			providerNames[i] = provider.Name
			titles[i] = provider.Name
		} else {
			providerNames[i] = "Unknown Provider"
			titles[i] = "Unknown Provider"
		}
	}

	return dashboard.NetTurn{
		NetTurn:      rawResponse.Data.NetTurn,
		Percent:      rawResponse.Data.Percent,
		Title:        titles,
		Total:        rawResponse.Data.Total,
		ProviderID:   rawResponse.Data.ProviderID,
		ProviderName: providerNames,
	}, nil
}

// loadGameTypesData โหลดและ join ข้อมูลประเภทเกม พร้อม group by game_type_slug
func (s *DashboardService) loadGameTypesData(gameTypes map[int]*dashboard.GameType, providers map[int]*dashboard.Provider) (dashboard.GameTypeData, error) {
	filePath := filepath.Join(s.dataPath, "6 [636] gameTypes.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return dashboard.GameTypeData{}, err
	}

	var rawResponse struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			Data          []dashboard.GameTypeItem `json:"data"`
			WinLostTotal  float64                  `json:"winLost_total"`
			TurnoverTotal float64                  `json:"turnover_total"`
		} `json:"data"`
	}

	if err := json.Unmarshal(data, &rawResponse); err != nil {
		return dashboard.GameTypeData{}, err
	}

	// Join กับ game types และ providers
	for i := range rawResponse.Data.Data {
		if gameType, exists := gameTypes[rawResponse.Data.Data[i].GameTypeID]; exists {
			rawResponse.Data.Data[i].GameType = gameType
		}

		if provider, exists := providers[rawResponse.Data.Data[i].ProviderID]; exists {
			rawResponse.Data.Data[i].Provider = provider
		}
	}

	// Group by game_type_slug
	groupedBySlug := make(map[string]dashboard.GameTypeGroup)

	for _, item := range rawResponse.Data.Data {
		if item.GameType == nil {
			continue // Skip items without game type
		}

		slug := item.GameType.Slug

		// Convert string values to float for calculation
		winLose, _ := strconv.ParseFloat(item.WinLose, 64)
		turnover, _ := strconv.ParseFloat(item.Turnover, 64)
		netTurn, _ := strconv.ParseFloat(item.NetTurn, 64)

		if group, exists := groupedBySlug[slug]; exists {
			// Update existing group
			group.TotalWinLose += winLose
			group.TotalTurnover += turnover
			group.TotalNetTurn += netTurn
			group.Items = append(group.Items, item)
			groupedBySlug[slug] = group
		} else {
			// Create new group
			groupedBySlug[slug] = dashboard.GameTypeGroup{
				Slug:          slug,
				Name:          item.GameType.Name,
				TotalWinLose:  winLose,
				TotalTurnover: turnover,
				TotalNetTurn:  netTurn,
				Items:         []dashboard.GameTypeItem{item},
			}
		}
	}

	return dashboard.GameTypeData{
		GroupedBySlug: groupedBySlug,
		WinLostTotal:  rawResponse.Data.WinLostTotal,
		TurnoverTotal: rawResponse.Data.TurnoverTotal,
	}, nil
}

// loadCommissionAffiliates โหลดและ join ข้อมูลค่าคอมมิชชั่นแอฟฟิเลียท พร้อม group by game_type_slug
func (s *DashboardService) loadCommissionAffiliates(gameTypes map[int]*dashboard.GameType, providers map[int]*dashboard.Provider) (dashboard.CommissionAffiliate, error) {
	filePath := filepath.Join(s.dataPath, "7 [637] commissionAffiliates.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return dashboard.CommissionAffiliate{}, err
	}

	var rawResponse struct {
		Code    int  `json:"code"`
		Success bool `json:"success"`
		Data    struct {
			Data                 []dashboard.CommissionAffiliateItem `json:"data"`
			CommissionTotal      float64                             `json:"commission_total"`
			AffiliateLevel1Total float64                             `json:"affiliate_level_1_total"`
			AffiliateLevel2Total float64                             `json:"affiliate_level_2_total"`
		} `json:"data"`
	}

	if err := json.Unmarshal(data, &rawResponse); err != nil {
		return dashboard.CommissionAffiliate{}, err
	}

	// Join กับ game types และ providers
	for i := range rawResponse.Data.Data {
		if gameType, exists := gameTypes[rawResponse.Data.Data[i].GameTypeID]; exists {
			rawResponse.Data.Data[i].GameType = gameType
		}

		if provider, exists := providers[rawResponse.Data.Data[i].ProviderID]; exists {
			rawResponse.Data.Data[i].Provider = provider
		}
	}

	// Group by game_type_slug
	groupedBySlug := make(map[string]dashboard.CommissionAffiliateGroup)

	for _, item := range rawResponse.Data.Data {
		if item.GameType == nil {
			continue // Skip items without game type
		}

		slug := item.GameType.Slug

		// Convert string values to float for calculation
		commission, _ := strconv.ParseFloat(item.Commission, 64)
		affiliateLevel1, _ := strconv.ParseFloat(item.AffiliateLevel1, 64)
		affiliateLevel2, _ := strconv.ParseFloat(item.AffiliateLevel2, 64)

		if group, exists := groupedBySlug[slug]; exists {
			// Update existing group
			group.TotalCommission += commission
			group.TotalAffiliateLevel1 += affiliateLevel1
			group.TotalAffiliateLevel2 += affiliateLevel2
			group.Items = append(group.Items, item)
			groupedBySlug[slug] = group
		} else {
			// Create new group
			groupedBySlug[slug] = dashboard.CommissionAffiliateGroup{
				Slug:                 slug,
				Name:                 item.GameType.Name,
				TotalCommission:      commission,
				TotalAffiliateLevel1: affiliateLevel1,
				TotalAffiliateLevel2: affiliateLevel2,
				Items:                []dashboard.CommissionAffiliateItem{item},
			}
		}
	}

	return dashboard.CommissionAffiliate{
		GroupedBySlug:        groupedBySlug,
		CommissionTotal:      rawResponse.Data.CommissionTotal,
		AffiliateLevel1Total: rawResponse.Data.AffiliateLevel1Total,
		AffiliateLevel2Total: rawResponse.Data.AffiliateLevel2Total,
	}, nil
}

// GetBotSuccessData โหลดข้อมูลความสำเร็จของ bot สำหรับ API แยก
func (s *DashboardService) GetBotSuccessData(ctx context.Context, roundDate string) (*dashboard.BotSuccess, error) {
	botSuccess, err := s.loadBotSuccess()
	if err != nil {
		s.logger.WithError(err).Error("failed to load bot success")
		return nil, errors.NewInternalError("failed to load bot success")
	}

	return &botSuccess, nil
}

// loadBotSuccess โหลดข้อมูลรวมทั้งหมดของ bot จากทุกไฟล์
func (s *DashboardService) loadBotSuccess() (dashboard.BotSuccess, error) {
	// โหลด botSuccess.json
	botSuccessData, err := s.loadBotSuccessData()
	if err != nil {
		return dashboard.BotSuccess{}, err
	}

	// โหลด adminApprove.json
	adminApprove, err := s.loadAdminApprove()
	if err != nil {
		return dashboard.BotSuccess{}, err
	}

	// โหลด botDepositHour.json
	botDepositHour, err := s.loadBotDepositHour()
	if err != nil {
		return dashboard.BotSuccess{}, err
	}

	// โหลด botDepositMinute.json
	botDepositMinute, err := s.loadBotDepositMinute()
	if err != nil {
		return dashboard.BotSuccess{}, err
	}

	return dashboard.BotSuccess{
		BotSuccessData:   botSuccessData,
		AdminApprove:     adminApprove,
		BotDepositHour:   botDepositHour,
		BotDepositMinute: botDepositMinute,
	}, nil
}

// loadBotSuccessData โหลดข้อมูลความสำเร็จของ bot
func (s *DashboardService) loadBotSuccessData() (dashboard.BotSuccessData, error) {
	filePath := filepath.Join("test/testdata/dashboard-bot", "botSuccess.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return dashboard.BotSuccessData{}, err
	}

	var response struct {
		Request struct {
			RoundDate string `json:"round_date"`
		} `json:"request"`
		Response struct {
			Code    int  `json:"code"`
			Success bool `json:"success"`
			Data    struct {
				Data  []dashboard.BotSuccessItem `json:"data"`
				Title []string                   `json:"title"`
			} `json:"data"`
		} `json:"response"`
	}

	if err := json.Unmarshal(data, &response); err != nil {
		return dashboard.BotSuccessData{}, err
	}

	return dashboard.BotSuccessData{
		Data:  response.Response.Data.Data,
		Title: response.Response.Data.Title,
	}, nil
}

// loadAdminApprove โหลดข้อมูลการอนุมัติของแอดมิน
func (s *DashboardService) loadAdminApprove() (dashboard.AdminApprove, error) {
	filePath := filepath.Join("test/testdata/dashboard-bot", "adminApprove.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return dashboard.AdminApprove{}, err
	}

	var response struct {
		Request struct {
			RoundDate string `json:"round_date"`
		} `json:"request"`
		Response struct {
			Code    int  `json:"code"`
			Success bool `json:"success"`
			Data    struct {
				Data  []interface{} `json:"data"`
				Title []string      `json:"title"`
			} `json:"data"`
		} `json:"response"`
	}

	if err := json.Unmarshal(data, &response); err != nil {
		return dashboard.AdminApprove{}, err
	}

	return dashboard.AdminApprove{
		Data:  response.Response.Data.Data,
		Title: response.Response.Data.Title,
	}, nil
}

// loadBotDepositHour โหลดข้อมูลการฝากเงินของ bot แบบรายชั่วโมง
func (s *DashboardService) loadBotDepositHour() (dashboard.BotDepositData, error) {
	filePath := filepath.Join("test/testdata/dashboard-bot", "botDepositHour.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return dashboard.BotDepositData{}, err
	}

	var response struct {
		Request struct {
			Period    string `json:"period"`
			RoundDate string `json:"round_date"`
		} `json:"request"`
		Response struct {
			Code    int  `json:"code"`
			Success bool `json:"success"`
			Data    struct {
				Title     []string  `json:"title"`
				RoundNo   []int     `json:"round_no"`
				Count     []int     `json:"count"`
				Amount    []float64 `json:"amount"`
				SumCount  int       `json:"sum_hour_count"`
				SumAmount float64   `json:"sum_hour_amount"`
			} `json:"data"`
		} `json:"response"`
	}

	if err := json.Unmarshal(data, &response); err != nil {
		return dashboard.BotDepositData{}, err
	}

	return dashboard.BotDepositData{
		Title:     response.Response.Data.Title,
		RoundNo:   response.Response.Data.RoundNo,
		Count:     response.Response.Data.Count,
		Amount:    response.Response.Data.Amount,
		SumCount:  response.Response.Data.SumCount,
		SumAmount: response.Response.Data.SumAmount,
	}, nil
}

// loadBotDepositMinute โหลดข้อมูลการฝากเงินของ bot แบบรายนาที
func (s *DashboardService) loadBotDepositMinute() (dashboard.BotDepositData, error) {
	filePath := filepath.Join("test/testdata/dashboard-bot", "botDepositMinute.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return dashboard.BotDepositData{}, err
	}

	var response struct {
		Request struct {
			Period    string `json:"period"`
			RoundDate string `json:"round_date"`
		} `json:"request"`
		Response struct {
			Code    int  `json:"code"`
			Success bool `json:"success"`
			Data    struct {
				Title     []string  `json:"title"`
				RoundNo   []int     `json:"round_no"`
				Count     []int     `json:"count"`
				Amount    []float64 `json:"amount"`
				SumCount  int       `json:"sum_minute_count"`
				SumAmount float64   `json:"sum_minute_amount"`
			} `json:"data"`
		} `json:"response"`
	}

	if err := json.Unmarshal(data, &response); err != nil {
		return dashboard.BotDepositData{}, err
	}

	return dashboard.BotDepositData{
		Title:     response.Response.Data.Title,
		RoundNo:   response.Response.Data.RoundNo,
		Count:     response.Response.Data.Count,
		Amount:    response.Response.Data.Amount,
		SumCount:  response.Response.Data.SumCount,
		SumAmount: response.Response.Data.SumAmount,
	}, nil
}
