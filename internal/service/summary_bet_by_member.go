package service

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"blacking-api/internal/domain/summary_bet_by_member"
	"blacking-api/pkg/logger"
)

type SummaryBetByMemberService struct {
	logger   logger.Logger
	dataPath string
}

func NewSummaryBetByMemberService(logger logger.Logger) *SummaryBetByMemberService {
	return &SummaryBetByMemberService{
		logger:   logger,
		dataPath: "test/testdata/summary-bet-by-member",
	}
}

// GetSummaryBetByMember โหลดข้อมูลรายงานสรุปเดิมพันแยกตาม Member
func (s *SummaryBetByMemberService) GetSummaryBetByMember(ctx context.Context) (*summary_bet_by_member.SummaryBetByMember, error) {
	// โหลดข้อมูลจาก JSON file
	data, pagination, err := s.loadSummaryBetByMemberDataFromFile()
	if err != nil {
		s.logger.WithError(err).Error("failed to load summary bet by member data from file")
		// fallback to mock data
		mockData := s.generateMockSummaryBetByMemberData()
		return &mockData, nil
	}

	return &summary_bet_by_member.SummaryBetByMember{
		Data:       data,
		Pagination: pagination,
	}, nil
}

// GetSummaryBetByMemberDetail โหลดข้อมูลรายละเอียดรายงานสรุปเดิมพันแยกตาม Member
func (s *SummaryBetByMemberService) GetSummaryBetByMemberDetail(ctx context.Context, memberID int) (*summary_bet_by_member.SummaryBetByMemberDetail, error) {
	// โหลดข้อมูลจาก JSON file
	data, pagination, err := s.loadSummaryBetByMemberDetailDataFromFile()
	if err != nil {
		s.logger.WithError(err).Error("failed to load summary bet by member detail data from file")
		// fallback to mock data
		mockDetail := s.generateMockSummaryBetByMemberDetailData(memberID)
		return &mockDetail, nil
	}

	return &summary_bet_by_member.SummaryBetByMemberDetail{
		Data:       data,
		Pagination: pagination,
	}, nil
}

// loadSummaryBetByMemberDataFromFile โหลดข้อมูลจาก JSON file
func (s *SummaryBetByMemberService) loadSummaryBetByMemberDataFromFile() ([]summary_bet_by_member.SummaryBetByMemberItem, summary_bet_by_member.Pagination, error) {
	filePath := filepath.Join(s.dataPath, "summary-bet-by-member.json")

	// อ่านไฟล์ JSON
	fileData, err := os.ReadFile(filePath)
	if err != nil {
		return nil, summary_bet_by_member.Pagination{}, fmt.Errorf("failed to read file %s: %w", filePath, err)
	}

	// Parse JSON structure ที่มี nested response
	var jsonData struct {
		Response struct {
			Data       []summary_bet_by_member.SummaryBetByMemberItem `json:"data"`
			Pagination summary_bet_by_member.Pagination               `json:"pagination"`
		} `json:"response"`
	}

	err = json.Unmarshal(fileData, &jsonData)
	if err != nil {
		return nil, summary_bet_by_member.Pagination{}, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	return jsonData.Response.Data, jsonData.Response.Pagination, nil
}

// loadSummaryBetByMemberDetailDataFromFile โหลดข้อมูล detail จาก JSON file
func (s *SummaryBetByMemberService) loadSummaryBetByMemberDetailDataFromFile() ([]summary_bet_by_member.SummaryBetByMemberDetailItem, summary_bet_by_member.Pagination, error) {
	filePath := filepath.Join(s.dataPath, "summary-bet-by-member-detail.json")

	// อ่านไฟล์ JSON
	fileData, err := os.ReadFile(filePath)
	if err != nil {
		return nil, summary_bet_by_member.Pagination{}, fmt.Errorf("failed to read file %s: %w", filePath, err)
	}

	// Parse JSON structure ที่มี nested response
	var jsonData struct {
		Response struct {
			Data       []summary_bet_by_member.SummaryBetByMemberDetailItem `json:"data"`
			Pagination summary_bet_by_member.Pagination                     `json:"pagination"`
		} `json:"response"`
	}

	err = json.Unmarshal(fileData, &jsonData)
	if err != nil {
		return nil, summary_bet_by_member.Pagination{}, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	return jsonData.Response.Data, jsonData.Response.Pagination, nil
}

// generateMockSummaryBetByMemberData สร้างข้อมูล mock สำหรับหน้าหลัก (fallback)
func (s *SummaryBetByMemberService) generateMockSummaryBetByMemberData() summary_bet_by_member.SummaryBetByMember {
	data := []summary_bet_by_member.SummaryBetByMemberItem{
		{
			ID: struct {
				UserID int    `json:"user_id"`
				Phone  string `json:"phone"`
			}{
				UserID: 15362,
				Phone:  "**********",
			},
			CommissionAmount:         0.42,
			CommissionNetTurnover:    141,
			CountUser:                0,
			NetTurnOver:              141,
			TurnOver:                 141,
			WinLoseNotIncludeJackpot: -49.4,
			Jackpot:                  0,
			WinLose:                  -49.4,
			Balance:                  "0.68000000",
			UserCode:                 "ZAB1E1PM15362",
		},
	}

	pagination := summary_bet_by_member.Pagination{
		PerPage:     "10",
		CurrentPage: "1",
		From:        0,
		To:          10,
		Total:       28,
		LastPage:    3,
		PrevPage:    nil,
		NextPage:    nil,
	}

	return summary_bet_by_member.SummaryBetByMember{
		Data:       data,
		Pagination: pagination,
	}
}

// generateMockSummaryBetByMemberDetailData สร้างข้อมูล mock สำหรับรายละเอียด (fallback)
func (s *SummaryBetByMemberService) generateMockSummaryBetByMemberDetailData(memberID int) summary_bet_by_member.SummaryBetByMemberDetail {
	details := []summary_bet_by_member.SummaryBetByMemberDetailItem{
		{
			ID: struct {
				UserID             int    `json:"user_id"`
				Phone              string `json:"phone"`
				GamingProviderCode string `json:"gaming_provider_code"`
				GameTypeID         int    `json:"game_type_id"`
			}{
				UserID:             memberID,
				Phone:              "**********",
				GamingProviderCode: "PGT",
				GameTypeID:         2,
			},
			TurnOver:                 37,
			NetTurnOver:              37,
			Jackpot:                  0,
			WinLoseNotIncludeJackpot: -20.3,
			WinLose:                  -20.3,
			CommissionNetTurnover:    37,
			CommissionAmount:         0.11,
		},
	}

	pagination := summary_bet_by_member.Pagination{
		PerPage:     "0",
		CurrentPage: "1",
		From:        0,
		To:          0,
		Total:       0,
		LastPage:    0,
		PrevPage:    nil,
		NextPage:    nil,
	}

	return summary_bet_by_member.SummaryBetByMemberDetail{
		Data:       details,
		Pagination: pagination,
	}
}
