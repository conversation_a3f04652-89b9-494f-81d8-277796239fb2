package service

import (
	"blacking-api/internal/domain/crypto_deposit"
	"blacking-api/internal/domain/payment_gateway_transaction"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/agapi"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"fmt"
	"strings"
	"time"
)

type CryptoDepositService struct {
	cryptoRepo         interfaces.CryptoDepositRepository
	paymentGatewayRepo interfaces.PaymentGatewayTransactionRepository
	paymentAccountRepo interfaces.PaymentGatewayAccountRepository
	memberRepo         interfaces.MemberRepository
	agentClient        *agapi.Client
	logger             logger.Logger
}

func NewCryptoDepositService(
	cryptoRepo interfaces.CryptoDepositRepository,
	paymentGatewayRepo interfaces.PaymentGatewayTransactionRepository,
	paymentAccountRepo interfaces.PaymentGatewayAccountRepository,
	memberRepo interfaces.MemberRepository,
	agentClient *agapi.Client,
	logger logger.Logger,
) *CryptoDepositService {
	return &CryptoDepositService{
		cryptoRepo:         cryptoRepo,
		paymentGatewayRepo: paymentGatewayRepo,
		paymentAccountRepo: paymentAccountRepo,
		memberRepo:         memberRepo,
		agentClient:        agentClient,
		logger:             logger,
	}
}

// GetConfiguration retrieves crypto deposit configuration
func (s *CryptoDepositService) GetConfiguration(ctx context.Context) (*crypto_deposit.CryptoDepositConfigResponse, error) {
	// Get supported networks
	networks, err := s.cryptoRepo.GetSupportedNetworks(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get supported networks")
		return nil, err
	}

	// Get all chain tokens
	chainTokens, err := s.cryptoRepo.ListChainTokens(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get chain tokens")
		return nil, err
	}

	// Get backend wallets
	wallets, err := s.cryptoRepo.GetBackendWallets(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get backend wallets")
		return nil, err
	}

	// Group tokens by network
	tokensByNetwork := make(map[int64][]crypto_deposit.TokenConfig)
	for _, token := range chainTokens {
		tokenConfig := crypto_deposit.TokenConfig{
			ID:              token.ID,
			ContractAddress: token.TokenContract,
			Symbol:          token.TokenSymbol,
			Decimals:        token.TokenDecimals,
			RateToTHB:       token.RateToTHB,
			IsActive:        token.IsActive,
		}
		tokensByNetwork[token.BlockchainNetworkID] = append(tokensByNetwork[token.BlockchainNetworkID], tokenConfig)
	}

	// Convert to response format with tokens joined
	var networkConfigs []crypto_deposit.NetworkConfig
	for _, network := range networks {
		networkConfig := crypto_deposit.NetworkConfig{
			ChainID:              network.ChainID,
			NetworkName:          network.NetworkName,
			NetworkType:          network.NetworkType,
			RPCUrl:               network.RPCUrl,
			ExplorerUrl:          network.ExplorerUrl,
			NativeCurrencySymbol: network.NativeCurrencySymbol,
			IsActive:             network.IsActive,
			Tokens:               tokensByNetwork[network.ID], // Join tokens for this network
		}
		networkConfigs = append(networkConfigs, networkConfig)
	}

	// Create consolidated token list for backward compatibility
	var allTokenConfigs []crypto_deposit.TokenConfig
	for _, tokens := range tokensByNetwork {
		allTokenConfigs = append(allTokenConfigs, tokens...)
	}

	// Default addresses (should be configurable)
	backendWalletAddress := "******************************************"
	finalRecipientAddress := "******************************************"

	// Use first backend wallet if available
	if len(wallets) > 0 {
		backendWalletAddress = wallets[0].WalletAddress
	}

	response := &crypto_deposit.CryptoDepositConfigResponse{
		BackendWalletAddress:  backendWalletAddress,
		FinalRecipientAddress: finalRecipientAddress,
		SupportedNetworks:     networkConfigs,
		TokenContracts:        allTokenConfigs, // For backward compatibility
	}

	return response, nil
}

// InitiateDeposit creates a new crypto deposit transaction
func (s *CryptoDepositService) InitiateDeposit(ctx context.Context, req *crypto_deposit.InitiateCryptoDepositRequest) (*crypto_deposit.InitiateDepositResponse, error) {
	// Validate request
	if err := s.validateInitiateRequest(req); err != nil {
		return nil, err
	}

	// Check if transaction ID already exists
	existingTx, _ := s.paymentGatewayRepo.GetByTransactionID(ctx, req.TransactionID)
	if existingTx != nil {
		return nil, errors.NewValidationError("transaction ID already exists")
	}

	// Get network information
	network, err := s.cryptoRepo.GetNetworkByChainID(ctx, req.ChainID)
	if err != nil {
		return nil, errors.NewValidationError("unsupported network chain ID")
	}

	// Get backend wallet for this chain
	backendWallet, err := s.cryptoRepo.GetBackendWalletByChainID(ctx, req.ChainID)
	if err != nil {
		return nil, errors.NewValidationError("no backend wallet available for this chain")
	}

	// Get crypto payment gateway account
	cryptoAccount, err := s.paymentAccountRepo.GetByCode(ctx, "CRYPTO")
	if err != nil {
		s.logger.WithError(err).Error("failed to get crypto payment gateway account")
		return nil, errors.NewInternalError("crypto payment gateway not configured")
	}

	// Create blockchain data
	blockchainData := map[string]interface{}{
		"deposit_type":   "TWO_STEP_CRYPTO",
		"chain_id":       req.ChainID,
		"network_name":   network.NetworkName,
		"token_contract": req.TokenContract,
		"token_symbol":   req.Currency,
		"wallets": map[string]string{
			"user_wallet":     req.UserWalletAddress,
			"backend_wallet":  backendWallet.WalletAddress,
			"final_recipient": "******************************************", // Should be configurable
		},
		"frontend_session_id": req.FrontendSessionID,
	}

	// Create payment gateway transaction
	now := time.Now()
	expiresAt := now.Add(30 * time.Minute) // 30 minutes expiry

	transaction := &payment_gateway_transaction.PaymentGatewayTransaction{
		TransactionID:           req.TransactionID,
		InternalReference:       req.InternalReference,
		PaymentGatewayAccountID: cryptoAccount.ID,
		Provider:                crypto_deposit.ProviderBlockchain,
		ProviderMerchantID:      crypto_deposit.ProviderBlockchain,
		TransactionType:         crypto_deposit.TransactionTypeDeposit,
		Amount:                  req.Amount,
		Currency:                req.Currency,
		Status:                  crypto_deposit.StatusInitiated,
		CustomerUsername:        req.CustomerUsername,
		InitiatedAt:             &now,
		ExpiresAt:               &expiresAt,
		Description:             fmt.Sprintf("Crypto 2-step deposit: %s %s", fmt.Sprintf("%.2f", req.Amount), req.Currency),
		BlockchainData:          blockchainData,
		CreatedBy:               req.CustomerUsername,
	}

	err = s.paymentGatewayRepo.Create(ctx, transaction)
	if err != nil {
		s.logger.WithError(err).Error("failed to create crypto deposit transaction")
		return nil, errors.NewInternalError("failed to create deposit transaction")
	}

	// Create initial log entry
	logEntry := &crypto_deposit.CryptoDepositLog{
		TransactionID: req.TransactionID,
		StepNumber:    1, // Use step 1 for initial creation (constraint allows only 1,2)
		EventType:     crypto_deposit.EventInitiated,
		EventData: map[string]interface{}{
			"amount":              req.Amount,
			"currency":            req.Currency,
			"user_wallet_address": req.UserWalletAddress,
			"chain_id":            req.ChainID,
			"token_contract":      req.TokenContract,
		},
	}
	if err := s.cryptoRepo.CreateDepositLog(ctx, logEntry); err != nil {
		s.logger.WithError(err).Error("failed to create initial crypto deposit log - continuing with transaction")
		// Don't fail the transaction for logging errors
	}

	// Prepare response
	response := &crypto_deposit.InitiateDepositResponse{
		TransactionID:         req.TransactionID,
		Status:                crypto_deposit.StatusInitiated,
		BackendWalletAddress:  backendWallet.WalletAddress,
		FinalRecipientAddress: "******************************************", // Should be configurable
		ChainID:               req.ChainID,
		TokenContract:         req.TokenContract,
		Amount:                fmt.Sprintf("%.2f", req.Amount),
		Currency:              req.Currency,
		CreatedAt:             transaction.CreatedAt,
		ExpiresAt:             expiresAt,
	}

	s.logger.WithFields(map[string]interface{}{
		"transaction_id":    req.TransactionID,
		"customer_username": req.CustomerUsername,
		"amount":            req.Amount,
		"currency":          req.Currency,
		"chain_id":          req.ChainID,
	}).Info("crypto deposit initiated successfully")

	return response, nil
}

// UpdateStep1 updates Step 1 information for a crypto deposit
func (s *CryptoDepositService) UpdateStep1(ctx context.Context, transactionID string, req *crypto_deposit.UpdateStep1Request) (*crypto_deposit.UpdateStepResponse, error) {
	// Validate request
	if err := s.validateUpdateStep1Request(req); err != nil {
		return nil, err
	}

	// Update Step 1 in repository
	err := s.cryptoRepo.UpdateCryptoDepositStep1(ctx, transactionID, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to update crypto deposit step1")
		return nil, err
	}

	// Get updated transaction for response
	updatedTx, err := s.cryptoRepo.GetCryptoDepositByTransactionID(ctx, transactionID)
	if err != nil {
		s.logger.WithError(err).Error("failed to get updated transaction")
		return nil, err
	}

	// Prepare response
	response := &crypto_deposit.UpdateStepResponse{
		TransactionID: transactionID,
		OverallStatus: updatedTx.OverallStatus,
		UpdatedAt:     updatedTx.Timestamps.UpdatedAt,
	}

	if updatedTx.Step1 != nil {
		response.Step1Status = updatedTx.Step1.Status
		response.Step1TransactionHash = updatedTx.Step1.TransactionHash
		response.Step1CompletedAt = updatedTx.Step1.ConfirmedAt
	}

	s.logger.WithFields(map[string]interface{}{
		"transaction_id":   transactionID,
		"step1_status":     req.Status,
		"transaction_hash": req.TransactionHash,
		"actual_amount":    req.ActualAmount,
	}).Info("crypto deposit step1 updated successfully")

	return response, nil
}

// UpdateStep2 updates Step 2 information for a crypto deposit
func (s *CryptoDepositService) UpdateStep2(ctx context.Context, transactionID string, req *crypto_deposit.UpdateStep2Request) (*crypto_deposit.UpdateStepResponse, error) {
	// Validate request
	if err := s.validateUpdateStep2Request(req); err != nil {
		return nil, err
	}

	// Update Step 2 in repository
	err := s.cryptoRepo.UpdateCryptoDepositStep2(ctx, transactionID, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to update crypto deposit step2")
		return nil, err
	}

	// If Step 2 is completed, automatically process credit update
	if req.Status == crypto_deposit.StepStatusConfirmed {
		err = s.processAutomaticCreditUpdate(ctx, transactionID, req.ActualAmount)
		if err != nil {
			s.logger.WithError(err).WithFields(map[string]interface{}{
				"transaction_id": transactionID,
				"actual_amount":  req.ActualAmount,
			}).Error("failed to process automatic credit update - transaction completed but credit update failed")
			// Don't fail the whole transaction, just log the error
			// The transaction is still considered successful from blockchain perspective
		}
	}

	// Get updated transaction for response
	updatedTx, err := s.cryptoRepo.GetCryptoDepositByTransactionID(ctx, transactionID)
	if err != nil {
		s.logger.WithError(err).Error("failed to get updated transaction")
		return nil, err
	}

	// Prepare response
	response := &crypto_deposit.UpdateStepResponse{
		TransactionID: transactionID,
		OverallStatus: updatedTx.OverallStatus,
		UpdatedAt:     updatedTx.Timestamps.UpdatedAt,
		CompletedAt:   updatedTx.Timestamps.CompletedAt,
	}

	if updatedTx.Step1 != nil {
		response.Step1Status = updatedTx.Step1.Status
		response.Step1TransactionHash = updatedTx.Step1.TransactionHash
		response.Step1CompletedAt = updatedTx.Step1.ConfirmedAt
	}

	if updatedTx.Step2 != nil {
		response.Step2Status = updatedTx.Step2.Status
		response.Step2TransactionHash = updatedTx.Step2.TransactionHash
		response.Step2CompletedAt = updatedTx.Step2.ConfirmedAt
	}

	if updatedTx.Summary.FinalReceivedAmount != nil {
		response.FinalReceivedAmount = updatedTx.Summary.FinalReceivedAmount
	}

	if updatedTx.Summary.CompletionTimeSeconds != nil {
		response.CompletionTimeSeconds = updatedTx.Summary.CompletionTimeSeconds
	}

	s.logger.WithFields(map[string]interface{}{
		"transaction_id":          transactionID,
		"step2_status":            req.Status,
		"transaction_hash":        req.TransactionHash,
		"actual_amount":           req.ActualAmount,
		"gas_fee_eth":             req.GasFeeETH,
		"thirdweb_transaction_id": req.ThirdwebTransactionID,
		"overall_status":          response.OverallStatus,
	}).Info("crypto deposit step2 updated successfully")

	return response, nil
}

// GetDepositByTransactionID retrieves detailed crypto deposit information
func (s *CryptoDepositService) GetDepositByTransactionID(ctx context.Context, transactionID string) (*crypto_deposit.CryptoDepositResponse, error) {
	if strings.TrimSpace(transactionID) == "" {
		return nil, errors.NewValidationError("transaction ID is required")
	}

	deposit, err := s.cryptoRepo.GetCryptoDepositByTransactionID(ctx, transactionID)
	if err != nil {
		return nil, err
	}

	// Add conversion information if deposit is completed and has token info
	if deposit.OverallStatus == crypto_deposit.StatusCompleted && deposit.Network.TokenContract != "" {
		err = s.enrichDepositWithConversionInfo(ctx, deposit)
		if err != nil {
			// Don't fail the whole response if conversion info fails, just log
			s.logger.WithError(err).WithField("transaction_id", transactionID).Warn("failed to add conversion info to deposit response")
		}
	}

	return deposit, nil
}

// ListDeposits retrieves a paginated list of crypto deposits
func (s *CryptoDepositService) ListDeposits(ctx context.Context, filters *crypto_deposit.CryptoDepositFilters) (*crypto_deposit.CryptoDepositListResponse, error) {
	// Validate and set defaults
	if filters.Page < 1 {
		filters.Page = 1
	}
	if filters.PageSize < 1 || filters.PageSize > 100 {
		filters.PageSize = 20
	}
	if filters.SortBy == "" {
		filters.SortBy = "created_at"
	}
	if filters.SortOrder == "" {
		filters.SortOrder = "desc"
	}

	// Validate sort order
	filters.SortOrder = strings.ToLower(filters.SortOrder)
	if filters.SortOrder != "asc" && filters.SortOrder != "desc" {
		filters.SortOrder = "desc"
	}

	deposits, total, err := s.cryptoRepo.ListCryptoDeposits(ctx, filters)
	if err != nil {
		return nil, err
	}

	// Calculate pagination info
	totalPages := int((total + int64(filters.PageSize) - 1) / int64(filters.PageSize))

	response := &crypto_deposit.CryptoDepositListResponse{
		Data: deposits,
		Pagination: crypto_deposit.PaginationInfo{
			CurrentPage: filters.Page,
			PageSize:    filters.PageSize,
			Total:       total,
			TotalPages:  totalPages,
		},
	}

	return response, nil
}

// GetDepositLogs retrieves logs for a specific transaction
func (s *CryptoDepositService) GetDepositLogs(ctx context.Context, transactionID string) ([]*crypto_deposit.CryptoDepositLog, error) {
	if strings.TrimSpace(transactionID) == "" {
		return nil, errors.NewValidationError("transaction ID is required")
	}

	logs, err := s.cryptoRepo.GetDepositLogs(ctx, transactionID)
	if err != nil {
		return nil, err
	}

	return logs, nil
}

// CalculateConversion calculates THB amount from token amount using stored rates
func (s *CryptoDepositService) CalculateConversion(ctx context.Context, chainID int, tokenContract string, tokenAmount float64) (*crypto_deposit.ConversionCalculationResponse, error) {
	if tokenAmount <= 0 {
		return nil, errors.NewValidationError("token amount must be greater than 0")
	}

	if !crypto_deposit.IsValidWalletAddress(tokenContract) {
		return nil, errors.NewValidationError("invalid token contract address")
	}

	// Get token rate from database
	chainToken, err := s.cryptoRepo.GetChainTokenByChainIDAndContract(ctx, chainID, tokenContract)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"chain_id":       chainID,
			"token_contract": tokenContract,
		}).Error("failed to get chain token for conversion")
		return nil, err
	}

	// Calculate THB amount
	thbAmount := tokenAmount * chainToken.RateToTHB

	response := &crypto_deposit.ConversionCalculationResponse{
		TokenAmount:   tokenAmount,
		Rate:          chainToken.RateToTHB,
		AmountTHB:     thbAmount,
		TokenSymbol:   chainToken.TokenSymbol,
		ChainID:       chainID,
		TokenContract: tokenContract,
	}

	s.logger.WithFields(map[string]interface{}{
		"chain_id":       chainID,
		"token_contract": tokenContract,
		"token_amount":   tokenAmount,
		"rate":           chainToken.RateToTHB,
		"thb_amount":     thbAmount,
	}).Info("conversion calculated successfully")

	return response, nil
}

// ListChainTokens retrieves all available chain tokens with rates
func (s *CryptoDepositService) ListChainTokens(ctx context.Context) ([]*crypto_deposit.ChainToken, error) {
	tokens, err := s.cryptoRepo.ListChainTokens(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to list chain tokens")
		return nil, err
	}

	return tokens, nil
}

// UpdateChainTokenRate updates conversion rate for a token (admin function)
func (s *CryptoDepositService) UpdateChainTokenRate(ctx context.Context, tokenID int64, newRate float64) error {
	if newRate <= 0 {
		return errors.NewValidationError("rate must be greater than 0")
	}

	err := s.cryptoRepo.UpdateChainTokenRate(ctx, tokenID, newRate)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"token_id": tokenID,
			"new_rate": newRate,
		}).Error("failed to update chain token rate")
		return err
	}

	s.logger.WithFields(map[string]interface{}{
		"token_id": tokenID,
		"new_rate": newRate,
	}).Info("chain token rate updated successfully")

	return nil
}

// processCreditUpdate processes credit update for completed deposit
func (s *CryptoDepositService) processCreditUpdate(ctx context.Context, transactionID string, creditReq *crypto_deposit.CreditUpdateRequest) error {
	// Get transaction details for logging
	deposit, err := s.cryptoRepo.GetCryptoDepositByTransactionID(ctx, transactionID)
	if err != nil {
		return err
	}

	// Validate credit update request
	if strings.TrimSpace(creditReq.GameUsername) == "" {
		return errors.NewValidationError("game username is required for credit update")
	}

	if creditReq.AmountTHB <= 0 {
		return errors.NewValidationError("THB amount must be greater than 0")
	}

	s.logger.WithFields(map[string]interface{}{
		"transaction_id":    transactionID,
		"customer_username": deposit.CustomerUsername,
		"game_username":     creditReq.GameUsername,
		"amount_thb":        creditReq.AmountTHB,
		"token_amount":      deposit.Amount,
		"currency":          deposit.Currency,
		"overall_status":    deposit.OverallStatus,
	}).Info("processing crypto deposit credit update")

	// Get member by customer username to validate
	member, err := s.memberRepo.GetByUsername(ctx, deposit.CustomerUsername)
	if err != nil {
		s.logger.WithError(err).WithField("customer_username", deposit.CustomerUsername).Error("member not found for crypto deposit credit update")
		return fmt.Errorf("member not found with username %s: %w", deposit.CustomerUsername, err)
	}

	// Validate that the game username matches
	if member.GameUsername == nil || *member.GameUsername != creditReq.GameUsername {
		memberGameUsername := "nil"
		if member.GameUsername != nil {
			memberGameUsername = *member.GameUsername
		}
		s.logger.WithFields(map[string]interface{}{
			"member_game_username":  memberGameUsername,
			"request_game_username": creditReq.GameUsername,
		}).Error("game username mismatch for crypto deposit credit update")
		return errors.NewValidationError("game username mismatch")
	}

	// Get conversion rate for the token used in this deposit
	chainToken, err := s.cryptoRepo.GetChainTokenByChainIDAndContract(ctx, deposit.Network.ChainID, deposit.Network.TokenContract)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"chain_id":       deposit.Network.ChainID,
			"token_contract": deposit.Network.TokenContract,
		}).Error("failed to get chain token for conversion rate")
		return fmt.Errorf("failed to get chain token for conversion: %w", err)
	}

	// Calculate THB amount from token amount using stored conversion rate
	// Use actual received amount from Step 2, not the original deposit amount
	var actualTokenAmount float64
	if deposit.Step2 != nil && deposit.Step2.ActualAmount != nil {
		actualTokenAmount = *deposit.Step2.ActualAmount
	} else {
		actualTokenAmount = deposit.Amount // fallback to original amount
	}

	calculatedAmountTHB := actualTokenAmount * chainToken.RateToTHB

	s.logger.WithFields(map[string]interface{}{
		"token_amount":   actualTokenAmount,
		"rate_to_thb":    chainToken.RateToTHB,
		"calculated_thb": calculatedAmountTHB,
		"requested_thb":  creditReq.AmountTHB,
		"token_symbol":   chainToken.TokenSymbol,
	}).Info("calculated THB amount from token amount for crypto deposit")

	// Use calculated amount instead of requested amount for consistency
	finalAmountTHB := calculatedAmountTHB

	// Call agent API to deposit credit
	walletAfter, err := s.callGameAPIDeposit(ctx, creditReq.GameUsername, finalAmountTHB, transactionID)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"game_username":    creditReq.GameUsername,
			"final_amount_thb": finalAmountTHB,
		}).Error("failed to call agent API for crypto deposit credit update")
		return fmt.Errorf("failed to call agent API for credit update: %w", err)
	}

	// Update member balance with wallet_after from game API
	member.Balance = walletAfter
	member.UpdatedAt = time.Now()

	if err := s.memberRepo.Update(ctx, member); err != nil {
		s.logger.WithError(err).WithField("member_id", member.ID).Error("failed to update member balance after crypto deposit")
		return fmt.Errorf("failed to update member balance: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"transaction_id":       transactionID,
		"customer_username":    deposit.CustomerUsername,
		"game_username":        creditReq.GameUsername,
		"requested_amount_thb": creditReq.AmountTHB,
		"final_amount_thb":     finalAmountTHB,
		"token_amount":         actualTokenAmount,
		"rate_to_thb":          chainToken.RateToTHB,
		"wallet_after":         walletAfter,
		"member_balance":       member.Balance,
	}).Info("crypto deposit credit update completed successfully")

	return nil
}

// callGameAPIDeposit calls the agent API to deposit credit and returns wallet_after
func (s *CryptoDepositService) callGameAPIDeposit(ctx context.Context, gameUsername string, amount float64, referenceID string) (float64, error) {
	// Call agent API using client
	response, err := s.agentClient.GamesDeposit(ctx, gameUsername, amount, referenceID)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"game_username": gameUsername,
			"amount":        amount,
			"reference_id":  referenceID,
		}).Error("failed to call agent API for games deposit")
		return 0, fmt.Errorf("failed to call agent API for games deposit: %w", err)
	}

	// AgAPI response contains WalletAfter as float64 directly
	s.logger.WithFields(map[string]interface{}{
		"game_username": gameUsername,
		"amount":        amount,
		"reference_id":  referenceID,
		"wallet_after":  response.WalletAfter,
		"wallet_before": response.WalletBefore,
		"txn_id":        response.TxnID,
	}).Info("agent API games deposit completed successfully")

	return response.WalletAfter, nil
}

// processAutomaticCreditUpdate processes credit update automatically when step2 is confirmed
func (s *CryptoDepositService) processAutomaticCreditUpdate(ctx context.Context, transactionID string, actualTokenAmount float64) error {
	// Get transaction details
	deposit, err := s.cryptoRepo.GetCryptoDepositByTransactionID(ctx, transactionID)
	if err != nil {
		return fmt.Errorf("failed to get deposit for credit update: %w", err)
	}

	// Get member by customer username
	member, err := s.memberRepo.GetByUsername(ctx, deposit.CustomerUsername)
	if err != nil {
		s.logger.WithError(err).WithField("customer_username", deposit.CustomerUsername).Error("member not found for automatic credit update")
		return fmt.Errorf("member not found with username %s: %w", deposit.CustomerUsername, err)
	}

	// Check if member has game_username
	if member.GameUsername == nil || *member.GameUsername == "" {
		s.logger.WithField("customer_username", deposit.CustomerUsername).Error("member does not have game_username for automatic credit update")
		return fmt.Errorf("member %s does not have game_username", deposit.CustomerUsername)
	}

	// Get conversion rate for the token
	chainToken, err := s.cryptoRepo.GetChainTokenByChainIDAndContract(ctx, deposit.Network.ChainID, deposit.Network.TokenContract)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"chain_id":       deposit.Network.ChainID,
			"token_contract": deposit.Network.TokenContract,
		}).Error("failed to get chain token for automatic credit update")
		return fmt.Errorf("failed to get chain token for conversion: %w", err)
	}

	// Calculate THB amount from actual token amount
	finalAmountTHB := actualTokenAmount * chainToken.RateToTHB

	s.logger.WithFields(map[string]interface{}{
		"transaction_id":      transactionID,
		"customer_username":   deposit.CustomerUsername,
		"game_username":       *member.GameUsername,
		"actual_token_amount": actualTokenAmount,
		"conversion_rate":     chainToken.RateToTHB,
		"final_amount_thb":    finalAmountTHB,
		"token_symbol":        chainToken.TokenSymbol,
	}).Info("processing automatic credit update for crypto deposit")

	// Call agent API to deposit credit
	walletAfter, err := s.callGameAPIDeposit(ctx, *member.GameUsername, finalAmountTHB, transactionID)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"game_username":    *member.GameUsername,
			"final_amount_thb": finalAmountTHB,
		}).Error("failed to call agent API for automatic credit update")
		return fmt.Errorf("failed to call agent API for automatic credit update: %w", err)
	}

	// Update member balance with wallet_after from game API
	member.Balance = walletAfter
	member.UpdatedAt = time.Now()

	if err := s.memberRepo.Update(ctx, member); err != nil {
		s.logger.WithError(err).WithField("member_id", member.ID).Error("failed to update member balance after automatic credit update")
		return fmt.Errorf("failed to update member balance: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"transaction_id":      transactionID,
		"customer_username":   deposit.CustomerUsername,
		"game_username":       *member.GameUsername,
		"actual_token_amount": actualTokenAmount,
		"final_amount_thb":    finalAmountTHB,
		"wallet_after":        walletAfter,
		"member_balance":      member.Balance,
	}).Info("automatic credit update completed successfully")

	return nil
}

// enrichDepositWithConversionInfo adds conversion information to deposit response
func (s *CryptoDepositService) enrichDepositWithConversionInfo(ctx context.Context, deposit *crypto_deposit.CryptoDepositResponse) error {
	// Get chain token for conversion rate
	chainToken, err := s.cryptoRepo.GetChainTokenByChainIDAndContract(ctx, deposit.Network.ChainID, deposit.Network.TokenContract)
	if err != nil {
		return fmt.Errorf("failed to get chain token for conversion: %w", err)
	}

	// Determine actual token amount (prefer Step2 actual amount, fallback to original amount)
	var actualTokenAmount float64
	if deposit.Step2 != nil && deposit.Step2.ActualAmount != nil {
		actualTokenAmount = *deposit.Step2.ActualAmount
	} else {
		actualTokenAmount = deposit.Amount
	}

	// Calculate credit amount in THB
	creditAmount := actualTokenAmount * chainToken.RateToTHB

	// Add conversion info to deposit
	deposit.Conversion = &crypto_deposit.ConversionInfo{
		TokenAmount:    actualTokenAmount,
		ConversionRate: chainToken.RateToTHB,
		CreditAmount:   creditAmount,
		TokenSymbol:    chainToken.TokenSymbol,
		BaseCurrency:   "THB",
	}

	s.logger.WithFields(map[string]interface{}{
		"transaction_id":  deposit.TransactionID,
		"token_amount":    actualTokenAmount,
		"conversion_rate": chainToken.RateToTHB,
		"credit_amount":   creditAmount,
		"token_symbol":    chainToken.TokenSymbol,
	}).Info("added conversion information to crypto deposit response")

	return nil
}

// Validation methods

func (s *CryptoDepositService) validateInitiateRequest(req *crypto_deposit.InitiateCryptoDepositRequest) error {
	if strings.TrimSpace(req.TransactionID) == "" {
		return errors.NewValidationError("transaction ID is required")
	}

	if req.Amount <= 0 {
		return errors.NewValidationError("amount must be greater than 0")
	}

	if strings.TrimSpace(req.Currency) == "" {
		return errors.NewValidationError("currency is required")
	}

	if strings.TrimSpace(req.CustomerUsername) == "" {
		return errors.NewValidationError("customer username is required")
	}

	if !crypto_deposit.IsValidWalletAddress(req.UserWalletAddress) {
		return errors.NewValidationError("invalid user wallet address format")
	}

	if !crypto_deposit.IsValidWalletAddress(req.TokenContract) {
		return errors.NewValidationError("invalid token contract address format")
	}

	if req.ChainID <= 0 {
		return errors.NewValidationError("chain ID must be greater than 0")
	}

	return nil
}

func (s *CryptoDepositService) validateUpdateStep1Request(req *crypto_deposit.UpdateStep1Request) error {
	if !crypto_deposit.IsValidTransactionHash(req.TransactionHash) {
		return errors.NewValidationError("invalid transaction hash format")
	}

	if !crypto_deposit.IsValidStepStatus(req.Status) {
		return errors.NewValidationError("invalid step status")
	}

	if req.ActualAmount <= 0 {
		return errors.NewValidationError("actual amount must be greater than 0")
	}

	if req.Status == crypto_deposit.StepStatusConfirmed && req.ConfirmedAt == nil {
		return errors.NewValidationError("confirmed_at is required when status is confirmed")
	}

	return nil
}

func (s *CryptoDepositService) validateUpdateStep2Request(req *crypto_deposit.UpdateStep2Request) error {
	if !crypto_deposit.IsValidTransactionHash(req.TransactionHash) {
		return errors.NewValidationError("invalid transaction hash format")
	}

	if !crypto_deposit.IsValidStepStatus(req.Status) {
		return errors.NewValidationError("invalid step status")
	}

	if req.ActualAmount <= 0 {
		return errors.NewValidationError("actual amount must be greater than 0")
	}

	if req.GasFeeETH < 0 {
		return errors.NewValidationError("gas fee cannot be negative")
	}

	if req.Status == crypto_deposit.StepStatusConfirmed && req.ConfirmedAt == nil {
		return errors.NewValidationError("confirmed_at is required when status is confirmed")
	}

	return nil
}

// UpdateBackendWalletBalance updates backend wallet balance (for monitoring)
func (s *CryptoDepositService) UpdateBackendWalletBalance(ctx context.Context, walletAddress string, ethBalance, tokenBalance float64) error {
	if !crypto_deposit.IsValidWalletAddress(walletAddress) {
		return errors.NewValidationError("invalid wallet address format")
	}

	if ethBalance < 0 || tokenBalance < 0 {
		return errors.NewValidationError("balances cannot be negative")
	}

	err := s.cryptoRepo.UpdateBackendWalletBalance(ctx, walletAddress, ethBalance, tokenBalance)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"wallet_address": walletAddress,
			"eth_balance":    ethBalance,
			"token_balance":  tokenBalance,
		}).Error("failed to update backend wallet balance")
		return err
	}

	s.logger.WithFields(map[string]interface{}{
		"wallet_address": walletAddress,
		"eth_balance":    ethBalance,
		"token_balance":  tokenBalance,
	}).Info("backend wallet balance updated successfully")

	return nil
}
