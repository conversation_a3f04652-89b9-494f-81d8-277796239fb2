package service

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"time"

	"blacking-api/internal/domain/referral"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/internal/repository/json"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/google/uuid"
)

type ReferralService interface {
	GetOverview(ctx context.Context, memberID int) (*referral.OverviewResponse, error)
	GetReferralMembers(ctx context.Context, memberID int, req referral.ReferralMembersRequest) (*referral.ReferralMembersResponse, error) // Updated signature with pagination
	GetIncomeData(ctx context.Context, memberID int, req referral.IncomeRequest) (*referral.IncomeResponse, error)
	GetTutorialAndFAQ(ctx context.Context) (*referral.TutorialFAQResponse, error)
	CreateRegister(ctx context.Context, register *referral.Register) error
	IncrementReferralView(ctx context.Context, memberID int) error
	IncrementReferralViewByCode(ctx context.Context, referralCode string) error
	IncrementDownlineCount(ctx context.Context, memberID int) error
	WithdrawCommission(ctx context.Context, memberID int, req referral.WithdrawCommissionRequest) (*referral.WithdrawCommissionResponse, error)
	GetPendingCommissions(ctx context.Context, memberID, page, limit int) (*referral.PendingCommissionResponse, error)
	ApproveCommissions(ctx context.Context, memberID int) (*referral.ApproveCommissionResponse, error)
}

type referralService struct {
	referralRepo         interfaces.ReferralRepository
	faqService           FAQService
	systemSettingService SystemSettingService
	winlossRepo          referral.ExternalWinlossRepository // For reading winloss data
	graphProcessor       *GraphProcessor                    // For processing graph data
	logger               logger.Logger
}

func NewReferralService(
	referralRepo interfaces.ReferralRepository,
	faqService FAQService,
	systemSettingService SystemSettingService,
	logger logger.Logger,
) ReferralService {
	// Initialize JSON-based winloss repository (temporary)
	winlossRepo := json.GetDefaultWinlossRepository()

	// Initialize graph processor
	graphProcessor := NewGraphProcessor(systemSettingService)

	return &referralService{
		referralRepo:         referralRepo,
		faqService:           faqService,
		systemSettingService: systemSettingService,
		winlossRepo:          winlossRepo,
		graphProcessor:       graphProcessor,
		logger:               logger,
	}
}

// GetOverview returns referral overview statistics
func (s *referralService) GetOverview(ctx context.Context, memberID int) (*referral.OverviewResponse, error) {
	if memberID <= 0 {
		return nil, errors.NewValidationError("member ID is required")
	}

	overview, err := s.referralRepo.GetOverview(memberID)
	if err != nil {
		s.logger.WithError(err).Error("failed to get referral overview")
		return nil, err
	}

	return overview, nil
}

// GetReferralMembers returns comprehensive referral data including downlines and analytics
func (s *referralService) GetReferralMembers(ctx context.Context, memberID int, req referral.ReferralMembersRequest) (*referral.ReferralMembersResponse, error) {
	if memberID <= 0 {
		return nil, errors.NewValidationError("member ID is required")
	}

	// Validate and set default pagination parameters
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 20
	}
	if req.Limit > 100 {
		req.Limit = 100 // Maximum limit for performance
	}

	// Get paginated downlines from database
	downlines, totalCount, err := s.referralRepo.GetDownlinesPaginated(memberID, req.Page, req.Limit)
	if err != nil {
		s.logger.WithError(err).Error("failed to get paginated downlines")
		return nil, err
	}

	// Calculate pagination metadata
	totalPages := (totalCount + req.Limit - 1) / req.Limit
	pagination := &referral.PaginationMeta{
		Page:       req.Page,
		Limit:      req.Limit,
		TotalPages: totalPages,
		TotalCount: totalCount,
		HasNext:    req.Page < totalPages,
		HasPrev:    req.Page > 1,
	}

	// Get commission percentage for calculations
	commissionPercent, err := s.getCommissionPercent(ctx)
	if err != nil {
		s.logger.WithError(err).Warn("failed to get commission percent, using default 5%")
		commissionPercent = 5.0 // Default fallback
	}

	// Get winloss data for today analysis
	winlossData, err := s.getWinlossDataForToday(ctx)
	if err != nil {
		s.logger.WithError(err).Warn("failed to get winloss data, using empty data")
		winlossData = []referral.WinlossDataItem{}
	}

	// Process hourly chart data
	hourlyChart := s.processHourlyChartData(winlossData)

	// Calculate today's commission
	todayCommission := s.calculateTodayCommission(winlossData, commissionPercent)

	// Group by game category
	gameStats := s.processGameCategoryStats(winlossData, commissionPercent)

	// Calculate growth percent vs yesterday
	growthPercent, err := s.calculateGrowthPercent(ctx, memberID, todayCommission)
	if err != nil {
		s.logger.WithError(err).Warn("failed to calculate growth percent")
		growthPercent = 0.0
	}

	response := &referral.ReferralMembersResponse{
		Downlines:         downlines,
		HourlyChart:       hourlyChart,
		TodayCommission:   todayCommission,
		GameCategoryStats: gameStats,
		GrowthPercent:     growthPercent,
		Pagination:        pagination,
	}

	return response, nil
}

// getWinlossDataForToday gets winloss data for today from test data (will be replaced with real API call)
func (s *referralService) getWinlossDataForToday(ctx context.Context) ([]referral.WinlossDataItem, error) {
	// Mock data simulating today's transactions throughout different hours
	now := time.Now()

	// Create comprehensive mock data for today with various hours and games
	mockData := []referral.WinlossDataItem{
		// Early morning transactions (00:00-06:00)
		{
			UplineWinloss: 2.15,
			GameCategory:  "Slot",
			CreatedAt:     time.Date(now.Year(), now.Month(), now.Day(), 1, 30, 0, 0, now.Location()),
			CreatedISO:    time.Date(now.Year(), now.Month(), now.Day(), 1, 30, 0, 0, now.Location()),
			Username:      "bk0m1",
			GameName:      "Win Win Won",
			Provider:      "PGSOFT",
			BetAmount:     10,
		},
		{
			UplineWinloss: -1.80,
			GameCategory:  "Casino",
			CreatedAt:     time.Date(now.Year(), now.Month(), now.Day(), 2, 45, 0, 0, now.Location()),
			CreatedISO:    time.Date(now.Year(), now.Month(), now.Day(), 2, 45, 0, 0, now.Location()),
			Username:      "bk0m2",
			GameName:      "Baccarat",
			Provider:      "Evolution",
			BetAmount:     50,
		},
		{
			UplineWinloss: 5.25,
			GameCategory:  "Slot",
			CreatedAt:     time.Date(now.Year(), now.Month(), now.Day(), 3, 15, 0, 0, now.Location()),
			CreatedISO:    time.Date(now.Year(), now.Month(), now.Day(), 3, 15, 0, 0, now.Location()),
			Username:      "bk0m3",
			GameName:      "Sweet Bonanza",
			Provider:      "Pragmatic",
			BetAmount:     25,
		},

		// Morning transactions (06:00-12:00)
		{
			UplineWinloss: -3.40,
			GameCategory:  "Sport",
			CreatedAt:     time.Date(now.Year(), now.Month(), now.Day(), 8, 20, 0, 0, now.Location()),
			CreatedISO:    time.Date(now.Year(), now.Month(), now.Day(), 8, 20, 0, 0, now.Location()),
			Username:      "bk0m1",
			GameName:      "Football Betting",
			Provider:      "SBO",
			BetAmount:     100,
		},
		{
			UplineWinloss: 1.85,
			GameCategory:  "Slot",
			CreatedAt:     time.Date(now.Year(), now.Month(), now.Day(), 9, 45, 0, 0, now.Location()),
			CreatedISO:    time.Date(now.Year(), now.Month(), now.Day(), 9, 45, 0, 0, now.Location()),
			Username:      "bk0m4",
			GameName:      "Gates of Olympus",
			Provider:      "Pragmatic",
			BetAmount:     20,
		},
		{
			UplineWinloss: 7.60,
			GameCategory:  "Casino",
			CreatedAt:     time.Date(now.Year(), now.Month(), now.Day(), 10, 30, 0, 0, now.Location()),
			CreatedISO:    time.Date(now.Year(), now.Month(), now.Day(), 10, 30, 0, 0, now.Location()),
			Username:      "bk0m2",
			GameName:      "Lightning Roulette",
			Provider:      "Evolution",
			BetAmount:     75,
		},

		// Afternoon transactions (12:00-18:00)
		{
			UplineWinloss: -2.20,
			GameCategory:  "Slot",
			CreatedAt:     time.Date(now.Year(), now.Month(), now.Day(), 13, 15, 0, 0, now.Location()),
			CreatedISO:    time.Date(now.Year(), now.Month(), now.Day(), 13, 15, 0, 0, now.Location()),
			Username:      "bk0m5",
			GameName:      "Starlight Princess",
			Provider:      "Pragmatic",
			BetAmount:     40,
		},
		{
			UplineWinloss: 4.75,
			GameCategory:  "Casino",
			CreatedAt:     time.Date(now.Year(), now.Month(), now.Day(), 14, 45, 0, 0, now.Location()),
			CreatedISO:    time.Date(now.Year(), now.Month(), now.Day(), 14, 45, 0, 0, now.Location()),
			Username:      "bk0m3",
			GameName:      "Dragon Tiger",
			Provider:      "Asia Gaming",
			BetAmount:     60,
		},
		{
			UplineWinloss: -1.95,
			GameCategory:  "Sport",
			CreatedAt:     time.Date(now.Year(), now.Month(), now.Day(), 15, 30, 0, 0, now.Location()),
			CreatedISO:    time.Date(now.Year(), now.Month(), now.Day(), 15, 30, 0, 0, now.Location()),
			Username:      "bk0m1",
			GameName:      "Basketball Betting",
			Provider:      "SBO",
			BetAmount:     80,
		},
		{
			UplineWinloss: 3.10,
			GameCategory:  "Fishing",
			CreatedAt:     time.Date(now.Year(), now.Month(), now.Day(), 16, 20, 0, 0, now.Location()),
			CreatedISO:    time.Date(now.Year(), now.Month(), now.Day(), 16, 20, 0, 0, now.Location()),
			Username:      "bk0m4",
			GameName:      "Fishing God",
			Provider:      "JDB",
			BetAmount:     30,
		},

		// Evening transactions (18:00-24:00)
		{
			UplineWinloss: -4.80,
			GameCategory:  "Slot",
			CreatedAt:     time.Date(now.Year(), now.Month(), now.Day(), 19, 15, 0, 0, now.Location()),
			CreatedISO:    time.Date(now.Year(), now.Month(), now.Day(), 19, 15, 0, 0, now.Location()),
			Username:      "bk0m2",
			GameName:      "Sugar Rush",
			Provider:      "Pragmatic",
			BetAmount:     35,
		},
		{
			UplineWinloss: 6.45,
			GameCategory:  "Casino",
			CreatedAt:     time.Date(now.Year(), now.Month(), now.Day(), 20, 45, 0, 0, now.Location()),
			CreatedISO:    time.Date(now.Year(), now.Month(), now.Day(), 20, 45, 0, 0, now.Location()),
			Username:      "bk0m5",
			GameName:      "Speed Baccarat",
			Provider:      "Evolution",
			BetAmount:     90,
		},
		{
			UplineWinloss: -2.75,
			GameCategory:  "Sport",
			CreatedAt:     time.Date(now.Year(), now.Month(), now.Day(), 21, 30, 0, 0, now.Location()),
			CreatedISO:    time.Date(now.Year(), now.Month(), now.Day(), 21, 30, 0, 0, now.Location()),
			Username:      "bk0m3",
			GameName:      "Tennis Betting",
			Provider:      "SBO",
			BetAmount:     70,
		},
		{
			UplineWinloss: 1.90,
			GameCategory:  "Slot",
			CreatedAt:     time.Date(now.Year(), now.Month(), now.Day(), 22, 15, 0, 0, now.Location()),
			CreatedISO:    time.Date(now.Year(), now.Month(), now.Day(), 22, 15, 0, 0, now.Location()),
			Username:      "bk0m1",
			GameName:      "Big Bass Bonanza",
			Provider:      "Pragmatic",
			BetAmount:     15,
		},
		{
			UplineWinloss: 3.35,
			GameCategory:  "Fishing",
			CreatedAt:     time.Date(now.Year(), now.Month(), now.Day(), 23, 45, 0, 0, now.Location()),
			CreatedISO:    time.Date(now.Year(), now.Month(), now.Day(), 23, 45, 0, 0, now.Location()),
			Username:      "bk0m4",
			GameName:      "Ocean King",
			Provider:      "JDB",
			BetAmount:     45,
		},
	}

	// All data is already for today, no filtering needed
	return mockData, nil
}

// processHourlyChartData creates Chart.js compatible hourly data
func (s *referralService) processHourlyChartData(data []referral.WinlossDataItem) *referral.HourlyChartData {
	// Initialize hourly buckets (24 hours)
	hourlyData := make([]float64, 24)
	labels := make([]string, 24)

	// Generate labels (00:00, 01:00, ..., 23:00)
	for i := 0; i < 24; i++ {
		labels[i] = fmt.Sprintf("%02d:00", i)
	}

	// Sum upline_winloss by hour - only positive values
	for _, item := range data {
		if item.UplineWinloss > 0 { // Only include positive winloss
			hour := item.CreatedAt.Hour()
			if hour >= 0 && hour < 24 {
				hourlyData[hour] += item.UplineWinloss
			}
		}
	}

	return &referral.HourlyChartData{
		Labels: labels,
		Datasets: []referral.Dataset{
			{
				Label:           "Commission",
				Data:            hourlyData,
				BackgroundColor: "rgba(54, 162, 235, 0.2)",
				BorderColor:     "rgba(54, 162, 235, 1)",
			},
		},
	}
}

// calculateTodayCommission calculates total commission for today - only positive winloss
func (s *referralService) calculateTodayCommission(data []referral.WinlossDataItem, commissionPercent float64) float64 {
	var totalWinloss float64
	for _, item := range data {
		if item.UplineWinloss > 0 { // Only include positive winloss
			totalWinloss += item.UplineWinloss
		}
	}
	commission := totalWinloss * (commissionPercent / 100.0)
	return math.Round(commission*100) / 100 // Round to 2 decimal places
}

// processGameCategoryStats groups data by game category - only positive winloss
func (s *referralService) processGameCategoryStats(data []referral.WinlossDataItem, commissionPercent float64) []referral.GameCategoryStat {
	categoryMap := make(map[string]float64)

	// Sum by category - only positive winloss
	for _, item := range data {
		if item.UplineWinloss > 0 { // Only include positive winloss
			categoryMap[item.GameCategory] += item.UplineWinloss
		}
	}

	// Convert to slice - only include categories with positive totals
	var stats []referral.GameCategoryStat
	for category, totalWinloss := range categoryMap {
		if totalWinloss > 0 { // Only include categories with positive totals
			commission := totalWinloss * (commissionPercent / 100.0)
			stats = append(stats, referral.GameCategoryStat{
				Category:     category,
				TotalWinloss: math.Round(totalWinloss*100) / 100, // Round to 2 decimal places
				Commission:   math.Round(commission*100) / 100,   // Round to 2 decimal places
			})
		}
	}

	return stats
}

// calculateGrowthPercent compares today's commission with yesterday's referral_transactions
func (s *referralService) calculateGrowthPercent(ctx context.Context, memberID int, todayAmount float64) (float64, error) {
	// Get yesterday's commission from referral_transactions table
	yesterdayAmount, err := s.referralRepo.GetYesterdayCommission(memberID)
	if err != nil {
		return 0.0, err
	}

	if yesterdayAmount == 0 {
		if todayAmount > 0 {
			return 100.0, nil // 100% growth if no yesterday data but have today
		}
		return 0.0, nil
	}

	growthPercent := ((todayAmount - yesterdayAmount) / yesterdayAmount) * 100
	return growthPercent, nil
}

// Legacy functions removed since we updated the API structure

// getCommissionPercent retrieves commission percentage from system settings
func (s *referralService) getCommissionPercent(ctx context.Context) (float64, error) {
	// Try to get commission percent from system settings
	if s.systemSettingService != nil {
		if setting, err := s.systemSettingService.GetSystemSetting(ctx, "referral_commission_percent"); err == nil && setting != nil {
			// Parse float value from string
			if setting.Value != "" {
				var percent float64
				switch setting.Value {
				case "1", "1.0":
					percent = 1.0
				case "2", "2.0":
					percent = 2.0
				case "3", "3.0":
					percent = 3.0
				case "4", "4.0":
					percent = 4.0
				case "5", "5.0":
					percent = 5.0
				case "10", "10.0":
					percent = 10.0
				default:
					percent = 5.0 // Default fallback
				}
				return percent, nil
			}
		}
	}

	// Default fallback value
	return 5.0, nil
}

// GetIncomeData returns income statistics and history with pagination
func (s *referralService) GetIncomeData(ctx context.Context, memberID int, req referral.IncomeRequest) (*referral.IncomeResponse, error) {
	if memberID <= 0 {
		return nil, errors.NewValidationError("member ID is required")
	}

	// Validate and set default parameters
	if req.Months < 1 || req.Months > 24 {
		req.Months = 12 // Default to 12 months
	}
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10 // Default limit for transactions
	}
	if req.Limit > 100 {
		req.Limit = 100 // Maximum limit for performance
	}

	incomeData, err := s.referralRepo.GetIncomeDataPaginated(memberID, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to get paginated income data")
		return nil, err
	}

	return incomeData, nil
}

// GetTutorialAndFAQ returns FAQs for referral system
func (s *referralService) GetTutorialAndFAQ(ctx context.Context) (*referral.TutorialFAQResponse, error) {
	var faqs []*referral.FAQ

	// Get FAQs from FAQService
	if s.faqService != nil {
		faqResponses, err := s.faqService.ListFAQs(ctx, 100, 0, "")
		if err != nil {
			s.logger.WithError(err).Error("failed to get FAQs from faq service")
			// Continue with empty FAQs if service fails
		} else {
			// Convert faq.FAQResponse to referral.FAQ
			faqs = make([]*referral.FAQ, len(faqResponses))
			for i, faqResp := range faqResponses {
				faqs[i] = &referral.FAQ{
					ID:       faqResp.ID,
					Position: faqResp.Position,
					Title:    faqResp.Title,
					Answer:   faqResp.Answer,
					Status:   string(faqResp.Status),
				}
			}
		}
	}

	// Create response with system setting data
	response := &referral.TutorialFAQResponse{
		FAQs: faqs,
	}

	// Get network tutorial settings from system settings
	if networkImage, err := s.systemSettingService.GetSystemSetting(ctx, "network_tutorial_image"); err == nil && networkImage != nil {
		response.NetworkTutorialImage = networkImage.Value
	}

	if networkText, err := s.systemSettingService.GetSystemSetting(ctx, "network_tutorial_text"); err == nil && networkText != nil {
		response.NetworkTutorialText = networkText.Value
	}

	if networkMoney, err := s.systemSettingService.GetSystemSetting(ctx, "network_tutorial_make_money_image"); err == nil && networkMoney != nil {
		response.NetworkTutorialMakeMoneyImage = networkMoney.Value
	}

	return response, nil
}

// CreateRegister creates a new referral register entry
func (s *referralService) CreateRegister(ctx context.Context, register *referral.Register) error {
	if register == nil {
		return errors.NewValidationError("register data is required")
	}

	if register.ReferUserID == uuid.Nil {
		return errors.NewValidationError("refer user ID is required")
	}

	if register.MemberID == uuid.Nil {
		return errors.NewValidationError("member ID is required")
	}

	if register.RegisterReferCode == "" {
		return errors.NewValidationError("refer code is required")
	}

	// Check if member already has a register entry
	existing, err := s.referralRepo.GetRegisterByMemberID(register.MemberID)
	if err != nil && !errors.IsNotFoundError(err) {
		s.logger.WithError(err).Error("failed to check existing register")
		return err
	}

	if existing != nil {
		return errors.NewValidationError("member already has a referral register entry")
	}

	err = s.referralRepo.Create(register)
	if err != nil {
		s.logger.WithError(err).Error("failed to create register")
		return err
	}

	s.logger.WithField("member_id", register.MemberID).
		WithField("refer_user_id", register.ReferUserID).
		Info("referral register created successfully")

	return nil
}

// IncrementReferralView increments the referral view count for a member
func (s *referralService) IncrementReferralView(ctx context.Context, memberID int) error {
	if memberID <= 0 {
		return errors.NewValidationError("invalid member ID")
	}

	err := s.referralRepo.IncrementReferralView(memberID)
	if err != nil {
		s.logger.WithError(err).WithField("member_id", memberID).Error("failed to increment referral view count")
		return err
	}

	s.logger.WithField("member_id", memberID).Info("referral view count incremented successfully")
	return nil
}

// IncrementReferralViewByCode increments the referral view count by referral code
func (s *referralService) IncrementReferralViewByCode(ctx context.Context, referralCode string) error {
	if referralCode == "" {
		return errors.NewValidationError("referral code is required")
	}

	err := s.referralRepo.IncrementReferralViewByCode(referralCode)
	if err != nil {
		s.logger.WithError(err).WithField("referral_code", referralCode).Error("failed to increment referral view count by code")
		return err
	}

	s.logger.WithField("referral_code", referralCode).Info("referral view count incremented successfully by code")
	return nil
}

// IncrementDownlineCount increments the downline count for a member when someone registers under them
func (s *referralService) IncrementDownlineCount(ctx context.Context, memberID int) error {
	if memberID <= 0 {
		return errors.NewValidationError("invalid member ID")
	}

	err := s.referralRepo.IncrementDownlineCount(memberID)
	if err != nil {
		s.logger.WithError(err).WithField("member_id", memberID).Error("failed to increment downline count")
		return err
	}

	s.logger.WithField("member_id", memberID).Info("downline count incremented successfully")
	return nil
}

// WithdrawCommission processes commission withdrawal for a member
func (s *referralService) WithdrawCommission(ctx context.Context, memberID int, req referral.WithdrawCommissionRequest) (*referral.WithdrawCommissionResponse, error) {
	if memberID <= 0 {
		return nil, errors.NewValidationError("invalid member ID")
	}

	if req.Amount <= 0 {
		return nil, errors.NewValidationError("withdraw amount must be greater than 0")
	}

	// Get current commission balance
	currentBalance, err := s.referralRepo.GetCommissionBalance(memberID)
	if err != nil {
		s.logger.WithError(err).WithField("member_id", memberID).Error("failed to get commission balance")
		return nil, err
	}

	// Get minimum withdraw amount from system settings
	minWithdrawSetting, err := s.systemSettingService.GetSystemSetting(ctx, "commission_minimum_withdraw")
	if err != nil {
		s.logger.WithError(err).Error("failed to get commission_minimum_withdraw setting")
		return nil, errors.NewDatabaseError("การถอนไม่สามารถดำเนินการได้ เนื่องจากระบบขัดข้อง")
	}

	// Parse minimum withdraw amount - must be available
	if minWithdrawSetting == nil || minWithdrawSetting.Value == "" {
		s.logger.Error("commission_minimum_withdraw setting is not configured")
		return nil, errors.NewValidationError("การถอนไม่สามารถดำเนินการได้ เนื่องจากไม่มีการกำหนดจำนวนเงินขั้นต่ำ")
	}

	// Parse the setting value to float64
	minWithdrawAmount, err := strconv.ParseFloat(minWithdrawSetting.Value, 64)
	if err != nil || minWithdrawAmount <= 0 {
		s.logger.WithError(err).WithField("setting_value", minWithdrawSetting.Value).Error("invalid commission_minimum_withdraw setting value")
		return nil, errors.NewValidationError("การถอนไม่สามารถดำเนินการได้ เนื่องจากการกำหนดจำนวนเงินขั้นต่ำไม่ถูกต้อง")
	}

	// Check if requested withdraw amount meets minimum requirement
	if req.Amount < minWithdrawAmount {
		return nil, errors.NewValidationError(fmt.Sprintf("จำนวนเงินที่ต้องการถอนต้องมากกว่าหรือเท่ากับขั้นต่ำ %.2f บาท (ต้องการถอน: %.2f บาท)", minWithdrawAmount, req.Amount))
	}

	// Check if current balance meets minimum withdraw requirement
	if currentBalance < minWithdrawAmount {
		return nil, errors.NewValidationError(fmt.Sprintf("ยอดเงินคงเหลือไม่ถึงขั้นต่ำในการถอน (ขั้นต่ำ: %.2f บาท, ยอดคงเหลือ: %.2f บาท)", minWithdrawAmount, currentBalance))
	}

	// Check if requested amount doesn't exceed current balance
	if req.Amount > currentBalance {
		return nil, errors.NewValidationError(fmt.Sprintf("ยอดเงินไม่เพียงพอ (ต้องการถอน: %.2f บาท, ยอดคงเหลือ: %.2f บาท)", req.Amount, currentBalance))
	}

	// Calculate new balance
	newBalance := currentBalance - req.Amount

	// Create withdraw transaction
	withdrawRemark := "ถอนรายได้"
	transaction := referral.NewWithdrawTransactionWithRemark(memberID, req.Amount, currentBalance, newBalance, &withdrawRemark)

	// Save transaction and update balance
	transactionID, err := s.referralRepo.CreateWithdrawTransaction(memberID, transaction)
	if err != nil {
		s.logger.WithError(err).WithField("member_id", memberID).Error("failed to create withdraw transaction")
		return nil, err
	}

	// Create response
	response := &referral.WithdrawCommissionResponse{
		TransactionID:  transactionID,
		MemberID:       memberID,
		WithdrawAmount: req.Amount,
		BalanceBefore:  currentBalance,
		BalanceAfter:   newBalance,
		Status:         referral.StatusPending,
		Remark:         withdrawRemark,
		Message:        "การถอนรายได้ของคุณอยู่ในสถานะรอดำเนินการ",
		CreatedAt:      transaction.CreatedAt.Format("2006-01-02 15:04:05"),
	}

	s.logger.WithFields(map[string]interface{}{
		"member_id":       memberID,
		"transaction_id":  transactionID,
		"withdraw_amount": req.Amount,
		"balance_before":  currentBalance,
		"balance_after":   newBalance,
	}).Info("commission withdraw transaction created successfully")

	return response, nil
}

func (s *referralService) GetPendingCommissions(ctx context.Context, memberID, page, limit int) (*referral.PendingCommissionResponse, error) {
	// Validate parameters
	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	pendingCommissions, totalCount, totalAmount, commissionPercentByCategory, totalByCategory, err := s.referralRepo.GetPendingCommissions(memberID, page, limit)
	if err != nil {
		s.logger.WithError(err).Error("failed to get pending commissions")
		return nil, err
	}

	// Calculate pagination metadata
	totalPages := (totalCount + limit - 1) / limit
	pagination := &referral.PaginationMeta{
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
		TotalCount: totalCount,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}

	response := &referral.PendingCommissionResponse{
		PendingCommissions:          pendingCommissions,
		CommissionPercentByCategory: commissionPercentByCategory,
		TotalAmount:                 totalAmount,
		TotalByCategory:             totalByCategory,
		Pagination:                  pagination,
	}

	return response, nil
}

func (s *referralService) ApproveCommissions(ctx context.Context, memberID int) (*referral.ApproveCommissionResponse, error) {
	approvedCount, totalAmount, updatedMemberIDs, err := s.referralRepo.ApproveCommissions(memberID)
	if err != nil {
		s.logger.WithError(err).Error("failed to approve commissions")
		return nil, err
	}

	response := &referral.ApproveCommissionResponse{
		ApprovedCount:    approvedCount,
		TotalAmount:      totalAmount,
		UpdatedMemberIDs: updatedMemberIDs,
	}

	s.logger.WithFields(map[string]interface{}{
		"approved_count":     approvedCount,
		"total_amount":       totalAmount,
		"updated_member_ids": updatedMemberIDs,
	}).Info("commissions approved successfully")

	return response, nil
}
