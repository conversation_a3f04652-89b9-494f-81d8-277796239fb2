package service

import (
	"context"
	"encoding/csv"
	"fmt"
	"strings"
	"time"

	"blacking-api/internal/domain/report"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
)

type ReferralReportService struct {
	repo   interfaces.ReferralReportRepository
	logger logger.Logger
}

func NewReferralReportService(repo interfaces.ReferralReportRepository, logger logger.Logger) *ReferralReportService {
	return &ReferralReportService{
		repo:   repo,
		logger: logger,
	}
}

// GetReferralReportSummary returns referral report summary with pagination
func (s *ReferralReportService) GetReferralReportSummary(ctx context.Context, filter report.ReferralReportFilter) (report.ReferralReportSummaryResponse, error) {
	// Validate filter
	if err := filter.Validate(); err != nil {
		s.logger.WithError(err).Error("invalid filter parameters")
		return report.ReferralReportSummaryResponse{}, errors.NewValidationError("invalid filter parameters")
	}

	// Get summary data
	summaries, totalCount, err := s.repo.GetReferralReportSummary(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("failed to get referral report summary")
		return report.ReferralReportSummaryResponse{}, err
	}

	// Get summary statistics
	summaryStats, err := s.repo.GetReportSummaryStatistics(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("failed to get report summary statistics")
		return report.ReferralReportSummaryResponse{}, err
	}

	// Calculate pagination
	totalPages := (totalCount + filter.Limit - 1) / filter.Limit
	pagination := report.PaginationResponse{
		Page:       filter.Page,
		Limit:      filter.Limit,
		TotalPages: totalPages,
		TotalCount: totalCount,
		HasNext:    filter.Page < totalPages,
		HasPrev:    filter.Page > 1,
	}

	response := report.ReferralReportSummaryResponse{
		Data:       summaries,
		Pagination: pagination,
		Summary:    summaryStats,
	}

	return response, nil
}

// GetReferralReportDetail returns detailed referral transactions for a specific member
func (s *ReferralReportService) GetReferralReportDetail(ctx context.Context, memberID int, filter report.ReferralReportFilter) (report.ReferralReportDetailResponse, error) {
	if memberID <= 0 {
		return report.ReferralReportDetailResponse{}, errors.NewValidationError("invalid member ID")
	}

	// Validate filter
	if err := filter.Validate(); err != nil {
		s.logger.WithError(err).Error("invalid filter parameters")
		return report.ReferralReportDetailResponse{}, errors.NewValidationError("invalid filter parameters")
	}

	// Get detail data
	details, totalCount, err := s.repo.GetReferralReportDetail(ctx, memberID, filter)
	if err != nil {
		s.logger.WithError(err).Error("failed to get referral report detail")
		return report.ReferralReportDetailResponse{}, err
	}

	// Get detail summary statistics
	summaryStats, err := s.repo.GetDetailReportSummaryStatistics(ctx, memberID, filter)
	if err != nil {
		s.logger.WithError(err).Error("failed to get detail report summary statistics")
		return report.ReferralReportDetailResponse{}, err
	}

	// Calculate pagination
	totalPages := (totalCount + filter.Limit - 1) / filter.Limit
	pagination := report.PaginationResponse{
		Page:       filter.Page,
		Limit:      filter.Limit,
		TotalPages: totalPages,
		TotalCount: totalCount,
		HasNext:    filter.Page < totalPages,
		HasPrev:    filter.Page > 1,
	}

	response := report.ReferralReportDetailResponse{
		Data:       details,
		Pagination: pagination,
		Summary:    summaryStats,
	}

	return response, nil
}

// GetReferralReportDetailGrouped returns grouped referral transactions for a specific member
func (s *ReferralReportService) GetReferralReportDetailGrouped(ctx context.Context, memberID int, filter report.ReferralReportFilter) (report.ReferralReportDetailGroupedResponse, error) {
	if memberID <= 0 {
		return report.ReferralReportDetailGroupedResponse{}, errors.NewValidationError("invalid member ID")
	}

	// Validate filter
	if err := filter.Validate(); err != nil {
		s.logger.WithError(err).Error("invalid filter parameters")
		return report.ReferralReportDetailGroupedResponse{}, errors.NewValidationError("invalid filter parameters")
	}

	// Get grouped detail data
	groupedDetails, totalCount, err := s.repo.GetReferralReportDetailGrouped(ctx, memberID, filter)
	if err != nil {
		s.logger.WithError(err).Error("failed to get referral report grouped detail")
		return report.ReferralReportDetailGroupedResponse{}, err
	}

	// Get detail summary statistics
	summaryStats, err := s.repo.GetDetailReportSummaryStatistics(ctx, memberID, filter)
	if err != nil {
		s.logger.WithError(err).Error("failed to get detail report summary statistics")
		return report.ReferralReportDetailGroupedResponse{}, err
	}

	// Calculate pagination
	totalPages := (totalCount + filter.Limit - 1) / filter.Limit
	pagination := report.PaginationResponse{
		Page:       filter.Page,
		Limit:      filter.Limit,
		TotalPages: totalPages,
		TotalCount: totalCount,
		HasNext:    filter.Page < totalPages,
		HasPrev:    filter.Page > 1,
	}

	response := report.ReferralReportDetailGroupedResponse{
		Data:       groupedDetails,
		Pagination: pagination,
		Summary:    summaryStats,
	}

	return response, nil
}

// ExportReferralReportSummaryCSV exports summary report to CSV format
func (s *ReferralReportService) ExportReferralReportSummaryCSV(ctx context.Context, filter report.ReferralReportFilter) ([]byte, error) {
	// Validate filter
	if err := filter.Validate(); err != nil {
		s.logger.WithError(err).Error("invalid filter parameters for CSV export")
		return nil, errors.NewValidationError("invalid filter parameters")
	}

	// Get all data without pagination
	summaries, err := s.repo.GetReferralReportSummaryForExport(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("failed to get referral report summary for CSV export")
		return nil, err
	}

	// Generate CSV
	return s.generateSummaryCSV(summaries)
}

// ExportReferralReportDetailCSV exports detail report to CSV format
func (s *ReferralReportService) ExportReferralReportDetailCSV(ctx context.Context, memberID int, filter report.ReferralReportFilter) ([]byte, error) {
	if memberID <= 0 {
		return nil, errors.NewValidationError("invalid member ID")
	}

	// Validate filter
	if err := filter.Validate(); err != nil {
		s.logger.WithError(err).Error("invalid filter parameters for CSV export")
		return nil, errors.NewValidationError("invalid filter parameters")
	}

	// Get all data without pagination
	details, err := s.repo.GetReferralReportDetailForExport(ctx, memberID, filter)
	if err != nil {
		s.logger.WithError(err).Error("failed to get referral report detail for CSV export")
		return nil, err
	}

	// Generate CSV
	return s.generateDetailCSV(details)
}

// ExportReferralReportDetailGroupedCSV exports grouped detail report to CSV format
func (s *ReferralReportService) ExportReferralReportDetailGroupedCSV(ctx context.Context, memberID int, filter report.ReferralReportFilter) ([]byte, error) {
	if memberID <= 0 {
		return nil, errors.NewValidationError("invalid member ID")
	}

	// Validate filter
	if err := filter.Validate(); err != nil {
		s.logger.WithError(err).Error("invalid filter parameters for CSV export")
		return nil, errors.NewValidationError("invalid filter parameters")
	}

	// Get all data without pagination
	groupedDetails, err := s.repo.GetReferralReportDetailGroupedForExport(ctx, memberID, filter)
	if err != nil {
		s.logger.WithError(err).Error("failed to get referral report grouped detail for CSV export")
		return nil, err
	}

	// Generate CSV
	return s.generateDetailGroupedCSV(groupedDetails)
}

// generateSummaryCSV generates CSV content for summary report
func (s *ReferralReportService) generateSummaryCSV(summaries []report.ReferralReportSummary) ([]byte, error) {
	var buffer strings.Builder
	writer := csv.NewWriter(&buffer)

	// Write header
	if len(summaries) > 0 {
		headers := summaries[0].ToCSVHeaders()
		if err := writer.Write(headers); err != nil {
			s.logger.WithError(err).Error("failed to write CSV headers")
			return nil, errors.NewInternalError("failed to generate CSV")
		}
	}

	// Write data rows
	for _, summary := range summaries {
		row := summary.ToCSVRow()
		if err := writer.Write(row); err != nil {
			s.logger.WithError(err).Error("failed to write CSV row")
			return nil, errors.NewInternalError("failed to generate CSV")
		}
	}

	writer.Flush()

	if err := writer.Error(); err != nil {
		s.logger.WithError(err).Error("CSV writer error")
		return nil, errors.NewInternalError("failed to generate CSV")
	}

	return []byte(buffer.String()), nil
}

// generateDetailCSV generates CSV content for detail report
func (s *ReferralReportService) generateDetailCSV(details []report.ReferralReportDetail) ([]byte, error) {
	var buffer strings.Builder
	writer := csv.NewWriter(&buffer)

	// Write header
	if len(details) > 0 {
		headers := details[0].ToCSVHeaders()
		if err := writer.Write(headers); err != nil {
			s.logger.WithError(err).Error("failed to write CSV headers")
			return nil, errors.NewInternalError("failed to generate CSV")
		}
	}

	// Write data rows
	for _, detail := range details {
		row := detail.ToCSVRow()
		if err := writer.Write(row); err != nil {
			s.logger.WithError(err).Error("failed to write CSV row")
			return nil, errors.NewInternalError("failed to generate CSV")
		}
	}

	writer.Flush()

	if err := writer.Error(); err != nil {
		s.logger.WithError(err).Error("CSV writer error")
		return nil, errors.NewInternalError("failed to generate CSV")
	}

	return []byte(buffer.String()), nil
}

// generateDetailGroupedCSV generates CSV content for grouped detail report
func (s *ReferralReportService) generateDetailGroupedCSV(groupedDetails []report.ReferralReportDetailGrouped) ([]byte, error) {
	var buffer strings.Builder
	writer := csv.NewWriter(&buffer)

	// Write header
	if len(groupedDetails) > 0 {
		headers := groupedDetails[0].ToCSVHeaders()
		if err := writer.Write(headers); err != nil {
			s.logger.WithError(err).Error("failed to write CSV headers")
			return nil, errors.NewInternalError("failed to generate CSV")
		}
	}

	// Write data rows
	for _, groupedDetail := range groupedDetails {
		row := groupedDetail.ToCSVRow()
		if err := writer.Write(row); err != nil {
			s.logger.WithError(err).Error("failed to write CSV row")
			return nil, errors.NewInternalError("failed to generate CSV")
		}
	}

	writer.Flush()

	if err := writer.Error(); err != nil {
		s.logger.WithError(err).Error("CSV writer error")
		return nil, errors.NewInternalError("failed to generate CSV")
	}

	return []byte(buffer.String()), nil
}

// ValidateReportFilter validates and applies business rules to the filter
func (s *ReferralReportService) ValidateReportFilter(filter *report.ReferralReportFilter) error {
	// Set default date range if not provided (today)
	if filter.StartDate == nil || filter.EndDate == nil {
		now := time.Now()
		startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999999999, now.Location())

		if filter.StartDate == nil {
			filter.StartDate = &startOfDay
		}
		if filter.EndDate == nil {
			filter.EndDate = &endOfDay
		}
	}

	// Validate date range
	if filter.StartDate != nil && filter.EndDate != nil {
		if filter.EndDate.Before(*filter.StartDate) {
			return errors.NewValidationError("end date must be after start date")
		}

		// Check if date range is not too large (e.g., max 1 year)
		maxRange := 365 * 24 * time.Hour
		if filter.EndDate.Sub(*filter.StartDate) > maxRange {
			return errors.NewValidationError("date range cannot exceed 1 year")
		}
	}

	// Validate pagination
	if filter.Page <= 0 {
		filter.Page = 1
	}
	if filter.Limit <= 0 {
		filter.Limit = 20
	}
	if filter.Limit > 1000 {
		filter.Limit = 1000 // Maximum limit for performance
	}

	// Validate search length
	if len(filter.Search) > 0 && len(filter.Search) < 2 {
		return errors.NewValidationError("search term must be at least 2 characters")
	}

	// Set default status if not provided
	if len(filter.Status) == 0 {
		filter.Status = []string{"success"} // Default to successful transactions
	}

	// Set default type to commission if not provided
	if len(filter.Type) == 0 {
		filter.Type = []string{"commission"} // Default to commission transactions
	}

	// Validate status values
	validStatuses := map[string]bool{
		"pending": true,
		"success": true,
		"cancel":  true,
	}
	for _, status := range filter.Status {
		if !validStatuses[status] {
			return errors.NewValidationError(fmt.Sprintf("invalid status: %s", status))
		}
	}

	// Validate type values
	if len(filter.Type) > 0 {
		validTypes := map[string]bool{
			"commission": true,
			"withdraw":   true,
			"adjustment": true,
			"bonus":      true,
		}
		for _, transactionType := range filter.Type {
			if !validTypes[transactionType] {
				return errors.NewValidationError(fmt.Sprintf("invalid type: %s", transactionType))
			}
		}
	}

	return nil
}

// GetDefaultFilter returns a default filter for today's data
func (s *ReferralReportService) GetDefaultFilter() report.ReferralReportFilter {
	return report.DefaultFilter()
}

// GetCommissionByGameCategory returns commission transactions filtered by game category
func (s *ReferralReportService) GetCommissionByGameCategory(ctx context.Context, filter report.CommissionByGameCategoryFilter) (report.CommissionByGameCategoryResponse, error) {
	// Validate filter
	if err := filter.Validate(); err != nil {
		s.logger.WithError(err).Error("invalid commission by game category filter parameters")
		return report.CommissionByGameCategoryResponse{}, errors.NewValidationError("invalid filter parameters: " + err.Error())
	}

	// Get commission data by game category
	details, totalCount, summary, err := s.repo.GetCommissionByGameCategory(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("failed to get commission by game category")
		return report.CommissionByGameCategoryResponse{}, err
	}

	// Calculate pagination
	totalPages := (totalCount + filter.Limit - 1) / filter.Limit
	pagination := report.PaginationResponse{
		Page:       filter.Page,
		Limit:      filter.Limit,
		TotalPages: totalPages,
		TotalCount: totalCount,
		HasNext:    filter.Page < totalPages,
		HasPrev:    filter.Page > 1,
	}

	response := report.CommissionByGameCategoryResponse{
		Data:       details,
		Pagination: pagination,
		Summary:    summary,
	}

	s.logger.WithFields(map[string]interface{}{
		"game_category":    filter.GameCategory,
		"total_count":      totalCount,
		"page":             filter.Page,
		"limit":            filter.Limit,
		"total_commission": summary.TotalCommissionAmount,
		"unique_members":   summary.UniqueMembers,
	}).Info("commission by game category report retrieved successfully")

	return response, nil
}