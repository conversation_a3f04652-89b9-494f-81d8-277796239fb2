package service

import (
	"blacking-api/internal/domain/bank_transaction_slip"
	"blacking-api/internal/domain/response"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"fmt"
)

// BankTransactionSlipService interface
type BankTransactionSlipService interface {
	Create(ctx context.Context, req *bank_transaction_slip.CreateRequest) (*bank_transaction_slip.Response, error)
	GetByID(ctx context.Context, id int64) (*bank_transaction_slip.Response, error)
	GetByTransactionID(ctx context.Context, transactionID int64) (*bank_transaction_slip.Response, error)
	GetList(ctx context.Context, filter *bank_transaction_slip.FilterRequest) (*response.SuccessWithPagination, error)
	GetByMemberID(ctx context.Context, memberID int64, filter *bank_transaction_slip.FilterRequest) (*response.SuccessWithPagination, error)
	Update(ctx context.Context, id int64, req *bank_transaction_slip.UpdateRequest) (*bank_transaction_slip.Response, error)
	UpdateStatus(ctx context.Context, id int64, req *bank_transaction_slip.UpdateStatusRequest) error
	Delete(ctx context.Context, id int64) error
	GetStatusCounts(ctx context.Context) (map[string]int64, error)
}

type bankTransactionSlipService struct {
	repo                interfaces.BankTransactionSlipRepository
	userTransactionRepo interfaces.UserTransactionRepository
	memberRepo          interfaces.MemberRepository
	logger              logger.Logger
}

// NewBankTransactionSlipService creates a new bank transaction slip service
func NewBankTransactionSlipService(
	repo interfaces.BankTransactionSlipRepository,
	userTransactionRepo interfaces.UserTransactionRepository,
	memberRepo interfaces.MemberRepository,
	logger logger.Logger,
) BankTransactionSlipService {
	return &bankTransactionSlipService{
		repo:                repo,
		userTransactionRepo: userTransactionRepo,
		memberRepo:          memberRepo,
		logger:              logger,
	}
}

// Create creates a new bank transaction slip
func (s *bankTransactionSlipService) Create(ctx context.Context, req *bank_transaction_slip.CreateRequest) (*bank_transaction_slip.Response, error) {
	// Validate member exists
	member, err := s.memberRepo.GetByID(ctx, fmt.Sprintf("%d", req.MemberID))
	if err != nil {
		s.logger.WithError(err).Error("failed to get member")
		return nil, errors.NewNotFoundError("member not found")
	}

	// Validate transaction exists
	transaction, err := s.userTransactionRepo.GetByID(ctx, req.TransactionID)
	if err != nil {
		s.logger.WithError(err).Error("failed to get transaction")
		return nil, errors.NewNotFoundError("transaction not found")
	}

	// Verify transaction belongs to the member
	if transaction.MemberID != int(req.MemberID) {
		return nil, errors.NewValidationError("transaction does not belong to this member")
	}

	// Create the slip
	slip := &bank_transaction_slip.BankTransactionSlip{
		MemberID:          req.MemberID,
		Status:            bank_transaction_slip.StatusPending,
		TransactionID:     req.TransactionID,
		RawQrCode:         req.RawQrCode,
		FromAccountNumber: req.FromAccountNumber,
		FromAccountName:   req.FromAccountName,
		FromBankName:      req.FromBankName,
		ToAccountNumber:   req.ToAccountNumber,
		ToAccountName:     req.ToAccountName,
		Amount:            req.Amount,
		TransactionDate:   req.TransactionDate,
		Remark:            req.Remark,
	}

	createdSlip, err := s.repo.Create(ctx, slip)
	if err != nil {
		s.logger.WithError(err).Error("failed to create bank transaction slip")
		return nil, err
	}

	// Set member info for response
	createdSlip.MemberUsername = member.Username
	fullname := ""
	if member.FirstName != nil {
		fullname = *member.FirstName
	}
	if member.LastName != nil {
		if fullname != "" {
			fullname += " "
		}
		fullname += *member.LastName
	}
	createdSlip.MemberFullname = &fullname

	resp := createdSlip.ToResponse()
	return &resp, nil
}

// GetByID retrieves a bank transaction slip by ID
func (s *bankTransactionSlipService) GetByID(ctx context.Context, id int64) (*bank_transaction_slip.Response, error) {
	slip, err := s.repo.GetByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to get bank transaction slip")
		return nil, err
	}

	resp := slip.ToResponse()
	return &resp, nil
}

// GetByTransactionID retrieves a bank transaction slip by transaction ID
func (s *bankTransactionSlipService) GetByTransactionID(ctx context.Context, transactionID int64) (*bank_transaction_slip.Response, error) {
	slip, err := s.repo.GetByTransactionID(ctx, transactionID)
	if err != nil {
		s.logger.WithError(err).Error("failed to get bank transaction slip by transaction ID")
		return nil, err
	}

	resp := slip.ToResponse()
	return &resp, nil
}

// GetList retrieves a paginated list of bank transaction slips
func (s *bankTransactionSlipService) GetList(ctx context.Context, filter *bank_transaction_slip.FilterRequest) (*response.SuccessWithPagination, error) {
	// Set default pagination
	if filter.Page < 1 {
		filter.Page = 1
	}
	if filter.Limit < 1 {
		filter.Limit = 10
	}
	if filter.Limit > 100 {
		filter.Limit = 100
	}

	slips, total, err := s.repo.GetList(ctx, filter)
	if err != nil {
		s.logger.WithError(err).Error("failed to get bank transaction slips")
		return nil, err
	}

	// Convert to responses
	responses := make([]bank_transaction_slip.Response, len(slips))
	for i, slip := range slips {
		responses[i] = slip.ToResponse()
	}

	totalPages := total / int64(filter.Limit)
	if total%int64(filter.Limit) > 0 {
		totalPages++
	}

	return &response.SuccessWithPagination{
		Message:    "Success",
		Content:    responses,
		Page:       int64(filter.Page),
		Limit:      int64(filter.Limit),
		TotalPages: totalPages,
		TotalItems: total,
	}, nil
}

// GetByMemberID retrieves bank transaction slips for a specific member
func (s *bankTransactionSlipService) GetByMemberID(ctx context.Context, memberID int64, filter *bank_transaction_slip.FilterRequest) (*response.SuccessWithPagination, error) {
	// Set default pagination
	if filter.Page < 1 {
		filter.Page = 1
	}
	if filter.Limit < 1 {
		filter.Limit = 10
	}
	if filter.Limit > 100 {
		filter.Limit = 100
	}

	slips, total, err := s.repo.GetByMemberID(ctx, memberID, filter)
	if err != nil {
		s.logger.WithError(err).Error("failed to get bank transaction slips by member ID")
		return nil, err
	}

	// Convert to responses
	responses := make([]bank_transaction_slip.Response, len(slips))
	for i, slip := range slips {
		responses[i] = slip.ToResponse()
	}

	totalPages := total / int64(filter.Limit)
	if total%int64(filter.Limit) > 0 {
		totalPages++
	}

	return &response.SuccessWithPagination{
		Message:    "Success",
		Content:    responses,
		Page:       int64(filter.Page),
		Limit:      int64(filter.Limit),
		TotalPages: totalPages,
		TotalItems: total,
	}, nil
}

// Update updates a bank transaction slip
func (s *bankTransactionSlipService) Update(ctx context.Context, id int64, req *bank_transaction_slip.UpdateRequest) (*bank_transaction_slip.Response, error) {
	// Check if slip exists
	slip, err := s.repo.GetByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to get bank transaction slip")
		return nil, err
	}

	// Only allow updates if status is pending
	if slip.Status != bank_transaction_slip.StatusPending {
		return nil, errors.NewValidationError("can only update pending slips")
	}

	// Update the slip
	err = s.repo.Update(ctx, id, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to update bank transaction slip")
		return nil, err
	}

	// Get updated slip
	updatedSlip, err := s.repo.GetByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to get updated bank transaction slip")
		return nil, err
	}

	resp := updatedSlip.ToResponse()
	return &resp, nil
}

// UpdateStatus updates the status of a bank transaction slip
func (s *bankTransactionSlipService) UpdateStatus(ctx context.Context, id int64, req *bank_transaction_slip.UpdateStatusRequest) error {
	// Validate status
	if req.Status < 1 || req.Status > 4 {
		return errors.NewValidationError("invalid status")
	}

	// Check if slip exists
	slip, err := s.repo.GetByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to get bank transaction slip")
		return err
	}

	// Validate status transition
	if err := s.validateStatusTransition(slip.Status, req.Status); err != nil {
		return err
	}

	// Update status
	err = s.repo.UpdateStatus(ctx, id, req.Status, req.Remark)
	if err != nil {
		s.logger.WithError(err).Error("failed to update bank transaction slip status")
		return err
	}

	// If approved, update the related transaction status
	if req.Status == bank_transaction_slip.StatusApproved {
		// TODO: Update user_transaction status to approved/completed
		// This would require calling the user transaction service
		s.logger.Info(fmt.Sprintf("Bank slip %d approved for transaction %d", id, slip.TransactionID))
	}

	return nil
}

// Delete deletes a bank transaction slip
func (s *bankTransactionSlipService) Delete(ctx context.Context, id int64) error {
	// Check if slip exists
	slip, err := s.repo.GetByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to get bank transaction slip")
		return err
	}

	// Only allow deletion if status is pending or cancelled
	if slip.Status != bank_transaction_slip.StatusPending && slip.Status != bank_transaction_slip.StatusCancelled {
		return errors.NewValidationError("can only delete pending or cancelled slips")
	}

	err = s.repo.Delete(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to delete bank transaction slip")
		return err
	}

	return nil
}

// GetStatusCounts gets count of slips by status
func (s *bankTransactionSlipService) GetStatusCounts(ctx context.Context) (map[string]int64, error) {
	counts := make(map[string]int64)

	// Get counts for each status
	statuses := []struct {
		status int
		name   string
	}{
		{bank_transaction_slip.StatusPending, "pending"},
		{bank_transaction_slip.StatusApproved, "approved"},
		{bank_transaction_slip.StatusRejected, "rejected"},
		{bank_transaction_slip.StatusCancelled, "cancelled"},
	}

	var total int64
	for _, st := range statuses {
		count, err := s.repo.CountByStatus(ctx, st.status)
		if err != nil {
			s.logger.WithError(err).Error(fmt.Sprintf("failed to count slips with status %d", st.status))
			return nil, err
		}
		counts[st.name] = count
		total += count
	}

	counts["total"] = total
	return counts, nil
}

// validateStatusTransition validates if a status transition is allowed
func (s *bankTransactionSlipService) validateStatusTransition(currentStatus, newStatus int) error {
	// Define allowed transitions
	allowedTransitions := map[int][]int{
		bank_transaction_slip.StatusPending: {
			bank_transaction_slip.StatusApproved,
			bank_transaction_slip.StatusRejected,
			bank_transaction_slip.StatusCancelled,
		},
		bank_transaction_slip.StatusApproved: {
			// Once approved, cannot change status
		},
		bank_transaction_slip.StatusRejected: {
			bank_transaction_slip.StatusPending, // Can resubmit
		},
		bank_transaction_slip.StatusCancelled: {
			// Once cancelled, cannot change status
		},
	}

	allowed, exists := allowedTransitions[currentStatus]
	if !exists {
		return errors.NewValidationError("invalid current status")
	}

	for _, status := range allowed {
		if status == newStatus {
			return nil
		}
	}

	return errors.NewValidationError(fmt.Sprintf("cannot transition from %s to %s",
		bank_transaction_slip.GetStatusName(currentStatus),
		bank_transaction_slip.GetStatusName(newStatus)))
}
