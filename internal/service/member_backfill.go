package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// BackfillGameUsernameV2 calls Game Service directly to get proper game username
func (s *memberService) BackfillGameUsernameV2(ctx context.Context, memberID string) (string, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "BackfillGameUsernameV2").WithField("member_id", memberID)

	// Get member by ID
	member, err := s.memberRepo.GetByID(ctx, memberID)
	if err != nil {
		log.WithError(err).Error("failed to get member by ID")
		return "", fmt.Errorf("failed to get member: %w", err)
	}

	// Log current game_username status
	currentGameUsername := "nil"
	if member.GameUsername != nil {
		currentGameUsername = *member.GameUsername
	}

	// Always force create new game username for backfill (don't check existing)
	log.With<PERSON>ields(map[string]interface{}{
		"current_game_username": currentGameUsername,
		"phone":                 *member.Phone,
	}).Info("forcing game username creation regardless of existing value")

	// Member either has no game_username or has invalid game_username (like phone number)
	log.WithFields(map[string]interface{}{
		"current_game_username": func() string {
			if member.GameUsername != nil {
				return *member.GameUsername
			}
			return "nil"
		}(),
		"phone": *member.Phone,
	}).Info("member needs valid game_username, calling Game Service")

	// Check if member has phone for Game API call
	if member.Phone == nil || *member.Phone == "" {
		log.Error("member has no phone number for Game API call")
		return "", fmt.Errorf("member has no phone number for Game API call")
	}

	// Check if AG API client is configured
	if s.agClient == nil {
		log.Error("AG API client not configured")
		return "", fmt.Errorf("AG API client not configured")
	}

	// Call AG API force game user endpoint
	agAPIURL := fmt.Sprintf("%s/members/by-phone/%s/force-game-user", strings.TrimSuffix(s.agClient.GetBaseURL(), "/"), *member.Phone)

	// Create HTTP request (no body needed)
	req, err := http.NewRequestWithContext(ctx, "POST", agAPIURL, nil)
	if err != nil {
		log.WithError(err).Error("failed to create AG API request")
		return "", fmt.Errorf("failed to create AG API request: %w", err)
	}

	// Add Basic Auth header
	authHeader := s.agClient.CreateAuthHeader()
	req.Header.Set("Authorization", authHeader)

	// Log request details
	log.WithFields(map[string]interface{}{
		"url":   agAPIURL,
		"phone": *member.Phone,
	}).Info("calling AG API force game user endpoint")

	// Make HTTP request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		log.WithError(err).Error("failed to call Game API")
		return "", fmt.Errorf("failed to call Game API: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.WithError(err).Error("failed to read Game API response")
		return "", fmt.Errorf("failed to read Game API response: %w", err)
	}

	// Log response details
	log.WithFields(map[string]interface{}{
		"status_code":   resp.StatusCode,
		"response_body": string(bodyBytes),
	}).Info("AG API response received")

	// Check if request was successful
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		log.WithFields(map[string]interface{}{
			"status_code":   resp.StatusCode,
			"response_body": string(bodyBytes),
		}).Error("AG API returned non-success status")
		return "", fmt.Errorf("AG API returned status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse AG API response (from force game user endpoint)
	var agResponse struct {
		Success bool `json:"success"`
		Data    struct {
			Username string `json:"username"`
		} `json:"data"`
	}

	if err := json.Unmarshal(bodyBytes, &agResponse); err != nil {
		log.WithError(err).WithField("response_body", string(bodyBytes)).Error("failed to parse AG API response")
		return "", fmt.Errorf("failed to parse AG API response: %w", err)
	}

	if !agResponse.Success {
		log.Error("AG API returned unsuccessful response")
		return "", fmt.Errorf("AG API returned unsuccessful response")
	}

	gameUsername := agResponse.Data.Username
	if gameUsername == "" {
		log.Error("AG API returned empty username")
		return "", fmt.Errorf("AG API returned empty username")
	}

	log.WithField("game_username", gameUsername).Info("successfully created user via AG API")

	// Update game_username in blacking-api database
	member.GameUsername = &gameUsername
	member.UpdatedAt = time.Now()

	if err := s.memberRepo.Update(ctx, member); err != nil {
		log.WithError(err).Error("failed to update member game_username")
		return "", fmt.Errorf("failed to update member game_username: %w", err)
	}

	log.WithField("game_username", gameUsername).Info("successfully backfilled game_username")
	return gameUsername, nil
}
