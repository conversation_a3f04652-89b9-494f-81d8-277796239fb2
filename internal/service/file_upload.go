package service

import (
	"blacking-api/internal/domain/promotion_web"
	"blacking-api/pkg/logger"
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
)

type FileUploadService interface {
	UploadPromotionCover(file *multipart.FileHeader) (*promotion_web.FileUploadResponse, error)
	ValidateFile(file *multipart.FileHeader) error
}

type fileUploadService struct {
	logger     logger.Logger
	s3Client   *s3.Client
	bucketName string
	useS3      bool
	localPath  string
	maxSize    int64
}

func NewFileUploadService(logger logger.Logger) FileUploadService {
	useS3 := os.Getenv("USE_S3") == "true"
	maxSize := int64(5 * 1024 * 1024) // 5MB default

	if useS3 {
		cfg, err := config.LoadDefaultConfig(context.TODO(),
			config.WithRegion(os.Getenv("AWS_REGION")),
		)
		if err != nil {
			logger.WithError(err).Error("failed to load AWS config")
			// Fall back to local storage
			return &fileUploadService{
				logger:    logger,
				useS3:     false,
				localPath: getLocalPath(),
				maxSize:   maxSize,
			}
		}

		return &fileUploadService{
			logger:     logger,
			s3Client:   s3.NewFromConfig(cfg),
			bucketName: os.Getenv("S3_BUCKET_NAME"),
			useS3:      true,
			maxSize:    maxSize,
		}
	}

	return &fileUploadService{
		logger:    logger,
		useS3:     false,
		localPath: getLocalPath(),
		maxSize:   maxSize,
	}
}

func getLocalPath() string {
	path := os.Getenv("UPLOAD_PATH")
	if path == "" {
		path = "./uploads" // Default upload path
	}
	return path
}

func (s *fileUploadService) UploadPromotionCover(file *multipart.FileHeader) (*promotion_web.FileUploadResponse, error) {
	s.logger.WithField("filename", file.Filename).WithField("size", file.Size).Info("uploading promotion cover")

	// Validate file
	if err := s.ValidateFile(file); err != nil {
		s.logger.WithError(err).Error("file validation failed")
		return nil, err
	}

	// Generate unique filename
	filename := s.generateUniqueFilename(file.Filename)

	if s.useS3 {
		return s.uploadToS3(file, filename)
	}

	return s.uploadToLocal(file, filename)
}

func (s *fileUploadService) ValidateFile(file *multipart.FileHeader) error {
	// Check file size
	if file.Size > s.maxSize {
		return fmt.Errorf("file size exceeds maximum allowed size of %d bytes", s.maxSize)
	}

	// Check file extension
	ext := strings.ToLower(filepath.Ext(file.Filename))
	allowedExts := []string{".jpg", ".jpeg", ".png", ".gif", ".webp"}

	isAllowed := false
	for _, allowedExt := range allowedExts {
		if ext == allowedExt {
			isAllowed = true
			break
		}
	}

	if !isAllowed {
		return fmt.Errorf("file type %s is not allowed. Allowed types: %v", ext, allowedExts)
	}

	return nil
}

func (s *fileUploadService) generateUniqueFilename(originalFilename string) string {
	ext := filepath.Ext(originalFilename)
	timestamp := time.Now().Unix()
	return fmt.Sprintf("promotion_cover_%d%s", timestamp, ext)
}

func (s *fileUploadService) uploadToS3(file *multipart.FileHeader, filename string) (*promotion_web.FileUploadResponse, error) {
	src, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer src.Close()

	key := fmt.Sprintf("promotion-covers/%s", filename)

	_, err = s.s3Client.PutObject(context.TODO(), &s3.PutObjectInput{
		Bucket:      aws.String(s.bucketName),
		Key:         aws.String(key),
		Body:        src,
		ContentType: aws.String(s.getContentType(filename)),
		ACL:         types.ObjectCannedACLPublicRead,
	})

	if err != nil {
		return nil, fmt.Errorf("failed to upload to S3: %w", err)
	}

	url := fmt.Sprintf("https://%s.s3.amazonaws.com/%s", s.bucketName, key)

	s.logger.WithField("url", url).Info("file uploaded to S3 successfully")

	return &promotion_web.FileUploadResponse{
		URL: url,
	}, nil
}

func (s *fileUploadService) uploadToLocal(file *multipart.FileHeader, filename string) (*promotion_web.FileUploadResponse, error) {
	// Ensure upload directory exists
	uploadDir := filepath.Join(s.localPath, "promotion-covers")
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create upload directory: %w", err)
	}

	// Open source file
	src, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open source file: %w", err)
	}
	defer src.Close()

	// Create destination file
	dstPath := filepath.Join(uploadDir, filename)
	dst, err := os.Create(dstPath)
	if err != nil {
		return nil, fmt.Errorf("failed to create destination file: %w", err)
	}
	defer dst.Close()

	// Copy file content
	_, err = io.Copy(dst, src)
	if err != nil {
		return nil, fmt.Errorf("failed to copy file content: %w", err)
	}

	url := fmt.Sprintf("/uploads/promotion-covers/%s", filename)

	s.logger.WithField("path", dstPath).Info("file uploaded locally successfully")

	return &promotion_web.FileUploadResponse{
		URL: url,
	}, nil
}

func (s *fileUploadService) getContentType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))
	switch ext {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".webp":
		return "image/webp"
	default:
		return "application/octet-stream"
	}
}
