package service

import (
	"blacking-api/internal/domain/member_audit_log"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/logger"
	"context"
	"encoding/json"
	"fmt"
	"strconv"
)

// MemberAuditLogService defines the interface for member audit log service operations
type MemberAuditLogService interface {
	// LogMemberAction logs a member action to audit log
	LogMemberAction(ctx context.Context, req member_audit_log.CreateAuditLogRequest) error

	// LogMemberActionWithDetails logs a member action with detailed field changes
	LogMemberActionWithDetails(ctx context.Context, req member_audit_log.CreateAuditLogRequest, oldMember, newMember interface{}) error

	// ListAuditLogs retrieves audit logs with pagination and filters
	ListAuditLogs(ctx context.Context, limit, offset int, filter *member_audit_log.MemberAuditLogFilter) ([]*member_audit_log.MemberAuditLogResponse, error)

	// GetAuditLogsCount returns the total count of audit logs with filters
	GetAuditLogsCount(ctx context.Context, filter *member_audit_log.MemberAuditLogFilter) (int64, error)
}

type memberAuditLogService struct {
	auditLogRepo        interfaces.MemberAuditLogRepository
	memberGroupRepo     interfaces.MemberGroupRepository
	referralGroupRepo   interfaces.ReferralGroupRepository
	commissionGroupRepo interfaces.CommissionGroupRepository
	bankingRepo         interfaces.BankingRepository
	logger              logger.Logger
}

// NewMemberAuditLogService creates a new member audit log service
func NewMemberAuditLogService(
	auditLogRepo interfaces.MemberAuditLogRepository,
	memberGroupRepo interfaces.MemberGroupRepository,
	referralGroupRepo interfaces.ReferralGroupRepository,
	commissionGroupRepo interfaces.CommissionGroupRepository,
	bankingRepo interfaces.BankingRepository,
	logger logger.Logger,
) MemberAuditLogService {
	return &memberAuditLogService{
		auditLogRepo:        auditLogRepo,
		memberGroupRepo:     memberGroupRepo,
		referralGroupRepo:   referralGroupRepo,
		commissionGroupRepo: commissionGroupRepo,
		bankingRepo:         bankingRepo,
		logger:              logger,
	}
}

// LogMemberAction logs a member action to audit log
func (s *memberAuditLogService) LogMemberAction(ctx context.Context, req member_audit_log.CreateAuditLogRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "LogMemberAction")

	// Create new audit log
	auditLog, err := member_audit_log.NewMemberAuditLog(req)
	if err != nil {
		log.WithError(err).Error("failed to create member audit log domain object")
		return err
	}

	// Save to repository
	if err := s.auditLogRepo.Create(ctx, auditLog); err != nil {
		log.WithError(err).Error("failed to save member audit log")
		return err
	}

	log.WithField("audit_log_id", auditLog.ID).Info("member audit log created successfully")
	return nil
}

// LogMemberActionWithDetails logs a member action with detailed field changes
func (s *memberAuditLogService) LogMemberActionWithDetails(ctx context.Context, req member_audit_log.CreateAuditLogRequest, oldMember, newMember interface{}) error {
	log := s.logger.WithContext(ctx).WithField("operation", "LogMemberActionWithDetails")

	// Create new audit log
	auditLog, err := member_audit_log.NewMemberAuditLog(req)
	if err != nil {
		log.WithError(err).Error("failed to create member audit log domain object")
		return err
	}

	// Save to repository
	if err := s.auditLogRepo.Create(ctx, auditLog); err != nil {
		log.WithError(err).Error("failed to save member audit log")
		return err
	}

	log.WithField("audit_log_id", auditLog.ID).Info("member audit log with details created successfully")
	return nil
}

// ListAuditLogs retrieves audit logs with pagination and filters
func (s *memberAuditLogService) ListAuditLogs(ctx context.Context, limit, offset int, filter *member_audit_log.MemberAuditLogFilter) ([]*member_audit_log.MemberAuditLogResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListAuditLogs")

	// Validate pagination parameters
	if limit <= 0 {
		limit = 10 // default limit
	}
	if limit > 100 {
		limit = 100 // max limit
	}
	if offset < 0 {
		offset = 0
	}

	// Handle nil filter
	if filter == nil {
		filter = &member_audit_log.MemberAuditLogFilter{}
	}

	// Get audit logs from repository
	auditLogs, err := s.auditLogRepo.ListWithFilter(ctx, limit, offset, filter)
	if err != nil {
		log.WithError(err).Error("failed to get audit logs from repository")
		return nil, err
	}

	// Convert to response format with detailed changes
	responses := make([]*member_audit_log.MemberAuditLogResponse, len(auditLogs))
	for i, auditLog := range auditLogs {
		response := auditLog.ToResponse()

		// Extract action_remark from NewValues JSON if exists
		if auditLog.NewValues != nil {
			var newValues map[string]interface{}
			if err := json.Unmarshal([]byte(*auditLog.NewValues), &newValues); err == nil {
				if actionRemark, exists := newValues["action_remark"]; exists {
					if remarkStr, ok := actionRemark.(string); ok {
						response.ActionRemark = &remarkStr
					}
				}
			}
		}

		// Hide raw JSON values for cleaner response
		response.OldValues = nil
		response.NewValues = nil

		// Handle special cases for password_change and change_partner actions
		if auditLog.Action == member_audit_log.ActionPassword {
			// For password changes, create a simple change entry
			passwordChange := member_audit_log.CreateAuditFieldChange(
				"รหัสผ่าน",
				"xxxxxxxx", // Mask old password
				"xxxxxxxx", // Mask new password
			)
			passwordChange.HasChange = true // Always true for password changes
			response.Changes = []member_audit_log.AuditFieldChange{passwordChange}

			// Populate separate arrays for frontend table rendering
			response.Titles = []string{"รหัสผ่าน"}
			response.Befores = []string{"xxxxxxxx"}
			response.Afters = []string{"xxxxxxxx"}
			response.HasChanges = []bool{true}
		} else if auditLog.Action == member_audit_log.ActionChangePartner {
			// For partner changes, parse and display partner information
			if auditLog.OldValues != nil || auditLog.NewValues != nil {
				changes, err := s.buildPartnerChanges(ctx, auditLog.OldValues, auditLog.NewValues)
				if err != nil {
					log.WithError(err).Warn("failed to build partner changes")
				} else {
					response.Changes = changes

					// Populate separate arrays for frontend table rendering
					titles := make([]string, len(changes))
					befores := make([]string, len(changes))
					afters := make([]string, len(changes))
					hasChanges := make([]bool, len(changes))

					for i, change := range changes {
						titles[i] = change.Title
						befores[i] = change.Before
						afters[i] = change.After
						hasChanges[i] = change.HasChange
					}

					response.Titles = titles
					response.Befores = befores
					response.Afters = afters
					response.HasChanges = hasChanges
				}
			}
		} else if auditLog.Action == member_audit_log.ActionUpdateBankInfo {
			// For bank info updates, parse and display bank information
			if auditLog.OldValues != nil || auditLog.NewValues != nil {
				changes, err := s.buildBankInfoChanges(ctx, auditLog.OldValues, auditLog.NewValues)
				if err != nil {
					log.WithError(err).Warn("failed to build bank info changes")
				} else {
					response.Changes = changes

					// Populate separate arrays for frontend table rendering
					titles := make([]string, len(changes))
					befores := make([]string, len(changes))
					afters := make([]string, len(changes))
					hasChanges := make([]bool, len(changes))

					for i, change := range changes {
						titles[i] = change.Title
						befores[i] = change.Before
						afters[i] = change.After
						hasChanges[i] = change.HasChange
					}

					response.Titles = titles
					response.Befores = befores
					response.Afters = afters
					response.HasChanges = hasChanges
				}
			}
		} else {
			// Populate detailed changes for other actions
			if auditLog.OldValues != nil || auditLog.NewValues != nil {
				changes, err := s.buildDetailedChanges(ctx, auditLog.OldValues, auditLog.NewValues)
				if err != nil {
					log.WithError(err).Warn("failed to build detailed changes")
				} else {
					response.Changes = changes

					// Populate separate arrays for frontend table rendering
					titles := make([]string, len(changes))
					befores := make([]string, len(changes))
					afters := make([]string, len(changes))
					hasChanges := make([]bool, len(changes))

					for i, change := range changes {
						titles[i] = change.Title
						befores[i] = change.Before
						afters[i] = change.After
						hasChanges[i] = change.HasChange
					}

					response.Titles = titles
					response.Befores = befores
					response.Afters = afters
					response.HasChanges = hasChanges
				}
			}
		}

		responses[i] = &response
	}

	log.WithField("count", len(responses)).Info("member audit logs retrieved successfully")
	return responses, nil
}

// GetAuditLogsCount returns the total count of audit logs with filters
func (s *memberAuditLogService) GetAuditLogsCount(ctx context.Context, filter *member_audit_log.MemberAuditLogFilter) (int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetAuditLogsCount")

	// Handle nil filter
	if filter == nil {
		filter = &member_audit_log.MemberAuditLogFilter{}
	}

	count, err := s.auditLogRepo.CountWithFilter(ctx, filter)
	if err != nil {
		log.WithError(err).Error("failed to get audit logs count from repository")
		return 0, err
	}

	log.WithField("count", count).Info("member audit logs count retrieved successfully")
	return count, nil
}

// buildPartnerChanges creates detailed field changes for partner changes
func (s *memberAuditLogService) buildPartnerChanges(ctx context.Context, oldValuesJSON, newValuesJSON *string) ([]member_audit_log.AuditFieldChange, error) {
	var changes []member_audit_log.AuditFieldChange

	// Parse old and new values
	var oldValues, newValues map[string]interface{}

	if oldValuesJSON != nil {
		if err := json.Unmarshal([]byte(*oldValuesJSON), &oldValues); err != nil {
			s.logger.WithError(err).Error("failed to parse old values JSON for partner change")
			oldValues = make(map[string]interface{})
		}
	} else {
		oldValues = make(map[string]interface{})
	}

	if newValuesJSON != nil {
		if err := json.Unmarshal([]byte(*newValuesJSON), &newValues); err != nil {
			s.logger.WithError(err).Error("failed to parse new values JSON for partner change")
			newValues = make(map[string]interface{})
		}
	} else {
		newValues = make(map[string]interface{})
	}

	// Handle refer_user_id and partner_name changes
	oldPartnerName := s.getStringValue(oldValues, "partner_name")
	newPartnerName := s.getStringValue(newValues, "partner_name")
	oldReferUserID := s.getStringValue(oldValues, "refer_user_id")
	newReferUserID := s.getStringValue(newValues, "refer_user_id")

	// Debug log for partner values
	s.logger.WithField("old_partner_name", oldPartnerName).WithField("new_partner_name", newPartnerName).
		WithField("old_refer_user_id", oldReferUserID).WithField("new_refer_user_id", newReferUserID).
		Info("parsing partner data from audit log")

	// Add partner ID change entry
	if oldReferUserID != newReferUserID {
		partnerIDChange := member_audit_log.CreateAuditFieldChange(
			"Partner ID",
			s.formatPartnerIDValue(oldReferUserID),
			s.formatPartnerIDValue(newReferUserID),
		)
		partnerIDChange.HasChange = true
		changes = append(changes, partnerIDChange)
	}

	// Add partner name change entry
	partnerNameChange := member_audit_log.CreateAuditFieldChange(
		"Partner",
		s.formatPartnerValue(oldPartnerName),
		s.formatPartnerValue(newPartnerName),
	)
	partnerNameChange.HasChange = (oldPartnerName != newPartnerName)
	changes = append(changes, partnerNameChange)

	return changes, nil
}

// Helper function to get string value from map
func (s *memberAuditLogService) getStringValue(values map[string]interface{}, key string) string {
	if val, exists := values[key]; exists {
		if val == nil {
			return ""
		}
		// Handle string
		if strVal, ok := val.(string); ok {
			return strVal
		}
		// Handle pointer to string
		if strPtr, ok := val.(*string); ok {
			if strPtr == nil {
				return ""
			}
			return *strPtr
		}
		// Handle integer
		if intVal, ok := val.(int); ok {
			return fmt.Sprintf("%d", intVal)
		}
		// Handle pointer to integer
		if intPtr, ok := val.(*int); ok {
			if intPtr == nil {
				return ""
			}
			return fmt.Sprintf("%d", *intPtr)
		}
		// Handle float64 (JSON unmarshal often produces float64 for numbers)
		if floatVal, ok := val.(float64); ok {
			return fmt.Sprintf("%.0f", floatVal)
		}
	}
	return ""
}

// Helper function to format partner value for display
func (s *memberAuditLogService) formatPartnerValue(partnerName string) string {
	if partnerName == "" {
		return "ไม่มี Partner"
	}
	return partnerName
}

// Helper function to format partner ID value for display
func (s *memberAuditLogService) formatPartnerIDValue(partnerID string) string {
	if partnerID == "" || partnerID == "<nil>" || partnerID == "null" {
		return "ไม่มี Partner"
	}
	return partnerID
}

// buildBankInfoChanges creates detailed field changes for bank info updates
func (s *memberAuditLogService) buildBankInfoChanges(ctx context.Context, oldValuesJSON, newValuesJSON *string) ([]member_audit_log.AuditFieldChange, error) {
	var changes []member_audit_log.AuditFieldChange

	// Parse old and new values
	var oldValues, newValues map[string]interface{}

	if oldValuesJSON != nil {
		if err := json.Unmarshal([]byte(*oldValuesJSON), &oldValues); err != nil {
			s.logger.WithError(err).Error("failed to parse old values JSON for bank info change")
			oldValues = make(map[string]interface{})
		}
	} else {
		oldValues = make(map[string]interface{})
	}

	if newValuesJSON != nil {
		if err := json.Unmarshal([]byte(*newValuesJSON), &newValues); err != nil {
			s.logger.WithError(err).Error("failed to parse new values JSON for bank info change")
			newValues = make(map[string]interface{})
		}
	} else {
		newValues = make(map[string]interface{})
	}

	// List of bank info fields to check
	bankInfoFields := []string{
		"bank_code",
		"bank_number",
		"first_name",
		"last_name",
		"tw_username",
	}

	for _, field := range bankInfoFields {
		oldValue := s.getStringValue(oldValues, field)
		newValue := s.getStringValue(newValues, field)

		if oldValue != newValue {
			// Get display title from MemberFieldTitles
			title := member_audit_log.MemberFieldTitles[field]
			if title == "" {
				title = field // Fallback to field name if no title found
			}

			change := member_audit_log.CreateAuditFieldChange(
				title,
				s.formatBankValue(field, oldValue),
				s.formatBankValue(field, newValue),
			)
			change.HasChange = true
			changes = append(changes, change)
		}
	}

	return changes, nil
}

// Helper function to format bank field values for display
func (s *memberAuditLogService) formatBankValue(field, value string) string {
	if value == "" {
		switch field {
		case "bank_code":
			return "ไม่มีข้อมูล"
		case "bank_number":
			return "ไม่มีข้อมูล"
		case "first_name":
			return "ไม่มีข้อมูล"
		case "last_name":
			return "ไม่มีข้อมูล"
		case "tw_username":
			return "ไม่มีข้อมูล"
		default:
			return "ไม่มีข้อมูล"
		}
	}
	return value
}

// buildDetailedChanges creates detailed field changes by comparing old and new values with join lookups
func (s *memberAuditLogService) buildDetailedChanges(ctx context.Context, oldValuesJSON, newValuesJSON *string) ([]member_audit_log.AuditFieldChange, error) {
	var changes []member_audit_log.AuditFieldChange

	// Parse old and new values
	var oldValues, newValues map[string]interface{}

	if oldValuesJSON != nil {
		if err := json.Unmarshal([]byte(*oldValuesJSON), &oldValues); err != nil {
			s.logger.WithError(err).Error("failed to parse old values JSON")
			oldValues = make(map[string]interface{})
		}
	} else {
		oldValues = make(map[string]interface{})
	}

	if newValuesJSON != nil {
		if err := json.Unmarshal([]byte(*newValuesJSON), &newValues); err != nil {
			s.logger.WithError(err).Error("failed to parse new values JSON")
			newValues = make(map[string]interface{})
		}
	} else {
		newValues = make(map[string]interface{})
	}

	// Define field order as requested (15 fields)
	fieldOrder := []string{
		"fullname",            // 1.ชื่อ-นามสกุล
		"gender",              // 2.เพศ
		"birth_date",          // 3.วันเกิด
		"phone",               // 4.เบอร์โทร
		"member_group_id",     // 5.กลุ่มผู้ใช้งาน
		"referral_group_id",   // 6.กลุ่มเพื่อนชวนเพื่อน
		"commission_group_id", // 7.กลุ่มคอมิชชั่น
		"remark",              // 8.หมายเหตุ
		"bank_code",           // 9.ธนาคาร
		"bank_account_name",   // 10.ชื่อบัญชีธนาคาร
		"bank_number",         // 11.เลขบัญชีธนาคาร
		"address_detail",      // 12.รายละเอียดที่อยู่
		"address_province",    // 13.จังหวัด
		"address_amphoe",      // 14.อำเภอ
		"address_district",    // 15.ตำบล
		"password",            // 16.รหัสผ่าน (เพิ่มเติม)
	}

	// Get all unique keys from both old and new values
	allKeys := make(map[string]bool)
	for key := range oldValues {
		allKeys[key] = true
	}
	for key := range newValues {
		allKeys[key] = true
	}

	// Build field changes in the specified order
	for _, key := range fieldOrder {
		if allKeys[key] {
			change, err := s.buildFieldChange(ctx, key, oldValues[key], newValues[key])
			if err != nil {
				s.logger.WithError(err).WithField("field", key).Warn("failed to build field change")
				continue
			}
			if change != nil {
				changes = append(changes, *change)
				delete(allKeys, key) // Remove from allKeys to avoid duplication
			}
		}
	}

	// Add any remaining keys that are not in the predefined order
	for key := range allKeys {
		change, err := s.buildFieldChange(ctx, key, oldValues[key], newValues[key])
		if err != nil {
			s.logger.WithError(err).WithField("field", key).Warn("failed to build field change")
			continue
		}
		if change != nil {
			changes = append(changes, *change)
		}
	}

	return changes, nil
}

// buildFieldChange creates a field change entry for a specific field with join lookup if needed
func (s *memberAuditLogService) buildFieldChange(ctx context.Context, key string, oldValue, newValue interface{}) (*member_audit_log.AuditFieldChange, error) {
	// Get field title from mapping
	title, exists := member_audit_log.MemberFieldTitles[key]
	if !exists {
		// If field not in our mapping, skip it or use the key as title
		return nil, nil
	}

	// Convert values to strings and handle special cases with joins
	beforeStr := s.convertValueToString(ctx, key, oldValue)
	afterStr := s.convertValueToString(ctx, key, newValue)

	// Handle special composite fields
	switch key {
	case "fullname":
		beforeStr = s.buildFullName(oldValue)
		afterStr = s.buildFullName(newValue)
	case "bank_account_name":
		beforeStr = s.buildBankAccountName(oldValue)
		afterStr = s.buildBankAccountName(newValue)
	}

	change := member_audit_log.CreateAuditFieldChange(title, beforeStr, afterStr)
	return &change, nil
}

// convertValueToString converts a value to string with join lookup for reference fields
func (s *memberAuditLogService) convertValueToString(ctx context.Context, key string, value interface{}) string {
	if value == nil {
		return ""
	}

	// Handle reference fields that need join lookups
	switch key {
	case "member_group_id":
		return s.getMemberGroupName(ctx, value)
	case "referral_group_id":
		return s.getReferralGroupName(ctx, value)
	case "commission_group_id":
		return s.getCommissionGroupName(ctx, value)
	case "bank_code":
		return s.getBankName(ctx, value)
	case "gender":
		return s.getGenderLabel(value)
	case "password":
		// Always mask password values for security
		return "xxxxxxxx"
	default:
		return s.interfaceToString(value)
	}
}

// Helper functions for join lookups
func (s *memberAuditLogService) getMemberGroupName(ctx context.Context, groupID interface{}) string {
	if groupID == nil {
		return ""
	}

	idStr := s.interfaceToString(groupID)
	if idStr == "" || idStr == "0" {
		return ""
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		s.logger.WithError(err).WithField("member_group_id", idStr).Warn("failed to convert group ID to int")
		return idStr // fallback to string ID
	}

	group, err := s.memberGroupRepo.GetByID(ctx, id)
	if err != nil || group == nil {
		s.logger.WithError(err).WithField("member_group_id", id).Warn("failed to get member group name")
		return idStr // fallback to string ID
	}

	return group.Name
}

func (s *memberAuditLogService) getReferralGroupName(ctx context.Context, groupID interface{}) string {
	if groupID == nil {
		return ""
	}

	idStr := s.interfaceToString(groupID)
	if idStr == "" || idStr == "0" {
		return ""
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		s.logger.WithError(err).WithField("referral_group_id", idStr).Warn("failed to convert group ID to int")
		return idStr // fallback to string ID
	}

	group, err := s.referralGroupRepo.GetByID(ctx, id)
	if err != nil || group == nil {
		s.logger.WithError(err).WithField("referral_group_id", id).Warn("failed to get referral group name")
		return idStr // fallback to string ID
	}

	return group.Name
}

func (s *memberAuditLogService) getCommissionGroupName(ctx context.Context, groupID interface{}) string {
	if groupID == nil {
		return ""
	}

	idStr := s.interfaceToString(groupID)
	if idStr == "" || idStr == "0" {
		return ""
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		s.logger.WithError(err).WithField("commission_group_id", idStr).Warn("failed to convert group ID to int")
		return idStr // fallback to string ID
	}

	group, err := s.commissionGroupRepo.GetByID(ctx, id)
	if err != nil || group == nil {
		s.logger.WithError(err).WithField("commission_group_id", id).Warn("failed to get commission group name")
		return idStr // fallback to string ID
	}

	return group.Name
}

func (s *memberAuditLogService) getBankName(ctx context.Context, bankCode interface{}) string {
	if bankCode == nil {
		return ""
	}

	code := s.interfaceToString(bankCode)
	if code == "" {
		return ""
	}

	// Note: Banking repository doesn't have GetByCode method, return code as fallback
	// TODO: Implement GetByCode method or use alternative approach to get bank name
	return code
}

// Helper functions for special formatting
func (s *memberAuditLogService) getGenderLabel(value interface{}) string {
	genderStr := s.interfaceToString(value)
	switch genderStr {
	case "male":
		return "ชาย"
	case "female":
		return "หญิง"
	case "other":
		return "อื่นๆ"
	default:
		return genderStr
	}
}

func (s *memberAuditLogService) buildFullName(value interface{}) string {
	if value == nil {
		return ""
	}

	// Handle if value is a map with first_name and last_name
	if valueMap, ok := value.(map[string]interface{}); ok {
		firstName := s.interfaceToString(valueMap["first_name"])
		lastName := s.interfaceToString(valueMap["last_name"])
		return firstName + " " + lastName
	}

	return s.interfaceToString(value)
}

func (s *memberAuditLogService) buildBankAccountName(value interface{}) string {
	if value == nil {
		return ""
	}

	// Handle if value is a map with bank account name fields
	if valueMap, ok := value.(map[string]interface{}); ok {
		firstName := s.interfaceToString(valueMap["bank_first_name"])
		lastName := s.interfaceToString(valueMap["bank_last_name"])
		if firstName != "" || lastName != "" {
			return firstName + " " + lastName
		}
	}

	return s.interfaceToString(value)
}

func (s *memberAuditLogService) interfaceToString(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case int:
		return fmt.Sprintf("%d", v)
	case int64:
		return fmt.Sprintf("%d", v)
	case float64:
		return fmt.Sprintf("%.2f", v)
	case bool:
		if v {
			return "true"
		}
		return "false"
	default:
		return fmt.Sprintf("%v", v)
	}
}

// Helper functions for creating audit log requests

// CreateMemberAuditLog creates an audit log for member creation
func CreateMemberAuditLog(memberID int, username string, changedBy int, changedByName string, newValues interface{}) member_audit_log.CreateAuditLogRequest {
	newValuesJSON := marshalToJSON(newValues)

	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionCreate,
		NewValues:     newValuesJSON,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// UpdateMemberAuditLog creates an audit log for member update
func UpdateMemberAuditLog(memberID int, username string, changedBy int, changedByName string, oldValues, newValues interface{}) member_audit_log.CreateAuditLogRequest {
	oldValuesJSON := marshalToJSON(oldValues)
	newValuesJSON := marshalToJSON(newValues)

	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionUpdate,
		OldValues:     oldValuesJSON,
		NewValues:     newValuesJSON,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// DeleteMemberAuditLog creates an audit log for member deletion
func DeleteMemberAuditLog(memberID int, username string, changedBy int, changedByName string, oldValues interface{}, actionRemark string) member_audit_log.CreateAuditLogRequest {
	// Include action_remark in newValues if provided
	var newValues interface{}
	if actionRemark != "" {
		newValues = map[string]interface{}{
			"action_remark": actionRemark,
		}
	}

	oldValuesJSON := marshalToJSON(oldValues)
	newValuesJSON := marshalToJSON(newValues)

	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionDelete,
		OldValues:     oldValuesJSON,
		NewValues:     newValuesJSON,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// ActivateMemberAuditLog creates an audit log for member activation
func ActivateMemberAuditLog(memberID int, username string, changedBy int, changedByName string) member_audit_log.CreateAuditLogRequest {
	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionActivate,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// DeactivateMemberAuditLog creates an audit log for member deactivation
func DeactivateMemberAuditLog(memberID int, username string, changedBy int, changedByName string) member_audit_log.CreateAuditLogRequest {
	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionDeactivate,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// SuspendMemberAuditLog creates an audit log for member suspension
func SuspendMemberAuditLog(memberID int, username string, changedBy int, changedByName string, actionRemark string) member_audit_log.CreateAuditLogRequest {
	// Include action_remark in newValues if provided
	var newValues interface{}
	if actionRemark != "" {
		newValues = map[string]interface{}{
			"action_remark": actionRemark,
		}
	}

	newValuesJSON := marshalToJSON(newValues)

	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionSuspend,
		NewValues:     newValuesJSON,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// PasswordChangeMemberAuditLog creates an audit log for member password change
func PasswordChangeMemberAuditLog(memberID int, username string, changedBy int, changedByName string) member_audit_log.CreateAuditLogRequest {
	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionPassword,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// GameCredentialsChangeMemberAuditLog creates an audit log for member game credentials change
func GameCredentialsChangeMemberAuditLog(memberID int, username string, changedBy int, changedByName string, oldValues, newValues interface{}) member_audit_log.CreateAuditLogRequest {
	oldValuesJSON := marshalToJSON(oldValues)
	newValuesJSON := marshalToJSON(newValues)

	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionGameCredentials,
		OldValues:     oldValuesJSON,
		NewValues:     newValuesJSON,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// ChangePartnerMemberAuditLog creates an audit log for member partner change
func ChangePartnerMemberAuditLog(memberID int, username string, changedBy int, changedByName string, oldValues, newValues interface{}) member_audit_log.CreateAuditLogRequest {
	oldValuesJSON := marshalToJSON(oldValues)
	newValuesJSON := marshalToJSON(newValues)
	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionChangePartner,
		OldValues:     oldValuesJSON,
		NewValues:     newValuesJSON,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// UpdateBankInfoMemberAuditLog creates an audit log for member bank info update
func UpdateBankInfoMemberAuditLog(memberID int, username string, changedBy int, changedByName string, oldValues, newValues interface{}) member_audit_log.CreateAuditLogRequest {
	oldValuesJSON := marshalToJSON(oldValues)
	newValuesJSON := marshalToJSON(newValues)
	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionUpdateBankInfo,
		OldValues:     oldValuesJSON,
		NewValues:     newValuesJSON,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// UnsuspendMemberAuditLog creates an audit log for member unsuspension
func UnsuspendMemberAuditLog(memberID int, username string, changedBy int, changedByName string, actionRemark string) member_audit_log.CreateAuditLogRequest {
	// Include action_remark in newValues if provided
	var newValues interface{}
	if actionRemark != "" {
		newValues = map[string]interface{}{
			"action_remark": actionRemark,
		}
	}

	newValuesJSON := marshalToJSON(newValues)

	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionUnsuspend,
		NewValues:     newValuesJSON,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// BalanceUpdateMemberAuditLog creates an audit log for member balance update
func BalanceUpdateMemberAuditLog(memberID int, username string, changedBy int, changedByName string, oldBalance, newBalance float64) member_audit_log.CreateAuditLogRequest {
	oldValues := map[string]interface{}{"balance": oldBalance}
	newValues := map[string]interface{}{"balance": newBalance}

	oldValuesJSON := marshalToJSON(oldValues)
	newValuesJSON := marshalToJSON(newValues)

	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionBalanceUpdate,
		OldValues:     oldValuesJSON,
		NewValues:     newValuesJSON,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}
