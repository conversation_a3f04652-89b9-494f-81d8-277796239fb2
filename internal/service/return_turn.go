package service

import (
	"blacking-api/internal/domain/return_turn"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/gameapi"
	"blacking-api/pkg/logger"
	"context"
	"fmt"
	"strings"
	"time"
)

// ReturnTurnService defines the interface for return turn service operations
type ReturnTurnService interface {
	// Settings
	GetReturnSetting(ctx context.Context) (*return_turn.ReturnTurnSettingResponse, error)
	UpdateReturnSetting(ctx context.Context, req return_turn.ReturnTurnSettingUpdateRequest) error

	// Reference Data
	GetReturnTurnCutTypes(ctx context.Context) ([]return_turn.ReturnTurnCutType, error)
	GetReturnTurnLoserTypes(ctx context.Context) ([]return_turn.ReturnTurnLoserType, error)
	GetCalculatePlayTypes(ctx context.Context) ([]return_turn.CalculatePlayType, error)

	// User Operations
	GetUserCurrentReturnDetail(ctx context.Context, userId int64) (*return_turn.ReturnTurnUserDetail, error)
	GetUserCurrentReturnDetailByCode(ctx context.Context, memberCode string) (*return_turn.ReturnTurnUserDetail, error)
	TakeUserReturnAmount(ctx context.Context, userId int64) error
	TakeUserReturnAmountByCode(ctx context.Context, memberCode string) error
	GetUserReturnHistoryList(ctx context.Context, req return_turn.ReturnTurnTransactionListRequest) (*return_turn.SuccessWithPagination, error)
	GetUserReturnHistoryListByCode(ctx context.Context, memberCode string, req return_turn.ReturnTurnTransactionListRequest) (*return_turn.SuccessWithPagination, error)

	// Reports
	GetReturnHistoryMemberList(ctx context.Context, req return_turn.ReturnTurnHistoryUserListRequest) (*return_turn.SuccessWithPagination, error)
	GetReturnHistoryMemberSummary(ctx context.Context, req return_turn.ReturnTurnHistoryUserListRequest) (*return_turn.ReturnTurnHistoryUserSummaryResponse, error)
	GetReturnHistoryLogList(ctx context.Context, req return_turn.ReturnTurnHistoryListRequest) (*return_turn.SuccessWithPagination, error)

	// Customer Promotion Management
	GetCustomerPromotionList(ctx context.Context, req return_turn.CustomerPromotionListRequest) (*return_turn.SuccessWithPagination, error)
	CancelCustomerPromotion(ctx context.Context, req return_turn.CustomerPromotionCancelRequest) error

	// Cron Jobs
	CronCutReturnLossDaily(ctx context.Context) error
	CronCutReturnLossByDate(ctx context.Context, ofDate string) error
}

type returnTurnService struct {
	returnTurnRepo interfaces.ReturnTurnRepository
	logger         logger.Logger
	settingCache   *return_turn.ReturnTurnSettingResponse
	cacheExpiredAt time.Time
	gameAPIClient  *gameapi.Client
	gameUpline     string
}

// NewReturnTurnService creates a new return turn service
func NewReturnTurnService(
	returnTurnRepo interfaces.ReturnTurnRepository,
	logger logger.Logger,
	gameBaseURL string,
	gameUpline string,
	gameWinLossMemberInfoEndpoint string,
	gameUserDepositEndpoint string,
) ReturnTurnService {
	// Initialize game API client
	gameAPIClient := gameapi.NewClient(gameBaseURL, gameUpline, gameWinLossMemberInfoEndpoint, gameUserDepositEndpoint)

	return &returnTurnService{
		returnTurnRepo: returnTurnRepo,
		logger:         logger,
		gameAPIClient:  gameAPIClient,
		gameUpline:     gameUpline,
	}
}

// GetReturnSetting retrieves the current promotion return setting with caching
func (s *returnTurnService) GetReturnSetting(ctx context.Context) (*return_turn.ReturnTurnSettingResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetReturnSetting")

	// Check cache
	if s.settingCache != nil && time.Now().Before(s.cacheExpiredAt) {
		return s.settingCache, nil
	}

	// Get from database
	setting, err := s.returnTurnRepo.GetReturnSetting(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get return setting")
		return nil, err
	}

	// Update cache
	s.settingCache = setting
	s.cacheExpiredAt = time.Now().Add(60 * time.Minute)

	return setting, nil
}

// UpdateReturnSetting updates the promotion return setting
func (s *returnTurnService) UpdateReturnSetting(ctx context.Context, req return_turn.ReturnTurnSettingUpdateRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateReturnSetting")

	// Update setting
	err := s.returnTurnRepo.UpdateReturnSetting(ctx, 1, req)
	if err != nil {
		log.WithError(err).Error("failed to update return setting")
		return err
	}

	// Clear cache
	s.settingCache = nil

	log.Info("promotion return setting updated successfully")
	return nil
}

// GetUserCurrentReturnDetail retrieves current return detail for a user
func (s *returnTurnService) GetUserCurrentReturnDetail(ctx context.Context, userId int64) (*return_turn.ReturnTurnUserDetail, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetUserCurrentReturnDetail").WithField("userId", userId)

	// Get setting
	setting, err := s.GetReturnSetting(ctx)
	if err != nil {
		return nil, err
	}

	// Get current transactions
	transactions, err := s.returnTurnRepo.GetCurrentReturnTransactionList(ctx, userId)
	if err != nil {
		log.WithError(err).Error("failed to get current return transactions")
		return nil, err
	}

	// Calculate total return price
	var totalReturnPrice float64
	var statusId int64 = return_turn.RETURN_TURN_STATUS_PENDING
	var statusName string = "PENDING"

	if len(transactions) > 0 {
		for _, t := range transactions {
			totalReturnPrice += t.ReturnPriceLiveCasino + t.ReturnPriceSlot + t.ReturnPriceSport
		}
		statusId = transactions[0].StatusID
		statusName = transactions[0].StatusName
	}

	detail := &return_turn.ReturnTurnUserDetail{
		MemberCode:      "", // We don't have member code when using user ID
		StatusID:        statusId,
		StatusName:      statusName,
		ReturnPercent:   setting.ReturnPercent,
		ReturnPrice:     totalReturnPrice,
		Detail:          setting.Detail,
		RelatedItemList: transactions,
	}

	return detail, nil
}

// GetUserCurrentReturnDetailByCode retrieves current return detail for a user by member code
func (s *returnTurnService) GetUserCurrentReturnDetailByCode(ctx context.Context, memberCode string) (*return_turn.ReturnTurnUserDetail, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetUserCurrentReturnDetailByCode").WithField("memberCode", memberCode)

	// Get setting
	setting, err := s.GetReturnSetting(ctx)
	if err != nil {
		return nil, err
	}

	// Get current transactions by member code
	transactions, err := s.returnTurnRepo.GetCurrentReturnTransactionListByCode(ctx, memberCode)
	if err != nil {
		log.WithError(err).Error("failed to get current return transactions by code")
		return nil, err
	}

	// Calculate total return price
	var totalReturnPrice float64
	var statusId int64 = return_turn.RETURN_TURN_STATUS_PENDING
	var statusName string = "PENDING"

	if len(transactions) > 0 {
		for _, t := range transactions {
			totalReturnPrice += t.ReturnPriceLiveCasino + t.ReturnPriceSlot + t.ReturnPriceSport
		}
		statusId = transactions[0].StatusID
		statusName = transactions[0].StatusName
	}

	detail := &return_turn.ReturnTurnUserDetail{
		MemberCode:      memberCode,
		StatusID:        statusId,
		StatusName:      statusName,
		ReturnPercent:   setting.ReturnPercent,
		ReturnPrice:     totalReturnPrice,
		Detail:          setting.Detail,
		RelatedItemList: transactions,
	}

	return detail, nil
}

// TakeUserReturnAmount processes user taking the return amount
func (s *returnTurnService) TakeUserReturnAmount(ctx context.Context, userId int64) error {
	log := s.logger.WithContext(ctx).WithField("operation", "TakeUserReturnAmount").WithField("userId", userId)

	// Get current transactions
	transactions, err := s.returnTurnRepo.GetCurrentReturnTransactionList(ctx, userId)
	if err != nil {
		log.WithError(err).Error("failed to get current return transactions")
		return err
	}

	if len(transactions) == 0 {
		return errors.NewValidationError("no return amount available")
	}

	// Calculate total return amount
	var totalReturnAmount float64
	var transactionIds []int64

	for _, t := range transactions {
		if t.StatusID != return_turn.RETURN_TURN_STATUS_READY {
			continue
		}
		totalReturnAmount += t.ReturnPriceLiveCasino + t.ReturnPriceSlot + t.ReturnPriceSport
		transactionIds = append(transactionIds, t.ID)
	}

	if totalReturnAmount <= 0 {
		return errors.NewValidationError("no return amount available to take")
	}

	// Begin transaction
	tx, err := s.returnTurnRepo.BeginTx(ctx)
	if err != nil {
		log.WithError(err).Error("failed to begin transaction")
		return errors.NewDatabaseError("failed to begin transaction")
	}
	defer tx.Rollback(ctx)

	// Increase user credit
	creditRequest := return_turn.UserTransactionCreateRequest{
		MemberID:                userId,
		UserTransactionTypeID:   10, // Return loss type
		UserTransactionStatusID: 2,  // Success
		Amount:                  totalReturnAmount,
		Detail:                  fmt.Sprintf("คืนยอดเสีย %.2f บาท", totalReturnAmount),
		CreatedByAdminID:        0, // System
	}

	_, err = s.returnTurnRepo.IncreaseUserCredit(ctx, tx, creditRequest)
	if err != nil {
		log.WithError(err).Error("failed to increase user credit")
		return err
	}

	// Update transaction status
	now := time.Now()
	for _, id := range transactionIds {
		// Find the corresponding transaction to get individual return prices
		var targetTransaction return_turn.ReturnTurnLoserResponse
		for _, t := range transactions {
			if t.ID == id {
				targetTransaction = t
				break
			}
		}

		updateBody := return_turn.ReturnTurnLoserUpdateBody{
			StatusID:             return_turn.RETURN_TURN_STATUS_TAKEN,
			TakeAt:               &now,
			TakenPriceLiveCasino: targetTransaction.ReturnPriceLiveCasino,
			TakenPriceSlot:       targetTransaction.ReturnPriceSlot,
			TakenPriceSport:      targetTransaction.ReturnPriceSport,
			UpdatedAt:            now,
		}

		err = s.returnTurnRepo.UpdateTakeReturnTransaction(ctx, id, updateBody)
		if err != nil {
			log.WithError(err).Error("failed to update transaction status")
			return err
		}
	}

	// Commit transaction
	err = tx.Commit(ctx)
	if err != nil {
		log.WithError(err).Error("failed to commit transaction")
		return errors.NewDatabaseError("failed to commit transaction")
	}

	log.WithField("amount", totalReturnAmount).Info("user took return amount successfully")
	return nil
}

// TakeUserReturnAmountByCode processes user taking the return amount using member code
func (s *returnTurnService) TakeUserReturnAmountByCode(ctx context.Context, memberCode string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "TakeUserReturnAmountByCode").WithField("memberCode", memberCode)

	// Get current transactions by member code
	transactions, err := s.returnTurnRepo.GetCurrentReturnTransactionListByCode(ctx, memberCode)
	if err != nil {
		log.WithError(err).Error("failed to get current return transactions by code")
		return err
	}

	if len(transactions) == 0 {
		return errors.NewValidationError("no return amount available")
	}

	// Calculate total return amount
	var totalReturnAmount float64
	var transactionIds []int64

	for _, t := range transactions {
		if t.StatusID != return_turn.RETURN_TURN_STATUS_READY {
			continue
		}
		totalReturnAmount += t.ReturnPriceLiveCasino + t.ReturnPriceSlot + t.ReturnPriceSport
		transactionIds = append(transactionIds, t.ID)
	}

	if totalReturnAmount <= 0 {
		return errors.NewValidationError("no return amount available to take")
	}

	// Begin transaction
	tx, err := s.returnTurnRepo.BeginTx(ctx)
	if err != nil {
		log.WithError(err).Error("failed to begin transaction")
		return errors.NewDatabaseError("failed to begin transaction")
	}
	defer tx.Rollback(ctx)

	// Call external API to deposit credit to user
	depositReq := gameapi.UserDepositRequest{
		Username:    memberCode,
		Amount:      totalReturnAmount,
		ReferenceID: "**********", // Empty reference ID as specified
	}

	depositResp, err := s.gameAPIClient.UserDeposit(ctx, depositReq)
	if err != nil {
		log.WithError(err).Error("failed to deposit credit via external API")
		return errors.NewExternalAPIError("failed to deposit credit to user account")
	}

	log.WithFields(map[string]interface{}{
		"txnID":        depositResp.Data.TxnID,
		"walletBefore": depositResp.Data.WalletBefore,
		"walletAfter":  depositResp.Data.WalletAfter,
		"amount":       depositResp.Data.Amount,
	}).Info("credit deposited successfully via external API")

	// Update transaction status
	now := time.Now()
	for _, id := range transactionIds {
		// Find the corresponding transaction to get individual return prices
		var targetTransaction return_turn.ReturnTurnLoserResponse
		for _, t := range transactions {
			if t.ID == id {
				targetTransaction = t
				break
			}
		}

		updateBody := return_turn.ReturnTurnLoserUpdateBody{
			StatusID:             return_turn.RETURN_TURN_STATUS_TAKEN,
			TakeAt:               &now,
			TakenPriceLiveCasino: targetTransaction.ReturnPriceLiveCasino,
			TakenPriceSlot:       targetTransaction.ReturnPriceSlot,
			TakenPriceSport:      targetTransaction.ReturnPriceSport,
			UpdatedAt:            now,
		}

		err = s.returnTurnRepo.UpdateTakeReturnTransaction(ctx, id, updateBody)
		if err != nil {
			log.WithError(err).Error("failed to update transaction status")
			return err
		}
	}
	// Commit transaction
	err = tx.Commit(ctx)
	if err != nil {
		log.WithError(err).Error("failed to commit transaction")
		return errors.NewDatabaseError("failed to commit transaction")
	}

	log.WithField("amount", totalReturnAmount).WithField("memberCode", memberCode).Info("user took return amount successfully by code")
	return nil
}

// GetUserReturnHistoryList retrieves user's return history
func (s *returnTurnService) GetUserReturnHistoryList(ctx context.Context, req return_turn.ReturnTurnTransactionListRequest) (*return_turn.SuccessWithPagination, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetUserReturnHistoryList")

	list, total, err := s.returnTurnRepo.GetReturnTransactionList(ctx, req)
	if err != nil {
		log.WithError(err).Error("failed to get user return history")
		return nil, err
	}

	return &return_turn.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

// GetUserReturnHistoryListByCode retrieves user's return history by member code
func (s *returnTurnService) GetUserReturnHistoryListByCode(ctx context.Context, memberCode string, req return_turn.ReturnTurnTransactionListRequest) (*return_turn.SuccessWithPagination, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetUserReturnHistoryListByCode").WithField("memberCode", memberCode)

	// Set default status to TAKEN for history list
	if req.StatusID == nil {
		statusID := return_turn.RETURN_TURN_STATUS_TAKEN
		req.StatusID = &statusID
	}

	list, total, err := s.returnTurnRepo.GetReturnTransactionListByCode(ctx, memberCode, req)
	if err != nil {
		log.WithError(err).Error("failed to get user return history by code")
		return nil, err
	}

	return &return_turn.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

// GetReturnHistoryMemberList retrieves return history member list for admin
func (s *returnTurnService) GetReturnHistoryMemberList(ctx context.Context, req return_turn.ReturnTurnHistoryUserListRequest) (*return_turn.SuccessWithPagination, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetReturnHistoryMemberList")

	list, total, err := s.returnTurnRepo.GetReturnHistoryMemberList(ctx, req)
	if err != nil {
		log.WithError(err).Error("failed to get return history member list")
		return nil, err
	}

	return &return_turn.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

// GetReturnHistoryMemberSummary retrieves return history summary
func (s *returnTurnService) GetReturnHistoryMemberSummary(ctx context.Context, req return_turn.ReturnTurnHistoryUserListRequest) (*return_turn.ReturnTurnHistoryUserSummaryResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetReturnHistoryMemberSummary")

	summary, err := s.returnTurnRepo.GetReturnHistoryMemberSummary(ctx, req)
	if err != nil {
		log.WithError(err).Error("failed to get return history summary")
		return nil, err
	}

	return summary, nil
}

// GetReturnHistoryLogList retrieves return history log list
func (s *returnTurnService) GetReturnHistoryLogList(ctx context.Context, req return_turn.ReturnTurnHistoryListRequest) (*return_turn.SuccessWithPagination, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetReturnHistoryLogList")

	list, total, err := s.returnTurnRepo.GetReturnHistoryLogList(ctx, req)
	if err != nil {
		log.WithError(err).Error("failed to get return history log list")
		return nil, err
	}

	return &return_turn.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

// GetCustomerPromotionList retrieves customer promotion list
func (s *returnTurnService) GetCustomerPromotionList(ctx context.Context, req return_turn.CustomerPromotionListRequest) (*return_turn.SuccessWithPagination, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetCustomerPromotionList")

	list, total, err := s.returnTurnRepo.GetCustomerPromotionList(ctx, req)
	if err != nil {
		log.WithError(err).Error("failed to get customer promotion list")
		return nil, err
	}

	return &return_turn.SuccessWithPagination{
		List:  list,
		Total: total,
	}, nil
}

// CancelCustomerPromotion cancels a customer promotion
func (s *returnTurnService) CancelCustomerPromotion(ctx context.Context, req return_turn.CustomerPromotionCancelRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "CancelCustomerPromotion")

	err := s.returnTurnRepo.CancelCustomerPromotion(ctx, req.StatementID, 0)
	if err != nil {
		log.WithError(err).Error("failed to cancel customer promotion")
		return err
	}

	log.WithField("statementId", req.StatementID).Info("customer promotion canceled successfully")
	return nil
}

// CronCutReturnLossDaily processes daily return loss calculation
func (s *returnTurnService) CronCutReturnLossDaily(ctx context.Context) error {
	// Get yesterday's date
	yesterday := time.Now().AddDate(0, 0, 0).Format("2006-01-02")
	return s.CronCutReturnLossByDate(ctx, yesterday)
}

// CronCutReturnLossByDate processes return loss calculation for specific date
func (s *returnTurnService) CronCutReturnLossByDate(ctx context.Context, ofDate string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "CronCutReturnLossByDate").WithField("ofDate", ofDate)

	log.Info("CronCutReturnLossByDate started")

	// Get setting
	setting, err := s.GetReturnSetting(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get return setting")
		return err
	}

	log.WithFields(map[string]interface{}{
		"isEnabled":     setting.IsEnabled,
		"minLossPrice":  setting.MinLossPrice,
		"returnPercent": setting.ReturnPercent,
		"cutTypeID":     setting.CutTypeID,
	}).Info("retrieved return setting")

	if !setting.IsEnabled {
		log.Info("promotion return is disabled, skipping")
		return nil
	}

	// Get play log data from external API
	var playLogs []return_turn.PlaylogTotalAmount

	// Calculate date range based on cut type
	var startDate, endDate string
	if setting.CutTypeID == return_turn.RETURN_TURN_CUT_TYPE_DAILY {
		// For daily, get data for the specific date
		startDate = ofDate + " 00:00:00"
		endDate = ofDate + " 23:59:59"
	} else if setting.CutTypeID == return_turn.RETURN_TURN_CUT_TYPE_WEEKLY {
		// For weekly, get data for 7 days before the date
		date, _ := time.Parse("2006-01-02", ofDate)
		weekBefore := date.AddDate(0, 0, -6)
		startDate = weekBefore.Format("2006-01-02") + " 00:00:00"
		endDate = ofDate + " 23:59:59"
	}

	// Call external API to get win/loss data
	playLogs, err = s.getWinLossDataFromAPI(ctx, startDate, endDate)
	if err != nil {
		log.WithError(err).Error("failed to get win/loss data from API")
		return err
	}

	log.WithField("userCount", len(playLogs)).Info("processing return loss for users")

	// Process each user
	for _, playLog := range playLogs {
		// Calculate losses separately by game type based on selected game types
		var lossLiveCasino, lossSlot, lossSport float64
		var gameDetails []string

		for _, typeId := range setting.CalculateTypes {
			switch typeId {
			case return_turn.CALCULATE_PLAY_TYPE_LIVE_CASINO:
				if playLog.TotalLossLiveCasino > 0 {
					lossLiveCasino = playLog.TotalLossLiveCasino
					gameDetails = append(gameDetails, "Live-Casino")
				}
			case return_turn.CALCULATE_PLAY_TYPE_SLOT:
				if playLog.TotalLossSlot > 0 {
					lossSlot = playLog.TotalLossSlot
					gameDetails = append(gameDetails, "Slot")
				}
			case return_turn.CALCULATE_PLAY_TYPE_SPORT:
				if playLog.TotalLossSport > 0 {
					lossSport = playLog.TotalLossSport
					gameDetails = append(gameDetails, "Sport")
				}
			}
		}

		// Calculate total loss for minimum check
		totalLoss := lossLiveCasino + lossSlot + lossSport

		// Check minimum loss
		if totalLoss < setting.MinLossPrice {
			log.WithFields(map[string]interface{}{
				"username":     playLog.MemberUsername,
				"totalLoss":    totalLoss,
				"minLossPrice": setting.MinLossPrice,
			}).Info("skipped: totalLoss below minimum")
			continue
		}

		// Calculate return amounts separately by game type (only for games with losses)
		var returnAmountLiveCasino, returnAmountSlot, returnAmountSport float64

		if lossLiveCasino > 0 {
			returnAmountLiveCasino = lossLiveCasino * (setting.ReturnPercent / 100)
		}
		if lossSlot > 0 {
			returnAmountSlot = lossSlot * (setting.ReturnPercent / 100)
		}
		if lossSport > 0 {
			returnAmountSport = lossSport * (setting.ReturnPercent / 100)
		}

		// Apply max return price limit to total return amount
		totalReturnAmount := returnAmountLiveCasino + returnAmountSlot + returnAmountSport
		if totalReturnAmount > setting.MaxReturnPrice {
			// Scale down proportionally only for games that have return amounts
			scale := setting.MaxReturnPrice / totalReturnAmount

			if returnAmountLiveCasino > 0 {
				returnAmountLiveCasino *= scale
			}
			if returnAmountSlot > 0 {
				returnAmountSlot *= scale
			}
			if returnAmountSport > 0 {
				returnAmountSport *= scale
			}
			// Recalculate total after scaling
			totalReturnAmount = returnAmountLiveCasino + returnAmountSlot + returnAmountSport
		}

		// Create daily key using username
		dailyKey := fmt.Sprintf("%s_%s", playLog.MemberUsername, ofDate)

		// Check if already processed
		existing, err := s.returnTurnRepo.GetReturnTransactionByDailyKey(ctx, dailyKey)
		if err != nil {
			log.WithError(err).WithField("dailyKey", dailyKey).Error("failed to check existing transaction")
			continue
		}

		if existing != nil {
			log.WithField("dailyKey", dailyKey).Debug("transaction already exists, skipping")
			continue
		}

		// Create new transaction
		createBody := return_turn.ReturnTurnLoserCreateBody{
			MemberCode:            playLog.MemberUsername, // Use username as member code
			StatusID:              return_turn.RETURN_TURN_STATUS_PENDING,
			DailyKey:              dailyKey,
			OfDate:                ofDate,
			TotalLossAmount:       totalLoss,
			TotalLossLiveCasino:   lossLiveCasino,
			TotalLossSlot:         lossSlot,
			TotalLossSport:        lossSport,
			ReturnPercent:         setting.ReturnPercent,
			GameDetail:            strings.Join(gameDetails, ", "),
			ReturnTypeID:          setting.ReturnTypeID,
			CutTypeID:             setting.CutTypeID,
			MinLossPrice:          setting.MinLossPrice,
			MaxReturnPrice:        setting.MaxReturnPrice,
			ReturnPriceLiveCasino: returnAmountLiveCasino,
			ReturnPriceSlot:       returnAmountSlot,
			ReturnPriceSport:      returnAmountSport,
		}

		transactionId, err := s.returnTurnRepo.CreateReturnTransaction(ctx, createBody)
		if err != nil {
			log.WithError(err).WithField("memberCode", playLog.MemberUsername).Error("failed to create return transaction")
			continue
		}

		log.WithField("transactionId", *transactionId).WithField("memberCode", playLog.MemberUsername).Info("return transaction created successfully")

		// Update to READY status with separate return prices by game type
		calcBody := return_turn.ReturnTurnLoserCalcBody{
			StatusID:              return_turn.RETURN_TURN_STATUS_READY,
			ReturnPriceLiveCasino: returnAmountLiveCasino,
			ReturnPriceSlot:       returnAmountSlot,
			ReturnPriceSport:      returnAmountSport,
			CalcAt:                time.Now(),
		}

		err = s.returnTurnRepo.UpdateCalcReturnTransaction(ctx, *transactionId, calcBody)
		if err != nil {
			log.WithError(err).WithField("transactionId", *transactionId).Error("failed to update transaction to ready")
		}

		log.WithField("memberCode", playLog.MemberUsername).WithField("totalReturnAmount", totalReturnAmount).Info("return loss calculated")
	}

	// Process expired transactions
	s.processExpiredTransactions(ctx)

	log.Info("daily return loss calculation completed")
	return nil
}

// processExpiredTransactions marks expired transactions
func (s *returnTurnService) processExpiredTransactions(ctx context.Context) {
	log := s.logger.WithContext(ctx).WithField("operation", "processExpiredTransactions")

	// Get expired transactions (7 days old)
	expiredTransactions, err := s.returnTurnRepo.GetExpiredReturnTransactions(ctx, 7)
	if err != nil {
		log.WithError(err).Error("failed to get expired transactions")
		return
	}

	for _, t := range expiredTransactions {
		updateBody := return_turn.ReturnTurnLoserUpdateBody{
			StatusID:  return_turn.RETURN_TURN_STATUS_EXPIRED,
			UpdatedAt: time.Now(),
		}

		err = s.returnTurnRepo.UpdateExpiredReturnTransaction(ctx, t.ID, updateBody)
		if err != nil {
			log.WithError(err).WithField("transactionId", t.ID).Error("failed to update expired transaction")
		}
	}

	if len(expiredTransactions) > 0 {
		log.WithField("count", len(expiredTransactions)).Info("expired transactions processed")
	}
}

// GetReturnTurnCutTypes retrieves all return turn cut types
func (s *returnTurnService) GetReturnTurnCutTypes(ctx context.Context) ([]return_turn.ReturnTurnCutType, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetReturnTurnCutTypes")

	types, err := s.returnTurnRepo.GetReturnTurnCutTypes(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get return turn cut types")
		return nil, err
	}

	return types, nil
}

// GetReturnTurnLoserTypes retrieves all return turn loser types
func (s *returnTurnService) GetReturnTurnLoserTypes(ctx context.Context) ([]return_turn.ReturnTurnLoserType, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetReturnTurnLoserTypes")

	types, err := s.returnTurnRepo.GetReturnTurnLoserTypes(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get return turn loser types")
		return nil, err
	}

	return types, nil
}

// GetCalculatePlayTypes retrieves all calculate play types
func (s *returnTurnService) GetCalculatePlayTypes(ctx context.Context) ([]return_turn.CalculatePlayType, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetCalculatePlayTypes")

	types, err := s.returnTurnRepo.GetCalculatePlayTypes(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get calculate play types")
		return nil, err
	}

	return types, nil
}

// getWinLossDataFromAPI fetches win/loss data from external API and converts to PlaylogTotalAmount
func (s *returnTurnService) getWinLossDataFromAPI(ctx context.Context, startDate, endDate string) ([]return_turn.PlaylogTotalAmount, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "getWinLossDataFromAPI")

	// Use configured upline
	upline := s.gameUpline

	// First check if there's any data
	checkReq := gameapi.WinLossRequest{
		StartTime: startDate,
		EndTime:   endDate,
		Upline:    upline,
		Page:      0,
		Limit:     1,
	}

	// Call API to check data availability
	checkResp, err := s.gameAPIClient.GetWinLossMemberInfo(ctx, checkReq)
	if err != nil {
		log.WithError(err).Error("failed to check win/loss API")
		return nil, fmt.Errorf("failed to check win/loss data: %w", err)
	}

	// If no data, return empty
	if checkResp.Data.Meta.Total == 0 {
		log.Info("no win/loss data available from API")
		return []return_turn.PlaylogTotalAmount{}, nil
	}

	// Calculate how many pages we need to fetch
	totalPages := checkResp.Data.Meta.TotalPage
	log.WithField("totalPages", totalPages).WithField("totalRecords", checkResp.Data.Meta.Total).Info("fetching win/loss data")

	// Collect all data from all pages
	allData := make([]gameapi.WinLossItem, 0)

	for page := 0; page < totalPages; page++ {
		req := gameapi.WinLossRequest{
			StartTime: startDate,
			EndTime:   endDate,
			Upline:    upline,
			Page:      page,
			Limit:     100, // Fetch 100 records per page
		}

		resp, err := s.gameAPIClient.GetWinLossMemberInfo(ctx, req)
		if err != nil {
			log.WithError(err).WithField("page", page).Error("failed to fetch win/loss page")
			continue
		}

		allData = append(allData, resp.Data.Data...)
		log.WithField("page", page+1).WithField("fetched", len(resp.Data.Data)).Debug("fetched page data")
	}

	// Group data by username and calculate totals
	userSummaries := make(map[string]*return_turn.PlaylogTotalAmount)

	for _, item := range allData {
		// Skip non-game transactions (deposits, withdrawals)
		if item.ActionType == "deposit" || item.ActionType == "withdraw" {
			continue
		}

		// Get or create user summary
		if _, exists := userSummaries[item.Username]; !exists {
			userSummaries[item.Username] = &return_turn.PlaylogTotalAmount{
				MemberCode:          item.Username, // Store username as member code
				MemberUsername:      item.Username, // MemberUsername is same as MemberCode
				TotalLossAmount:     0,
				TotalLossLiveCasino: 0,
				TotalLossSlot:       0,
				TotalLossSport:      0,
			}
		}

		summary := userSummaries[item.Username]

		// Only count losses (negative bet_winloss)
		if item.BetWinloss < 0 {
			lossAmount := -item.BetWinloss // Convert to positive for loss amount

			// Add to total loss
			summary.TotalLossAmount += lossAmount

			// Categorize by game type
			switch item.GameCategory {
			case "Slot":
				summary.TotalLossSlot += lossAmount
			case "Live Casino", "LiveCasino":
				summary.TotalLossLiveCasino += lossAmount
			case "Sport", "Sports":
				summary.TotalLossSport += lossAmount
			}
		}
	}

	// Convert map to slice and set member IDs from usernames
	var playLogs []return_turn.PlaylogTotalAmount

	// Sort usernames for consistent ordering
	usernames := make([]string, 0, len(userSummaries))
	for username := range userSummaries {
		usernames = append(usernames, username)
	}

	// Sort usernames alphabetically
	for i := 0; i < len(usernames); i++ {
		for j := i + 1; j < len(usernames); j++ {
			if usernames[i] > usernames[j] {
				usernames[i], usernames[j] = usernames[j], usernames[i]
			}
		}
	}

	// Store usernames in PlaylogTotalAmount
	for _, username := range usernames {
		summary := userSummaries[username]
		// Store username in MemberUsername field
		summary.MemberUsername = username
		playLogs = append(playLogs, *summary)

		// Log the username for debugging
		log.WithField("username", username).WithField("totalLoss", summary.TotalLossAmount).Debug("processed user loss data")
	}

	log.WithField("totalUsers", len(playLogs)).Info("processed win/loss data from API")
	return playLogs, nil
}

// checkAPIHasData checks if the API has any data for the given date range
func (s *returnTurnService) checkAPIHasData(ctx context.Context, startDate, endDate string) (bool, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "checkAPIHasData")

	// Use configured upline
	upline := s.gameUpline

	// Prepare request - just get first page with limit 1 to check if data exists
	req := gameapi.WinLossRequest{
		StartTime: startDate,
		EndTime:   endDate,
		Upline:    upline,
		Page:      0,
		Limit:     1, // Only need to check meta.total
	}

	// Call API
	resp, err := s.gameAPIClient.GetWinLossMemberInfo(ctx, req)
	if err != nil {
		log.WithError(err).Error("failed to call win/loss API for data check")
		return false, fmt.Errorf("failed to check win/loss data: %w", err)
	}

	// Check meta.total to see if there's any data
	hasData := resp.Data.Meta.Total > 0

	log.WithField("hasData", hasData).WithField("total", resp.Data.Meta.Total).Info("checked API for data")
	return hasData, nil
}
