package service

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"blacking-api/internal/domain/summary_report"
	"blacking-api/pkg/logger"
)

type SummaryReportService struct {
	logger   logger.Logger
	dataPath string
}

func NewSummaryReportService(logger logger.Logger) *SummaryReportService {
	return &SummaryReportService{
		logger:   logger,
		dataPath: "test/testdata/summary-report",
	}
}

// GetSummaryReports โหลดข้อมูลรายงานสรุปหน้าหลัก
func (s *SummaryReportService) GetSummaryReports(ctx context.Context, dateFrom, dateTo, category string) (*summary_report.SummaryReport, error) {
	// โหลดข้อมูลจาก JSON file
	data, err := s.loadSummaryDataFromFile()
	if err != nil {
		s.logger.WithError(err).Error("failed to load summary data from file")
		// fallback to mock data
		mockData := s.generateMockSummaryData(dateFrom, dateTo, category)
		return &summary_report.SummaryReport{
			Data: mockData,
		}, nil
	}

	// โหลดข้อมูล providers และ join
	providers, err := s.loadProvidersFromFile()
	if err != nil {
		s.logger.WithError(err).Error("failed to load providers data from file")
	} else {
		data = s.joinProviderData(data, providers)
	}

	return &summary_report.SummaryReport{
		Data: data,
	}, nil
}

// GetSummaryReportDetail โหลดข้อมูลรายละเอียดรายงานสรุป
func (s *SummaryReportService) GetSummaryReportDetail(ctx context.Context, id int, dateFrom, dateTo string) (*summary_report.SummaryReportDetail, error) {
	// โหลดข้อมูลจาก JSON file
	data, err := s.loadSummaryDetailDataFromFile()
	if err != nil {
		s.logger.WithError(err).Error("failed to load summary detail data from file")
		// fallback to mock data
		mockDetail := s.generateMockDetailData(id, dateFrom, dateTo)
		return &mockDetail, nil
	}

	// โหลดข้อมูล providers และ join
	providers, err := s.loadProvidersFromFile()
	if err != nil {
		s.logger.WithError(err).Error("failed to load providers data from file")
	} else {
		data = s.joinProviderDataForDetail(data, providers)
	}

	return &summary_report.SummaryReportDetail{
		Data: data,
	}, nil
}

// loadSummaryDataFromFile โหลดข้อมูลจาก JSON file
func (s *SummaryReportService) loadSummaryDataFromFile() ([]summary_report.SummaryReportItem, error) {
	filePath := filepath.Join(s.dataPath, "summary_report.json")

	// อ่านไฟล์ JSON
	fileData, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file %s: %w", filePath, err)
	}

	// Parse JSON structure ที่มี nested response
	var jsonData struct {
		Response struct {
			Data []summary_report.SummaryReportItem `json:"data"`
		} `json:"response"`
	}

	err = json.Unmarshal(fileData, &jsonData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	return jsonData.Response.Data, nil
}

// loadSummaryDetailDataFromFile โหลดข้อมูล detail จาก JSON file
func (s *SummaryReportService) loadSummaryDetailDataFromFile() ([]summary_report.SummaryReportDetailItem, error) {
	filePath := filepath.Join(s.dataPath, "summary_report_detail.json")

	// อ่านไฟล์ JSON
	fileData, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file %s: %w", filePath, err)
	}

	// Parse JSON structure ที่มี nested response
	var jsonData struct {
		Response struct {
			Data []summary_report.SummaryReportDetailItem `json:"data"`
		} `json:"response"`
	}

	err = json.Unmarshal(fileData, &jsonData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	return jsonData.Response.Data, nil
}

// generateMockSummaryData สร้างข้อมูล mock สำหรับหน้าหลัก (fallback)
func (s *SummaryReportService) generateMockSummaryData(dateFrom, dateTo, category string) []summary_report.SummaryReportItem {
	// fallback mock data ตามโครงสร้างใหม่
	return []summary_report.SummaryReportItem{
		{
			ID: struct {
				GamingProviderCode string `json:"gaming_provider_code"`
				GameTypeID         int    `json:"game_type_id"`
			}{
				GamingProviderCode: "PGT",
				GameTypeID:         2,
			},
			CommissionAmount:         155.64,
			CommissionNetTurnover:    73854,
			CountUser:                22,
			NetTurnOver:              73854,
			TurnOver:                 73854,
			Jackpot:                  0,
			WinLoseNotIncludeJackpot: -4094.25,
			WinLose:                  -4094.25,
		},
		{
			ID: struct {
				GamingProviderCode string `json:"gaming_provider_code"`
				GameTypeID         int    `json:"game_type_id"`
			}{
				GamingProviderCode: "CQ",
				GameTypeID:         2,
			},
			CommissionAmount:         91.26,
			CommissionNetTurnover:    45630,
			CountUser:                1,
			NetTurnOver:              45630,
			TurnOver:                 45630,
			Jackpot:                  0,
			WinLoseNotIncludeJackpot: -3200,
			WinLose:                  -3200,
		},
	}
}

// generateMockDetailData สร้างข้อมูล mock สำหรับรายละเอียด (fallback)
func (s *SummaryReportService) generateMockDetailData(id int, dateFrom, dateTo string) summary_report.SummaryReportDetail {
	details := []summary_report.SummaryReportDetailItem{
		{
			ID: struct {
				GamingProviderCode string `json:"gaming_provider_code"`
				GameCode           string `json:"game_code"`
				GameName           string `json:"game_name"`
			}{
				GamingProviderCode: "CQ",
				GameCode:           "196",
				GameName:           "Tenfold Eggs",
			},
			TurnOver:                 45630,
			NetTurnOver:              45630,
			Jackpot:                  0,
			WinLoseNotIncludeJackpot: -3200,
			WinLose:                  -3200,
			CommissionNetTurnover:    45630,
			CommissionAmount:         91.26,
		},
	}

	return summary_report.SummaryReportDetail{
		Data: details,
	}
}

// loadProvidersFromFile โหลดข้อมูล providers จาก JSON file
func (s *SummaryReportService) loadProvidersFromFile() ([]summary_report.Provider, error) {
	filePath := filepath.Join("test/testdata/dashboard", "provider.json")

	// อ่านไฟล์ JSON
	fileData, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file %s: %w", filePath, err)
	}

	// Parse JSON structure
	var jsonData struct {
		Data struct {
			Providers []summary_report.Provider `json:"providers"`
		} `json:"data"`
	}

	err = json.Unmarshal(fileData, &jsonData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	return jsonData.Data.Providers, nil
}

// joinProviderData join ข้อมูล provider กับ summary report data
func (s *SummaryReportService) joinProviderData(data []summary_report.SummaryReportItem, providers []summary_report.Provider) []summary_report.SummaryReportItem {
	// สร้าง map สำหรับ lookup providers ด้วย code
	providerMap := make(map[string]*summary_report.Provider)
	for i := range providers {
		providerMap[providers[i].Code] = &providers[i]
	}

	// join ข้อมูล provider เข้ากับแต่ละ item
	for i := range data {
		if provider, found := providerMap[data[i].ID.GamingProviderCode]; found {
			data[i].Provider = provider
		}
	}

	return data
}

// joinProviderDataForDetail join ข้อมูล provider กับ summary report detail data
func (s *SummaryReportService) joinProviderDataForDetail(data []summary_report.SummaryReportDetailItem, providers []summary_report.Provider) []summary_report.SummaryReportDetailItem {
	// สร้าง map สำหรับ lookup providers ด้วย code
	providerMap := make(map[string]*summary_report.Provider)
	for i := range providers {
		providerMap[providers[i].Code] = &providers[i]
	}

	// join ข้อมูล provider เข้ากับแต่ละ item
	for i := range data {
		if provider, found := providerMap[data[i].ID.GamingProviderCode]; found {
			data[i].Provider = provider
		}
	}

	return data
}
