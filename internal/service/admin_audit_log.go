package service

import (
	"blacking-api/internal/domain/admin_audit_log"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/auth"
	"blacking-api/pkg/logger"
	"context"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// AdminAuditLogService defines the interface for admin audit log service operations
type AdminAuditLogService interface {
	// LogAdminAction logs an admin action to audit log
	LogAdminAction(ctx context.Context, req admin_audit_log.CreateAdminAuditLogRequest) error

	// LogAdminActionFromGin logs an admin action from gin context automatically
	LogAdminActionFromGin(c *gin.Context, responseStatus int) error

	// ListAuditLogs retrieves audit logs with pagination and filters
	ListAuditLogs(ctx context.Context, limit, offset int, filter *admin_audit_log.AdminAuditLogFilter) ([]*admin_audit_log.AdminAuditLogResponse, error)

	// GetAuditLogsCount returns the total count of audit logs with filters
	GetAuditLogsCount(ctx context.Context, filter *admin_audit_log.AdminAuditLogFilter) (int64, error)

	// CleanupOldEntries deletes old audit log entries
	CleanupOldEntries(ctx context.Context, daysToKeep int) (int64, error)
}

type adminAuditLogService struct {
	auditLogRepo interfaces.AdminAuditLogRepository
	logger       logger.Logger
}

// NewAdminAuditLogService creates a new admin audit log service
func NewAdminAuditLogService(auditLogRepo interfaces.AdminAuditLogRepository, logger logger.Logger) AdminAuditLogService {
	return &adminAuditLogService{
		auditLogRepo: auditLogRepo,
		logger:       logger,
	}
}

// LogAdminAction logs an admin action to audit log
func (s *adminAuditLogService) LogAdminAction(ctx context.Context, req admin_audit_log.CreateAdminAuditLogRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "LogAdminAction")

	// Create new audit log
	auditLog, err := admin_audit_log.NewAdminAuditLog(req)
	if err != nil {
		log.WithError(err).Error("failed to create admin audit log domain object")
		return err
	}

	// Save to repository
	if err := s.auditLogRepo.Create(ctx, auditLog); err != nil {
		log.WithError(err).Error("failed to save admin audit log")
		return err
	}

	log.WithField("audit_log_id", auditLog.ID).Info("admin audit log created successfully")
	return nil
}

// LogAdminActionFromGin logs an admin action from gin context automatically
func (s *adminAuditLogService) LogAdminActionFromGin(c *gin.Context, responseStatus int) error {
	log := s.logger.WithContext(c.Request.Context()).WithField("operation", "LogAdminActionFromGin")

	// Extract admin information from context
	userID := auth.GetUserIDFromContext(c)
	username := auth.GetUsernameFromContext(c)

	// Skip logging if no valid admin user found
	if userID == 0 || username == "" || username == "system" {
		log.Debug("skipping audit log - no valid admin user found")
		return nil
	}

	// Create timeout context for database operation (5 seconds)
	// Use background context to avoid inheritance of canceled parent context
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	log.WithFields(map[string]interface{}{
		"user_id":       userID,
		"username":      username,
		"method":        c.Request.Method,
		"path":          c.Request.URL.Path,
		"timeout":       "5s",
		"original_ctx_err": c.Request.Context().Err(),
	}).Debug("starting admin audit log creation")

	// Get request information
	method := c.Request.Method
	path := c.Request.URL.Path
	if c.Request.URL.RawQuery != "" {
		path += "?" + c.Request.URL.RawQuery
	}

	// Get IP address
	ipAddress := c.ClientIP()
	
	// Get User Agent
	userAgent := c.Request.UserAgent()

	// Get request body if available
	var requestBody *string
	if bodyBytes, exists := c.Get("request_body"); exists {
		if bodyStr, ok := bodyBytes.(string); ok && bodyStr != "" {
			requestBody = &bodyStr
		}
	}

	// Create audit log request
	auditReq := admin_audit_log.CreateAdminAuditLogRequest{
		UserID:         userID,
		Username:       username,
		Method:         method,
		Path:           path,
		RequestBody:    requestBody,
		ResponseStatus: responseStatus,
		IPAddress:      &ipAddress,
		UserAgent:      &userAgent,
	}

	// Log the action with timeout context
	if err := s.LogAdminAction(ctx, auditReq); err != nil {
		log.WithFields(map[string]interface{}{
			"user_id":         userID,
			"username":        username,
			"method":          method,
			"path":            path,
			"response_status": responseStatus,
			"timeout_ctx_err": ctx.Err(),
			"original_ctx_err": c.Request.Context().Err(),
		}).WithError(err).Warn("failed to log admin action with detailed context")
		// Don't return error to avoid disrupting the main request flow
		return nil
	}

	log.WithFields(map[string]interface{}{
		"user_id":         userID,
		"username":        username,
		"method":          method,
		"path":            path,
		"response_status": responseStatus,
	}).Debug("admin audit log completed successfully")

	return nil
}

// ListAuditLogs retrieves audit logs with pagination and filters
func (s *adminAuditLogService) ListAuditLogs(ctx context.Context, limit, offset int, filter *admin_audit_log.AdminAuditLogFilter) ([]*admin_audit_log.AdminAuditLogResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListAuditLogs")

	// Validate pagination parameters
	if limit <= 0 {
		limit = 50 // default limit
	}
	if limit > 1000 {
		limit = 1000 // max limit
	}
	if offset < 0 {
		offset = 0
	}

	// Get audit logs from repository
	auditLogs, err := s.auditLogRepo.List(ctx, limit, offset, filter)
	if err != nil {
		log.WithError(err).Error("failed to get audit logs from repository")
		return nil, err
	}

	// Convert to response format
	var responses []*admin_audit_log.AdminAuditLogResponse
	for _, auditLog := range auditLogs {
		response := auditLog.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("count", len(responses)).Info("admin audit logs retrieved successfully")
	return responses, nil
}

// GetAuditLogsCount returns the total count of audit logs with filters
func (s *adminAuditLogService) GetAuditLogsCount(ctx context.Context, filter *admin_audit_log.AdminAuditLogFilter) (int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetAuditLogsCount")

	count, err := s.auditLogRepo.Count(ctx, filter)
	if err != nil {
		log.WithError(err).Error("failed to get audit logs count from repository")
		return 0, err
	}

	log.WithField("count", count).Info("admin audit logs count retrieved successfully")
	return count, nil
}

// CleanupOldEntries deletes old audit log entries
func (s *adminAuditLogService) CleanupOldEntries(ctx context.Context, daysToKeep int) (int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CleanupOldEntries").WithField("days_to_keep", daysToKeep)

	if daysToKeep <= 0 {
		daysToKeep = 90 // Default to 90 days
	}

	deletedCount, err := s.auditLogRepo.DeleteOldEntries(ctx, daysToKeep)
	if err != nil {
		log.WithError(err).Error("failed to cleanup old audit log entries")
		return 0, err
	}

	log.WithField("deleted_count", deletedCount).Info("old admin audit log entries cleaned up successfully")
	return deletedCount, nil
}

// Helper functions for creating audit log requests

// CreateAdminAuditLog creates an audit log request for admin API operations
func CreateAdminAuditLog(userID int, username, method, path string, requestBody *string, responseStatus int, ipAddress, userAgent *string) admin_audit_log.CreateAdminAuditLogRequest {
	return admin_audit_log.CreateAdminAuditLogRequest{
		UserID:         userID,
		Username:       username,
		Method:         method,
		Path:           path,
		RequestBody:    requestBody,
		ResponseStatus: responseStatus,
		IPAddress:      ipAddress,
		UserAgent:      userAgent,
	}
}

// CreateAdminAuditLogFromGin creates an audit log request from gin context
func CreateAdminAuditLogFromGin(c *gin.Context, responseStatus int) admin_audit_log.CreateAdminAuditLogRequest {
	userID := auth.GetUserIDFromContext(c)
	username := auth.GetUsernameFromContext(c)
	method := c.Request.Method
	path := c.Request.URL.Path
	
	if c.Request.URL.RawQuery != "" {
		path += "?" + c.Request.URL.RawQuery
	}

	// Get request body if available
	var requestBody *string
	if bodyBytes, exists := c.Get("request_body"); exists {
		if bodyStr, ok := bodyBytes.(string); ok && bodyStr != "" {
			requestBody = &bodyStr
		}
	}

	ipAddress := c.ClientIP()
	userAgent := c.Request.UserAgent()

	return admin_audit_log.CreateAdminAuditLogRequest{
		UserID:         userID,
		Username:       username,
		Method:         method,
		Path:           path,
		RequestBody:    requestBody,
		ResponseStatus: responseStatus,
		IPAddress:      &ipAddress,
		UserAgent:      &userAgent,
	}
}

// ShouldAuditPath checks if a path should be audited based on the specified endpoints
func ShouldAuditPath(method, path string) bool {
	// Define the paths that should be audited as specified
	// All paths verified as admin-only routes with proper authentication middleware
	auditPaths := []struct {
		methods []string
		pattern string
	}{
		// Channels
		{[]string{"POST"}, "/api/v1/channels"},
		{[]string{"DELETE"}, "/api/v1/channels/"},
		
		// Allowed IPs
		{[]string{"POST"}, "/api/v1/allowed-ips/sync"},
		
		// User Roles
		{[]string{"POST"}, "/api/v1/user-roles/"},
		{[]string{"DELETE"}, "/api/v1/user-roles/"},
		{[]string{"PATCH"}, "/api/v1/user-roles/reorder"},
		
		// Permissions
		{[]string{"PUT"}, "/api/v1/permissions/user-roles/"},
		
		// System Settings
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/system/settings/"},
		
		// Banners
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/banners"},
		
		// Members
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/members"},
		
		// Member Groups
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/member-groups"},
		
		// Member Group Types
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/member-group-types"},
		
		// Commission Groups
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/commission-groups"},
		
		// Referral Groups
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/referral-groups"},
		
		// Partners
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/partners"},
		
		// Payment Methods (✓ Admin-only route)
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/payment-method"},
		
		// Banking Accounts (✓ All admin-only routes)
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/deposit-account"},
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/withdraw-account"},
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/holding-account"},
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/payment-gateway-account"},
		
		// SMS & Communication (✓ Admin-only routes - no middleware in communication.go but likely handled at parent level)
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/sms-provider"},
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/contact"},
		
		// Promotions (✓ Admin-only routes with RequireAdminRole middleware)
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/promotion-web"},
		
		// Lock Credit (⚠️ WARNING: Admin middleware is commented out in promotion_web.go:87)
		// Currently only has JWT auth but missing RequireAdminRole() - needs to be fixed
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/lock-credit"},
		
		// Admin Transactions (✓ Admin-only routes with RequireAdminRole middleware)
		{[]string{"POST", "PUT", "DELETE"}, "/api/v1/transaction/admin"},
	}

	for _, auditPath := range auditPaths {
		// Check if method matches
		methodMatches := false
		for _, m := range auditPath.methods {
			if m == method {
				methodMatches = true
				break
			}
		}

		if !methodMatches {
			continue
		}

		// Check if path matches (simple prefix or exact match)
		if matchesPath(path, auditPath.pattern) {
			return true
		}
	}

	return false
}

// matchesPath checks if a request path matches an audit pattern
func matchesPath(requestPath, pattern string) bool {
	// Exact match
	if requestPath == pattern {
		return true
	}

	// Prefix match (for patterns ending with /)
	if len(pattern) > 0 && pattern[len(pattern)-1] == '/' {
		return len(requestPath) > len(pattern) && requestPath[:len(pattern)] == pattern
	}

	// Pattern match for dynamic segments (basic implementation)
	// For more complex patterns, consider using a proper router matcher
	if containsPathParam(pattern) {
		return matchesWithParams(requestPath, pattern)
	}

	return false
}

// containsPathParam checks if pattern contains path parameters
func containsPathParam(pattern string) bool {
	// Simple check for common dynamic patterns
	return len(pattern) > 0 && (pattern[len(pattern)-1] == '/' || containsNumber(pattern))
}

// containsNumber checks if the pattern suggests a numeric parameter
func containsNumber(pattern string) bool {
	// This is a simple heuristic - in production, use proper pattern matching
	parts := splitPath(pattern)
	for _, part := range parts {
		if part == "user-roles" || part == "permissions" || part == "settings" {
			return true
		}
	}
	return false
}

// matchesWithParams matches paths with dynamic parameters
func matchesWithParams(requestPath, pattern string) bool {
	requestParts := splitPath(requestPath)
	patternParts := splitPath(pattern)

	if len(requestParts) < len(patternParts) {
		return false
	}

	for i, patternPart := range patternParts {
		if i >= len(requestParts) {
			return false
		}
		
		requestPart := requestParts[i]
		
		// Check for exact matches or numeric parameters
		if patternPart != requestPart && !isNumeric(requestPart) {
			return false
		}
	}

	return true
}

// splitPath splits a path into parts
func splitPath(path string) []string {
	if path == "" || path == "/" {
		return []string{}
	}
	
	// Remove leading and trailing slashes
	if path[0] == '/' {
		path = path[1:]
	}
	if len(path) > 0 && path[len(path)-1] == '/' {
		path = path[:len(path)-1]
	}
	
	if path == "" {
		return []string{}
	}
	
	parts := []string{}
	current := ""
	for _, char := range path {
		if char == '/' {
			if current != "" {
				parts = append(parts, current)
				current = ""
			}
		} else {
			current += string(char)
		}
	}
	if current != "" {
		parts = append(parts, current)
	}
	
	return parts
}

// isNumeric checks if a string represents a number
func isNumeric(s string) bool {
	_, err := strconv.Atoi(s)
	return err == nil
}