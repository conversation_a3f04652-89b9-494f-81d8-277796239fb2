package service

import (
	"blacking-api/internal/domain/promotion_web"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"net/http"
)

type PromotionWebService interface {
	// Promotion Web CRUD operations
	CreatePromotionWeb(ctx context.Context, req promotion_web.PromotionWebCreateRequest) (int64, error)
	GetPromotionWebList(ctx context.Context, req promotion_web.PromotionWebGetListRequest) ([]promotion_web.PromotionWebGetListResponse, int64, error)
	GetPromotionWebById(ctx context.Context, id int64) (*promotion_web.PromotionWebGetByIdResponse, error)
	UpdatePromotionWeb(ctx context.Context, req promotion_web.PromotionWebUpdateRequest) error
	DeletePromotionWeb(ctx context.Context, req promotion_web.DeletePromotionWebRequest) error
	CancelPromotionWeb(ctx context.Context, req promotion_web.CancelPromotionWebRequest) error

	// User promotion operations
	CollectPromotion(ctx context.Context, req promotion_web.CollectPromotionRequest) (int64, error)
	GetUserPromotions(ctx context.Context, userID int64) ([]promotion_web.ShowPromotionForUserResponse, error)
	GetUserPromotionWebList(ctx context.Context, req promotion_web.PromotionWebUserGetListRequest) ([]promotion_web.PromotionWebUserGetListResponse, int64, error)
	GetPromotionWebUserById(ctx context.Context, req promotion_web.GetPromotionWebUserById) (*promotion_web.GetPromotionWebUserByIdResponse, error)
	PromotionWebUserGetListByUserId(ctx context.Context, req promotion_web.PromotionWebUserGetListByUserIdRequest) ([]promotion_web.PromotionWebUserGetListByUserIdResponse, int64, error)
	CancelPromotionWebUserById(ctx context.Context, req promotion_web.CancelPromotionWebUserById) error

	// Option/lookup operations
	GetPromotionTypes(ctx context.Context) ([]promotion_web.PromotionWebTypeResponse, error)
	GetPromotionStatuses(ctx context.Context) ([]promotion_web.PromotionWebStatusResponse, error)
	GetPromotionBonusConditions(ctx context.Context) ([]promotion_web.PromotionWebBonusConditionResponse, error)
	GetPromotionBonusTypes(ctx context.Context) ([]promotion_web.PromotionWebBonusTypeResponse, error)
	GetPromotionTurnoverTypes(ctx context.Context) ([]promotion_web.PromotionWebTurnoverTypeResponse, error)
	GetPromotionDateTypes(ctx context.Context) ([]promotion_web.PromotionWebDateTypeResponse, error)

	// Lock credit operations
	CreateLockCredit(ctx context.Context, req promotion_web.LockCreditPromotionCreateRequest) (int64, error)
	GetLockCreditWithdrawList(ctx context.Context, req promotion_web.GetLockCreditWithdrawListRequest) (*promotion_web.PaginatedResponse, error)
	UnlockCreditWithdraw(ctx context.Context, id int64, adminID int64) error
	CheckLockedCredit(ctx context.Context, userID int64) (bool, error)

	// Public operations
	GetPublicPromotions(ctx context.Context) ([]promotion_web.ShowPromotionForUserResponse, error)
	GetPublicPromotionByID(ctx context.Context, id int64) (*promotion_web.PromotionWebGetByIdResponse, error)

	// Business logic validation
	ValidatePromotionEligibility(ctx context.Context, userID, promotionID int64) error
	ProcessPromotionBonus(ctx context.Context, userPromotionID int64) error
	CheckTurnoverRequirements(ctx context.Context, userID int64, promotionID int64) (bool, error)

	// Legacy operations (for backward compatibility)
	GetPromotionWebUserToCancel(ctx context.Context, promotionWebId int64) ([]promotion_web.GetPromotionWebIdToCancel, error)
	GetUserPromotionWebByUserId(ctx context.Context, userId int64) (*promotion_web.PromotionWebUserByUserIdResponse, error)
	ExpiredPromotionWebUserByIds(ctx context.Context, req promotion_web.CancelPromotionWebUserByPromotionWebId) error
	PromotionConfirmUpdatePromotionWebUser(ctx context.Context, confirmId int64, promotionWebUserId int64) error

	// Utility operations
	GetExpiredPromotionWeb(ctx context.Context, today string) ([]promotion_web.PromotionWebExpired, error)
	PromotionWebGetSildeListOnlyActive(ctx context.Context) ([]promotion_web.PromotionWebGetSildeListOnlyActive, error)
	UpdatePromotionWebPriorityOrderCreate(ctx context.Context, id int64) error
	SortPromotionWebPriorityOrder(ctx context.Context, req promotion_web.DragSortRequest) error

	// File upload operations
	UploadImageToCloudflare(ctx context.Context, fileBody *http.Request) (string, error)
	UploadPromotionCover(ctx context.Context, fileBody *http.Request) (*promotion_web.FileUploadResponse, error)

	// Additional business operations from migration document
	GetPromotionSlideList(ctx context.Context) ([]promotion_web.PromotionSlideResponse, error)
	SortPromotionPriorityOrder(ctx context.Context, req promotion_web.DragSortRequest, adminID int64) error
	GetPromotionSummary(ctx context.Context, req promotion_web.PromotionSummaryRequest) (*promotion_web.PromotionSummaryResponse, error)
	GetUserPromotionSummary(ctx context.Context, req promotion_web.UserPromotionSummaryRequest) (*promotion_web.UserPromotionSummaryResponse, error)
	GetPromotionByHiddenURL(ctx context.Context, userID int64, hiddenURL string) (*promotion_web.GetPromotionByHiddenURLResponse, error)
	GetUserTurnoverSummary(ctx context.Context, userPromotionID int64) (*promotion_web.UserTurnoverSummaryResponse, error)
	CheckUserPromotionForDeposit(ctx context.Context, req promotion_web.CheckUserPromotionRequest) (string, error)
	CheckPromotionWithdraw(ctx context.Context, req promotion_web.CheckPromotionWithdrawRequest) (*promotion_web.CheckPromotionWithdrawResponse, error)
	GetUserCollectedPromotions(ctx context.Context, userID int64, req promotion_web.GetUserCollectedPromotionsRequest) (*promotion_web.PaginatedResponse, error)
	GetUserPromotionByID(ctx context.Context, userID int64, promotionID int64) (*promotion_web.GetPromotionByHiddenURLResponse, error)
}

type promotionWebService struct {
	promotionWebRepo  interfaces.PromotionWebRepository
	fileUploadService FileUploadService
	awsS3Repo         interfaces.AWSS3Repository
	logger            logger.Logger
}

func NewPromotionWebService(promotionWebRepo interfaces.PromotionWebRepository, fileUploadService FileUploadService, awsS3Repo interfaces.AWSS3Repository, logger logger.Logger) PromotionWebService {
	return &promotionWebService{
		promotionWebRepo:  promotionWebRepo,
		fileUploadService: fileUploadService,
		awsS3Repo:         awsS3Repo,
		logger:            logger,
	}
}

// CreatePromotionWeb creates a new promotion web
func (s *promotionWebService) CreatePromotionWeb(ctx context.Context, req promotion_web.PromotionWebCreateRequest) (int64, error) {
	s.logger.WithField("name", req.Name).Info("creating promotion web")

	id, err := s.promotionWebRepo.CreatePromotionWeb(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to create promotion web")
		return 0, err
	}

	s.logger.WithField("id", id).Info("promotion web created successfully")
	return id, nil
}

// GetPromotionWebList retrieves a list of promotion webs with pagination and filtering
func (s *promotionWebService) GetPromotionWebList(ctx context.Context, req promotion_web.PromotionWebGetListRequest) ([]promotion_web.PromotionWebGetListResponse, int64, error) {
	s.logger.WithField("page", req.Page).WithField("limit", req.Limit).Info("getting promotion web list")

	list, total, err := s.promotionWebRepo.GetPromotionWebList(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to get promotion web list")
		return nil, 0, err
	}

	s.logger.WithField("total", total).WithField("count", len(list)).Info("promotion web list retrieved successfully")
	return list, total, nil
}

// GetPromotionWebById retrieves a promotion web by ID
func (s *promotionWebService) GetPromotionWebById(ctx context.Context, id int64) (*promotion_web.PromotionWebGetByIdResponse, error) {
	s.logger.WithField("id", id).Info("getting promotion web by ID")

	result, err := s.promotionWebRepo.GetPromotionWebById(ctx, id)
	if err != nil {
		s.logger.WithError(err).WithField("id", id).Error("failed to get promotion web by ID")
		return nil, err
	}

	s.logger.WithField("id", id).Info("promotion web retrieved successfully")
	return result, nil
}

// UpdatePromotionWeb updates an existing promotion web
func (s *promotionWebService) UpdatePromotionWeb(ctx context.Context, req promotion_web.PromotionWebUpdateRequest) error {
	s.logger.WithField("id", req.Id).Info("updating promotion web")

	err := s.promotionWebRepo.UpdatePromotionWeb(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("id", req.Id).Error("failed to update promotion web")
		return err
	}

	s.logger.WithField("id", req.Id).Info("promotion web updated successfully")
	return nil
}

// DeletePromotionWeb soft deletes a promotion web
func (s *promotionWebService) DeletePromotionWeb(ctx context.Context, req promotion_web.DeletePromotionWebRequest) error {
	s.logger.WithField("id", req.Id).Info("deleting promotion web")

	err := s.promotionWebRepo.DeletePromotionWeb(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("id", req.Id).Error("failed to delete promotion web")
		return err
	}

	s.logger.WithField("id", req.Id).Info("promotion web deleted successfully")
	return nil
}

// CancelPromotionWeb cancels a promotion web
func (s *promotionWebService) CancelPromotionWeb(ctx context.Context, req promotion_web.CancelPromotionWebRequest) error {
	s.logger.WithField("id", req.Id).Info("canceling promotion web")

	err := s.promotionWebRepo.CancelPromotionWeb(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("id", req.Id).Error("failed to cancel promotion web")
		return err
	}

	s.logger.WithField("id", req.Id).Info("promotion web canceled successfully")
	return nil
}

// GetPromotionWebUserToCancel retrieves promotion web users to cancel
func (s *promotionWebService) GetPromotionWebUserToCancel(ctx context.Context, promotionWebId int64) ([]promotion_web.GetPromotionWebIdToCancel, error) {
	s.logger.WithField("promotion_web_id", promotionWebId).Info("getting promotion web users to cancel")

	result, err := s.promotionWebRepo.GetPromotionWebUserToCancel(ctx, promotionWebId)
	if err != nil {
		s.logger.WithError(err).WithField("promotion_web_id", promotionWebId).Error("failed to get promotion web users to cancel")
		return nil, err
	}

	s.logger.WithField("promotion_web_id", promotionWebId).WithField("count", len(result)).Info("promotion web users to cancel retrieved successfully")
	return result, nil
}

// GetUserPromotionWebByUserId retrieves user promotion web by user ID
func (s *promotionWebService) GetUserPromotionWebByUserId(ctx context.Context, userId int64) (*promotion_web.PromotionWebUserByUserIdResponse, error) {
	s.logger.WithField("user_id", userId).Info("getting user promotion web by user ID")

	result, err := s.promotionWebRepo.GetUserPromotionWebByUserId(ctx, userId)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userId).Error("failed to get user promotion web by user ID")
		return nil, err
	}

	s.logger.WithField("user_id", userId).Info("user promotion web retrieved successfully")
	return result, nil
}

// GetUserPromotionWebList retrieves a list of promotion web users
func (s *promotionWebService) GetUserPromotionWebList(ctx context.Context, req promotion_web.PromotionWebUserGetListRequest) ([]promotion_web.PromotionWebUserGetListResponse, int64, error) {
	s.logger.WithField("page", req.Page).WithField("limit", req.Limit).Info("getting user promotion web list")

	list, total, err := s.promotionWebRepo.GetUserPromotionWebList(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to get user promotion web list")
		return nil, 0, err
	}

	s.logger.WithField("total", total).WithField("count", len(list)).Info("user promotion web list retrieved successfully")
	return list, total, nil
}

// GetPromotionWebUserById retrieves a promotion web user by ID
func (s *promotionWebService) GetPromotionWebUserById(ctx context.Context, req promotion_web.GetPromotionWebUserById) (*promotion_web.GetPromotionWebUserByIdResponse, error) {
	s.logger.WithField("id", req.Id).Info("getting promotion web user by ID")

	result, err := s.promotionWebRepo.GetPromotionWebUserById(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("id", req.Id).Error("failed to get promotion web user by ID")
		return nil, err
	}

	s.logger.WithField("id", req.Id).Info("promotion web user retrieved successfully")
	return result, nil
}

// PromotionWebUserGetListByUserId retrieves promotion web users by user ID
func (s *promotionWebService) PromotionWebUserGetListByUserId(ctx context.Context, req promotion_web.PromotionWebUserGetListByUserIdRequest) ([]promotion_web.PromotionWebUserGetListByUserIdResponse, int64, error) {
	s.logger.WithField("user_id", req.UserId).WithField("page", req.Page).Info("getting promotion web user list by user ID")

	list, total, err := s.promotionWebRepo.PromotionWebUserGetListByUserId(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserId).Error("failed to get promotion web user list by user ID")
		return nil, 0, err
	}

	s.logger.WithField("user_id", req.UserId).WithField("total", total).WithField("count", len(list)).Info("promotion web user list by user ID retrieved successfully")
	return list, total, nil
}

// CancelPromotionWebUserById cancels a promotion web user by ID
func (s *promotionWebService) CancelPromotionWebUserById(ctx context.Context, req promotion_web.CancelPromotionWebUserById) error {
	s.logger.WithField("id", req.Id).Info("canceling promotion web user by ID")

	err := s.promotionWebRepo.CancelPromotionWebUserById(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("id", req.Id).Error("failed to cancel promotion web user by ID")
		return err
	}

	s.logger.WithField("id", req.Id).Info("promotion web user canceled successfully")
	return nil
}

// ExpiredPromotionWebUserByIds expires promotion web users by promotion web ID
func (s *promotionWebService) ExpiredPromotionWebUserByIds(ctx context.Context, req promotion_web.CancelPromotionWebUserByPromotionWebId) error {
	s.logger.WithField("promotion_web_id", req.PromotionWebId).Info("expiring promotion web users by promotion web ID")

	err := s.promotionWebRepo.ExpiredPromotionWebUserByIds(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("promotion_web_id", req.PromotionWebId).Error("failed to expire promotion web users by promotion web ID")
		return err
	}

	s.logger.WithField("promotion_web_id", req.PromotionWebId).Info("promotion web users expired successfully")
	return nil
}

// PromotionConfirmUpdatePromotionWebUser updates promotion web user confirmation
func (s *promotionWebService) PromotionConfirmUpdatePromotionWebUser(ctx context.Context, confirmId int64, promotionWebUserId int64) error {
	s.logger.WithField("confirm_id", confirmId).WithField("promotion_web_user_id", promotionWebUserId).Info("updating promotion web user confirmation")

	err := s.promotionWebRepo.PromotionConfirmUpdatePromotionWebUser(ctx, confirmId, promotionWebUserId)
	if err != nil {
		s.logger.WithError(err).WithField("confirm_id", confirmId).Error("failed to update promotion web user confirmation")
		return err
	}

	s.logger.WithField("confirm_id", confirmId).Info("promotion web user confirmation updated successfully")
	return nil
}

// GetExpiredPromotionWeb retrieves expired promotion webs
func (s *promotionWebService) GetExpiredPromotionWeb(ctx context.Context, today string) ([]promotion_web.PromotionWebExpired, error) {
	s.logger.WithField("date", today).Info("getting expired promotion webs")

	result, err := s.promotionWebRepo.GetExpiredPromotionWeb(ctx, today)
	if err != nil {
		s.logger.WithError(err).WithField("date", today).Error("failed to get expired promotion webs")
		return nil, err
	}

	s.logger.WithField("date", today).WithField("count", len(result)).Info("expired promotion webs retrieved successfully")
	return result, nil
}

// PromotionWebGetSildeListOnlyActive retrieves active promotion webs for slides
func (s *promotionWebService) PromotionWebGetSildeListOnlyActive(ctx context.Context) ([]promotion_web.PromotionWebGetSildeListOnlyActive, error) {
	s.logger.Info("getting active promotion web slide list")

	result, err := s.promotionWebRepo.PromotionWebGetSildeListOnlyActive(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get active promotion web slide list")
		return nil, err
	}

	s.logger.WithField("count", len(result)).Info("active promotion web slide list retrieved successfully")
	return result, nil
}

// UpdatePromotionWebPriorityOrderCreate updates promotion web priority order
func (s *promotionWebService) UpdatePromotionWebPriorityOrderCreate(ctx context.Context, id int64) error {
	s.logger.WithField("id", id).Info("updating promotion web priority order")

	err := s.promotionWebRepo.UpdatePromotionWebPriorityOrderCreate(ctx, id)
	if err != nil {
		s.logger.WithError(err).WithField("id", id).Error("failed to update promotion web priority order")
		return err
	}

	s.logger.WithField("id", id).Info("promotion web priority order updated successfully")
	return nil
}

// SortPromotionWebPriorityOrder sorts promotion web priority order
func (s *promotionWebService) SortPromotionWebPriorityOrder(ctx context.Context, req promotion_web.DragSortRequest) error {
	s.logger.WithField("from_id", req.FromItemID).WithField("to_id", req.ToItemID).Info("sorting promotion web priority order")

	err := s.promotionWebRepo.SortPromotionWebPriorityOrder(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("from_id", req.FromItemID).WithField("to_id", req.ToItemID).Error("failed to sort promotion web priority order")
		return err
	}

	s.logger.WithField("from_id", req.FromItemID).WithField("to_id", req.ToItemID).Info("promotion web priority order sorted successfully")
	return nil
}

// User promotion operations
func (s *promotionWebService) CollectPromotion(ctx context.Context, req promotion_web.CollectPromotionRequest) (int64, error) {
	s.logger.WithField("user_id", req.UserID).WithField("promotion_id", req.PromotionWebID).Info("collecting promotion")

	// Validate promotion eligibility
	err := s.ValidatePromotionEligibility(ctx, req.UserID, req.PromotionWebID)
	if err != nil {
		s.logger.WithError(err).Error("promotion eligibility validation failed")
		return 0, err
	}

	// Create user promotion
	id, err := s.promotionWebRepo.CreateUserPromotion(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to create user promotion")
		return 0, err
	}

	s.logger.WithField("id", id).Info("promotion collected successfully")
	return id, nil
}

func (s *promotionWebService) GetUserPromotions(ctx context.Context, userID int64) ([]promotion_web.ShowPromotionForUserResponse, error) {
	s.logger.WithField("user_id", userID).Info("getting user promotions")

	promotions, err := s.promotionWebRepo.GetUserPromotionsByUserID(ctx, userID)
	if err != nil {
		s.logger.WithError(err).Error("failed to get user promotions")
		return nil, err
	}

	s.logger.WithField("count", len(promotions)).Info("user promotions retrieved successfully")
	return promotions, nil
}

// Option/lookup operations
func (s *promotionWebService) GetPromotionTypes(ctx context.Context) ([]promotion_web.PromotionWebTypeResponse, error) {
	s.logger.Info("getting promotion types")

	types, err := s.promotionWebRepo.GetPromotionTypes(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get promotion types")
		return nil, err
	}

	s.logger.WithField("count", len(types)).Info("promotion types retrieved successfully")
	return types, nil
}

func (s *promotionWebService) GetPromotionStatuses(ctx context.Context) ([]promotion_web.PromotionWebStatusResponse, error) {
	s.logger.Info("getting promotion statuses")

	statuses, err := s.promotionWebRepo.GetPromotionStatuses(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get promotion statuses")
		return nil, err
	}

	s.logger.WithField("count", len(statuses)).Info("promotion statuses retrieved successfully")
	return statuses, nil
}

func (s *promotionWebService) GetPromotionBonusConditions(ctx context.Context) ([]promotion_web.PromotionWebBonusConditionResponse, error) {
	s.logger.Info("getting promotion bonus conditions")

	conditions, err := s.promotionWebRepo.GetPromotionBonusConditions(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get promotion bonus conditions")
		return nil, err
	}

	s.logger.WithField("count", len(conditions)).Info("promotion bonus conditions retrieved successfully")
	return conditions, nil
}

func (s *promotionWebService) GetPromotionBonusTypes(ctx context.Context) ([]promotion_web.PromotionWebBonusTypeResponse, error) {
	s.logger.Info("getting promotion bonus types")

	types, err := s.promotionWebRepo.GetPromotionBonusTypes(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get promotion bonus types")
		return nil, err
	}

	s.logger.WithField("count", len(types)).Info("promotion bonus types retrieved successfully")
	return types, nil
}

func (s *promotionWebService) GetPromotionTurnoverTypes(ctx context.Context) ([]promotion_web.PromotionWebTurnoverTypeResponse, error) {
	s.logger.Info("getting promotion turnover types")

	types, err := s.promotionWebRepo.GetPromotionTurnoverTypes(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get promotion turnover types")
		return nil, err
	}

	s.logger.WithField("count", len(types)).Info("promotion turnover types retrieved successfully")
	return types, nil
}

func (s *promotionWebService) GetPromotionDateTypes(ctx context.Context) ([]promotion_web.PromotionWebDateTypeResponse, error) {
	s.logger.Info("getting promotion date types")

	types, err := s.promotionWebRepo.GetPromotionDateTypes(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get promotion date types")
		return nil, err
	}

	s.logger.WithField("count", len(types)).Info("promotion date types retrieved successfully")
	return types, nil
}

// Lock credit operations
func (s *promotionWebService) CreateLockCredit(ctx context.Context, req promotion_web.LockCreditPromotionCreateRequest) (int64, error) {
	s.logger.WithField("user_id", req.UserID).WithField("promotion_id", req.PromotionID).Info("creating lock credit")

	id, err := s.promotionWebRepo.CreateLockCredit(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to create lock credit")
		return 0, err
	}

	s.logger.WithField("id", id).Info("lock credit created successfully")
	return id, nil
}

func (s *promotionWebService) GetLockCreditWithdrawList(ctx context.Context, req promotion_web.GetLockCreditWithdrawListRequest) (*promotion_web.PaginatedResponse, error) {
	s.logger.WithField("page", req.Page).WithField("limit", req.Limit).Info("getting lock credit withdraw list")

	list, total, err := s.promotionWebRepo.GetLockCreditWithdrawList(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to get lock credit withdraw list")
		return nil, err
	}

	response := &promotion_web.PaginatedResponse{
		List:  list,
		Total: total,
		Page:  req.Page,
		Limit: req.Limit,
	}

	s.logger.WithField("total", total).Info("lock credit withdraw list retrieved successfully")
	return response, nil
}

func (s *promotionWebService) UnlockCreditWithdraw(ctx context.Context, id int64, adminID int64) error {
	s.logger.WithField("id", id).WithField("admin_id", adminID).Info("unlocking credit withdraw")

	err := s.promotionWebRepo.UnlockCreditWithdraw(ctx, id, adminID)
	if err != nil {
		s.logger.WithError(err).Error("failed to unlock credit withdraw")
		return err
	}

	s.logger.WithField("id", id).Info("credit withdraw unlocked successfully")
	return nil
}

func (s *promotionWebService) CheckLockedCredit(ctx context.Context, userID int64) (bool, error) {
	s.logger.WithField("user_id", userID).Info("checking locked credit")

	isLocked, err := s.promotionWebRepo.CheckLockedCredit(ctx, userID)
	if err != nil {
		s.logger.WithError(err).Error("failed to check locked credit")
		return false, err
	}

	s.logger.WithField("is_locked", isLocked).Info("locked credit checked successfully")
	return isLocked, nil
}

// Public operations
func (s *promotionWebService) GetPublicPromotions(ctx context.Context) ([]promotion_web.ShowPromotionForUserResponse, error) {
	s.logger.Info("getting public promotions")

	promotions, err := s.promotionWebRepo.GetPublicPromotions(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get public promotions")
		return nil, err
	}

	s.logger.WithField("count", len(promotions)).Info("public promotions retrieved successfully")
	return promotions, nil
}

func (s *promotionWebService) GetPublicPromotionByID(ctx context.Context, id int64) (*promotion_web.PromotionWebGetByIdResponse, error) {
	s.logger.WithField("id", id).Info("getting public promotion by ID")

	promotion, err := s.promotionWebRepo.GetPublicPromotionByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to get public promotion by ID")
		return nil, err
	}

	s.logger.WithField("id", id).Info("public promotion retrieved successfully")
	return promotion, nil
}

// Business logic validation
func (s *promotionWebService) ValidatePromotionEligibility(ctx context.Context, userID, promotionID int64) error {
	s.logger.WithField("user_id", userID).WithField("promotion_id", promotionID).Info("validating promotion eligibility")

	err := s.promotionWebRepo.ValidatePromotionEligibility(ctx, userID, promotionID)
	if err != nil {
		s.logger.WithError(err).Error("promotion eligibility validation failed")
		return err
	}

	s.logger.Info("promotion eligibility validated successfully")
	return nil
}

func (s *promotionWebService) ProcessPromotionBonus(ctx context.Context, userPromotionID int64) error {
	s.logger.WithField("user_promotion_id", userPromotionID).Info("processing promotion bonus")

	// TODO: Implement bonus processing logic based on promotion type
	// This would involve calculating bonus amounts, updating user balances, etc.

	s.logger.WithField("user_promotion_id", userPromotionID).Info("promotion bonus processed successfully")
	return nil
}

func (s *promotionWebService) CheckTurnoverRequirements(ctx context.Context, userID int64, promotionID int64) (bool, error) {
	s.logger.WithField("user_id", userID).WithField("promotion_id", promotionID).Info("checking turnover requirements")

	// TODO: Implement turnover requirement checking logic
	// This would involve checking user's betting history against promotion requirements

	s.logger.Info("turnover requirements checked successfully")
	return true, nil
}

// Additional business operations from migration document
func (s *promotionWebService) GetPromotionSlideList(ctx context.Context) ([]promotion_web.PromotionSlideResponse, error) {
	s.logger.Info("getting promotion slide list")

	slides, err := s.promotionWebRepo.GetPromotionSlideList(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get promotion slide list")
		return nil, err
	}

	s.logger.WithField("count", len(slides)).Info("promotion slide list retrieved successfully")
	return slides, nil
}

func (s *promotionWebService) SortPromotionPriorityOrder(ctx context.Context, req promotion_web.DragSortRequest, adminID int64) error {
	s.logger.WithField("from_id", req.FromItemID).WithField("to_id", req.ToItemID).WithField("admin_id", adminID).Info("sorting promotion priority order")

	err := s.promotionWebRepo.SortPromotionWebPriorityOrder(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to sort promotion priority order")
		return err
	}

	s.logger.Info("promotion priority order sorted successfully")
	return nil
}

func (s *promotionWebService) GetPromotionSummary(ctx context.Context, req promotion_web.PromotionSummaryRequest) (*promotion_web.PromotionSummaryResponse, error) {
	s.logger.Info("getting promotion summary")

	summary, err := s.promotionWebRepo.GetPromotionSummary(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to get promotion summary")
		return nil, err
	}

	s.logger.Info("promotion summary retrieved successfully")
	return summary, nil
}

func (s *promotionWebService) GetUserPromotionSummary(ctx context.Context, req promotion_web.UserPromotionSummaryRequest) (*promotion_web.UserPromotionSummaryResponse, error) {
	s.logger.WithField("user_id", req.UserID).Info("getting user promotion summary")

	summary, err := s.promotionWebRepo.GetUserPromotionSummary(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to get user promotion summary")
		return nil, err
	}

	s.logger.Info("user promotion summary retrieved successfully")
	return summary, nil
}

func (s *promotionWebService) GetPromotionByHiddenURL(ctx context.Context, userID int64, hiddenURL string) (*promotion_web.GetPromotionByHiddenURLResponse, error) {
	s.logger.WithField("user_id", userID).WithField("hidden_url", hiddenURL).Info("getting promotion by hidden URL")

	promotion, err := s.promotionWebRepo.GetPromotionByHiddenURL(ctx, userID, hiddenURL)
	if err != nil {
		s.logger.WithError(err).Error("failed to get promotion by hidden URL")
		return nil, err
	}

	s.logger.Info("promotion by hidden URL retrieved successfully")
	return promotion, nil
}

func (s *promotionWebService) GetUserTurnoverSummary(ctx context.Context, userPromotionID int64) (*promotion_web.UserTurnoverSummaryResponse, error) {
	s.logger.WithField("user_promotion_id", userPromotionID).Info("getting user turnover summary")

	summary, err := s.promotionWebRepo.GetUserTurnoverSummary(ctx, userPromotionID)
	if err != nil {
		s.logger.WithError(err).Error("failed to get user turnover summary")
		return nil, err
	}

	s.logger.Info("user turnover summary retrieved successfully")
	return summary, nil
}

func (s *promotionWebService) CheckUserPromotionForDeposit(ctx context.Context, req promotion_web.CheckUserPromotionRequest) (string, error) {
	s.logger.WithField("user_id", req.UserID).WithField("promotion_id", req.PromotionID).WithField("amount", req.Amount).Info("checking user promotion for deposit")

	// Get promotion details
	promotion, err := s.promotionWebRepo.GetPromotionWebById(ctx, req.PromotionID)
	if err != nil {
		s.logger.WithError(err).Error("failed to get promotion details")
		return promotion_web.NOT_PASS_PROMOTION, err
	}

	// Check if promotion is active
	if promotion.PromotionWebStatusId != promotion_web.PROMOTION_WEB_STATUS_ACTIVE {
		s.logger.Info("promotion is not active")
		return promotion_web.NOT_PASS_PROMOTION, nil
	}

	// Check if user already has this promotion type
	hasPromotion, err := s.promotionWebRepo.CheckUserHasPromotionType(ctx, req.UserID, promotion.PromotionWebTypeId)
	if err != nil {
		s.logger.WithError(err).Error("failed to check user promotion type")
		return promotion_web.NOT_PASS_PROMOTION, err
	}

	if hasPromotion {
		s.logger.Info("user already has this promotion type")
		return promotion_web.NOT_PASS_PROMOTION, nil
	}

	// Additional business logic based on promotion type
	switch promotion.PromotionWebTypeId {
	case promotion_web.PROMOTION_WEB_TYPE_FIRST_DEPOSIT:
		// Check if this is user's first deposit
		isFirstDeposit, err := s.promotionWebRepo.CheckUserFirstDeposit(ctx, req.UserID)
		if err != nil {
			s.logger.WithError(err).Error("failed to check user first deposit")
			return promotion_web.NOT_PASS_PROMOTION, err
		}
		if !isFirstDeposit {
			s.logger.Info("not user's first deposit")
			return promotion_web.NOT_PASS_PROMOTION, nil
		}

	case promotion_web.PROMOTION_WEB_TYPE_DEPOSIT_MINIMUM_PER_DAY:
		// Check minimum amount requirement
		if req.Amount < promotion.BonusConditionAmount {
			s.logger.Info("deposit amount below minimum requirement")
			return promotion_web.NOT_PASS_PROMOTION, nil
		}
	}

	s.logger.Info("user promotion check passed")
	return promotion_web.PASS_PROMOTION, nil
}

func (s *promotionWebService) CheckPromotionWithdraw(ctx context.Context, req promotion_web.CheckPromotionWithdrawRequest) (*promotion_web.CheckPromotionWithdrawResponse, error) {
	s.logger.WithField("user_id", req.UserID).WithField("amount", req.Amount).Info("checking promotion withdraw")

	// Check if user has locked credit
	isLocked, err := s.promotionWebRepo.CheckLockedCredit(ctx, req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("failed to check locked credit")
		return nil, err
	}

	if isLocked {
		// Get locked amount details
		lockedAmount, err := s.promotionWebRepo.GetUserLockedAmount(ctx, req.UserID)
		if err != nil {
			s.logger.WithError(err).Error("failed to get locked amount")
			return nil, err
		}

		return &promotion_web.CheckPromotionWithdrawResponse{
			CanWithdraw:  false,
			LockedAmount: lockedAmount,
			Message:      "Cannot withdraw due to locked credit from active promotions",
		}, nil
	}

	return &promotion_web.CheckPromotionWithdrawResponse{
		CanWithdraw: true,
		Message:     "Withdrawal allowed",
	}, nil
}

func (s *promotionWebService) GetUserCollectedPromotions(ctx context.Context, userID int64, req promotion_web.GetUserCollectedPromotionsRequest) (*promotion_web.PaginatedResponse, error) {
	s.logger.WithField("user_id", userID).WithField("page", req.Page).WithField("limit", req.Limit).Info("getting user collected promotions")

	list, total, err := s.promotionWebRepo.GetUserCollectedPromotions(ctx, userID, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to get user collected promotions")
		return nil, err
	}

	response := &promotion_web.PaginatedResponse{
		List:  list,
		Total: total,
		Page:  req.Page,
		Limit: req.Limit,
	}

	s.logger.WithField("total", total).Info("user collected promotions retrieved successfully")
	return response, nil
}

func (s *promotionWebService) GetUserPromotionByID(ctx context.Context, userID int64, promotionID int64) (*promotion_web.GetPromotionByHiddenURLResponse, error) {
	s.logger.WithField("user_id", userID).WithField("promotion_id", promotionID).Info("getting user promotion by ID")

	promotion, err := s.promotionWebRepo.GetUserPromotionByID(ctx, userID, promotionID)
	if err != nil {
		s.logger.WithError(err).Error("failed to get user promotion by ID")
		return nil, err
	}

	s.logger.Info("user promotion by ID retrieved successfully")
	return promotion, nil
}

func (s *promotionWebService) UploadPromotionCover(ctx context.Context, fileBody *http.Request) (*promotion_web.FileUploadResponse, error) {
	fileReader, _, err := fileBody.FormFile("file")
	if err != nil {
		s.logger.WithError(err).Error("failed to read file from request")
		return nil, errors.NewValidationError("failed to read file from request")
	}

	pathName := "backoffice/promotion-web/"

	fileData, err := s.awsS3Repo.UploadFileToS3(ctx, pathName, fileReader)
	if err != nil {
		s.logger.WithError(err).Error("failed to upload file to S3")
		return nil, errors.NewValidationError("failed to upload file to S3")
	}

	fileUploadResponse := promotion_web.FileUploadResponse{
		URL: fileData.FileUrl,
	}

	return &fileUploadResponse, nil
}

// UploadImageToCloudflare uploads an image to Cloudflare
func (s *promotionWebService) UploadImageToCloudflare(ctx context.Context, fileBody *http.Request) (string, error) {
	// Get path_upload from form data
	pathUpload := fileBody.PostFormValue("path_upload")
	if pathUpload == "" {
		s.logger.Error("path_upload is required")
		return "", errors.NewValidationError("path_upload is required")
	}

	// Get file from request
	fileReader, header, err := fileBody.FormFile("file")
	if err != nil {
		s.logger.WithError(err).Error("failed to read file from request")
		return "", errors.NewValidationError("failed to read file from request")
	}
	defer fileReader.Close()

	s.logger.WithField("filename", header.Filename).WithField("path", pathUpload).Info("uploading image to Cloudflare")

	// Upload to Cloudflare via repository
	result, err := s.promotionWebRepo.UploadImageToCloudflare(ctx, pathUpload, header.Filename, fileReader)
	if err != nil {
		s.logger.WithError(err).WithField("filename", header.Filename).Error("failed to upload image to Cloudflare")
		return "", err
	}

	// Return the file URL or image ID
	if result.FileUrl != "" {
		s.logger.WithField("url", result.FileUrl).Info("image uploaded to Cloudflare successfully")
		return result.FileUrl, nil
	}

	s.logger.WithField("image_id", result.ImageId).Info("image uploaded to Cloudflare successfully")
	return result.ImageId, nil
}
