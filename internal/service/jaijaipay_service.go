package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"blacking-api/internal/config"
	"blacking-api/internal/domain/payment_gateway_transaction"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/agapi"
	"blacking-api/pkg/jaijaipay"
	"blacking-api/pkg/logger"
	"blacking-api/pkg/websocket"
)

// JaiJaiPayService handles business logic for JaiJaiPay API operations
type JaiJaiPayService struct {
	paymentGatewayService *PaymentGatewayService
	apiLogRepo            interfaces.JaiJaiPayAPILogRepository
	transactionRepo       interfaces.PaymentGatewayTransactionRepository
	memberService         MemberService
	config                *config.Config
	logger                logger.Logger
	wsClient              *websocket.WSClient
	agentClient           *agapi.Client
}

// NewJaiJaiPayService creates a new JaiJaiPay service instance
func NewJaiJaiPayService(
	paymentGatewayService *PaymentGatewayService,
	apiLogRepo interfaces.JaiJaiPayAPILogRepository,
	transactionRepo interfaces.PaymentGatewayTransactionRepository,
	memberService MemberService,
	config *config.Config,
	logger logger.Logger,
) *JaiJaiPayService {
	service := &JaiJaiPayService{
		paymentGatewayService: paymentGatewayService,
		apiLogRepo:            apiLogRepo,
		transactionRepo:       transactionRepo,
		memberService:         memberService,
		config:                config,
		logger:                logger,
		agentClient:           agapi.NewClient(config, logger),
	}

	// Initialize WebSocket client if URL is configured
	if config.WebSocket.URL != "" {
		service.wsClient = websocket.NewWSClient(config.WebSocket.URL)

		// Set up WebSocket event handlers
		service.wsClient.OnConnect(func() {
			logger.Info("JaiJaiPay WebSocket client connected")
		})

		service.wsClient.OnDisconnect(func(err error) {
			if err != nil {
				logger.WithError(err).Warn("JaiJaiPay WebSocket client disconnected")
			} else {
				logger.Info("JaiJaiPay WebSocket client disconnected")
			}

			// Auto-reconnect after disconnection
			go func() {
				time.Sleep(5 * time.Second) // Wait 5 seconds before reconnecting
				logger.Info("Attempting to reconnect JaiJaiPay WebSocket client...")
				if reconnectErr := service.wsClient.Connect(); reconnectErr != nil {
					logger.WithError(reconnectErr).Error("Failed to reconnect JaiJaiPay WebSocket client")
				} else {
					logger.Info("JaiJaiPay WebSocket client reconnected successfully")
				}
			}()
		})

		service.wsClient.OnError(func(err error) {
			logger.WithError(err).Error("JaiJaiPay WebSocket client error")
		})

		// Set reconnection configuration
		service.wsClient.SetReconnectConfig(5*time.Second, 5)

		// Connect to WebSocket server
		if err := service.wsClient.Connect(); err != nil {
			logger.WithError(err).Warn("Failed to connect JaiJaiPay WebSocket client, continuing without it")
			service.wsClient = nil
		}
	}

	return service
}

// Deposits Service Methods

// CreateDeposit creates a new deposit transaction using database configuration
func (s *JaiJaiPayService) CreateDeposit(ctx context.Context, req *jaijaipay.CreateDepositRequest) (*jaijaipay.DepositResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"customer_reference": req.CustomerReference,
		"amount":             req.Amount,
		"currency":           req.Currency,
		"bank_code":          req.BankCode,
	}).Info("creating deposit transaction using database configuration")

	// Get JaiJaiPay client from database configuration
	client, err := s.paymentGatewayService.CreateJaiJaiPayClient(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to create JaiJaiPay client from database")
		return nil, fmt.Errorf("failed to initialize payment gateway: %w", err)
	}

	result, err := client.Deposits.Create(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"customer_reference": req.CustomerReference,
		}).Error("failed to create deposit via database configuration")
		return nil, err
	}

	// Parse amount from string to float64
	amount, parseErr := strconv.ParseFloat(result.Amount, 64)
	if parseErr != nil {
		s.logger.WithError(parseErr).WithField("amount", result.Amount).Error("failed to parse amount from JaiJai response")
		return nil, fmt.Errorf("failed to parse amount: %w", parseErr)
	}

	// Save the deposit response to payment_gateway_transactions table with QR code data
	now := time.Now()
	transaction := &payment_gateway_transaction.PaymentGatewayTransaction{
		TransactionID:           result.TransactionID,
		InternalReference:       result.OrderID,
		PaymentGatewayAccountID: 1, // Default JaiJaiPay account ID
		Provider:                "jaijaipay",
		ProviderMerchantID:      result.MerchantID,
		ProviderOrderID:         result.OrderID,
		TransactionType:         "DEPOSIT",
		Amount:                  amount, // Use parsed float64
		Currency:                result.Currency,
		Status:                  result.Status,
		CustomerReference:       req.CustomerReference,
		CustomerUsername:        req.CustomerReference, // Assuming customer reference is username
		PaymentURL:              result.PaymentURL,
		QRCode:                  result.QRCode,
		QRText:                  result.QRText,
		QRImageURL:              result.QRImageURL,
		InitiatedAt:             &now,
		ExpiresAt:               result.ExpiresAt,
		Description:             "Deposit transaction created via JaiJai API",
		Metadata: map[string]interface{}{
			"jaijai_request": map[string]interface{}{
				"customer_reference":  req.CustomerReference,
				"amount":              req.Amount,
				"currency":            req.Currency,
				"asset_type":          req.AssetType,
				"bank_code":           req.BankCode,
				"bank_account_number": req.BankAccountNumber,
				"account_holder_name": req.AccountHolderName,
				"description":         req.Description,
				"webhook_url":         req.WebhookURL,
			},
			"created_via": "api_call",
			"source":      "database_configuration",
		},
		ProviderResponse: map[string]interface{}{
			"transaction_id":        result.TransactionID,
			"transaction_reference": result.TransactionReference,
			"order_id":              result.OrderID,
			"merchant_id":           result.MerchantID,
			"company_id":            result.CompanyID,
			"status":                result.Status,
			"amount":                result.Amount,
			"original_amount":       result.OriginalAmount,
			"currency":              result.Currency,
			"bank_code":             result.BankCode,
			"bank_account_number":   result.BankAccountNumber,
			"account_holder_name":   result.AccountHolderName,
			"qr_code":               result.QRCode,
			"qr_text":               result.QRText,
			"qr_image_url":          result.QRImageURL,
			"payment_url":           result.PaymentURL,
			"expires_at":            result.ExpiresAt,
			"created_at":            result.CreatedAt,
		},
		CreatedBy: "system",
	}

	// Save transaction to database
	if saveErr := s.transactionRepo.Create(ctx, transaction); saveErr != nil {
		s.logger.WithError(saveErr).WithFields(map[string]interface{}{
			"transaction_id":     result.TransactionID,
			"customer_reference": req.CustomerReference,
		}).Error("failed to save deposit transaction to database")
		// Don't return error here - the deposit was created successfully in JaiJai
		// Just log the error and continue
	} else {
		s.logger.WithFields(map[string]interface{}{
			"transaction_id":     result.TransactionID,
			"database_id":        transaction.ID,
			"customer_reference": req.CustomerReference,
			"qr_code_saved":      result.QRCode != "",
			"qr_image_url_saved": result.QRImageURL != "",
		}).Info("deposit transaction saved to payment_gateway_transactions table with QR code data")
	}

	s.logger.WithFields(map[string]interface{}{
		"transaction_id": result.TransactionID,
		"status":         result.Status,
		"amount":         result.Amount,
		"source":         "database_configuration",
		"saved_to_db":    true,
		"qr_code_saved":  result.QRCode != "",
	}).Info("deposit transaction created successfully and saved with QR code data")

	// Add database ID to the result for frontend redirect functionality
	result.ID = transaction.ID

	return result, nil
}

// ListDeposits retrieves a paginated list of deposit transactions
func (s *JaiJaiPayService) ListDeposits(ctx context.Context, req *jaijaipay.ListDepositsRequest) (*jaijaipay.ListDepositsResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"page":     req.Page,
		"limit":    req.Limit,
		"currency": req.Currency,
		"status":   req.Status,
	}).Info("listing deposit transactions using database configuration")

	// Get JaiJaiPay client from database configuration
	client, err := s.paymentGatewayService.CreateJaiJaiPayClient(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to create JaiJaiPay client from database")
		return nil, fmt.Errorf("failed to initialize payment gateway: %w", err)
	}

	result, err := client.Deposits.List(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to list deposits via database configuration")
		return nil, err
	}

	s.logger.WithFields(map[string]interface{}{
		"total_records": result.Pagination.Total,
		"returned":      len(result.Data),
		"source":        "database_configuration",
	}).Info("deposits listed successfully via database configuration")

	return result, nil
}

// GetDepositByID retrieves a specific deposit transaction by ID
func (s *JaiJaiPayService) GetDepositByID(ctx context.Context, transactionID string) (*jaijaipay.DepositResponse, error) {
	s.logger.WithField("transaction_id", transactionID).Info("getting deposit by ID using database configuration")

	// Get JaiJaiPay client from database configuration
	client, err := s.paymentGatewayService.CreateJaiJaiPayClient(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to create JaiJaiPay client from database")
		return nil, fmt.Errorf("failed to initialize payment gateway: %w", err)
	}

	result, err := client.Deposits.GetByID(ctx, transactionID)
	if err != nil {
		s.logger.WithError(err).WithField("transaction_id", transactionID).Error("failed to get deposit by ID")
		return nil, err
	}

	s.logger.WithFields(map[string]interface{}{
		"transaction_id": result.TransactionID,
		"status":         result.Status,
		"source":         "database_configuration",
	}).Info("deposit retrieved successfully via database configuration")

	return result, nil
}

// CancelDeposit cancels a pending deposit transaction
func (s *JaiJaiPayService) CancelDeposit(ctx context.Context, req *jaijaipay.CancelDepositRequest) (*jaijaipay.CancelDepositResponse, error) {
	s.logger.WithField("transaction_id", req.TransactionID).Info("cancelling deposit using database configuration")

	// Get JaiJaiPay client from database configuration
	client, err := s.paymentGatewayService.CreateJaiJaiPayClient(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to create JaiJaiPay client from database")
		return nil, fmt.Errorf("failed to initialize payment gateway: %w", err)
	}

	result, err := client.Deposits.Cancel(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("transaction_id", req.TransactionID).Error("failed to cancel deposit via database configuration")
		return nil, err
	}

	s.logger.WithFields(map[string]interface{}{
		"transaction_id": result.TransactionID,
		"status":         result.Status,
		"source":         "database_configuration",
	}).Info("deposit cancelled successfully via database configuration")

	return result, nil
}

// Withdrawals Service Methods

// CreateWithdrawal creates a new withdrawal transaction and saves it to payment_gateway_transactions
func (s *JaiJaiPayService) CreateWithdrawal(ctx context.Context, req *jaijaipay.CreateWithdrawalRequest) (*jaijaipay.WithdrawalResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"customer_reference": req.CustomerReference,
		"amount":             req.Amount,
		"currency":           req.Currency,
		"bank_code":          req.BankCode,
	}).Info("creating withdrawal transaction using database configuration")

	// Get JaiJaiPay client from database configuration
	client, err := s.paymentGatewayService.CreateJaiJaiPayClient(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to create JaiJaiPay client from database")
		return nil, fmt.Errorf("failed to initialize payment gateway: %w", err)
	}

	// Call JaiJai API to create withdrawal
	result, err := client.Withdrawals.Create(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"customer_reference": req.CustomerReference,
		}).Error("failed to create withdrawal via database configuration")
		return nil, err
	}

	// Parse amount from string to float64
	amount, parseErr := strconv.ParseFloat(result.Amount, 64)
	if parseErr != nil {
		s.logger.WithError(parseErr).WithField("amount", result.Amount).Error("failed to parse amount from JaiJai response")
		return nil, fmt.Errorf("failed to parse amount: %w", parseErr)
	}

	// Save the response to payment_gateway_transactions table with PENDING status
	now := time.Now()
	transaction := &payment_gateway_transaction.PaymentGatewayTransaction{
		TransactionID:           result.TransactionID,
		InternalReference:       result.OrderID,
		PaymentGatewayAccountID: 1, // Default JaiJaiPay account ID
		Provider:                "jaijaipay",
		ProviderMerchantID:      result.MerchantID,
		ProviderOrderID:         result.OrderID,
		TransactionType:         "WITHDRAW",
		Amount:                  amount, // Use parsed float64
		Currency:                result.Currency,
		Status:                  "PENDING", // Always set to PENDING initially, webhook will update
		CustomerReference:       req.CustomerReference,
		CustomerUsername:        req.CustomerReference, // Assuming customer reference is username
		CustomerBankAccount:     req.BankAccountNumber,
		CustomerBankName:        req.BankCode,
		InitiatedAt:             &now,
		Description:             "Withdrawal transaction created via JaiJai API",
		Metadata: map[string]interface{}{
			"jaijai_request": map[string]interface{}{
				"customer_reference":  req.CustomerReference,
				"amount":              req.Amount,
				"currency":            req.Currency,
				"asset_type":          req.AssetType,
				"bank_code":           req.BankCode,
				"bank_account_number": req.BankAccountNumber,
				"account_holder_name": req.AccountHolderName,
				"description":         req.Description,
				"webhook_url":         req.WebhookURL,
			},
			"created_via": "api_call",
			"source":      "database_configuration",
		},
		ProviderResponse: map[string]interface{}{
			"transaction_id":        result.TransactionID,
			"transaction_reference": result.TransactionReference,
			"order_id":              result.OrderID,
			"merchant_id":           result.MerchantID,
			"company_id":            result.CompanyID,
			"status":                result.Status,
			"amount":                result.Amount,
			"currency":              result.Currency,
			"bank_code":             result.BankCode,
			"bank_account_number":   result.BankAccountNumber,
			"account_holder_name":   result.AccountHolderName,
			"created_at":            result.CreatedAt,
		},
		CreatedBy: "system",
	}

	// Save transaction to database
	if saveErr := s.transactionRepo.Create(ctx, transaction); saveErr != nil {
		s.logger.WithError(saveErr).WithFields(map[string]interface{}{
			"transaction_id":     result.TransactionID,
			"customer_reference": req.CustomerReference,
		}).Error("failed to save withdrawal transaction to database")
		// Don't return error here - the withdrawal was created successfully in JaiJai
		// Just log the error and continue
	} else {
		s.logger.WithFields(map[string]interface{}{
			"transaction_id":     result.TransactionID,
			"database_id":        transaction.ID,
			"customer_reference": req.CustomerReference,
		}).Info("withdrawal transaction saved to payment_gateway_transactions table")
	}

	s.logger.WithFields(map[string]interface{}{
		"transaction_id": result.TransactionID,
		"status":         result.Status,
		"amount":         result.Amount,
		"source":         "database_configuration",
		"saved_to_db":    true,
		"db_status":      "PENDING",
	}).Info("withdrawal transaction created successfully and saved with PENDING status - will be updated by webhook")

	// Return the result but note that status updates will come via webhook
	return result, nil
}

// ListWithdrawals retrieves a paginated list of withdrawal transactions
func (s *JaiJaiPayService) ListWithdrawals(ctx context.Context, req *jaijaipay.ListWithdrawalsRequest) (*jaijaipay.ListWithdrawalsResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"page":     req.Page,
		"limit":    req.Limit,
		"currency": req.Currency,
		"status":   req.Status,
	}).Info("listing withdrawal transactions using database configuration")

	// Get JaiJaiPay client from database configuration
	client, err := s.paymentGatewayService.CreateJaiJaiPayClient(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to create JaiJaiPay client from database")
		return nil, fmt.Errorf("failed to initialize payment gateway: %w", err)
	}

	result, err := client.Withdrawals.List(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to list withdrawals via database configuration")
		return nil, err
	}

	s.logger.WithFields(map[string]interface{}{
		"total_records": result.Pagination.Total,
		"returned":      len(result.Data),
		"source":        "database_configuration",
	}).Info("withdrawals listed successfully via database configuration")

	return result, nil
}

// GetWithdrawalByID retrieves a specific withdrawal transaction by ID
func (s *JaiJaiPayService) GetWithdrawalByID(ctx context.Context, transactionID string) (*jaijaipay.WithdrawalResponse, error) {
	s.logger.WithField("transaction_id", transactionID).Info("getting withdrawal by ID using database configuration")

	// Get JaiJaiPay client from database configuration
	client, err := s.paymentGatewayService.CreateJaiJaiPayClient(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to create JaiJaiPay client from database")
		return nil, fmt.Errorf("failed to initialize payment gateway: %w", err)
	}

	result, err := client.Withdrawals.GetByID(ctx, transactionID)
	if err != nil {
		s.logger.WithError(err).WithField("transaction_id", transactionID).Error("failed to get withdrawal by ID via database configuration")
		return nil, err
	}

	s.logger.WithFields(map[string]interface{}{
		"transaction_id": result.TransactionID,
		"status":         result.Status,
		"source":         "database_configuration",
	}).Info("withdrawal retrieved successfully via database configuration")

	return result, nil
}

// Balance Service Methods

// GetBalance retrieves current account balance and financial summary
func (s *JaiJaiPayService) GetBalance(ctx context.Context) (*jaijaipay.BalanceResponse, error) {
	s.logger.Info("getting account balance using database configuration")

	// Get JaiJaiPay client from database configuration
	client, err := s.paymentGatewayService.CreateJaiJaiPayClient(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to create JaiJaiPay client from database")
		return nil, fmt.Errorf("failed to initialize payment gateway: %w", err)
	}

	result, err := client.Balance.Get(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get balance via database configuration")
		return nil, err
	}

	s.logger.WithFields(map[string]interface{}{
		"merchant_id": result.MerchantID,
		"currencies":  len(result.Results),
		"source":      "database_configuration",
	}).Info("balance retrieved successfully via database configuration")

	return result, nil
}

// Fees Service Methods

// GetFeePreview calculates estimated fees for a transaction
func (s *JaiJaiPayService) GetFeePreview(ctx context.Context, req *jaijaipay.FeePreviewRequest) (*jaijaipay.FeePreviewResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"amount":           req.Amount,
		"transaction_type": req.TransactionType,
		"currency":         req.Currency,
	}).Info("getting fee preview using database configuration")

	// Get JaiJaiPay client from database configuration
	client, err := s.paymentGatewayService.CreateJaiJaiPayClient(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to create JaiJaiPay client from database")
		return nil, fmt.Errorf("failed to initialize payment gateway: %w", err)
	}

	result, err := client.Fees.GetPreview(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"amount":           req.Amount,
			"transaction_type": req.TransactionType,
		}).Error("failed to get fee preview via database configuration")
		return nil, err
	}

	s.logger.WithFields(map[string]interface{}{
		"total_fee":         result.FeeCalculation.TotalFeeAmount,
		"customer_pays":     result.AmountBreakdown.CustomerPays,
		"merchant_receives": result.AmountBreakdown.MerchantReceives,
		"source":            "database_configuration",
	}).Info("fee preview calculated successfully via database configuration")

	return result, nil
}

// Analytics Service Methods

// GetTransactionAnalytics retrieves comprehensive transaction analytics
func (s *JaiJaiPayService) GetTransactionAnalytics(ctx context.Context, req *jaijaipay.AnalyticsRequest) (*jaijaipay.AnalyticsResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"start_date":       req.StartDate,
		"end_date":         req.EndDate,
		"currency":         req.Currency,
		"transaction_type": req.TransactionType,
	}).Info("getting transaction analytics using database configuration")

	// Get JaiJaiPay client from database configuration
	client, err := s.paymentGatewayService.CreateJaiJaiPayClient(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to create JaiJaiPay client from database")
		return nil, fmt.Errorf("failed to initialize payment gateway: %w", err)
	}

	result, err := client.Analytics.GetTransactionAnalytics(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"start_date": req.StartDate,
			"end_date":   req.EndDate,
		}).Error("failed to get transaction analytics via database configuration")
		return nil, err
	}

	s.logger.WithFields(map[string]interface{}{
		"total_transactions": result.Summary.TotalTransactions,
		"total_volume":       result.Summary.TotalVolume,
		"unique_customers":   result.Summary.UniqueCustomers,
		"source":             "database_configuration",
	}).Info("transaction analytics retrieved successfully via database configuration")

	return result, nil
}

// Webhooks Service Methods

// ResendWebhook resends webhook notifications for specific transactions
func (s *JaiJaiPayService) ResendWebhook(ctx context.Context, req *jaijaipay.ResendWebhookRequest) (*jaijaipay.ResendWebhookResponse, error) {
	s.logger.WithField("transaction_id", req.TransactionID).Info("resending webhook using database configuration")

	// Get JaiJaiPay client from database configuration
	client, err := s.paymentGatewayService.CreateJaiJaiPayClient(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to create JaiJaiPay client from database")
		return nil, fmt.Errorf("failed to initialize payment gateway: %w", err)
	}

	result, err := client.Webhooks.Resend(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("transaction_id", req.TransactionID).Error("failed to resend webhook via database configuration")
		return nil, err
	}

	s.logger.WithFields(map[string]interface{}{
		"transaction_id":        result.TransactionID,
		"success":               result.Success,
		"previous_resend_count": result.PreviousResendCount,
		"source":                "database_configuration",
	}).Info("webhook resent successfully via database configuration")

	return result, nil
}

// ValidateWebhookPayload validates incoming webhook payload signature
func (s *JaiJaiPayService) ValidateWebhookPayload(payload string, signature string) bool {
	// Get JaiJaiPay client from database configuration
	client, err := s.paymentGatewayService.CreateJaiJaiPayClient(context.Background())
	if err != nil {
		s.logger.WithError(err).Error("failed to create JaiJaiPay client from database for webhook validation")
		return false
	}

	isValid := client.Webhooks.ValidateWebhookPayload(payload, signature)

	s.logger.WithFields(map[string]interface{}{
		"signature_valid": isValid,
		"payload_length":  len(payload),
		"source":          "database_configuration",
	}).Info("webhook payload validation completed using database configuration")

	return isValid
}

// ProcessWebhook processes incoming webhook from JaiJaiPay
func (s *JaiJaiPayService) ProcessWebhook(ctx context.Context, payload *jaijaipay.WebhookPayload, signature string, rawBody []byte) error {
	s.logger.WithFields(map[string]interface{}{
		"transaction_id": payload.TransactionID,
		"event":          payload.Event,
		"status":         payload.Status,
		"amount":         payload.Amount,
		"currency":       payload.Currency,
	}).Info("processing JaiJaiPay webhook")

	// Get JaiJaiPay client from database configuration for webhook validation
	client, err := s.paymentGatewayService.CreateJaiJaiPayClient(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to create JaiJaiPay client from database for webhook validation")
		return fmt.Errorf("failed to initialize payment gateway: %w", err)
	}

	// Skip signature validation as per JaiJaiPay team requirements
	// They only need 200 OK response without signature verification
	isValid := true
	if signature != "" {
		// Optional signature validation for logging purposes only
		isValid = client.Webhooks.ValidateWebhookPayload(string(rawBody), signature)
		s.logger.WithFields(map[string]interface{}{
			"transaction_id":  payload.TransactionID,
			"signature_valid": isValid,
		}).Info("webhook signature validation (optional)")
	}

	// Log webhook to payment_gateway_webhooks table
	webhookLog := &payment_gateway_transaction.PaymentGatewayWebhook{
		TransactionID: payload.TransactionID,
		WebhookEvent:  payload.Event,
		WebhookPayload: map[string]interface{}{
			"transactionId":        payload.TransactionID,
			"transactionReference": payload.TransactionReference,
			"status":               payload.Status,
			"amount":               payload.Amount,
			"currency":             payload.Currency,
			"transactionType":      payload.TransactionType,
			"orderId":              payload.OrderID,
			"customerReference":    payload.CustomerReference,
			"merchantId":           payload.MerchantID,
			"companyId":            payload.CompanyID,
			"timestamp":            payload.Timestamp,
			"metadata":             payload.Metadata,
		},
		WebhookSignature: signature,
		IsSignatureValid: isValid,
		ProcessingStatus: "PROCESSED",
		Provider:         "jaijaipay",
		HTTPMethod:       "POST",
		HTTPStatusCode:   200,
	}

	if logErr := s.transactionRepo.CreateWebhookLog(ctx, webhookLog); logErr != nil {
		s.logger.WithError(logErr).WithField("transaction_id", payload.TransactionID).
			Warn("failed to log webhook to database")
	}

	// Log successful webhook processing for audit trail
	s.logger.WithFields(map[string]interface{}{
		"transaction_id":        payload.TransactionID,
		"transaction_reference": payload.TransactionReference,
		"event":                 payload.Event,
		"status":                payload.Status,
		"order_id":              payload.OrderID,
		"customer_reference":    payload.CustomerReference,
		"merchant_id":           payload.MerchantID,
		"company_id":            payload.CompanyID,
		"timestamp":             payload.Timestamp,
		"logged_to_db":          true,
	}).Info("webhook processed successfully")

	// Update or create payment gateway transaction record
	s.logger.WithField("transaction_id", payload.TransactionID).Info("WEBHOOK DEBUG: About to update payment gateway transaction")
	err = s.updatePaymentGatewayTransaction(ctx, payload)
	if err != nil {
		s.logger.WithError(err).WithField("transaction_id", payload.TransactionID).
			Error("WEBHOOK DEBUG: failed to update payment gateway transaction")
	} else {
		s.logger.WithField("transaction_id", payload.TransactionID).Info("WEBHOOK DEBUG: Successfully updated payment gateway transaction")
	}

	// Here you can add specific business logic based on the event type
	switch payload.Event {
	case "transaction.completed":
		s.logger.WithField("transaction_id", payload.TransactionID).Info("transaction completed via webhook")
		s.handleTransactionCompleted(payload)
	case "transaction.cancelled":
		s.logger.WithField("transaction_id", payload.TransactionID).Info("transaction cancelled via webhook")
		s.handleTransactionFailed(payload)
	case "transaction.failed":
		s.logger.WithField("transaction_id", payload.TransactionID).Info("transaction failed via webhook")
		s.handleTransactionFailed(payload)
	case "transaction.pending":
		s.logger.WithField("transaction_id", payload.TransactionID).Info("transaction pending via webhook")
	default:
		s.logger.WithFields(map[string]interface{}{
			"transaction_id": payload.TransactionID,
			"event":          payload.Event,
		}).Info("received webhook with unhandled event type")
	}

	return nil
}

// updatePaymentGatewayTransaction updates or creates payment gateway transaction record from webhook payload
func (s *JaiJaiPayService) updatePaymentGatewayTransaction(ctx context.Context, payload *jaijaipay.WebhookPayload) error {
	// Check if transaction already exists
	existingTransaction, err := s.transactionRepo.GetByTransactionID(ctx, payload.TransactionID)

	// Handle errors properly - pgx.ErrNoRows means transaction doesn't exist
	if err != nil {
		if err.Error() != "no rows in result set" && err.Error() != "transaction not found" {
			s.logger.WithError(err).WithField("transaction_id", payload.TransactionID).
				Error("error checking existing transaction")
			return fmt.Errorf("failed to get existing transaction: %w", err)
		}
		// Transaction doesn't exist, we'll create it
		existingTransaction = nil
	}

	if existingTransaction != nil {
		// Update existing transaction status and metadata
		updatedMetadata := map[string]interface{}{
			"webhook_payload": payload,
			"updated_via":     "webhook",
			"updated_at":      payload.Timestamp,
		}

		// Merge with existing metadata
		if existingTransaction.Metadata != nil {
			for k, v := range existingTransaction.Metadata {
				updatedMetadata[k] = v
			}
		}

		err = s.transactionRepo.UpdateStatus(ctx, payload.TransactionID, payload.Status, updatedMetadata)
		if err != nil {
			s.logger.WithError(err).WithField("transaction_id", payload.TransactionID).
				Error("failed to update payment gateway transaction status")
			return fmt.Errorf("failed to update payment gateway transaction: %w", err)
		}

		s.logger.WithField("transaction_id", payload.TransactionID).Info("updated payment gateway transaction from webhook")

		// If transaction status changed to COMPLETED, process game API calls
		if payload.Status == "COMPLETED" {
			// First, update game_username from AG API if member doesn't have it yet
			if err := s.updateGameUsernameBeforeCredit(ctx, payload); err != nil {
				s.logger.WithError(err).WithField("transaction_id", payload.TransactionID).
					Warn("failed to update game username from AG API, continuing with transaction processing")
			}

			if payload.TransactionType == "DEPOSIT" {
				if err := s.processDepositCredit(ctx, payload); err != nil {
					s.logger.WithError(err).WithField("transaction_id", payload.TransactionID).
						Error("failed to process deposit credit, but transaction was updated")
				}
			} else if payload.TransactionType == "WITHDRAW" {
				if err := s.processWithdrawalDebit(ctx, payload); err != nil {
					s.logger.WithError(err).WithField("transaction_id", payload.TransactionID).
						Error("failed to process withdrawal debit, but transaction was updated")
				}
			}
		}

		return nil
	} else {
		// Parse amount from string to float64
		amount, err := strconv.ParseFloat(payload.Amount, 64)
		if err != nil {
			return fmt.Errorf("invalid amount format: %w", err)
		}

		// Parse metadata from JSON
		var metadata map[string]interface{}
		if payload.Metadata != nil {
			err = json.Unmarshal(payload.Metadata, &metadata)
			if err != nil {
				s.logger.WithError(err).Warn("failed to parse webhook metadata")
				metadata = map[string]interface{}{}
			}
		} else {
			metadata = map[string]interface{}{}
		}

		// Add webhook info to metadata
		metadata["webhook_payload"] = payload
		metadata["created_via"] = "webhook"
		metadata["created_at"] = payload.Timestamp

		// Use timestamp from payload for InitiatedAt
		initiatedAt := payload.Timestamp
		if initiatedAt.IsZero() {
			// Fallback to current time if timestamp is zero
			initiatedAt = time.Now()
		}

		// Create new transaction record from webhook
		transaction := &payment_gateway_transaction.PaymentGatewayTransaction{
			TransactionID:           payload.TransactionID,
			InternalReference:       payload.OrderID,
			PaymentGatewayAccountID: 1, // Default JaiJaiPay account ID
			Provider:                "jaijaipay",
			ProviderMerchantID:      payload.MerchantID,
			ProviderOrderID:         payload.OrderID,
			TransactionType:         payload.TransactionType,
			Amount:                  amount,
			Currency:                payload.Currency,
			Status:                  payload.Status,
			CustomerReference:       payload.CustomerReference,
			InitiatedAt:             &initiatedAt, // Add required field
			Description:             "Transaction created from webhook",
			Metadata:                metadata,
			ProviderResponse: map[string]interface{}{
				"webhook_payload": payload,
				"created_via":     "webhook",
				"created_at":      payload.Timestamp,
			},
			CreatedBy: "system",
		}

		err = s.transactionRepo.Create(ctx, transaction)
		if err != nil {
			s.logger.WithError(err).WithFields(map[string]interface{}{
				"transaction_id":     payload.TransactionID,
				"amount":             amount,
				"currency":           payload.Currency,
				"customer_reference": payload.CustomerReference,
			}).Error("failed to create payment gateway transaction")
			return fmt.Errorf("failed to create payment gateway transaction: %w", err)
		}

		s.logger.WithField("transaction_id", payload.TransactionID).Info("created new payment gateway transaction from webhook")

		// If transaction is COMPLETED, process game API calls
		if payload.Status == "COMPLETED" {
			// First, update game_username from AG API if member doesn't have it yet
			if err := s.updateGameUsernameBeforeCredit(ctx, payload); err != nil {
				s.logger.WithError(err).WithField("transaction_id", payload.TransactionID).
					Warn("failed to update game username from AG API, continuing with transaction processing")
			}

			if payload.TransactionType == "DEPOSIT" {
				if err := s.processDepositCredit(ctx, payload); err != nil {
					s.logger.WithError(err).WithField("transaction_id", payload.TransactionID).
						Error("failed to process deposit credit, but transaction was saved")
				}
			} else if payload.TransactionType == "WITHDRAW" {
				if err := s.processWithdrawalDebit(ctx, payload); err != nil {
					s.logger.WithError(err).WithField("transaction_id", payload.TransactionID).
						Error("failed to process withdrawal debit, but transaction was saved")
				}
			}
		}

		return nil
	}
}

// processDepositCredit calls game API to add credit for completed deposit
func (s *JaiJaiPayService) processDepositCredit(ctx context.Context, payload *jaijaipay.WebhookPayload) error {
	s.logger.WithField("transaction_id", payload.TransactionID).Info("DEBUG: processDepositCredit START")
	s.logger.WithField("transaction_id", payload.TransactionID).Info("processing deposit credit")

	// Debug: Print to stdout as well
	fmt.Printf("DEBUG: processDepositCredit called with transaction_id: %s\n", payload.TransactionID)

	// Get member by customer reference
	memberRepo := s.paymentGatewayService.memberRepo
	if memberRepo == nil {
		return fmt.Errorf("member repository not available")
	}

	// Find member by ID (assuming customer_reference is member ID)
	memberID := payload.CustomerReference

	fmt.Printf("DEBUG: Looking for member with ID: %s\n", memberID)
	s.logger.WithField("member_id", memberID).Info("DEBUG: Looking for member")

	member, err := memberRepo.GetByID(ctx, memberID)
	if err != nil {
		fmt.Printf("DEBUG: Member not found with ID %s: %v\n", memberID, err)
		s.logger.WithError(err).WithField("member_id", memberID).Error("DEBUG: Member not found")
		return fmt.Errorf("member not found with ID %s: %w", memberID, err)
	}

	fmt.Printf("DEBUG: Found member: %+v\n", member)
	s.logger.WithFields(map[string]interface{}{
		"member_id": member.ID,
		"username":  member.Username,
		"game_username": func() string {
			if member.GameUsername != nil {
				return *member.GameUsername
			}
			return "nil"
		}(),
	}).Info("DEBUG: Member details")

	// Check if member has game_username (required for game API)
	if member.GameUsername == nil || *member.GameUsername == "" {
		gameUsernameValue := "nil"
		if member.GameUsername != nil {
			gameUsernameValue = fmt.Sprintf("'%s' (empty)", *member.GameUsername)
		}
		fmt.Printf("DEBUG: Member %s does not have game_username. Value: %s\n", *member.Username, gameUsernameValue)
		s.logger.WithFields(map[string]interface{}{
			"member_username":     *member.Username,
			"game_username_nil":   member.GameUsername == nil,
			"game_username_value": gameUsernameValue,
		}).Error("DEBUG: Member does not have game_username")
		return fmt.Errorf("member %s does not have game_username", *member.Username)
	}

	// Parse amount
	amount, err := strconv.ParseFloat(payload.Amount, 64)
	if err != nil {
		return fmt.Errorf("invalid amount format: %w", err)
	}

	// Call game API to deposit credit and get updated balance
	fmt.Printf("DEBUG: About to call game API with game_username: %s, amount: %f\n", *member.GameUsername, amount)
	s.logger.WithFields(map[string]interface{}{
		"game_username":  *member.GameUsername,
		"amount":         amount,
		"transaction_id": payload.TransactionID,
	}).Info("DEBUG: About to call game API")

	walletAfter, err := s.callGameAPIDeposit(ctx, *member.GameUsername, amount, payload.TransactionID)
	if err != nil {
		fmt.Printf("DEBUG: Game API call failed: %v\n", err)
		s.logger.WithError(err).WithField("game_username", *member.GameUsername).Error("DEBUG: Game API call failed")
		return fmt.Errorf("failed to call game API: %w", err)
	}

	fmt.Printf("DEBUG: Game API call successful, wallet_after: %f\n", walletAfter)
	s.logger.WithField("wallet_after", walletAfter).Info("DEBUG: Game API call successful")

	// Update member balance with wallet_after from game API
	member.Balance = walletAfter
	member.UpdatedAt = time.Now()

	if err := s.paymentGatewayService.memberRepo.Update(ctx, member); err != nil {
		s.logger.WithError(err).WithField("member_id", member.ID).Error("failed to update member balance")
		return fmt.Errorf("failed to update member balance: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"transaction_id": payload.TransactionID,
		"game_username":  *member.GameUsername,
		"amount":         amount,
		"wallet_after":   walletAfter,
		"member_balance": member.Balance,
	}).Info("deposit credit processed and member balance updated successfully")

	// Send WebSocket notification about successful deposit
	s.sendDepositNotification(member.ID, payload.TransactionID)

	return nil
}

// callGameAPIDeposit calls the agent API to deposit credit and returns wallet_after
func (s *JaiJaiPayService) callGameAPIDeposit(ctx context.Context, gameUsername string, amount float64, referenceID string) (float64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "callGameAPIDeposit")

	// Call agent API using client
	response, err := s.agentClient.GamesDeposit(ctx, gameUsername, amount, referenceID)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"game_username": gameUsername,
			"amount":        amount,
			"reference_id":  referenceID,
		}).Error("failed to call agent API for games deposit")
		return 0, fmt.Errorf("failed to call agent API for games deposit: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"game_username":   gameUsername,
		"amount":          amount,
		"reference_id":    referenceID,
		"wallet_before":   response.WalletBefore,
		"wallet_after":    response.WalletAfter,
		"txn_id":          response.TxnID,
		"response_amount": response.Amount,
	}).Info("deposit credit processed successfully via agent API")

	return response.WalletAfter, nil
}

// API Logs Service Methods

// GetAPILogsByOrderID retrieves API logs by order ID for debugging
func (s *JaiJaiPayService) GetAPILogsByOrderID(ctx context.Context, orderID string) (interface{}, error) {
	s.logger.WithField("order_id", orderID).Info("getting API logs by order ID")

	logs, err := s.apiLogRepo.GetByOrderID(ctx, orderID)
	if err != nil {
		s.logger.WithError(err).WithField("order_id", orderID).Error("failed to get API logs by order ID")
		return nil, err
	}

	s.logger.WithFields(map[string]interface{}{
		"order_id":  orderID,
		"log_count": len(logs),
	}).Info("API logs retrieved successfully")

	return logs, nil
}

// GetAPILogsByTransactionID retrieves API logs by transaction ID
func (s *JaiJaiPayService) GetAPILogsByTransactionID(ctx context.Context, transactionID string) (interface{}, error) {
	s.logger.WithField("transaction_id", transactionID).Info("getting API logs by transaction ID")

	logs, err := s.apiLogRepo.GetByTransactionID(ctx, transactionID)
	if err != nil {
		s.logger.WithError(err).WithField("transaction_id", transactionID).Error("failed to get API logs by transaction ID")
		return nil, err
	}

	s.logger.WithFields(map[string]interface{}{
		"transaction_id": transactionID,
		"log_count":      len(logs),
	}).Info("API logs retrieved successfully")

	return logs, nil
}

// sendDepositNotification sends WebSocket notification with retry mechanism
func (s *JaiJaiPayService) sendDepositNotification(memberID int, transactionID string) {
	if s.wsClient == nil {
		s.logger.Debug("WebSocket client not initialized, skipping deposit notification")
		return
	}

	// Retry mechanism for WebSocket notifications
	maxRetries := 3
	for attempt := 1; attempt <= maxRetries; attempt++ {
		if !s.wsClient.IsConnected() {
			s.logger.WithFields(map[string]interface{}{
				"attempt":        attempt,
				"member_id":      memberID,
				"transaction_id": transactionID,
			}).Warn("WebSocket not connected, attempting to send notification anyway")
		}

		err := s.wsClient.EmitDeposit(memberID, true)
		if err == nil {
			s.logger.WithFields(map[string]interface{}{
				"member_id":      memberID,
				"transaction_id": transactionID,
				"channel_id":     memberID,
				"attempt":        attempt,
			}).Info("WebSocket deposit notification sent successfully")
			return
		}

		s.logger.WithError(err).WithFields(map[string]interface{}{
			"member_id":      memberID,
			"transaction_id": transactionID,
			"attempt":        attempt,
			"max_retries":    maxRetries,
		}).Warn("failed to send WebSocket deposit notification")

		// If not the last attempt, wait and retry
		if attempt < maxRetries {
			time.Sleep(time.Duration(attempt) * time.Second)
		}
	}

	s.logger.WithFields(map[string]interface{}{
		"member_id":      memberID,
		"transaction_id": transactionID,
		"max_retries":    maxRetries,
	}).Error("failed to send WebSocket deposit notification after all retries")
}

// sendWithdrawalNotification sends WebSocket notification for withdrawal with retry mechanism
func (s *JaiJaiPayService) sendWithdrawalNotification(memberID int, transactionID string, success bool) {
	if s.wsClient == nil {
		s.logger.Debug("WebSocket client not initialized, skipping withdrawal notification")
		return
	}

	// Retry mechanism for WebSocket notifications
	maxRetries := 3
	for attempt := 1; attempt <= maxRetries; attempt++ {
		if !s.wsClient.IsConnected() {
			s.logger.WithFields(map[string]interface{}{
				"attempt":        attempt,
				"member_id":      memberID,
				"transaction_id": transactionID,
				"success":        success,
			}).Warn("WebSocket not connected, attempting to send withdrawal notification anyway")
		}

		err := s.wsClient.EmitWithdraw(memberID, success)
		if err == nil {
			s.logger.WithFields(map[string]interface{}{
				"member_id":      memberID,
				"transaction_id": transactionID,
				"channel_id":     memberID,
				"attempt":        attempt,
				"success":        success,
			}).Info("WebSocket withdrawal notification sent successfully")
			return
		}

		s.logger.WithError(err).WithFields(map[string]interface{}{
			"member_id":      memberID,
			"transaction_id": transactionID,
			"attempt":        attempt,
			"max_retries":    maxRetries,
			"success":        success,
		}).Warn("failed to send WebSocket withdrawal notification")

		// If not the last attempt, wait and retry
		if attempt < maxRetries {
			time.Sleep(time.Duration(attempt) * time.Second)
		}
	}

	s.logger.WithFields(map[string]interface{}{
		"member_id":      memberID,
		"transaction_id": transactionID,
		"max_retries":    maxRetries,
		"success":        success,
	}).Error("failed to send WebSocket withdrawal notification after all retries")
}

// handleTransactionCompleted handles completed transactions and sends appropriate WebSocket notifications
func (s *JaiJaiPayService) handleTransactionCompleted(payload *jaijaipay.WebhookPayload) {
	// Parse member ID from customer reference
	memberID, err := strconv.Atoi(payload.CustomerReference)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"transaction_id":     payload.TransactionID,
			"customer_reference": payload.CustomerReference,
		}).Error("failed to parse member ID from customer reference for WebSocket notification")
		return
	}

	// Send appropriate WebSocket notification based on transaction type
	switch payload.TransactionType {
	case "DEPOSIT":
		// Deposit notification (existing logic - already handled elsewhere)
		s.sendDepositNotification(memberID, payload.TransactionID)
	case "WITHDRAW":
		// Withdrawal successful notification
		s.sendWithdrawalNotification(memberID, payload.TransactionID, true)
	default:
		s.logger.WithFields(map[string]interface{}{
			"transaction_id":   payload.TransactionID,
			"transaction_type": payload.TransactionType,
		}).Info("no WebSocket notification needed for transaction type")
	}
}

// handleTransactionFailed handles failed/cancelled transactions and sends failure WebSocket notifications
func (s *JaiJaiPayService) handleTransactionFailed(payload *jaijaipay.WebhookPayload) {
	// Parse member ID from customer reference
	memberID, err := strconv.Atoi(payload.CustomerReference)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"transaction_id":     payload.TransactionID,
			"customer_reference": payload.CustomerReference,
		}).Error("failed to parse member ID from customer reference for WebSocket notification")
		return
	}

	// Send appropriate WebSocket notification based on transaction type
	switch payload.TransactionType {
	case "DEPOSIT":
		// Deposit failed notification - using EmitDeposit with false status
		if s.wsClient != nil {
			if err := s.wsClient.EmitDeposit(memberID, false); err != nil {
				s.logger.WithError(err).WithField("member_id", memberID).Warn("failed to send deposit failure WebSocket notification")
			} else {
				s.logger.WithFields(map[string]interface{}{
					"member_id":      memberID,
					"transaction_id": payload.TransactionID,
					"success":        false,
				}).Info("deposit failure WebSocket notification sent successfully")
			}
		}
	case "WITHDRAW":
		// Withdrawal failed notification
		s.sendWithdrawalNotification(memberID, payload.TransactionID, false)
	default:
		s.logger.WithFields(map[string]interface{}{
			"transaction_id":   payload.TransactionID,
			"transaction_type": payload.TransactionType,
		}).Info("no WebSocket notification needed for transaction type")
	}
}

// processWithdrawalDebit calls game API to deduct credit for completed withdrawal
func (s *JaiJaiPayService) processWithdrawalDebit(ctx context.Context, payload *jaijaipay.WebhookPayload) error {
	s.logger.WithField("transaction_id", payload.TransactionID).Info("DEBUG: processWithdrawalDebit START")
	s.logger.WithField("transaction_id", payload.TransactionID).Info("processing withdrawal debit")

	// Debug: Print to stdout as well
	fmt.Printf("DEBUG: processWithdrawalDebit called with transaction_id: %s\n", payload.TransactionID)

	// Get member by customer reference
	memberRepo := s.paymentGatewayService.memberRepo
	if memberRepo == nil {
		return fmt.Errorf("member repository not available")
	}

	// Find member by ID (assuming customer_reference is member ID)
	memberID := payload.CustomerReference

	fmt.Printf("DEBUG: Looking for member with ID: %s\n", memberID)
	s.logger.WithField("member_id", memberID).Info("DEBUG: Looking for member")

	member, err := memberRepo.GetByID(ctx, memberID)
	if err != nil {
		fmt.Printf("DEBUG: Member not found with ID %s: %v\n", memberID, err)
		s.logger.WithError(err).WithField("member_id", memberID).Error("DEBUG: Member not found")
		return fmt.Errorf("member not found with ID %s: %w", memberID, err)
	}

	fmt.Printf("DEBUG: Found member: %+v\n", member)
	s.logger.WithFields(map[string]interface{}{
		"member_id": member.ID,
		"username":  member.Username,
		"game_username": func() string {
			if member.GameUsername != nil {
				return *member.GameUsername
			}
			return "nil"
		}(),
	}).Info("DEBUG: Member details")

	// Check if member has game_username (required for game API)
	if member.GameUsername == nil || *member.GameUsername == "" {
		gameUsernameValue := "nil"
		if member.GameUsername != nil {
			gameUsernameValue = fmt.Sprintf("'%s' (empty)", *member.GameUsername)
		}
		fmt.Printf("DEBUG: Member %s does not have game_username. Value: %s\n", *member.Username, gameUsernameValue)
		s.logger.WithFields(map[string]interface{}{
			"member_username":     *member.Username,
			"game_username_nil":   member.GameUsername == nil,
			"game_username_value": gameUsernameValue,
		}).Error("DEBUG: Member does not have game_username")
		return fmt.Errorf("member %s does not have game_username", *member.Username)
	}

	// Parse amount
	amount, err := strconv.ParseFloat(payload.Amount, 64)
	if err != nil {
		return fmt.Errorf("invalid amount format: %w", err)
	}

	// Call game API to withdraw credit and get updated balance
	fmt.Printf("DEBUG: About to call game API withdraw with game_username: %s, amount: %f\n", *member.GameUsername, amount)
	s.logger.WithFields(map[string]interface{}{
		"game_username":  *member.GameUsername,
		"amount":         amount,
		"transaction_id": payload.TransactionID,
	}).Info("DEBUG: About to call game API withdraw")

	walletAfter, err := s.callGameAPIWithdraw(ctx, *member.GameUsername, amount, payload.TransactionID)
	if err != nil {
		fmt.Printf("DEBUG: Game API withdraw call failed: %v\n", err)
		s.logger.WithError(err).WithField("game_username", *member.GameUsername).Error("DEBUG: Game API withdraw call failed")
		return fmt.Errorf("failed to call game API withdraw: %w", err)
	}

	fmt.Printf("DEBUG: Game API withdraw call successful, wallet_after: %f\n", walletAfter)
	s.logger.WithField("wallet_after", walletAfter).Info("DEBUG: Game API withdraw call successful")

	// Update member balance with wallet_after from game API
	member.Balance = walletAfter
	member.UpdatedAt = time.Now()

	if err := s.paymentGatewayService.memberRepo.Update(ctx, member); err != nil {
		s.logger.WithError(err).WithField("member_id", member.ID).Error("failed to update member balance")
		return fmt.Errorf("failed to update member balance: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"transaction_id": payload.TransactionID,
		"game_username":  *member.GameUsername,
		"amount":         amount,
		"wallet_after":   walletAfter,
		"member_balance": member.Balance,
	}).Info("withdrawal debit processed and member balance updated successfully")

	// Send WebSocket notification about successful withdrawal
	memberIDInt, _ := strconv.Atoi(payload.CustomerReference)
	s.sendWithdrawalNotification(memberIDInt, payload.TransactionID, true)

	return nil
}

// callGameAPIWithdraw calls the agent API to withdraw credit and returns wallet_after
func (s *JaiJaiPayService) callGameAPIWithdraw(ctx context.Context, gameUsername string, amount float64, referenceID string) (float64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "callGameAPIWithdraw")

	// Call agent API using client
	response, err := s.agentClient.GamesWithdraw(ctx, gameUsername, amount, referenceID)
	if err != nil {
		log.WithError(err).WithFields(map[string]interface{}{
			"game_username": gameUsername,
			"amount":        amount,
			"reference_id":  referenceID,
		}).Error("failed to call agent API for games withdraw")
		return 0, fmt.Errorf("failed to call agent API for games withdraw: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"game_username":   gameUsername,
		"amount":          amount,
		"reference_id":    referenceID,
		"wallet_before":   response.WalletBefore,
		"wallet_after":    response.WalletAfter,
		"txn_id":          response.TxnID,
		"response_amount": response.Amount,
	}).Info("withdraw debit processed successfully via agent API")

	return response.WalletAfter, nil
}
