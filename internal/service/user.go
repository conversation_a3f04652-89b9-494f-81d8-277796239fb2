package service

import (
	"context"
	"strconv"
	"time"

	"blacking-api/internal/domain/login_attempt"
	"blacking-api/internal/domain/system_setting"
	"blacking-api/internal/domain/user"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

type UserService interface {
	CreateUser(ctx context.Context, req user.CreateUserRequest, adminId string, adminUsername string) (*user.UserResponse, error)
	GetUserByID(ctx context.Context, id string) (*user.UserResponse, error)
	GetUserByUsername(ctx context.Context, username string) (*user.UserResponse, error)
	UpdateUser(ctx context.Context, id string, req user.UpdateUserRequest, adminId string, adminUsername string) (*user.UserResponse, error)
	DeleteUser(ctx context.Context, id string, adminId string, adminUsername string) error
	ListUsers(ctx context.Context, limit, offset int, search string, userRoleID *int) ([]*user.UserResponse, error)
	GetUsersCount(ctx context.Context, search string, userRoleID *int) (int64, error)
	ActivateUser(ctx context.Context, id string, adminId string, adminUsername string) error
	DeactivateUser(ctx context.Context, id string, adminId string, adminUsername string) error
	SuspendUser(ctx context.Context, id string, adminId string, adminUsername string) error
	ChangePassword(ctx context.Context, id string, newPassword string, adminId string, adminUsername string) error
	ValidateUserCredentials(ctx *gin.Context, username, password string) (*user.UserResponse, error)

	// Dropdown methods
	GetUsersForDropdown(ctx context.Context, page string) (*user.UserDropdownResponse, error)
}

type userService struct {
	userRepo             interfaces.UserRepository
	userRoleRepo         interfaces.UserRoleRepository
	auditLogService      UserAuditLogService
	systemSettingService SystemSettingService
	loginAttemptRepo     interfaces.LoginAttemptRepository
	logger               logger.Logger
}

func NewUserService(userRepo interfaces.UserRepository, userRoleRepo interfaces.UserRoleRepository, auditLogService UserAuditLogService, systemSettingService SystemSettingService, loginAttemptRepo interfaces.LoginAttemptRepository, logger logger.Logger) UserService {
	return &userService{
		userRepo:             userRepo,
		userRoleRepo:         userRoleRepo,
		auditLogService:      auditLogService,
		systemSettingService: systemSettingService,
		loginAttemptRepo:     loginAttemptRepo,
		logger:               logger,
	}
}

func (s *userService) CreateUser(ctx context.Context, req user.CreateUserRequest, adminId string, adminUsername string) (*user.UserResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CreateUser")

	// Check if user with username already exists
	existingUser, err := s.userRepo.GetByUsername(ctx, req.Username)
	if err != nil && !errors.IsAppError(err) {
		log.WithError(err).Error("failed to check existing user")
		return nil, err
	}
	if existingUser != nil {
		return nil, errors.NewConflictError("user with this username already exists")
	}

	// Create new user
	newUser, err := user.NewUser(req)
	if err != nil {
		log.WithError(err).Error("failed to create user domain object")
		return nil, err
	}

	// Check User Role
	existingRole, err := s.userRoleRepo.GetByID(ctx, strconv.Itoa(req.UserRoleID))
	if err != nil && !errors.IsAppError(err) {
		log.WithError(err).Error("failed to check existing user role")
		return nil, err
	}
	if existingRole == nil {
		return nil, errors.NewConflictError("not found user role")
	}
	if existingRole != nil && !existingRole.IsEnable {
		log.WithError(err).Error("user role not enable")
		return nil, errors.NewConflictError("user role not enable")
	}

	newUser.UserRoleName = *existingRole.Name

	// Save to repository
	if err := s.userRepo.Create(ctx, newUser); err != nil {
		log.WithError(err).Error("failed to save user to repository")
		return nil, err
	}

	// Log audit trail for user creation
	if s.auditLogService != nil {
		changedByInt, err := strconv.Atoi(adminId)
		if err != nil {
			log.WithError(err).Warn("failed to convert admin ID to integer for audit log")
			changedByInt = 0 // Use 0 as fallback
		}
		changedByName := adminUsername
		auditReq := CreateUserAuditLog(newUser.ID, newUser.Username, changedByInt, changedByName, newUser)
		if err := s.auditLogService.LogUserAction(ctx, auditReq); err != nil {
			log.WithError(err).Warn("failed to log user creation audit")
			// Don't fail the operation if audit logging fails
		}
	}

	response := newUser.ToResponse()
	log.WithField("user_id", response.ID).Info("user created successfully")
	return &response, nil
}

func (s *userService) GetUserByID(ctx context.Context, id string) (*user.UserResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetUserByID").WithField("user_id", id)

	if id == "" {
		return nil, errors.NewValidationError("user ID is required")
	}

	u, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get user by ID")
		return nil, err
	}

	response := u.ToResponse()
	return &response, nil
}

func (s *userService) GetUserByUsername(ctx context.Context, username string) (*user.UserResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetUserByUsername").WithField("username", username)

	if username == "" {
		return nil, errors.NewValidationError("username is required")
	}

	u, err := s.userRepo.GetByUsername(ctx, username)
	if err != nil {
		log.WithError(err).Error("failed to get user by username")
		return nil, err
	}

	response := u.ToResponse()
	return &response, nil
}

func (s *userService) UpdateUser(ctx context.Context, id string, req user.UpdateUserRequest, adminId string, adminUsername string) (*user.UserResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateUser").WithField("user_id", id)

	if id == "" {
		return nil, errors.NewValidationError("user ID is required")
	}

	// Get existing user
	log.WithField("querying_user_id", id).Info("Querying user from database")
	u, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get user for update")
		return nil, err
	}

	// Debug: Log user data from database
	log.WithFields(map[string]interface{}{
		"user_id":        u.ID,
		"username":       u.Username,
		"user_role_id":   u.UserRoleID,
		"user_role_name": u.UserRoleName,
	}).Info("User data from database")

	// Check username uniqueness if username is being updated
	if req.Username != nil && *req.Username != u.Username {
		existingUser, err := s.userRepo.GetByUsername(ctx, *req.Username)
		if err != nil && !errors.IsNotFoundError(err) {
			log.WithError(err).Error("failed to check username uniqueness")
			return nil, err
		}
		if existingUser != nil {
			return nil, errors.NewConflictError("username already exists")
		}
	}

	// Store old values for audit log
	oldValues := map[string]interface{}{
		"username":       u.Username,
		"first_name":     u.FirstName,
		"last_name":      u.LastName,
		"user_role_id":   u.UserRoleID,
		"user_role_name": u.UserRoleName,
		"is_enable":      u.IsEnable,
	}

	if req.UserRoleID != nil && *req.UserRoleID != u.UserRoleID {
		existingRole, err := s.userRoleRepo.GetByID(ctx, strconv.Itoa(*req.UserRoleID))
		if err != nil && !errors.IsAppError(err) {
			log.WithError(err).Error("failed to check existing user role")
			return nil, err
		}
		if existingRole == nil {
			log.WithError(err).Error("not found user role")
			return nil, errors.NewConflictError("not found user role")
		}
		if !existingRole.IsEnable {
			log.WithError(err).Error("user role not enable")
			return nil, errors.NewConflictError("user role not enable")
		}
		u.UserRoleName = *existingRole.Name

		log.WithFields(map[string]interface{}{
			"user_id":          u.ID,
			"old_user_role_id": oldValues["user_role_id"],
			"new_user_role_id": *req.UserRoleID,
		}).Info("user role changed")
	} else if req.UserRoleID != nil {
		// User role unchanged, but we still need to get the role name for response
		existingRole, err := s.userRoleRepo.GetByID(ctx, strconv.Itoa(*req.UserRoleID))
		if err == nil && existingRole != nil && existingRole.Name != nil {
			u.UserRoleName = *existingRole.Name
		}

		log.WithFields(map[string]interface{}{
			"user_id":      u.ID,
			"user_role_id": *req.UserRoleID,
		}).Info("user role unchanged - skipping validation")
	}

	// Update user domain object after validation
	if err := u.Update(req); err != nil {
		log.WithError(err).Error("failed to update user domain object")
		return nil, err
	}

	// Save to repository
	if err := s.userRepo.Update(ctx, u); err != nil {
		log.WithError(err).Error("failed to save updated user")
		return nil, err
	}

	// Log audit trail for user update
	if s.auditLogService != nil {
		// Use admin info from parameters
		changedByInt, err := strconv.Atoi(adminId)
		if err != nil {
			log.WithError(err).Warn("failed to convert admin ID to integer for audit log")
			changedByInt = 0 // Use 0 as fallback
		}
		changedByName := adminUsername

		newValues := map[string]interface{}{
			"username":       u.Username,
			"first_name":     u.FirstName,
			"last_name":      u.LastName,
			"user_role_id":   u.UserRoleID,
			"user_role_name": u.UserRoleName,
			"is_enable":      u.IsEnable,
		}
		auditReq := UpdateUserAuditLog(u.ID, u.Username, changedByInt, changedByName, oldValues, newValues)
		if err := s.auditLogService.LogUserAction(ctx, auditReq); err != nil {
			log.WithError(err).Warn("failed to log user update audit")
			// Don't fail the operation if audit logging fails
		}
	}

	response := u.ToResponse()
	log.Info("user updated successfully")
	return &response, nil
}

func (s *userService) DeleteUser(ctx context.Context, id string, adminId string, adminUsername string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "DeleteUser").WithField("user_id", id)

	if id == "" {
		return errors.NewValidationError("user ID is required")
	}

	// Get user data before deletion for audit log
	var oldValues interface{}
	var targetUsername string
	if s.auditLogService != nil {
		u, err := s.userRepo.GetByID(ctx, id)
		if err == nil {
			targetUsername = u.Username
			oldValues = map[string]interface{}{
				"username":     u.Username,
				"first_name":   u.FirstName,
				"last_name":    u.LastName,
				"user_role_id": u.UserRoleID,
				"is_enable":    u.IsEnable,
				"status":       u.Status,
			}
		}
	}

	if err := s.userRepo.Delete(ctx, id); err != nil {
		log.WithError(err).Error("failed to delete user")
		return err
	}

	// Log audit trail for user deletion
	if s.auditLogService != nil && oldValues != nil {
		changedByInt, err := strconv.Atoi(adminId)
		if err != nil {
			log.WithError(err).Warn("failed to convert admin ID to integer for audit log")
			changedByInt = 0 // Use 0 as fallback
		}
		changedByName := adminUsername
		userIDInt, err := strconv.Atoi(id)
		if err != nil {
			log.WithError(err).Warn("failed to convert user ID to integer for audit log")
		} else {
			auditReq := DeleteUserAuditLog(userIDInt, targetUsername, changedByInt, changedByName, oldValues)
			if err := s.auditLogService.LogUserAction(ctx, auditReq); err != nil {
				log.WithError(err).Warn("failed to log user deletion audit")
				// Don't fail the operation if audit logging fails
			}
		}
	}

	log.Info("user deleted successfully")
	return nil
}

func (s *userService) ListUsers(ctx context.Context, limit, offset int, search string, userRoleID *int) ([]*user.UserResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListUsers")

	if limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}
	if offset < 0 {
		offset = 0
	}

	users, err := s.userRepo.List(ctx, limit, offset, search, userRoleID)
	if err != nil {
		log.WithError(err).Error("failed to list users")
		return nil, err
	}

	responses := make([]*user.UserResponse, len(users))
	for i, u := range users {
		response := u.ToResponse()
		responses[i] = &response
	}

	return responses, nil
}

func (s *userService) GetUsersCount(ctx context.Context, search string, userRoleID *int) (int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetUsersCount")

	count, err := s.userRepo.Count(ctx, search, userRoleID)
	if err != nil {
		log.WithError(err).Error("failed to get users count")
		return 0, err
	}

	return count, nil
}

func (s *userService) ActivateUser(ctx context.Context, id string, adminId string, adminUsername string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "ActivateUser").WithField("user_id", id)

	u, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get user for activation")
		return err
	}

	u.Activate()

	if err := s.userRepo.Update(ctx, u); err != nil {
		log.WithError(err).Error("failed to save activated user")
		return err
	}

	// Log audit trail for user activation
	if s.auditLogService != nil {
		changedByInt, err := strconv.Atoi(adminId)
		if err != nil {
			log.WithError(err).Warn("failed to convert admin ID to integer for audit log")
			changedByInt = 0 // Use 0 as fallback
		}
		changedByName := adminUsername
		auditReq := ActivateUserAuditLog(u.ID, u.Username, changedByInt, changedByName)
		if err := s.auditLogService.LogUserAction(ctx, auditReq); err != nil {
			log.WithError(err).Warn("failed to log user activation audit")
			// Don't fail the operation if audit logging fails
		}
	}

	log.Info("user activated successfully")
	return nil
}

func (s *userService) DeactivateUser(ctx context.Context, id string, adminId string, adminUsername string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "DeactivateUser").WithField("user_id", id)

	u, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get user for deactivation")
		return err
	}

	u.Deactivate()

	if err := s.userRepo.Update(ctx, u); err != nil {
		log.WithError(err).Error("failed to save deactivated user")
		return err
	}

	// Log audit trail for user deactivation
	if s.auditLogService != nil {
		changedByInt, err := strconv.Atoi(adminId)
		if err != nil {
			log.WithError(err).Warn("failed to convert admin ID to integer for audit log")
			changedByInt = 0 // Use 0 as fallback
		}
		changedByName := adminUsername
		auditReq := DeactivateUserAuditLog(u.ID, u.Username, changedByInt, changedByName)
		if err := s.auditLogService.LogUserAction(ctx, auditReq); err != nil {
			log.WithError(err).Warn("failed to log user deactivation audit")
			// Don't fail the operation if audit logging fails
		}
	}

	log.Info("user deactivated successfully")
	return nil
}

func (s *userService) SuspendUser(ctx context.Context, id string, adminId string, adminUsername string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "SuspendUser").WithField("user_id", id)

	u, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get user for suspension")
		return err
	}

	u.Suspend()

	if err := s.userRepo.Update(ctx, u); err != nil {
		log.WithError(err).Error("failed to save suspended user")
		return err
	}

	// Log audit trail for user suspension
	if s.auditLogService != nil {
		changedByInt, err := strconv.Atoi(adminId)
		if err != nil {
			log.WithError(err).Warn("failed to convert admin ID to integer for audit log")
			changedByInt = 0 // Use 0 as fallback
		}
		changedByName := adminUsername
		auditReq := SuspendUserAuditLog(u.ID, u.Username, changedByInt, changedByName)
		if err := s.auditLogService.LogUserAction(ctx, auditReq); err != nil {
			log.WithError(err).Warn("failed to log user suspension audit")
			// Don't fail the operation if audit logging fails
		}
	}

	log.Info("user suspended successfully")
	return nil
}

func (s *userService) ChangePassword(ctx context.Context, id string, newPassword string, adminId string, adminUsername string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "ChangePassword").WithField("user_id", id)

	u, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get user for password change")
		return err
	}

	if err := u.SetPassword(newPassword); err != nil {
		log.WithError(err).Error("failed to set new password")
		return err
	}

	if err := s.userRepo.Update(ctx, u); err != nil {
		log.WithError(err).Error("failed to save user with new password")
		return err
	}

	// Log audit trail for password change
	if s.auditLogService != nil {
		changedByInt, err := strconv.Atoi(adminId)
		if err != nil {
			log.WithError(err).Warn("failed to convert admin ID to integer for audit log")
			changedByInt = 0 // Use 0 as fallback
		}
		changedByName := adminUsername
		auditReq := PasswordChangeAuditLog(u.ID, u.Username, changedByInt, changedByName)
		if err := s.auditLogService.LogUserAction(ctx, auditReq); err != nil {
			log.WithError(err).Warn("failed to log password change audit")
			// Don't fail the operation if audit logging fails
		}
	}

	log.Info("user password changed successfully")
	return nil
}

func (s *userService) ValidateUserCredentials(ctx *gin.Context, username, password string) (*user.UserResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ValidateUserCredentials").WithField("username", username)

	if username == "" || password == "" {
		return nil, errors.NewValidationError("username and password are required")
	}

	// Get login attempt limit from system settings
	loginLimit, err := s.systemSettingService.GetLoginAttemptLimit(ctx.Request.Context())
	if err != nil {
		log.WithError(err).Warn("failed to get login attempt limit, continuing without limit")
		loginLimit = &system_setting.LoginAttemptLimitResponse{MaxAttempts: 0} // No limit
	}

	u, err := s.userRepo.GetByUsername(ctx.Request.Context(), username)
	if err != nil {
		// Record failed login attempt - now handled by middleware
		// s.recordLoginAttempt(ctx, username, false)

		if errors.GetAppError(err) != nil && errors.GetAppError(err).Type == errors.NotFoundError {
			return nil, errors.NewUnauthorizedError("ไม่พบผู้ใช้งานในระบบ")
		}
		log.WithError(err).Error("failed to get user for credential validation")
		return nil, err
	}

	// Check if user is locked due to too many failed attempts
	if loginLimit.MaxAttempts > 0 && u.IsEnable != nil && !*u.IsEnable {
		summary, err := s.loginAttemptRepo.GetAttemptsSummary(ctx.Request.Context(), username, loginLimit.MaxAttempts)
		if err != nil {
			log.WithError(err).Warn("failed to get login attempts summary")
		} else if summary.IsLocked {
			log.WithField("failed_attempts", summary.FailedAttempts).Warn("user is locked due to too many failed login attempts")
			return nil, errors.NewUnauthorizedError("account is temporarily locked due to too many failed login attempts")
		}
	}

	if !u.CheckPassword(password) {
		// Record failed login attempt - now handled by middleware
		// s.recordLoginAttempt(ctx, username, false)

		// Check if this failed attempt should lock the account
		if loginLimit.MaxAttempts > 0 {
			failedCount, err := s.loginAttemptRepo.GetFailedAttemptsCount(ctx.Request.Context(), username, time.Now().Add(-24*time.Hour))
			if err == nil && failedCount >= loginLimit.MaxAttempts {
				// Disable user account by setting is_enable = false
				falseValue := false
				u.IsEnable = &falseValue
				u.UpdatedAt = time.Now()
				if updateErr := s.userRepo.Update(ctx.Request.Context(), u); updateErr != nil {
					log.WithError(updateErr).Error("failed to disable user account after max login attempts")
				} else {
					// Log audit trail for account lockout
					s.logAccountLockout(ctx.Request.Context(), u.ID, u.Username, failedCount)
					log.WithField("failed_attempts", failedCount).Warn("user account disabled due to max login attempts exceeded")
				}
			}
		}

		return nil, errors.NewUnauthorizedError("รหัสผ่านไม่ถูกต้อง")
	}

	if !u.IsActive() {
		// Record failed login attempt (account not active)
		s.recordLoginAttempt(ctx, username, false)
		return nil, errors.NewUnauthorizedError("บัญชีผู้ใช้ไม่ได้เปิดใช้งาน")
	}

	// Record successful login attempt
	s.recordLoginAttempt(ctx, username, true)

	// Log successful login to audit trail
	// s.logSuccessfulLogin(ctx, u)

	response := u.ToResponse()
	log.Info("user credentials validated successfully")
	return &response, nil
}

// recordLoginAttempt records a login attempt (success or failure)
func (s *userService) recordLoginAttempt(ctx *gin.Context, username string, success bool) {
	// Extract IP address and user agent from gin context
	ipAddress := ctx.ClientIP()
	userAgent := ctx.Request.UserAgent()

	// For admin users, set isAdmin=true, isMember=false
	// This is for admin login attempts
	attempt := login_attempt.NewLoginAttempt(username, ipAddress, userAgent, success, true, false)
	if err := s.loginAttemptRepo.Create(ctx.Request.Context(), attempt); err != nil {
		s.logger.WithError(err).WithField("username", username).Error("failed to record login attempt")
	}
}

// logAccountLockout logs an audit trail when account is locked due to max login attempts
func (s *userService) logAccountLockout(ctx context.Context, userID int, username string, failedAttempts int) {
	if s.auditLogService != nil {
		changedBy := 0 // System action (use 0 for system)
		changedByName := "System"
		oldValues := map[string]interface{}{
			"is_enable": true,
		}
		newValues := map[string]interface{}{
			"is_enable":       false,
			"lockout_reason":  "max_login_attempts_exceeded",
			"failed_attempts": failedAttempts,
		}

		auditReq := UpdateUserAuditLog(userID, username, changedBy, changedByName, oldValues, newValues)
		if err := s.auditLogService.LogUserAction(ctx, auditReq); err != nil {
			s.logger.WithError(err).Warn("failed to log account lockout audit")
		}
	}
}

// GetUsersForDropdown returns users formatted for dropdown with special handling for page parameter
func (s *userService) GetUsersForDropdown(ctx context.Context, page string) (*user.UserDropdownResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetUsersForDropdown").WithField("page", page)

	users, err := s.userRepo.GetForDropdown(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get users for dropdown")
		return nil, err
	}

	// If page is "member", add special entry for "ลูกค้าสมัครเอง"
	if page == "member" {
		specialEntry := user.UserDropdownItem{
			ID:   0,
			Name: "ลูกค้าสมัครเอง",
		}
		// Insert at the beginning
		users = append([]user.UserDropdownItem{specialEntry}, users...)
	}

	response := &user.UserDropdownResponse{
		Users: users,
	}

	log.WithField("total_users", len(users)).WithField("has_special_entry", page == "member").Info("users for dropdown retrieved successfully")
	return response, nil
}
