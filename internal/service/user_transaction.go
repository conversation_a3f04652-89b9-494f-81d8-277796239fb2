package service

import (
	"blacking-api/internal/domain/bank_transaction_slip"
	"blacking-api/internal/domain/response"
	"blacking-api/internal/domain/user_transaction"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"fmt"
	"net/http"
	"time"
)

type UserTransactionService interface {
	// Transaction operations
	CreateDeposit(ctx context.Context, req *user_transaction.CreateUserTransactionAdminDepositRequest) error
	CreateWithdraw(ctx context.Context, req *user_transaction.CreateUserTransactionWithdrawRequest) (*user_transaction.UserTransactionResponse, error)
	CreateWebWithdraw(ctx context.Context, memberID int64, req *user_transaction.CreateUserTransactionWebWithdrawRequest) (*user_transaction.UserTransactionResponse, error)
	CreateTransfer(ctx context.Context, req *user_transaction.CreateUserTransactionTransferRequest) (*user_transaction.UserTransactionResponse, error)
	CreateWebDeposit(ctx context.Context, memberID int64, req *user_transaction.CreateUserTransactionWebDepositRequest) (*user_transaction.UserTransactionResponse, error)

	// File upload operations
	UploadSlip(ctx context.Context, fileBody *http.Request) (string, error)

	// Pagination operations
	GetDepositPage(ctx context.Context, req *user_transaction.UserTransactionDepositPageRequest) (*response.SuccessWithPagination, error)
	GetWithdrawPage(ctx context.Context, req *user_transaction.UserTransactionWithdrawPageRequest) (*response.SuccessWithPagination, error)
	GetTransferPage(ctx context.Context, req *user_transaction.UserTransactionTransferPageRequest) (*response.SuccessWithPagination, error)

	// Individual transaction retrieval operations
	GetDepositByID(ctx context.Context, id int64) (*user_transaction.UserTransactionResponse, error)
	GetWithdrawByID(ctx context.Context, id int64) (*user_transaction.UserTransactionResponse, error)
	GetTransferByID(ctx context.Context, id int64) (*user_transaction.UserTransactionResponse, error)

	// User-specific transaction list operations
	GetDepositsByMemberID(ctx context.Context, memberID int, req *user_transaction.UserTransactionDepositByUserRequest) (*response.SuccessWithPagination, error)
	GetWithdrawsByMemberID(ctx context.Context, memberID int, req *user_transaction.UserTransactionWithdrawByUserRequest) (*response.SuccessWithPagination, error)

	// Status update operations
	UpdateDepositStatus(ctx context.Context, id int64, req *user_transaction.UpdateUserTransactionStatusRequest) error
	UpdateWithdrawStatus(ctx context.Context, id int64, req *user_transaction.UpdateUserTransactionStatusRequest) error
	UpdateTransferStatus(ctx context.Context, id int64, req *user_transaction.UpdateUserTransactionStatusRequest) error
}

type userTransactionService struct {
	userTransactionRepo       interfaces.UserTransactionRepository
	bankingRepo               interfaces.BankingRepository
	depositAccountRepo        interfaces.DepositAccountRepository
	paymentGatewayAccountRepo interfaces.PaymentGatewayAccountRepository
	bankTransactionSlipRepo   interfaces.BankTransactionSlipRepository
	memberRepo                interfaces.MemberRepository
	awsS3Repo                 interfaces.AWSS3Repository
	logger                    logger.Logger
}

func NewUserTransactionService(
	userTransactionRepo interfaces.UserTransactionRepository,
	bankingRepo interfaces.BankingRepository,
	depositAccountRepo interfaces.DepositAccountRepository,
	paymentGatewayAccountRepo interfaces.PaymentGatewayAccountRepository,
	memberRepo interfaces.MemberRepository,
	logger logger.Logger,
) UserTransactionService {
	return &userTransactionService{
		userTransactionRepo:       userTransactionRepo,
		bankingRepo:               bankingRepo,
		depositAccountRepo:        depositAccountRepo,
		paymentGatewayAccountRepo: paymentGatewayAccountRepo,
		memberRepo:                memberRepo,
		logger:                    logger,
	}
}

// NewUserTransactionServiceWithSlip creates a new user transaction service with bank slip support
func NewUserTransactionServiceWithSlip(
	userTransactionRepo interfaces.UserTransactionRepository,
	bankingRepo interfaces.BankingRepository,
	depositAccountRepo interfaces.DepositAccountRepository,
	paymentGatewayAccountRepo interfaces.PaymentGatewayAccountRepository,
	bankTransactionSlipRepo interfaces.BankTransactionSlipRepository,
	memberRepo interfaces.MemberRepository,
	logger logger.Logger,
) UserTransactionService {
	return &userTransactionService{
		userTransactionRepo:       userTransactionRepo,
		bankingRepo:               bankingRepo,
		depositAccountRepo:        depositAccountRepo,
		paymentGatewayAccountRepo: paymentGatewayAccountRepo,
		bankTransactionSlipRepo:   bankTransactionSlipRepo,
		memberRepo:                memberRepo,
		logger:                    logger,
	}
}

// CreateDeposit creates a new deposit transaction with QR verification
func (s *userTransactionService) CreateDeposit(ctx context.Context, req *user_transaction.CreateUserTransactionAdminDepositRequest) error {
	// Validate deposit account ID exists in either DepositAccount or PaymentGatewayAccount
	var validAccount bool

	// First check in DepositAccount
	if s.depositAccountRepo != nil {
		exists, checkErr := s.depositAccountRepo.FindIdExists(ctx, int64(req.DepositAccountId))
		if checkErr == nil && exists {
			validAccount = true
		}
	}

	// If not found in DepositAccount, check in PaymentGatewayAccount
	if !validAccount && s.paymentGatewayAccountRepo != nil {
		exists, checkErr := s.paymentGatewayAccountRepo.FindIdExists(ctx, int64(req.DepositAccountId))
		if checkErr == nil && exists {
			validAccount = true
		}
	}

	// If not found in either, return error
	if !validAccount {
		return errors.NewValidationError("invalid deposit account ID - account not found in DepositAccount or PaymentGatewayAccount")
	}

	// Determine member ID
	var memberID int
	var err error
	if req.PhoneOrUsername != "" && s.memberRepo != nil {
		// Try to find member by phone first
		member, err := s.memberRepo.GetByPhone(ctx, req.PhoneOrUsername)
		if err != nil {
			// If not found by phone, try username
			member, err = s.memberRepo.GetByUsername(ctx, req.PhoneOrUsername)
			if err != nil {
				return errors.NewValidationError("member not found with phone or username: " + req.PhoneOrUsername)
			}
		}
		// Use member ID directly (it's already an int)
		if member != nil {
			memberID = member.ID
		}
	} else {
		return errors.NewValidationError("phone or username is required")
	}

	// Parse deposited at time
	var depositedAt time.Time
	if req.Date != "" && req.Time != "" {
		// Fall back to date and time fields
		depositedAt, err = s.parseDateTime(req.Date, req.Time)
		if err != nil {
			return errors.NewValidationError("invalid date or time format")
		}
	} else {
		depositedAt = time.Now()
	}

	// Generate reference ID
	refID, err := s.userTransactionRepo.GenerateRefID(ctx)
	if err != nil {
		return err
	}

	// Create transaction entity
	transaction := &user_transaction.UserTransaction{
		DirectionID:  user_transaction.USER_TRANSACTION_DIRECTION_DEPOSIT, // DEPOSIT
		TypeID:       user_transaction.USER_TRANSACTION_TYPE_DEPOSIT,      // DEPOSIT type
		MemberID:     memberID,
		BankingID:    req.DepositAccountId,
		RefID:        refID,
		Detail:       &req.Description,
		Date:         depositedAt,
		CreditAmount: req.DepositAmount,
		CreditBefore: 0, // Will be calculated based on member's current balance
		CreditAfter:  0, // Will be calculated after credit
		CreditBack:   0,
		BonusAmount:  0,
		PromotionID:  req.PromotionID,
		StatusID:     user_transaction.USER_TRANSACTION_STATUS_WAITING_FOR_TRANSFER,
	}

	// Create the transaction
	createdTransaction, err := s.userTransactionRepo.Create(ctx, transaction)
	if err != nil {
		return err
	}

	// Create bank transaction slip if SlipUrl is provided
	if req.SlipUrl != "" && s.bankTransactionSlipRepo != nil {
		slip := &bank_transaction_slip.BankTransactionSlip{
			MemberID:          int64(memberID),
			Status:            bank_transaction_slip.StatusPending,
			TransactionID:     createdTransaction.ID,
			SlipUrl:           &req.SlipUrl,
			FromAccountNumber: "", // Can be populated from QR data if available
			FromAccountName:   "", // Can be populated from QR data if available
			FromBankName:      "", // Can be populated from QR data if available
			ToAccountNumber:   "", // Can be populated from banking info
			ToAccountName:     "", // Can be populated from banking info
			Amount:            req.DepositAmount,
			TransactionDate:   depositedAt,
			Remark:            req.Description,
		}

		_, err = s.bankTransactionSlipRepo.Create(ctx, slip)
		if err != nil {
			s.logger.WithError(err).Error("failed to create bank transaction slip")
			// Don't fail the transaction, just log the error
		}
	}

	// Log transaction creation
	s.logger.Info("Deposit transaction created successfully")

	return nil
}

// CreateWithdraw creates a new withdraw transaction (admin)
func (s *userTransactionService) CreateWithdraw(ctx context.Context, req *user_transaction.CreateUserTransactionWithdrawRequest) (*user_transaction.UserTransactionResponse, error) {
	// Get member ID by phone or username
	var memberID int
	if req.PhoneOrUsername != "" && s.memberRepo != nil {
		// Try to find member by phone first
		member, err := s.memberRepo.GetByPhone(ctx, req.PhoneOrUsername)
		if err != nil {
			// If not found by phone, try username
			member, err = s.memberRepo.GetByUsername(ctx, req.PhoneOrUsername)
			if err != nil {
				return nil, errors.NewValidationError("member not found with phone or username: " + req.PhoneOrUsername)
			}
		}
		memberID = member.ID
	} else {
		return nil, errors.NewValidationError("phone or username is required")
	}

	// Generate reference ID
	refID, err := s.userTransactionRepo.GenerateRefID(ctx)
	if err != nil {
		return nil, err
	}

	// Create transaction entity
	transaction := &user_transaction.UserTransaction{
		DirectionID:  user_transaction.USER_TRANSACTION_DIRECTION_WITHDRAW, // WITHDRAW
		TypeID:       user_transaction.USER_TRANSACTION_TYPE_WITHDRAW,      // WITHDRAW type
		MemberID:     memberID,
		BankingID:    1, // Default banking for withdrawals
		RefID:        refID,
		Detail:       &req.Description,
		Date:         time.Now(),
		CreditAmount: req.CreditAmount,
		CreditBefore: 0, // Will be calculated based on member's current balance
		CreditAfter:  0, // Will be calculated after debit
		CreditBack:   0,
		BonusAmount:  0,
		StatusID:     user_transaction.USER_TRANSACTION_STATUS_WAITING_VERIFICATION, // Waiting for verification
	}

	// Create the transaction
	createdTransaction, err := s.userTransactionRepo.Create(ctx, transaction)
	if err != nil {
		return nil, err
	}

	// Get the full transaction with joined data
	fullTransaction, err := s.userTransactionRepo.GetByID(ctx, createdTransaction.ID)
	if err != nil {
		return nil, err
	}

	response := fullTransaction.ToResponse()
	return &response, nil
}

// CreateWebWithdraw creates a new withdraw transaction for a specific member
func (s *userTransactionService) CreateWebWithdraw(ctx context.Context, memberID int64, req *user_transaction.CreateUserTransactionWebWithdrawRequest) (*user_transaction.UserTransactionResponse, error) {
	// Validate withdraw bank ID exists
	if s.bankingRepo != nil {
		exists, err := s.bankingRepo.FindIdExists(ctx, int64(req.WithdrawBankId))
		if err != nil || !exists {
			return nil, errors.NewValidationError("invalid withdraw bank ID")
		}
	}

	// Generate reference ID
	refID, err := s.userTransactionRepo.GenerateRefID(ctx)
	if err != nil {
		return nil, err
	}

	// Create transaction entity using the provided member ID
	transaction := &user_transaction.UserTransaction{
		DirectionID:  user_transaction.USER_TRANSACTION_DIRECTION_WITHDRAW, // WITHDRAW
		TypeID:       user_transaction.USER_TRANSACTION_TYPE_WITHDRAW,      // WITHDRAW type
		MemberID:     int(memberID),
		BankingID:    req.WithdrawBankId,
		RefID:        refID,
		Detail:       &req.Description,
		Date:         time.Now(),
		CreditAmount: req.CreditAmount,
		CreditBefore: 0, // Will be calculated based on member's current balance
		CreditAfter:  0, // Will be calculated after debit
		CreditBack:   0,
		BonusAmount:  0,
		StatusID:     user_transaction.USER_TRANSACTION_STATUS_WAITING_VERIFICATION, // Waiting for verification
	}

	// Create the transaction
	createdTransaction, err := s.userTransactionRepo.Create(ctx, transaction)
	if err != nil {
		return nil, err
	}

	// TODO: Store member's bank account details (AccountNumber, AccountName) in a separate table for withdrawals
	// This information should be linked to the transaction for processing

	// Get the full transaction with joined data for response
	fullTransaction, err := s.userTransactionRepo.GetByID(ctx, createdTransaction.ID)
	if err != nil {
		// If we can't get the full data, return a basic response
		return &user_transaction.UserTransactionResponse{
			ID:         createdTransaction.ID,
			StatusID:   user_transaction.USER_TRANSACTION_STATUS_WAITING_VERIFICATION,
			StatusName: "Waiting for Verification",
		}, nil
	}

	response := fullTransaction.ToResponse()
	return &response, nil
}

// CreateTransfer creates a new transfer transaction
func (s *userTransactionService) CreateTransfer(ctx context.Context, req *user_transaction.CreateUserTransactionTransferRequest) (*user_transaction.UserTransactionResponse, error) {
	// Validate banking IDs exist
	exists, err := s.bankingRepo.FindIdExists(ctx, int64(req.BankingID))
	if err != nil || !exists {
		return nil, errors.NewValidationError("invalid source banking ID")
	}

	exists, err = s.bankingRepo.FindIdExists(ctx, int64(req.TransferBankingID))
	if err != nil || !exists {
		return nil, errors.NewValidationError("invalid destination banking ID")
	}

	// Generate reference ID
	refID, err := s.userTransactionRepo.GenerateRefID(ctx)
	if err != nil {
		return nil, err
	}

	// For transfers, we need a system member ID
	// Try to get system member ID or use the first available member
	systemMemberID, err := s.getSystemMemberID(ctx)
	if err != nil {
		// If no system member found, log warning but continue with default member ID
		s.logger.Warn("No system member found for transfer operation, using default member ID 1")
		systemMemberID = 1 // Use default system member ID
	}

	transaction := &user_transaction.UserTransaction{
		DirectionID:       user_transaction.USER_TRANSACTION_DIRECTION_DEPOSIT, // Transfers are recorded as deposits
		TypeID:            user_transaction.USER_TRANSACTION_TYPE_DEPOSIT,      // Use deposit type for transfers
		MemberID:          systemMemberID,
		BankingID:         req.BankingID,
		TransferBankingID: &req.TransferBankingID,
		RefID:             refID,
		Detail:            &req.Description,
		Date:              time.Now(),
		CreditAmount:      req.CreditAmount,
		CreditBefore:      0,
		CreditAfter:       0,
		CreditBack:        0,
		BonusAmount:       0,
		StatusID:          user_transaction.USER_TRANSACTION_STATUS_SUCCESS, // Transfers are immediately successful
	}

	// Create the transaction
	createdTransaction, err := s.userTransactionRepo.Create(ctx, transaction)
	if err != nil {
		return nil, err
	}

	// Get the full transaction with joined data
	fullTransaction, err := s.userTransactionRepo.GetByID(ctx, createdTransaction.ID)
	if err != nil {
		return nil, err
	}

	response := fullTransaction.ToResponse()
	return &response, nil
}

// CreateWebDeposit creates a new web deposit transaction for a specific member
func (s *userTransactionService) CreateWebDeposit(ctx context.Context, memberID int64, req *user_transaction.CreateUserTransactionWebDepositRequest) (*user_transaction.UserTransactionResponse, error) {
	// Validate deposit account ID exists in either DepositAccount or PaymentGatewayAccount
	var validAccount bool

	// First check in DepositAccount
	if s.depositAccountRepo != nil {
		exists, checkErr := s.depositAccountRepo.FindIdExists(ctx, int64(req.DepositAccountId))
		if checkErr == nil && exists {
			validAccount = true
		}
	}

	// If not found in DepositAccount, check in PaymentGatewayAccount
	if !validAccount && s.paymentGatewayAccountRepo != nil {
		exists, checkErr := s.paymentGatewayAccountRepo.FindIdExists(ctx, int64(req.DepositAccountId))
		if checkErr == nil && exists {
			validAccount = true
		}
	}

	// If not found in either, return error
	if !validAccount {
		return nil, errors.NewValidationError("invalid deposit account ID - account not found in DepositAccount or PaymentGatewayAccount")
	}

	// Parse deposited at time
	var depositedAt time.Time
	var err error
	if req.Date != "" && req.Time != "" {
		depositedAt, err = s.parseDateTime(req.Date, req.Time)
		if err != nil {
			return nil, errors.NewValidationError("invalid date or time format")
		}
	} else {
		depositedAt = time.Now()
	}

	// Generate reference ID
	refID, err := s.userTransactionRepo.GenerateRefID(ctx)
	if err != nil {
		return nil, err
	}

	// Create transaction entity using the provided member ID
	transaction := &user_transaction.UserTransaction{
		DirectionID:  user_transaction.USER_TRANSACTION_DIRECTION_DEPOSIT, // DEPOSIT
		TypeID:       user_transaction.USER_TRANSACTION_TYPE_DEPOSIT,      // DEPOSIT type
		MemberID:     int(memberID),
		BankingID:    req.DepositAccountId,
		RefID:        refID,
		Detail:       &req.Description,
		Date:         depositedAt,
		CreditAmount: req.DepositAmount,
		CreditBefore: 0, // Will be calculated based on member's current balance
		CreditAfter:  0, // Will be calculated after credit
		CreditBack:   0,
		BonusAmount:  0,
		PromotionID:  req.PromotionID,
		StatusID:     user_transaction.USER_TRANSACTION_STATUS_WAITING_FOR_TRANSFER,
	}

	// Create the transaction
	createdTransaction, err := s.userTransactionRepo.Create(ctx, transaction)
	if err != nil {
		return nil, err
	}

	// Create bank transaction slip if SlipUrl is provided
	if req.SlipUrl != "" && s.bankTransactionSlipRepo != nil {
		slip := &bank_transaction_slip.BankTransactionSlip{
			MemberID:          memberID,
			Status:            bank_transaction_slip.StatusPending,
			TransactionID:     createdTransaction.ID,
			SlipUrl:           &req.SlipUrl,
			FromAccountNumber: "",
			FromAccountName:   "",
			FromBankName:      "",
			ToAccountNumber:   "",
			ToAccountName:     "",
			Amount:            req.DepositAmount,
			TransactionDate:   depositedAt,
			Remark:            req.Description,
		}

		_, err = s.bankTransactionSlipRepo.Create(ctx, slip)
		if err != nil {
			s.logger.WithError(err).Error("failed to create bank transaction slip")
			// Don't fail the transaction, just log the error
		}
	}

	// Get the full transaction with joined data for response
	fullTransaction, err := s.userTransactionRepo.GetByID(ctx, createdTransaction.ID)
	if err != nil {
		// If we can't get the full data, return a basic response
		return &user_transaction.UserTransactionResponse{
			ID:         createdTransaction.ID,
			StatusID:   user_transaction.USER_TRANSACTION_STATUS_WAITING_FOR_TRANSFER,
			StatusName: "Waiting for Transfer",
		}, nil
	}

	response := fullTransaction.ToResponse()
	return &response, nil
}

// parseDateTime parses date and time strings into a time.Time
func (s *userTransactionService) parseDateTime(dateStr, timeStr string) (time.Time, error) {
	// Combine date and time strings
	dateTimeStr := fmt.Sprintf("%s %s", dateStr, timeStr)

	// Try different time formats
	formats := []string{
		"2006-01-02 15:04:05",
		"2006-01-02 15:04",
		"02/01/2006 15:04:05",
		"02/01/2006 15:04",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, dateTimeStr); err == nil {
			return t, nil
		}
	}

	return time.Time{}, fmt.Errorf("unable to parse date time: %s", dateTimeStr)
}

// GetDepositPage retrieves paginated deposit transactions
func (s *userTransactionService) GetDepositPage(ctx context.Context, req *user_transaction.UserTransactionDepositPageRequest) (*response.SuccessWithPagination, error) {
	// Set default pagination values
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}

	transactions, total, err := s.userTransactionRepo.GetDepositPage(ctx, req)
	if err != nil {
		return nil, err
	}

	// Convert to response DTOs
	var responseData []user_transaction.UserTransactionResponse
	for _, transaction := range transactions {
		responseData = append(responseData, transaction.ToResponse())
	}

	return &response.SuccessWithPagination{
		Message:    "Deposit transactions retrieved successfully",
		Content:    responseData,
		Page:       int64(req.Page),
		Limit:      int64(req.Limit),
		TotalItems: total,
		TotalPages: (total + int64(req.Limit) - 1) / int64(req.Limit),
	}, nil
}

// GetWithdrawPage retrieves paginated withdraw transactions
func (s *userTransactionService) GetWithdrawPage(ctx context.Context, req *user_transaction.UserTransactionWithdrawPageRequest) (*response.SuccessWithPagination, error) {
	// Set default pagination values
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}

	transactions, total, err := s.userTransactionRepo.GetWithdrawPage(ctx, req)
	if err != nil {
		return nil, err
	}

	// Convert to response DTOs
	var responseData []user_transaction.UserTransactionResponse
	for _, transaction := range transactions {
		responseData = append(responseData, transaction.ToResponse())
	}

	return &response.SuccessWithPagination{
		Message:    "Withdraw transactions retrieved successfully",
		Content:    responseData,
		Page:       int64(req.Page),
		Limit:      int64(req.Limit),
		TotalItems: total,
		TotalPages: (total + int64(req.Limit) - 1) / int64(req.Limit),
	}, nil
}

// GetTransferPage retrieves paginated transfer transactions
func (s *userTransactionService) GetTransferPage(ctx context.Context, req *user_transaction.UserTransactionTransferPageRequest) (*response.SuccessWithPagination, error) {
	// Set default pagination values
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}

	transactions, total, err := s.userTransactionRepo.GetTransferPage(ctx, req)
	if err != nil {
		return nil, err
	}

	// Convert to response DTOs
	var responseData []user_transaction.UserTransactionResponse
	for _, transaction := range transactions {
		responseData = append(responseData, transaction.ToResponse())
	}

	return &response.SuccessWithPagination{
		Message:    "Transfer transactions retrieved successfully",
		Content:    responseData,
		Page:       int64(req.Page),
		Limit:      int64(req.Limit),
		TotalItems: total,
		TotalPages: (total + int64(req.Limit) - 1) / int64(req.Limit),
	}, nil
}

// GetDepositByID retrieves a specific deposit transaction by ID
func (s *userTransactionService) GetDepositByID(ctx context.Context, id int64) (*user_transaction.UserTransactionResponse, error) {
	transaction, err := s.userTransactionRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// Verify it's a deposit transaction
	if transaction.DirectionID != 1 {
		return nil, errors.NewNotFoundError("deposit transaction not found")
	}

	response := transaction.ToResponse()
	return &response, nil
}

// GetWithdrawByID retrieves a specific withdraw transaction by ID
func (s *userTransactionService) GetWithdrawByID(ctx context.Context, id int64) (*user_transaction.UserTransactionResponse, error) {
	transaction, err := s.userTransactionRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// Verify it's a withdrawal transaction
	if transaction.DirectionID != 2 {
		return nil, errors.NewNotFoundError("withdraw transaction not found")
	}

	response := transaction.ToResponse()
	return &response, nil
}

// GetTransferByID retrieves a specific transfer transaction by ID
func (s *userTransactionService) GetTransferByID(ctx context.Context, id int64) (*user_transaction.UserTransactionResponse, error) {
	transaction, err := s.userTransactionRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// Verify it's a transfer transaction (has transfer_banking_id)
	if transaction.TransferBankingID == nil {
		return nil, errors.NewNotFoundError("transfer transaction not found")
	}

	response := transaction.ToResponse()
	return &response, nil
}

// GetDepositsByMemberID retrieves paginated deposit transactions for a specific member
func (s *userTransactionService) GetDepositsByMemberID(ctx context.Context, memberID int, req *user_transaction.UserTransactionDepositByUserRequest) (*response.SuccessWithPagination, error) {
	// Set default pagination values
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}

	transactions, total, err := s.userTransactionRepo.GetDepositsByMemberID(ctx, memberID, req)
	if err != nil {
		return nil, err
	}

	// Convert to response DTOs
	var responseData []user_transaction.UserTransactionResponse
	for _, transaction := range transactions {
		responseData = append(responseData, transaction.ToResponse())
	}

	return &response.SuccessWithPagination{
		Message:    "User deposit transactions retrieved successfully",
		Content:    responseData,
		Page:       int64(req.Page),
		Limit:      int64(req.Limit),
		TotalItems: total,
		TotalPages: (total + int64(req.Limit) - 1) / int64(req.Limit),
	}, nil
}

// GetWithdrawsByMemberID retrieves paginated withdraw transactions for a specific member
func (s *userTransactionService) GetWithdrawsByMemberID(ctx context.Context, memberID int, req *user_transaction.UserTransactionWithdrawByUserRequest) (*response.SuccessWithPagination, error) {
	// Set default pagination values
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}

	transactions, total, err := s.userTransactionRepo.GetWithdrawsByMemberID(ctx, memberID, req)
	if err != nil {
		return nil, err
	}

	// Convert to response DTOs
	var responseData []user_transaction.UserTransactionResponse
	for _, transaction := range transactions {
		responseData = append(responseData, transaction.ToResponse())
	}

	return &response.SuccessWithPagination{
		Message:    "User withdraw transactions retrieved successfully",
		Content:    responseData,
		Page:       int64(req.Page),
		Limit:      int64(req.Limit),
		TotalItems: total,
		TotalPages: (total + int64(req.Limit) - 1) / int64(req.Limit),
	}, nil
}

// UpdateDepositStatus updates the status of a deposit transaction
func (s *userTransactionService) UpdateDepositStatus(ctx context.Context, id int64, req *user_transaction.UpdateUserTransactionStatusRequest) error {
	// Verify the transaction exists and is a deposit
	transaction, err := s.userTransactionRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	if transaction.DirectionID != 1 {
		return errors.NewValidationError("transaction is not a deposit")
	}

	return s.userTransactionRepo.UpdateStatus(ctx, id, req)
}

// UpdateWithdrawStatus updates the status of a withdraw transaction
func (s *userTransactionService) UpdateWithdrawStatus(ctx context.Context, id int64, req *user_transaction.UpdateUserTransactionStatusRequest) error {
	// Verify the transaction exists and is a withdrawal
	transaction, err := s.userTransactionRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	if transaction.DirectionID != 2 {
		return errors.NewValidationError("transaction is not a withdrawal")
	}

	return s.userTransactionRepo.UpdateStatus(ctx, id, req)
}

// UpdateTransferStatus updates the status of a transfer transaction
func (s *userTransactionService) UpdateTransferStatus(ctx context.Context, id int64, req *user_transaction.UpdateUserTransactionStatusRequest) error {
	// Verify the transaction exists and is a transfer
	transaction, err := s.userTransactionRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	if transaction.TransferBankingID == nil {
		return errors.NewValidationError("transaction is not a transfer")
	}

	return s.userTransactionRepo.UpdateStatus(ctx, id, req)
}

// getSystemMemberID gets the first available member ID for system operations
// This is a temporary solution - in production, there should be a dedicated system member
func (s *userTransactionService) getSystemMemberID(ctx context.Context) (int, error) {
	// Try to find a system member by username
	if s.memberRepo != nil {
		// Try to find member with username "system"
		member, err := s.memberRepo.GetByUsername(ctx, "system")
		if err == nil && member != nil {
			return member.ID, nil
		}

		// If no system member exists, try to find member with ID 1 (usually the first/system member)
		// This is a fallback - in production, there should be a dedicated system member
		s.logger.Warn("No 'system' member found, attempting to use default member ID 1")
		return 1, nil
	}

	// If memberRepo is not available, use default
	s.logger.Warn("Member repository not available, using default member ID 1 for transfer")
	return 1, nil
}

// UploadSlip uploads a transaction slip image
func (s *userTransactionService) UploadSlip(ctx context.Context, fileBody *http.Request) (string, error) {
	// Check if AWS S3 repository is available
	if s.awsS3Repo == nil {
		return "", errors.NewInternalError("AWS S3 repository not configured")
	}

	// Get the file from request
	fileReader, _, err := fileBody.FormFile("file")
	if err != nil {
		s.logger.WithError(err).Error("failed to read file from request")
		return "", errors.NewValidationError("failed to read file from request")
	}

	// Set the path for transaction slips
	pathName := "backoffice/transaction-slips/"

	// Upload file to S3
	fileData, err := s.awsS3Repo.UploadFileToS3(ctx, pathName, fileReader)
	if err != nil {
		s.logger.WithError(err).Error("failed to upload file to S3")
		return "", errors.NewValidationError("failed to upload file to S3")
	}

	return fileData.FileUrl, nil
}
