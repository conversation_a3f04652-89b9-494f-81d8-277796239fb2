package service

import (
	"context"
	"fmt"
	"net/http"
	"strconv"

	"blacking-api/internal/domain/member_group"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
)

// MemberGroupService defines the interface for member group service operations
type MemberGroupService interface {
	CreateMemberGroup(ctx context.Context, req member_group.CreateMemberGroupRequest) (*member_group.MemberGroupResponse, error)
	GetMemberGroupByID(ctx context.Context, id int) (*member_group.MemberGroupResponse, error)
	GetMemberGroupByCode(ctx context.Context, code string) (*member_group.MemberGroupResponse, error)
	GetDefaultMemberGroup(ctx context.Context) (*member_group.MemberGroupResponse, error)
	UpdateMemberGroup(ctx context.Context, id int, req member_group.UpdateMemberGroupRequest) (*member_group.MemberGroupResponse, error)
	UpdateMemberGroupWithNewFile(ctx context.Context, id int, req member_group.UpdateMemberGroupRequest) (*member_group.MemberGroupResponse, error)
	DeleteMemberGroup(ctx context.Context, id int) error
	ListMemberGroups(ctx context.Context, limit, offset int, search, sortBy, sortOrder string) ([]*member_group.MemberGroupResponse, int64, error)
	ListActiveMemberGroups(ctx context.Context) ([]*member_group.MemberGroupResponse, error)
	ListMemberGroupsForDropdown(ctx context.Context) ([]*member_group.MemberGroupDropdownResponse, error)
	SetDefaultMemberGroup(ctx context.Context, id int) error

	// File upload methods
	FileUpload(ctx context.Context, fileBody *http.Request, fieldName string) (*member_group.FileUploadResponse, error)
	DeleteFile(ctx context.Context, req *member_group.DeleteFileRequest) error
}

type memberGroupService struct {
	memberGroupRepo     interfaces.MemberGroupRepository
	userRoleRepo        interfaces.UserRoleRepository
	commissionGroupRepo interfaces.CommissionGroupRepository
	memberGroupTypeRepo interfaces.MemberGroupTypeRepository
	depositAccountRepo  interfaces.DepositAccountRepository
	awsS3Repo           interfaces.AWSS3Repository
	logger              logger.Logger
}

// NewMemberGroupService creates a new member group service
func NewMemberGroupService(memberGroupRepo interfaces.MemberGroupRepository, userRoleRepo interfaces.UserRoleRepository, commissionGroupRepo interfaces.CommissionGroupRepository, memberGroupTypeRepo interfaces.MemberGroupTypeRepository, depositAccountRepo interfaces.DepositAccountRepository, awsS3Repo interfaces.AWSS3Repository, logger logger.Logger) MemberGroupService {
	return &memberGroupService{
		memberGroupRepo:     memberGroupRepo,
		userRoleRepo:        userRoleRepo,
		commissionGroupRepo: commissionGroupRepo,
		memberGroupTypeRepo: memberGroupTypeRepo,
		depositAccountRepo:  depositAccountRepo,
		awsS3Repo:           awsS3Repo,
		logger:              logger,
	}
}

// CreateMemberGroup creates a new member group
func (s *memberGroupService) CreateMemberGroup(ctx context.Context, req member_group.CreateMemberGroupRequest) (*member_group.MemberGroupResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CreateMemberGroup")

	// Check if member group code already exists
	existingMemberGroup, err := s.memberGroupRepo.GetByCode(ctx, req.Code)
	if err == nil && existingMemberGroup != nil {
		log.WithField("code", req.Code).Error("member group code already exists")
		return nil, errors.NewValidationError("member group code already exists")
	}

	// Validate commission group ID
	if err := s.validateCommissionGroupID(ctx, req.CommissionGroupID); err != nil {
		log.WithError(err).Error("invalid commission group ID")
		return nil, err
	}

	// Validate member group type ID (if provided)
	if err := s.validateMemberGroupTypeID(ctx, req.MemberGroupTypeID); err != nil {
		log.WithError(err).Error("invalid member group type ID")
		return nil, err
	}

	// Validate user role IDs in withdrawal approvals
	if err := s.validateUserRoleIDs(ctx, req.WithdrawalApprovals); err != nil {
		log.WithError(err).Error("invalid user role ID in withdrawal approvals")
		return nil, err
	}

	// Validate deposit account IDs
	if err := s.validateDepositAccountIDs(ctx, req.DepositAccountIDs); err != nil {
		log.WithError(err).Error("invalid deposit account ID")
		return nil, err
	}

	// Create new member group
	newMemberGroup, err := member_group.NewMemberGroup(req)
	if err != nil {
		log.WithError(err).Error("failed to create member group domain object")
		return nil, err
	}

	// Save to repository with related data
	if err := s.memberGroupRepo.Create(ctx, newMemberGroup, req.WithdrawalApprovals, req.DepositAccountIDs); err != nil {
		log.WithError(err).Error("failed to save member group to repository")
		return nil, err
	}

	// Get the created member group with related data
	createdMemberGroup, err := s.memberGroupRepo.GetByID(ctx, newMemberGroup.ID)
	if err != nil {
		log.WithError(err).Error("failed to get created member group")
		return nil, err
	}

	response := createdMemberGroup.ToResponse()
	log.WithField("member_group_id", response.ID).WithField("code", req.Code).Info("member group created successfully")
	return &response, nil
}

// GetMemberGroupByID retrieves member group by ID
func (s *memberGroupService) GetMemberGroupByID(ctx context.Context, id int) (*member_group.MemberGroupResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetMemberGroupByID")

	mg, err := s.memberGroupRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).WithField("member_group_id", id).Error("failed to get member group by ID")
		return nil, err
	}

	response := mg.ToResponse()
	return &response, nil
}

// GetMemberGroupByCode retrieves member group by code
func (s *memberGroupService) GetMemberGroupByCode(ctx context.Context, code string) (*member_group.MemberGroupResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetMemberGroupByCode")

	mg, err := s.memberGroupRepo.GetByCode(ctx, code)
	if err != nil {
		log.WithError(err).WithField("member_group_code", code).Error("failed to get member group by code")
		return nil, err
	}

	response := mg.ToResponse()
	return &response, nil
}

// GetDefaultMemberGroup retrieves the default member group
func (s *memberGroupService) GetDefaultMemberGroup(ctx context.Context) (*member_group.MemberGroupResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetDefaultMemberGroup")

	mg, err := s.memberGroupRepo.GetDefault(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get default member group")
		return nil, err
	}

	response := mg.ToResponse()
	return &response, nil
}

// UpdateMemberGroup updates an existing member group
func (s *memberGroupService) UpdateMemberGroup(ctx context.Context, id int, req member_group.UpdateMemberGroupRequest) (*member_group.MemberGroupResponse, error) {
	return s.updateMemberGroupWithFileFlag(ctx, id, req, false)
}

// UpdateMemberGroupWithNewFile updates an existing member group with new file uploaded
func (s *memberGroupService) UpdateMemberGroupWithNewFile(ctx context.Context, id int, req member_group.UpdateMemberGroupRequest) (*member_group.MemberGroupResponse, error) {
	return s.updateMemberGroupWithFileFlag(ctx, id, req, true)
}

// updateMemberGroupWithFileFlag internal method with file upload flag
func (s *memberGroupService) updateMemberGroupWithFileFlag(ctx context.Context, id int, req member_group.UpdateMemberGroupRequest, hasNewFile bool) (*member_group.MemberGroupResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateMemberGroup")

	// Get existing member group
	mg, err := s.memberGroupRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).WithField("member_group_id", id).Error("failed to get member group for update")
		return nil, err
	}

	// Check if new code conflicts with existing member group (excluding current member group)
	if mg.Code != req.Code {
		existingMemberGroup, err := s.memberGroupRepo.GetByCode(ctx, req.Code)
		if err == nil && existingMemberGroup != nil && existingMemberGroup.ID != id {
			log.WithField("code", req.Code).Error("member group code already exists")
			return nil, errors.NewValidationError("member group code already exists")
		}
	}

	// Validate commission group ID
	if err := s.validateCommissionGroupID(ctx, req.CommissionGroupID); err != nil {
		log.WithError(err).Error("invalid commission group ID")
		return nil, err
	}

	// Validate member group type ID (if provided)
	if err := s.validateMemberGroupTypeID(ctx, req.MemberGroupTypeID); err != nil {
		log.WithError(err).Error("invalid member group type ID")
		return nil, err
	}

	// Validate user role IDs in withdrawal approvals
	if err := s.validateUserRoleIDs(ctx, req.WithdrawalApprovals); err != nil {
		log.WithError(err).Error("invalid user role ID in withdrawal approvals")
		return nil, err
	}

	// Validate deposit account IDs
	if err := s.validateDepositAccountIDs(ctx, req.DepositAccountIDs); err != nil {
		log.WithError(err).Error("invalid deposit account ID")
		return nil, err
	}

	// Handle image file deletion if requested (only for explicit delete)
	oldImage := mg.Image
	if req.ImageDelete && oldImage != nil {
		log.WithField("file_url", *oldImage).Info("deleting image file")
		if err := s.DeleteFile(ctx, &member_group.DeleteFileRequest{FileUrl: *oldImage}); err != nil {
			log.WithError(err).WithField("file_url", *oldImage).Warn("failed to delete old image file")
		} else {
			log.WithField("file_url", *oldImage).Info("successfully deleted image file")
		}
	}
	// Note: File replacement deletion is now handled in the handler layer

	// Update member group
	if err := mg.Update(req); err != nil {
		log.WithError(err).Error("failed to update member group domain object")
		return nil, err
	}

	// Save to repository with related data
	if err := s.memberGroupRepo.Update(ctx, mg, req.WithdrawalApprovals, req.DepositAccountIDs); err != nil {
		log.WithError(err).Error("failed to save updated member group to repository")
		return nil, err
	}

	// Get the updated member group with related data
	updatedMemberGroup, err := s.memberGroupRepo.GetByID(ctx, mg.ID)
	if err != nil {
		log.WithError(err).Error("failed to get updated member group")
		return nil, err
	}

	response := updatedMemberGroup.ToResponse()
	log.WithField("member_group_id", response.ID).Info("member group updated successfully")
	return &response, nil
}

// DeleteMemberGroup soft deletes a member group
func (s *memberGroupService) DeleteMemberGroup(ctx context.Context, id int) error {
	log := s.logger.WithContext(ctx).WithField("operation", "DeleteMemberGroup")

	if err := s.memberGroupRepo.Delete(ctx, id); err != nil {
		log.WithError(err).WithField("member_group_id", id).Error("failed to delete member group")
		return err
	}

	log.WithField("member_group_id", id).Info("member group deleted successfully")
	return nil
}

// ListMemberGroups retrieves member groups with pagination, search, and sorting
func (s *memberGroupService) ListMemberGroups(ctx context.Context, limit, offset int, search, sortBy, sortOrder string) ([]*member_group.MemberGroupResponse, int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListMemberGroups")

	// Get total count
	total, err := s.memberGroupRepo.Count(ctx, search)
	if err != nil {
		log.WithError(err).Error("failed to count member groups")
		return nil, 0, err
	}

	// Get member groups
	memberGroups, err := s.memberGroupRepo.List(ctx, limit, offset, search, sortBy, sortOrder)
	if err != nil {
		log.WithError(err).Error("failed to list member groups")
		return nil, 0, err
	}

	// Convert to response format
	var responses []*member_group.MemberGroupResponse
	for _, mg := range memberGroups {
		response := mg.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("total", total).WithField("returned", len(responses)).Info("member groups listed successfully")
	return responses, total, nil
}

// ListActiveMemberGroups retrieves only active member groups
func (s *memberGroupService) ListActiveMemberGroups(ctx context.Context) ([]*member_group.MemberGroupResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListActiveMemberGroups")

	memberGroups, err := s.memberGroupRepo.ListActive(ctx)
	if err != nil {
		log.WithError(err).Error("failed to list active member groups")
		return nil, err
	}

	// Convert to response format
	var responses []*member_group.MemberGroupResponse
	for _, mg := range memberGroups {
		response := mg.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("count", len(responses)).Info("active member groups listed successfully")
	return responses, nil
}

// ListMemberGroupsForDropdown retrieves member groups for dropdown filter
func (s *memberGroupService) ListMemberGroupsForDropdown(ctx context.Context) ([]*member_group.MemberGroupDropdownResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListMemberGroupsForDropdown")

	memberGroups, err := s.memberGroupRepo.ListActive(ctx)
	if err != nil {
		log.WithError(err).Error("failed to list member groups for dropdown")
		return nil, err
	}

	var responses []*member_group.MemberGroupDropdownResponse
	for _, mg := range memberGroups {
		responses = append(responses, &member_group.MemberGroupDropdownResponse{
			ID:   mg.ID,
			Name: mg.Name,
		})
	}

	log.WithField("count", len(responses)).Info("member groups for dropdown listed successfully")
	return responses, nil
}

// SetDefaultMemberGroup sets a member group as default
func (s *memberGroupService) SetDefaultMemberGroup(ctx context.Context, id int) error {
	log := s.logger.WithContext(ctx).WithField("operation", "SetDefaultMemberGroup")

	// Verify the member group exists
	_, err := s.memberGroupRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).WithField("member_group_id", id).Error("member group not found")
		return err
	}

	if err := s.memberGroupRepo.SetDefault(ctx, id); err != nil {
		log.WithError(err).WithField("member_group_id", id).Error("failed to set default member group")
		return err
	}

	log.WithField("member_group_id", id).Info("member group set as default successfully")
	return nil
}

// validateUserRoleIDs validates that all user role IDs exist in the system
func (s *memberGroupService) validateUserRoleIDs(ctx context.Context, withdrawalApprovals []member_group.CreateWithdrawalApprovalRequest) error {
	for _, approval := range withdrawalApprovals {
		for _, userRoleID := range approval.UserRoleIDs {
			userRoleIDStr := strconv.Itoa(userRoleID)
			_, err := s.userRoleRepo.GetByID(ctx, userRoleIDStr)
			if err != nil {
				return errors.NewValidationError("user_role_id does not exist")
			}
		}
	}
	return nil
}

// validateCommissionGroupID validates that commission group ID exists
func (s *memberGroupService) validateCommissionGroupID(ctx context.Context, commissionGroupID int) error {
	_, err := s.commissionGroupRepo.GetByID(ctx, commissionGroupID)
	if err != nil {
		return errors.NewValidationError("commission_group_id does not exist")
	}
	return nil
}

// validateMemberGroupTypeID validates that member group type ID exists (if provided)
func (s *memberGroupService) validateMemberGroupTypeID(ctx context.Context, memberGroupTypeID *int) error {
	if memberGroupTypeID != nil {
		_, err := s.memberGroupTypeRepo.GetByID(ctx, *memberGroupTypeID)
		if err != nil {
			return errors.NewValidationError("member_group_type_id does not exist")
		}
	}
	return nil
}

// validateDepositAccountIDs validates that all deposit account IDs exist in the system
func (s *memberGroupService) validateDepositAccountIDs(ctx context.Context, depositAccountIDs []int) error {
	for _, accountID := range depositAccountIDs {
		_, err := s.depositAccountRepo.FindByID(ctx, int64(accountID))
		if err != nil {
			// Check if it's a not found error
			if errors.IsNotFoundError(err) {
				return errors.NewValidationError(fmt.Sprintf("deposit_account_id %d does not exist", accountID))
			}
			// For other errors, return the original error
			return err
		}
	}
	return nil
}

// FileUpload uploads a file to S3 for member group
func (s *memberGroupService) FileUpload(ctx context.Context, fileBody *http.Request, fieldName string) (*member_group.FileUploadResponse, error) {
	fileReader, _, err := fileBody.FormFile(fieldName)
	if err != nil {
		s.logger.WithError(err).Error("failed to read file from request")
		return nil, errors.NewValidationError("failed to read file from request")
	}
	defer fileReader.Close()

	pathName := "backoffice/member-groups/"

	fileData, err := s.awsS3Repo.UploadFileToS3(ctx, pathName, fileReader)
	if err != nil {
		s.logger.WithError(err).Error("failed to upload file to S3")
		return nil, errors.NewValidationError("failed to upload file to S3")
	}

	fileUrl := member_group.FileUploadResponse{
		FileUrl: fileData.FileUrl,
	}

	return &fileUrl, nil
}

// DeleteFile deletes a file from S3 for member group
func (s *memberGroupService) DeleteFile(ctx context.Context, req *member_group.DeleteFileRequest) error {
	if err := s.awsS3Repo.DeleteFileFromS3(ctx, req.FileUrl); err != nil {
		s.logger.WithError(err).Error("failed to delete file from S3")
		return errors.NewValidationError("failed to delete file from S3")
	}

	s.logger.WithField("file_url", req.FileUrl).Info("file deleted from S3 successfully")
	return nil
}
