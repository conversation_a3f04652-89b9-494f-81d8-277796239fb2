package service

import (
	"blacking-api/internal/domain/deposit_account"
	"blacking-api/internal/domain/response"
	"blacking-api/internal/helper"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"strings"
)

type DepositAccountService interface {
	CreateDepositAccount(ctx context.Context, req *deposit_account.DepositAccountRequest) error
	FindAllDepositAccounts(ctx context.Context, req *deposit_account.DepositAccountSearchRequest) (*response.SuccessWithPagination, error)
	FindDepositAccountByID(ctx context.Context, id int64) (*deposit_account.DepositAccountByIdResponse, error)
	FindDepositAccountSettingAlgorithmByID(ctx context.Context, id int64) (*deposit_account.DepositAccountSettingAlgorithmResponse, error)
	UpdateDepositAccount(ctx context.Context, id int64, req *deposit_account.DepositAccountUpdateRequest) error
	UpdateDepositAccountAutoBot(ctx context.Context, id int64, botId int64) error
	UpdateDepositAccountAlgorithm(ctx context.Context, id int64, req *deposit_account.DepositAccountSettingAlgorithm) error
	ActiveDepositAccount(ctx context.Context, id int64, status bool) error
	DeleteDepositAccount(ctx context.Context, id int64) error
	GetAccountTransferTypes(ctx context.Context) ([]*deposit_account.AccountTransferTypeResponse, error)
}

type depositAccountService struct {
	depositAccountRepo interfaces.DepositAccountRepository
	bankingRepo        interfaces.BankingRepository
	paymentMethodRepo  interfaces.PaymentMethodRepository
	algorithmRepo      interfaces.AlgorithmRepository
	autoBotRepo        interfaces.AutoBotRepository
	logger             logger.Logger
}

func NewDepositAccountService(
	depositAccountRepo interfaces.DepositAccountRepository,
	bankingRepo interfaces.BankingRepository,
	paymentMethodRepo interfaces.PaymentMethodRepository,
	algorithmRepo interfaces.AlgorithmRepository,
	autoBotRepo interfaces.AutoBotRepository,
	logger logger.Logger,
) DepositAccountService {
	return &depositAccountService{
		depositAccountRepo: depositAccountRepo,
		bankingRepo:        bankingRepo,
		paymentMethodRepo:  paymentMethodRepo,
		algorithmRepo:      algorithmRepo,
		autoBotRepo:        autoBotRepo,
		logger:             logger,
	}
}

func (s *depositAccountService) CreateDepositAccount(ctx context.Context, req *deposit_account.DepositAccountRequest) error {

	bankingExists, err := s.bankingRepo.FindIdExists(ctx, int64(req.BankingID))
	if err != nil {
		s.logger.WithError(err).Error("banking repo failed to find banking id")
		return err
	}
	if !bankingExists {
		s.logger.WithError(err).Error("banking repo failed to find banking id")
		return errors.NewValidationError("banking ID does not exist")
	}

	paymentMethodExists, err := s.paymentMethodRepo.FindIdExists(ctx, int64(req.PaymentMethodID))
	if err != nil {
		s.logger.WithError(err).Error("failed to check if payment method ID exists")
		return err
	}
	if !paymentMethodExists {
		s.logger.WithError(err).Error("payment method ID does not exist")
		return errors.NewValidationError("payment method ID does not exist")
	}

	accountNumberDuplicate, err := s.depositAccountRepo.FindByAccountNumberDuplicate(ctx, req.AccountNumber)
	if err != nil {
		s.logger.WithError(err).Error("failed to check for duplicate account number")
		return err
	}
	if accountNumberDuplicate {
		s.logger.WithError(err).Error("duplicate account number")
		return errors.NewValidationError("account number already exists")
	}

	if err := s.depositAccountRepo.Create(ctx, req); err != nil {
		s.logger.WithError(err).Error("failed to create deposit account")
		return err
	}

	return nil
}

func (s *depositAccountService) FindAllDepositAccounts(ctx context.Context, req *deposit_account.DepositAccountSearchRequest) (*response.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		s.logger.WithError(err).Error("failed to limit pagination")
		return nil, err
	}

	pagination := helper.CalculatePagination(req.Page, req.Limit)

	var conditions []string
	var queryArgs []interface{}

	if req.AccountName != nil && *req.AccountName != "" {
		conditions = append(conditions, "UPPER(account_name) ILIKE UPPER($1)")
		queryArgs = append(queryArgs, "%"+*req.AccountName+"%")
	}

	if req.BankingID != nil && *req.BankingID > 0 {
		conditions = append(conditions, "banking_id = $2")
		queryArgs = append(queryArgs, *req.BankingID)
	}

	if req.AlgorithmID != nil && *req.AlgorithmID > 0 {
		conditions = append(conditions, "algorithm_id = $3")
		queryArgs = append(queryArgs, *req.AlgorithmID)
	}

	if req.AutoBotID != nil && *req.AutoBotID > 0 {
		conditions = append(conditions, "auto_bot_id = $4")
		queryArgs = append(queryArgs, *req.AutoBotID)
	}

	var whereClause string
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	accounts, total, err := s.depositAccountRepo.FindAll(ctx, pagination.Limit, pagination.Offset, whereClause, queryArgs)
	if err != nil {
		s.logger.WithError(err).Error("failed to find all deposit accounts")
		return nil, err
	}

	content := make([]*deposit_account.DepositAccountListResponse, len(accounts))
	if len(accounts) != 0 {
		for i, a := range accounts {
			content[i] = a.ToListResponse()
		}
	}

	responses := &response.SuccessWithPagination{
		Message:    "Deposit accounts retrieved successfully",
		Content:    content,
		Page:       int64(pagination.Page),
		Limit:      int64(pagination.Limit),
		TotalPages: helper.CalculateTotalPages(total, pagination.Limit),
		TotalItems: total,
	}

	return responses, nil
}

func (s *depositAccountService) FindDepositAccountByID(ctx context.Context, id int64) (*deposit_account.DepositAccountByIdResponse, error) {

	if id == 0 {
		return nil, errors.NewValidationError("deposit account ID cannot be zero")
	}

	account, err := s.depositAccountRepo.FindByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to find deposit account by ID")
		return nil, err
	}

	return account.ToByIdResponse(), nil
}

func (s *depositAccountService) FindDepositAccountSettingAlgorithmByID(ctx context.Context, id int64) (*deposit_account.DepositAccountSettingAlgorithmResponse, error) {

	if id == 0 {
		return nil, errors.NewValidationError("deposit account ID cannot be zero")
	}

	account, err := s.depositAccountRepo.FindDepositAccountSettingAlgorithmByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to find deposit account setting algorithm by ID")
		return nil, err
	}

	return account.ToResponseSettingAlgorithm(), nil
}

func (s *depositAccountService) UpdateDepositAccount(ctx context.Context, id int64, req *deposit_account.DepositAccountUpdateRequest) error {

	bankingExists, err := s.bankingRepo.FindIdExists(ctx, int64(req.BankingID))
	if err != nil {
		s.logger.WithError(err).Error("banking repo failed to find banking id")
		return err
	}
	if !bankingExists {
		s.logger.WithError(err).Error("banking repo failed to find banking id")
		return errors.NewValidationError("banking ID does not exist")
	}

	paymentMethodExists, err := s.paymentMethodRepo.FindIdExists(ctx, int64(req.PaymentMethodID))
	if err != nil {
		s.logger.WithError(err).Error("failed to check if payment method ID exists")
		return err
	}
	if !paymentMethodExists {
		s.logger.WithError(err).Error("payment method ID does not exist")
		return errors.NewValidationError("payment method ID does not exist")
	}

	accountNumberDuplicate, err := s.depositAccountRepo.FindByAccountNumberDuplicateAndIdNot(ctx, req.AccountNumber, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to check for duplicate account number")
		return err
	}
	if accountNumberDuplicate {
		s.logger.WithError(err).Error("duplicate account number")
		return errors.NewValidationError("account number already exists")
	}

	if err := s.depositAccountRepo.Update(ctx, id, req); err != nil {
		s.logger.WithError(err).Error("failed to update deposit account")
		return err
	}
	return nil
}

func (s *depositAccountService) UpdateDepositAccountAutoBot(ctx context.Context, id int64, botId int64) error {

	botIdExists, err := s.autoBotRepo.FindIdExists(ctx, botId)
	if err != nil {
		s.logger.WithError(err).Error("failed to check if auto bot ID exists")
		return err
	}
	if !botIdExists {
		s.logger.WithError(err).Error("auto bot ID does not exist")
		return errors.NewValidationError("auto bot ID does not exist")
	}

	if err := s.depositAccountRepo.UpdateAutoBot(ctx, id, botId); err != nil {
		s.logger.WithError(err).Error("failed to update deposit account auto bot")
		return err
	}
	return nil
}

func (s *depositAccountService) UpdateDepositAccountAlgorithm(ctx context.Context, id int64, req *deposit_account.DepositAccountSettingAlgorithm) error {

	algorithmExists, err := s.algorithmRepo.FindIdExists(ctx, int64(req.AlgorithmID))
	if err != nil {
		s.logger.WithError(err).Error("failed to check if algorithm ID exists")
		return err
	}
	if !algorithmExists {
		s.logger.WithError(err).Error("algorithm ID does not exist")
		return errors.NewValidationError("algorithm ID does not exist")
	}

	if err := s.depositAccountRepo.UpdateAlgorithm(ctx, id, req); err != nil {
		s.logger.WithError(err).Error("failed to update deposit account algorithm")
		return err
	}
	return nil
}

func (s *depositAccountService) ActiveDepositAccount(ctx context.Context, id int64, status bool) error {
	if err := s.depositAccountRepo.Active(ctx, id, status); err != nil {
		s.logger.WithError(err).Error("failed to activate/deactivate deposit account")
		return err
	}

	return nil
}

func (s *depositAccountService) DeleteDepositAccount(ctx context.Context, id int64) error {
	if err := s.depositAccountRepo.Delete(ctx, id); err != nil {
		s.logger.WithError(err).Error("failed to delete deposit account")
		return err
	}

	return nil
}

func (s *depositAccountService) GetAccountTransferTypes(ctx context.Context) ([]*deposit_account.AccountTransferTypeResponse, error) {
	accountTransferTypes, err := s.depositAccountRepo.GetAccountTransferTypes(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get account transfer types")
		return nil, err
	}

	responses := make([]*deposit_account.AccountTransferTypeResponse, len(accountTransferTypes))
	for i, accountTransferType := range accountTransferTypes {
		responses[i] = accountTransferType.ToResponse()
	}

	return responses, nil
}
