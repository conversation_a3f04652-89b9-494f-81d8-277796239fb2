package middleware

import (
	"blacking-api/internal/domain/admin_audit_log"
	"blacking-api/internal/service"
	"blacking-api/pkg/auth"
	"blacking-api/pkg/logger"
	"bytes"
	"context"
	"io"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// AdminAuditMiddleware creates middleware for logging admin API operations
func AdminAuditMiddleware(adminAuditService service.AdminAuditLogService, log logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip GET requests, OPTIONS, and health checks
		if c.Request.Method == "GET" || 
		   c.Request.Method == "OPTIONS" || 
		   strings.Contains(c.Request.URL.Path, "/health") ||
		   strings.Contains(c.Request.URL.Path, "/ping") {
			c.Next()
			return
		}

		// Check if this path should be audited
		if !service.ShouldAuditPath(c.Request.Method, c.Request.URL.Path) {
			c.Next()
			return
		}

		// Capture request body for audit logging
		var requestBody string
		if c.Request.Body != nil {
			bodyBytes, err := io.ReadAll(c.Request.Body)
			if err == nil && len(bodyBytes) > 0 {
				// Store original body for processing
				requestBody = string(bodyBytes)
				
				// Replace body reader so it can be read again by handlers
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
				
				// Store sanitized body in context for audit logging
				c.Set("request_body", requestBody)
			}
		}

		// Create a custom response writer to capture status code
		responseWriter := &auditResponseWriter{
			ResponseWriter: c.Writer,
			statusCode:     200, // Default status code
		}
		c.Writer = responseWriter

		// Process the request
		c.Next()

		// Log the admin action after request completion
		// Use goroutine to avoid blocking the response and prevent context cancellation issues
		go func() {
			defer func() {
				if r := recover(); r != nil {
					log.WithField("panic", r).Error("panic in admin audit logging")
				}
			}()

			// Create a detached context with timeout for async audit logging
			auditCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()

			// Create audit request manually to avoid gin context dependency
			userID := auth.GetUserIDFromContext(c)
			username := auth.GetUsernameFromContext(c)
			
			if userID == 0 || username == "" || username == "system" {
				return // Skip if no valid user
			}

			method := c.Request.Method
			path := c.Request.URL.Path
			if c.Request.URL.RawQuery != "" {
				path += "?" + c.Request.URL.RawQuery
			}

			var requestBody *string
			if bodyBytes, exists := c.Get("request_body"); exists {
				if bodyStr, ok := bodyBytes.(string); ok && bodyStr != "" {
					requestBody = &bodyStr
				}
			}

			ipAddress := c.ClientIP()
			userAgent := c.Request.UserAgent()

			auditReq := admin_audit_log.CreateAdminAuditLogRequest{
				UserID:         userID,
				Username:       username,
				Method:         method,
				Path:           path,
				RequestBody:    requestBody,
				ResponseStatus: responseWriter.statusCode,
				IPAddress:      &ipAddress,
				UserAgent:      &userAgent,
			}

			if err := adminAuditService.LogAdminAction(auditCtx, auditReq); err != nil {
				log.WithError(err).WithField("path", c.Request.URL.Path).Warn("failed to log admin audit in async mode")
			}
		}()
	}
}

// auditResponseWriter wraps gin.ResponseWriter to capture response status
type auditResponseWriter struct {
	gin.ResponseWriter
	statusCode int
}

// WriteHeader captures the status code
func (w *auditResponseWriter) WriteHeader(statusCode int) {
	w.statusCode = statusCode
	w.ResponseWriter.WriteHeader(statusCode)
}

// Write ensures status code is set even if WriteHeader is not called
func (w *auditResponseWriter) Write(data []byte) (int, error) {
	// If WriteHeader hasn't been called, the status code will be 200
	if w.statusCode == 0 {
		w.statusCode = 200
	}
	return w.ResponseWriter.Write(data)
}

// Status returns the captured status code
func (w *auditResponseWriter) Status() int {
	return w.statusCode
}