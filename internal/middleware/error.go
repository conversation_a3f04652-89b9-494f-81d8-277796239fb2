package middleware

import (
	"net/http"

	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

func ErrorHandler(log logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// Handle errors
		if len(c.Errors) > 0 {
			err := c.Errors.Last().Err

			// Check if it's an AppError
			if appErr := errors.GetAppError(err); appErr != nil {
				log.WithField("request_id", c.GetString("request_id")).
					WithError(appErr).
					Error("application error occurred")

				c.<PERSON>(appErr.StatusCode, appErr.ToResponse())
				return
			}

			// Handle unknown errors
			log.WithField("request_id", c.GetString("request_id")).
				WithError(err).
				Error("unexpected error occurred")

			response := errors.ErrorResponse{
				Error:   errors.InternalError,
				Message: "Internal server error",
			}
			c<PERSON>(http.StatusInternalServerError, response)
		}
	}
}

func Recovery(log logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if r := recover(); r != nil {
				log.WithField("request_id", c.GetString("request_id")).
					WithField("panic", r).
					Error("panic recovered")

				response := errors.ErrorResponse{
					Error:   errors.InternalError,
					Message: "Internal server error",
				}
				c.JSON(http.StatusInternalServerError, response)
				c.Abort()
			}
		}()
		c.Next()
	}
}
