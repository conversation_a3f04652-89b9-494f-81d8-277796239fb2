package middleware

import (
	"blacking-api/internal/domain/refresh_token"
	"blacking-api/internal/service"
	"blacking-api/pkg/auth"
	"errors"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	jwt "github.com/appleboy/gin-jwt/v2"
	"github.com/gin-gonic/gin"
)

// Helper function to get string value from *string
func getUsernameString(username *string) string {
	if username != nil {
		return *username
	}
	return ""
}

type LoginInput struct {
	Username string `form:"username" json:"username" binding:"required"`
	Password string `form:"password" json:"password" binding:"required"`
}

// Use LoginClaims and MemberLoginClaims from pkg/auth
type LoginClaims = auth.LoginClaims
type MemberLoginClaims = auth.MemberLoginClaims

type AuthMiddleware struct {
	userService         service.UserService
	userRoleService     service.UserRoleService
	user2FAService      service.User2FAService
	memberService       service.MemberService
	refreshTokenService service.RefreshTokenService
}

func NewAuthMiddleware(userService service.UserService, userRoleService service.UserRoleService, user2FAService service.User2FAService, memberService service.MemberService, refreshTokenService service.RefreshTokenService) *AuthMiddleware {
	return &AuthMiddleware{
		userService:         userService,
		userRoleService:     userRoleService,
		user2FAService:      user2FAService,
		memberService:       memberService,
		refreshTokenService: refreshTokenService,
	}
}

func (a *AuthMiddleware) HandlerMiddleware(authMiddleware *jwt.GinJWTMiddleware) gin.HandlerFunc {
	return authMiddleware.MiddlewareFunc()
}

func (a *AuthMiddleware) NewUserJWT() *jwt.GinJWTMiddleware {
	// Get JWT secret from environment variable, fallback to default if not set
	jwtSecret := os.Getenv("JWT_SECRET")
	if jwtSecret == "" {
		jwtSecret = "icqHh7zR3.yLZdKUimV7" // fallback to original hardcoded key
	}

	m, _ := jwt.New(&jwt.GinJWTMiddleware{
		Realm:       "user zone",
		Key:         []byte(jwtSecret),
		IdentityKey: "username",
		Timeout:     time.Hour * 24, // 1 day access token
		MaxRefresh:  time.Minute * 15,

		// Force re-authentication by making tokens single-use
		DisabledAbort: false,
		PayloadFunc:   a.PayloadFunc(),

		IdentityHandler: a.AdminIdentityHandler(),
		Authenticator:   a.authenticator(),
		Authorizator:    a.authorizator(),
		Unauthorized:    a.unauthorized(),

		SendCookie:     true,
		CookieName:     "auth-token",
		CookieHTTPOnly: true,
		// CookieSecure:   true,                 // ต้องใช้ HTTPS
		//CookieSameSite: http.SameSiteLaxMode, // Strict / None ตาม
		//CookieDomain:   "yourdomain.com",     // ถ้าจำเป็น

		// TokenLookup:     "header: Authorization, query: token, cookie: jwt",
		// TokenLookup: "query:token",
		// TokenLookup:   "cookie:token",
		TokenLookup:   "header: Authorization, cookie: auth-token",
		TokenHeadName: "Bearer",
		TimeFunc:      time.Now,
		LoginResponse: func(c *gin.Context, code int, token string, expire time.Time) {
			// Check if response was already sent (e.g., for 2FA flow)
			if c.Writer.Written() {
				return
			}

			// Create refresh token for the logged in user
			var refreshTokenResp *refresh_token.RefreshTokenResponse

			// Get user ID from context (set by authenticator)
			claims, exists := c.Get("user_claims")
			if exists {
				if userClaims, ok := claims.(*LoginClaims); ok {
					refreshTokenResp, _ = a.refreshTokenService.CreateRefreshToken(c.Request.Context(), userClaims.ID, refresh_token.UserTypeAdmin)
				}
			}

			response := gin.H{
				"status":     "success",
				"message":    "Login successful",
				"token":      token,
				"expire":     expire.Unix(),
				"expires_in": 24 * 60 * 60, // 1 day in seconds
				"token_type": "Bearer",
			}

			if refreshTokenResp != nil {
				response["refresh_token"] = refreshTokenResp.Token
			}

			c.JSON(code, response)
		},
		LogoutResponse: func(c *gin.Context, code int) {
			// Get user ID from JWT claims before logout
			claims := jwt.ExtractClaims(c)
			if userID, ok := claims["id"].(float64); ok {
				// Invalidate all refresh tokens for this user
				a.refreshTokenService.InvalidateAllUserTokens(c.Request.Context(), int(userID), refresh_token.UserTypeAdmin)
			}

			c.JSON(code, gin.H{
				"status":  "success",
				"message": "Admin logout successful",
			})
		},
	})
	return m
}

func (a *AuthMiddleware) NewMemberJWT() *jwt.GinJWTMiddleware {
	// Get JWT secret from environment variable, fallback to default if not set
	jwtSecret := os.Getenv("JWT_SECRET")
	if jwtSecret == "" {
		jwtSecret = "memberSecretKey123" // fallback to original hardcoded key
	}

	m, _ := jwt.New(&jwt.GinJWTMiddleware{
		Realm:       "member zone",
		Key:         []byte(jwtSecret),
		IdentityKey: "username",
		Timeout:     time.Hour * 24, // 1 day access token for members
		MaxRefresh:  time.Minute * 15,
		PayloadFunc: a.MemberPayloadFunc(),

		IdentityHandler: a.MemberIdentityHandler(),
		Authenticator:   a.memberAuthenticator(),
		Authorizator:    a.memberAuthorizator(),
		Unauthorized:    a.unauthorized(),

		SendCookie:     true,
		CookieName:     "auth-token", // Same cookie name as admin
		CookieHTTPOnly: true,
		CookieSameSite: http.SameSiteLaxMode,
		CookieDomain:   "yourdomain.com",

		TokenLookup:   "header: Authorization, cookie: auth-token",
		TokenHeadName: "Bearer",
		TimeFunc:      time.Now,
		LoginResponse: func(c *gin.Context, code int, token string, expire time.Time) {
			if c.Writer.Written() {
				return
			}

			// Create refresh token for the logged in member
			var refreshTokenResp *refresh_token.RefreshTokenResponse

			// Get member ID from context (set by authenticator)
			claims, exists := c.Get("member_claims")
			if exists {
				if memberClaims, ok := claims.(*MemberLoginClaims); ok {
					refreshTokenResp, _ = a.refreshTokenService.CreateRefreshToken(c.Request.Context(), memberClaims.ID, refresh_token.UserTypeMember)
				}
			}

			response := gin.H{
				"status":     "success",
				"message":    "Member login successful",
				"token":      token,
				"expire":     expire.Unix(),
				"expires_in": 24 * 60 * 60, // 1 day in seconds
				"token_type": "Bearer",
			}

			if refreshTokenResp != nil {
				response["refresh_token"] = refreshTokenResp.Token
			}

			c.JSON(code, response)
		},
		LogoutResponse: func(c *gin.Context, code int) {
			// Get member ID from JWT claims before logout
			claims := jwt.ExtractClaims(c)
			if memberID, ok := claims["id"].(float64); ok {
				// Invalidate all refresh tokens for this member
				a.refreshTokenService.InvalidateAllUserTokens(c.Request.Context(), int(memberID), refresh_token.UserTypeMember)
			}

			c.JSON(code, gin.H{
				"status":  "success",
				"message": "Member logout successful",
			})
		},
	})
	return m
}

func (a *AuthMiddleware) MemberPayloadFunc() func(data interface{}) jwt.MapClaims {
	return func(data interface{}) jwt.MapClaims {
		log.Printf("MemberPayloadFunc called with data: %+v, type: %T", data, data)

		if v, ok := data.(*MemberLoginClaims); ok {
			log.Printf("Successfully cast to MemberLoginClaims: %+v", v)

			claims := jwt.MapClaims{
				"phone":          getUsernameString(v.Phone),
				"username":       getUsernameString(v.Username),
				"game_username":  getUsernameString(v.GameUsername),
				"id":             v.ID,
				"is_enable":      v.IsEnable,
				"login_time":     time.Now().Unix(), // Add current login timestamp
				"user_role_id":   v.MemberRoleID,    // Add role ID for permission checking
				"user_role_name": v.MemberRoleName,  // Add role name for permission checking
			}
			log.Printf("Generated member claims: %+v", claims)
			return claims
		}

		log.Printf("Failed to cast to MemberLoginClaims, returning empty claims")
		return jwt.MapClaims{}
	}
}

func (a *AuthMiddleware) PayloadFunc() func(data interface{}) jwt.MapClaims {
	return func(data interface{}) jwt.MapClaims {
		// Try pointer first
		if v, ok := data.(*LoginClaims); ok {
			log.Printf("Successfully cast to *LoginClaims: %+v", v)
			claims := jwt.MapClaims{
				"username":       v.Username,
				"id":             v.ID,
				"user_role_id":   v.AdminRoleID,
				"user_role_name": v.AdminRoleName,
				"is_enable":      v.IsEnable,
				"login_time":     time.Now().Unix(), // Add current login timestamp
			}
			log.Printf("Generated claims: %+v", claims)
			return claims
		}

		// Try value type
		if v, ok := data.(LoginClaims); ok {
			log.Printf("Successfully cast to LoginClaims: %+v", v)
			claims := jwt.MapClaims{
				"username":       v.Username,
				"id":             v.ID,
				"user_role_id":   v.AdminRoleID,
				"user_role_name": v.AdminRoleName,
				"is_enable":      v.IsEnable,
			}
			log.Printf("Generated claims: %+v", claims)
			return claims
		}

		log.Printf("Failed to cast to LoginClaims, returning empty claims")
		return jwt.MapClaims{}
	}
}

func (a *AuthMiddleware) AdminIdentityHandler() func(c *gin.Context) interface{} {
	return func(c *gin.Context) interface{} {

		claims := jwt.ExtractClaims(c)
		username, _ := claims["username"].(string)
		idFloat, _ := claims["id"].(float64)
		userRoleFloat, ok := claims["user_role_id"].(float64)
		isEnable, isEnableExists := claims["is_enable"].(bool)

		// If is_enable claim doesn't exist in JWT (old token), default to false for security
		if !isEnableExists {
			isEnable = false
		}

		if !ok {
			log.Println("cannot convert role id to float64")

		}

		userRoleID := int(userRoleFloat)
		userRole, _ := claims["user_role_name"].(string)
		id := int(idFloat)

		authUser := LoginClaims{
			Username:      username,
			ID:            id,
			AdminRoleName: userRole,
			AdminRoleID:   userRoleID,
			IsEnable:      isEnable,
		}

		c.Set("auth-user", &authUser)
		c.Set("auth-username", username)
		c.Set("user_id", id)

		return &authUser
	}
}

func (a *AuthMiddleware) MemberIdentityHandler() func(c *gin.Context) interface{} {
	return func(c *gin.Context) interface{} {
		claims := jwt.ExtractClaims(c)
		phone, _ := claims["phone"].(string)
		username, _ := claims["username"].(string)
		gameUsername, _ := claims["game_username"].(string)
		isEnable, _ := claims["is_enable"].(bool)

		// Handle id as either float64 (from JWT) or string
		var id int
		if idFloat, ok := claims["id"].(float64); ok {
			id = int(idFloat)
		} else if idStr, ok := claims["id"].(string); ok {
			var err error
			id, err = strconv.Atoi(idStr)
			if err != nil {
				id = 0 // fallback to 0 if conversion fails
			}
		} else {
			id = 0 // fallback to 0 if conversion fails
		}

		// Extract role information from claims
		memberRoleIDFloat, _ := claims["user_role_id"].(float64)
		memberRoleName, _ := claims["user_role_name"].(string)
		memberRoleID := int(memberRoleIDFloat)

		var usernamePtr *string
		if username != "" {
			usernamePtr = &username
		}

		var phonePtr *string
		if phone != "" {
			phonePtr = &phone
		}

		var gameUsernamePtr *string
		if gameUsername != "" {
			gameUsernamePtr = &gameUsername
		}

		authMember := MemberLoginClaims{
			Phone:          phonePtr,
			Username:       usernamePtr,
			GameUsername:   gameUsernamePtr,
			ID:             id,
			IsEnable:       isEnable,
			MemberRoleID:   memberRoleID,
			MemberRoleName: memberRoleName,
		}

		c.Set("auth-member", &authMember)
		c.Set("member_id", id) // Set member_id for handlers to use
		return &authMember
	}
}

func (a *AuthMiddleware) authorizator() func(data interface{}, c *gin.Context) bool {
	return func(data interface{}, c *gin.Context) bool {
		if v, ok := data.(*LoginClaims); ok {
			// Check if user is enabled

			if !v.IsEnable {
				return false
			}

			if v.ID == -1 {
				return true
			}
			_, err := a.userRoleService.GetUserRoleByID(c.Request.Context(), strconv.Itoa(v.AdminRoleID))
			if err != nil {
				return false
			}
			return true
		}
		return false
	}
}

func (a *AuthMiddleware) memberAuthorizator() func(data interface{}, c *gin.Context) bool {
	return func(data interface{}, c *gin.Context) bool {
		if v, ok := data.(*MemberLoginClaims); ok {
			// Check if member is enabled
			if !v.IsEnable {
				return false
			}
			// Additional member authorization logic can be added here
			return true
		}
		return false
	}
}

func (a *AuthMiddleware) unauthorized() func(c *gin.Context, code int, message string) {
	return func(c *gin.Context, code int, message string) {
		// Provide more descriptive error messages
		var errorMessage string
		var errorCode string

		switch message {
		case "authentication failed":
			errorMessage = "ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง"
			errorCode = "INVALID_CREDENTIALS"
		case "invalid credentials":
			errorMessage = "ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง"
			errorCode = "INVALID_CREDENTIALS"
		case "ไม่พบผู้ใช้งานในระบบ":
			errorMessage = "ไม่พบผู้ใช้งานในระบบ"
			errorCode = "USER_NOT_FOUND"
		case "ไม่พบสมาชิกในระบบ":
			errorMessage = "ไม่พบสมาชิกในระบบ"
			errorCode = "MEMBER_NOT_FOUND"
		case "รหัสผ่านไม่ถูกต้อง":
			errorMessage = "รหัสผ่านไม่ถูกต้อง"
			errorCode = "INVALID_PASSWORD"
		case "user account is disabled":
			errorMessage = "บัญชีผู้ใช้ถูกปิดใช้งาน"
			errorCode = "ACCOUNT_DISABLED"
		case "member account is disabled":
			errorMessage = "บัญชีสมาชิกถูกปิดใช้งาน"
			errorCode = "ACCOUNT_DISABLED"
		case "user account is not active":
			errorMessage = "บัญชีผู้ใช้ไม่ได้เปิดใช้งาน"
			errorCode = "ACCOUNT_INACTIVE"
		case "บัญชีผู้ใช้ไม่ได้เปิดใช้งาน":
			errorMessage = "บัญชีผู้ใช้ไม่ได้เปิดใช้งาน"
			errorCode = "ACCOUNT_INACTIVE"
		case "บัญชีสมาชิกไม่ได้เปิดใช้งาน":
			errorMessage = "บัญชีสมาชิกไม่ได้เปิดใช้งาน"
			errorCode = "ACCOUNT_INACTIVE"
		default:
			// Handle account locked messages
			if strings.Contains(message, "temporarily locked") || strings.Contains(message, "locked due to") {
				errorMessage = "บัญชีถูกล็อกชั่วคราวเนื่องจากพยายามเข้าสู่ระบบผิดหลายครั้ง"
				errorCode = "ACCOUNT_LOCKED"
			} else if message == "" {
				errorMessage = "การเข้าสู่ระบบล้มเหลว"
				errorCode = "LOGIN_FAILED"
			} else {
				errorMessage = message
				errorCode = "LOGIN_ERROR"
			}
		}

		c.JSON(code, gin.H{
			"error":   errorCode,
			"message": errorMessage,
		})
	}
}

func (a *AuthMiddleware) authenticator() func(c *gin.Context) (interface{}, error) {
	return func(c *gin.Context) (interface{}, error) {
		log.Printf("🔐 Authenticator called - new login attempt")

		var input LoginInput
		if err := c.ShouldBindJSON(&input); err != nil {
			return "", jwt.ErrMissingLoginValues
		}

		username := input.Username
		password := input.Password

		log.Printf("🔐 Login attempt for username: %s", username)

		// ✅ dev login
		if os.Getenv("GIN_MODE") != "release" &&
			username == os.Getenv("DEV_USERNAME") &&
			password == os.Getenv("DEV_PASSWORD") {
			return &LoginClaims{
				ID:            1, // Developer ID
				Username:      username,
				AdminRoleID:   1,
				AdminRoleName: "Super Admin Dev",
				IsEnable:      true, // Developer is always enabled
			}, nil
		}

		// ✅ ค้นหา user จาก DB ( logic ตรง้)
		user, err := a.userService.ValidateUserCredentials(c, username, password)

		if err != nil {
			log.Printf("🔐 Login failed for username: %s, error: %v", username, err)
			// Return more specific error messages based on the error type
			return nil, err
		}

		// Check if user is enabled
		if !user.IsEnable {
			return nil, errors.New("user account is disabled")
		}

		// Check if user role requires 2FA
		userRole, err := a.userRoleService.GetUserRoleByID(c.Request.Context(), strconv.Itoa(user.UserRoleID))
		if err != nil {
			// If error getting user role, continue without 2FA (backward compatibility)
			return &LoginClaims{
				Username:      user.Username,
				AdminRoleName: user.UserRoleName,
				AdminRoleID:   user.UserRoleID,
				ID:            user.ID,
				IsEnable:      user.IsEnable, // UserResponse.IsEnable is bool, not *bool
				LoginTime:     time.Now().Unix(),
			}, nil
		}

		// If user role requires 2FA, check if user has 2FA setup and enabled
		if userRole.Is2FA {

			claims := &LoginClaims{
				Username:      "__2fa_setup__",
				AdminRoleName: "__2fa_setup__",
				AdminRoleID:   0,
				ID:            user.ID,
				IsEnable:      user.IsEnable, // UserResponse.IsEnable is bool, not *bool
			}

			is2FAEnabled, err := a.user2FAService.Get2FAStatus(c.Request.Context(), strconv.Itoa(user.ID))

			if err != nil || !is2FAEnabled.IsEnabled {

				// Generate temporary JWT token for 2FA setup
				setupToken, err := a.generateTempToken(claims)
				if err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{
						"error":   "failed_to_generate_token",
						"message": "Failed to generate setup token. Please try again.",
					})
					c.Abort()
					return nil, nil
				}

				// If error getting 2FA status, auto setup 2FA
				setupResponse, setupErr := a.autoSetup2FA(c, user.ID, user.Username, setupToken)
				if setupErr != nil {
					c.JSON(http.StatusInternalServerError, gin.H{
						"error":   "failed_to_setup_2fa",
						"message": "Failed to setup 2FA automatically. Please try again.",
					})
					c.Abort()
					return nil, nil
				}

				c.JSON(http.StatusPreconditionRequired, gin.H{
					"requires_2fa_setup": true,
					"setup_data":         setupResponse,
					"message":            "2FA is required for your role. Please scan the QR code and enable 2FA.",
				})
				c.Abort()
				return nil, nil
			}
			if is2FAEnabled.IsEnabled {

				tempToken, _ := a.generateTempToken(claims)

				c.JSON(http.StatusAccepted, gin.H{
					"requires_2fa": true,
					"temp_token":   tempToken,
					"message":      "2FA verification required",
				})

			}
			c.Abort()
			return nil, nil
		}

		return &LoginClaims{
			Username:      user.Username,
			AdminRoleName: user.UserRoleName,
			AdminRoleID:   user.UserRoleID,
			ID:            user.ID,
			IsEnable:      user.IsEnable, // UserResponse.IsEnable is bool, not *bool
			LoginTime:     time.Now().Unix(),
		}, nil
	}
}

func (a *AuthMiddleware) memberAuthenticator() func(c *gin.Context) (interface{}, error) {
	return func(c *gin.Context) (interface{}, error) {
		log.Printf("👤 Member Authenticator called - new member login attempt")

		var input LoginInput
		if err := c.ShouldBindJSON(&input); err != nil {
			return "", jwt.ErrMissingLoginValues
		}

		username := input.Username
		password := input.Password // TODO: Implement password verification

		log.Printf("👤 Member login attempt for username: %s", username)

		member, err := a.memberService.ValidateMemberCredentials(c, username, password)

		if err != nil {
			log.Printf("👤 Member login failed for username: %s, error: %v", username, err)
			// Return more specific error messages based on the error type
			return nil, err
		}

		// Check if member is enabled
		if !member.IsEnable {
			// Record failed member login attempt
			return nil, errors.New("member account is disabled")
		}

		return &MemberLoginClaims{
			Phone:          member.Phone,
			Username:       member.Username,
			GameUsername:   member.GameUsername,
			ID:             member.ID,
			IsEnable:       member.IsEnable,
			LoginTime:      time.Now().Unix(),
			MemberRoleID:   1,        // Default role ID for members (can be customized)
			MemberRoleName: "Member", // Default role name for members
		}, nil
	}
}

// generateTempToken generates a temporary JWT token for 2FA step
func (a *AuthMiddleware) generateTempToken(claims *LoginClaims) (string, error) {
	// Create JWT middleware instance for token generation
	jwtMiddleware := a.NewUserJWT()

	// Generate JWT token manually
	token, _, err := jwtMiddleware.TokenGenerator(claims)
	if err != nil {
		return "", err
	}

	log.Println(token)

	return token, nil
}

// autoSetup2FA automatically sets up 2FA for a user
func (a *AuthMiddleware) autoSetup2FA(c *gin.Context, userID int, username string, setupToken string) (interface{}, error) {
	// For now, return a mock response that tells user to use the setup API
	// In production, this should call the actual 2FA service
	return map[string]interface{}{
		"message":        "Please use the 2FA setup API to complete setup",
		"setup_endpoint": "/api/v1/auth/2fa/my-setup",
		"user_id":        userID,
		"setup_token":    setupToken,
	}, nil
}

// RequireAdminRole middleware to ensure user has admin role
func (a *AuthMiddleware) RequireAdminRole() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get auth user from context (set by JWT middleware)
		authUserInterface, exists := c.Get("auth-user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Authentication required",
					"code":    "UNAUTHORIZED",
				},
			})
			c.Abort()
			return
		}

		// Type assert to LoginClaims (admin user)
		authUser, ok := authUserInterface.(*LoginClaims)
		if !ok {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Admin access required",
					"code":    "FORBIDDEN",
				},
			})
			c.Abort()
			return
		}

		// Check if user is enabled
		if !authUser.IsEnable {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Account is disabled",
					"code":    "ACCOUNT_DISABLED",
				},
			})
			c.Abort()
			return
		}

		// Check if user has admin role (you can customize this logic based on your role system)
		if authUser.AdminRoleID == 0 || authUser.AdminRoleName == "" {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Admin role required",
					"code":    "INSUFFICIENT_PERMISSIONS",
				},
			})
			c.Abort()
			return
		}

		// Set admin context for downstream handlers
		c.Set("admin_id", authUser.ID)
		c.Set("admin_role", authUser.AdminRoleName)
		c.Set("admin_role_id", authUser.AdminRoleID)

		c.Next()
	}
}

// RequireMemberRole middleware to ensure user has member role
func (a *AuthMiddleware) RequireMemberRole() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get auth member from context (set by JWT middleware)
		authMemberInterface, exists := c.Get("auth-member")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Authentication required",
					"code":    "UNAUTHORIZED",
				},
			})
			c.Abort()
			return
		}

		// Type assert to MemberLoginClaims (member user)
		authMember, ok := authMemberInterface.(*MemberLoginClaims)
		if !ok {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Member access required",
					"code":    "FORBIDDEN",
				},
			})
			c.Abort()
			return
		}

		// Check if member is enabled
		if !authMember.IsEnable {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Account is disabled",
					"code":    "ACCOUNT_DISABLED",
				},
			})
			c.Abort()
			return
		}

		// Set member context for downstream handlers
		c.Set("member_id", authMember.ID)
		c.Set("member_role", authMember.MemberRoleName)
		c.Set("member_role_id", authMember.MemberRoleID)
		c.Set("member_code", authMember.GameUsername)

		c.Next()
	}
}

// RequireAdminOrMemberRole middleware for endpoints that allow both admin and member access
// RequireAuth middleware to ensure user is authenticated (admin or member)
func (a *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return a.RequireAdminOrMemberRole()
}

func (a *AuthMiddleware) RequireAdminOrMemberRole() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get auth user from context (set by JWT middleware)
		authUserInterface, exists := c.Get("auth-user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Authentication required",
					"code":    "UNAUTHORIZED",
				},
			})
			c.Abort()
			return
		}

		// Try to cast as admin user first
		if authUser, ok := authUserInterface.(*LoginClaims); ok {
			if !authUser.IsEnable {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"error": gin.H{
						"message": "Account is disabled",
						"code":    "ACCOUNT_DISABLED",
					},
				})
				c.Abort()
				return
			}
			// Set admin context
			c.Set("admin_id", authUser.ID)
			c.Set("admin_role", authUser.AdminRoleName)
			c.Set("admin_role_id", authUser.AdminRoleID)
			c.Set("user_type", "admin")
			c.Next()
			return
		}

		// Try to cast as member user
		if authMember, ok := authUserInterface.(*MemberLoginClaims); ok {
			if !authMember.IsEnable {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"error": gin.H{
						"message": "Account is disabled",
						"code":    "ACCOUNT_DISABLED",
					},
				})
				c.Abort()
				return
			}
			// Set member context
			c.Set("member_id", authMember.ID)
			c.Set("member_role", authMember.MemberRoleName)
			c.Set("member_role_id", authMember.MemberRoleID)
			c.Set("user_type", "member")
			c.Next()
			return
		}

		// If neither admin nor member, deny access
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"error": gin.H{
				"message": "Valid user role required",
				"code":    "INVALID_USER_TYPE",
			},
		})
		c.Abort()
	}
}
