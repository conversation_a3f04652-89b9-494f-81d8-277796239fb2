package json

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"blacking-api/internal/domain/referral"
)

// WinlossJSONRepository reads winloss data from JSON file (temporary implementation)
type <PERSON>lossJSONRepository struct {
	dataFilePath string
}

// NewWinlossJSONRepository creates a new JSON-based winloss repository
func NewWinlossJSONRepository(dataFilePath string) *WinlossJSONRepository {
	return &WinlossJSONRepository{
		dataFilePath: dataFilePath,
	}
}

// GetWinlossData reads winloss data from JSON file
// This implements the ExternalWinlossRepository interface
func (r *WinlossJSONRepository) GetWinlossData(upline string, startDate, endDate time.Time, page, limit int) (*referral.WinlossResponse, error) {
	// Read JSON file
	data, err := os.ReadFile(r.dataFilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read JSON file: %w", err)
	}

	// Parse JSON
	var response referral.WinlossResponse
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}

	// Filter data by upline if specified
	if upline != "" && upline != "all" {
		filteredData := make([]referral.WinlossDataItem, 0)
		for _, item := range response.Data.Data {
			if item.Upline == upline {
				filteredData = append(filteredData, item)
			}
		}
		response.Data.Data = filteredData
	}

	// Filter by date range
	if !startDate.IsZero() || !endDate.IsZero() {
		filteredData := make([]referral.WinlossDataItem, 0)
		for _, item := range response.Data.Data {
			// Check if item date is within range
			if !startDate.IsZero() && item.CreatedAt.Before(startDate) {
				continue
			}
			if !endDate.IsZero() && item.CreatedAt.After(endDate) {
				continue
			}
			filteredData = append(filteredData, item)
		}
		response.Data.Data = filteredData
	}

	// Handle pagination
	total := len(response.Data.Data)

	// Calculate offset
	offset := (page - 1) * limit
	if offset > total {
		offset = total
	}

	// Calculate end index
	end := offset + limit
	if end > total {
		end = total
	}

	// Slice the data
	if offset < total {
		response.Data.Data = response.Data.Data[offset:end]
	} else {
		response.Data.Data = []referral.WinlossDataItem{}
	}

	// Update metadata
	response.Data.Meta.Page = page
	response.Data.Meta.Limit = limit
	response.Data.Meta.Total = total
	response.Data.Meta.TotalPage = (total + limit - 1) / limit // Ceiling division

	return &response, nil
}

// GetDefaultWinlossRepository creates a repository using the default test data file
func GetDefaultWinlossRepository() *WinlossJSONRepository {
	// Get the project root directory
	currentDir, _ := os.Getwd()
	testDataPath := filepath.Join(currentDir, "test", "testdata", "referral", "winloss-member-info.json")
	return NewWinlossJSONRepository(testDataPath)
}
