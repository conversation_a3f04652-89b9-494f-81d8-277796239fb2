package postgres

import (
	"blacking-api/internal/domain/theme_setting"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type ThemeSettingRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewThemeSettingRepository creates a new theme setting repository
func NewThemeSettingRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.ThemeSettingRepository {
	return &ThemeSettingRepository{
		pool:   pool,
		logger: logger,
	}
}

// Get retrieves the current theme setting (there should only be one record)
func (r *ThemeSettingRepository) Get(ctx context.Context) (*theme_setting.ThemeSetting, error) {
	query := `
		SELECT id, theme_value, updated_at, updated_by
		FROM theme_setting
		ORDER BY id DESC
		LIMIT 1
	`

	setting := &theme_setting.ThemeSetting{}
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query)
	err := row.Scan(
		&setting.ID,
		&setting.ThemeValue,
		&setting.UpdatedAt,
		&setting.UpdatedBy,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("theme setting not found")
		}
		r.logger.WithError(err).Error("failed to get theme setting")
		return nil, errors.NewDatabaseError("failed to get theme setting")
	}

	return setting, nil
}

// Create creates a new theme setting
func (r *ThemeSettingRepository) Create(ctx context.Context, setting *theme_setting.ThemeSetting) error {
	query := `
		INSERT INTO theme_setting (theme_value, updated_at, updated_by)
		VALUES ($1, $2, $3)
		RETURNING id
	`

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		setting.ThemeValue,
		setting.UpdatedAt,
		setting.UpdatedBy,
	)
	err := row.Scan(&setting.ID)

	if err != nil {
		r.logger.WithError(err).WithField("theme_value", setting.ThemeValue).Error("failed to create theme setting")
		return errors.NewDatabaseError("failed to create theme setting")
	}

	r.logger.WithField("theme_value", setting.ThemeValue).Info("theme setting created successfully")
	return nil
}

// Update updates the theme setting
func (r *ThemeSettingRepository) Update(ctx context.Context, setting *theme_setting.ThemeSetting) error {
	query := `
		UPDATE theme_setting 
		SET theme_value = $2, updated_at = $3, updated_by = $4
		WHERE id = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		setting.ID,
		setting.ThemeValue,
		setting.UpdatedAt,
		setting.UpdatedBy,
	)

	if err != nil {
		r.logger.WithError(err).WithField("id", setting.ID).Error("failed to update theme setting")
		return errors.NewDatabaseError("failed to update theme setting")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("theme setting not found")
	}

	r.logger.WithField("theme_value", setting.ThemeValue).Info("theme setting updated successfully")
	return nil
}

// Upsert creates or updates the theme setting
func (r *ThemeSettingRepository) Upsert(ctx context.Context, setting *theme_setting.ThemeSetting) error {
	// Since we want only one theme setting record, we'll use a different approach
	// First, try to get the existing record
	existing, err := r.Get(ctx)
	if err != nil && !errors.IsNotFoundError(err) {
		return err
	}

	if existing != nil {
		// Update existing record
		setting.ID = existing.ID
		return r.Update(ctx, setting)
	}

	// Create new record
	return r.Create(ctx, setting)
}
