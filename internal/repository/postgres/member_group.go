package postgres

import (
	"context"
	"encoding/json"
	"fmt"

	"blacking-api/internal/domain/member_group"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type MemberGroupRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewMemberGroupRepository creates a new member group repository
func NewMemberGroupRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.MemberGroupRepository {
	return &MemberGroupRepository{
		pool:   pool,
		logger: logger,
	}
}

// Create creates a new member group with related data
func (r *MemberGroupRepository) Create(ctx context.Context, mg *member_group.MemberGroup, withdrawalApprovals []member_group.CreateWithdrawalApprovalRequest, depositAccountIDs []int) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction")
		return errors.NewDatabaseError("failed to create member group")
	}
	defer tx.Rollback(ctx)

	// If this is set as default, unset all other defaults first
	if mg.IsDefault {
		if err := r.unsetAllDefaultsInTx(ctx, tx); err != nil {
			return err
		}
	}

	query := `
		INSERT INTO member_groups (
			code, name, member_group_type_id, min_deposit, min_withdraw, max_deposit,
			is_default, is_vip, daily_withdraw_limit, daily_withdraw_amount_limit,
			commission_group_id, deposit_turnover_type, deposit_turnover_amount,
			deposit_turnover_release_type, deposit_turnover_release_amount,
			calculate_min_deposit, image, status, created_at, updated_at
		)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20)
		RETURNING id
	`

	var lastInsertID int
	err = dbutil.TxQueryRowWithSchema(ctx, tx, query,
		mg.Code, mg.Name, mg.MemberGroupTypeID, mg.MinDeposit, mg.MinWithdraw, mg.MaxDeposit,
		mg.IsDefault, mg.IsVIP, mg.DailyWithdrawLimit, mg.DailyWithdrawAmountLimit,
		mg.CommissionGroupID, mg.DepositTurnoverType, mg.DepositTurnoverAmount,
		mg.DepositTurnoverReleaseType, mg.DepositTurnoverReleaseAmount,
		mg.CalculateMinDeposit, mg.Image, mg.Status, mg.CreatedAt, mg.UpdatedAt,
	).Scan(&lastInsertID)

	if err != nil {
		r.logger.WithError(err).Error("failed to create member group")
		return errors.NewDatabaseError("failed to create member group")
	}

	mg.ID = lastInsertID

	// Create withdrawal approvals
	if len(withdrawalApprovals) > 0 {
		if err := r.createWithdrawalApprovalsInTx(ctx, tx, lastInsertID, withdrawalApprovals); err != nil {
			return err
		}
	}

	// Create deposit account assignments
	if len(depositAccountIDs) > 0 {
		if err := r.createDepositAccountsInTx(ctx, tx, lastInsertID, depositAccountIDs); err != nil {
			return err
		}
	}

	if err := tx.Commit(ctx); err != nil {
		r.logger.WithError(err).Error("failed to commit transaction")
		return errors.NewDatabaseError("failed to create member group")
	}

	r.logger.WithField("member_group_id", mg.ID).Info("member group created successfully")
	return nil
}

// GetByID retrieves member group by ID with related data
func (r *MemberGroupRepository) GetByID(ctx context.Context, id int) (*member_group.MemberGroup, error) {
	query := `
		SELECT mg.id, mg.code, mg.name, mg.member_group_type_id, mg.min_deposit, mg.min_withdraw, mg.max_deposit,
			   mg.is_default, mg.is_vip, mg.daily_withdraw_limit, mg.daily_withdraw_amount_limit,
			   mg.commission_group_id, cg.name as commission_group_name, mgt.show_in_lobby as member_group_type_show_in_lobby,
			   mg.deposit_turnover_type, mg.deposit_turnover_amount,
			   mg.deposit_turnover_release_type, mg.deposit_turnover_release_amount,
			   mg.calculate_min_deposit, mg.image, mg.status, mg.created_at, mg.updated_at
		FROM member_groups mg
		LEFT JOIN commission_groups cg ON mg.commission_group_id = cg.id
		LEFT JOIN member_group_types mgt ON mg.member_group_type_id = mgt.id
		WHERE mg.id = $1 AND mg.status = 'active'
	`

	mg := &member_group.MemberGroup{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&mg.ID, &mg.Code, &mg.Name, &mg.MemberGroupTypeID, &mg.MinDeposit, &mg.MinWithdraw, &mg.MaxDeposit,
		&mg.IsDefault, &mg.IsVIP, &mg.DailyWithdrawLimit, &mg.DailyWithdrawAmountLimit,
		&mg.CommissionGroupID, &mg.CommissionGroupName, &mg.MemberGroupTypeShowInLobby,
		&mg.DepositTurnoverType, &mg.DepositTurnoverAmount,
		&mg.DepositTurnoverReleaseType, &mg.DepositTurnoverReleaseAmount,
		&mg.CalculateMinDeposit, &mg.Image, &mg.Status, &mg.CreatedAt, &mg.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("member group not found")
		}
		r.logger.WithError(err).Error("failed to get member group by ID")
		return nil, errors.NewDatabaseError("failed to get member group")
	}

	// Load related data
	withdrawalApprovals, err := r.GetWithdrawalApprovals(ctx, id)
	if err != nil {
		return nil, err
	}
	mg.WithdrawalApprovals = withdrawalApprovals

	depositAccounts, err := r.GetDepositAccounts(ctx, id)
	if err != nil {
		return nil, err
	}
	mg.DepositAccounts = depositAccounts

	return mg, nil
}

// GetByCode retrieves member group by code
func (r *MemberGroupRepository) GetByCode(ctx context.Context, code string) (*member_group.MemberGroup, error) {
	query := `
		SELECT id, code, name, member_group_type_id, min_deposit, min_withdraw, max_deposit,
			   is_default, is_vip, daily_withdraw_limit, daily_withdraw_amount_limit,
			   commission_group_id, deposit_turnover_type, deposit_turnover_amount,
			   deposit_turnover_release_type, deposit_turnover_release_amount,
			   calculate_min_deposit, image, status, created_at, updated_at
		FROM member_groups
		WHERE code = $1 AND status = 'active'
	`

	mg := &member_group.MemberGroup{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, code).Scan(
		&mg.ID, &mg.Code, &mg.Name, &mg.MemberGroupTypeID, &mg.MinDeposit, &mg.MinWithdraw, &mg.MaxDeposit,
		&mg.IsDefault, &mg.IsVIP, &mg.DailyWithdrawLimit, &mg.DailyWithdrawAmountLimit,
		&mg.CommissionGroupID, &mg.DepositTurnoverType, &mg.DepositTurnoverAmount,
		&mg.DepositTurnoverReleaseType, &mg.DepositTurnoverReleaseAmount,
		&mg.CalculateMinDeposit, &mg.Image, &mg.Status, &mg.CreatedAt, &mg.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("member group not found")
		}
		r.logger.WithError(err).Error("failed to get member group by code")
		return nil, errors.NewDatabaseError("failed to get member group")
	}

	return mg, nil
}

// GetDefault retrieves the default member group
func (r *MemberGroupRepository) GetDefault(ctx context.Context) (*member_group.MemberGroup, error) {
	query := `
		SELECT id, code, name, member_group_type_id, min_deposit, min_withdraw, max_deposit,
			   is_default, is_vip, daily_withdraw_limit, daily_withdraw_amount_limit,
			   commission_group_id, deposit_turnover_type, deposit_turnover_amount,
			   deposit_turnover_release_type, deposit_turnover_release_amount,
			   calculate_min_deposit, image, status, created_at, updated_at
		FROM member_groups
		WHERE is_default = true AND status = 'active'
		LIMIT 1
	`

	mg := &member_group.MemberGroup{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query).Scan(
		&mg.ID, &mg.Code, &mg.Name, &mg.MemberGroupTypeID, &mg.MinDeposit, &mg.MinWithdraw, &mg.MaxDeposit,
		&mg.IsDefault, &mg.IsVIP, &mg.DailyWithdrawLimit, &mg.DailyWithdrawAmountLimit,
		&mg.CommissionGroupID, &mg.DepositTurnoverType, &mg.DepositTurnoverAmount,
		&mg.DepositTurnoverReleaseType, &mg.DepositTurnoverReleaseAmount,
		&mg.CalculateMinDeposit, &mg.Image, &mg.Status, &mg.CreatedAt, &mg.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("default member group not found")
		}
		r.logger.WithError(err).Error("failed to get default member group")
		return nil, errors.NewDatabaseError("failed to get default member group")
	}

	return mg, nil
}

// Update updates an existing member group with related data
func (r *MemberGroupRepository) Update(ctx context.Context, mg *member_group.MemberGroup, withdrawalApprovals []member_group.CreateWithdrawalApprovalRequest, depositAccountIDs []int) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction")
		return errors.NewDatabaseError("failed to update member group")
	}
	defer tx.Rollback(ctx)

	// If this is set as default, unset all other defaults first
	if mg.IsDefault {
		if err := r.unsetAllDefaultsInTx(ctx, tx); err != nil {
			return err
		}
	}

	query := `
		UPDATE member_groups
		SET code = $2, name = $3, member_group_type_id = $4, min_deposit = $5, min_withdraw = $6, max_deposit = $7,
			is_default = $8, is_vip = $9, daily_withdraw_limit = $10, daily_withdraw_amount_limit = $11,
			commission_group_id = $12, deposit_turnover_type = $13, deposit_turnover_amount = $14,
			deposit_turnover_release_type = $15, deposit_turnover_release_amount = $16,
			calculate_min_deposit = $17, image = $18, updated_at = $19
		WHERE id = $1 AND status = 'active'
	`

	result, err := dbutil.TxExecWithSchema(ctx, tx, query,
		mg.ID, mg.Code, mg.Name, mg.MemberGroupTypeID, mg.MinDeposit, mg.MinWithdraw, mg.MaxDeposit,
		mg.IsDefault, mg.IsVIP, mg.DailyWithdrawLimit, mg.DailyWithdrawAmountLimit,
		mg.CommissionGroupID, mg.DepositTurnoverType, mg.DepositTurnoverAmount,
		mg.DepositTurnoverReleaseType, mg.DepositTurnoverReleaseAmount,
		mg.CalculateMinDeposit, mg.Image, mg.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to update member group")
		return errors.NewDatabaseError("failed to update member group")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("member group not found")
	}

	// Smart update withdrawal approvals
	if err := r.smartUpdateWithdrawalApprovalsInTx(ctx, tx, mg.ID, withdrawalApprovals); err != nil {
		return err
	}

	// Smart update deposit accounts
	if err := r.smartUpdateDepositAccountsInTx(ctx, tx, mg.ID, depositAccountIDs); err != nil {
		return err
	}

	if err := tx.Commit(ctx); err != nil {
		r.logger.WithError(err).Error("failed to commit transaction")
		return errors.NewDatabaseError("failed to update member group")
	}

	r.logger.WithField("member_group_id", mg.ID).Info("member group updated successfully")
	return nil
}

// Delete soft deletes a member group (sets status to inactive)
func (r *MemberGroupRepository) Delete(ctx context.Context, id int) error {
	// Check if this is the default group
	mg, err := r.GetByID(ctx, id)
	if err != nil {
		return err
	}

	if mg.IsDefault {
		return errors.NewValidationError("cannot delete default member group")
	}

	query := `
		UPDATE member_groups
		SET status = 'inactive', updated_at = CURRENT_TIMESTAMP
		WHERE id = $1 AND status = 'active'
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete member group")
		return errors.NewDatabaseError("failed to delete member group")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("member group not found")
	}

	r.logger.WithField("member_group_id", id).Info("member group deleted successfully")
	return nil
}

// List retrieves member groups with pagination, search, and sorting
func (r *MemberGroupRepository) List(ctx context.Context, limit, offset int, search, sortBy, sortOrder string) ([]*member_group.MemberGroup, error) {
	baseQuery := `
		SELECT mg.id, mg.code, mg.name, mg.member_group_type_id, mg.min_deposit, mg.min_withdraw, mg.max_deposit,
			   mg.is_default, mg.is_vip, mg.daily_withdraw_limit, mg.daily_withdraw_amount_limit,
			   mg.commission_group_id, cg.name as commission_group_name, mgt.show_in_lobby as member_group_type_show_in_lobby,
			   mg.deposit_turnover_type, mg.deposit_turnover_amount,
			   mg.deposit_turnover_release_type, mg.deposit_turnover_release_amount,
			   mg.calculate_min_deposit, mg.image, mg.status, mg.created_at, mg.updated_at
		FROM member_groups mg
		LEFT JOIN commission_groups cg ON mg.commission_group_id = cg.id
		LEFT JOIN member_group_types mgt ON mg.member_group_type_id = mgt.id
		WHERE mg.status = 'active'
	`

	var args []interface{}
	argIndex := 1

	// Add search condition
	if search != "" {
		baseQuery += fmt.Sprintf(" AND (mg.code ILIKE $%d OR mg.name ILIKE $%d)", argIndex, argIndex)
		args = append(args, "%"+search+"%")
		argIndex++
	}

	// Add sorting
	validSortColumns := map[string]bool{
		"id":         true,
		"code":       true,
		"name":       true,
		"is_default": true,
		"is_vip":     true,
		"created_at": true,
		"updated_at": true,
	}

	if sortBy != "" && validSortColumns[sortBy] {
		if sortOrder == "desc" {
			baseQuery += fmt.Sprintf(" ORDER BY mg.%s DESC", sortBy)
		} else {
			baseQuery += fmt.Sprintf(" ORDER BY mg.%s ASC", sortBy)
		}
	} else {
		baseQuery += " ORDER BY mg.is_default DESC, mg.created_at DESC" // Default first, then by created_at
	}

	// Add pagination
	baseQuery += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, offset)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, baseQuery, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list member groups")
		return nil, errors.NewDatabaseError("failed to list member groups")
	}
	defer rows.Close()

	var memberGroups []*member_group.MemberGroup
	for rows.Next() {
		mg := &member_group.MemberGroup{}
		err := rows.Scan(
			&mg.ID, &mg.Code, &mg.Name, &mg.MemberGroupTypeID, &mg.MinDeposit, &mg.MinWithdraw, &mg.MaxDeposit,
			&mg.IsDefault, &mg.IsVIP, &mg.DailyWithdrawLimit, &mg.DailyWithdrawAmountLimit,
			&mg.CommissionGroupID, &mg.CommissionGroupName, &mg.MemberGroupTypeShowInLobby,
			&mg.DepositTurnoverType, &mg.DepositTurnoverAmount,
			&mg.DepositTurnoverReleaseType, &mg.DepositTurnoverReleaseAmount,
			&mg.CalculateMinDeposit, &mg.Image, &mg.Status, &mg.CreatedAt, &mg.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan member group")
			return nil, errors.NewDatabaseError("failed to scan member group")
		}
		memberGroups = append(memberGroups, mg)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating member group rows")
		return nil, errors.NewDatabaseError("error iterating member group rows")
	}

	return memberGroups, nil
}

// Count returns total count of member groups with search filter
func (r *MemberGroupRepository) Count(ctx context.Context, search string) (int64, error) {
	query := "SELECT COUNT(*) FROM member_groups mg WHERE mg.status = 'active'"
	var args []interface{}

	if search != "" {
		query += " AND (mg.code ILIKE $1 OR mg.name ILIKE $1)"
		args = append(args, "%"+search+"%")
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count member groups")
		return 0, errors.NewDatabaseError("failed to count member groups")
	}

	return count, nil
}

// ListActive retrieves only active member groups
func (r *MemberGroupRepository) ListActive(ctx context.Context) ([]*member_group.MemberGroup, error) {
	query := `
		SELECT mg.id, mg.code, mg.name, mg.member_group_type_id, mg.min_deposit, mg.min_withdraw, mg.max_deposit,
			   mg.is_default, mg.is_vip, mg.daily_withdraw_limit, mg.daily_withdraw_amount_limit,
			   mg.commission_group_id, cg.name as commission_group_name, mgt.show_in_lobby as member_group_type_show_in_lobby,
			   mg.deposit_turnover_type, mg.deposit_turnover_amount,
			   mg.deposit_turnover_release_type, mg.deposit_turnover_release_amount,
			   mg.calculate_min_deposit, mg.image, mg.status, mg.created_at, mg.updated_at
		FROM member_groups mg
		LEFT JOIN commission_groups cg ON mg.commission_group_id = cg.id
		LEFT JOIN member_group_types mgt ON mg.member_group_type_id = mgt.id
		WHERE mg.status = 'active'
		ORDER BY mg.is_default DESC, mg.name ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to list active member groups")
		return nil, errors.NewDatabaseError("failed to list active member groups")
	}
	defer rows.Close()

	var memberGroups []*member_group.MemberGroup
	for rows.Next() {
		mg := &member_group.MemberGroup{}
		err := rows.Scan(
			&mg.ID, &mg.Code, &mg.Name, &mg.MemberGroupTypeID, &mg.MinDeposit, &mg.MinWithdraw, &mg.MaxDeposit,
			&mg.IsDefault, &mg.IsVIP, &mg.DailyWithdrawLimit, &mg.DailyWithdrawAmountLimit,
			&mg.CommissionGroupID, &mg.CommissionGroupName, &mg.MemberGroupTypeShowInLobby,
			&mg.DepositTurnoverType, &mg.DepositTurnoverAmount,
			&mg.DepositTurnoverReleaseType, &mg.DepositTurnoverReleaseAmount,
			&mg.CalculateMinDeposit, &mg.Image, &mg.Status, &mg.CreatedAt, &mg.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan active member group")
			return nil, errors.NewDatabaseError("failed to scan active member group")
		}
		memberGroups = append(memberGroups, mg)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating active member group rows")
		return nil, errors.NewDatabaseError("error iterating active member group rows")
	}

	return memberGroups, nil
}

// ListForDropdown retrieves unique member group names for dropdown filter
func (r *MemberGroupRepository) ListForDropdown(ctx context.Context) ([]string, error) {
	query := `
		SELECT name
		FROM member_groups
		WHERE status = 'active'
		ORDER BY is_default DESC, name ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to list member groups for dropdown")
		return nil, errors.NewDatabaseError("failed to list member groups for dropdown")
	}
	defer rows.Close()

	var names []string
	for rows.Next() {
		var name string
		err := rows.Scan(&name)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan member group name")
			return nil, errors.NewDatabaseError("failed to scan member group name")
		}
		names = append(names, name)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating member group name rows")
		return nil, errors.NewDatabaseError("error iterating member group name rows")
	}

	return names, nil
}

// SetDefault sets a member group as default (and unsets others)
func (r *MemberGroupRepository) SetDefault(ctx context.Context, id int) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction")
		return errors.NewDatabaseError("failed to set default member group")
	}
	defer tx.Rollback(ctx)

	// Unset all defaults first
	if err := r.unsetAllDefaultsInTx(ctx, tx); err != nil {
		return err
	}

	// Set the specified group as default
	query := `
		UPDATE member_groups
		SET is_default = true, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1 AND status = 'active'
	`

	result, err := dbutil.TxExecWithSchema(ctx, tx, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to set default member group")
		return errors.NewDatabaseError("failed to set default member group")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("member group not found")
	}

	if err := tx.Commit(ctx); err != nil {
		r.logger.WithError(err).Error("failed to commit transaction")
		return errors.NewDatabaseError("failed to set default member group")
	}

	r.logger.WithField("member_group_id", id).Info("member group set as default successfully")
	return nil
}

// UnsetAllDefaults unsets all member groups as default
func (r *MemberGroupRepository) UnsetAllDefaults(ctx context.Context) error {
	query := `
		UPDATE member_groups
		SET is_default = false, updated_at = CURRENT_TIMESTAMP
		WHERE is_default = true AND status = 'active'
	`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to unset all default member groups")
		return errors.NewDatabaseError("failed to unset all default member groups")
	}

	r.logger.Info("all member groups unset as default successfully")
	return nil
}

// GetWithdrawalApprovals retrieves withdrawal approvals for a member group
func (r *MemberGroupRepository) GetWithdrawalApprovals(ctx context.Context, memberGroupID int) ([]member_group.MemberGroupWithdrawalApproval, error) {
	query := `
		SELECT id, member_group_id, min_amount, max_amount, 
		       COALESCE(user_role_ids, '[]'::jsonb) as user_role_ids,
		       order_index, created_at, updated_at
		FROM member_group_withdrawal_approvals
		WHERE member_group_id = $1
		ORDER BY order_index ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, memberGroupID)
	if err != nil {
		r.logger.WithError(err).Error("failed to get withdrawal approvals")
		return nil, errors.NewDatabaseError("failed to get withdrawal approvals")
	}
	defer rows.Close()

	var approvals []member_group.MemberGroupWithdrawalApproval
	for rows.Next() {
		var approval member_group.MemberGroupWithdrawalApproval
		var userRoleIDsJSON []byte
		err := rows.Scan(
			&approval.ID, &approval.MemberGroupID, &approval.MinAmount, &approval.MaxAmount,
			&userRoleIDsJSON, &approval.OrderIndex, &approval.CreatedAt, &approval.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan withdrawal approval")
			return nil, errors.NewDatabaseError("failed to scan withdrawal approval")
		}

		// Parse JSONB array to []int
		if len(userRoleIDsJSON) > 0 {
			var userRoleIDs []int
			if err := json.Unmarshal(userRoleIDsJSON, &userRoleIDs); err != nil {
				r.logger.WithError(err).Error("failed to parse user_role_ids JSON")
				return nil, errors.NewDatabaseError("failed to parse user_role_ids")
			}
			approval.UserRoleIDs = userRoleIDs
		}

		approvals = append(approvals, approval)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating withdrawal approval rows")
		return nil, errors.NewDatabaseError("error iterating withdrawal approval rows")
	}

	return approvals, nil
}

// GetDepositAccounts retrieves deposit accounts for a member group
func (r *MemberGroupRepository) GetDepositAccounts(ctx context.Context, memberGroupID int) ([]member_group.MemberGroupDepositAccount, error) {
	query := `
		SELECT id, member_group_id, account_deposit_id, created_at
		FROM member_group_deposit_accounts
		WHERE member_group_id = $1
		ORDER BY created_at ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, memberGroupID)
	if err != nil {
		r.logger.WithError(err).Error("failed to get deposit accounts")
		return nil, errors.NewDatabaseError("failed to get deposit accounts")
	}
	defer rows.Close()

	var accounts []member_group.MemberGroupDepositAccount
	for rows.Next() {
		var account member_group.MemberGroupDepositAccount
		err := rows.Scan(
			&account.ID, &account.MemberGroupID, &account.AccountDepositID, &account.CreatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan deposit account")
			return nil, errors.NewDatabaseError("failed to scan deposit account")
		}
		accounts = append(accounts, account)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating deposit account rows")
		return nil, errors.NewDatabaseError("error iterating deposit account rows")
	}

	return accounts, nil
}

// CreateWithdrawalApprovals creates withdrawal approvals for a member group
func (r *MemberGroupRepository) CreateWithdrawalApprovals(ctx context.Context, memberGroupID int, approvals []member_group.CreateWithdrawalApprovalRequest) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction")
		return errors.NewDatabaseError("failed to create withdrawal approvals")
	}
	defer tx.Rollback(ctx)

	if err := r.createWithdrawalApprovalsInTx(ctx, tx, memberGroupID, approvals); err != nil {
		return err
	}

	if err := tx.Commit(ctx); err != nil {
		r.logger.WithError(err).Error("failed to commit transaction")
		return errors.NewDatabaseError("failed to create withdrawal approvals")
	}

	return nil
}

// DeleteWithdrawalApprovals deletes all withdrawal approvals for a member group
func (r *MemberGroupRepository) DeleteWithdrawalApprovals(ctx context.Context, memberGroupID int) error {
	query := `DELETE FROM member_group_withdrawal_approvals WHERE member_group_id = $1`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query, memberGroupID)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete withdrawal approvals")
		return errors.NewDatabaseError("failed to delete withdrawal approvals")
	}

	return nil
}

// CreateDepositAccounts creates deposit account assignments for a member group
func (r *MemberGroupRepository) CreateDepositAccounts(ctx context.Context, memberGroupID int, accountIDs []int) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction")
		return errors.NewDatabaseError("failed to create deposit accounts")
	}
	defer tx.Rollback(ctx)

	if err := r.createDepositAccountsInTx(ctx, tx, memberGroupID, accountIDs); err != nil {
		return err
	}

	if err := tx.Commit(ctx); err != nil {
		r.logger.WithError(err).Error("failed to commit transaction")
		return errors.NewDatabaseError("failed to create deposit accounts")
	}

	return nil
}

// DeleteDepositAccounts deletes all deposit account assignments for a member group
func (r *MemberGroupRepository) DeleteDepositAccounts(ctx context.Context, memberGroupID int) error {
	query := `DELETE FROM member_group_deposit_accounts WHERE member_group_id = $1`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query, memberGroupID)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete deposit accounts")
		return errors.NewDatabaseError("failed to delete deposit accounts")
	}

	return nil
}

// Helper methods for transaction operations

// unsetAllDefaultsInTx unsets all member groups as default within a transaction
func (r *MemberGroupRepository) unsetAllDefaultsInTx(ctx context.Context, tx pgx.Tx) error {
	query := `
		UPDATE member_groups
		SET is_default = false, updated_at = CURRENT_TIMESTAMP
		WHERE is_default = true AND status = 'active'
	`

	_, err := dbutil.TxExecWithSchema(ctx, tx, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to unset all default member groups in transaction")
		return errors.NewDatabaseError("failed to unset all default member groups")
	}

	return nil
}

// createWithdrawalApprovalsInTx creates withdrawal approvals within a transaction
func (r *MemberGroupRepository) createWithdrawalApprovalsInTx(ctx context.Context, tx pgx.Tx, memberGroupID int, approvals []member_group.CreateWithdrawalApprovalRequest) error {
	if len(approvals) == 0 {
		return nil
	}

	query := `
		INSERT INTO member_group_withdrawal_approvals (
			member_group_id, min_amount, max_amount, user_role_ids, order_index, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
	`

	for i, approval := range approvals {
		userRoleIDsJSON, err := json.Marshal(approval.UserRoleIDs)
		if err != nil {
			r.logger.WithError(err).Error("failed to marshal user_role_ids")
			return errors.NewDatabaseError("failed to marshal user_role_ids")
		}

		_, err = dbutil.TxExecWithSchema(ctx, tx, query,
			memberGroupID, approval.MinAmount, approval.MaxAmount, userRoleIDsJSON, i+1,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to create withdrawal approval")
			return errors.NewDatabaseError("failed to create withdrawal approval")
		}
	}

	return nil
}

// deleteWithdrawalApprovalsInTx deletes withdrawal approvals within a transaction
func (r *MemberGroupRepository) deleteWithdrawalApprovalsInTx(ctx context.Context, tx pgx.Tx, memberGroupID int) error {
	query := `DELETE FROM member_group_withdrawal_approvals WHERE member_group_id = $1`

	_, err := dbutil.TxExecWithSchema(ctx, tx, query, memberGroupID)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete withdrawal approvals in transaction")
		return errors.NewDatabaseError("failed to delete withdrawal approvals")
	}

	return nil
}

// createDepositAccountsInTx creates deposit account assignments within a transaction
func (r *MemberGroupRepository) createDepositAccountsInTx(ctx context.Context, tx pgx.Tx, memberGroupID int, accountIDs []int) error {
	if len(accountIDs) == 0 {
		return nil
	}

	query := `
		INSERT INTO member_group_deposit_accounts (
			member_group_id, account_deposit_id, created_at
		) VALUES ($1, $2, CURRENT_TIMESTAMP)
	`

	for _, accountID := range accountIDs {
		_, err := dbutil.TxExecWithSchema(ctx, tx, query, memberGroupID, accountID)
		if err != nil {
			r.logger.WithError(err).Error("failed to create deposit account assignment")
			return errors.NewDatabaseError("failed to create deposit account assignment")
		}
	}

	return nil
}

// deleteDepositAccountsInTx deletes deposit account assignments within a transaction
func (r *MemberGroupRepository) deleteDepositAccountsInTx(ctx context.Context, tx pgx.Tx, memberGroupID int) error {
	query := `DELETE FROM member_group_deposit_accounts WHERE member_group_id = $1`

	_, err := dbutil.TxExecWithSchema(ctx, tx, query, memberGroupID)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete deposit accounts in transaction")
		return errors.NewDatabaseError("failed to delete deposit accounts")
	}

	return nil
}

// smartUpdateWithdrawalApprovalsInTx intelligently updates withdrawal approvals
func (r *MemberGroupRepository) smartUpdateWithdrawalApprovalsInTx(ctx context.Context, tx pgx.Tx, memberGroupID int, newApprovals []member_group.CreateWithdrawalApprovalRequest) error {
	// Get existing approvals
	existingQuery := `
		SELECT id, min_amount, max_amount, COALESCE(user_role_ids, '[]'::jsonb) as user_role_ids, order_index
		FROM member_group_withdrawal_approvals
		WHERE member_group_id = $1
		ORDER BY order_index ASC
	`

	rows, err := dbutil.TxQueryWithSchema(ctx, tx, existingQuery, memberGroupID)
	if err != nil {
		r.logger.WithError(err).Error("failed to get existing withdrawal approvals")
		return errors.NewDatabaseError("failed to get existing withdrawal approvals")
	}
	defer rows.Close()

	type existingApproval struct {
		ID          int
		MinAmount   float64
		MaxAmount   float64
		UserRoleIDs []int
		OrderIndex  int
	}

	var existing []existingApproval
	for rows.Next() {
		var approval existingApproval
		var userRoleIDsJSON []byte
		err := rows.Scan(&approval.ID, &approval.MinAmount, &approval.MaxAmount, &userRoleIDsJSON, &approval.OrderIndex)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan existing withdrawal approval")
			return errors.NewDatabaseError("failed to scan existing withdrawal approval")
		}

		// Parse JSONB array to []int
		if len(userRoleIDsJSON) > 0 {
			if err := json.Unmarshal(userRoleIDsJSON, &approval.UserRoleIDs); err != nil {
				r.logger.WithError(err).Error("failed to parse existing user_role_ids JSON")
				return errors.NewDatabaseError("failed to parse existing user_role_ids")
			}
		}

		existing = append(existing, approval)
	}

	// Helper function to compare int slices
	compareIntSlices := func(a, b []int) bool {
		if len(a) != len(b) {
			return false
		}
		for i := range a {
			if a[i] != b[i] {
				return false
			}
		}
		return true
	}

	// Compare and update
	maxLen := len(existing)
	if len(newApprovals) > maxLen {
		maxLen = len(newApprovals)
	}

	for i := 0; i < maxLen; i++ {
		if i < len(newApprovals) && i < len(existing) {
			// Update existing record if different
			newApproval := newApprovals[i]
			existingApproval := existing[i]

			if newApproval.MinAmount != existingApproval.MinAmount ||
				newApproval.MaxAmount != existingApproval.MaxAmount ||
				!compareIntSlices(newApproval.UserRoleIDs, existingApproval.UserRoleIDs) {

				userRoleIDsJSON, err := json.Marshal(newApproval.UserRoleIDs)
				if err != nil {
					r.logger.WithError(err).Error("failed to marshal user_role_ids for update")
					return errors.NewDatabaseError("failed to marshal user_role_ids for update")
				}

				updateQuery := `
					UPDATE member_group_withdrawal_approvals
					SET min_amount = $2, max_amount = $3, user_role_ids = $4, order_index = $5, updated_at = CURRENT_TIMESTAMP
					WHERE id = $1
				`
				_, err = dbutil.TxExecWithSchema(ctx, tx, updateQuery,
					existingApproval.ID, newApproval.MinAmount, newApproval.MaxAmount, userRoleIDsJSON, i+1,
				)
				if err != nil {
					r.logger.WithError(err).Error("failed to update withdrawal approval")
					return errors.NewDatabaseError("failed to update withdrawal approval")
				}
			}
		} else if i < len(newApprovals) {
			// Insert new record
			newApproval := newApprovals[i]

			userRoleIDsJSON, err := json.Marshal(newApproval.UserRoleIDs)
			if err != nil {
				r.logger.WithError(err).Error("failed to marshal user_role_ids for insert")
				return errors.NewDatabaseError("failed to marshal user_role_ids for insert")
			}

			insertQuery := `
				INSERT INTO member_group_withdrawal_approvals (
					member_group_id, min_amount, max_amount, user_role_ids, order_index, created_at, updated_at
				) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
			`
			_, err = dbutil.TxExecWithSchema(ctx, tx, insertQuery,
				memberGroupID, newApproval.MinAmount, newApproval.MaxAmount, userRoleIDsJSON, i+1,
			)
			if err != nil {
				r.logger.WithError(err).Error("failed to insert withdrawal approval")
				return errors.NewDatabaseError("failed to insert withdrawal approval")
			}
		} else {
			// Delete excess existing record
			deleteQuery := `DELETE FROM member_group_withdrawal_approvals WHERE id = $1`
			_, err := dbutil.TxExecWithSchema(ctx, tx, deleteQuery, existing[i].ID)
			if err != nil {
				r.logger.WithError(err).Error("failed to delete excess withdrawal approval")
				return errors.NewDatabaseError("failed to delete excess withdrawal approval")
			}
		}
	}

	return nil
}

// smartUpdateDepositAccountsInTx intelligently updates deposit accounts
func (r *MemberGroupRepository) smartUpdateDepositAccountsInTx(ctx context.Context, tx pgx.Tx, memberGroupID int, newAccountIDs []int) error {
	// Get existing account IDs
	existingQuery := `
		SELECT account_deposit_id
		FROM member_group_deposit_accounts
		WHERE member_group_id = $1
		ORDER BY created_at ASC
	`

	rows, err := dbutil.TxQueryWithSchema(ctx, tx, existingQuery, memberGroupID)
	if err != nil {
		r.logger.WithError(err).Error("failed to get existing deposit accounts")
		return errors.NewDatabaseError("failed to get existing deposit accounts")
	}
	defer rows.Close()

	var existingAccountIDs []int
	for rows.Next() {
		var accountID int
		err := rows.Scan(&accountID)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan existing deposit account")
			return errors.NewDatabaseError("failed to scan existing deposit account")
		}
		existingAccountIDs = append(existingAccountIDs, accountID)
	}

	// Convert slices to maps for easier comparison
	existingMap := make(map[int]bool)
	for _, id := range existingAccountIDs {
		existingMap[id] = true
	}

	newMap := make(map[int]bool)
	for _, id := range newAccountIDs {
		newMap[id] = true
	}

	// Delete accounts that are no longer needed
	for _, existingID := range existingAccountIDs {
		if !newMap[existingID] {
			deleteQuery := `DELETE FROM member_group_deposit_accounts WHERE member_group_id = $1 AND account_deposit_id = $2`
			_, err := dbutil.TxExecWithSchema(ctx, tx, deleteQuery, memberGroupID, existingID)
			if err != nil {
				r.logger.WithError(err).Error("failed to delete deposit account")
				return errors.NewDatabaseError("failed to delete deposit account")
			}
		}
	}

	// Insert new accounts that don't exist
	for _, newID := range newAccountIDs {
		if !existingMap[newID] {
			insertQuery := `
				INSERT INTO member_group_deposit_accounts (
					member_group_id, account_deposit_id, created_at
				) VALUES ($1, $2, CURRENT_TIMESTAMP)
			`
			_, err := dbutil.TxExecWithSchema(ctx, tx, insertQuery, memberGroupID, newID)
			if err != nil {
				r.logger.WithError(err).Error("failed to insert deposit account")
				return errors.NewDatabaseError("failed to insert deposit account")
			}
		}
	}

	return nil
}
