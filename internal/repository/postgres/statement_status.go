package postgres

import (
	"context"
	"fmt"
	"strings"
	"time"

	"blacking-api/internal/domain/statement_status"
	"blacking-api/internal/helper"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type statementStatusRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewStatementStatusRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.StatementStatusRepository {
	return &statementStatusRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *statementStatusRepository) Create(ctx context.Context, req *statement_status.CreateStatementStatusRequest) (*statement_status.StatementStatus, error) {
	query := `
		INSERT INTO statement_status (name, label_th, label_en, created_at)
		VALUES ($1, $2, $3, $4)
		RETURNING id, name, label_th, label_en, created_at, updated_at, deleted_at
	`

	now := time.Now()
	var ss statement_status.StatementStatus

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		req.Name, req.LabelTH, req.LabelEN, now,
	).Scan(
		&ss.ID, &ss.Name, &ss.LabelTH, &ss.LabelEN,
		&ss.CreatedAt, &ss.UpdatedAt, &ss.DeletedAt,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to create statement status")
		return nil, errors.NewDatabaseError("failed to create statement status")
	}

	return &ss, nil
}

func (r *statementStatusRepository) CreateWithTx(ctx context.Context, tx pgx.Tx, req *statement_status.CreateStatementStatusRequest) (*statement_status.StatementStatus, error) {
	query := `
		INSERT INTO statement_status (name, label_th, label_en, created_at)
		VALUES ($1, $2, $3, $4)
		RETURNING id, name, label_th, label_en, created_at, updated_at, deleted_at
	`

	now := time.Now()
	var ss statement_status.StatementStatus

	err := dbutil.TxQueryRowWithSchema(ctx, tx, query,
		req.Name, req.LabelTH, req.LabelEN, now,
	).Scan(
		&ss.ID, &ss.Name, &ss.LabelTH, &ss.LabelEN,
		&ss.CreatedAt, &ss.UpdatedAt, &ss.DeletedAt,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to create statement status with tx")
		return nil, errors.NewDatabaseError("failed to create statement status")
	}

	return &ss, nil
}

func (r *statementStatusRepository) Update(ctx context.Context, id int64, req *statement_status.UpdateStatementStatusRequest) (*statement_status.StatementStatus, error) {
	var setClauses []string
	var args []interface{}
	argCount := 1

	if req.Name != nil {
		setClauses = append(setClauses, fmt.Sprintf("name = $%d", argCount))
		args = append(args, *req.Name)
		argCount++
	}
	if req.LabelTH != nil {
		setClauses = append(setClauses, fmt.Sprintf("label_th = $%d", argCount))
		args = append(args, *req.LabelTH)
		argCount++
	}
	if req.LabelEN != nil {
		setClauses = append(setClauses, fmt.Sprintf("label_en = $%d", argCount))
		args = append(args, *req.LabelEN)
		argCount++
	}

	if len(setClauses) == 0 {
		return nil, errors.NewValidationError("no fields to update")
	}

	setClauses = append(setClauses, fmt.Sprintf("updated_at = $%d", argCount))
	args = append(args, time.Now())
	argCount++

	args = append(args, id)

	query := fmt.Sprintf(`
		UPDATE statement_status
		SET %s
		WHERE id = $%d AND deleted_at IS NULL
		RETURNING id, name, label_th, label_en, created_at, updated_at, deleted_at
	`, strings.Join(setClauses, ", "), argCount)

	var ss statement_status.StatementStatus
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(
		&ss.ID, &ss.Name, &ss.LabelTH, &ss.LabelEN,
		&ss.CreatedAt, &ss.UpdatedAt, &ss.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("statement status not found")
		}
		r.logger.WithError(err).Error("failed to update statement status")
		return nil, errors.NewDatabaseError("failed to update statement status")
	}

	return &ss, nil
}

func (r *statementStatusRepository) UpdateWithTx(ctx context.Context, tx pgx.Tx, id int64, req *statement_status.UpdateStatementStatusRequest) (*statement_status.StatementStatus, error) {
	var setClauses []string
	var args []interface{}
	argCount := 1

	if req.Name != nil {
		setClauses = append(setClauses, fmt.Sprintf("name = $%d", argCount))
		args = append(args, *req.Name)
		argCount++
	}
	if req.LabelTH != nil {
		setClauses = append(setClauses, fmt.Sprintf("label_th = $%d", argCount))
		args = append(args, *req.LabelTH)
		argCount++
	}
	if req.LabelEN != nil {
		setClauses = append(setClauses, fmt.Sprintf("label_en = $%d", argCount))
		args = append(args, *req.LabelEN)
		argCount++
	}

	if len(setClauses) == 0 {
		return nil, errors.NewValidationError("no fields to update")
	}

	setClauses = append(setClauses, fmt.Sprintf("updated_at = $%d", argCount))
	args = append(args, time.Now())
	argCount++

	args = append(args, id)

	query := fmt.Sprintf(`
		UPDATE statement_status
		SET %s
		WHERE id = $%d AND deleted_at IS NULL
		RETURNING id, name, label_th, label_en, created_at, updated_at, deleted_at
	`, strings.Join(setClauses, ", "), argCount)

	var ss statement_status.StatementStatus
	err := dbutil.TxQueryRowWithSchema(ctx, tx, query, args...).Scan(
		&ss.ID, &ss.Name, &ss.LabelTH, &ss.LabelEN,
		&ss.CreatedAt, &ss.UpdatedAt, &ss.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("statement status not found")
		}
		r.logger.WithError(err).Error("failed to update statement status with tx")
		return nil, errors.NewDatabaseError("failed to update statement status")
	}

	return &ss, nil
}

func (r *statementStatusRepository) Delete(ctx context.Context, id int64) error {
	query := `DELETE FROM statement_status WHERE id = $1`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete statement status")
		return errors.NewDatabaseError("failed to delete statement status")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("statement status not found")
	}

	return nil
}

func (r *statementStatusRepository) DeleteWithTx(ctx context.Context, tx pgx.Tx, id int64) error {
	query := `DELETE FROM statement_status WHERE id = $1`

	result, err := dbutil.TxExecWithSchema(ctx, tx, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete statement status with tx")
		return errors.NewDatabaseError("failed to delete statement status")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("statement status not found")
	}

	return nil
}

func (r *statementStatusRepository) SoftDelete(ctx context.Context, id int64) error {
	query := `
		UPDATE statement_status
		SET deleted_at = $1, updated_at = $1
		WHERE id = $2 AND deleted_at IS NULL
	`

	now := time.Now()
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, now, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to soft delete statement status")
		return errors.NewDatabaseError("failed to soft delete statement status")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("statement status not found")
	}

	return nil
}

func (r *statementStatusRepository) SoftDeleteWithTx(ctx context.Context, tx pgx.Tx, id int64) error {
	query := `
		UPDATE statement_status
		SET deleted_at = $1, updated_at = $1
		WHERE id = $2 AND deleted_at IS NULL
	`

	now := time.Now()
	result, err := dbutil.TxExecWithSchema(ctx, tx, query, now, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to soft delete statement status with tx")
		return errors.NewDatabaseError("failed to soft delete statement status")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("statement status not found")
	}

	return nil
}

func (r *statementStatusRepository) Restore(ctx context.Context, id int64) error {
	query := `
		UPDATE statement_status
		SET deleted_at = NULL, updated_at = $1
		WHERE id = $2 AND deleted_at IS NOT NULL
	`

	now := time.Now()
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, now, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to restore statement status")
		return errors.NewDatabaseError("failed to restore statement status")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("statement status not found or not deleted")
	}

	return nil
}

func (r *statementStatusRepository) RestoreWithTx(ctx context.Context, tx pgx.Tx, id int64) error {
	query := `
		UPDATE statement_status
		SET deleted_at = NULL, updated_at = $1
		WHERE id = $2 AND deleted_at IS NOT NULL
	`

	now := time.Now()
	result, err := dbutil.TxExecWithSchema(ctx, tx, query, now, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to restore statement status with tx")
		return errors.NewDatabaseError("failed to restore statement status")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("statement status not found or not deleted")
	}

	return nil
}

func (r *statementStatusRepository) GetByID(ctx context.Context, id int64) (*statement_status.StatementStatus, error) {
	query := `
		SELECT id, name, label_th, label_en, created_at, updated_at, deleted_at
		FROM statement_status
		WHERE id = $1 AND deleted_at IS NULL
	`

	var ss statement_status.StatementStatus
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&ss.ID, &ss.Name, &ss.LabelTH, &ss.LabelEN,
		&ss.CreatedAt, &ss.UpdatedAt, &ss.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("statement status not found")
		}
		r.logger.WithError(err).Error("failed to get statement status by id")
		return nil, errors.NewDatabaseError("failed to get statement status")
	}

	return &ss, nil
}

func (r *statementStatusRepository) GetByIDWithTx(ctx context.Context, tx pgx.Tx, id int64) (*statement_status.StatementStatus, error) {
	query := `
		SELECT id, name, label_th, label_en, created_at, updated_at, deleted_at
		FROM statement_status
		WHERE id = $1 AND deleted_at IS NULL
	`

	var ss statement_status.StatementStatus
	err := dbutil.TxQueryRowWithSchema(ctx, tx, query, id).Scan(
		&ss.ID, &ss.Name, &ss.LabelTH, &ss.LabelEN,
		&ss.CreatedAt, &ss.UpdatedAt, &ss.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("statement status not found")
		}
		r.logger.WithError(err).Error("failed to get statement status by id with tx")
		return nil, errors.NewDatabaseError("failed to get statement status")
	}

	return &ss, nil
}

func (r *statementStatusRepository) GetByName(ctx context.Context, name string) (*statement_status.StatementStatus, error) {
	query := `
		SELECT id, name, label_th, label_en, created_at, updated_at, deleted_at
		FROM statement_status
		WHERE name = $1 AND deleted_at IS NULL
	`

	var ss statement_status.StatementStatus
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, name).Scan(
		&ss.ID, &ss.Name, &ss.LabelTH, &ss.LabelEN,
		&ss.CreatedAt, &ss.UpdatedAt, &ss.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("statement status not found")
		}
		r.logger.WithError(err).Error("failed to get statement status by name")
		return nil, errors.NewDatabaseError("failed to get statement status")
	}

	return &ss, nil
}

func (r *statementStatusRepository) GetByNameWithTx(ctx context.Context, tx pgx.Tx, name string) (*statement_status.StatementStatus, error) {
	query := `
		SELECT id, name, label_th, label_en, created_at, updated_at, deleted_at
		FROM statement_status
		WHERE name = $1 AND deleted_at IS NULL
	`

	var ss statement_status.StatementStatus
	err := dbutil.TxQueryRowWithSchema(ctx, tx, query, name).Scan(
		&ss.ID, &ss.Name, &ss.LabelTH, &ss.LabelEN,
		&ss.CreatedAt, &ss.UpdatedAt, &ss.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("statement status not found")
		}
		r.logger.WithError(err).Error("failed to get statement status by name with tx")
		return nil, errors.NewDatabaseError("failed to get statement status")
	}

	return &ss, nil
}

func (r *statementStatusRepository) GetAll(ctx context.Context, filter *statement_status.StatementStatusFilter, pagination *helper.Pagination) ([]statement_status.StatementStatus, int64, error) {
	var whereClauses []string
	var args []interface{}
	argCount := 1

	// Handle soft delete filter
	if filter != nil && !filter.IncludeDeleted {
		whereClauses = append(whereClauses, "deleted_at IS NULL")
	} else if filter == nil {
		whereClauses = append(whereClauses, "deleted_at IS NULL")
	}

	if filter != nil {
		if filter.Name != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("name ILIKE $%d", argCount))
			args = append(args, "%"+*filter.Name+"%")
			argCount++
		}
	}

	whereClause := ""
	if len(whereClauses) > 0 {
		whereClause = "WHERE " + strings.Join(whereClauses, " AND ")
	}

	countQuery := fmt.Sprintf(`SELECT COUNT(*) FROM statement_status %s`, whereClause)
	var totalCount int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...).Scan(&totalCount)
	if err != nil {
		r.logger.WithError(err).Error("failed to count statement statuses")
		return nil, 0, errors.NewDatabaseError("failed to count statement statuses")
	}

	query := fmt.Sprintf(`
		SELECT id, name, label_th, label_en, created_at, updated_at, deleted_at
		FROM statement_status
		%s
		ORDER BY id ASC
	`, whereClause)

	if pagination != nil {
		query = fmt.Sprintf("%s LIMIT $%d OFFSET $%d", query, argCount, argCount+1)
		args = append(args, pagination.GetLimit(), pagination.GetOffset())
	}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get all statement statuses")
		return nil, 0, errors.NewDatabaseError("failed to get statement statuses")
	}
	defer rows.Close()

	var statuses []statement_status.StatementStatus
	for rows.Next() {
		var ss statement_status.StatementStatus
		err := rows.Scan(
			&ss.ID, &ss.Name, &ss.LabelTH, &ss.LabelEN,
			&ss.CreatedAt, &ss.UpdatedAt, &ss.DeletedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan statement status")
			return nil, 0, errors.NewDatabaseError("failed to scan statement status")
		}
		statuses = append(statuses, ss)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating statement status rows")
		return nil, 0, errors.NewDatabaseError("failed to get statement statuses")
	}

	return statuses, totalCount, nil
}

func (r *statementStatusRepository) GetAllWithTx(ctx context.Context, tx pgx.Tx, filter *statement_status.StatementStatusFilter, pagination *helper.Pagination) ([]statement_status.StatementStatus, int64, error) {
	var whereClauses []string
	var args []interface{}
	argCount := 1

	// Handle soft delete filter
	if filter != nil && !filter.IncludeDeleted {
		whereClauses = append(whereClauses, "deleted_at IS NULL")
	} else if filter == nil {
		whereClauses = append(whereClauses, "deleted_at IS NULL")
	}

	if filter != nil {
		if filter.Name != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("name ILIKE $%d", argCount))
			args = append(args, "%"+*filter.Name+"%")
			argCount++
		}
	}

	whereClause := ""
	if len(whereClauses) > 0 {
		whereClause = "WHERE " + strings.Join(whereClauses, " AND ")
	}

	countQuery := fmt.Sprintf(`SELECT COUNT(*) FROM statement_status %s`, whereClause)
	var totalCount int64
	err := dbutil.TxQueryRowWithSchema(ctx, tx, countQuery, args...).Scan(&totalCount)
	if err != nil {
		r.logger.WithError(err).Error("failed to count statement statuses with tx")
		return nil, 0, errors.NewDatabaseError("failed to count statement statuses")
	}

	query := fmt.Sprintf(`
		SELECT id, name, label_th, label_en, created_at, updated_at, deleted_at
		FROM statement_status
		%s
		ORDER BY id ASC
	`, whereClause)

	if pagination != nil {
		query = fmt.Sprintf("%s LIMIT $%d OFFSET $%d", query, argCount, argCount+1)
		args = append(args, pagination.GetLimit(), pagination.GetOffset())
	}

	rows, err := dbutil.TxQueryWithSchema(ctx, tx, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get all statement statuses with tx")
		return nil, 0, errors.NewDatabaseError("failed to get statement statuses")
	}
	defer rows.Close()

	var statuses []statement_status.StatementStatus
	for rows.Next() {
		var ss statement_status.StatementStatus
		err := rows.Scan(
			&ss.ID, &ss.Name, &ss.LabelTH, &ss.LabelEN,
			&ss.CreatedAt, &ss.UpdatedAt, &ss.DeletedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan statement status with tx")
			return nil, 0, errors.NewDatabaseError("failed to scan statement status")
		}
		statuses = append(statuses, ss)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating statement status rows with tx")
		return nil, 0, errors.NewDatabaseError("failed to get statement statuses")
	}

	return statuses, totalCount, nil
}
