package postgres

import (
	"context"
	"fmt"
	"strings"
	"time"

	"blacking-api/internal/domain/bank_statement"
	"blacking-api/internal/helper"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type bankStatementRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewBankStatementRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.BankStatementRepository {
	return &bankStatementRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *bankStatementRepository) Create(ctx context.Context, req *bank_statement.CreateBankStatementRequest) (*bank_statement.BankStatement, error) {
	query := `
		INSERT INTO bank_statement (
			account_id, external_id, amount, statement_type_id, statement_status_id,
			from_bank_id, from_account_number, transfer_at, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10
		) RETURNING id, account_id, external_id, amount, statement_type_id, statement_status_id,
			from_bank_id, from_account_number, transfer_at, created_at, updated_at
	`

	now := time.Now()
	var bs bank_statement.BankStatement

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		req.AccountID, req.ExternalID, req.Amount, req.StatementTypeID, req.StatementStatusID,
		req.FromBankID, req.FromAccountNumber, req.TransferAt, now, now,
	).Scan(
		&bs.ID, &bs.AccountID, &bs.ExternalID, &bs.Amount, &bs.StatementTypeID,
		&bs.StatementStatusID, &bs.FromBankID, &bs.FromAccountNumber, &bs.TransferAt,
		&bs.CreatedAt, &bs.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to create bank statement")
		return nil, errors.NewDatabaseError("failed to create bank statement")
	}

	return &bs, nil
}

func (r *bankStatementRepository) CreateWithTx(ctx context.Context, tx pgx.Tx, req *bank_statement.CreateBankStatementRequest) (*bank_statement.BankStatement, error) {
	query := `
		INSERT INTO bank_statement (
			account_id, external_id, amount, statement_type_id, statement_status_id,
			from_bank_id, from_account_number, transfer_at, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10
		) RETURNING id, account_id, external_id, amount, statement_type_id, statement_status_id,
			from_bank_id, from_account_number, transfer_at, created_at, updated_at
	`

	now := time.Now()
	var bs bank_statement.BankStatement

	err := dbutil.TxQueryRowWithSchema(ctx, tx, query,
		req.AccountID, req.ExternalID, req.Amount, req.StatementTypeID, req.StatementStatusID,
		req.FromBankID, req.FromAccountNumber, req.TransferAt, now, now,
	).Scan(
		&bs.ID, &bs.AccountID, &bs.ExternalID, &bs.Amount, &bs.StatementTypeID,
		&bs.StatementStatusID, &bs.FromBankID, &bs.FromAccountNumber, &bs.TransferAt,
		&bs.CreatedAt, &bs.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to create bank statement with tx")
		return nil, errors.NewDatabaseError("failed to create bank statement")
	}

	return &bs, nil
}

func (r *bankStatementRepository) Update(ctx context.Context, id int64, req *bank_statement.UpdateBankStatementRequest) (*bank_statement.BankStatement, error) {
	var setClauses []string
	var args []interface{}
	argCount := 1

	if req.AccountID != nil {
		setClauses = append(setClauses, fmt.Sprintf("account_id = $%d", argCount))
		args = append(args, *req.AccountID)
		argCount++
	}
	if req.ExternalID != nil {
		setClauses = append(setClauses, fmt.Sprintf("external_id = $%d", argCount))
		args = append(args, *req.ExternalID)
		argCount++
	}
	if req.Amount != nil {
		setClauses = append(setClauses, fmt.Sprintf("amount = $%d", argCount))
		args = append(args, *req.Amount)
		argCount++
	}
	if req.StatementTypeID != nil {
		setClauses = append(setClauses, fmt.Sprintf("statement_type_id = $%d", argCount))
		args = append(args, *req.StatementTypeID)
		argCount++
	}
	if req.StatementStatusID != nil {
		setClauses = append(setClauses, fmt.Sprintf("statement_status_id = $%d", argCount))
		args = append(args, *req.StatementStatusID)
		argCount++
	}
	if req.FromBankID != nil {
		setClauses = append(setClauses, fmt.Sprintf("from_bank_id = $%d", argCount))
		args = append(args, *req.FromBankID)
		argCount++
	}
	if req.FromAccountNumber != nil {
		setClauses = append(setClauses, fmt.Sprintf("from_account_number = $%d", argCount))
		args = append(args, *req.FromAccountNumber)
		argCount++
	}
	if req.TransferAt != nil {
		setClauses = append(setClauses, fmt.Sprintf("transfer_at = $%d", argCount))
		args = append(args, *req.TransferAt)
		argCount++
	}

	if len(setClauses) == 0 {
		return nil, errors.NewValidationError("no fields to update")
	}

	setClauses = append(setClauses, fmt.Sprintf("updated_at = $%d", argCount))
	args = append(args, time.Now())
	argCount++

	args = append(args, id)

	query := fmt.Sprintf(`
		UPDATE bank_statement
		SET %s
		WHERE id = $%d
		RETURNING id, account_id, external_id, amount, statement_type_id, statement_status_id,
			from_bank_id, from_account_number, transfer_at, created_at, updated_at
	`, strings.Join(setClauses, ", "), argCount)

	var bs bank_statement.BankStatement
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(
		&bs.ID, &bs.AccountID, &bs.ExternalID, &bs.Amount, &bs.StatementTypeID,
		&bs.StatementStatusID, &bs.FromBankID, &bs.FromAccountNumber, &bs.TransferAt,
		&bs.CreatedAt, &bs.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("bank statement not found")
		}
		r.logger.WithError(err).Error("failed to update bank statement")
		return nil, errors.NewDatabaseError("failed to update bank statement")
	}

	return &bs, nil
}

func (r *bankStatementRepository) UpdateWithTx(ctx context.Context, tx pgx.Tx, id int64, req *bank_statement.UpdateBankStatementRequest) (*bank_statement.BankStatement, error) {
	var setClauses []string
	var args []interface{}
	argCount := 1

	if req.AccountID != nil {
		setClauses = append(setClauses, fmt.Sprintf("account_id = $%d", argCount))
		args = append(args, *req.AccountID)
		argCount++
	}
	if req.ExternalID != nil {
		setClauses = append(setClauses, fmt.Sprintf("external_id = $%d", argCount))
		args = append(args, *req.ExternalID)
		argCount++
	}
	if req.Amount != nil {
		setClauses = append(setClauses, fmt.Sprintf("amount = $%d", argCount))
		args = append(args, *req.Amount)
		argCount++
	}
	if req.StatementTypeID != nil {
		setClauses = append(setClauses, fmt.Sprintf("statement_type_id = $%d", argCount))
		args = append(args, *req.StatementTypeID)
		argCount++
	}
	if req.StatementStatusID != nil {
		setClauses = append(setClauses, fmt.Sprintf("statement_status_id = $%d", argCount))
		args = append(args, *req.StatementStatusID)
		argCount++
	}
	if req.FromBankID != nil {
		setClauses = append(setClauses, fmt.Sprintf("from_bank_id = $%d", argCount))
		args = append(args, *req.FromBankID)
		argCount++
	}
	if req.FromAccountNumber != nil {
		setClauses = append(setClauses, fmt.Sprintf("from_account_number = $%d", argCount))
		args = append(args, *req.FromAccountNumber)
		argCount++
	}
	if req.TransferAt != nil {
		setClauses = append(setClauses, fmt.Sprintf("transfer_at = $%d", argCount))
		args = append(args, *req.TransferAt)
		argCount++
	}

	if len(setClauses) == 0 {
		return nil, errors.NewValidationError("no fields to update")
	}

	setClauses = append(setClauses, fmt.Sprintf("updated_at = $%d", argCount))
	args = append(args, time.Now())
	argCount++

	args = append(args, id)

	query := fmt.Sprintf(`
		UPDATE bank_statement
		SET %s
		WHERE id = $%d
		RETURNING id, account_id, external_id, amount, statement_type_id, statement_status_id,
			from_bank_id, from_account_number, transfer_at, created_at, updated_at
	`, strings.Join(setClauses, ", "), argCount)

	var bs bank_statement.BankStatement
	err := dbutil.TxQueryRowWithSchema(ctx, tx, query, args...).Scan(
		&bs.ID, &bs.AccountID, &bs.ExternalID, &bs.Amount, &bs.StatementTypeID,
		&bs.StatementStatusID, &bs.FromBankID, &bs.FromAccountNumber, &bs.TransferAt,
		&bs.CreatedAt, &bs.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("bank statement not found")
		}
		r.logger.WithError(err).Error("failed to update bank statement with tx")
		return nil, errors.NewDatabaseError("failed to update bank statement")
	}

	return &bs, nil
}

func (r *bankStatementRepository) Delete(ctx context.Context, id int64) error {
	query := `DELETE FROM bank_statement WHERE id = $1`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete bank statement")
		return errors.NewDatabaseError("failed to delete bank statement")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("bank statement not found")
	}

	return nil
}

func (r *bankStatementRepository) DeleteWithTx(ctx context.Context, tx pgx.Tx, id int64) error {
	query := `DELETE FROM bank_statement WHERE id = $1`

	result, err := dbutil.TxExecWithSchema(ctx, tx, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete bank statement with tx")
		return errors.NewDatabaseError("failed to delete bank statement")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("bank statement not found")
	}

	return nil
}

func (r *bankStatementRepository) GetByID(ctx context.Context, id int64) (*bank_statement.BankStatement, error) {
	query := `
		SELECT id, account_id, external_id, amount, statement_type_id, statement_status_id,
			from_bank_id, from_account_number, transfer_at, created_at, updated_at
		FROM bank_statement
		WHERE id = $1
	`

	var bs bank_statement.BankStatement
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&bs.ID, &bs.AccountID, &bs.ExternalID, &bs.Amount, &bs.StatementTypeID,
		&bs.StatementStatusID, &bs.FromBankID, &bs.FromAccountNumber, &bs.TransferAt,
		&bs.CreatedAt, &bs.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("bank statement not found")
		}
		r.logger.WithError(err).Error("failed to get bank statement by id")
		return nil, errors.NewDatabaseError("failed to get bank statement")
	}

	return &bs, nil
}

func (r *bankStatementRepository) GetByIDWithTx(ctx context.Context, tx pgx.Tx, id int64) (*bank_statement.BankStatement, error) {
	query := `
		SELECT id, account_id, external_id, amount, statement_type_id, statement_status_id,
			from_bank_id, from_account_number, transfer_at, created_at, updated_at
		FROM bank_statement
		WHERE id = $1
	`

	var bs bank_statement.BankStatement
	err := dbutil.TxQueryRowWithSchema(ctx, tx, query, id).Scan(
		&bs.ID, &bs.AccountID, &bs.ExternalID, &bs.Amount, &bs.StatementTypeID,
		&bs.StatementStatusID, &bs.FromBankID, &bs.FromAccountNumber, &bs.TransferAt,
		&bs.CreatedAt, &bs.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("bank statement not found")
		}
		r.logger.WithError(err).Error("failed to get bank statement by id with tx")
		return nil, errors.NewDatabaseError("failed to get bank statement")
	}

	return &bs, nil
}

func (r *bankStatementRepository) GetByExternalID(ctx context.Context, externalID int64) (*bank_statement.BankStatement, error) {
	query := `
		SELECT id, account_id, external_id, amount, statement_type_id, statement_status_id,
			from_bank_id, from_account_number, transfer_at, created_at, updated_at
		FROM bank_statement
		WHERE external_id = $1
	`

	var bs bank_statement.BankStatement
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, externalID).Scan(
		&bs.ID, &bs.AccountID, &bs.ExternalID, &bs.Amount, &bs.StatementTypeID,
		&bs.StatementStatusID, &bs.FromBankID, &bs.FromAccountNumber, &bs.TransferAt,
		&bs.CreatedAt, &bs.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("bank statement not found")
		}
		r.logger.WithError(err).Error("failed to get bank statement by external id")
		return nil, errors.NewDatabaseError("failed to get bank statement")
	}

	return &bs, nil
}

func (r *bankStatementRepository) GetByExternalIDWithTx(ctx context.Context, tx pgx.Tx, externalID int64) (*bank_statement.BankStatement, error) {
	query := `
		SELECT id, account_id, external_id, amount, statement_type_id, statement_status_id,
			from_bank_id, from_account_number, transfer_at, created_at, updated_at
		FROM bank_statement
		WHERE external_id = $1
	`

	var bs bank_statement.BankStatement
	err := dbutil.TxQueryRowWithSchema(ctx, tx, query, externalID).Scan(
		&bs.ID, &bs.AccountID, &bs.ExternalID, &bs.Amount, &bs.StatementTypeID,
		&bs.StatementStatusID, &bs.FromBankID, &bs.FromAccountNumber, &bs.TransferAt,
		&bs.CreatedAt, &bs.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("bank statement not found")
		}
		r.logger.WithError(err).Error("failed to get bank statement by external id with tx")
		return nil, errors.NewDatabaseError("failed to get bank statement")
	}

	return &bs, nil
}

func (r *bankStatementRepository) GetAll(ctx context.Context, filter *bank_statement.BankStatementFilter, pagination *helper.Pagination) ([]bank_statement.BankStatement, int64, error) {
	var whereClauses []string
	var args []interface{}
	argCount := 1

	if filter != nil {
		if filter.AccountID != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("account_id = $%d", argCount))
			args = append(args, *filter.AccountID)
			argCount++
		}
		if filter.ExternalID != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("external_id = $%d", argCount))
			args = append(args, *filter.ExternalID)
			argCount++
		}
		if filter.StatementTypeID != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("statement_type_id = $%d", argCount))
			args = append(args, *filter.StatementTypeID)
			argCount++
		}
		if filter.StatementStatusID != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("statement_status_id = $%d", argCount))
			args = append(args, *filter.StatementStatusID)
			argCount++
		}
		if filter.FromBankID != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("from_bank_id = $%d", argCount))
			args = append(args, *filter.FromBankID)
			argCount++
		}
		if filter.FromAccountNumber != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("from_account_number ILIKE $%d", argCount))
			args = append(args, "%"+*filter.FromAccountNumber+"%")
			argCount++
		}
		if filter.TransferAtFrom != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("transfer_at >= $%d", argCount))
			args = append(args, *filter.TransferAtFrom)
			argCount++
		}
		if filter.TransferAtTo != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("transfer_at <= $%d", argCount))
			args = append(args, *filter.TransferAtTo)
			argCount++
		}
		if filter.AmountMin != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("amount >= $%d", argCount))
			args = append(args, *filter.AmountMin)
			argCount++
		}
		if filter.AmountMax != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("amount <= $%d", argCount))
			args = append(args, *filter.AmountMax)
			argCount++
		}
	}

	whereClause := ""
	if len(whereClauses) > 0 {
		whereClause = "WHERE " + strings.Join(whereClauses, " AND ")
	}

	countQuery := fmt.Sprintf(`SELECT COUNT(*) FROM bank_statement %s`, whereClause)
	var totalCount int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...).Scan(&totalCount)
	if err != nil {
		r.logger.WithError(err).Error("failed to count bank statements")
		return nil, 0, errors.NewDatabaseError("failed to count bank statements")
	}

	query := fmt.Sprintf(`
		SELECT id, account_id, external_id, amount, statement_type_id, statement_status_id,
			from_bank_id, from_account_number, transfer_at, created_at, updated_at
		FROM bank_statement
		%s
		ORDER BY created_at DESC
	`, whereClause)

	if pagination != nil {
		query = fmt.Sprintf("%s LIMIT $%d OFFSET $%d", query, argCount, argCount+1)
		args = append(args, pagination.GetLimit(), pagination.GetOffset())
	}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get all bank statements")
		return nil, 0, errors.NewDatabaseError("failed to get bank statements")
	}
	defer rows.Close()

	var statements []bank_statement.BankStatement
	for rows.Next() {
		var bs bank_statement.BankStatement
		err := rows.Scan(
			&bs.ID, &bs.AccountID, &bs.ExternalID, &bs.Amount, &bs.StatementTypeID,
			&bs.StatementStatusID, &bs.FromBankID, &bs.FromAccountNumber, &bs.TransferAt,
			&bs.CreatedAt, &bs.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan bank statement")
			return nil, 0, errors.NewDatabaseError("failed to scan bank statement")
		}
		statements = append(statements, bs)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating bank statement rows")
		return nil, 0, errors.NewDatabaseError("failed to get bank statements")
	}

	return statements, totalCount, nil
}

func (r *bankStatementRepository) GetAllWithTx(ctx context.Context, tx pgx.Tx, filter *bank_statement.BankStatementFilter, pagination *helper.Pagination) ([]bank_statement.BankStatement, int64, error) {
	var whereClauses []string
	var args []interface{}
	argCount := 1

	if filter != nil {
		if filter.AccountID != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("account_id = $%d", argCount))
			args = append(args, *filter.AccountID)
			argCount++
		}
		if filter.ExternalID != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("external_id = $%d", argCount))
			args = append(args, *filter.ExternalID)
			argCount++
		}
		if filter.StatementTypeID != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("statement_type_id = $%d", argCount))
			args = append(args, *filter.StatementTypeID)
			argCount++
		}
		if filter.StatementStatusID != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("statement_status_id = $%d", argCount))
			args = append(args, *filter.StatementStatusID)
			argCount++
		}
		if filter.FromBankID != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("from_bank_id = $%d", argCount))
			args = append(args, *filter.FromBankID)
			argCount++
		}
		if filter.FromAccountNumber != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("from_account_number ILIKE $%d", argCount))
			args = append(args, "%"+*filter.FromAccountNumber+"%")
			argCount++
		}
		if filter.TransferAtFrom != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("transfer_at >= $%d", argCount))
			args = append(args, *filter.TransferAtFrom)
			argCount++
		}
		if filter.TransferAtTo != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("transfer_at <= $%d", argCount))
			args = append(args, *filter.TransferAtTo)
			argCount++
		}
		if filter.AmountMin != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("amount >= $%d", argCount))
			args = append(args, *filter.AmountMin)
			argCount++
		}
		if filter.AmountMax != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("amount <= $%d", argCount))
			args = append(args, *filter.AmountMax)
			argCount++
		}
	}

	whereClause := ""
	if len(whereClauses) > 0 {
		whereClause = "WHERE " + strings.Join(whereClauses, " AND ")
	}

	countQuery := fmt.Sprintf(`SELECT COUNT(*) FROM bank_statement %s`, whereClause)
	var totalCount int64
	err := dbutil.TxQueryRowWithSchema(ctx, tx, countQuery, args...).Scan(&totalCount)
	if err != nil {
		r.logger.WithError(err).Error("failed to count bank statements with tx")
		return nil, 0, errors.NewDatabaseError("failed to count bank statements")
	}

	query := fmt.Sprintf(`
		SELECT id, account_id, external_id, amount, statement_type_id, statement_status_id,
			from_bank_id, from_account_number, transfer_at, created_at, updated_at
		FROM bank_statement
		%s
		ORDER BY created_at DESC
	`, whereClause)

	if pagination != nil {
		query = fmt.Sprintf("%s LIMIT $%d OFFSET $%d", query, argCount, argCount+1)
		args = append(args, pagination.GetLimit(), pagination.GetOffset())
	}

	rows, err := dbutil.TxQueryWithSchema(ctx, tx, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get all bank statements with tx")
		return nil, 0, errors.NewDatabaseError("failed to get bank statements")
	}
	defer rows.Close()

	var statements []bank_statement.BankStatement
	for rows.Next() {
		var bs bank_statement.BankStatement
		err := rows.Scan(
			&bs.ID, &bs.AccountID, &bs.ExternalID, &bs.Amount, &bs.StatementTypeID,
			&bs.StatementStatusID, &bs.FromBankID, &bs.FromAccountNumber, &bs.TransferAt,
			&bs.CreatedAt, &bs.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan bank statement with tx")
			return nil, 0, errors.NewDatabaseError("failed to scan bank statement")
		}
		statements = append(statements, bs)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating bank statement rows with tx")
		return nil, 0, errors.NewDatabaseError("failed to get bank statements")
	}

	return statements, totalCount, nil
}

func (r *bankStatementRepository) GetByAccountID(ctx context.Context, accountID int64, pagination *helper.Pagination) ([]bank_statement.BankStatement, int64, error) {
	countQuery := `SELECT COUNT(*) FROM bank_statement WHERE account_id = $1`
	var totalCount int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, accountID).Scan(&totalCount)
	if err != nil {
		r.logger.WithError(err).Error("failed to count bank statements by account id")
		return nil, 0, errors.NewDatabaseError("failed to count bank statements")
	}

	query := `
		SELECT id, account_id, external_id, amount, statement_type_id, statement_status_id,
			from_bank_id, from_account_number, transfer_at, created_at, updated_at
		FROM bank_statement
		WHERE account_id = $1
		ORDER BY created_at DESC
	`

	args := []interface{}{accountID}
	if pagination != nil {
		query = fmt.Sprintf("%s LIMIT $2 OFFSET $3", query)
		args = append(args, pagination.GetLimit(), pagination.GetOffset())
	}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get bank statements by account id")
		return nil, 0, errors.NewDatabaseError("failed to get bank statements")
	}
	defer rows.Close()

	var statements []bank_statement.BankStatement
	for rows.Next() {
		var bs bank_statement.BankStatement
		err := rows.Scan(
			&bs.ID, &bs.AccountID, &bs.ExternalID, &bs.Amount, &bs.StatementTypeID,
			&bs.StatementStatusID, &bs.FromBankID, &bs.FromAccountNumber, &bs.TransferAt,
			&bs.CreatedAt, &bs.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan bank statement by account id")
			return nil, 0, errors.NewDatabaseError("failed to scan bank statement")
		}
		statements = append(statements, bs)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating bank statement rows by account id")
		return nil, 0, errors.NewDatabaseError("failed to get bank statements")
	}

	return statements, totalCount, nil
}

func (r *bankStatementRepository) GetByAccountIDWithTx(ctx context.Context, tx pgx.Tx, accountID int64, pagination *helper.Pagination) ([]bank_statement.BankStatement, int64, error) {
	countQuery := `SELECT COUNT(*) FROM bank_statement WHERE account_id = $1`
	var totalCount int64
	err := dbutil.TxQueryRowWithSchema(ctx, tx, countQuery, accountID).Scan(&totalCount)
	if err != nil {
		r.logger.WithError(err).Error("failed to count bank statements by account id with tx")
		return nil, 0, errors.NewDatabaseError("failed to count bank statements")
	}

	query := `
		SELECT id, account_id, external_id, amount, statement_type_id, statement_status_id,
			from_bank_id, from_account_number, transfer_at, created_at, updated_at
		FROM bank_statement
		WHERE account_id = $1
		ORDER BY created_at DESC
	`

	args := []interface{}{accountID}
	if pagination != nil {
		query = fmt.Sprintf("%s LIMIT $2 OFFSET $3", query)
		args = append(args, pagination.GetLimit(), pagination.GetOffset())
	}

	rows, err := dbutil.TxQueryWithSchema(ctx, tx, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get bank statements by account id with tx")
		return nil, 0, errors.NewDatabaseError("failed to get bank statements")
	}
	defer rows.Close()

	var statements []bank_statement.BankStatement
	for rows.Next() {
		var bs bank_statement.BankStatement
		err := rows.Scan(
			&bs.ID, &bs.AccountID, &bs.ExternalID, &bs.Amount, &bs.StatementTypeID,
			&bs.StatementStatusID, &bs.FromBankID, &bs.FromAccountNumber, &bs.TransferAt,
			&bs.CreatedAt, &bs.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan bank statement by account id with tx")
			return nil, 0, errors.NewDatabaseError("failed to scan bank statement")
		}
		statements = append(statements, bs)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating bank statement rows by account id with tx")
		return nil, 0, errors.NewDatabaseError("failed to get bank statements")
	}

	return statements, totalCount, nil
}
