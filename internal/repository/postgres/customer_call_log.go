package postgres

import (
	"context"
	"fmt"
	"strings"

	"blacking-api/internal/domain/customer_call_log"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type CustomerCallLogRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewCustomerCallLogRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.CustomerCallLogRepository {
	return &CustomerCallLogRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *CustomerCallLogRepository) Create(ctx context.Context, callLog *customer_call_log.CustomerCallLog) error {
	query := `
		INSERT INTO customer_call_logs (member_id, admin_id, admin_name, notes, call_duration, call_status, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		RETURNING id
	`

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		callLog.MemberID, callLog.AdminID, callLog.AdminName, callLog.Notes,
		callLog.CallDuration, callLog.CallStatus, callLog.CreatedAt, callLog.UpdatedAt,
	).Scan(&callLog.ID)

	if err != nil {
		r.logger.WithError(err).Error("failed to create customer call log")
		return errors.NewDatabaseError("failed to create customer call log")
	}

	return nil
}

func (r *CustomerCallLogRepository) GetByID(ctx context.Context, id int) (*customer_call_log.CustomerCallLog, error) {
	query := `
		SELECT id, member_id, admin_id, admin_name, notes, call_duration, call_status, created_at, updated_at
		FROM customer_call_logs
		WHERE id = $1
	`

	callLog := &customer_call_log.CustomerCallLog{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&callLog.ID, &callLog.MemberID, &callLog.AdminID, &callLog.AdminName,
		&callLog.Notes, &callLog.CallDuration, &callLog.CallStatus,
		&callLog.CreatedAt, &callLog.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("customer call log not found")
		}
		r.logger.WithError(err).Error("failed to get customer call log by ID")
		return nil, errors.NewDatabaseError("failed to get customer call log")
	}

	return callLog, nil
}

func (r *CustomerCallLogRepository) Update(ctx context.Context, callLog *customer_call_log.CustomerCallLog) error {
	query := `
		UPDATE customer_call_logs
		SET notes = $2, call_duration = $3, call_status = $4, updated_at = $5
		WHERE id = $1
	`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		callLog.ID, callLog.Notes, callLog.CallDuration, callLog.CallStatus, callLog.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to update customer call log")
		return errors.NewDatabaseError("failed to update customer call log")
	}

	return nil
}

func (r *CustomerCallLogRepository) Delete(ctx context.Context, id int) error {
	query := `DELETE FROM customer_call_logs WHERE id = $1`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete customer call log")
		return errors.NewDatabaseError("failed to delete customer call log")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("customer call log not found")
	}

	return nil
}

func (r *CustomerCallLogRepository) List(ctx context.Context, limit, offset int) ([]*customer_call_log.CustomerCallLog, error) {
	query := `
		SELECT id, member_id, admin_id, admin_name, notes, call_duration, call_status, created_at, updated_at
		FROM customer_call_logs
		ORDER BY created_at DESC
		LIMIT $1 OFFSET $2
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, limit, offset)
	if err != nil {
		r.logger.WithError(err).Error("failed to list customer call logs")
		return nil, errors.NewDatabaseError("failed to list customer call logs")
	}
	defer rows.Close()

	var callLogs []*customer_call_log.CustomerCallLog
	for rows.Next() {
		callLog := &customer_call_log.CustomerCallLog{}
		err := rows.Scan(
			&callLog.ID, &callLog.MemberID, &callLog.AdminID, &callLog.AdminName,
			&callLog.Notes, &callLog.CallDuration, &callLog.CallStatus,
			&callLog.CreatedAt, &callLog.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan customer call log row")
			return nil, errors.NewDatabaseError("failed to scan customer call log")
		}
		callLogs = append(callLogs, callLog)
	}

	return callLogs, nil
}

func (r *CustomerCallLogRepository) ListWithFilter(ctx context.Context, limit, offset int, filter *customer_call_log.CustomerCallLogFilter) ([]*customer_call_log.CustomerCallLog, error) {
	baseQuery := `
		SELECT id, member_id, admin_id, admin_name, notes, call_duration, call_status, created_at, updated_at
		FROM customer_call_logs
	`

	var conditions []string
	var args []interface{}
	argIndex := 1

	// Apply filters
	if filter.MemberID != nil {
		conditions = append(conditions, fmt.Sprintf("member_id = $%d", argIndex))
		args = append(args, *filter.MemberID)
		argIndex++
	}

	if filter.AdminID != nil {
		conditions = append(conditions, fmt.Sprintf("admin_id = $%d", argIndex))
		args = append(args, *filter.AdminID)
		argIndex++
	}

	if filter.CallStatus != "" {
		conditions = append(conditions, fmt.Sprintf("call_status = $%d", argIndex))
		args = append(args, filter.CallStatus)
		argIndex++
	}

	if filter.StartDateTime != nil {
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, *filter.StartDateTime)
		argIndex++
	}

	if filter.EndDateTime != nil {
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, *filter.EndDateTime)
		argIndex++
	}

	// Build final query
	query := baseQuery
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	query += " ORDER BY created_at DESC"
	query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, offset)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list customer call logs with filter")
		return nil, errors.NewDatabaseError("failed to list customer call logs")
	}
	defer rows.Close()

	var callLogs []*customer_call_log.CustomerCallLog
	for rows.Next() {
		callLog := &customer_call_log.CustomerCallLog{}
		err := rows.Scan(
			&callLog.ID, &callLog.MemberID, &callLog.AdminID, &callLog.AdminName,
			&callLog.Notes, &callLog.CallDuration, &callLog.CallStatus,
			&callLog.CreatedAt, &callLog.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan customer call log row")
			return nil, errors.NewDatabaseError("failed to scan customer call log")
		}
		callLogs = append(callLogs, callLog)
	}

	return callLogs, nil
}

func (r *CustomerCallLogRepository) Count(ctx context.Context) (int64, error) {
	query := `SELECT COUNT(*) FROM customer_call_logs`

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count customer call logs")
		return 0, errors.NewDatabaseError("failed to count customer call logs")
	}

	return count, nil
}

func (r *CustomerCallLogRepository) CountWithFilter(ctx context.Context, filter *customer_call_log.CustomerCallLogFilter) (int64, error) {
	baseQuery := `SELECT COUNT(*) FROM customer_call_logs`

	var conditions []string
	var args []interface{}
	argIndex := 1

	// Apply same filters as ListWithFilter
	if filter.MemberID != nil {
		conditions = append(conditions, fmt.Sprintf("member_id = $%d", argIndex))
		args = append(args, *filter.MemberID)
		argIndex++
	}

	if filter.AdminID != nil {
		conditions = append(conditions, fmt.Sprintf("admin_id = $%d", argIndex))
		args = append(args, *filter.AdminID)
		argIndex++
	}

	if filter.CallStatus != "" {
		conditions = append(conditions, fmt.Sprintf("call_status = $%d", argIndex))
		args = append(args, filter.CallStatus)
		argIndex++
	}

	if filter.StartDateTime != nil {
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, *filter.StartDateTime)
		argIndex++
	}

	if filter.EndDateTime != nil {
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, *filter.EndDateTime)
		argIndex++
	}

	query := baseQuery
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count customer call logs with filter")
		return 0, errors.NewDatabaseError("failed to count customer call logs")
	}

	return count, nil
}

func (r *CustomerCallLogRepository) CountByMemberID(ctx context.Context, memberID int) (int64, error) {
	query := `SELECT COUNT(*) FROM customer_call_logs WHERE member_id = $1`

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, memberID).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count customer call logs by member ID")
		return 0, errors.NewDatabaseError("failed to count customer call logs by member ID")
	}

	return count, nil
}

func (r *CustomerCallLogRepository) GetLatestByMemberID(ctx context.Context, memberID int) (*customer_call_log.CustomerCallLog, error) {
	query := `
		SELECT id, member_id, admin_id, admin_name, notes, call_duration, call_status, created_at, updated_at
		FROM customer_call_logs
		WHERE member_id = $1
		ORDER BY created_at DESC
		LIMIT 1
	`

	callLog := &customer_call_log.CustomerCallLog{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, memberID).Scan(
		&callLog.ID, &callLog.MemberID, &callLog.AdminID, &callLog.AdminName,
		&callLog.Notes, &callLog.CallDuration, &callLog.CallStatus,
		&callLog.CreatedAt, &callLog.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("no customer call log found for member")
		}
		r.logger.WithError(err).Error("failed to get latest customer call log by member ID")
		return nil, errors.NewDatabaseError("failed to get latest customer call log")
	}

	return callLog, nil
}
