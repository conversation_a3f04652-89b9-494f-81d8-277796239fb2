package postgres

import (
	"blacking-api/internal/domain/bank_transaction_slip"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type BankTransactionSlipRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewBankTransactionSlipRepository creates a new bank transaction slip repository
func NewBankTransactionSlipRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.BankTransactionSlipRepository {
	return &BankTransactionSlipRepository{
		pool:   pool,
		logger: logger,
	}
}

// <PERSON><PERSON> creates a new bank transaction slip
func (r *BankTransactionSlipRepository) Create(ctx context.Context, slip *bank_transaction_slip.BankTransactionSlip) (*bank_transaction_slip.BankTransactionSlip, error) {
	query := `
		INSERT INTO bank_transaction_slip (
			member_id, status, transaction_id, slip_url, raw_qr_code,
			from_account_number, from_account_name, from_bank_name,
			to_account_number, to_account_name, amount,
			transaction_date, remark
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
		) RETURNING id, created_at, updated_at
	`

	slip.Status = bank_transaction_slip.StatusPending // Default status

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		slip.MemberID,
		slip.Status,
		slip.TransactionID,
		slip.SlipUrl,
		slip.RawQrCode,
		slip.FromAccountNumber,
		slip.FromAccountName,
		slip.FromBankName,
		slip.ToAccountNumber,
		slip.ToAccountName,
		slip.Amount,
		slip.TransactionDate,
		slip.Remark,
	)

	err := row.Scan(&slip.ID, &slip.CreatedAt, &slip.UpdatedAt)
	if err != nil {
		r.logger.WithError(err).Error("failed to create bank transaction slip")
		return nil, errors.NewDatabaseError("failed to create bank transaction slip")
	}

	return slip, nil
}

// GetByID retrieves a bank transaction slip by ID with joined data
func (r *BankTransactionSlipRepository) GetByID(ctx context.Context, id int64) (*bank_transaction_slip.BankTransactionSlip, error) {
	query := `
		SELECT 
			bts.id, bts.member_id, bts.status, bts.transaction_id,
			bts.slip_url, bts.raw_qr_code, bts.from_account_number, bts.from_account_name,
			bts.from_bank_name, bts.to_account_number, bts.to_account_name,
			bts.amount, bts.transaction_date, bts.remark,
			bts.created_at, bts.updated_at,
			m.username as member_username,
			m.fullname as member_fullname
		FROM bank_transaction_slip bts
		LEFT JOIN members m ON bts.member_id = m.id
		WHERE bts.id = $1
	`

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)

	slip := &bank_transaction_slip.BankTransactionSlip{}
	err := row.Scan(
		&slip.ID, &slip.MemberID, &slip.Status, &slip.TransactionID,
		&slip.SlipUrl, &slip.RawQrCode, &slip.FromAccountNumber, &slip.FromAccountName,
		&slip.FromBankName, &slip.ToAccountNumber, &slip.ToAccountName,
		&slip.Amount, &slip.TransactionDate, &slip.Remark,
		&slip.CreatedAt, &slip.UpdatedAt,
		&slip.MemberUsername, &slip.MemberFullname,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("bank transaction slip not found")
		}
		r.logger.WithError(err).Error("failed to get bank transaction slip by ID")
		return nil, errors.NewDatabaseError("failed to get bank transaction slip")
	}

	return slip, nil
}

// GetByTransactionID retrieves a bank transaction slip by transaction ID
func (r *BankTransactionSlipRepository) GetByTransactionID(ctx context.Context, transactionID int64) (*bank_transaction_slip.BankTransactionSlip, error) {
	query := `
		SELECT 
			bts.id, bts.member_id, bts.status, bts.transaction_id,
			bts.slip_url, bts.raw_qr_code, bts.from_account_number, bts.from_account_name,
			bts.from_bank_name, bts.to_account_number, bts.to_account_name,
			bts.amount, bts.transaction_date, bts.remark,
			bts.created_at, bts.updated_at,
			m.username as member_username,
			m.fullname as member_fullname
		FROM bank_transaction_slip bts
		LEFT JOIN members m ON bts.member_id = m.id
		WHERE bts.transaction_id = $1
	`

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, transactionID)

	slip := &bank_transaction_slip.BankTransactionSlip{}
	err := row.Scan(
		&slip.ID, &slip.MemberID, &slip.Status, &slip.TransactionID,
		&slip.SlipUrl, &slip.RawQrCode, &slip.FromAccountNumber, &slip.FromAccountName,
		&slip.FromBankName, &slip.ToAccountNumber, &slip.ToAccountName,
		&slip.Amount, &slip.TransactionDate, &slip.Remark,
		&slip.CreatedAt, &slip.UpdatedAt,
		&slip.MemberUsername, &slip.MemberFullname,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("bank transaction slip not found")
		}
		r.logger.WithError(err).Error("failed to get bank transaction slip by transaction ID")
		return nil, errors.NewDatabaseError("failed to get bank transaction slip")
	}

	return slip, nil
}

// GetList retrieves a paginated list of bank transaction slips with filters
func (r *BankTransactionSlipRepository) GetList(ctx context.Context, filter *bank_transaction_slip.FilterRequest) ([]*bank_transaction_slip.BankTransactionSlip, int64, error) {
	// Base query
	baseQuery := `
		FROM bank_transaction_slip bts
		LEFT JOIN members m ON bts.member_id = m.id
		WHERE 1=1
	`

	// Build conditions
	conditions := []string{}
	args := []interface{}{}
	argCount := 0

	if filter.MemberID != nil {
		argCount++
		conditions = append(conditions, fmt.Sprintf("bts.member_id = $%d", argCount))
		args = append(args, *filter.MemberID)
	}

	if filter.Status != nil {
		argCount++
		conditions = append(conditions, fmt.Sprintf("bts.status = $%d", argCount))
		args = append(args, *filter.Status)
	}

	if filter.TransactionID != nil {
		argCount++
		conditions = append(conditions, fmt.Sprintf("bts.transaction_id = $%d", argCount))
		args = append(args, *filter.TransactionID)
	}

	if filter.FromDate != nil {
		argCount++
		conditions = append(conditions, fmt.Sprintf("bts.transaction_date >= $%d", argCount))
		args = append(args, *filter.FromDate)
	}

	if filter.ToDate != nil {
		argCount++
		conditions = append(conditions, fmt.Sprintf("bts.transaction_date <= $%d", argCount))
		args = append(args, *filter.ToDate)
	}

	if filter.Search != nil && *filter.Search != "" {
		argCount++
		conditions = append(conditions, fmt.Sprintf(
			"(m.username ILIKE $%d OR m.fullname ILIKE $%d OR bts.from_account_name ILIKE $%d OR bts.to_account_name ILIKE $%d)",
			argCount, argCount, argCount, argCount,
		))
		searchTerm := "%" + *filter.Search + "%"
		args = append(args, searchTerm)
	}

	// Add conditions to base query
	if len(conditions) > 0 {
		baseQuery += " AND " + strings.Join(conditions, " AND ")
	}

	// Count total records
	countQuery := "SELECT COUNT(*) " + baseQuery
	var total int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.WithError(err).Error("failed to count bank transaction slips")
		return nil, 0, errors.NewDatabaseError("failed to count bank transaction slips")
	}

	// Get paginated data
	selectQuery := `
		SELECT 
			bts.id, bts.member_id, bts.status, bts.transaction_id,
			bts.slip_url, bts.raw_qr_code, bts.from_account_number, bts.from_account_name,
			bts.from_bank_name, bts.to_account_number, bts.to_account_name,
			bts.amount, bts.transaction_date, bts.remark,
			bts.created_at, bts.updated_at,
			m.username as member_username,
			m.fullname as member_fullname
	` + baseQuery + ` ORDER BY bts.created_at DESC`

	// Add pagination
	if filter.Limit > 0 {
		argCount++
		selectQuery += fmt.Sprintf(" LIMIT $%d", argCount)
		args = append(args, filter.Limit)

		if filter.Page > 0 {
			argCount++
			offset := (filter.Page - 1) * filter.Limit
			selectQuery += fmt.Sprintf(" OFFSET $%d", argCount)
			args = append(args, offset)
		}
	}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, selectQuery, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get bank transaction slips")
		return nil, 0, errors.NewDatabaseError("failed to get bank transaction slips")
	}
	defer rows.Close()

	slips := []*bank_transaction_slip.BankTransactionSlip{}
	for rows.Next() {
		slip := &bank_transaction_slip.BankTransactionSlip{}
		err := rows.Scan(
			&slip.ID, &slip.MemberID, &slip.Status, &slip.TransactionID,
			&slip.SlipUrl, &slip.RawQrCode, &slip.FromAccountNumber, &slip.FromAccountName,
			&slip.FromBankName, &slip.ToAccountNumber, &slip.ToAccountName,
			&slip.Amount, &slip.TransactionDate, &slip.Remark,
			&slip.CreatedAt, &slip.UpdatedAt,
			&slip.MemberUsername, &slip.MemberFullname,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan bank transaction slip")
			continue
		}
		slips = append(slips, slip)
	}

	return slips, total, nil
}

// GetByMemberID retrieves bank transaction slips for a specific member
func (r *BankTransactionSlipRepository) GetByMemberID(ctx context.Context, memberID int64, filter *bank_transaction_slip.FilterRequest) ([]*bank_transaction_slip.BankTransactionSlip, int64, error) {
	// Set member ID in filter
	filter.MemberID = &memberID
	return r.GetList(ctx, filter)
}

// Update updates a bank transaction slip
func (r *BankTransactionSlipRepository) Update(ctx context.Context, id int64, req *bank_transaction_slip.UpdateRequest) error {
	query := `
		UPDATE bank_transaction_slip 
		SET 
			raw_qr_code = $2,
			from_account_number = $3,
			from_account_name = $4,
			from_bank_name = $5,
			to_account_number = $6,
			to_account_name = $7,
			amount = $8,
			transaction_date = $9,
			remark = $10,
			updated_at = $11
		WHERE id = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		id,
		req.RawQrCode,
		req.FromAccountNumber,
		req.FromAccountName,
		req.FromBankName,
		req.ToAccountNumber,
		req.ToAccountName,
		req.Amount,
		req.TransactionDate,
		req.Remark,
		time.Now(),
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to update bank transaction slip")
		return errors.NewDatabaseError("failed to update bank transaction slip")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("bank transaction slip not found")
	}

	return nil
}

// UpdateStatus updates the status of a bank transaction slip
func (r *BankTransactionSlipRepository) UpdateStatus(ctx context.Context, id int64, status int, remark string) error {
	query := `
		UPDATE bank_transaction_slip 
		SET 
			status = $2,
			remark = CASE WHEN $3 != '' THEN $3 ELSE remark END,
			updated_at = $4
		WHERE id = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id, status, remark, time.Now())
	if err != nil {
		r.logger.WithError(err).Error("failed to update bank transaction slip status")
		return errors.NewDatabaseError("failed to update bank transaction slip status")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("bank transaction slip not found")
	}

	return nil
}

// Delete deletes a bank transaction slip
func (r *BankTransactionSlipRepository) Delete(ctx context.Context, id int64) error {
	query := `DELETE FROM bank_transaction_slip WHERE id = $1`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete bank transaction slip")
		return errors.NewDatabaseError("failed to delete bank transaction slip")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("bank transaction slip not found")
	}

	return nil
}

// CountByStatus counts bank transaction slips by status
func (r *BankTransactionSlipRepository) CountByStatus(ctx context.Context, status int) (int64, error) {
	query := `SELECT COUNT(*) FROM bank_transaction_slip WHERE status = $1`

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, status).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count bank transaction slips by status")
		return 0, errors.NewDatabaseError("failed to count bank transaction slips")
	}

	return count, nil
}

// CountByMemberID counts bank transaction slips by member ID
func (r *BankTransactionSlipRepository) CountByMemberID(ctx context.Context, memberID int64) (int64, error) {
	query := `SELECT COUNT(*) FROM bank_transaction_slip WHERE member_id = $1`

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, memberID).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count bank transaction slips by member ID")
		return 0, errors.NewDatabaseError("failed to count bank transaction slips")
	}

	return count, nil
}
