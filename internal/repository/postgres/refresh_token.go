package postgres

import (
	"context"

	"blacking-api/internal/domain/refresh_token"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type RefreshTokenRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewRefreshTokenRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.RefreshTokenRepository {
	return &RefreshTokenRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *RefreshTokenRepository) Create(ctx context.Context, rt *refresh_token.RefreshToken) error {
	query := `
		INSERT INTO refresh_tokens (token, user_id, member_id, user_type, expires_at, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
		RETURNING id
	`

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		rt.Token, rt.UserID, rt.MemberID, rt.UserType, rt.ExpiresAt, rt.CreatedAt, rt.UpdatedAt,
	).Scan(&rt.ID)

	if err != nil {
		r.logger.WithError(err).WithField("token", rt.Token).Error("failed to create refresh token")
		return errors.NewDatabaseError("failed to create refresh token")
	}

	r.logger.WithField("token_id", rt.ID).WithField("user_type", rt.UserType).Info("refresh token created successfully")
	return nil
}

func (r *RefreshTokenRepository) GetByToken(ctx context.Context, token string) (*refresh_token.RefreshToken, error) {
	query := `
		SELECT id, token, user_id, member_id, user_type, expires_at, created_at, updated_at
		FROM refresh_tokens
		WHERE token = $1
	`

	rt := &refresh_token.RefreshToken{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, token).Scan(
		&rt.ID, &rt.Token, &rt.UserID, &rt.MemberID, &rt.UserType, &rt.ExpiresAt, &rt.CreatedAt, &rt.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("refresh token not found")
		}
		r.logger.WithError(err).WithField("token", token).Error("failed to get refresh token by token")
		return nil, errors.NewDatabaseError("failed to get refresh token")
	}

	return rt, nil
}

func (r *RefreshTokenRepository) DeleteByToken(ctx context.Context, token string) error {
	query := `DELETE FROM refresh_tokens WHERE token = $1`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, token)
	if err != nil {
		r.logger.WithError(err).WithField("token", token).Error("failed to delete refresh token by token")
		return errors.NewDatabaseError("failed to delete refresh token")
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return errors.NewNotFoundError("refresh token not found")
	}

	r.logger.WithField("token", token).Info("refresh token deleted successfully")
	return nil
}

func (r *RefreshTokenRepository) DeleteByUserID(ctx context.Context, userID int, userType refresh_token.UserType) error {
	var query string
	var args []interface{}

	if userType == refresh_token.UserTypeAdmin {
		query = `DELETE FROM refresh_tokens WHERE user_id = $1 AND user_type = $2`
		args = []interface{}{userID, userType}
	} else {
		query = `DELETE FROM refresh_tokens WHERE member_id = $1 AND user_type = $2`
		args = []interface{}{userID, userType}
	}

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).WithField("user_id", userID).WithField("user_type", userType).Error("failed to delete refresh tokens by user ID")
		return errors.NewDatabaseError("failed to delete refresh tokens")
	}

	rowsAffected := result.RowsAffected()
	r.logger.WithField("user_id", userID).WithField("user_type", userType).WithField("deleted_count", rowsAffected).Info("refresh tokens deleted by user ID")
	return nil
}

func (r *RefreshTokenRepository) DeleteExpired(ctx context.Context) error {
	query := `DELETE FROM refresh_tokens WHERE expires_at < NOW()`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete expired refresh tokens")
		return errors.NewDatabaseError("failed to delete expired refresh tokens")
	}

	rowsAffected := result.RowsAffected()
	r.logger.WithField("deleted_count", rowsAffected).Info("expired refresh tokens deleted")
	return nil
}

func (r *RefreshTokenRepository) GetByUserID(ctx context.Context, userID int, userType refresh_token.UserType) ([]*refresh_token.RefreshToken, error) {
	var query string
	var args []interface{}

	if userType == refresh_token.UserTypeAdmin {
		query = `
			SELECT id, token, user_id, member_id, user_type, expires_at, created_at, updated_at
			FROM refresh_tokens
			WHERE user_id = $1 AND user_type = $2
			ORDER BY created_at DESC
		`
		args = []interface{}{userID, userType}
	} else {
		query = `
			SELECT id, token, user_id, member_id, user_type, expires_at, created_at, updated_at
			FROM refresh_tokens
			WHERE member_id = $1 AND user_type = $2
			ORDER BY created_at DESC
		`
		args = []interface{}{userID, userType}
	}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).WithField("user_id", userID).WithField("user_type", userType).Error("failed to get refresh tokens by user ID")
		return nil, errors.NewDatabaseError("failed to get refresh tokens")
	}
	defer rows.Close()

	var tokens []*refresh_token.RefreshToken
	for rows.Next() {
		rt := &refresh_token.RefreshToken{}
		err := rows.Scan(&rt.ID, &rt.Token, &rt.UserID, &rt.MemberID, &rt.UserType, &rt.ExpiresAt, &rt.CreatedAt, &rt.UpdatedAt)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan refresh token")
			return nil, errors.NewDatabaseError("failed to scan refresh token")
		}
		tokens = append(tokens, rt)
	}

	return tokens, nil
}
