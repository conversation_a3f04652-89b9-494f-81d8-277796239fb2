package postgres

import (
	"context"
	"fmt"
	"strings"
	"time"

	"blacking-api/internal/domain/statement_type"
	"blacking-api/internal/helper"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type statementTypeRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewStatementTypeRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.StatementTypeRepository {
	return &statementTypeRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *statementTypeRepository) Create(ctx context.Context, req *statement_type.CreateStatementTypeRequest) (*statement_type.StatementType, error) {
	query := `
		INSERT INTO statement_type (name, label_th, label_en, created_at)
		VALUES ($1, $2, $3, $4)
		RETURNING id, name, label_th, label_en, created_at, updated_at, deleted_at
	`

	now := time.Now()
	var st statement_type.StatementType

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		req.Name, req.LabelTH, req.LabelEN, now,
	).Scan(
		&st.ID, &st.Name, &st.LabelTH, &st.LabelEN,
		&st.CreatedAt, &st.UpdatedAt, &st.DeletedAt,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to create statement type")
		return nil, errors.NewDatabaseError("failed to create statement type")
	}

	return &st, nil
}

func (r *statementTypeRepository) CreateWithTx(ctx context.Context, tx pgx.Tx, req *statement_type.CreateStatementTypeRequest) (*statement_type.StatementType, error) {
	query := `
		INSERT INTO statement_type (name, label_th, label_en, created_at)
		VALUES ($1, $2, $3, $4)
		RETURNING id, name, label_th, label_en, created_at, updated_at, deleted_at
	`

	now := time.Now()
	var st statement_type.StatementType

	err := dbutil.TxQueryRowWithSchema(ctx, tx, query,
		req.Name, req.LabelTH, req.LabelEN, now,
	).Scan(
		&st.ID, &st.Name, &st.LabelTH, &st.LabelEN,
		&st.CreatedAt, &st.UpdatedAt, &st.DeletedAt,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to create statement type with tx")
		return nil, errors.NewDatabaseError("failed to create statement type")
	}

	return &st, nil
}

func (r *statementTypeRepository) Update(ctx context.Context, id int64, req *statement_type.UpdateStatementTypeRequest) (*statement_type.StatementType, error) {
	var setClauses []string
	var args []interface{}
	argCount := 1

	if req.Name != nil {
		setClauses = append(setClauses, fmt.Sprintf("name = $%d", argCount))
		args = append(args, *req.Name)
		argCount++
	}
	if req.LabelTH != nil {
		setClauses = append(setClauses, fmt.Sprintf("label_th = $%d", argCount))
		args = append(args, *req.LabelTH)
		argCount++
	}
	if req.LabelEN != nil {
		setClauses = append(setClauses, fmt.Sprintf("label_en = $%d", argCount))
		args = append(args, *req.LabelEN)
		argCount++
	}

	if len(setClauses) == 0 {
		return nil, errors.NewValidationError("no fields to update")
	}

	setClauses = append(setClauses, fmt.Sprintf("updated_at = $%d", argCount))
	args = append(args, time.Now())
	argCount++

	args = append(args, id)

	query := fmt.Sprintf(`
		UPDATE statement_type
		SET %s
		WHERE id = $%d AND deleted_at IS NULL
		RETURNING id, name, label_th, label_en, created_at, updated_at, deleted_at
	`, strings.Join(setClauses, ", "), argCount)

	var st statement_type.StatementType
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(
		&st.ID, &st.Name, &st.LabelTH, &st.LabelEN,
		&st.CreatedAt, &st.UpdatedAt, &st.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("statement type not found")
		}
		r.logger.WithError(err).Error("failed to update statement type")
		return nil, errors.NewDatabaseError("failed to update statement type")
	}

	return &st, nil
}

func (r *statementTypeRepository) UpdateWithTx(ctx context.Context, tx pgx.Tx, id int64, req *statement_type.UpdateStatementTypeRequest) (*statement_type.StatementType, error) {
	var setClauses []string
	var args []interface{}
	argCount := 1

	if req.Name != nil {
		setClauses = append(setClauses, fmt.Sprintf("name = $%d", argCount))
		args = append(args, *req.Name)
		argCount++
	}
	if req.LabelTH != nil {
		setClauses = append(setClauses, fmt.Sprintf("label_th = $%d", argCount))
		args = append(args, *req.LabelTH)
		argCount++
	}
	if req.LabelEN != nil {
		setClauses = append(setClauses, fmt.Sprintf("label_en = $%d", argCount))
		args = append(args, *req.LabelEN)
		argCount++
	}

	if len(setClauses) == 0 {
		return nil, errors.NewValidationError("no fields to update")
	}

	setClauses = append(setClauses, fmt.Sprintf("updated_at = $%d", argCount))
	args = append(args, time.Now())
	argCount++

	args = append(args, id)

	query := fmt.Sprintf(`
		UPDATE statement_type
		SET %s
		WHERE id = $%d AND deleted_at IS NULL
		RETURNING id, name, label_th, label_en, created_at, updated_at, deleted_at
	`, strings.Join(setClauses, ", "), argCount)

	var st statement_type.StatementType
	err := dbutil.TxQueryRowWithSchema(ctx, tx, query, args...).Scan(
		&st.ID, &st.Name, &st.LabelTH, &st.LabelEN,
		&st.CreatedAt, &st.UpdatedAt, &st.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("statement type not found")
		}
		r.logger.WithError(err).Error("failed to update statement type with tx")
		return nil, errors.NewDatabaseError("failed to update statement type")
	}

	return &st, nil
}

func (r *statementTypeRepository) Delete(ctx context.Context, id int64) error {
	query := `DELETE FROM statement_type WHERE id = $1`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete statement type")
		return errors.NewDatabaseError("failed to delete statement type")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("statement type not found")
	}

	return nil
}

func (r *statementTypeRepository) DeleteWithTx(ctx context.Context, tx pgx.Tx, id int64) error {
	query := `DELETE FROM statement_type WHERE id = $1`

	result, err := dbutil.TxExecWithSchema(ctx, tx, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete statement type with tx")
		return errors.NewDatabaseError("failed to delete statement type")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("statement type not found")
	}

	return nil
}

func (r *statementTypeRepository) SoftDelete(ctx context.Context, id int64) error {
	query := `
		UPDATE statement_type
		SET deleted_at = $1, updated_at = $1
		WHERE id = $2 AND deleted_at IS NULL
	`

	now := time.Now()
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, now, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to soft delete statement type")
		return errors.NewDatabaseError("failed to soft delete statement type")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("statement type not found")
	}

	return nil
}

func (r *statementTypeRepository) SoftDeleteWithTx(ctx context.Context, tx pgx.Tx, id int64) error {
	query := `
		UPDATE statement_type
		SET deleted_at = $1, updated_at = $1
		WHERE id = $2 AND deleted_at IS NULL
	`

	now := time.Now()
	result, err := dbutil.TxExecWithSchema(ctx, tx, query, now, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to soft delete statement type with tx")
		return errors.NewDatabaseError("failed to soft delete statement type")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("statement type not found")
	}

	return nil
}

func (r *statementTypeRepository) Restore(ctx context.Context, id int64) error {
	query := `
		UPDATE statement_type
		SET deleted_at = NULL, updated_at = $1
		WHERE id = $2 AND deleted_at IS NOT NULL
	`

	now := time.Now()
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, now, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to restore statement type")
		return errors.NewDatabaseError("failed to restore statement type")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("statement type not found or not deleted")
	}

	return nil
}

func (r *statementTypeRepository) RestoreWithTx(ctx context.Context, tx pgx.Tx, id int64) error {
	query := `
		UPDATE statement_type
		SET deleted_at = NULL, updated_at = $1
		WHERE id = $2 AND deleted_at IS NOT NULL
	`

	now := time.Now()
	result, err := dbutil.TxExecWithSchema(ctx, tx, query, now, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to restore statement type with tx")
		return errors.NewDatabaseError("failed to restore statement type")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("statement type not found or not deleted")
	}

	return nil
}

func (r *statementTypeRepository) GetByID(ctx context.Context, id int64) (*statement_type.StatementType, error) {
	query := `
		SELECT id, name, label_th, label_en, created_at, updated_at, deleted_at
		FROM statement_type
		WHERE id = $1 AND deleted_at IS NULL
	`

	var st statement_type.StatementType
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&st.ID, &st.Name, &st.LabelTH, &st.LabelEN,
		&st.CreatedAt, &st.UpdatedAt, &st.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("statement type not found")
		}
		r.logger.WithError(err).Error("failed to get statement type by id")
		return nil, errors.NewDatabaseError("failed to get statement type")
	}

	return &st, nil
}

func (r *statementTypeRepository) GetByIDWithTx(ctx context.Context, tx pgx.Tx, id int64) (*statement_type.StatementType, error) {
	query := `
		SELECT id, name, label_th, label_en, created_at, updated_at, deleted_at
		FROM statement_type
		WHERE id = $1 AND deleted_at IS NULL
	`

	var st statement_type.StatementType
	err := dbutil.TxQueryRowWithSchema(ctx, tx, query, id).Scan(
		&st.ID, &st.Name, &st.LabelTH, &st.LabelEN,
		&st.CreatedAt, &st.UpdatedAt, &st.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("statement type not found")
		}
		r.logger.WithError(err).Error("failed to get statement type by id with tx")
		return nil, errors.NewDatabaseError("failed to get statement type")
	}

	return &st, nil
}

func (r *statementTypeRepository) GetByName(ctx context.Context, name string) (*statement_type.StatementType, error) {
	query := `
		SELECT id, name, label_th, label_en, created_at, updated_at, deleted_at
		FROM statement_type
		WHERE name = $1 AND deleted_at IS NULL
	`

	var st statement_type.StatementType
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, name).Scan(
		&st.ID, &st.Name, &st.LabelTH, &st.LabelEN,
		&st.CreatedAt, &st.UpdatedAt, &st.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("statement type not found")
		}
		r.logger.WithError(err).Error("failed to get statement type by name")
		return nil, errors.NewDatabaseError("failed to get statement type")
	}

	return &st, nil
}

func (r *statementTypeRepository) GetByNameWithTx(ctx context.Context, tx pgx.Tx, name string) (*statement_type.StatementType, error) {
	query := `
		SELECT id, name, label_th, label_en, created_at, updated_at, deleted_at
		FROM statement_type
		WHERE name = $1 AND deleted_at IS NULL
	`

	var st statement_type.StatementType
	err := dbutil.TxQueryRowWithSchema(ctx, tx, query, name).Scan(
		&st.ID, &st.Name, &st.LabelTH, &st.LabelEN,
		&st.CreatedAt, &st.UpdatedAt, &st.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("statement type not found")
		}
		r.logger.WithError(err).Error("failed to get statement type by name with tx")
		return nil, errors.NewDatabaseError("failed to get statement type")
	}

	return &st, nil
}

func (r *statementTypeRepository) GetAll(ctx context.Context, filter *statement_type.StatementTypeFilter, pagination *helper.Pagination) ([]statement_type.StatementType, int64, error) {
	var whereClauses []string
	var args []interface{}
	argCount := 1

	// Handle soft delete filter
	if filter != nil && !filter.IncludeDeleted {
		whereClauses = append(whereClauses, "deleted_at IS NULL")
	} else if filter == nil {
		whereClauses = append(whereClauses, "deleted_at IS NULL")
	}

	if filter != nil {
		if filter.Name != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("name ILIKE $%d", argCount))
			args = append(args, "%"+*filter.Name+"%")
			argCount++
		}
	}

	whereClause := ""
	if len(whereClauses) > 0 {
		whereClause = "WHERE " + strings.Join(whereClauses, " AND ")
	}

	countQuery := fmt.Sprintf(`SELECT COUNT(*) FROM statement_type %s`, whereClause)
	var totalCount int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...).Scan(&totalCount)
	if err != nil {
		r.logger.WithError(err).Error("failed to count statement types")
		return nil, 0, errors.NewDatabaseError("failed to count statement types")
	}

	query := fmt.Sprintf(`
		SELECT id, name, label_th, label_en, created_at, updated_at, deleted_at
		FROM statement_type
		%s
		ORDER BY id ASC
	`, whereClause)

	if pagination != nil {
		query = fmt.Sprintf("%s LIMIT $%d OFFSET $%d", query, argCount, argCount+1)
		args = append(args, pagination.GetLimit(), pagination.GetOffset())
	}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get all statement types")
		return nil, 0, errors.NewDatabaseError("failed to get statement types")
	}
	defer rows.Close()

	var types []statement_type.StatementType
	for rows.Next() {
		var st statement_type.StatementType
		err := rows.Scan(
			&st.ID, &st.Name, &st.LabelTH, &st.LabelEN,
			&st.CreatedAt, &st.UpdatedAt, &st.DeletedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan statement type")
			return nil, 0, errors.NewDatabaseError("failed to scan statement type")
		}
		types = append(types, st)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating statement type rows")
		return nil, 0, errors.NewDatabaseError("failed to get statement types")
	}

	return types, totalCount, nil
}

func (r *statementTypeRepository) GetAllWithTx(ctx context.Context, tx pgx.Tx, filter *statement_type.StatementTypeFilter, pagination *helper.Pagination) ([]statement_type.StatementType, int64, error) {
	var whereClauses []string
	var args []interface{}
	argCount := 1

	// Handle soft delete filter
	if filter != nil && !filter.IncludeDeleted {
		whereClauses = append(whereClauses, "deleted_at IS NULL")
	} else if filter == nil {
		whereClauses = append(whereClauses, "deleted_at IS NULL")
	}

	if filter != nil {
		if filter.Name != nil {
			whereClauses = append(whereClauses, fmt.Sprintf("name ILIKE $%d", argCount))
			args = append(args, "%"+*filter.Name+"%")
			argCount++
		}
	}

	whereClause := ""
	if len(whereClauses) > 0 {
		whereClause = "WHERE " + strings.Join(whereClauses, " AND ")
	}

	countQuery := fmt.Sprintf(`SELECT COUNT(*) FROM statement_type %s`, whereClause)
	var totalCount int64
	err := dbutil.TxQueryRowWithSchema(ctx, tx, countQuery, args...).Scan(&totalCount)
	if err != nil {
		r.logger.WithError(err).Error("failed to count statement types with tx")
		return nil, 0, errors.NewDatabaseError("failed to count statement types")
	}

	query := fmt.Sprintf(`
		SELECT id, name, label_th, label_en, created_at, updated_at, deleted_at
		FROM statement_type
		%s
		ORDER BY id ASC
	`, whereClause)

	if pagination != nil {
		query = fmt.Sprintf("%s LIMIT $%d OFFSET $%d", query, argCount, argCount+1)
		args = append(args, pagination.GetLimit(), pagination.GetOffset())
	}

	rows, err := dbutil.TxQueryWithSchema(ctx, tx, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get all statement types with tx")
		return nil, 0, errors.NewDatabaseError("failed to get statement types")
	}
	defer rows.Close()

	var types []statement_type.StatementType
	for rows.Next() {
		var st statement_type.StatementType
		err := rows.Scan(
			&st.ID, &st.Name, &st.LabelTH, &st.LabelEN,
			&st.CreatedAt, &st.UpdatedAt, &st.DeletedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan statement type with tx")
			return nil, 0, errors.NewDatabaseError("failed to scan statement type")
		}
		types = append(types, st)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating statement type rows with tx")
		return nil, 0, errors.NewDatabaseError("failed to get statement types")
	}

	return types, totalCount, nil
}
