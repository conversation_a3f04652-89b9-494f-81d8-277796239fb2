package postgres

import (
	"blacking-api/internal/domain/promotion_web"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"bytes"
	"context"
	"crypto/rand"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"math/big"
	"mime/multipart"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
)

type PromotionWebRepository struct {
	pool     *pgxpool.Pool
	logger   logger.Logger
	datetime interfaces.DateTimeRepository
}

func NewPromotionWebRepository(pool *pgxpool.Pool, logger logger.Logger, datetime interfaces.DateTimeRepository) interfaces.PromotionWebRepository {
	return &PromotionWebRepository{
		pool:     pool,
		logger:   logger,
		datetime: datetime,
	}
}

// generateRandomString generates a random string for unique file naming
func (r *PromotionWebRepository) generateRandomString() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	const length = 16

	result := make([]byte, length)
	for i := range result {
		num, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		result[i] = charset[num.Int64()]
	}
	return string(result)
}

func (r *PromotionWebRepository) CreatePromotionWeb(ctx context.Context, req promotion_web.PromotionWebCreateRequest) (int64, error) {
	query := `
		INSERT INTO promotion_web (
			promotion_web_type_id, promotion_web_status_id, name, short_description, description,
			condition_detail, image_url, start_date, end_date, free_bonus_amount, privilege_per_day,
			able_withdraw_morethan, promotion_web_bonus_condition_id, bonus_condition_amount,
			promotion_web_bonus_type_id, bonus_type_amount, able_withdraw_pertime,
			promotion_web_turnover_type_id, turnover_amount, monday, tuesday, wednesday, thursday,
			friday, saturday, sunday, time_start, time_end, promotion_web_date_type_id,
			bonus_type_amount_max, hidden_url_link, created_by_user_id, updated_by_user_id,
			created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19,
			$20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35
		) RETURNING id
	`

	now := time.Now().UTC()
	var id int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		req.PromotionWebTypeId, req.PromotionWebStatusId, req.Name, req.ShortDescription, req.Description,
		req.ConditionDetail, req.ImageUrl, req.StartDate, req.EndDate, req.FreeBonusAmount, req.PrivilegePerDay,
		req.AbleWithdrawMorethan, req.PromotionWebBonusConditionId, req.BonusConditionAmount,
		req.PromotionWebBonusTypeId, req.BonusTypeAmount, req.AbleWithdrawPertime,
		req.PromotionWebTurnoverTypeId, req.TurnoverAmount, req.Monday, req.Tuesday, req.Wednesday, req.Thursday,
		req.Friday, req.Saturday, req.Sunday, req.TimeStart, req.TimeEnd, req.PromotionWebDateTypeId,
		req.BonusTypeAmountMax, req.HiddenUrlLink, req.CreatedByAdminId, req.CreatedByAdminId,
		now, now,
	).Scan(&id)

	if err != nil {
		if pgErr, ok := err.(*pgconn.PgError); ok {
			switch pgErr.Code {
			case "23505": // unique_violation
				return 0, errors.NewValidationError("promotion web already exists")
			}
		}
		r.logger.WithError(err).Error("failed to create promotion web")
		return 0, errors.NewDatabaseError("failed to create promotion web")
	}

	return id, nil
}

func (r *PromotionWebRepository) GetPromotionWebList(ctx context.Context, req promotion_web.PromotionWebGetListRequest) ([]promotion_web.PromotionWebGetListResponse, int64, error) {
	var list []promotion_web.PromotionWebGetListResponse
	var total int64

	// Build WHERE conditions
	var whereConditions []string
	var args []interface{}
	argIndex := 1

	// Base WHERE condition
	whereConditions = append(whereConditions, "promotion_web.deleted_at IS NULL")

	// Date filters
	if req.StartDate != "" {
		startDateAtBkk, err := r.datetime.ParseBodBkk(req.StartDate)
		if err != nil {
			return nil, 0, errors.NewValidationError("invalid start date format")
		}
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web.created_at >= $%d", argIndex))
		args = append(args, *startDateAtBkk)
		argIndex++
	}

	if req.EndDate != "" {
		endDateAtBkk, err := r.datetime.ParseEodBkk(req.EndDate)
		if err != nil {
			return nil, 0, errors.NewValidationError("invalid end date format")
		}
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web.created_at <= $%d", argIndex))
		args = append(args, *endDateAtBkk)
		argIndex++
	}

	// Search filter
	if req.Search != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web.name ILIKE $%d", argIndex))
		args = append(args, "%"+req.Search+"%")
		argIndex++
	}

	// Status filter
	if req.PromotionWebStatusId != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web.promotion_web_status_id = $%d", argIndex))
		args = append(args, *req.PromotionWebStatusId)
		argIndex++
	}

	whereClause := strings.Join(whereConditions, " AND ")

	// Get total count
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM promotion_web
		LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id
		LEFT JOIN promotion_web_status ON promotion_web.promotion_web_status_id = promotion_web_status.id
		LEFT JOIN users AS admincreate ON promotion_web.created_by_user_id = admincreate.id
		LEFT JOIN users AS adminupdate ON promotion_web.updated_by_user_id = adminupdate.id
		LEFT JOIN users AS admincancel ON promotion_web.canceled_by_user_id = admincancel.id
		LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id
		WHERE %s
	`, whereClause)

	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.WithError(err).Error("failed to count promotion web list")
		return nil, 0, errors.NewDatabaseError("failed to count promotion web list")
	}

	if total > 0 {
		// Build the main query with pagination
		selectedFields := `
			promotion_web.id AS id,
			promotion_web.promotion_web_type_id AS promotion_web_type_id,
			promotion_web_type.label_th AS promotion_web_type_th,
			promotion_web.promotion_web_status_id AS promotion_web_status_id,
			promotion_web_status.label_th AS promotion_web_status_th,
			promotion_web.name AS name,
			promotion_web.start_date AS start_date,
			promotion_web.end_date AS end_date,
			promotion_web.created_by_user_id AS created_by_admin_id,
			CASE WHEN promotion_web.created_by_user_id = 0 THEN 'อัตโนมัติ' ELSE admincreate.username END AS created_by_admin_name,
			promotion_web.updated_by_user_id AS updated_by_admin_id,
			CASE WHEN promotion_web.updated_by_user_id = 0 THEN 'อัตโนมัติ' ELSE adminupdate.username END AS updated_by_admin_name,
			promotion_web.canceled_by_user_id AS canceled_by_admin_id,
			CASE WHEN promotion_web.canceled_by_user_id = 0 THEN 'อัตโนมัติ' ELSE admincancel.username END AS canceled_by_admin_name,
			promotion_web.updated_at AS updated_at,
			promotion_web.promotion_web_date_type_id AS promotion_web_date_type_id,
			promotion_web_date_type.label_th AS promotion_web_date_type_th,
			promotion_web.time_start AS time_start,
			promotion_web.time_end AS time_end,
			promotion_web.hidden_url_link AS hidden_url_link
		`

		// Add pagination parameters
		offset := (req.Page - 1) * req.Limit
		paginationArgs := append(args, req.Limit, offset)

		dataQuery := fmt.Sprintf(`
			SELECT %s
			FROM promotion_web
			LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id
			LEFT JOIN promotion_web_status ON promotion_web.promotion_web_status_id = promotion_web_status.id
			LEFT JOIN users AS admincreate ON promotion_web.created_by_user_id = admincreate.id
			LEFT JOIN users AS adminupdate ON promotion_web.updated_by_user_id = adminupdate.id
			LEFT JOIN users AS admincancel ON promotion_web.canceled_by_user_id = admincancel.id
			LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id
			WHERE %s
			ORDER BY promotion_web.priority_order ASC
			LIMIT $%d OFFSET $%d
		`, selectedFields, whereClause, len(args)+1, len(args)+2)

		rows, err := dbutil.QueryWithSchema(ctx, r.pool, dataQuery, paginationArgs...)
		if err != nil {
			r.logger.WithError(err).Error("failed to get promotion web list")
			return nil, 0, errors.NewDatabaseError("failed to get promotion web list")
		}
		defer rows.Close()

		for rows.Next() {
			var item promotion_web.PromotionWebGetListResponse
			var promotionWebTypeTh, promotionWebStatusTh, createdByAdminName, updatedByAdminName, canceledByAdminName, promotionWebDateTypeTh sql.NullString
			var timeStart, timeEnd sql.NullString

			err := rows.Scan(
				&item.Id, &item.PromotionWebTypeId, &promotionWebTypeTh,
				&item.PromotionWebStatusId, &promotionWebStatusTh,
				&item.Name, &item.StartDate, &item.EndDate,
				&item.CreatedByAdminId, &createdByAdminName,
				&item.UpdatedByAdminId, &updatedByAdminName,
				&item.CanceledByAdminId, &canceledByAdminName,
				&item.UpdatedAt, &item.PromotionWebDateTypeId, &promotionWebDateTypeTh,
				&timeStart, &timeEnd, &item.HiddenUrlLink,
			)
			if err != nil {
				r.logger.WithError(err).Error("failed to scan promotion web row")
				return nil, 0, errors.NewDatabaseError("failed to scan promotion web row")
			}

			// Convert sql.NullString to string for all potentially NULL fields from LEFT JOINs
			if promotionWebTypeTh.Valid {
				item.PromotionWebTypeTh = promotionWebTypeTh.String
			}
			if promotionWebStatusTh.Valid {
				item.PromotionWebStatusTh = promotionWebStatusTh.String
			}
			if createdByAdminName.Valid {
				item.CreatedByAdminName = createdByAdminName.String
			}
			if updatedByAdminName.Valid {
				item.UpdatedByAdminName = updatedByAdminName.String
			}
			if canceledByAdminName.Valid {
				// CanceledByAdminName is a pointer to string, so assign the pointer
				name := canceledByAdminName.String
				item.CanceledByAdminName = &name
			}
			if promotionWebDateTypeTh.Valid {
				item.PromotionWebDateTypeTh = promotionWebDateTypeTh.String
			}
			if timeStart.Valid {
				item.TimeStart = timeStart.String
			}
			if timeEnd.Valid {
				item.TimeEnd = timeEnd.String
			}

			list = append(list, item)
		}

		if err = rows.Err(); err != nil {
			r.logger.WithError(err).Error("error iterating promotion web rows")
			return nil, 0, errors.NewDatabaseError("error iterating promotion web rows")
		}
	}

	return list, total, nil
}

func (r *PromotionWebRepository) GetPromotionWebById(ctx context.Context, id int64) (*promotion_web.PromotionWebGetByIdResponse, error) {
	query := `
		SELECT
			promotion_web.id AS id,
			promotion_web.promotion_web_type_id AS promotion_web_type_id,
			promotion_web_type.label_th AS promotion_web_type_th,
			promotion_web.promotion_web_status_id AS promotion_web_status_id,
			promotion_web_status.label_th AS promotion_web_status_th,
			promotion_web.condition_detail AS condition_detail,
			promotion_web.image_url AS image_url,
			promotion_web.name AS name,
			promotion_web.short_description AS short_description,
			promotion_web.description AS description,
			promotion_web.start_date AS start_date,
			promotion_web.end_date AS end_date,
			promotion_web.free_bonus_amount AS free_bonus_amount,
			promotion_web.privilege_per_day AS privilege_per_day,
			promotion_web.able_withdraw_morethan AS able_withdraw_morethan,
			promotion_web.promotion_web_bonus_condition_id AS promotion_web_bonus_condition_id,
			promotion_web_bonus_condition.label_th AS promotion_web_bonus_condition_th,
			promotion_web_bonus_condition.syntax AS promotion_web_bonus_condition_syntax,
			promotion_web.bonus_condition_amount AS bonus_condition_amount,
			promotion_web.promotion_web_bonus_type_id AS promotion_web_bonus_type_id,
			promotion_web_bonus_type.label_th AS promotion_web_bonus_type_th,
			promotion_web.bonus_type_amount AS bonus_type_amount,
			promotion_web.able_withdraw_pertime AS able_withdraw_pertime,
			promotion_web.promotion_web_turnover_type_id AS promotion_web_turnover_type_id,
			promotion_web_turnover_type.label_th AS promotion_web_turnover_type_th,
			promotion_web.turnover_amount AS turnover_amount,
			promotion_web.monday AS monday,
			promotion_web.tuesday AS tuesday,
			promotion_web.wednesday AS wednesday,
			promotion_web.thursday AS thursday,
			promotion_web.friday AS friday,
			promotion_web.saturday AS saturday,
			promotion_web.sunday AS sunday,
			promotion_web.time_start AS time_start,
			promotion_web.time_end AS time_end,
			promotion_web.promotion_web_date_type_id AS promotion_web_date_type_id,
			promotion_web_date_type.label_th AS promotion_web_date_type_th,
			promotion_web.bonus_type_amount_max AS bonus_type_amount_max
		FROM promotion_web
		LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id
		LEFT JOIN promotion_web_status ON promotion_web.promotion_web_status_id = promotion_web_status.id
		LEFT JOIN promotion_web_bonus_condition ON promotion_web.promotion_web_bonus_condition_id = promotion_web_bonus_condition.id
		LEFT JOIN promotion_web_bonus_type ON promotion_web.promotion_web_bonus_type_id = promotion_web_bonus_type.id
		LEFT JOIN promotion_web_turnover_type ON promotion_web.promotion_web_turnover_type_id = promotion_web_turnover_type.id
		LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id
		WHERE promotion_web.id = $1 AND promotion_web.deleted_at IS NULL
	`

	var result promotion_web.PromotionWebGetByIdResponse
	var promotionWebTypeTh, promotionWebStatusTh sql.NullString
	var bonusConditionTh, bonusConditionSyntax, bonusTypeTh, turnoverTypeTh, promotionWebDateTypeTh sql.NullString
	var bonusConditionId, bonusTypeId, turnoverTypeId, promotionWebDateTypeId sql.NullInt64
	var monday, tuesday, wednesday, thursday, friday, saturday, sunday sql.NullBool
	var timeStart, timeEnd sql.NullString

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&result.Id, &result.PromotionWebTypeId, &promotionWebTypeTh,
		&result.PromotionWebStatusId, &promotionWebStatusTh,
		&result.ConditionDetail, &result.ImageUrl, &result.Name,
		&result.ShortDescription, &result.Description, &result.StartDate, &result.EndDate,
		&result.FreeBonusAmount, &result.PrivilegePerDay, &result.AbleWithdrawMorethan,
		&bonusConditionId, &bonusConditionTh, &bonusConditionSyntax,
		&result.BonusConditionAmount, &bonusTypeId, &bonusTypeTh,
		&result.BonusTypeAmount, &result.AbleWithdrawPertime, &turnoverTypeId,
		&turnoverTypeTh, &result.TurnoverAmount,
		&monday, &tuesday, &wednesday, &thursday,
		&friday, &saturday, &sunday,
		&timeStart, &timeEnd, &promotionWebDateTypeId,
		&promotionWebDateTypeTh, &result.BonusTypeAmountMax,
	)

	if err == nil {
		// Convert sql.NullString to string for all joined table fields
		if promotionWebTypeTh.Valid {
			result.PromotionWebTypeTh = promotionWebTypeTh.String
		}
		if promotionWebStatusTh.Valid {
			result.PromotionWebStatusTh = promotionWebStatusTh.String
		}
		if bonusConditionTh.Valid {
			result.PromotionWebBonusConditionTh = bonusConditionTh.String
		}
		if bonusConditionSyntax.Valid {
			result.PromotionWebBonusConditionSyntax = bonusConditionSyntax.String
		}
		if bonusTypeTh.Valid {
			result.PromotionWebBonusTypeTh = bonusTypeTh.String
		}
		if turnoverTypeTh.Valid {
			result.PromotionWebTurnoverTypeTh = turnoverTypeTh.String
		}
		if promotionWebDateTypeTh.Valid {
			result.PromotionWebDateTypeTh = promotionWebDateTypeTh.String
		}

		// Convert sql.NullInt64 to int64
		if bonusConditionId.Valid {
			result.PromotionWebBonusConditionId = bonusConditionId.Int64
		}
		if bonusTypeId.Valid {
			result.PromotionWebBonusTypeId = bonusTypeId.Int64
		}
		if turnoverTypeId.Valid {
			result.PromotionWebTurnoverTypeId = turnoverTypeId.Int64
		}
		if promotionWebDateTypeId.Valid {
			result.PromotionWebDateTypeId = promotionWebDateTypeId.Int64
		}

		// Convert sql.NullString to string for time fields
		if timeStart.Valid {
			result.TimeStart = timeStart.String
		}
		if timeEnd.Valid {
			result.TimeEnd = timeEnd.String
		}

		// Convert sql.NullBool to bool for weekday fields
		if monday.Valid {
			result.Monday = monday.Bool
		}
		if tuesday.Valid {
			result.Tuesday = tuesday.Bool
		}
		if wednesday.Valid {
			result.Wednesday = wednesday.Bool
		}
		if thursday.Valid {
			result.Thursday = thursday.Bool
		}
		if friday.Valid {
			result.Friday = friday.Bool
		}
		if saturday.Valid {
			result.Saturday = saturday.Bool
		}
		if sunday.Valid {
			result.Sunday = sunday.Bool
		}
	}

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("promotion web not found")
		}
		r.logger.WithError(err).Error("failed to get promotion web by ID")
		return nil, errors.NewDatabaseError("failed to get promotion web")
	}

	return &result, nil
}

func (r *PromotionWebRepository) UpdatePromotionWeb(ctx context.Context, req promotion_web.PromotionWebUpdateRequest) error {
	// Validate promotion_web_date_type_id if provided
	if req.PromotionWebDateTypeId != 0 &&
		req.PromotionWebDateTypeId != promotion_web.PROMOTION_WEB_DATE_TYPE_FIXED_DATE &&
		req.PromotionWebDateTypeId != promotion_web.PROMOTION_WEB_DATE_TYPE_NON_FIXED_DATE {
		r.logger.WithField("promotion_web_date_type_id", req.PromotionWebDateTypeId).Error("invalid promotion_web_date_type_id value")
		return fmt.Errorf("invalid promotion_web_date_type_id: must be 1 (FIXED_DATE) or 2 (NON_FIXED_DATE)")
	}

	// Build dynamic UPDATE query based on provided fields
	setParts := []string{}
	args := []interface{}{req.Id} // $1 is always the ID
	argIndex := 2

	// Only update fields that are provided (not nil)
	if req.PromotionWebTypeId != nil {
		setParts = append(setParts, fmt.Sprintf("promotion_web_type_id = $%d", argIndex))
		args = append(args, *req.PromotionWebTypeId)
		argIndex++
	}

	if req.PromotionWebStatusId != nil {
		setParts = append(setParts, fmt.Sprintf("promotion_web_status_id = $%d", argIndex))
		args = append(args, *req.PromotionWebStatusId)
		argIndex++
	}

	if req.Name != nil {
		setParts = append(setParts, fmt.Sprintf("name = $%d", argIndex))
		args = append(args, *req.Name)
		argIndex++
	}

	if req.ShortDescription != nil {
		setParts = append(setParts, fmt.Sprintf("short_description = $%d", argIndex))
		args = append(args, *req.ShortDescription)
		argIndex++
	}

	if req.Description != nil {
		setParts = append(setParts, fmt.Sprintf("description = $%d", argIndex))
		args = append(args, *req.Description)
		argIndex++
	}

	if req.ConditionDetail != nil {
		setParts = append(setParts, fmt.Sprintf("condition_detail = $%d", argIndex))
		args = append(args, *req.ConditionDetail)
		argIndex++
	}

	if req.ImageUrl != nil {
		setParts = append(setParts, fmt.Sprintf("image_url = $%d", argIndex))
		args = append(args, *req.ImageUrl)
		argIndex++
	}

	if req.StartDate != nil {
		setParts = append(setParts, fmt.Sprintf("start_date = $%d", argIndex))
		args = append(args, *req.StartDate)
		argIndex++
	}

	if req.EndDate != nil {
		setParts = append(setParts, fmt.Sprintf("end_date = $%d", argIndex))
		args = append(args, *req.EndDate)
		argIndex++
	}

	if req.FreeBonusAmount != nil {
		setParts = append(setParts, fmt.Sprintf("free_bonus_amount = $%d", argIndex))
		args = append(args, *req.FreeBonusAmount)
		argIndex++
	}

	if req.PrivilegePerDay != nil {
		setParts = append(setParts, fmt.Sprintf("privilege_per_day = $%d", argIndex))
		args = append(args, *req.PrivilegePerDay)
		argIndex++
	}

	if req.AbleWithdrawMorethan != nil {
		setParts = append(setParts, fmt.Sprintf("able_withdraw_morethan = $%d", argIndex))
		args = append(args, *req.AbleWithdrawMorethan)
		argIndex++
	}

	if req.PromotionWebBonusConditionId != nil {
		setParts = append(setParts, fmt.Sprintf("promotion_web_bonus_condition_id = $%d", argIndex))
		args = append(args, *req.PromotionWebBonusConditionId)
		argIndex++
	}

	if req.BonusConditionAmount != nil {
		setParts = append(setParts, fmt.Sprintf("bonus_condition_amount = $%d", argIndex))
		args = append(args, *req.BonusConditionAmount)
		argIndex++
	}

	if req.PromotionWebBonusTypeId != nil {
		setParts = append(setParts, fmt.Sprintf("promotion_web_bonus_type_id = $%d", argIndex))
		args = append(args, *req.PromotionWebBonusTypeId)
		argIndex++
	}

	if req.BonusTypeAmount != nil {
		setParts = append(setParts, fmt.Sprintf("bonus_type_amount = $%d", argIndex))
		args = append(args, *req.BonusTypeAmount)
		argIndex++
	}

	if req.AbleWithdrawPertime != nil {
		setParts = append(setParts, fmt.Sprintf("able_withdraw_pertime = $%d", argIndex))
		args = append(args, *req.AbleWithdrawPertime)
		argIndex++
	}

	if req.PromotionWebTurnoverTypeId != nil {
		setParts = append(setParts, fmt.Sprintf("promotion_web_turnover_type_id = $%d", argIndex))
		args = append(args, *req.PromotionWebTurnoverTypeId)
		argIndex++
	}

	if req.TurnoverAmount != nil {
		setParts = append(setParts, fmt.Sprintf("turnover_amount = $%d", argIndex))
		args = append(args, *req.TurnoverAmount)
		argIndex++
	}

	if req.Monday != nil {
		setParts = append(setParts, fmt.Sprintf("monday = $%d", argIndex))
		args = append(args, *req.Monday)
		argIndex++
	}

	if req.Tuesday != nil {
		setParts = append(setParts, fmt.Sprintf("tuesday = $%d", argIndex))
		args = append(args, *req.Tuesday)
		argIndex++
	}

	if req.Wednesday != nil {
		setParts = append(setParts, fmt.Sprintf("wednesday = $%d", argIndex))
		args = append(args, *req.Wednesday)
		argIndex++
	}

	if req.Thursday != nil {
		setParts = append(setParts, fmt.Sprintf("thursday = $%d", argIndex))
		args = append(args, *req.Thursday)
		argIndex++
	}

	if req.Friday != nil {
		setParts = append(setParts, fmt.Sprintf("friday = $%d", argIndex))
		args = append(args, *req.Friday)
		argIndex++
	}

	if req.Saturday != nil {
		setParts = append(setParts, fmt.Sprintf("saturday = $%d", argIndex))
		args = append(args, *req.Saturday)
		argIndex++
	}

	if req.Sunday != nil {
		setParts = append(setParts, fmt.Sprintf("sunday = $%d", argIndex))
		args = append(args, *req.Sunday)
		argIndex++
	}

	if req.TimeStart != nil {
		setParts = append(setParts, fmt.Sprintf("time_start = $%d", argIndex))
		args = append(args, *req.TimeStart)
		argIndex++
	}

	if req.TimeEnd != nil {
		setParts = append(setParts, fmt.Sprintf("time_end = $%d", argIndex))
		args = append(args, *req.TimeEnd)
		argIndex++
	}

	// Update promotion_web_date_type_id only if provided and valid
	if req.PromotionWebDateTypeId != 0 {
		setParts = append(setParts, fmt.Sprintf("promotion_web_date_type_id = $%d", argIndex))
		args = append(args, req.PromotionWebDateTypeId)
		argIndex++
	}

	if req.BonusTypeAmountMax != nil {
		setParts = append(setParts, fmt.Sprintf("bonus_type_amount_max = $%d", argIndex))
		args = append(args, *req.BonusTypeAmountMax)
		argIndex++
	}

	if req.HiddenUrlLink != nil {
		setParts = append(setParts, fmt.Sprintf("hidden_url_link = $%d", argIndex))
		args = append(args, *req.HiddenUrlLink)
		argIndex++
	}

	// Always update these system fields
	setParts = append(setParts, fmt.Sprintf("updated_by_user_id = $%d", argIndex))
	args = append(args, req.UpdatedByAdminId)
	argIndex++

	now := time.Now().UTC()
	setParts = append(setParts, fmt.Sprintf("updated_at = $%d", argIndex))
	args = append(args, now)

	// Check if there are any fields to update
	if len(setParts) <= 2 { // Only system fields (updated_by_user_id, updated_at)
		return errors.NewValidationError("no fields provided for update")
	}

	query := fmt.Sprintf(`
		UPDATE promotion_web SET
			%s
		WHERE id = $1 AND deleted_at IS NULL
	`, strings.Join(setParts, ", "))

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to update promotion web")
		return errors.NewDatabaseError("failed to update promotion web")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("promotion web not found")
	}

	return nil
}

func (r *PromotionWebRepository) GetPromotionWebUserToCancel(ctx context.Context, PromotionWebId int64) ([]promotion_web.GetPromotionWebIdToCancel, error) {
	query := `
		SELECT
			promotion_web_user.id AS id,
			promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id
		FROM promotion_web_user
		WHERE promotion_web_id = $1
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, PromotionWebId)
	if err != nil {
		r.logger.WithError(err).Error("failed to get promotion web users to cancel")
		return nil, errors.NewDatabaseError("failed to get promotion web users to cancel")
	}
	defer rows.Close()

	var list []promotion_web.GetPromotionWebIdToCancel
	for rows.Next() {
		var item promotion_web.GetPromotionWebIdToCancel
		err := rows.Scan(&item.Id, &item.PromotionWebUserStatusId)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan promotion web user to cancel row")
			return nil, errors.NewDatabaseError("failed to scan promotion web user to cancel row")
		}
		list = append(list, item)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating promotion web users to cancel rows")
		return nil, errors.NewDatabaseError("error iterating promotion web users to cancel rows")
	}

	return list, nil
}

func (r *PromotionWebRepository) CancelPromotionWeb(ctx context.Context, req promotion_web.CancelPromotionWebRequest) error {
	query := `
		UPDATE promotion_web SET
			promotion_web_status_id = $2,
			canceled_by_user_id = $3,
			updated_at = $4
		WHERE id = $1 AND deleted_at IS NULL
	`

	now := time.Now().UTC()
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		req.Id, req.PromotionWebStatusId, req.CanceledByAdminId, now,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to cancel promotion web")
		return errors.NewDatabaseError("failed to cancel promotion web")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("promotion web not found")
	}

	return nil
}

func (r *PromotionWebRepository) DeletePromotionWeb(ctx context.Context, req promotion_web.DeletePromotionWebRequest) error {
	query := `
		UPDATE promotion_web SET
			deleted_by_user_id = $2,
			deleted_at = $3
		WHERE id = $1 AND deleted_at IS NULL
	`

	now := time.Now().UTC()
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		req.Id, req.DeletedByAdminId, now,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to delete promotion web")
		return errors.NewDatabaseError("failed to delete promotion web")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("promotion web not found")
	}

	return nil
}

func (r *PromotionWebRepository) GetExpiredPromotionWeb(ctx context.Context, StartCleanUpDate string) ([]promotion_web.PromotionWebExpired, error) {
	query := `
		SELECT promotion_web.id AS id
		FROM promotion_web
		WHERE promotion_web.end_date < $1
		AND promotion_web.promotion_web_date_type_id = $2
		AND promotion_web.deleted_at IS NULL
	`

	// Use the constant from promotion_web domain
	const PROMOTION_WEB_DATE_TYPE_FIXED_DATE = 1

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, StartCleanUpDate, PROMOTION_WEB_DATE_TYPE_FIXED_DATE)
	if err != nil {
		r.logger.WithError(err).Error("failed to get expired promotion web")
		return nil, errors.NewDatabaseError("failed to get expired promotion web")
	}
	defer rows.Close()

	var list []promotion_web.PromotionWebExpired
	for rows.Next() {
		var item promotion_web.PromotionWebExpired
		err := rows.Scan(&item.Id)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan expired promotion web row")
			return nil, errors.NewDatabaseError("failed to scan expired promotion web row")
		}
		list = append(list, item)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating expired promotion web rows")
		return nil, errors.NewDatabaseError("error iterating expired promotion web rows")
	}

	return list, nil
}

func (r *PromotionWebRepository) PromotionConfirmUpdatePromotionWebUser(ctx context.Context, confirmId int64, promotionWebUserId int64) error {
	query := `
		UPDATE promotion_web_user_confirm
		SET promotion_web_user_id = $2
		WHERE id = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, confirmId, promotionWebUserId)
	if err != nil {
		r.logger.WithError(err).Error("failed to update promotion web user confirm")
		return errors.NewDatabaseError("failed to update promotion web user confirm")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("promotion web user confirm not found")
	}

	return nil
}

func (r *PromotionWebRepository) CancelPromotionWebUserById(ctx context.Context, req promotion_web.CancelPromotionWebUserById) error {
	query := `
		UPDATE promotion_web_user SET
			promotion_web_user_status_id = $2,
			canceled_by_user_id = $3,
			canceled_at = $4
		WHERE id = $1
		AND promotion_web_user_status_id = $5
	`

	// Use constants from promotion_web domain
	const PROMOTION_WEB_USER_STATUS_ON_PROCESS = 1
	statusCanceled := promotion_web.PROMOTION_WEB_USER_STATUS_CANCELED
	now := time.Now().UTC()

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		req.Id, statusCanceled, req.CanceledByAdminId, now, PROMOTION_WEB_USER_STATUS_ON_PROCESS,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to cancel promotion web user by id")
		return errors.NewDatabaseError("failed to cancel promotion web user by id")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("promotion web user not found or not in process status")
	}

	return nil
}

func (r *PromotionWebRepository) ExpiredPromotionWebUserByIds(ctx context.Context, req promotion_web.CancelPromotionWebUserByPromotionWebId) error {
	query := `
		UPDATE promotion_web_user SET
			promotion_web_user_status_id = $2,
			canceled_by_user_id = $3,
			canceled_at = $4
		WHERE promotion_web_id = $1
		AND promotion_web_user_status_id = $5
	`

	// Use constants from promotion_web domain
	const PROMOTION_WEB_USER_STATUS_ON_PROCESS = 1
	statusCanceled := promotion_web.PROMOTION_WEB_USER_STATUS_CANCELED

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		req.PromotionWebId, statusCanceled, req.CanceledByAdminId, req.CanceledAt, PROMOTION_WEB_USER_STATUS_ON_PROCESS,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to expire promotion web users by promotion web id")
		return errors.NewDatabaseError("failed to expire promotion web users by promotion web id")
	}

	return nil
}

func (r *PromotionWebRepository) GetUserPromotionWebByUserId(ctx context.Context, userId int64) (*promotion_web.PromotionWebUserByUserIdResponse, error) {
	query := `
		SELECT
			promotion_web_user.id AS id,
			promotion_web_user.promotion_web_id AS promotion_web_id,
			promotion_web.name AS promotion_name,
			promotion_web_user.user_id AS user_id,
			user.member_code AS member_code,
			user.fullname AS full_name,
			user.phone AS phone,
			promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id,
			promotion_web_user_status.label_th AS promotion_web_user_status_th,
			promotion_web_user.total_amount AS total_amount,
			promotion_web_user.created_at AS created_at
		FROM promotion_web_user
		LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id
		LEFT JOIN user ON promotion_web_user.user_id = user.id
		LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id
		WHERE promotion_web_user.user_id = $1
		AND promotion_web_user.deleted_at IS NULL
		LIMIT 1
	`

	var result promotion_web.PromotionWebUserByUserIdResponse
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, userId).Scan(
		&result.Id, &result.PromotionWebId, &result.PromotionName,
		&result.UserId, &result.MemberCode, &result.FullName, &result.Phone,
		&result.PromotionWebUserStatusId, &result.PromotionWebUserStatusTh,
		&result.TotalAmount, &result.CreatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("promotion web user not found")
		}
		r.logger.WithError(err).Error("failed to get user promotion web by user ID")
		return nil, errors.NewDatabaseError("failed to get user promotion web")
	}

	return &result, nil
}

// UploadImageToCloudflare uploads an image to Cloudflare Images
func (r *PromotionWebRepository) UploadImageToCloudflare(ctx context.Context, pathUpload string, filename string, fileReader io.Reader) (*promotion_web.CloudFlareUploadCreateBody, error) {
	// Generate random filename for unique identification
	randomFilename := r.generateRandomString()

	// Set imageCloudFlarePathName
	imageCloudFlarePathName := fmt.Sprintf("%s%s", pathUpload, randomFilename)

	// Create multipart form body
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// Set custom ID for the image
	err := writer.WriteField("id", imageCloudFlarePathName)
	if err != nil {
		r.logger.WithError(err).Error("failed to write id field to multipart form")
		return nil, errors.NewInternalError("failed to prepare upload form")
	}

	// Create form file part
	part, err := writer.CreateFormFile("file", filename)
	if err != nil {
		r.logger.WithError(err).Error("failed to create form file part")
		return nil, errors.NewInternalError("failed to prepare file upload")
	}

	// Copy file content to form
	_, err = io.Copy(part, fileReader)
	if err != nil {
		r.logger.WithError(err).Error("failed to copy file content")
		return nil, errors.NewInternalError("failed to copy file content")
	}

	err = writer.Close()
	if err != nil {
		r.logger.WithError(err).Error("failed to close multipart writer")
		return nil, errors.NewInternalError("failed to finalize upload form")
	}

	// Get Cloudflare configuration from environment
	accountId := os.Getenv("CLOUDFLARE_ACCOUNT_ID")
	token := os.Getenv("CLOUDFLARE_API_TOKEN")
	baseURL := os.Getenv("CLOUDFLARE_UPLOAD_URL")

	if accountId == "" || token == "" || baseURL == "" {
		r.logger.Error("missing Cloudflare configuration environment variables")
		return nil, errors.NewInternalError("Cloudflare configuration not found")
	}

	// Build Cloudflare API URL
	url := fmt.Sprintf("%s/accounts/%s/images/v1", baseURL, accountId)

	// Create HTTP request with context
	request, err := http.NewRequestWithContext(ctx, "POST", url, body)
	if err != nil {
		r.logger.WithError(err).Error("failed to create HTTP request")
		return nil, errors.NewInternalError("failed to create upload request")
	}

	// Set headers
	request.Header.Set("Authorization", "Bearer "+token)
	request.Header.Set("Content-Type", writer.FormDataContentType())

	// Execute request
	client := &http.Client{}
	resp, err := client.Do(request)
	if err != nil {
		r.logger.WithError(err).Error("failed to execute Cloudflare upload request")
		return nil, errors.NewInternalError("failed to upload to Cloudflare")
	}
	defer resp.Body.Close()

	// Read response
	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		r.logger.WithError(err).Error("failed to read Cloudflare response")
		return nil, errors.NewInternalError("failed to read upload response")
	}

	// Check response status
	if resp.StatusCode != 200 {
		r.logger.WithField("status_code", resp.StatusCode).WithField("response", string(responseData)).Error("Cloudflare upload failed")
		return nil, errors.NewInternalError(fmt.Sprintf("Cloudflare upload failed with status %d", resp.StatusCode))
	}

	// Parse response
	var response promotion_web.CloudFlareUploadResponse
	if err := json.Unmarshal(responseData, &response); err != nil {
		r.logger.WithError(err).Error("failed to unmarshal Cloudflare response")
		return nil, errors.NewInternalError("failed to parse upload response")
	}

	// Validate response structure
	if len(response.Result.Variants) == 0 {
		r.logger.Error("Cloudflare response missing variants")
		return nil, errors.NewInternalError("invalid Cloudflare response format")
	}

	// Build result
	saveData := promotion_web.CloudFlareUploadCreateBody{
		ImageId:           response.Result.Id,
		Filename:          response.Result.Filename,
		Uploaded:          response.Result.Uploaded,
		RequireSignedURLs: response.Result.RequireSignedURLs,
		FileUrl:           response.Result.Variants[0],
	}

	r.logger.WithField("image_id", saveData.ImageId).WithField("filename", saveData.Filename).Info("successfully uploaded image to Cloudflare")
	return &saveData, nil
}

func (r *PromotionWebRepository) GetUserPromotionWebList(ctx context.Context, req promotion_web.PromotionWebUserGetListRequest) ([]promotion_web.PromotionWebUserGetListResponse, int64, error) {
	var list []promotion_web.PromotionWebUserGetListResponse
	var total int64

	// Build WHERE conditions
	var whereConditions []string
	var args []interface{}
	argIndex := 1

	// Base WHERE condition
	whereConditions = append(whereConditions, "promotion_web_user.deleted_at IS NULL")

	// Promotion web filter
	if req.PromotionWebId != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.promotion_web_id = $%d", argIndex))
		args = append(args, *req.PromotionWebId)
		argIndex++
	}

	// Status filter
	if req.PromotionWebUserStatusId != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.promotion_web_user_status_id = $%d", argIndex))
		args = append(args, *req.PromotionWebUserStatusId)
		argIndex++
	}

	// Date filters
	if req.StartDate != "" {
		startDateAtBkk, err := r.datetime.ParseBodBkk(req.StartDate)
		if err != nil {
			return nil, 0, errors.NewValidationError("invalid start date format")
		}
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.created_at >= $%d", argIndex))
		args = append(args, *startDateAtBkk)
		argIndex++
	}

	if req.EndDate != "" {
		endDateAtBkk, err := r.datetime.ParseEodBkk(req.EndDate)
		if err != nil {
			return nil, 0, errors.NewValidationError("invalid end date format")
		}
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.created_at <= $%d", argIndex))
		args = append(args, *endDateAtBkk)
		argIndex++
	}

	// Search filter
	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		whereConditions = append(whereConditions, fmt.Sprintf("(promotion_web.name ILIKE $%d OR user.member_code ILIKE $%d OR user.fullname ILIKE $%d OR user.phone ILIKE $%d)", argIndex, argIndex+1, argIndex+2, argIndex+3))
		args = append(args, searchPattern, searchPattern, searchPattern, searchPattern)
		argIndex += 4
	}

	whereClause := strings.Join(whereConditions, " AND ")

	// Get total count
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM promotion_web_user
		LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id
		LEFT JOIN user ON promotion_web_user.user_id = user.id
		LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id
		WHERE %s
	`, whereClause)

	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.WithError(err).Error("failed to count promotion web user list")
		return nil, 0, errors.NewDatabaseError("failed to count promotion web user list")
	}

	if total > 0 {
		// Build the main query with pagination
		selectedFields := `
			promotion_web_user.id AS id,
			promotion_web_user.promotion_web_id AS promotion_web_id,
			promotion_web.name AS promotion_name,
			promotion_web_user.user_id AS user_id,
			user.member_code AS member_code,
			user.fullname AS full_name,
			user.phone AS phone,
			promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id,
			promotion_web_user_status.label_th AS promotion_web_user_status_th,
			promotion_web_user.total_amount AS total_amount,
			promotion_web_user.created_at AS created_at,
			NULL AS canceled_by_admin_id,
			NULL AS canceled_at,
			NULL AS canceled_by_admin_name,
			promotion_web_lock_credit.is_locked AS is_locked,
			NULL AS approve_credit_by_admin_id,
			NULL AS approve_credit_by_admin_name,
			NULL AS approve_credit_at,
			promotion_web.able_withdraw_morethan AS able_withdraw_morethan,
			promotion_web.able_withdraw_pertime AS able_withdraw_pertime
		`

		// Add pagination parameters
		offset := (req.Page - 1) * req.Limit
		paginationArgs := append(args, req.Limit, offset)

		dataQuery := fmt.Sprintf(`
			SELECT %s
			FROM promotion_web_user
			LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id
			LEFT JOIN user ON promotion_web_user.user_id = user.id
			LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id
			LEFT JOIN promotion_web_lock_credit ON promotion_web_user.user_id = promotion_web_lock_credit.user_id AND promotion_web_user.promotion_web_id = promotion_web_lock_credit.promotion_id
			WHERE %s
			ORDER BY promotion_web_user.created_at DESC
			LIMIT $%d OFFSET $%d
		`, selectedFields, whereClause, len(args)+1, len(args)+2)

		rows, err := dbutil.QueryWithSchema(ctx, r.pool, dataQuery, paginationArgs...)
		if err != nil {
			r.logger.WithError(err).Error("failed to get promotion web user list")
			return nil, 0, errors.NewDatabaseError("failed to get promotion web user list")
		}
		defer rows.Close()

		for rows.Next() {
			var item promotion_web.PromotionWebUserGetListResponse
			err := rows.Scan(
				&item.Id, &item.PromotionWebId, &item.PromotionName,
				&item.UserId, &item.MemberCode, &item.FullName, &item.Phone,
				&item.PromotionWebUserStatusId, &item.PromotionWebUserStatusTh,
				&item.TotalAmount, &item.CreatedAt,
				&item.CanceledByAdminId, &item.CanceledAt, &item.CanceledByAdminName,
				&item.IsLocked, &item.ApproveCreditByAdminId, &item.ApproveCreditByAdminName,
				&item.ApproveCreditAt, &item.AbleWithdrawMorethan, &item.AbleWithdrawPertime,
			)
			if err != nil {
				r.logger.WithError(err).Error("failed to scan promotion web user row")
				return nil, 0, errors.NewDatabaseError("failed to scan promotion web user row")
			}
			list = append(list, item)
		}

		if err = rows.Err(); err != nil {
			r.logger.WithError(err).Error("error iterating promotion web user rows")
			return nil, 0, errors.NewDatabaseError("error iterating promotion web user rows")
		}
	}

	return list, total, nil
}

func (r *PromotionWebRepository) GetPromotionWebUserById(ctx context.Context, req promotion_web.GetPromotionWebUserById) (*promotion_web.GetPromotionWebUserByIdResponse, error) {
	query := `
		SELECT
			promotion_web_user.id AS id,
			promotion_web_user.promotion_web_id AS promotion_web_id,
			promotion_web.name AS promotion_name,
			promotion_web_user.user_id AS user_id,
			user.member_code AS member_code,
			user.fullname AS full_name,
			user.phone AS phone,
			promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id,
			promotion_web_user_status.label_th AS promotion_web_user_status_th,
			promotion_web_user.total_amount AS total_amount,
			promotion_web_user.created_at AS created_at
		FROM promotion_web_user
		LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id
		LEFT JOIN user ON promotion_web_user.user_id = user.id
		LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id
		WHERE promotion_web_user.id = $1
		AND promotion_web_user.deleted_at IS NULL
	`

	var result promotion_web.GetPromotionWebUserByIdResponse
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, req.Id).Scan(
		&result.Id, &result.PromotionWebId, &result.PromotionName,
		&result.UserId, &result.MemberCode, &result.FullName, &result.Phone,
		&result.PromotionWebUserStatusId, &result.PromotionWebUserStatusTh,
		&result.TotalAmount, &result.CreatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("promotion web user not found")
		}
		r.logger.WithError(err).Error("failed to get promotion web user by ID")
		return nil, errors.NewDatabaseError("failed to get promotion web user")
	}

	return &result, nil
}

func (r *PromotionWebRepository) PromotionWebUserGetListByUserId(ctx context.Context, req promotion_web.PromotionWebUserGetListByUserIdRequest) ([]promotion_web.PromotionWebUserGetListByUserIdResponse, int64, error) {
	var list []promotion_web.PromotionWebUserGetListByUserIdResponse
	var total int64

	// Build WHERE conditions
	var whereConditions []string
	var args []interface{}
	argIndex := 1

	// Base WHERE conditions
	whereConditions = append(whereConditions, "promotion_web_user.deleted_at IS NULL")
	whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.user_id = $%d", argIndex))
	args = append(args, req.UserId)
	argIndex++

	// Status filter
	if req.PromotionWebUserStatusId != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.promotion_web_user_status_id = $%d", argIndex))
		args = append(args, *req.PromotionWebUserStatusId)
		argIndex++
	}

	// Date filtering logic
	if req.OfDate != "" {
		// OfDate is Primary - use specific date
		startDateAtBkk, err := r.datetime.ParseBodBkk(req.OfDate)
		if err != nil {
			return nil, 0, errors.NewValidationError("invalid OfDate format")
		}
		endDateAtBkk, err := r.datetime.ParseEodBkk(req.OfDate)
		if err != nil {
			return nil, 0, errors.NewValidationError("invalid OfDate format")
		}
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.created_at >= $%d", argIndex))
		args = append(args, *startDateAtBkk)
		argIndex++
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.created_at <= $%d", argIndex))
		args = append(args, *endDateAtBkk)
		argIndex++
	} else {
		// Handle DateType logic
		action := time.Now().UTC()
		bkkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
		filterTime := action.In(bkkLoc)

		var fromDate, toDate string

		switch req.DateType {
		case "today":
			fromDate = filterTime.Format("2006-01-02")
			toDate = filterTime.Format("2006-01-02")
		case "yesterday":
			fromDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
			toDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
		case "this_month":
			fromDate = filterTime.Format("2006-01") + "-01"
			toDate = filterTime.AddDate(0, 1, -1).Format("2006-01-02")
		default:
			// Use FromDate and ToDate from request
			fromDate = req.FromDate
			toDate = req.ToDate
		}

		// Apply date filters
		if fromDate != "" {
			startDateAtBkk, err := r.datetime.ParseBodBkk(fromDate)
			if err != nil {
				return nil, 0, errors.NewValidationError("invalid FromDate format")
			}
			whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.created_at >= $%d", argIndex))
			args = append(args, *startDateAtBkk)
			argIndex++
		}

		if toDate != "" {
			endDateAtBkk, err := r.datetime.ParseEodBkk(toDate)
			if err != nil {
				return nil, 0, errors.NewValidationError("invalid ToDate format")
			}
			whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.created_at <= $%d", argIndex))
			args = append(args, *endDateAtBkk)
			argIndex++
		}
	}

	// Search filter
	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		whereConditions = append(whereConditions, fmt.Sprintf("(promotion_web.name ILIKE $%d OR user.member_code ILIKE $%d OR user.fullname ILIKE $%d OR user.phone ILIKE $%d)", argIndex, argIndex+1, argIndex+2, argIndex+3))
		args = append(args, searchPattern, searchPattern, searchPattern, searchPattern)
		argIndex += 4
	}

	whereClause := strings.Join(whereConditions, " AND ")

	// Get total count
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM promotion_web_user
		LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id
		LEFT JOIN user ON promotion_web_user.user_id = user.id
		LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id
		WHERE %s
	`, whereClause)

	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.WithError(err).Error("failed to count promotion web user list by user id")
		return nil, 0, errors.NewDatabaseError("failed to count promotion web user list by user id")
	}

	if total > 0 {
		// Build the main query with pagination and sorting
		selectedFields := `
			promotion_web_user.id AS id,
			promotion_web_user.promotion_web_id AS promotion_web_id,
			promotion_web.name AS promotion_name,
			promotion_web_user.user_id AS user_id,
			user.member_code AS member_code,
			user.fullname AS full_name,
			user.phone AS phone,
			promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id,
			promotion_web_user_status.label_th AS promotion_web_user_status_th,
			promotion_web_user.total_amount AS total_amount,
			promotion_web_user.created_at AS created_at,
			NULL AS canceled_by_admin_id,
			NULL AS canceled_by_admin_name,
			NULL AS canceled_at
		`

		// Build ORDER BY clause
		orderBy := "ORDER BY promotion_web_user.created_at DESC" // default
		if req.SortCol != "" {
			sortCol := strings.TrimSpace(req.SortCol)
			sortAsc := "ASC"
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				sortAsc = "DESC"
			}
			// Validate sort column to prevent SQL injection
			validSortCols := map[string]bool{
				"promotion_web_user.id":           true,
				"promotion_web_user.created_at":   true,
				"promotion_web.name":              true,
				"user.member_code":                true,
				"user.fullname":                   true,
				"promotion_web_user.total_amount": true,
			}
			if validSortCols[sortCol] {
				orderBy = fmt.Sprintf("ORDER BY %s %s", sortCol, sortAsc)
			}
		}

		// Add pagination parameters
		offset := (req.Page - 1) * req.Limit
		paginationArgs := append(args, req.Limit, offset)

		dataQuery := fmt.Sprintf(`
			SELECT %s
			FROM promotion_web_user
			LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id
			LEFT JOIN user ON promotion_web_user.user_id = user.id
			LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id
			WHERE %s
			%s
			LIMIT $%d OFFSET $%d
		`, selectedFields, whereClause, orderBy, len(args)+1, len(args)+2)

		rows, err := dbutil.QueryWithSchema(ctx, r.pool, dataQuery, paginationArgs...)
		if err != nil {
			r.logger.WithError(err).Error("failed to get promotion web user list by user id")
			return nil, 0, errors.NewDatabaseError("failed to get promotion web user list by user id")
		}
		defer rows.Close()

		for rows.Next() {
			var item promotion_web.PromotionWebUserGetListByUserIdResponse
			err := rows.Scan(
				&item.Id, &item.PromotionWebId, &item.PromotionName,
				&item.UserId, &item.MemberCode, &item.FullName, &item.Phone,
				&item.PromotionWebUserStatusId, &item.PromotionWebUserStatusTh,
				&item.TotalAmount, &item.CreatedAt,
				&item.CanceledByAdminId, &item.CanceledByAdminName, &item.CanceledAt,
			)
			if err != nil {
				r.logger.WithError(err).Error("failed to scan promotion web user by user id row")
				return nil, 0, errors.NewDatabaseError("failed to scan promotion web user by user id row")
			}
			list = append(list, item)
		}

		if err = rows.Err(); err != nil {
			r.logger.WithError(err).Error("error iterating promotion web user by user id rows")
			return nil, 0, errors.NewDatabaseError("error iterating promotion web user by user id rows")
		}
	}

	return list, total, nil
}

func (r *PromotionWebRepository) PromotionWebGetSildeListOnlyActive(ctx context.Context) ([]promotion_web.PromotionWebGetSildeListOnlyActive, error) {
	query := `
		SELECT
			promotion_web.id AS id,
			promotion_web.promotion_web_type_id AS promotion_web_type_id,
			promotion_web_type.label_th AS promotion_web_type_th,
			promotion_web.promotion_web_status_id AS promotion_web_status_id,
			promotion_web_status.label_th AS promotion_web_status_th,
			promotion_web.name AS name,
			promotion_web.promotion_web_date_type_id AS promotion_web_date_type_id,
			promotion_web.start_date AS start_date,
			promotion_web.end_date AS end_date,
			promotion_web.time_start AS time_start,
			promotion_web.time_end AS time_end
		FROM promotion_web
		LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id
		LEFT JOIN promotion_web_status ON promotion_web.promotion_web_status_id = promotion_web_status.id
		LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id
		WHERE promotion_web.promotion_web_status_id IN ($1, $2, $3)
		AND promotion_web.deleted_at IS NULL
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query,
		promotion_web.PROMOTION_WEB_STATUS_ACTIVE,
		promotion_web.PROMOTION_WEB_STATUS_DISABLE_WEB,
		promotion_web.PROMOTION_WEB_STATUS_ONLY_URL,
	)
	if err != nil {
		r.logger.WithError(err).Error("failed to get active promotion web slide list")
		return nil, errors.NewDatabaseError("failed to get active promotion web slide list")
	}
	defer rows.Close()

	var list []promotion_web.PromotionWebGetSildeListOnlyActive
	for rows.Next() {
		var item promotion_web.PromotionWebGetSildeListOnlyActive
		err := rows.Scan(
			&item.Id, &item.PromotionWebTypeId, &item.PromotionWebTypeTh,
			&item.PromotionWebStatusId, &item.PromotionWebStatusTh,
			&item.Name, &item.PromotionWebDateTypeId,
			&item.StartDate, &item.EndDate, &item.TimeStart, &item.TimeEnd,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan promotion web slide row")
			return nil, errors.NewDatabaseError("failed to scan promotion web slide row")
		}
		list = append(list, item)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating promotion web slide rows")
		return nil, errors.NewDatabaseError("error iterating promotion web slide rows")
	}

	return list, nil
}

func (r *PromotionWebRepository) UpdatePromotionWebPriorityOrderCreate(ctx context.Context, id int64) error {
	query := `
		UPDATE promotion_web
		SET priority_order = $2
		WHERE id = $1 AND deleted_at IS NULL
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to update promotion web priority order")
		return errors.NewDatabaseError("failed to update promotion web priority order")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("promotion web not found")
	}

	return nil
}

func (r *PromotionWebRepository) SortPromotionWebPriorityOrder(ctx context.Context, req promotion_web.DragSortRequest) error {
	// Get the priority orders for both items
	query := `
		SELECT id, priority_order
		FROM promotion_web
		WHERE id IN ($1, $2) AND deleted_at IS NULL
		LIMIT 2
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, req.FromItemID, req.ToItemID)
	if err != nil {
		r.logger.WithError(err).Error("failed to get promotion web items for sorting")
		return errors.NewDatabaseError("failed to get promotion web items for sorting")
	}
	defer rows.Close()

	var results []promotion_web.PrioritySortResponse
	for rows.Next() {
		var result promotion_web.PrioritySortResponse
		err := rows.Scan(&result.Id, &result.PriorityOrder)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan priority sort row")
			return errors.NewDatabaseError("failed to scan priority sort row")
		}
		results = append(results, result)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating priority sort rows")
		return errors.NewDatabaseError("error iterating priority sort rows")
	}

	if len(results) != 2 {
		return errors.NewValidationError("both items must exist for sorting")
	}

	var fromItem, toItem *promotion_web.PrioritySortResponse
	for _, result := range results {
		if result.Id == req.FromItemID {
			fromItem = &result
		} else if result.Id == req.ToItemID {
			toItem = &result
		}
	}

	if fromItem == nil || toItem == nil {
		return errors.NewValidationError("invalid item IDs for sorting")
	}

	// Perform the sorting operations
	if fromItem.PriorityOrder < toItem.PriorityOrder {
		// Drag down - shift items up
		shiftQuery := `
			UPDATE promotion_web
			SET priority_order = priority_order - 1
			WHERE priority_order > $1 AND priority_order <= $2 AND deleted_at IS NULL
		`
		_, err = dbutil.ExecWithSchema(ctx, r.pool, shiftQuery, fromItem.PriorityOrder, toItem.PriorityOrder)
		if err != nil {
			r.logger.WithError(err).Error("failed to shift items down")
			return errors.NewDatabaseError("failed to shift items down")
		}

		// Move the from item to the to position
		moveQuery := `UPDATE promotion_web SET priority_order = $2 WHERE id = $1`
		_, err = dbutil.ExecWithSchema(ctx, r.pool, moveQuery, fromItem.Id, toItem.PriorityOrder)
		if err != nil {
			r.logger.WithError(err).Error("failed to move item to new position")
			return errors.NewDatabaseError("failed to move item to new position")
		}
	} else if fromItem.PriorityOrder > toItem.PriorityOrder {
		// Drag up - shift items down
		shiftQuery := `
			UPDATE promotion_web
			SET priority_order = priority_order + 1
			WHERE priority_order < $1 AND priority_order >= $2 AND deleted_at IS NULL
		`
		_, err = dbutil.ExecWithSchema(ctx, r.pool, shiftQuery, fromItem.PriorityOrder, toItem.PriorityOrder)
		if err != nil {
			r.logger.WithError(err).Error("failed to shift items up")
			return errors.NewDatabaseError("failed to shift items up")
		}

		// Move the from item to the to position
		moveQuery := `UPDATE promotion_web SET priority_order = $2 WHERE id = $1`
		_, err = dbutil.ExecWithSchema(ctx, r.pool, moveQuery, fromItem.Id, toItem.PriorityOrder)
		if err != nil {
			r.logger.WithError(err).Error("failed to move item to new position")
			return errors.NewDatabaseError("failed to move item to new position")
		}
	}

	return nil
}

// Option/lookup operations
func (r *PromotionWebRepository) GetPromotionTypes(ctx context.Context) ([]promotion_web.PromotionWebTypeResponse, error) {
	query := `SELECT id, name, label_th, label_en FROM promotion_web_type ORDER BY id`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to get promotion types")
		return nil, errors.NewDatabaseError("failed to get promotion types")
	}
	defer rows.Close()

	var types []promotion_web.PromotionWebTypeResponse
	for rows.Next() {
		var t promotion_web.PromotionWebTypeResponse
		err := rows.Scan(&t.ID, &t.Name, &t.LabelTh, &t.LabelEn)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan promotion type")
			return nil, errors.NewDatabaseError("failed to scan promotion type")
		}
		types = append(types, t)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating promotion types")
		return nil, errors.NewDatabaseError("error iterating promotion types")
	}

	return types, nil
}

func (r *PromotionWebRepository) GetPromotionStatuses(ctx context.Context) ([]promotion_web.PromotionWebStatusResponse, error) {
	query := `SELECT id, name, label_th, label_en FROM promotion_web_status ORDER BY id`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to get promotion statuses")
		return nil, errors.NewDatabaseError("failed to get promotion statuses")
	}
	defer rows.Close()

	var statuses []promotion_web.PromotionWebStatusResponse
	for rows.Next() {
		var s promotion_web.PromotionWebStatusResponse
		err := rows.Scan(&s.ID, &s.Name, &s.LabelTh, &s.LabelEn)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan promotion status")
			return nil, errors.NewDatabaseError("failed to scan promotion status")
		}
		statuses = append(statuses, s)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating promotion statuses")
		return nil, errors.NewDatabaseError("error iterating promotion statuses")
	}

	return statuses, nil
}

func (r *PromotionWebRepository) GetPromotionBonusConditions(ctx context.Context) ([]promotion_web.PromotionWebBonusConditionResponse, error) {
	query := `SELECT id, syntax, name, label_th, label_en FROM promotion_web_bonus_condition ORDER BY id`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to get promotion bonus conditions")
		return nil, errors.NewDatabaseError("failed to get promotion bonus conditions")
	}
	defer rows.Close()

	var conditions []promotion_web.PromotionWebBonusConditionResponse
	for rows.Next() {
		var c promotion_web.PromotionWebBonusConditionResponse
		err := rows.Scan(&c.ID, &c.Syntax, &c.Name, &c.LabelTh, &c.LabelEn)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan promotion bonus condition")
			return nil, errors.NewDatabaseError("failed to scan promotion bonus condition")
		}
		conditions = append(conditions, c)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating promotion bonus conditions")
		return nil, errors.NewDatabaseError("error iterating promotion bonus conditions")
	}

	return conditions, nil
}

func (r *PromotionWebRepository) GetPromotionBonusTypes(ctx context.Context) ([]promotion_web.PromotionWebBonusTypeResponse, error) {
	query := `SELECT id, name, label_th, label_en FROM promotion_web_bonus_type ORDER BY id`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to get promotion bonus types")
		return nil, errors.NewDatabaseError("failed to get promotion bonus types")
	}
	defer rows.Close()

	var types []promotion_web.PromotionWebBonusTypeResponse
	for rows.Next() {
		var t promotion_web.PromotionWebBonusTypeResponse
		err := rows.Scan(&t.ID, &t.Name, &t.LabelTh, &t.LabelEn)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan promotion bonus type")
			return nil, errors.NewDatabaseError("failed to scan promotion bonus type")
		}
		types = append(types, t)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating promotion bonus types")
		return nil, errors.NewDatabaseError("error iterating promotion bonus types")
	}

	return types, nil
}

func (r *PromotionWebRepository) GetPromotionTurnoverTypes(ctx context.Context) ([]promotion_web.PromotionWebTurnoverTypeResponse, error) {
	query := `SELECT id, name, label_th, label_en FROM promotion_web_turnover_type ORDER BY id`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to get promotion turnover types")
		return nil, errors.NewDatabaseError("failed to get promotion turnover types")
	}
	defer rows.Close()

	var types []promotion_web.PromotionWebTurnoverTypeResponse
	for rows.Next() {
		var t promotion_web.PromotionWebTurnoverTypeResponse
		err := rows.Scan(&t.ID, &t.Name, &t.LabelTh, &t.LabelEn)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan promotion turnover type")
			return nil, errors.NewDatabaseError("failed to scan promotion turnover type")
		}
		types = append(types, t)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating promotion turnover types")
		return nil, errors.NewDatabaseError("error iterating promotion turnover types")
	}

	return types, nil
}

func (r *PromotionWebRepository) GetPromotionDateTypes(ctx context.Context) ([]promotion_web.PromotionWebDateTypeResponse, error) {
	query := `SELECT id, name, label_th, label_en FROM promotion_web_date_type ORDER BY id`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to get promotion date types")
		return nil, errors.NewDatabaseError("failed to get promotion date types")
	}
	defer rows.Close()

	var types []promotion_web.PromotionWebDateTypeResponse
	for rows.Next() {
		var t promotion_web.PromotionWebDateTypeResponse
		err := rows.Scan(&t.ID, &t.Name, &t.LabelTh, &t.LabelEn)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan promotion date type")
			return nil, errors.NewDatabaseError("failed to scan promotion date type")
		}
		types = append(types, t)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating promotion date types")
		return nil, errors.NewDatabaseError("error iterating promotion date types")
	}

	return types, nil
}

// Lock credit operations
func (r *PromotionWebRepository) CreateLockCredit(ctx context.Context, req promotion_web.LockCreditPromotionCreateRequest) (int64, error) {
	query := `
		INSERT INTO lock_credit_promotion (
			user_id, promotion_id, promotion_web_user_id, bonus_amount, is_locked, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
		RETURNING id
	`

	var id int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		req.UserID, req.PromotionID, req.PromotionWebUserID, req.BonusAmount, req.IsLocked).Scan(&id)
	if err != nil {
		r.logger.WithError(err).Error("failed to create lock credit")
		return 0, errors.NewDatabaseError("failed to create lock credit")
	}

	return id, nil
}

func (r *PromotionWebRepository) GetLockCreditWithdrawList(ctx context.Context, req promotion_web.GetLockCreditWithdrawListRequest) ([]promotion_web.GetLockCreditWithdrawListResponse, int64, error) {
	baseQuery := `
		SELECT
			lcw.id,
			lcw.user_id,
			u.member_code,
			u.fullname,
			u.phone,
			lcw.ref_id,
			lcw.detail,
			lcw.user_withdraw_lock_credit_type_id,
			uwlct.label_th as user_withdraw_lock_credit_type_th,
			lcw.credit_more_than,
			lcw.allow_withdraw_amount,
			lcw.withdraw_amount,
			lcw.pull_credit_amount,
			lcw.is_locked,
			lcw.is_pull_credit,
			lcw.created_at,
			lcw.approved_at,
			lcw.approved_by_id,
			COALESCE(approver.username, '') as approved_by_name
		FROM lock_credit_withdraw lcw
		LEFT JOIN users u ON lcw.user_id = u.id
		LEFT JOIN user_withdraw_lock_credit_type uwlct ON lcw.user_withdraw_lock_credit_type_id = uwlct.id
		LEFT JOIN users approver ON lcw.approved_by_id = approver.id
		WHERE 1=1
	`

	var conditions []string
	var args []interface{}
	argIndex := 1

	if req.UserID > 0 {
		conditions = append(conditions, fmt.Sprintf("lcw.user_id = $%d", argIndex))
		args = append(args, req.UserID)
		argIndex++
	}

	if req.Search != "" {
		conditions = append(conditions, fmt.Sprintf("(u.member_code ILIKE $%d OR u.fullname ILIKE $%d OR u.phone ILIKE $%d)", argIndex, argIndex+1, argIndex+2))
		searchTerm := "%" + req.Search + "%"
		args = append(args, searchTerm, searchTerm, searchTerm)
		argIndex += 3
	}

	if req.StartDate != "" {
		conditions = append(conditions, fmt.Sprintf("lcw.created_at >= $%d", argIndex))
		args = append(args, req.StartDate)
		argIndex++
	}

	if req.EndDate != "" {
		conditions = append(conditions, fmt.Sprintf("lcw.created_at <= $%d", argIndex))
		args = append(args, req.EndDate+" 23:59:59")
		argIndex++
	}

	if len(conditions) > 0 {
		baseQuery += " AND " + strings.Join(conditions, " AND ")
	}

	// Count total
	countQuery := "SELECT COUNT(*) FROM (" + baseQuery + ") as count_query"
	var total int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.WithError(err).Error("failed to count lock credit withdraw list")
		return nil, 0, errors.NewDatabaseError("failed to count lock credit withdraw list")
	}

	// Add pagination and ordering
	baseQuery += " ORDER BY lcw.created_at DESC"
	baseQuery += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, req.Limit, (req.Page-1)*req.Limit)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, baseQuery, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get lock credit withdraw list")
		return nil, 0, errors.NewDatabaseError("failed to get lock credit withdraw list")
	}
	defer rows.Close()

	var results []promotion_web.GetLockCreditWithdrawListResponse
	for rows.Next() {
		var result promotion_web.GetLockCreditWithdrawListResponse
		err := rows.Scan(
			&result.ID, &result.UserID, &result.MemberCode, &result.Fullname, &result.Phone,
			&result.RefID, &result.Detail, &result.UserWithdrawLockCreditTypeID,
			&result.UserWithdrawLockCreditTypeTh, &result.CreditMoreThan,
			&result.AllowWithdrawAmount, &result.WithdrawAmount, &result.PullCreditAmount,
			&result.IsLocked, &result.IsPullCredit, &result.CreatedAt,
			&result.ApprovedAt, &result.ApprovedByID, &result.ApprovedByName,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan lock credit withdraw row")
			return nil, 0, errors.NewDatabaseError("failed to scan lock credit withdraw row")
		}
		results = append(results, result)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating lock credit withdraw rows")
		return nil, 0, errors.NewDatabaseError("error iterating lock credit withdraw rows")
	}

	return results, total, nil
}

func (r *PromotionWebRepository) UnlockCreditWithdraw(ctx context.Context, id int64, adminID int64) error {
	query := `
		UPDATE lock_credit_withdraw
		SET is_locked = false, approved_by_id = $2, approved_at = NOW(), updated_at = NOW()
		WHERE id = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id, adminID)
	if err != nil {
		r.logger.WithError(err).Error("failed to unlock credit withdraw")
		return errors.NewDatabaseError("failed to unlock credit withdraw")
	}

	rowsAffected := result.RowsAffected()

	if rowsAffected == 0 {
		return errors.NewNotFoundError("lock credit withdraw not found")
	}

	return nil
}

func (r *PromotionWebRepository) CheckLockedCredit(ctx context.Context, userID int64) (bool, error) {
	query := `
		SELECT COUNT(*)
		FROM lock_credit_promotion
		WHERE user_id = $1 AND is_locked = true
	`

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, userID).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to check locked credit")
		return false, errors.NewDatabaseError("failed to check locked credit")
	}

	return count > 0, nil
}

// User promotion operations
func (r *PromotionWebRepository) CreateUserPromotion(ctx context.Context, req promotion_web.CollectPromotionRequest) (int64, error) {
	query := `
		INSERT INTO promotion_web_user (
			promotion_web_id, user_id, promotion_web_user_status_id,
			total_amount, total_deposit_amount, created_at, updated_at
		) VALUES ($1, $2, $3, 0, 0, NOW(), NOW())
		RETURNING id
	`

	var id int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		req.PromotionWebID, req.UserID, promotion_web.PROMOTION_WEB_USER_STATUS_ON_PROCESS).Scan(&id)
	if err != nil {
		r.logger.WithError(err).Error("failed to create user promotion")
		return 0, errors.NewDatabaseError("failed to create user promotion")
	}

	return id, nil
}

func (r *PromotionWebRepository) GetUserPromotionsByUserID(ctx context.Context, userID int64) ([]promotion_web.ShowPromotionForUserResponse, error) {
	query := `
		SELECT
			pw.id,
			pw.promotion_web_type_id,
			pwt.label_th as promotion_web_type_th,
			pwt.label_en as promotion_web_type_en,
			pw.promotion_web_status_id,
			pw.condition_detail,
			pw.image_url,
			pw.name,
			pw.short_description,
			pw.description,
			pw.promotion_web_date_type_id,
			COALESCE(pw.start_date::text, '') as start_date,
			COALESCE(pw.end_date::text, '') as end_date,
			pw.monday, pw.tuesday, pw.wednesday, pw.thursday, pw.friday, pw.saturday, pw.sunday,
			COALESCE(pw.time_start::text, '') as time_start,
			COALESCE(pw.time_end::text, '') as time_end,
			pw.hidden_url_link,
			CASE
				WHEN pwu.id IS NOT NULL THEN 'ON_PROCESS'
				WHEN pw.promotion_web_status_id = $2 THEN 'AVAILABLE'
				ELSE 'NOT_AVAILABLE'
			END as user_status_with_promotion
		FROM promotion_web pw
		LEFT JOIN promotion_web_type pwt ON pw.promotion_web_type_id = pwt.id
		LEFT JOIN promotion_web_user pwu ON pw.id = pwu.promotion_web_id AND pwu.user_id = $1
		WHERE pw.deleted_at IS NULL
		ORDER BY pw.priority_order ASC, pw.created_at DESC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, userID, promotion_web.PROMOTION_WEB_STATUS_ACTIVE)
	if err != nil {
		r.logger.WithError(err).Error("failed to get user promotions")
		return nil, errors.NewDatabaseError("failed to get user promotions")
	}
	defer rows.Close()

	var promotions []promotion_web.ShowPromotionForUserResponse
	for rows.Next() {
		var p promotion_web.ShowPromotionForUserResponse
		err := rows.Scan(
			&p.ID, &p.PromotionWebTypeID, &p.PromotionWebTypeTh, &p.PromotionWebTypeEn,
			&p.PromotionWebStatusID, &p.ConditionDetail, &p.ImageURL, &p.Name,
			&p.ShortDescription, &p.Description, &p.PromotionWebDateTypeID,
			&p.StartDate, &p.EndDate, &p.Monday, &p.Tuesday, &p.Wednesday,
			&p.Thursday, &p.Friday, &p.Saturday, &p.Sunday,
			&p.TimeStart, &p.TimeEnd, &p.HiddenURLLink, &p.UserStatusWithPromotion,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan user promotion")
			return nil, errors.NewDatabaseError("failed to scan user promotion")
		}
		promotions = append(promotions, p)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating user promotions")
		return nil, errors.NewDatabaseError("error iterating user promotions")
	}

	return promotions, nil
}

// Public operations
func (r *PromotionWebRepository) GetPublicPromotions(ctx context.Context) ([]promotion_web.ShowPromotionForUserResponse, error) {
	query := `
		SELECT
			pw.id,
			pw.promotion_web_type_id,
			pwt.label_th as promotion_web_type_th,
			pwt.label_en as promotion_web_type_en,
			pw.promotion_web_status_id,
			pw.condition_detail,
			pw.image_url,
			pw.name,
			pw.short_description,
			pw.description,
			pw.promotion_web_date_type_id,
			COALESCE(pw.start_date::text, '') as start_date,
			COALESCE(pw.end_date::text, '') as end_date,
			pw.monday, pw.tuesday, pw.wednesday, pw.thursday, pw.friday, pw.saturday, pw.sunday,
			COALESCE(pw.time_start::text, '') as time_start,
			COALESCE(pw.time_end::text, '') as time_end,
			pw.hidden_url_link,
			'AVAILABLE' as user_status_with_promotion
		FROM promotion_web pw
		LEFT JOIN promotion_web_type pwt ON pw.promotion_web_type_id = pwt.id
		WHERE pw.promotion_web_status_id = $1 AND pw.deleted_at IS NULL
		ORDER BY pw.priority_order ASC, pw.created_at DESC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, promotion_web.PROMOTION_WEB_STATUS_ACTIVE)
	if err != nil {
		r.logger.WithError(err).Error("failed to get public promotions")
		return nil, errors.NewDatabaseError("failed to get public promotions")
	}
	defer rows.Close()

	var promotions []promotion_web.ShowPromotionForUserResponse
	for rows.Next() {
		var p promotion_web.ShowPromotionForUserResponse
		err := rows.Scan(
			&p.ID, &p.PromotionWebTypeID, &p.PromotionWebTypeTh, &p.PromotionWebTypeEn,
			&p.PromotionWebStatusID, &p.ConditionDetail, &p.ImageURL, &p.Name,
			&p.ShortDescription, &p.Description, &p.PromotionWebDateTypeID,
			&p.StartDate, &p.EndDate, &p.Monday, &p.Tuesday, &p.Wednesday,
			&p.Thursday, &p.Friday, &p.Saturday, &p.Sunday,
			&p.TimeStart, &p.TimeEnd, &p.HiddenURLLink, &p.UserStatusWithPromotion,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan public promotion")
			return nil, errors.NewDatabaseError("failed to scan public promotion")
		}
		promotions = append(promotions, p)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating public promotions")
		return nil, errors.NewDatabaseError("error iterating public promotions")
	}

	return promotions, nil
}

func (r *PromotionWebRepository) GetPublicPromotionByID(ctx context.Context, id int64) (*promotion_web.PromotionWebGetByIdResponse, error) {
	// Reuse the existing GetPromotionWebById method but add public visibility check
	promotion, err := r.GetPromotionWebById(ctx, id)
	if err != nil {
		return nil, err
	}

	// Check if promotion is publicly visible
	if promotion.PromotionWebStatusId != promotion_web.PROMOTION_WEB_STATUS_ACTIVE {
		return nil, errors.NewNotFoundError("promotion not found or not publicly available")
	}

	return promotion, nil
}

// Business logic helpers
func (r *PromotionWebRepository) ValidatePromotionEligibility(ctx context.Context, userID, promotionID int64) error {
	// Check if user already has this promotion
	query := `
		SELECT COUNT(*)
		FROM promotion_web_user
		WHERE user_id = $1 AND promotion_web_id = $2 AND promotion_web_user_status_id IN ($3, $4)
	`

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, userID, promotionID,
		promotion_web.PROMOTION_WEB_USER_STATUS_ON_PROCESS, promotion_web.PROMOTION_WEB_USER_STATUS_SUCCESS).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to check promotion eligibility")
		return errors.NewDatabaseError("failed to check promotion eligibility")
	}

	if count > 0 {
		return errors.NewValidationError("user already has this promotion")
	}

	// Check if promotion is active and available
	promotionQuery := `
		SELECT promotion_web_status_id
		FROM promotion_web
		WHERE id = $1 AND deleted_at IS NULL
	`

	var statusID int64
	err = dbutil.QueryRowWithSchema(ctx, r.pool, promotionQuery, promotionID).Scan(&statusID)
	if err != nil {
		if err.Error() == "no rows in result set" {
			return errors.NewNotFoundError("promotion not found")
		}
		r.logger.WithError(err).Error("failed to get promotion status")
		return errors.NewDatabaseError("failed to get promotion status")
	}

	if statusID != promotion_web.PROMOTION_WEB_STATUS_ACTIVE {
		return errors.NewValidationError("promotion is not available")
	}

	return nil
}

func (r *PromotionWebRepository) UpdateExpiredPromotions(ctx context.Context) error {
	query := `
		UPDATE promotion_web
		SET promotion_web_status_id = $1, updated_at = NOW()
		WHERE end_date < CURRENT_DATE
		AND promotion_web_status_id = $2
		AND deleted_at IS NULL
	`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		promotion_web.PROMOTION_WEB_STATUS_CANCELED, promotion_web.PROMOTION_WEB_STATUS_ACTIVE)
	if err != nil {
		r.logger.WithError(err).Error("failed to update expired promotions")
		return errors.NewDatabaseError("failed to update expired promotions")
	}

	return nil
}

// Additional methods from migration document
func (r *PromotionWebRepository) GetPromotionSlideList(ctx context.Context) ([]promotion_web.PromotionSlideResponse, error) {
	query := `
		SELECT
			id, name, image_url, short_description, hidden_url_link
		FROM promotion_web
		WHERE promotion_web_status_id = $1
		AND deleted_at IS NULL
		ORDER BY priority_order ASC, created_at DESC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, promotion_web.PROMOTION_WEB_STATUS_ACTIVE)
	if err != nil {
		r.logger.WithError(err).Error("failed to get promotion slide list")
		return nil, errors.NewDatabaseError("failed to get promotion slide list")
	}
	defer rows.Close()

	var slides []promotion_web.PromotionSlideResponse
	for rows.Next() {
		var slide promotion_web.PromotionSlideResponse
		err := rows.Scan(&slide.ID, &slide.Name, &slide.ImageURL, &slide.ShortDescription, &slide.HiddenURLLink)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan promotion slide")
			return nil, errors.NewDatabaseError("failed to scan promotion slide")
		}
		slides = append(slides, slide)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating promotion slides")
		return nil, errors.NewDatabaseError("error iterating promotion slides")
	}

	return slides, nil
}

func (r *PromotionWebRepository) GetPromotionSummary(ctx context.Context, req promotion_web.PromotionSummaryRequest) (*promotion_web.PromotionSummaryResponse, error) {
	query := `
		SELECT
			COUNT(*) as total_promotions,
			COUNT(CASE WHEN promotion_web_status_id = $1 THEN 1 END) as active_promotions,
			COUNT(CASE WHEN promotion_web_status_id = $2 THEN 1 END) as inactive_promotions
		FROM promotion_web
		WHERE deleted_at IS NULL
	`

	args := []interface{}{promotion_web.PROMOTION_WEB_STATUS_ACTIVE, promotion_web.PROMOTION_WEB_STATUS_DISABLE_WEB}
	argIndex := 3

	if req.StartDate != "" {
		query += fmt.Sprintf(" AND created_at >= $%d", argIndex)
		args = append(args, req.StartDate)
		argIndex++
	}

	if req.EndDate != "" {
		query += fmt.Sprintf(" AND created_at <= $%d", argIndex)
		args = append(args, req.EndDate+" 23:59:59")
		argIndex++
	}

	var summary promotion_web.PromotionSummaryResponse
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(
		&summary.TotalPromotions, &summary.ActivePromotions, &summary.InactivePromotions)
	if err != nil {
		r.logger.WithError(err).Error("failed to get promotion summary")
		return nil, errors.NewDatabaseError("failed to get promotion summary")
	}

	return &summary, nil
}

func (r *PromotionWebRepository) GetUserPromotionSummary(ctx context.Context, req promotion_web.UserPromotionSummaryRequest) (*promotion_web.UserPromotionSummaryResponse, error) {
	query := `
		SELECT
			COUNT(*) as total_collected,
			COUNT(CASE WHEN promotion_web_user_status_id = $2 THEN 1 END) as total_success,
			COUNT(CASE WHEN promotion_web_user_status_id = $3 THEN 1 END) as total_canceled,
			COALESCE(SUM(total_amount), 0) as total_amount
		FROM promotion_web_user
		WHERE user_id = $1
	`

	args := []interface{}{req.UserID, promotion_web.PROMOTION_WEB_USER_STATUS_SUCCESS, promotion_web.PROMOTION_WEB_USER_STATUS_CANCELED}
	argIndex := 4

	if req.StartDate != "" {
		query += fmt.Sprintf(" AND created_at >= $%d", argIndex)
		args = append(args, req.StartDate)
		argIndex++
	}

	if req.EndDate != "" {
		query += fmt.Sprintf(" AND created_at <= $%d", argIndex)
		args = append(args, req.EndDate+" 23:59:59")
		argIndex++
	}

	var summary promotion_web.UserPromotionSummaryResponse
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(
		&summary.TotalCollected, &summary.TotalSuccess, &summary.TotalCanceled, &summary.TotalAmount)
	if err != nil {
		r.logger.WithError(err).Error("failed to get user promotion summary")
		return nil, errors.NewDatabaseError("failed to get user promotion summary")
	}

	return &summary, nil
}

func (r *PromotionWebRepository) GetPromotionByHiddenURL(ctx context.Context, userID int64, hiddenURL string) (*promotion_web.GetPromotionByHiddenURLResponse, error) {
	query := `
		SELECT
			pw.id,
			pw.promotion_web_type_id,
			pwt.label_th as promotion_web_type_th,
			pwt.label_en as promotion_web_type_en,
			pw.promotion_web_status_id,
			pw.condition_detail,
			pw.image_url,
			pw.name,
			pw.short_description,
			pw.description,
			pw.promotion_web_date_type_id,
			COALESCE(pw.start_date::text, '') as start_date,
			COALESCE(pw.end_date::text, '') as end_date,
			pw.monday, pw.tuesday, pw.wednesday, pw.thursday, pw.friday, pw.saturday, pw.sunday,
			COALESCE(pw.time_start::text, '') as time_start,
			COALESCE(pw.time_end::text, '') as time_end,
			pw.hidden_url_link,
			CASE
				WHEN pwu.id IS NOT NULL THEN 'ON_PROCESS'
				WHEN pw.promotion_web_status_id = $3 THEN 'AVAILABLE'
				ELSE 'NOT_AVAILABLE'
			END as user_status_with_promotion
		FROM promotion_web pw
		LEFT JOIN promotion_web_type pwt ON pw.promotion_web_type_id = pwt.id
		LEFT JOIN promotion_web_user pwu ON pw.id = pwu.promotion_web_id AND pwu.user_id = $1
		WHERE pw.hidden_url_link = $2 AND pw.deleted_at IS NULL
	`

	var promotion promotion_web.GetPromotionByHiddenURLResponse
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, userID, hiddenURL, promotion_web.PROMOTION_WEB_STATUS_ACTIVE).Scan(
		&promotion.ID, &promotion.PromotionWebTypeID, &promotion.PromotionWebTypeTh, &promotion.PromotionWebTypeEn,
		&promotion.PromotionWebStatusID, &promotion.ConditionDetail, &promotion.ImageURL, &promotion.Name,
		&promotion.ShortDescription, &promotion.Description, &promotion.PromotionWebDateTypeID,
		&promotion.StartDate, &promotion.EndDate, &promotion.Monday, &promotion.Tuesday, &promotion.Wednesday,
		&promotion.Thursday, &promotion.Friday, &promotion.Saturday, &promotion.Sunday,
		&promotion.TimeStart, &promotion.TimeEnd, &promotion.HiddenURLLink, &promotion.UserStatusWithPromotion,
	)
	if err != nil {
		if err.Error() == "no rows in result set" {
			return nil, errors.NewNotFoundError("promotion not found")
		}
		r.logger.WithError(err).Error("failed to get promotion by hidden URL")
		return nil, errors.NewDatabaseError("failed to get promotion by hidden URL")
	}

	return &promotion, nil
}

func (r *PromotionWebRepository) GetUserTurnoverSummary(ctx context.Context, userPromotionID int64) (*promotion_web.UserTurnoverSummaryResponse, error) {
	query := `
		SELECT
			pwu.id as user_promotion_id,
			COALESCE(pw.turnover_amount, 0) as required_turnover,
			COALESCE(pwu.total_amount, 0) as current_turnover,
			CASE
				WHEN pw.turnover_amount > 0 THEN
					GREATEST(0, pw.turnover_amount - COALESCE(pwu.total_amount, 0))
				ELSE 0
			END as remaining_turnover,
			CASE
				WHEN pw.turnover_amount > 0 THEN
					LEAST(100, (COALESCE(pwu.total_amount, 0) / pw.turnover_amount) * 100)
				ELSE 100
			END as turnover_percentage,
			CASE
				WHEN pw.turnover_amount > 0 THEN
					COALESCE(pwu.total_amount, 0) >= pw.turnover_amount
				ELSE true
			END as is_completed
		FROM promotion_web_user pwu
		LEFT JOIN promotion_web pw ON pwu.promotion_web_id = pw.id
		WHERE pwu.id = $1
	`

	var summary promotion_web.UserTurnoverSummaryResponse
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, userPromotionID).Scan(
		&summary.UserPromotionID, &summary.RequiredTurnover, &summary.CurrentTurnover,
		&summary.RemainingTurnover, &summary.TurnoverPercentage, &summary.IsCompleted)
	if err != nil {
		if err.Error() == "no rows in result set" {
			return nil, errors.NewNotFoundError("user promotion not found")
		}
		r.logger.WithError(err).Error("failed to get user turnover summary")
		return nil, errors.NewDatabaseError("failed to get user turnover summary")
	}

	return &summary, nil
}

func (r *PromotionWebRepository) CheckUserHasPromotionType(ctx context.Context, userID int64, promotionTypeID int64) (bool, error) {
	query := `
		SELECT EXISTS(
			SELECT 1 FROM promotion_web_user pwu
			JOIN promotion_web pw ON pwu.promotion_web_id = pw.id
			WHERE pwu.user_id = $1
			AND pw.promotion_web_type_id = $2
			AND pwu.promotion_web_user_status_id IN ($3, $4)
		)
	`

	var exists bool
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, userID, promotionTypeID,
		promotion_web.PROMOTION_WEB_USER_STATUS_ON_PROCESS,
		promotion_web.PROMOTION_WEB_USER_STATUS_SUCCESS).Scan(&exists)
	if err != nil {
		r.logger.WithError(err).Error("failed to check user promotion type")
		return false, errors.NewDatabaseError("failed to check user promotion type")
	}

	return exists, nil
}

func (r *PromotionWebRepository) CheckUserFirstDeposit(ctx context.Context, userID int64) (bool, error) {
	// This would typically check against a deposits table
	// For now, return a placeholder implementation
	query := `
		SELECT NOT EXISTS(
			SELECT 1 FROM user_deposits
			WHERE user_id = $1 AND status = 'completed'
		)
	`

	var isFirstDeposit bool
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, userID).Scan(&isFirstDeposit)
	if err != nil {
		// If deposits table doesn't exist, assume it's first deposit
		r.logger.WithError(err).Warn("deposits table not found, assuming first deposit")
		return true, nil
	}

	return isFirstDeposit, nil
}

func (r *PromotionWebRepository) GetUserLockedAmount(ctx context.Context, userID int64) (float64, error) {
	query := `
		SELECT COALESCE(SUM(bonus_amount), 0)
		FROM lock_credit_promotion
		WHERE user_id = $1 AND is_locked = true
	`

	var lockedAmount float64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, userID).Scan(&lockedAmount)
	if err != nil {
		r.logger.WithError(err).Error("failed to get user locked amount")
		return 0, errors.NewDatabaseError("failed to get user locked amount")
	}

	return lockedAmount, nil
}

func (r *PromotionWebRepository) GetUserCollectedPromotions(ctx context.Context, userID int64, req promotion_web.GetUserCollectedPromotionsRequest) ([]promotion_web.GetUserCollectedPromotionsResponse, int64, error) {
	baseQuery := `
		SELECT
			pwu.id,
			pwu.promotion_web_id,
			pw.name as promotion_name,
			pwu.promotion_web_user_status_id,
			pwus.label_th as status_name,
			pwu.total_amount,
			pwu.created_at
		FROM promotion_web_user pwu
		LEFT JOIN promotion_web pw ON pwu.promotion_web_id = pw.id
		LEFT JOIN promotion_web_user_status pwus ON pwu.promotion_web_user_status_id = pwus.id
		WHERE pwu.user_id = $1
	`

	// Count total
	countQuery := "SELECT COUNT(*) FROM (" + baseQuery + ") as count_query"
	var total int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, userID).Scan(&total)
	if err != nil {
		r.logger.WithError(err).Error("failed to count user collected promotions")
		return nil, 0, errors.NewDatabaseError("failed to count user collected promotions")
	}

	// Add pagination and ordering
	baseQuery += " ORDER BY pwu.created_at DESC"
	baseQuery += fmt.Sprintf(" LIMIT $2 OFFSET $3")
	args := []interface{}{userID, req.Limit, (req.Page - 1) * req.Limit}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, baseQuery, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get user collected promotions")
		return nil, 0, errors.NewDatabaseError("failed to get user collected promotions")
	}
	defer rows.Close()

	var results []promotion_web.GetUserCollectedPromotionsResponse
	for rows.Next() {
		var result promotion_web.GetUserCollectedPromotionsResponse
		err := rows.Scan(
			&result.ID, &result.PromotionWebID, &result.PromotionName,
			&result.PromotionWebUserStatusID, &result.StatusName,
			&result.TotalAmount, &result.CreatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan user collected promotion")
			return nil, 0, errors.NewDatabaseError("failed to scan user collected promotion")
		}
		results = append(results, result)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating user collected promotions")
		return nil, 0, errors.NewDatabaseError("error iterating user collected promotions")
	}

	return results, total, nil
}

func (r *PromotionWebRepository) GetUserPromotionByID(ctx context.Context, userID int64, promotionID int64) (*promotion_web.GetPromotionByHiddenURLResponse, error) {
	query := `
		SELECT
			pw.id,
			pw.promotion_web_type_id,
			pwt.label_th as promotion_web_type_th,
			pwt.label_en as promotion_web_type_en,
			pw.promotion_web_status_id,
			pw.condition_detail,
			pw.image_url,
			pw.name,
			pw.short_description,
			pw.description,
			pw.promotion_web_date_type_id,
			COALESCE(pw.start_date::text, '') as start_date,
			COALESCE(pw.end_date::text, '') as end_date,
			pw.monday, pw.tuesday, pw.wednesday, pw.thursday, pw.friday, pw.saturday, pw.sunday,
			COALESCE(pw.time_start::text, '') as time_start,
			COALESCE(pw.time_end::text, '') as time_end,
			pw.hidden_url_link,
			CASE
				WHEN pwu.id IS NOT NULL THEN 'ON_PROCESS'
				WHEN pw.promotion_web_status_id = $3 THEN 'AVAILABLE'
				ELSE 'NOT_AVAILABLE'
			END as user_status_with_promotion
		FROM promotion_web pw
		LEFT JOIN promotion_web_type pwt ON pw.promotion_web_type_id = pwt.id
		LEFT JOIN promotion_web_user pwu ON pw.id = pwu.promotion_web_id AND pwu.user_id = $1
		WHERE pw.id = $2 AND pw.deleted_at IS NULL
	`

	var promotion promotion_web.GetPromotionByHiddenURLResponse
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, userID, promotionID, promotion_web.PROMOTION_WEB_STATUS_ACTIVE).Scan(
		&promotion.ID, &promotion.PromotionWebTypeID, &promotion.PromotionWebTypeTh, &promotion.PromotionWebTypeEn,
		&promotion.PromotionWebStatusID, &promotion.ConditionDetail, &promotion.ImageURL, &promotion.Name,
		&promotion.ShortDescription, &promotion.Description, &promotion.PromotionWebDateTypeID,
		&promotion.StartDate, &promotion.EndDate, &promotion.Monday, &promotion.Tuesday, &promotion.Wednesday,
		&promotion.Thursday, &promotion.Friday, &promotion.Saturday, &promotion.Sunday,
		&promotion.TimeStart, &promotion.TimeEnd, &promotion.HiddenURLLink, &promotion.UserStatusWithPromotion,
	)
	if err != nil {
		if err.Error() == "no rows in result set" {
			return nil, errors.NewNotFoundError("promotion not found")
		}
		r.logger.WithError(err).Error("failed to get user promotion by ID")
		return nil, errors.NewDatabaseError("failed to get user promotion by ID")
	}

	return &promotion, nil
}
