package postgres

import (
	"blacking-api/internal/domain/user_transaction"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type UserTransactionRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewUserTransactionRepository creates a new user transaction repository
func NewUserTransactionRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.UserTransactionRepository {
	return &UserTransactionRepository{
		pool:   pool,
		logger: logger,
	}
}

// C<PERSON> creates a new user transaction
func (r *UserTransactionRepository) Create(ctx context.Context, transaction *user_transaction.UserTransaction) (*user_transaction.UserTransaction, error) {
	query := `
		INSERT INTO user_transaction (
			direction_id, type_id, member_id, banking_id, transfer_banking_id,
			ref_id, detail, date, credit_amount, credit_before, credit_after,
			credit_back, bonus_amount, transfer_at, confirm_admin_id, promotion_id, status_id
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17
		) RETURNING id, created_at, updated_at
	`

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		transaction.DirectionID,
		transaction.TypeID,
		transaction.MemberID,
		transaction.BankingID,
		transaction.TransferBankingID,
		transaction.RefID,
		transaction.Detail,
		transaction.Date,
		transaction.CreditAmount,
		transaction.CreditBefore,
		transaction.CreditAfter,
		transaction.CreditBack,
		transaction.BonusAmount,
		transaction.TransferAt,
		transaction.ConfirmAdminID,
		transaction.PromotionID,
		transaction.StatusID,
	)

	err := row.Scan(&transaction.ID, &transaction.CreatedAt, &transaction.UpdatedAt)
	if err != nil {
		r.logger.WithError(err).Error("failed to create user transaction")
		return nil, errors.NewDatabaseError("failed to create user transaction")
	}

	return transaction, nil
}

// GetByID retrieves a user transaction by ID with joined data
func (r *UserTransactionRepository) GetByID(ctx context.Context, id int64) (*user_transaction.UserTransaction, error) {
	query := `
		SELECT 
			ut.id, ut.direction_id, ut.type_id, ut.member_id, ut.banking_id, 
			ut.transfer_banking_id, ut.ref_id, ut.detail, ut.date, ut.credit_amount,
			ut.credit_before, ut.credit_after, ut.credit_back, ut.bonus_amount,
			ut.transfer_at, ut.confirm_admin_id, ut.promotion_id, ut.status_id,
			ut.created_at, ut.updated_at,
			utd.name as direction_name,
			utt.name as type_name,
			uts.name as status_name,
			uts.detail as status_detail,
			m.username,
			b.name as banking_name,
			tb.name as transfer_banking_name,
			u.username as confirm_admin_name
		FROM user_transaction ut
		LEFT JOIN user_transaction_direction utd ON ut.direction_id = utd.id
		LEFT JOIN user_transaction_type utt ON ut.type_id = utt.id
		LEFT JOIN user_transaction_status uts ON ut.status_id = uts.id
		LEFT JOIN members m ON ut.member_id = m.id
		LEFT JOIN banking b ON ut.banking_id = b.id
		LEFT JOIN banking tb ON ut.transfer_banking_id = tb.id
		LEFT JOIN users u ON ut.confirm_admin_id = u.id
		WHERE ut.id = $1
	`

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)

	var transaction user_transaction.UserTransaction
	err := row.Scan(
		&transaction.ID, &transaction.DirectionID, &transaction.TypeID, &transaction.MemberID,
		&transaction.BankingID, &transaction.TransferBankingID, &transaction.RefID,
		&transaction.Detail, &transaction.Date, &transaction.CreditAmount,
		&transaction.CreditBefore, &transaction.CreditAfter, &transaction.CreditBack,
		&transaction.BonusAmount, &transaction.TransferAt, &transaction.ConfirmAdminID,
		&transaction.PromotionID, &transaction.StatusID, &transaction.CreatedAt,
		&transaction.UpdatedAt, &transaction.DirectionName, &transaction.TypeName,
		&transaction.StatusName, &transaction.StatusDetail, &transaction.Username,
		&transaction.BankingName, &transaction.TransferBankingName,
		&transaction.ConfirmAdminName,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("user transaction not found")
		}
		r.logger.WithError(err).Error("failed to get user transaction by ID")
		return nil, errors.NewDatabaseError("failed to retrieve user transaction")
	}

	return &transaction, nil
}

// GenerateRefID generates a unique reference ID for transactions
func (r *UserTransactionRepository) GenerateRefID(ctx context.Context) (string, error) {
	// Generate a reference ID based on timestamp and random component
	now := time.Now()
	refID := fmt.Sprintf("TXN%d%02d%02d%02d%02d%02d",
		now.Year(), now.Month(), now.Day(),
		now.Hour(), now.Minute(), now.Second())

	// Check if this ref_id already exists and append a counter if needed
	for i := 0; i < 100; i++ {
		testRefID := refID
		if i > 0 {
			testRefID = fmt.Sprintf("%s%02d", refID, i)
		}

		query := `SELECT EXISTS(SELECT 1 FROM user_transaction WHERE ref_id = $1)`
		var exists bool
		row := dbutil.QueryRowWithSchema(ctx, r.pool, query, testRefID)
		err := row.Scan(&exists)
		if err != nil {
			r.logger.WithError(err).Error("failed to check ref_id existence")
			return "", errors.NewDatabaseError("failed to generate reference ID")
		}

		if !exists {
			return testRefID, nil
		}
	}

	return "", errors.NewDatabaseError("failed to generate unique reference ID")
}

// GetMemberByPhoneOrCode retrieves member ID by phone number or member code
func (r *UserTransactionRepository) GetMemberByPhoneOrCode(ctx context.Context, phoneOrMemberCode string) (int, error) {
	query := `
		SELECT id FROM members 
		WHERE phone = $1 OR username = $1
		LIMIT 1
	`

	var memberID int
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, phoneOrMemberCode)
	err := row.Scan(&memberID)

	if err != nil {
		if err == pgx.ErrNoRows {
			return 0, errors.NewNotFoundError("member not found with provided phone or member code")
		}
		r.logger.WithError(err).Error("failed to get member by phone or code")
		return 0, errors.NewDatabaseError("failed to retrieve member")
	}

	return memberID, nil
}

// UpdateStatus updates the status of a user transaction
func (r *UserTransactionRepository) UpdateStatus(ctx context.Context, id int64, req *user_transaction.UpdateUserTransactionStatusRequest) error {
	query := `
		UPDATE user_transaction 
		SET status_id = $1, confirm_admin_id = $2, detail = $3, updated_at = CURRENT_TIMESTAMP
		WHERE id = $4
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		req.StatusID, req.ConfirmAdminID, req.Detail, id)

	if err != nil {
		r.logger.WithError(err).Error("failed to update user transaction status")
		return errors.NewDatabaseError("failed to update transaction status")
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return errors.NewNotFoundError("user transaction not found")
	}

	return nil
}

// GetDepositPage retrieves paginated deposit transactions with filters
func (r *UserTransactionRepository) GetDepositPage(ctx context.Context, req *user_transaction.UserTransactionDepositPageRequest) ([]*user_transaction.UserTransaction, int64, error) {
	baseQuery := `
		FROM user_transaction ut
		LEFT JOIN user_transaction_direction utd ON ut.direction_id = utd.id
		LEFT JOIN user_transaction_type utt ON ut.type_id = utt.id
		LEFT JOIN user_transaction_status uts ON ut.status_id = uts.id
		LEFT JOIN members m ON ut.member_id = m.id
		LEFT JOIN banking b ON ut.banking_id = b.id
		LEFT JOIN banking tb ON ut.transfer_banking_id = tb.id
		LEFT JOIN users u ON ut.confirm_admin_id = u.id
		WHERE ut.direction_id = 1
	`

	var whereConditions []string
	var args []interface{}
	argIndex := 1

	// Build where conditions based on filters
	if req.StatusID != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.status_id = $%d", argIndex))
		args = append(args, *req.StatusID)
		argIndex++
	}

	if req.Date != nil && *req.Date != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("DATE(ut.date) = $%d", argIndex))
		args = append(args, *req.Date)
		argIndex++
	}

	if req.Username != nil && *req.Username != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("m.username ILIKE $%d", argIndex))
		args = append(args, "%"+*req.Username+"%")
		argIndex++
	}

	// Note: member_code column does not exist in members table
	// Removed member_code filter condition

	if req.BankingID != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.banking_id = $%d", argIndex))
		args = append(args, *req.BankingID)
		argIndex++
	}

	if req.StartAmount != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.credit_amount >= $%d", argIndex))
		args = append(args, *req.StartAmount)
		argIndex++
	}

	if req.EndAmount != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.credit_amount <= $%d", argIndex))
		args = append(args, *req.EndAmount)
		argIndex++
	}

	if req.Amount != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.credit_amount = $%d", argIndex))
		args = append(args, *req.Amount)
		argIndex++
	}

	if req.Admin != nil && *req.Admin != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("u.username ILIKE $%d", argIndex))
		args = append(args, "%"+*req.Admin+"%")
		argIndex++
	}

	// Add additional where conditions
	if len(whereConditions) > 0 {
		baseQuery += " AND " + strings.Join(whereConditions, " AND ")
	}

	return r.executePaginatedQuery(ctx, baseQuery, args, req.Page, req.Limit)
}

// GetWithdrawPage retrieves paginated withdraw transactions with filters
func (r *UserTransactionRepository) GetWithdrawPage(ctx context.Context, req *user_transaction.UserTransactionWithdrawPageRequest) ([]*user_transaction.UserTransaction, int64, error) {
	baseQuery := `
		FROM user_transaction ut
		LEFT JOIN user_transaction_direction utd ON ut.direction_id = utd.id
		LEFT JOIN user_transaction_type utt ON ut.type_id = utt.id
		LEFT JOIN user_transaction_status uts ON ut.status_id = uts.id
		LEFT JOIN members m ON ut.member_id = m.id
		LEFT JOIN banking b ON ut.banking_id = b.id
		LEFT JOIN banking tb ON ut.transfer_banking_id = tb.id
		LEFT JOIN users u ON ut.confirm_admin_id = u.id
		WHERE ut.direction_id = 2
	`

	var whereConditions []string
	var args []interface{}
	argIndex := 1

	// Build where conditions (similar to deposit but with withdraw-specific filters)
	if req.StatusID != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.status_id = $%d", argIndex))
		args = append(args, *req.StatusID)
		argIndex++
	}

	if req.Date != nil && *req.Date != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("DATE(ut.date) = $%d", argIndex))
		args = append(args, *req.Date)
		argIndex++
	}

	if req.Username != nil && *req.Username != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("m.username ILIKE $%d", argIndex))
		args = append(args, "%"+*req.Username+"%")
		argIndex++
	}

	// Note: member_code column does not exist in members table
	// Removed member_code filter condition

	if req.BankingID != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.banking_id = $%d", argIndex))
		args = append(args, *req.BankingID)
		argIndex++
	}

	if req.StartAmount != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.credit_amount >= $%d", argIndex))
		args = append(args, *req.StartAmount)
		argIndex++
	}

	if req.EndAmount != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.credit_amount <= $%d", argIndex))
		args = append(args, *req.EndAmount)
		argIndex++
	}

	if req.Amount != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.credit_amount = $%d", argIndex))
		args = append(args, *req.Amount)
		argIndex++
	}

	// Add additional where conditions
	if len(whereConditions) > 0 {
		baseQuery += " AND " + strings.Join(whereConditions, " AND ")
	}

	return r.executePaginatedQuery(ctx, baseQuery, args, req.Page, req.Limit)
}

// GetTransferPage retrieves paginated transfer transactions with filters
func (r *UserTransactionRepository) GetTransferPage(ctx context.Context, req *user_transaction.UserTransactionTransferPageRequest) ([]*user_transaction.UserTransaction, int64, error) {
	baseQuery := `
		FROM user_transaction ut
		LEFT JOIN user_transaction_direction utd ON ut.direction_id = utd.id
		LEFT JOIN user_transaction_type utt ON ut.type_id = utt.id
		LEFT JOIN user_transaction_status uts ON ut.status_id = uts.id
		LEFT JOIN members m ON ut.member_id = m.id
		LEFT JOIN banking b ON ut.banking_id = b.id
		LEFT JOIN banking tb ON ut.transfer_banking_id = tb.id
		LEFT JOIN users u ON ut.confirm_admin_id = u.id
		WHERE ut.transfer_banking_id IS NOT NULL
	`

	var whereConditions []string
	var args []interface{}
	argIndex := 1

	if req.StatusID != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.status_id = $%d", argIndex))
		args = append(args, *req.StatusID)
		argIndex++
	}

	if req.Date != nil && *req.Date != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("DATE(ut.date) = $%d", argIndex))
		args = append(args, *req.Date)
		argIndex++
	}

	if req.Amount != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.credit_amount = $%d", argIndex))
		args = append(args, *req.Amount)
		argIndex++
	}

	// Add additional where conditions
	if len(whereConditions) > 0 {
		baseQuery += " AND " + strings.Join(whereConditions, " AND ")
	}

	return r.executePaginatedQuery(ctx, baseQuery, args, req.Page, req.Limit)
}

// executePaginatedQuery executes a paginated query and returns results with total count
func (r *UserTransactionRepository) executePaginatedQuery(ctx context.Context, baseQuery string, args []interface{}, page, limit int) ([]*user_transaction.UserTransaction, int64, error) {
	// Calculate offset
	offset := (page - 1) * limit

	// Build the complete query with pagination
	selectFields := `
		ut.id, ut.direction_id, ut.type_id, ut.member_id, ut.banking_id,
		ut.transfer_banking_id, ut.ref_id, ut.detail, ut.date, ut.credit_amount,
		ut.credit_before, ut.credit_after, ut.credit_back, ut.bonus_amount,
		ut.transfer_at, ut.confirm_admin_id, ut.promotion_id, ut.status_id,
		ut.created_at, ut.updated_at,
		utd.name as direction_name,
		utt.name as type_name,
		uts.name as status_name,
		uts.detail as status_detail,
		m.username,
		b.name as banking_name,
		tb.name as transfer_banking_name,
		u.username as confirm_admin_name
	`

	// Count query
	countQuery := "SELECT COUNT(*) " + baseQuery
	var total int64
	countRow := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...)
	err := countRow.Scan(&total)
	if err != nil {
		r.logger.WithError(err).Error("failed to count user transactions")
		return nil, 0, errors.NewDatabaseError("failed to count transactions")
	}

	// Data query with pagination
	dataQuery := fmt.Sprintf("SELECT %s %s ORDER BY ut.created_at DESC LIMIT $%d OFFSET $%d",
		selectFields, baseQuery, len(args)+1, len(args)+2)
	args = append(args, limit, offset)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, dataQuery, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to query user transactions")
		return nil, 0, errors.NewDatabaseError("failed to retrieve transactions")
	}
	defer rows.Close()

	var transactions []*user_transaction.UserTransaction
	for rows.Next() {
		var transaction user_transaction.UserTransaction
		err := rows.Scan(
			&transaction.ID, &transaction.DirectionID, &transaction.TypeID, &transaction.MemberID,
			&transaction.BankingID, &transaction.TransferBankingID, &transaction.RefID,
			&transaction.Detail, &transaction.Date, &transaction.CreditAmount,
			&transaction.CreditBefore, &transaction.CreditAfter, &transaction.CreditBack,
			&transaction.BonusAmount, &transaction.TransferAt, &transaction.ConfirmAdminID,
			&transaction.PromotionID, &transaction.StatusID, &transaction.CreatedAt,
			&transaction.UpdatedAt, &transaction.DirectionName, &transaction.TypeName,
			&transaction.StatusName, &transaction.StatusDetail, &transaction.Username,
			&transaction.BankingName, &transaction.TransferBankingName,
			&transaction.ConfirmAdminName,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan user transaction")
			return nil, 0, errors.NewDatabaseError("failed to scan transaction")
		}
		transactions = append(transactions, &transaction)
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating user transaction rows")
		return nil, 0, errors.NewDatabaseError("failed to retrieve transactions")
	}

	return transactions, total, nil
}

// GetDepositsByMemberID retrieves paginated deposit transactions for a specific member
func (r *UserTransactionRepository) GetDepositsByMemberID(ctx context.Context, memberID int, req *user_transaction.UserTransactionDepositByUserRequest) ([]*user_transaction.UserTransaction, int64, error) {
	baseQuery := `
		FROM user_transaction ut
		LEFT JOIN user_transaction_direction utd ON ut.direction_id = utd.id
		LEFT JOIN user_transaction_type utt ON ut.type_id = utt.id
		LEFT JOIN user_transaction_status uts ON ut.status_id = uts.id
		LEFT JOIN members m ON ut.member_id = m.id
		LEFT JOIN banking b ON ut.banking_id = b.id
		LEFT JOIN banking tb ON ut.transfer_banking_id = tb.id
		LEFT JOIN users u ON ut.confirm_admin_id = u.id
		WHERE ut.direction_id = 1 AND ut.member_id = $1
	`

	var whereConditions []string
	var args []interface{}
	args = append(args, memberID) // First argument for member_id
	argIndex := 2

	// Build additional where conditions based on filters
	if req.StatusID != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.status_id = $%d", argIndex))
		args = append(args, *req.StatusID)
		argIndex++
	}

	if req.Date != nil && *req.Date != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("DATE(ut.date) = $%d", argIndex))
		args = append(args, *req.Date)
		argIndex++
	}

	if req.BankingID != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.banking_id = $%d", argIndex))
		args = append(args, *req.BankingID)
		argIndex++
	}

	if req.StartAmount != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.credit_amount >= $%d", argIndex))
		args = append(args, *req.StartAmount)
		argIndex++
	}

	if req.EndAmount != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.credit_amount <= $%d", argIndex))
		args = append(args, *req.EndAmount)
		argIndex++
	}

	if req.Amount != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.credit_amount = $%d", argIndex))
		args = append(args, *req.Amount)
		argIndex++
	}

	// Add additional where conditions
	if len(whereConditions) > 0 {
		baseQuery += " AND " + strings.Join(whereConditions, " AND ")
	}

	return r.executePaginatedQuery(ctx, baseQuery, args, req.Page, req.Limit)
}

// GetWithdrawsByMemberID retrieves paginated withdraw transactions for a specific member
func (r *UserTransactionRepository) GetWithdrawsByMemberID(ctx context.Context, memberID int, req *user_transaction.UserTransactionWithdrawByUserRequest) ([]*user_transaction.UserTransaction, int64, error) {
	baseQuery := `
		FROM user_transaction ut
		LEFT JOIN user_transaction_direction utd ON ut.direction_id = utd.id
		LEFT JOIN user_transaction_type utt ON ut.type_id = utt.id
		LEFT JOIN user_transaction_status uts ON ut.status_id = uts.id
		LEFT JOIN members m ON ut.member_id = m.id
		LEFT JOIN banking b ON ut.banking_id = b.id
		LEFT JOIN banking tb ON ut.transfer_banking_id = tb.id
		LEFT JOIN users u ON ut.confirm_admin_id = u.id
		WHERE ut.direction_id = 2 AND ut.member_id = $1
	`

	var whereConditions []string
	var args []interface{}
	args = append(args, memberID) // First argument for member_id
	argIndex := 2

	// Build additional where conditions based on filters
	if req.StatusID != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.status_id = $%d", argIndex))
		args = append(args, *req.StatusID)
		argIndex++
	}

	if req.Date != nil && *req.Date != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("DATE(ut.date) = $%d", argIndex))
		args = append(args, *req.Date)
		argIndex++
	}

	if req.BankingID != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.banking_id = $%d", argIndex))
		args = append(args, *req.BankingID)
		argIndex++
	}

	if req.StartAmount != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.credit_amount >= $%d", argIndex))
		args = append(args, *req.StartAmount)
		argIndex++
	}

	if req.EndAmount != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.credit_amount <= $%d", argIndex))
		args = append(args, *req.EndAmount)
		argIndex++
	}

	if req.Amount != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("ut.credit_amount = $%d", argIndex))
		args = append(args, *req.Amount)
		argIndex++
	}

	// Add additional where conditions
	if len(whereConditions) > 0 {
		baseQuery += " AND " + strings.Join(whereConditions, " AND ")
	}

	return r.executePaginatedQuery(ctx, baseQuery, args, req.Page, req.Limit)
}
