package postgres

import (
	"blacking-api/internal/domain/crypto_deposit"
	"blacking-api/internal/domain/payment_gateway_transaction"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type CryptoDepositRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewCryptoDepositRepository(pool *pgxpool.Pool, logger logger.Logger) *CryptoDepositRepository {
	return &CryptoDepositRepository{
		pool:   pool,
		logger: logger,
	}
}

// GetSupportedNetworks retrieves all active blockchain networks
func (r *CryptoDepositRepository) GetSupportedNetworks(ctx context.Context) ([]*crypto_deposit.BlockchainNetwork, error) {
	query := `
		SELECT id, chain_id, network_name, network_type, rpc_url, explorer_url, 
		       native_currency_symbol, is_active, created_at, updated_at
		FROM blockchain_networks 
		WHERE is_active = true
		ORDER BY chain_id
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to get supported networks")
		return nil, errors.NewDatabaseError("failed to get supported networks")
	}
	defer rows.Close()

	var networks []*crypto_deposit.BlockchainNetwork
	for rows.Next() {
		network := &crypto_deposit.BlockchainNetwork{}
		err := rows.Scan(
			&network.ID, &network.ChainID, &network.NetworkName, &network.NetworkType,
			&network.RPCUrl, &network.ExplorerUrl, &network.NativeCurrencySymbol,
			&network.IsActive, &network.CreatedAt, &network.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan blockchain network")
			return nil, errors.NewDatabaseError("failed to scan blockchain network")
		}
		networks = append(networks, network)
	}

	return networks, nil
}

// GetNetworkByChainID retrieves a specific network by chain ID
func (r *CryptoDepositRepository) GetNetworkByChainID(ctx context.Context, chainID int) (*crypto_deposit.BlockchainNetwork, error) {
	query := `
		SELECT id, chain_id, network_name, network_type, rpc_url, explorer_url,
		       native_currency_symbol, is_active, created_at, updated_at
		FROM blockchain_networks 
		WHERE chain_id = $1 AND is_active = true
	`

	network := &crypto_deposit.BlockchainNetwork{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, chainID).Scan(
		&network.ID, &network.ChainID, &network.NetworkName, &network.NetworkType,
		&network.RPCUrl, &network.ExplorerUrl, &network.NativeCurrencySymbol,
		&network.IsActive, &network.CreatedAt, &network.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("blockchain network not found")
		}
		r.logger.WithError(err).Error("failed to get network by chain ID")
		return nil, errors.NewDatabaseError("failed to get network by chain ID")
	}

	return network, nil
}

// GetBackendWallets retrieves all active backend wallets
func (r *CryptoDepositRepository) GetBackendWallets(ctx context.Context) ([]*crypto_deposit.BackendWallet, error) {
	query := `
		SELECT id, wallet_address, chain_id, wallet_name, current_eth_balance,
		       current_token_balance, minimum_eth_threshold, maximum_token_threshold,
		       last_balance_check, is_active, created_at, updated_at
		FROM backend_wallets 
		WHERE is_active = true
		ORDER BY chain_id, wallet_address
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to get backend wallets")
		return nil, errors.NewDatabaseError("failed to get backend wallets")
	}
	defer rows.Close()

	var wallets []*crypto_deposit.BackendWallet
	for rows.Next() {
		wallet := &crypto_deposit.BackendWallet{}
		err := rows.Scan(
			&wallet.ID, &wallet.WalletAddress, &wallet.ChainID, &wallet.WalletName,
			&wallet.CurrentETHBalance, &wallet.CurrentTokenBalance, &wallet.MinimumETHThreshold,
			&wallet.MaximumTokenThreshold, &wallet.LastBalanceCheck, &wallet.IsActive,
			&wallet.CreatedAt, &wallet.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan backend wallet")
			return nil, errors.NewDatabaseError("failed to scan backend wallet")
		}
		wallets = append(wallets, wallet)
	}

	return wallets, nil
}

// GetBackendWalletByChainID retrieves the active backend wallet for a specific chain
func (r *CryptoDepositRepository) GetBackendWalletByChainID(ctx context.Context, chainID int) (*crypto_deposit.BackendWallet, error) {
	query := `
		SELECT id, wallet_address, chain_id, wallet_name, current_eth_balance,
		       current_token_balance, minimum_eth_threshold, maximum_token_threshold,
		       last_balance_check, is_active, created_at, updated_at
		FROM backend_wallets 
		WHERE chain_id = $1 AND is_active = true
		LIMIT 1
	`

	wallet := &crypto_deposit.BackendWallet{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, chainID).Scan(
		&wallet.ID, &wallet.WalletAddress, &wallet.ChainID, &wallet.WalletName,
		&wallet.CurrentETHBalance, &wallet.CurrentTokenBalance, &wallet.MinimumETHThreshold,
		&wallet.MaximumTokenThreshold, &wallet.LastBalanceCheck, &wallet.IsActive,
		&wallet.CreatedAt, &wallet.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("backend wallet not found for chain")
		}
		r.logger.WithError(err).Error("failed to get backend wallet by chain ID")
		return nil, errors.NewDatabaseError("failed to get backend wallet by chain ID")
	}

	return wallet, nil
}

// UpdateBackendWalletBalance updates the balance of a backend wallet
func (r *CryptoDepositRepository) UpdateBackendWalletBalance(ctx context.Context, walletAddress string, ethBalance, tokenBalance float64) error {
	query := `
		UPDATE backend_wallets 
		SET current_eth_balance = $2, current_token_balance = $3, 
		    last_balance_check = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
		WHERE wallet_address = $1
	`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query, walletAddress, ethBalance, tokenBalance)
	if err != nil {
		r.logger.WithError(err).Error("failed to update backend wallet balance")
		return errors.NewDatabaseError("failed to update backend wallet balance")
	}

	return nil
}

// GetChainTokenByNetworkAndContract retrieves token by network ID and contract
func (r *CryptoDepositRepository) GetChainTokenByNetworkAndContract(ctx context.Context, networkID int64, contract string) (*crypto_deposit.ChainToken, error) {
	query := `
		SELECT id, blockchain_network_id, token_contract, token_symbol, 
		       token_decimals, rate_to_thb, is_active, created_at, updated_at
		FROM chain_tokens 
		WHERE blockchain_network_id = $1 AND token_contract = $2 AND is_active = true
	`

	token := &crypto_deposit.ChainToken{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, networkID, contract).Scan(
		&token.ID, &token.BlockchainNetworkID, &token.TokenContract,
		&token.TokenSymbol, &token.TokenDecimals, &token.RateToTHB,
		&token.IsActive, &token.CreatedAt, &token.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("chain token not found")
		}
		r.logger.WithError(err).Error("failed to get chain token by network and contract")
		return nil, errors.NewDatabaseError("failed to get chain token")
	}

	return token, nil
}

// GetChainTokenByChainIDAndContract retrieves token by chain ID and contract (convenience method)
func (r *CryptoDepositRepository) GetChainTokenByChainIDAndContract(ctx context.Context, chainID int, contract string) (*crypto_deposit.ChainToken, error) {
	query := `
		SELECT ct.id, ct.blockchain_network_id, ct.token_contract, ct.token_symbol, 
		       ct.token_decimals, ct.rate_to_thb, ct.is_active, ct.created_at, ct.updated_at
		FROM chain_tokens ct
		INNER JOIN blockchain_networks bn ON ct.blockchain_network_id = bn.id
		WHERE bn.chain_id = $1 AND ct.token_contract = $2 AND ct.is_active = true
	`

	token := &crypto_deposit.ChainToken{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, chainID, contract).Scan(
		&token.ID, &token.BlockchainNetworkID, &token.TokenContract,
		&token.TokenSymbol, &token.TokenDecimals, &token.RateToTHB,
		&token.IsActive, &token.CreatedAt, &token.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("chain token not found for chain and contract")
		}
		r.logger.WithError(err).Error("failed to get chain token by chain ID and contract")
		return nil, errors.NewDatabaseError("failed to get chain token")
	}

	return token, nil
}

// ListChainTokens retrieves all active chain tokens
func (r *CryptoDepositRepository) ListChainTokens(ctx context.Context) ([]*crypto_deposit.ChainToken, error) {
	query := `
		SELECT ct.id, ct.blockchain_network_id, ct.token_contract, ct.token_symbol,
		       ct.token_decimals, ct.rate_to_thb, ct.is_active, ct.created_at, ct.updated_at,
		       bn.chain_id, bn.network_name
		FROM chain_tokens ct
		INNER JOIN blockchain_networks bn ON ct.blockchain_network_id = bn.id
		WHERE ct.is_active = true
		ORDER BY bn.chain_id, ct.token_symbol
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to list chain tokens")
		return nil, errors.NewDatabaseError("failed to list chain tokens")
	}
	defer rows.Close()

	var tokens []*crypto_deposit.ChainToken
	for rows.Next() {
		token := &crypto_deposit.ChainToken{}
		var chainID int
		var networkName string

		err := rows.Scan(
			&token.ID, &token.BlockchainNetworkID, &token.TokenContract,
			&token.TokenSymbol, &token.TokenDecimals, &token.RateToTHB,
			&token.IsActive, &token.CreatedAt, &token.UpdatedAt,
			&chainID, &networkName,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan chain token")
			return nil, errors.NewDatabaseError("failed to scan chain token")
		}

		tokens = append(tokens, token)
	}

	return tokens, nil
}

// UpdateChainTokenRate updates the conversion rate for a chain token
func (r *CryptoDepositRepository) UpdateChainTokenRate(ctx context.Context, id int64, rate float64) error {
	query := `
		UPDATE chain_tokens 
		SET rate_to_thb = $2, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1 AND is_active = true
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id, rate)
	if err != nil {
		r.logger.WithError(err).Error("failed to update chain token rate")
		return errors.NewDatabaseError("failed to update chain token rate")
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return errors.NewNotFoundError("chain token not found or inactive")
	}

	r.logger.WithFields(map[string]interface{}{
		"token_id": id,
		"new_rate": rate,
	}).Info("chain token rate updated successfully")

	return nil
}

// CreateDepositLog creates a new crypto deposit log entry
func (r *CryptoDepositRepository) CreateDepositLog(ctx context.Context, log *crypto_deposit.CryptoDepositLog) error {
	eventDataJSON, _ := json.Marshal(log.EventData)

	query := `
		INSERT INTO crypto_deposit_logs (
			transaction_id, step_number, event_type, transaction_hash, block_number,
			gas_used, gas_price_gwei, event_data, error_details
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		RETURNING id, timestamp
	`

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		log.TransactionID, log.StepNumber, log.EventType, log.TransactionHash,
		log.BlockNumber, log.GasUsed, log.GasPriceGwei, eventDataJSON, log.ErrorDetails,
	).Scan(&log.ID, &log.Timestamp)

	if err != nil {
		r.logger.WithError(err).Error("failed to create crypto deposit log")
		return errors.NewDatabaseError("failed to create crypto deposit log")
	}

	return nil
}

// GetDepositLogs retrieves all logs for a specific transaction
func (r *CryptoDepositRepository) GetDepositLogs(ctx context.Context, transactionID string) ([]*crypto_deposit.CryptoDepositLog, error) {
	query := `
		SELECT id, transaction_id, step_number, event_type, transaction_hash,
		       block_number, gas_used, gas_price_gwei, event_data, error_details, timestamp
		FROM crypto_deposit_logs 
		WHERE transaction_id = $1
		ORDER BY timestamp ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, transactionID)
	if err != nil {
		r.logger.WithError(err).Error("failed to get deposit logs")
		return nil, errors.NewDatabaseError("failed to get deposit logs")
	}
	defer rows.Close()

	var logs []*crypto_deposit.CryptoDepositLog
	for rows.Next() {
		log := &crypto_deposit.CryptoDepositLog{}
		var eventDataJSON []byte

		err := rows.Scan(
			&log.ID, &log.TransactionID, &log.StepNumber, &log.EventType,
			&log.TransactionHash, &log.BlockNumber, &log.GasUsed, &log.GasPriceGwei,
			&eventDataJSON, &log.ErrorDetails, &log.Timestamp,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan crypto deposit log")
			return nil, errors.NewDatabaseError("failed to scan crypto deposit log")
		}

		if len(eventDataJSON) > 0 {
			json.Unmarshal(eventDataJSON, &log.EventData)
		}

		logs = append(logs, log)
	}

	return logs, nil
}

// GetCryptoDepositByTransactionID retrieves detailed crypto deposit information
func (r *CryptoDepositRepository) GetCryptoDepositByTransactionID(ctx context.Context, transactionID string) (*crypto_deposit.CryptoDepositResponse, error) {
	query := `
		SELECT 
			pgt.id, pgt.transaction_id, pgt.internal_reference, pgt.provider,
			pgt.transaction_type, pgt.amount, pgt.currency, pgt.status,
			pgt.customer_username, pgt.blockchain_data, pgt.created_at,
			pgt.updated_at, pgt.completed_at
		FROM payment_gateway_transactions pgt
		WHERE pgt.transaction_id = $1 AND pgt.provider = 'BLOCKCHAIN'
	`

	var blockchainDataJSON []byte
	transaction := &payment_gateway_transaction.PaymentGatewayTransaction{}

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, transactionID).Scan(
		&transaction.ID, &transaction.TransactionID, &transaction.InternalReference,
		&transaction.Provider, &transaction.TransactionType, &transaction.Amount,
		&transaction.Currency, &transaction.Status, &transaction.CustomerUsername,
		&blockchainDataJSON, &transaction.CreatedAt, &transaction.UpdatedAt,
		&transaction.CompletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("crypto deposit transaction not found")
		}
		r.logger.WithError(err).Error("failed to get crypto deposit by transaction ID")
		return nil, errors.NewDatabaseError("failed to get crypto deposit by transaction ID")
	}

	// Parse blockchain data
	var blockchainData map[string]interface{}
	if len(blockchainDataJSON) > 0 {
		json.Unmarshal(blockchainDataJSON, &blockchainData)
	}

	// Convert to response format
	response := r.convertToDepositResponse(transaction, blockchainData)
	return response, nil
}

// ListCryptoDeposits retrieves a paginated list of crypto deposits
func (r *CryptoDepositRepository) ListCryptoDeposits(ctx context.Context, filters *crypto_deposit.CryptoDepositFilters) ([]*crypto_deposit.CryptoDepositSummary, int64, error) {
	// Build WHERE clause
	whereConditions := []string{"pgt.provider = 'BLOCKCHAIN'"}
	args := []interface{}{}
	argIndex := 1

	if filters.Status != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("pgt.status = $%d", argIndex))
		args = append(args, filters.Status)
		argIndex++
	}

	if filters.Currency != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("pgt.currency = $%d", argIndex))
		args = append(args, filters.Currency)
		argIndex++
	}

	if filters.CustomerUsername != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("pgt.customer_username = $%d", argIndex))
		args = append(args, filters.CustomerUsername)
		argIndex++
	}

	if filters.ChainID != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("pgt.blockchain_data->>'chain_id' = $%d", argIndex))
		args = append(args, fmt.Sprintf("%d", *filters.ChainID))
		argIndex++
	}

	if filters.StartDate != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("pgt.created_at >= $%d", argIndex))
		args = append(args, *filters.StartDate)
		argIndex++
	}

	if filters.EndDate != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("pgt.created_at <= $%d", argIndex))
		args = append(args, *filters.EndDate)
		argIndex++
	}

	whereClause := strings.Join(whereConditions, " AND ")

	// Count total records
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*) 
		FROM payment_gateway_transactions pgt
		WHERE %s
	`, whereClause)

	var total int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.WithError(err).Error("failed to count crypto deposits")
		return nil, 0, errors.NewDatabaseError("failed to count crypto deposits")
	}

	// Get paginated results
	offset := (filters.Page - 1) * filters.PageSize
	dataQuery := fmt.Sprintf(`
		SELECT 
			pgt.id, pgt.transaction_id, pgt.amount, pgt.currency, pgt.status,
			pgt.customer_username, pgt.blockchain_data, pgt.created_at, pgt.completed_at
		FROM payment_gateway_transactions pgt
		WHERE %s
		ORDER BY pgt.%s %s
		LIMIT $%d OFFSET $%d
	`, whereClause, filters.SortBy, strings.ToUpper(filters.SortOrder), argIndex, argIndex+1)

	args = append(args, filters.PageSize, offset)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, dataQuery, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list crypto deposits")
		return nil, 0, errors.NewDatabaseError("failed to list crypto deposits")
	}
	defer rows.Close()

	var deposits []*crypto_deposit.CryptoDepositSummary
	for rows.Next() {
		var blockchainDataJSON []byte
		deposit := &crypto_deposit.CryptoDepositSummary{}

		err := rows.Scan(
			&deposit.ID, &deposit.TransactionID, &deposit.Amount, &deposit.Currency,
			&deposit.OverallStatus, &deposit.CustomerUsername, &blockchainDataJSON,
			&deposit.CreatedAt, &deposit.CompletedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan crypto deposit summary")
			return nil, 0, errors.NewDatabaseError("failed to scan crypto deposit summary")
		}

		// Parse blockchain data for additional info
		if len(blockchainDataJSON) > 0 {
			var blockchainData map[string]interface{}
			json.Unmarshal(blockchainDataJSON, &blockchainData)

			if chainID, ok := blockchainData["chain_id"].(float64); ok {
				deposit.ChainID = int(chainID)
			}
			if networkName, ok := blockchainData["network_name"].(string); ok {
				deposit.NetworkName = networkName
			}

			// Extract step transaction hashes
			if step1, ok := blockchainData["step1"].(map[string]interface{}); ok {
				if hash, ok := step1["transaction_hash"].(string); ok {
					deposit.Step1TransactionHash = &hash
				}
			}
			if step2, ok := blockchainData["step2"].(map[string]interface{}); ok {
				if hash, ok := step2["transaction_hash"].(string); ok {
					deposit.Step2TransactionHash = &hash
				}
				if amount, ok := step2["actual_amount"].(float64); ok {
					deposit.FinalReceivedAmount = &amount
				}
			}

			// Calculate completion time
			if step1InitiatedStr, ok := blockchainData["step1"].(map[string]interface{})["initiated_at"].(string); ok {
				if step2ConfirmedStr, ok := blockchainData["step2"].(map[string]interface{})["confirmed_at"].(string); ok {
					step1Initiated, _ := time.Parse(time.RFC3339, step1InitiatedStr)
					step2Confirmed, _ := time.Parse(time.RFC3339, step2ConfirmedStr)
					completionTime := int(step2Confirmed.Sub(step1Initiated).Seconds())
					deposit.CompletionTimeSeconds = &completionTime
				}
			}
		}

		deposits = append(deposits, deposit)
	}

	return deposits, total, nil
}

// UpdateCryptoDepositStep1 updates Step 1 information for a crypto deposit
func (r *CryptoDepositRepository) UpdateCryptoDepositStep1(ctx context.Context, transactionID string, req *crypto_deposit.UpdateStep1Request) error {
	// First get the current transaction
	getCurrentQuery := `
		SELECT id, blockchain_data, status
		FROM payment_gateway_transactions 
		WHERE transaction_id = $1 AND provider = 'BLOCKCHAIN'
	`

	var currentID int64
	var currentBlockchainDataJSON []byte
	var currentStatus string

	err := dbutil.QueryRowWithSchema(ctx, r.pool, getCurrentQuery, transactionID).Scan(
		&currentID, &currentBlockchainDataJSON, &currentStatus,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return errors.NewNotFoundError("crypto deposit transaction not found")
		}
		return errors.NewDatabaseError("failed to get current transaction")
	}

	// Parse existing blockchain data
	var blockchainData map[string]interface{}
	if len(currentBlockchainDataJSON) > 0 {
		json.Unmarshal(currentBlockchainDataJSON, &blockchainData)
	} else {
		blockchainData = make(map[string]interface{})
	}

	// Update Step 1 data
	step1Data := map[string]interface{}{
		"transaction_hash": req.TransactionHash,
		"block_number":     req.BlockNumber,
		"gas_used":         req.GasUsed,
		"actual_amount":    req.ActualAmount,
		"status":           req.Status,
		"initiated_at":     req.InitiatedAt.Format(time.RFC3339),
	}
	if req.ConfirmedAt != nil {
		step1Data["confirmed_at"] = req.ConfirmedAt.Format(time.RFC3339)
	}
	if req.ErrorMessage != nil {
		step1Data["error_message"] = *req.ErrorMessage
	}

	blockchainData["step1"] = step1Data

	// Determine new overall status
	var newStatus string
	switch req.Status {
	case crypto_deposit.StepStatusPending:
		newStatus = crypto_deposit.StatusStep1Pending
	case crypto_deposit.StepStatusConfirmed:
		newStatus = crypto_deposit.StatusStep1Completed
	case crypto_deposit.StepStatusFailed, crypto_deposit.StepStatusReverted:
		newStatus = crypto_deposit.StatusFailed
	default:
		newStatus = currentStatus
	}

	// Update transaction
	blockchainDataJSON, _ := json.Marshal(blockchainData)

	updateQuery := `
		UPDATE payment_gateway_transactions 
		SET blockchain_data = $2, status = $3, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
	`

	_, err = dbutil.ExecWithSchema(ctx, r.pool, updateQuery, currentID, blockchainDataJSON, newStatus)
	if err != nil {
		r.logger.WithError(err).Error("failed to update crypto deposit step1")
		return errors.NewDatabaseError("failed to update crypto deposit step1")
	}

	// Create log entry
	logEntry := &crypto_deposit.CryptoDepositLog{
		TransactionID:   transactionID,
		StepNumber:      1,
		EventType:       req.Status,
		TransactionHash: &req.TransactionHash,
		BlockNumber:     req.BlockNumber,
		GasUsed:         req.GasUsed,
		EventData: map[string]interface{}{
			"actual_amount": req.ActualAmount,
			"status":        req.Status,
		},
		ErrorDetails: req.ErrorMessage,
	}
	if err := r.CreateDepositLog(ctx, logEntry); err != nil {
		r.logger.WithError(err).Error("failed to create step1 crypto deposit log")
		// Don't fail the whole operation for logging errors
	}

	return nil
}

// UpdateCryptoDepositStep2 updates Step 2 information for a crypto deposit
func (r *CryptoDepositRepository) UpdateCryptoDepositStep2(ctx context.Context, transactionID string, req *crypto_deposit.UpdateStep2Request) error {
	// First get the current transaction
	getCurrentQuery := `
		SELECT id, blockchain_data, status
		FROM payment_gateway_transactions 
		WHERE transaction_id = $1 AND provider = 'BLOCKCHAIN'
	`

	var currentID int64
	var currentBlockchainDataJSON []byte
	var currentStatus string

	err := dbutil.QueryRowWithSchema(ctx, r.pool, getCurrentQuery, transactionID).Scan(
		&currentID, &currentBlockchainDataJSON, &currentStatus,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return errors.NewNotFoundError("crypto deposit transaction not found")
		}
		return errors.NewDatabaseError("failed to get current transaction")
	}

	// Check if Step 1 is completed
	if currentStatus != crypto_deposit.StatusStep1Completed {
		return errors.NewValidationError("step 1 must be completed before updating step 2")
	}

	// Parse existing blockchain data
	var blockchainData map[string]interface{}
	if len(currentBlockchainDataJSON) > 0 {
		json.Unmarshal(currentBlockchainDataJSON, &blockchainData)
	} else {
		blockchainData = make(map[string]interface{})
	}

	// Update Step 2 data
	step2Data := map[string]interface{}{
		"transaction_hash": req.TransactionHash,
		"block_number":     req.BlockNumber,
		"gas_used":         req.GasUsed,
		"gas_fee_eth":      req.GasFeeETH,
		"actual_amount":    req.ActualAmount,
		"status":           req.Status,
		"initiated_at":     req.InitiatedAt.Format(time.RFC3339),
	}
	if req.ConfirmedAt != nil {
		step2Data["confirmed_at"] = req.ConfirmedAt.Format(time.RFC3339)
	}
	if req.ErrorMessage != nil {
		step2Data["error_message"] = *req.ErrorMessage
	}
	if req.ThirdwebTransactionID != nil {
		step2Data["thirdweb_transaction_id"] = *req.ThirdwebTransactionID
	}

	blockchainData["step2"] = step2Data

	// Calculate completion time if both steps are confirmed
	if req.Status == crypto_deposit.StepStatusConfirmed && req.ConfirmedAt != nil {
		if step1, ok := blockchainData["step1"].(map[string]interface{}); ok {
			if step1InitiatedStr, ok := step1["initiated_at"].(string); ok {
				step1Initiated, _ := time.Parse(time.RFC3339, step1InitiatedStr)
				completionTime := int(req.ConfirmedAt.Sub(step1Initiated).Seconds())
				blockchainData["completion_time_seconds"] = completionTime
			}
		}
	}

	// Determine new overall status
	var newStatus string
	var completedAt *time.Time
	switch req.Status {
	case crypto_deposit.StepStatusPending:
		newStatus = crypto_deposit.StatusStep2Pending
	case crypto_deposit.StepStatusConfirmed:
		newStatus = crypto_deposit.StatusCompleted
		completedAt = req.ConfirmedAt
	case crypto_deposit.StepStatusFailed, crypto_deposit.StepStatusReverted:
		newStatus = crypto_deposit.StatusFailed
	default:
		newStatus = currentStatus
	}

	// Update transaction
	blockchainDataJSON, _ := json.Marshal(blockchainData)

	var updateQuery string
	var args []interface{}

	if completedAt != nil {
		updateQuery = `
			UPDATE payment_gateway_transactions 
			SET blockchain_data = $2, status = $3, completed_at = $4, updated_at = CURRENT_TIMESTAMP
			WHERE id = $1
		`
		args = []interface{}{currentID, blockchainDataJSON, newStatus, completedAt}
	} else {
		updateQuery = `
			UPDATE payment_gateway_transactions 
			SET blockchain_data = $2, status = $3, updated_at = CURRENT_TIMESTAMP
			WHERE id = $1
		`
		args = []interface{}{currentID, blockchainDataJSON, newStatus}
	}

	_, err = dbutil.ExecWithSchema(ctx, r.pool, updateQuery, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to update crypto deposit step2")
		return errors.NewDatabaseError("failed to update crypto deposit step2")
	}

	// Create log entry
	logEntry := &crypto_deposit.CryptoDepositLog{
		TransactionID:   transactionID,
		StepNumber:      2,
		EventType:       req.Status,
		TransactionHash: &req.TransactionHash,
		BlockNumber:     req.BlockNumber,
		GasUsed:         req.GasUsed,
		EventData: map[string]interface{}{
			"actual_amount":           req.ActualAmount,
			"gas_fee_eth":             req.GasFeeETH,
			"status":                  req.Status,
			"thirdweb_transaction_id": req.ThirdwebTransactionID,
		},
		ErrorDetails: req.ErrorMessage,
	}
	if err := r.CreateDepositLog(ctx, logEntry); err != nil {
		r.logger.WithError(err).Error("failed to create step2 crypto deposit log")
		// Don't fail the whole operation for logging errors
	}

	return nil
}

// Helper function to convert payment gateway transaction to crypto deposit response
func (r *CryptoDepositRepository) convertToDepositResponse(transaction *payment_gateway_transaction.PaymentGatewayTransaction, blockchainData map[string]interface{}) *crypto_deposit.CryptoDepositResponse {
	response := &crypto_deposit.CryptoDepositResponse{
		ID:                int64(transaction.ID),
		TransactionID:     transaction.TransactionID,
		InternalReference: transaction.InternalReference,
		Provider:          transaction.Provider,
		TransactionType:   transaction.TransactionType,
		Amount:            transaction.Amount,
		Currency:          transaction.Currency,
		OverallStatus:     transaction.Status,
		CustomerUsername:  transaction.CustomerUsername,
		Timestamps: crypto_deposit.TransactionTimestamps{
			CreatedAt:   transaction.CreatedAt,
			UpdatedAt:   transaction.UpdatedAt,
			CompletedAt: transaction.CompletedAt,
		},
	}

	if blockchainData != nil {
		// Network info
		if chainID, ok := blockchainData["chain_id"].(float64); ok {
			response.Network.ChainID = int(chainID)
		}
		if networkName, ok := blockchainData["network_name"].(string); ok {
			response.Network.NetworkName = networkName
		}
		if tokenContract, ok := blockchainData["token_contract"].(string); ok {
			response.Network.TokenContract = tokenContract
		}
		if tokenSymbol, ok := blockchainData["token_symbol"].(string); ok {
			response.Network.TokenSymbol = tokenSymbol
		}

		// Wallet info
		if wallets, ok := blockchainData["wallets"].(map[string]interface{}); ok {
			if userWallet, ok := wallets["user_wallet"].(string); ok {
				response.Wallets.UserWallet = userWallet
			}
			if backendWallet, ok := wallets["backend_wallet"].(string); ok {
				response.Wallets.BackendWallet = backendWallet
			}
			if finalRecipient, ok := wallets["final_recipient"].(string); ok {
				response.Wallets.FinalRecipient = finalRecipient
			}
		}

		// Step 1 info
		if step1, ok := blockchainData["step1"].(map[string]interface{}); ok {
			response.Step1 = &crypto_deposit.StepInfo{}
			if hash, ok := step1["transaction_hash"].(string); ok {
				response.Step1.TransactionHash = &hash
			}
			if blockNum, ok := step1["block_number"].(float64); ok {
				blockNumber := int64(blockNum)
				response.Step1.BlockNumber = &blockNumber
			}
			if gasUsed, ok := step1["gas_used"].(float64); ok {
				gas := int64(gasUsed)
				response.Step1.GasUsed = &gas
			}
			if amount, ok := step1["actual_amount"].(float64); ok {
				response.Step1.ActualAmount = &amount
			}
			if status, ok := step1["status"].(string); ok {
				response.Step1.Status = &status
			}
			if initiatedStr, ok := step1["initiated_at"].(string); ok {
				if initiated, err := time.Parse(time.RFC3339, initiatedStr); err == nil {
					response.Step1.InitiatedAt = &initiated
				}
			}
			if confirmedStr, ok := step1["confirmed_at"].(string); ok {
				if confirmed, err := time.Parse(time.RFC3339, confirmedStr); err == nil {
					response.Step1.ConfirmedAt = &confirmed
				}
			}
		}

		// Step 2 info
		if step2, ok := blockchainData["step2"].(map[string]interface{}); ok {
			response.Step2 = &crypto_deposit.StepInfo{}
			if hash, ok := step2["transaction_hash"].(string); ok {
				response.Step2.TransactionHash = &hash
			}
			if blockNum, ok := step2["block_number"].(float64); ok {
				blockNumber := int64(blockNum)
				response.Step2.BlockNumber = &blockNumber
			}
			if gasUsed, ok := step2["gas_used"].(float64); ok {
				gas := int64(gasUsed)
				response.Step2.GasUsed = &gas
			}
			if gasFee, ok := step2["gas_fee_eth"].(float64); ok {
				response.Step2.GasFeeETH = &gasFee
			}
			if amount, ok := step2["actual_amount"].(float64); ok {
				response.Step2.ActualAmount = &amount
				response.Summary.FinalReceivedAmount = &amount
			}
			if status, ok := step2["status"].(string); ok {
				response.Step2.Status = &status
			}
			if initiatedStr, ok := step2["initiated_at"].(string); ok {
				if initiated, err := time.Parse(time.RFC3339, initiatedStr); err == nil {
					response.Step2.InitiatedAt = &initiated
				}
			}
			if confirmedStr, ok := step2["confirmed_at"].(string); ok {
				if confirmed, err := time.Parse(time.RFC3339, confirmedStr); err == nil {
					response.Step2.ConfirmedAt = &confirmed
				}
			}
			if txID, ok := step2["thirdweb_transaction_id"].(string); ok {
				response.Step2.ThirdwebTransactionID = &txID
			}
		}

		// Summary info
		if completionTime, ok := blockchainData["completion_time_seconds"].(float64); ok {
			time := int(completionTime)
			response.Summary.CompletionTimeSeconds = &time
		}

		// Calculate efficiency and total gas cost
		if response.Step2 != nil && response.Step2.GasFeeETH != nil {
			response.Summary.TotalGasCostETH = response.Step2.GasFeeETH
		}

		if response.Summary.FinalReceivedAmount != nil && *response.Summary.FinalReceivedAmount > 0 {
			efficiency := (*response.Summary.FinalReceivedAmount / response.Amount) * 100
			response.Summary.EfficiencyPercentage = &efficiency
		}
	}

	return response
}
