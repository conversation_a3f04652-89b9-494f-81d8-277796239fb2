package postgres

import (
	"blacking-api/internal/domain/admin_audit_log"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"fmt"
	"strings"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
)

type adminAuditLogRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewAdminAuditLogRepository creates a new admin audit log repository
func NewAdminAuditLogRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.AdminAuditLogRepository {
	return &adminAuditLogRepository{
		pool:   pool,
		logger: logger,
	}
}

// Create creates a new admin audit log entry
func (r *adminAuditLogRepository) Create(ctx context.Context, auditLog *admin_audit_log.AdminAuditLog) error {
	log := r.logger.WithContext(ctx).WithField("operation", "Create")

	// Limit request body size to prevent timeout
	var requestBody *string
	if auditLog.RequestBody != nil {
		body := *auditLog.RequestBody
		if len(body) > 5000 { // Limit to 5KB
			truncated := body[:5000] + "... [TRUNCATED]"
			requestBody = &truncated
		} else {
			requestBody = auditLog.RequestBody
		}
	}

	query := `
		INSERT INTO admin_audit_log (
			user_id, username, method, path, request_body, 
			response_status, ip_address, user_agent, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) 
		RETURNING id`

	log.WithFields(map[string]interface{}{
		"user_id":         auditLog.UserID,
		"username":        auditLog.Username,
		"method":          auditLog.Method,
		"path":            auditLog.Path,
		"request_body_len": func() int {
			if requestBody != nil {
				return len(*requestBody)
			}
			return 0
		}(),
		"response_status": auditLog.ResponseStatus,
	}).Debug("attempting to create admin audit log")

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		auditLog.UserID,
		auditLog.Username,
		auditLog.Method,
		auditLog.Path,
		requestBody,
		auditLog.ResponseStatus,
		auditLog.IPAddress,
		auditLog.UserAgent,
		auditLog.CreatedAt,
	).Scan(&auditLog.ID)

	if err != nil {
		// Add detailed error logging
		log.WithFields(map[string]interface{}{
			"error_type":      fmt.Sprintf("%T", err),
			"error_message":   err.Error(),
			"user_id":         auditLog.UserID,
			"username":        auditLog.Username,
			"method":          auditLog.Method,
			"path":            auditLog.Path,
			"context_error":   ctx.Err(),
		}).Error("detailed admin audit log creation error")

		if pgErr, ok := err.(*pgconn.PgError); ok {
			log.WithError(err).WithFields(map[string]interface{}{
				"pg_code":         pgErr.Code,
				"pg_message":      pgErr.Message,
				"pg_detail":       pgErr.Detail,
				"pg_hint":         pgErr.Hint,
				"pg_position":     pgErr.Position,
				"pg_table_name":   pgErr.TableName,
				"pg_column_name":  pgErr.ColumnName,
				"pg_constraint_name": pgErr.ConstraintName,
			}).Error("postgres specific error creating admin audit log")
		} else if err == context.Canceled {
			log.WithError(err).Error("context was canceled while creating admin audit log")
		} else if err == context.DeadlineExceeded {
			log.WithError(err).Error("context deadline exceeded while creating admin audit log")
		} else {
			log.WithError(err).Error("unknown error type while creating admin audit log")
		}
		return errors.NewDatabaseError("failed to create admin audit log")
	}

	log.WithField("audit_log_id", auditLog.ID).Info("admin audit log created successfully")
	return nil
}

// GetByID retrieves an admin audit log by ID
func (r *adminAuditLogRepository) GetByID(ctx context.Context, id int) (*admin_audit_log.AdminAuditLog, error) {
	log := r.logger.WithContext(ctx).WithField("operation", "GetByID").WithField("id", id)

	query := `
		SELECT id, user_id, username, method, path, request_body, 
		       response_status, ip_address, user_agent, created_at
		FROM admin_audit_log 
		WHERE id = $1`

	var auditLog admin_audit_log.AdminAuditLog
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&auditLog.ID,
		&auditLog.UserID,
		&auditLog.Username,
		&auditLog.Method,
		&auditLog.Path,
		&auditLog.RequestBody,
		&auditLog.ResponseStatus,
		&auditLog.IPAddress,
		&auditLog.UserAgent,
		&auditLog.CreatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			log.Warn("admin audit log not found")
			return nil, errors.NewNotFoundError("admin audit log not found")
		}
		log.WithError(err).Error("failed to get admin audit log by ID")
		return nil, errors.NewDatabaseError("failed to get admin audit log")
	}

	log.Info("admin audit log retrieved successfully")
	return &auditLog, nil
}

// List retrieves admin audit logs with pagination and filters
func (r *adminAuditLogRepository) List(ctx context.Context, limit, offset int, filter *admin_audit_log.AdminAuditLogFilter) ([]*admin_audit_log.AdminAuditLog, error) {
	log := r.logger.WithContext(ctx).WithField("operation", "List")

	// Build query with filters
	queryBuilder := strings.Builder{}
	queryBuilder.WriteString(`
		SELECT id, user_id, username, method, path, request_body, 
		       response_status, ip_address, user_agent, created_at
		FROM admin_audit_log 
		WHERE 1=1`)

	args := []interface{}{}
	argIndex := 1

	// Apply filters
	if filter != nil {
		if filter.UserID != nil {
			queryBuilder.WriteString(fmt.Sprintf(" AND user_id = $%d", argIndex))
			args = append(args, *filter.UserID)
			argIndex++
		}

		if filter.Username != nil && *filter.Username != "" {
			queryBuilder.WriteString(fmt.Sprintf(" AND username ILIKE $%d", argIndex))
			args = append(args, "%"+*filter.Username+"%")
			argIndex++
		}

		if filter.Method != nil && *filter.Method != "" {
			queryBuilder.WriteString(fmt.Sprintf(" AND method = $%d", argIndex))
			args = append(args, *filter.Method)
			argIndex++
		}

		if filter.Path != nil && *filter.Path != "" {
			queryBuilder.WriteString(fmt.Sprintf(" AND path ILIKE $%d", argIndex))
			args = append(args, "%"+*filter.Path+"%")
			argIndex++
		}

		if filter.DateFrom != nil && *filter.DateFrom != "" {
			queryBuilder.WriteString(fmt.Sprintf(" AND created_at >= $%d::date", argIndex))
			args = append(args, *filter.DateFrom)
			argIndex++
		}

		if filter.DateTo != nil && *filter.DateTo != "" {
			queryBuilder.WriteString(fmt.Sprintf(" AND created_at < $%d::date + INTERVAL '1 day'", argIndex))
			args = append(args, *filter.DateTo)
			argIndex++
		}
	}

	// Add ordering and pagination
	queryBuilder.WriteString(" ORDER BY created_at DESC")
	queryBuilder.WriteString(fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1))
	args = append(args, limit, offset)

	query := queryBuilder.String()
	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		log.WithError(err).Error("failed to list admin audit logs")
		return nil, errors.NewDatabaseError("failed to list admin audit logs")
	}
	defer rows.Close()

	var auditLogs []*admin_audit_log.AdminAuditLog
	for rows.Next() {
		var auditLog admin_audit_log.AdminAuditLog
		err := rows.Scan(
			&auditLog.ID,
			&auditLog.UserID,
			&auditLog.Username,
			&auditLog.Method,
			&auditLog.Path,
			&auditLog.RequestBody,
			&auditLog.ResponseStatus,
			&auditLog.IPAddress,
			&auditLog.UserAgent,
			&auditLog.CreatedAt,
		)
		if err != nil {
			log.WithError(err).Error("failed to scan admin audit log row")
			return nil, errors.NewDatabaseError("failed to scan admin audit log")
		}
		auditLogs = append(auditLogs, &auditLog)
	}

	if err := rows.Err(); err != nil {
		log.WithError(err).Error("error iterating admin audit log rows")
		return nil, errors.NewDatabaseError("error iterating admin audit log rows")
	}

	log.WithField("count", len(auditLogs)).Info("admin audit logs retrieved successfully")
	return auditLogs, nil
}

// Count returns the total count of admin audit logs with filters
func (r *adminAuditLogRepository) Count(ctx context.Context, filter *admin_audit_log.AdminAuditLogFilter) (int64, error) {
	log := r.logger.WithContext(ctx).WithField("operation", "Count")

	// Build count query with filters
	queryBuilder := strings.Builder{}
	queryBuilder.WriteString("SELECT COUNT(*) FROM admin_audit_log WHERE 1=1")

	args := []interface{}{}
	argIndex := 1

	// Apply same filters as List method
	if filter != nil {
		if filter.UserID != nil {
			queryBuilder.WriteString(fmt.Sprintf(" AND user_id = $%d", argIndex))
			args = append(args, *filter.UserID)
			argIndex++
		}

		if filter.Username != nil && *filter.Username != "" {
			queryBuilder.WriteString(fmt.Sprintf(" AND username ILIKE $%d", argIndex))
			args = append(args, "%"+*filter.Username+"%")
			argIndex++
		}

		if filter.Method != nil && *filter.Method != "" {
			queryBuilder.WriteString(fmt.Sprintf(" AND method = $%d", argIndex))
			args = append(args, *filter.Method)
			argIndex++
		}

		if filter.Path != nil && *filter.Path != "" {
			queryBuilder.WriteString(fmt.Sprintf(" AND path ILIKE $%d", argIndex))
			args = append(args, "%"+*filter.Path+"%")
			argIndex++
		}

		if filter.DateFrom != nil && *filter.DateFrom != "" {
			queryBuilder.WriteString(fmt.Sprintf(" AND created_at >= $%d::date", argIndex))
			args = append(args, *filter.DateFrom)
			argIndex++
		}

		if filter.DateTo != nil && *filter.DateTo != "" {
			queryBuilder.WriteString(fmt.Sprintf(" AND created_at < $%d::date + INTERVAL '1 day'", argIndex))
			args = append(args, *filter.DateTo)
			argIndex++
		}
	}

	query := queryBuilder.String()
	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		log.WithError(err).Error("failed to count admin audit logs")
		return 0, errors.NewDatabaseError("failed to count admin audit logs")
	}

	log.WithField("count", count).Info("admin audit logs counted successfully")
	return count, nil
}

// DeleteOldEntries deletes audit log entries older than specified days
func (r *adminAuditLogRepository) DeleteOldEntries(ctx context.Context, daysToKeep int) (int64, error) {
	log := r.logger.WithContext(ctx).WithField("operation", "DeleteOldEntries").WithField("days_to_keep", daysToKeep)

	query := `
		DELETE FROM admin_audit_log 
		WHERE created_at < NOW() - INTERVAL '%d days'`

	query = fmt.Sprintf(query, daysToKeep)
	
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query)
	if err != nil {
		log.WithError(err).Error("failed to delete old admin audit log entries")
		return 0, errors.NewDatabaseError("failed to delete old admin audit log entries")
	}

	rowsAffected := result.RowsAffected()
	log.WithField("rows_deleted", rowsAffected).Info("old admin audit log entries deleted successfully")
	return rowsAffected, nil
}