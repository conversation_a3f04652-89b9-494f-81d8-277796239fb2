package postgres

import (
	"context"
	"fmt"
	"strings"

	"blacking-api/internal/domain/customer_sms_log"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type CustomerSMSLogRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewCustomerSMSLogRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.CustomerSMSLogRepository {
	return &CustomerSMSLogRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *CustomerSMSLogRepository) Create(ctx context.Context, smsLog *customer_sms_log.CustomerSMSLog) error {
	query := `
		INSERT INTO customer_sms_logs (member_id, admin_id, admin_name, message_content, sms_status, provider_response, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		RETURNING id
	`

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		smsLog.MemberID, smsLog.AdminID, smsLog.AdminName, smsLog.MessageContent,
		smsLog.SMSStatus, smsLog.ProviderResponse, smsLog.CreatedAt, smsLog.UpdatedAt,
	).Scan(&smsLog.ID)

	if err != nil {
		r.logger.WithError(err).Error("failed to create customer SMS log")
		return errors.NewDatabaseError("failed to create customer SMS log")
	}

	return nil
}

func (r *CustomerSMSLogRepository) GetByID(ctx context.Context, id int) (*customer_sms_log.CustomerSMSLog, error) {
	query := `
		SELECT id, member_id, admin_id, admin_name, message_content, sms_status, provider_response, created_at, updated_at
		FROM customer_sms_logs
		WHERE id = $1
	`

	smsLog := &customer_sms_log.CustomerSMSLog{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&smsLog.ID, &smsLog.MemberID, &smsLog.AdminID, &smsLog.AdminName,
		&smsLog.MessageContent, &smsLog.SMSStatus, &smsLog.ProviderResponse,
		&smsLog.CreatedAt, &smsLog.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("customer SMS log not found")
		}
		r.logger.WithError(err).Error("failed to get customer SMS log by ID")
		return nil, errors.NewDatabaseError("failed to get customer SMS log")
	}

	return smsLog, nil
}

func (r *CustomerSMSLogRepository) Update(ctx context.Context, smsLog *customer_sms_log.CustomerSMSLog) error {
	query := `
		UPDATE customer_sms_logs
		SET message_content = $2, sms_status = $3, provider_response = $4, updated_at = $5
		WHERE id = $1
	`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		smsLog.ID, smsLog.MessageContent, smsLog.SMSStatus, smsLog.ProviderResponse, smsLog.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to update customer SMS log")
		return errors.NewDatabaseError("failed to update customer SMS log")
	}

	return nil
}

func (r *CustomerSMSLogRepository) Delete(ctx context.Context, id int) error {
	query := `DELETE FROM customer_sms_logs WHERE id = $1`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete customer SMS log")
		return errors.NewDatabaseError("failed to delete customer SMS log")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("customer SMS log not found")
	}

	return nil
}

func (r *CustomerSMSLogRepository) List(ctx context.Context, limit, offset int) ([]*customer_sms_log.CustomerSMSLog, error) {
	query := `
		SELECT id, member_id, admin_id, admin_name, message_content, sms_status, provider_response, created_at, updated_at
		FROM customer_sms_logs
		ORDER BY created_at DESC
		LIMIT $1 OFFSET $2
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, limit, offset)
	if err != nil {
		r.logger.WithError(err).Error("failed to list customer SMS logs")
		return nil, errors.NewDatabaseError("failed to list customer SMS logs")
	}
	defer rows.Close()

	var smsLogs []*customer_sms_log.CustomerSMSLog
	for rows.Next() {
		smsLog := &customer_sms_log.CustomerSMSLog{}
		err := rows.Scan(
			&smsLog.ID, &smsLog.MemberID, &smsLog.AdminID, &smsLog.AdminName,
			&smsLog.MessageContent, &smsLog.SMSStatus, &smsLog.ProviderResponse,
			&smsLog.CreatedAt, &smsLog.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan customer SMS log row")
			return nil, errors.NewDatabaseError("failed to scan customer SMS log")
		}
		smsLogs = append(smsLogs, smsLog)
	}

	return smsLogs, nil
}

func (r *CustomerSMSLogRepository) ListWithFilter(ctx context.Context, limit, offset int, filter *customer_sms_log.CustomerSMSLogFilter) ([]*customer_sms_log.CustomerSMSLog, error) {
	baseQuery := `
		SELECT id, member_id, admin_id, admin_name, message_content, sms_status, provider_response, created_at, updated_at
		FROM customer_sms_logs
	`

	var conditions []string
	var args []interface{}
	argIndex := 1

	// Apply filters
	if filter.MemberID != nil {
		conditions = append(conditions, fmt.Sprintf("member_id = $%d", argIndex))
		args = append(args, *filter.MemberID)
		argIndex++
	}

	if filter.AdminID != nil {
		conditions = append(conditions, fmt.Sprintf("admin_id = $%d", argIndex))
		args = append(args, *filter.AdminID)
		argIndex++
	}

	if filter.SMSStatus != "" {
		conditions = append(conditions, fmt.Sprintf("sms_status = $%d", argIndex))
		args = append(args, filter.SMSStatus)
		argIndex++
	}

	if filter.Search != "" {
		conditions = append(conditions, fmt.Sprintf("message_content ILIKE $%d", argIndex))
		args = append(args, "%"+filter.Search+"%")
		argIndex++
	}

	if filter.StartDateTime != nil {
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, *filter.StartDateTime)
		argIndex++
	}

	if filter.EndDateTime != nil {
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, *filter.EndDateTime)
		argIndex++
	}

	// Build final query
	query := baseQuery
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	query += " ORDER BY created_at DESC"
	query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, offset)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list customer SMS logs with filter")
		return nil, errors.NewDatabaseError("failed to list customer SMS logs")
	}
	defer rows.Close()

	var smsLogs []*customer_sms_log.CustomerSMSLog
	for rows.Next() {
		smsLog := &customer_sms_log.CustomerSMSLog{}
		err := rows.Scan(
			&smsLog.ID, &smsLog.MemberID, &smsLog.AdminID, &smsLog.AdminName,
			&smsLog.MessageContent, &smsLog.SMSStatus, &smsLog.ProviderResponse,
			&smsLog.CreatedAt, &smsLog.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan customer SMS log row")
			return nil, errors.NewDatabaseError("failed to scan customer SMS log")
		}
		smsLogs = append(smsLogs, smsLog)
	}

	return smsLogs, nil
}

func (r *CustomerSMSLogRepository) Count(ctx context.Context) (int64, error) {
	query := `SELECT COUNT(*) FROM customer_sms_logs`

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count customer SMS logs")
		return 0, errors.NewDatabaseError("failed to count customer SMS logs")
	}

	return count, nil
}

func (r *CustomerSMSLogRepository) CountWithFilter(ctx context.Context, filter *customer_sms_log.CustomerSMSLogFilter) (int64, error) {
	baseQuery := `SELECT COUNT(*) FROM customer_sms_logs`

	var conditions []string
	var args []interface{}
	argIndex := 1

	// Apply same filters as ListWithFilter
	if filter.MemberID != nil {
		conditions = append(conditions, fmt.Sprintf("member_id = $%d", argIndex))
		args = append(args, *filter.MemberID)
		argIndex++
	}

	if filter.AdminID != nil {
		conditions = append(conditions, fmt.Sprintf("admin_id = $%d", argIndex))
		args = append(args, *filter.AdminID)
		argIndex++
	}

	if filter.SMSStatus != "" {
		conditions = append(conditions, fmt.Sprintf("sms_status = $%d", argIndex))
		args = append(args, filter.SMSStatus)
		argIndex++
	}

	if filter.Search != "" {
		conditions = append(conditions, fmt.Sprintf("message_content ILIKE $%d", argIndex))
		args = append(args, "%"+filter.Search+"%")
		argIndex++
	}

	if filter.StartDateTime != nil {
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, *filter.StartDateTime)
		argIndex++
	}

	if filter.EndDateTime != nil {
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, *filter.EndDateTime)
		argIndex++
	}

	query := baseQuery
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count customer SMS logs with filter")
		return 0, errors.NewDatabaseError("failed to count customer SMS logs")
	}

	return count, nil
}

func (r *CustomerSMSLogRepository) CountByMemberID(ctx context.Context, memberID int) (int64, error) {
	query := `SELECT COUNT(*) FROM customer_sms_logs WHERE member_id = $1`

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, memberID).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count customer SMS logs by member ID")
		return 0, errors.NewDatabaseError("failed to count customer SMS logs by member ID")
	}

	return count, nil
}

func (r *CustomerSMSLogRepository) GetLatestByMemberID(ctx context.Context, memberID int) (*customer_sms_log.CustomerSMSLog, error) {
	query := `
		SELECT id, member_id, admin_id, admin_name, message_content, sms_status, provider_response, created_at, updated_at
		FROM customer_sms_logs
		WHERE member_id = $1
		ORDER BY created_at DESC
		LIMIT 1
	`

	smsLog := &customer_sms_log.CustomerSMSLog{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, memberID).Scan(
		&smsLog.ID, &smsLog.MemberID, &smsLog.AdminID, &smsLog.AdminName,
		&smsLog.MessageContent, &smsLog.SMSStatus, &smsLog.ProviderResponse,
		&smsLog.CreatedAt, &smsLog.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("no customer SMS log found for member")
		}
		r.logger.WithError(err).Error("failed to get latest customer SMS log by member ID")
		return nil, errors.NewDatabaseError("failed to get latest customer SMS log")
	}

	return smsLog, nil
}
