package postgres

import (
	"context"
	"fmt"
	"strings"

	"blacking-api/internal/domain/report"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5/pgxpool"
)

type referralReportRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewReferralReportRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.ReferralReportRepository {
	return &referralReportRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *referralReportRepository) GetReferralReportSummary(ctx context.Context, filter report.ReferralReportFilter) ([]report.ReferralReportSummary, int, error) {
	// Validate and set defaults
	if err := filter.Validate(); err != nil {
		return nil, 0, err
	}

	// Build the base query with JOINs
	baseQuery := `
		SELECT 
			rt.member_id,
			m.username as member_username,
			m.phone as member_phone,
			m.first_name as member_first_name,
			m.last_name as member_last_name,
			m.refer_code,
			COALESCE(SUM(CASE WHEN rt.type = 'commission' AND rt.status = 'success' THEN rt.amount ELSE 0 END), 0) as total_commission_amount,
			COALESCE(SUM(CASE WHEN rt.type = 'withdraw' AND rt.status = 'success' THEN ABS(rt.amount) ELSE 0 END), 0) as total_withdraw_amount,
			COALESCE(SUM(CASE WHEN rt.type = 'commission' AND rt.status = 'success' THEN rt.amount ELSE 0 END), 0) - 
			COALESCE(SUM(CASE WHEN rt.type = 'withdraw' AND rt.status = 'success' THEN ABS(rt.amount) ELSE 0 END), 0) as net_amount,
			COUNT(rt.id) as transaction_count,
			COUNT(DISTINCT rt.downline_member_id) as downline_count,
			MIN(rt.created_at) as first_transaction_date,
			MAX(rt.created_at) as last_transaction_date,
			COALESCE(m.commission_balance, 0) as current_commission_balance
		FROM %s rt
		INNER JOIN %s m ON rt.member_id = m.id
		WHERE 1=1`

	// Add table names with schema
	baseQuery = fmt.Sprintf(baseQuery, 
		dbutil.TableName("referral_transactions"), 
		dbutil.TableName("members"))

	// Build WHERE conditions and parameters
	whereConditions, params := r.buildWhereConditions(filter)
	
	if len(whereConditions) > 0 {
		baseQuery += " AND " + strings.Join(whereConditions, " AND ")
	}

	// Add GROUP BY
	baseQuery += ` GROUP BY rt.member_id, m.username, m.phone, m.first_name, m.last_name, m.refer_code, m.commission_balance`

	// Count query for pagination
	countQuery := fmt.Sprintf(`
		SELECT COUNT(DISTINCT rt.member_id)
		FROM %s rt
		INNER JOIN %s m ON rt.member_id = m.id
		WHERE 1=1`,
		dbutil.TableName("referral_transactions"),
		dbutil.TableName("members"))

	if len(whereConditions) > 0 {
		countQuery += " AND " + strings.Join(whereConditions, " AND ")
	}

	// Get total count
	var totalCount int
	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, params...).Scan(&totalCount)
	if err != nil {
		r.logger.WithError(err).Error("failed to get referral report summary count")
		return nil, 0, errors.NewDatabaseError("failed to get report count")
	}

	// Add ORDER BY and LIMIT
	baseQuery += fmt.Sprintf(" ORDER BY %s %s LIMIT %d OFFSET %d", 
		filter.OrderBy, strings.ToUpper(filter.OrderDir), filter.Limit, filter.GetOffset())

	// Execute main query
	rows, err := dbutil.QueryWithSchema(ctx, r.pool, baseQuery, params...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get referral report summary")
		return nil, 0, errors.NewDatabaseError("failed to get referral report summary")
	}
	defer rows.Close()

	var summaries []report.ReferralReportSummary
	for rows.Next() {
		var summary report.ReferralReportSummary
		err := rows.Scan(
			&summary.MemberID,
			&summary.MemberUsername,
			&summary.MemberPhone,
			&summary.MemberFirstName,
			&summary.MemberLastName,
			&summary.ReferCode,
			&summary.TotalCommissionAmount,
			&summary.TotalWithdrawAmount,
			&summary.NetAmount,
			&summary.TransactionCount,
			&summary.DownlineCount,
			&summary.FirstTransactionDate,
			&summary.LastTransactionDate,
			&summary.CurrentCommissionBalance,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan referral report summary")
			return nil, 0, errors.NewDatabaseError("failed to scan report data")
		}
		summaries = append(summaries, summary)
	}

	return summaries, totalCount, nil
}

func (r *referralReportRepository) GetReferralReportDetail(ctx context.Context, memberID int, filter report.ReferralReportFilter) ([]report.ReferralReportDetail, int, error) {
	// Validate and set defaults
	if err := filter.Validate(); err != nil {
		return nil, 0, err
	}

	// Build the base query with JOINs
	baseQuery := `
		SELECT 
			rt.id,
			rt.member_id,
			m.username as member_username,
			m.phone as member_phone,
			rt.type,
			rt.amount,
			rt.game_category,
			rt.downline_member_id,
			dm.username as downline_member_username,
			dm.phone as downline_member_phone,
			rt.balance_before,
			rt.balance_after,
			rt.referral_start_date,
			rt.referral_end_date,
			rt.created_by_admin,
			rt.created_by_member,
			rt.status,
			rt.remark,
			rt.created_at,
			rt.updated_at
		FROM %s rt
		INNER JOIN %s m ON rt.member_id = m.id
		LEFT JOIN %s dm ON rt.downline_member_id = dm.id
		WHERE rt.member_id = $1`

	// Add table names with schema
	baseQuery = fmt.Sprintf(baseQuery,
		dbutil.TableName("referral_transactions"),
		dbutil.TableName("members"),
		dbutil.TableName("members"))

	// Build WHERE conditions and parameters (memberID is $1)
	params := []interface{}{memberID}
	whereConditions, additionalParams := r.buildWhereConditions(filter)

	// Adjust parameter indices for additional conditions
	for i, condition := range whereConditions {
		// Replace parameter placeholders to account for memberID being $1
		adjustedCondition := condition
		for j := len(additionalParams); j >= 1; j-- {
			oldPlaceholder := fmt.Sprintf("$%d", j)
			newPlaceholder := fmt.Sprintf("$%d", j+1)
			adjustedCondition = strings.ReplaceAll(adjustedCondition, oldPlaceholder, newPlaceholder)
		}
		whereConditions[i] = adjustedCondition
	}
	params = append(params, additionalParams...)

	if len(whereConditions) > 0 {
		baseQuery += " AND " + strings.Join(whereConditions, " AND ")
	}

	// Count query for pagination
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM %s rt
		INNER JOIN %s m ON rt.member_id = m.id
		WHERE rt.member_id = $1`,
		dbutil.TableName("referral_transactions"),
		dbutil.TableName("members"))

	if len(whereConditions) > 0 {
		countQuery += " AND " + strings.Join(whereConditions, " AND ")
	}

	// Get total count
	var totalCount int
	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, params...).Scan(&totalCount)
	if err != nil {
		r.logger.WithError(err).Error("failed to get referral report detail count")
		return nil, 0, errors.NewDatabaseError("failed to get report detail count")
	}

	// Add ORDER BY and LIMIT
	baseQuery += fmt.Sprintf(" ORDER BY rt.created_at %s LIMIT %d OFFSET %d", 
		strings.ToUpper(filter.OrderDir), filter.Limit, filter.GetOffset())

	// Execute main query
	rows, err := dbutil.QueryWithSchema(ctx, r.pool, baseQuery, params...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get referral report detail")
		return nil, 0, errors.NewDatabaseError("failed to get referral report detail")
	}
	defer rows.Close()

	var details []report.ReferralReportDetail
	for rows.Next() {
		var detail report.ReferralReportDetail
		err := rows.Scan(
			&detail.ID,
			&detail.MemberID,
			&detail.MemberUsername,
			&detail.MemberPhone,
			&detail.Type,
			&detail.Amount,
			&detail.GameCategory,
			&detail.DownlineMemberID,
			&detail.DownlineMemberUsername,
			&detail.DownlineMemberPhone,
			&detail.BalanceBefore,
			&detail.BalanceAfter,
			&detail.ReferralStartDate,
			&detail.ReferralEndDate,
			&detail.CreatedByAdmin,
			&detail.CreatedByMember,
			&detail.Status,
			&detail.Remark,
			&detail.CreatedAt,
			&detail.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan referral report detail")
			return nil, 0, errors.NewDatabaseError("failed to scan report detail data")
		}
		details = append(details, detail)
	}

	return details, totalCount, nil
}

func (r *referralReportRepository) GetReferralReportDetailGrouped(ctx context.Context, memberID int, filter report.ReferralReportFilter) ([]report.ReferralReportDetailGrouped, int, error) {
	// Validate and set defaults
	if err := filter.Validate(); err != nil {
		return nil, 0, err
	}

	// Build the base query with JOINs and GROUP BY - filter only commission type
	baseQuery := `
		SELECT 
			rt.member_id,
			m.username as member_username,
			m.phone as member_phone,
			rt.downline_member_id,
			dm.username as downline_member_username,
			dm.phone as downline_member_phone,
			COALESCE(SUM(rt.amount), 0) as total_commission_amount,
			0 as total_withdraw_amount,
			0 as total_adjustment_amount,
			0 as total_bonus_amount,
			COALESCE(SUM(rt.amount), 0) as net_amount,
			COUNT(rt.id) as transaction_count,
			COUNT(rt.id) as commission_count,
			0 as withdraw_count,
			0 as adjustment_count,
			0 as bonus_count,
			MIN(rt.created_at) as first_transaction_date,
			MAX(rt.created_at) as last_transaction_date
		FROM %s rt
		INNER JOIN %s m ON rt.member_id = m.id
		LEFT JOIN %s dm ON rt.downline_member_id = dm.id
		WHERE rt.member_id = $1 AND rt.type = 'commission'`

	// Add table names with schema
	baseQuery = fmt.Sprintf(baseQuery,
		dbutil.TableName("referral_transactions"),
		dbutil.TableName("members"),
		dbutil.TableName("members"))

	// Build WHERE conditions and parameters (memberID is $1)
	params := []interface{}{memberID}
	whereConditions, additionalParams := r.buildWhereConditions(filter)

	// Adjust parameter indices for additional conditions
	for i, condition := range whereConditions {
		adjustedCondition := condition
		for j := len(additionalParams); j >= 1; j-- {
			oldPlaceholder := fmt.Sprintf("$%d", j)
			newPlaceholder := fmt.Sprintf("$%d", j+1)
			adjustedCondition = strings.ReplaceAll(adjustedCondition, oldPlaceholder, newPlaceholder)
		}
		whereConditions[i] = adjustedCondition
	}
	params = append(params, additionalParams...)

	if len(whereConditions) > 0 {
		baseQuery += " AND " + strings.Join(whereConditions, " AND ")
	}

	// Add GROUP BY
	baseQuery += ` GROUP BY rt.member_id, m.username, m.phone, rt.downline_member_id, dm.username, dm.phone`

	// Count query for pagination - only commission type
	countQuery := fmt.Sprintf(`
		SELECT COUNT(DISTINCT COALESCE(rt.downline_member_id, -1)) 
		FROM %s rt
		INNER JOIN %s m ON rt.member_id = m.id
		WHERE rt.member_id = $1 AND rt.type = 'commission'`,
		dbutil.TableName("referral_transactions"),
		dbutil.TableName("members"))

	if len(whereConditions) > 0 {
		countQuery += " AND " + strings.Join(whereConditions, " AND ")
	}

	// Get total count
	var totalCount int
	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, params...).Scan(&totalCount)
	if err != nil {
		r.logger.WithError(err).Error("failed to get referral report grouped detail count")
		return nil, 0, errors.NewDatabaseError("failed to get report grouped detail count")
	}

	// Add ORDER BY and LIMIT
	baseQuery += fmt.Sprintf(" ORDER BY last_transaction_date %s LIMIT %d OFFSET %d", 
		strings.ToUpper(filter.OrderDir), filter.Limit, filter.GetOffset())

	// Execute main query
	rows, err := dbutil.QueryWithSchema(ctx, r.pool, baseQuery, params...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get referral report grouped detail")
		return nil, 0, errors.NewDatabaseError("failed to get referral report grouped detail")
	}
	defer rows.Close()

	var groupedDetails []report.ReferralReportDetailGrouped
	for rows.Next() {
		var detail report.ReferralReportDetailGrouped
		err := rows.Scan(
			&detail.MemberID,
			&detail.MemberUsername,
			&detail.MemberPhone,
			&detail.DownlineMemberID,
			&detail.DownlineMemberUsername,
			&detail.DownlineMemberPhone,
			&detail.TotalCommissionAmount,
			&detail.TotalWithdrawAmount,
			&detail.TotalAdjustmentAmount,
			&detail.TotalBonusAmount,
			&detail.NetAmount,
			&detail.TransactionCount,
			&detail.CommissionCount,
			&detail.WithdrawCount,
			&detail.AdjustmentCount,
			&detail.BonusCount,
			&detail.FirstTransactionDate,
			&detail.LastTransactionDate,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan referral report grouped detail")
			return nil, 0, errors.NewDatabaseError("failed to scan report grouped detail data")
		}
		groupedDetails = append(groupedDetails, detail)
	}

	return groupedDetails, totalCount, nil
}

func (r *referralReportRepository) GetReferralReportSummaryForExport(ctx context.Context, filter report.ReferralReportFilter) ([]report.ReferralReportSummary, error) {
	// Remove pagination for export
	filter.Page = 1
	filter.Limit = 100000 // Large limit for export

	summaries, _, err := r.GetReferralReportSummary(ctx, filter)
	return summaries, err
}

func (r *referralReportRepository) GetReferralReportDetailForExport(ctx context.Context, memberID int, filter report.ReferralReportFilter) ([]report.ReferralReportDetail, error) {
	// Remove pagination for export
	filter.Page = 1
	filter.Limit = 100000 // Large limit for export

	details, _, err := r.GetReferralReportDetail(ctx, memberID, filter)
	return details, err
}

func (r *referralReportRepository) GetReferralReportDetailGroupedForExport(ctx context.Context, memberID int, filter report.ReferralReportFilter) ([]report.ReferralReportDetailGrouped, error) {
	// Remove pagination for export
	filter.Page = 1
	filter.Limit = 100000 // Large limit for export

	groupedDetails, _, err := r.GetReferralReportDetailGrouped(ctx, memberID, filter)
	return groupedDetails, err
}

func (r *referralReportRepository) GetReportSummaryStatistics(ctx context.Context, filter report.ReferralReportFilter) (report.ReportSummary, error) {
	query := `
		SELECT 
			COUNT(DISTINCT rt.member_id) as total_members,
			COALESCE(SUM(CASE WHEN rt.type = 'commission' AND rt.status = 'success' THEN rt.amount ELSE 0 END), 0) as total_commission_amount,
			COALESCE(SUM(CASE WHEN rt.type = 'withdraw' AND rt.status = 'success' THEN ABS(rt.amount) ELSE 0 END), 0) as total_withdraw_amount,
			COUNT(rt.id) as total_transaction_count,
			COUNT(DISTINCT rt.downline_member_id) as total_downline_count
		FROM %s rt
		INNER JOIN %s m ON rt.member_id = m.id
		WHERE 1=1`

	query = fmt.Sprintf(query,
		dbutil.TableName("referral_transactions"),
		dbutil.TableName("members"))

	// Build WHERE conditions
	whereConditions, params := r.buildWhereConditions(filter)
	if len(whereConditions) > 0 {
		query += " AND " + strings.Join(whereConditions, " AND ")
	}

	var summary report.ReportSummary
	var totalMembers int
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, params...).Scan(
		&totalMembers,
		&summary.TotalCommissionAmount,
		&summary.TotalWithdrawAmount,
		&summary.TotalTransactionCount,
		&summary.TotalDownlineCount,
	)
	if err != nil {
		r.logger.WithError(err).Error("failed to get report summary statistics")
		return summary, errors.NewDatabaseError("failed to get report statistics")
	}

	summary.TotalMembers = totalMembers
	summary.NetAmount = summary.TotalCommissionAmount - summary.TotalWithdrawAmount
	if totalMembers > 0 {
		summary.AverageCommissionPerMember = summary.TotalCommissionAmount / float64(totalMembers)
	}

	return summary, nil
}

func (r *referralReportRepository) GetDetailReportSummaryStatistics(ctx context.Context, memberID int, filter report.ReferralReportFilter) (report.DetailReportSummary, error) {
	query := `
		SELECT 
			COUNT(*) as total_records,
			COALESCE(SUM(rt.amount), 0) as total_commission_amount,
			0 as total_withdraw_amount,
			COUNT(*) as commission_count,
			0 as withdraw_count
		FROM %s rt
		WHERE rt.member_id = $1 AND rt.type = 'commission'`

	query = fmt.Sprintf(query, dbutil.TableName("referral_transactions"))

	// Build WHERE conditions (memberID is $1)
	params := []interface{}{memberID}
	whereConditions, additionalParams := r.buildWhereConditions(filter)

	// Adjust parameter indices
	for i, condition := range whereConditions {
		adjustedCondition := condition
		for j := len(additionalParams); j >= 1; j-- {
			oldPlaceholder := fmt.Sprintf("$%d", j)
			newPlaceholder := fmt.Sprintf("$%d", j+1)
			adjustedCondition = strings.ReplaceAll(adjustedCondition, oldPlaceholder, newPlaceholder)
		}
		whereConditions[i] = adjustedCondition
	}
	params = append(params, additionalParams...)

	if len(whereConditions) > 0 {
		query += " AND " + strings.Join(whereConditions, " AND ")
	}

	var summary report.DetailReportSummary
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, params...).Scan(
		&summary.TotalRecords,
		&summary.TotalCommissionAmount,
		&summary.TotalWithdrawAmount,
		&summary.CommissionCount,
		&summary.WithdrawCount,
	)
	if err != nil {
		r.logger.WithError(err).Error("failed to get detail report summary statistics")
		return summary, errors.NewDatabaseError("failed to get detail report statistics")
	}

	summary.NetAmount = summary.TotalCommissionAmount - summary.TotalWithdrawAmount

	return summary, nil
}

// buildWhereConditions builds WHERE conditions and parameters for the filter
func (r *referralReportRepository) buildWhereConditions(filter report.ReferralReportFilter) ([]string, []interface{}) {
	var conditions []string
	var params []interface{}
	paramIndex := 1

	// Date range filter
	if filter.StartDate != nil {
		conditions = append(conditions, fmt.Sprintf("rt.created_at >= $%d", paramIndex))
		params = append(params, *filter.StartDate)
		paramIndex++
	}

	if filter.EndDate != nil {
		conditions = append(conditions, fmt.Sprintf("rt.created_at <= $%d", paramIndex))
		params = append(params, *filter.EndDate)
		paramIndex++
	}

	// Search filter (username or phone)
	if filter.Search != "" {
		conditions = append(conditions, fmt.Sprintf("(m.username ILIKE $%d OR m.phone ILIKE $%d)", paramIndex, paramIndex))
		params = append(params, "%"+filter.Search+"%")
		paramIndex++
	}

	// Exact phone match
	if filter.Phone != "" {
		conditions = append(conditions, fmt.Sprintf("m.phone = $%d", paramIndex))
		params = append(params, filter.Phone)
		paramIndex++
	}

	// Exact username match
	if filter.Username != "" {
		conditions = append(conditions, fmt.Sprintf("m.username = $%d", paramIndex))
		params = append(params, filter.Username)
		paramIndex++
	}

	// Status filter
	if len(filter.Status) > 0 {
		statusPlaceholders := make([]string, len(filter.Status))
		for i, status := range filter.Status {
			statusPlaceholders[i] = fmt.Sprintf("$%d", paramIndex)
			params = append(params, status)
			paramIndex++
		}
		conditions = append(conditions, fmt.Sprintf("rt.status IN (%s)", strings.Join(statusPlaceholders, ",")))
	}

	// Type filter
	if len(filter.Type) > 0 {
		typePlaceholders := make([]string, len(filter.Type))
		for i, transactionType := range filter.Type {
			typePlaceholders[i] = fmt.Sprintf("$%d", paramIndex)
			params = append(params, transactionType)
			paramIndex++
		}
		conditions = append(conditions, fmt.Sprintf("rt.type IN (%s)", strings.Join(typePlaceholders, ",")))
	}

	// Game category filter
	if filter.GameCategory != nil && *filter.GameCategory != "" {
		conditions = append(conditions, fmt.Sprintf("rt.game_category = $%d", paramIndex))
		params = append(params, *filter.GameCategory)
		paramIndex++
	}

	return conditions, params
}

func (r *referralReportRepository) GetCommissionByGameCategory(ctx context.Context, filter report.CommissionByGameCategoryFilter) ([]report.ReferralReportDetail, int, report.CategoryCommissionSummary, error) {
	// Validate filter
	if err := filter.Validate(); err != nil {
		return nil, 0, report.CategoryCommissionSummary{}, err
	}

	// Build main query for commission transactions with specific game category
	baseQuery := `
		SELECT 
			rt.id,
			rt.member_id,
			m.username as member_username,
			m.phone as member_phone,
			rt.type,
			rt.amount,
			rt.game_category,
			rt.downline_member_id,
			dm.username as downline_member_username,
			dm.phone as downline_member_phone,
			rt.balance_before,
			rt.balance_after,
			rt.referral_start_date,
			rt.referral_end_date,
			rt.created_by_admin,
			rt.created_by_member,
			rt.status,
			rt.remark,
			rt.created_at,
			rt.updated_at
		FROM %s rt
		INNER JOIN %s m ON rt.member_id = m.id
		LEFT JOIN %s dm ON rt.downline_member_id = dm.id
		WHERE rt.type = 'commission' 
		AND rt.game_category = $1
		AND rt.referral_start_date >= $2 
		AND rt.referral_end_date <= $3`

	// Add table names with schema
	baseQuery = fmt.Sprintf(baseQuery,
		dbutil.TableName("referral_transactions"),
		dbutil.TableName("members"),
		dbutil.TableName("members"))

	// Parameters for the query
	params := []interface{}{filter.GameCategory, filter.StartDate, filter.EndDate}

	// Count query for pagination
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM %s rt
		WHERE rt.type = 'commission' 
		AND rt.game_category = $1
		AND rt.referral_start_date >= $2 
		AND rt.referral_end_date <= $3`,
		dbutil.TableName("referral_transactions"))

	// Get total count
	var totalCount int
	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, params...).Scan(&totalCount)
	if err != nil {
		r.logger.WithError(err).Error("failed to get commission by game category count")
		return nil, 0, report.CategoryCommissionSummary{}, errors.NewDatabaseError("failed to get commission count")
	}

	// Get summary statistics
	summaryQuery := fmt.Sprintf(`
		SELECT 
			COALESCE(SUM(rt.amount), 0) as total_commission_amount,
			COUNT(rt.id) as transaction_count,
			COUNT(DISTINCT rt.member_id) as unique_members
		FROM %s rt
		WHERE rt.type = 'commission' 
		AND rt.game_category = $1
		AND rt.referral_start_date >= $2 
		AND rt.referral_end_date <= $3`,
		dbutil.TableName("referral_transactions"))

	var summary report.CategoryCommissionSummary
	summary.GameCategory = filter.GameCategory
	err = dbutil.QueryRowWithSchema(ctx, r.pool, summaryQuery, params...).Scan(
		&summary.TotalCommissionAmount,
		&summary.TransactionCount,
		&summary.UniqueMembers,
	)
	if err != nil {
		r.logger.WithError(err).Error("failed to get commission summary by game category")
		return nil, 0, report.CategoryCommissionSummary{}, errors.NewDatabaseError("failed to get commission summary")
	}

	// Add ORDER BY and LIMIT for main query
	baseQuery += fmt.Sprintf(" ORDER BY rt.created_at DESC LIMIT %d OFFSET %d", 
		filter.Limit, filter.GetOffset())

	// Execute main query
	rows, err := dbutil.QueryWithSchema(ctx, r.pool, baseQuery, params...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get commission by game category")
		return nil, 0, report.CategoryCommissionSummary{}, errors.NewDatabaseError("failed to get commission by game category")
	}
	defer rows.Close()

	var details []report.ReferralReportDetail
	for rows.Next() {
		var detail report.ReferralReportDetail
		err := rows.Scan(
			&detail.ID,
			&detail.MemberID,
			&detail.MemberUsername,
			&detail.MemberPhone,
			&detail.Type,
			&detail.Amount,
			&detail.GameCategory,
			&detail.DownlineMemberID,
			&detail.DownlineMemberUsername,
			&detail.DownlineMemberPhone,
			&detail.BalanceBefore,
			&detail.BalanceAfter,
			&detail.ReferralStartDate,
			&detail.ReferralEndDate,
			&detail.CreatedByAdmin,
			&detail.CreatedByMember,
			&detail.Status,
			&detail.Remark,
			&detail.CreatedAt,
			&detail.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan commission by game category detail")
			return nil, 0, report.CategoryCommissionSummary{}, errors.NewDatabaseError("failed to scan commission detail data")
		}
		details = append(details, detail)
	}

	return details, totalCount, summary, nil
}