package postgres

import (
	"context"

	"blacking-api/internal/domain/referral"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5/pgxpool"
)

type ReferralFAQRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewReferralFAQRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.ReferralFAQRepository {
	return &ReferralFAQRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *ReferralFAQRepository) GetActiveFAQsByCategory(category string) ([]referral.FAQ, error) {
	ctx := context.Background()

	query := `
		SELECT id, question, answer, category, "order", is_active
		FROM faqs 
		WHERE is_active = true AND category = $1
		ORDER BY "order" ASC, id ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, category)
	if err != nil {
		r.logger.WithError(err).Error("failed to get active FAQs by category")
		return nil, errors.NewDatabaseError("failed to get active FAQs by category")
	}
	defer rows.Close()

	var faqs []referral.FAQ
	for rows.Next() {
		var faq referral.FAQ

		var question, answer, dummyCategory string
		var order int
		var isActive bool

		err := rows.Scan(
			&faq.ID,
			&question,
			&answer,
			&dummyCategory, // dummy variable for unused category
			&order,         // dummy variable for unused order
			&isActive,      // dummy variable for unused isActive
		)
		if err == nil {
			faq.Title = &question
			faq.Answer = &answer
			if isActive {
				faq.Status = "active"
			} else {
				faq.Status = "inactive"
			}
			position := order
			faq.Position = &position
		}
		if err != nil {
			r.logger.WithError(err).Error("failed to scan FAQ")
			continue
		}

		faqs = append(faqs, faq)
	}

	return faqs, nil
}
