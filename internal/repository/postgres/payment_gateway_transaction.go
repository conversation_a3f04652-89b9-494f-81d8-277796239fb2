package postgres

import (
	"blacking-api/internal/domain/payment_gateway_transaction"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type PaymentGatewayTransactionRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewPaymentGatewayTransactionRepository(pool *pgxpool.Pool, logger logger.Logger) *PaymentGatewayTransactionRepository {
	return &PaymentGatewayTransactionRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *PaymentGatewayTransactionRepository) Create(ctx context.Context, transaction *payment_gateway_transaction.PaymentGatewayTransaction) error {
	// Convert metadata to JSON
	metadataJSON, _ := json.Marshal(transaction.Metadata)
	providerResponseJSON, _ := json.Marshal(transaction.ProviderResponse)
	var blockchainDataJSON []byte
	if transaction.BlockchainData != nil {
		blockchainDataJSON, _ = json.Marshal(transaction.BlockchainData)
	}

	query := `
		INSERT INTO payment_gateway_transactions (
			transaction_id, internal_reference, payment_gateway_account_id, provider,
			provider_merchant_id, provider_order_id, transaction_type, amount, currency,
			fee_amount, net_amount, status, customer_reference, customer_username,
			customer_bank_account, customer_bank_name, callback_url, return_url, payment_url,
			qr_code, qr_text, qr_image_url, initiated_at, expires_at, description, metadata, 
			provider_response, blockchain_data, created_by
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29)
		RETURNING id, created_at, updated_at
	`

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		transaction.TransactionID, transaction.InternalReference, transaction.PaymentGatewayAccountID,
		transaction.Provider, transaction.ProviderMerchantID, transaction.ProviderOrderID,
		transaction.TransactionType, transaction.Amount, transaction.Currency,
		transaction.FeeAmount, transaction.NetAmount, transaction.Status,
		transaction.CustomerReference, transaction.CustomerUsername,
		transaction.CustomerBankAccount, transaction.CustomerBankName,
		transaction.CallbackURL, transaction.ReturnURL, transaction.PaymentURL,
		transaction.QRCode, transaction.QRText, transaction.QRImageURL,
		transaction.InitiatedAt, transaction.ExpiresAt,
		transaction.Description, metadataJSON, providerResponseJSON, blockchainDataJSON,
		transaction.CreatedBy,
	).Scan(&transaction.ID, &transaction.CreatedAt, &transaction.UpdatedAt)

	if err != nil {
		r.logger.WithError(err).Error("failed to create payment gateway transaction")
		return errors.NewDatabaseError("failed to create payment gateway transaction")
	}

	r.logger.WithFields(map[string]interface{}{
		"transaction_id": transaction.TransactionID,
		"provider":       transaction.Provider,
		"amount":         transaction.Amount,
		"type":           transaction.TransactionType,
	}).Info("payment gateway transaction created")

	return nil
}

func (r *PaymentGatewayTransactionRepository) ExistsByTransactionID(ctx context.Context, transactionID string) (bool, error) {
	var exists bool
	query := `SELECT EXISTS(SELECT 1 FROM payment_gateway_transactions WHERE transaction_id = $1)`

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, transactionID).Scan(&exists)
	if err != nil {
		r.logger.WithError(err).Error("failed to check if transaction exists")
		return false, errors.NewDatabaseError("failed to check transaction existence")
	}

	return exists, nil
}

func (r *PaymentGatewayTransactionRepository) GetByTransactionID(ctx context.Context, transactionID string) (*payment_gateway_transaction.PaymentGatewayTransaction, error) {
	var transaction payment_gateway_transaction.PaymentGatewayTransaction
	var metadataJSON, providerResponseJSON []byte

	query := `
		SELECT id, transaction_id, internal_reference, payment_gateway_account_id, provider,
			   provider_merchant_id, provider_order_id, transaction_type, amount, currency,
			   fee_amount, net_amount, status, previous_status, status_updated_at,
			   customer_reference, customer_username, customer_bank_account, customer_bank_name,
			   callback_url, return_url, payment_url, qr_code, qr_text, qr_image_url,
			   initiated_at, expires_at, completed_at, failed_at,
			   webhook_received_at, webhook_count, last_webhook_at, webhook_status,
			   description, metadata, provider_response, error_code, error_message,
			   created_at, updated_at, created_by, updated_by
		FROM payment_gateway_transactions WHERE transaction_id = $1
	`

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, transactionID).Scan(
		&transaction.ID, &transaction.TransactionID, &transaction.InternalReference,
		&transaction.PaymentGatewayAccountID, &transaction.Provider,
		&transaction.ProviderMerchantID, &transaction.ProviderOrderID,
		&transaction.TransactionType, &transaction.Amount, &transaction.Currency,
		&transaction.FeeAmount, &transaction.NetAmount, &transaction.Status,
		&transaction.PreviousStatus, &transaction.StatusUpdatedAt,
		&transaction.CustomerReference, &transaction.CustomerUsername,
		&transaction.CustomerBankAccount, &transaction.CustomerBankName,
		&transaction.CallbackURL, &transaction.ReturnURL, &transaction.PaymentURL,
		&transaction.QRCode, &transaction.QRText, &transaction.QRImageURL,
		&transaction.InitiatedAt, &transaction.ExpiresAt, &transaction.CompletedAt, &transaction.FailedAt,
		&transaction.WebhookReceivedAt, &transaction.WebhookCount, &transaction.LastWebhookAt, &transaction.WebhookStatus,
		&transaction.Description, &metadataJSON, &providerResponseJSON,
		&transaction.ErrorCode, &transaction.ErrorMessage,
		&transaction.CreatedAt, &transaction.UpdatedAt, &transaction.CreatedBy, &transaction.UpdatedBy,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).Error("failed to get payment gateway transaction")
		return nil, errors.NewDatabaseError("failed to get payment gateway transaction")
	}

	// Parse JSON fields
	if metadataJSON != nil {
		json.Unmarshal(metadataJSON, &transaction.Metadata)
	}
	if providerResponseJSON != nil {
		json.Unmarshal(providerResponseJSON, &transaction.ProviderResponse)
	}

	return &transaction, nil
}

func (r *PaymentGatewayTransactionRepository) UpdateStatus(ctx context.Context, transactionID string, status string, metadata map[string]interface{}) error {
	metadataJSON, _ := json.Marshal(metadata)

	query := `
		UPDATE payment_gateway_transactions 
		SET status = $2, previous_status = status, status_updated_at = NOW(), 
		    metadata = $3, updated_at = NOW()
		WHERE transaction_id = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, transactionID, status, metadataJSON)
	if err != nil {
		r.logger.WithError(err).Error("failed to update transaction status")
		return errors.NewDatabaseError("failed to update transaction status")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("transaction not found")
	}

	r.logger.WithFields(map[string]interface{}{
		"transaction_id": transactionID,
		"status":         status,
	}).Info("transaction status updated")

	return nil
}

func (r *PaymentGatewayTransactionRepository) CreateWebhookLog(ctx context.Context, webhook *payment_gateway_transaction.PaymentGatewayWebhook) error {
	payloadJSON, _ := json.Marshal(webhook.WebhookPayload)
	headersJSON, _ := json.Marshal(webhook.WebhookHeaders)

	query := `
		INSERT INTO payment_gateway_webhooks (
			transaction_id, webhook_event, webhook_payload, webhook_signature,
			webhook_headers, is_signature_valid, processing_status, provider,
			provider_request_id, provider_timestamp, http_method, http_status_code,
			user_agent, ip_address
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
		RETURNING id, received_at
	`

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		webhook.TransactionID, webhook.WebhookEvent, payloadJSON, webhook.WebhookSignature,
		headersJSON, webhook.IsSignatureValid, webhook.ProcessingStatus, webhook.Provider,
		webhook.ProviderRequestID, webhook.ProviderTimestamp, webhook.HTTPMethod, webhook.HTTPStatusCode,
		webhook.UserAgent, webhook.IPAddress,
	).Scan(&webhook.ID, &webhook.ReceivedAt)

	if err != nil {
		r.logger.WithError(err).Error("failed to create webhook log")
		return errors.NewDatabaseError("failed to create webhook log")
	}

	r.logger.WithFields(map[string]interface{}{
		"webhook_id":     webhook.ID,
		"transaction_id": webhook.TransactionID,
		"event":          webhook.WebhookEvent,
		"provider":       webhook.Provider,
	}).Info("webhook log created")

	return nil
}

func (r *PaymentGatewayTransactionRepository) GetWebhooksByTransactionID(ctx context.Context, transactionID string) ([]*payment_gateway_transaction.PaymentGatewayWebhook, error) {
	query := `
		SELECT id, transaction_id, webhook_event, webhook_payload, webhook_signature,
			   webhook_headers, is_signature_valid, processing_status, processing_error,
			   processed_at, provider, provider_request_id, provider_timestamp,
			   http_method, http_status_code, user_agent, ip_address, received_at
		FROM payment_gateway_webhooks 
		WHERE transaction_id = $1 
		ORDER BY received_at DESC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, transactionID)
	if err != nil {
		r.logger.WithError(err).Error("failed to get webhooks by transaction ID")
		return nil, errors.NewDatabaseError("failed to get webhooks by transaction ID")
	}
	defer rows.Close()

	var webhooks []*payment_gateway_transaction.PaymentGatewayWebhook
	for rows.Next() {
		var webhook payment_gateway_transaction.PaymentGatewayWebhook
		var payloadJSON, headersJSON []byte

		err := rows.Scan(
			&webhook.ID, &webhook.TransactionID, &webhook.WebhookEvent, &payloadJSON, &webhook.WebhookSignature,
			&headersJSON, &webhook.IsSignatureValid, &webhook.ProcessingStatus, &webhook.ProcessingError,
			&webhook.ProcessedAt, &webhook.Provider, &webhook.ProviderRequestID, &webhook.ProviderTimestamp,
			&webhook.HTTPMethod, &webhook.HTTPStatusCode, &webhook.UserAgent, &webhook.IPAddress, &webhook.ReceivedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan webhook row")
			return nil, errors.NewDatabaseError("failed to scan webhook row")
		}

		// Parse JSON fields
		if payloadJSON != nil {
			json.Unmarshal(payloadJSON, &webhook.WebhookPayload)
		}
		if headersJSON != nil {
			json.Unmarshal(headersJSON, &webhook.WebhookHeaders)
		}

		webhooks = append(webhooks, &webhook)
	}

	return webhooks, nil
}

func (r *PaymentGatewayTransactionRepository) UpdateWebhookProcessingStatus(ctx context.Context, webhookID int64, status string, processingError string) error {
	query := `
		UPDATE payment_gateway_webhooks 
		SET processing_status = $2, processing_error = $3, processed_at = NOW()
		WHERE id = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, webhookID, status, processingError)
	if err != nil {
		r.logger.WithError(err).Error("failed to update webhook processing status")
		return errors.NewDatabaseError("failed to update webhook processing status")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("webhook not found")
	}

	return nil
}

func (r *PaymentGatewayTransactionRepository) GetTransactions(ctx context.Context, filters *payment_gateway_transaction.TransactionFilters) (*payment_gateway_transaction.TransactionListResponse, error) {
	// Build WHERE conditions
	var whereConditions []string
	var args []interface{}
	argCount := 0

	if filters.TransactionID != "" {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("transaction_id = $%d", argCount))
		args = append(args, filters.TransactionID)
	}

	if filters.InternalReference != "" {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("internal_reference = $%d", argCount))
		args = append(args, filters.InternalReference)
	}

	if filters.Provider != "" {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("provider = $%d", argCount))
		args = append(args, filters.Provider)
	}

	if filters.Status != "" {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("status = $%d", argCount))
		args = append(args, filters.Status)
	}

	if filters.TransactionType != "" {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("transaction_type = $%d", argCount))
		args = append(args, filters.TransactionType)
	}

	if filters.CustomerUsername != "" {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("customer_username = $%d", argCount))
		args = append(args, filters.CustomerUsername)
	}

	if filters.CustomerReference != "" {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("customer_reference = $%d", argCount))
		args = append(args, filters.CustomerReference)
	}

	if filters.CustomerBankAccount != "" {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("customer_bank_account = $%d", argCount))
		args = append(args, filters.CustomerBankAccount)
	}

	if filters.CustomerBankName != "" {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("customer_bank_name = $%d", argCount))
		args = append(args, filters.CustomerBankName)
	}

	if filters.PaymentGatewayAccountID != nil {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("payment_gateway_account_id = $%d", argCount))
		args = append(args, *filters.PaymentGatewayAccountID)
	}

	if filters.MinAmount != nil {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("amount >= $%d", argCount))
		args = append(args, *filters.MinAmount)
	}

	if filters.MaxAmount != nil {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("amount <= $%d", argCount))
		args = append(args, *filters.MaxAmount)
	}

	if filters.CreatedFromDate != nil {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("created_at >= $%d", argCount))
		args = append(args, *filters.CreatedFromDate)
	}

	if filters.CreatedToDate != nil {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("created_at <= $%d", argCount))
		args = append(args, *filters.CreatedToDate)
	}

	if filters.InitiatedFromDate != nil {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("initiated_at >= $%d", argCount))
		args = append(args, *filters.InitiatedFromDate)
	}

	if filters.InitiatedToDate != nil {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("initiated_at <= $%d", argCount))
		args = append(args, *filters.InitiatedToDate)
	}

	if filters.CompletedFromDate != nil {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("completed_at >= $%d", argCount))
		args = append(args, *filters.CompletedFromDate)
	}

	if filters.CompletedToDate != nil {
		argCount++
		whereConditions = append(whereConditions, fmt.Sprintf("completed_at <= $%d", argCount))
		args = append(args, *filters.CompletedToDate)
	}

	if filters.HasError {
		whereConditions = append(whereConditions, "(error_code IS NOT NULL AND error_code != '' OR error_message IS NOT NULL AND error_message != '')")
	}

	// Build WHERE clause
	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	// Build ORDER BY clause
	orderBy := "created_at DESC"
	if filters.SortBy != "" {
		validSortFields := map[string]bool{
			"id": true, "transaction_id": true, "provider": true, "status": true,
			"transaction_type": true, "amount": true, "created_at": true, "updated_at": true,
			"initiated_at": true, "completed_at": true, "customer_username": true,
		}
		if validSortFields[filters.SortBy] {
			sortOrder := "DESC"
			if filters.SortOrder == "asc" {
				sortOrder = "ASC"
			}
			orderBy = fmt.Sprintf("%s %s", filters.SortBy, sortOrder)
		}
	}

	// First, get total count
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM payment_gateway_transactions %s", whereClause)
	var totalCount int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...).Scan(&totalCount)
	if err != nil {
		r.logger.WithError(err).Error("failed to get transaction count")
		return nil, errors.NewDatabaseError("failed to get transaction count")
	}

	// Calculate pagination
	if filters.Page < 1 {
		filters.Page = 1
	}
	if filters.PageSize < 1 {
		filters.PageSize = 10
	}
	if filters.PageSize > 100 {
		filters.PageSize = 100 // Limit max page size
	}

	offset := (filters.Page - 1) * filters.PageSize
	totalPages := int((totalCount + int64(filters.PageSize) - 1) / int64(filters.PageSize))

	// Build main query with pagination
	argCount++
	limitArg := argCount
	argCount++
	offsetArg := argCount

	query := fmt.Sprintf(`
		SELECT id, transaction_id, internal_reference, payment_gateway_account_id, provider,
			   provider_merchant_id, provider_order_id, transaction_type, amount, currency,
			   fee_amount, net_amount, status, previous_status, status_updated_at,
			   customer_reference, customer_username, customer_bank_account, customer_bank_name,
			   callback_url, return_url, payment_url, qr_code, qr_text, qr_image_url,
			   initiated_at, expires_at, completed_at, failed_at,
			   webhook_received_at, webhook_count, last_webhook_at, webhook_status,
			   description, metadata, provider_response, error_code, error_message,
			   created_at, updated_at, created_by, updated_by
		FROM payment_gateway_transactions
		%s
		ORDER BY %s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderBy, limitArg, offsetArg)

	args = append(args, filters.PageSize, offset)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get transactions")
		return nil, errors.NewDatabaseError("failed to get transactions")
	}
	defer rows.Close()

	var transactions []*payment_gateway_transaction.PaymentGatewayTransaction
	for rows.Next() {
		var transaction payment_gateway_transaction.PaymentGatewayTransaction
		var metadataJSON, providerResponseJSON []byte

		// Use sql.NullString for nullable string fields
		var internalReference, providerMerchantID, providerOrderID sql.NullString
		var previousStatus, customerReference, customerUsername sql.NullString
		var customerBankAccount, customerBankName, callbackURL, returnURL, paymentURL sql.NullString
		var qrCode, qrText, qrImageURL sql.NullString
		var webhookStatus, description, errorCode, errorMessage sql.NullString
		var createdBy, updatedBy sql.NullString

		// Use sql.NullFloat64 for nullable numeric fields
		var feeAmount, netAmount sql.NullFloat64

		// Use sql.NullInt32 for nullable integer fields
		var webhookCount sql.NullInt32

		err := rows.Scan(
			&transaction.ID, &transaction.TransactionID, &internalReference,
			&transaction.PaymentGatewayAccountID, &transaction.Provider,
			&providerMerchantID, &providerOrderID,
			&transaction.TransactionType, &transaction.Amount, &transaction.Currency,
			&feeAmount, &netAmount, &transaction.Status,
			&previousStatus, &transaction.StatusUpdatedAt,
			&customerReference, &customerUsername,
			&customerBankAccount, &customerBankName,
			&callbackURL, &returnURL, &paymentURL,
			&qrCode, &qrText, &qrImageURL,
			&transaction.InitiatedAt, &transaction.ExpiresAt, &transaction.CompletedAt, &transaction.FailedAt,
			&transaction.WebhookReceivedAt, &webhookCount, &transaction.LastWebhookAt, &webhookStatus,
			&description, &metadataJSON, &providerResponseJSON,
			&errorCode, &errorMessage,
			&transaction.CreatedAt, &transaction.UpdatedAt, &createdBy, &updatedBy,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan transaction row")
			return nil, errors.NewDatabaseError("failed to scan transaction row")
		}

		// Handle nullable string fields
		if internalReference.Valid {
			transaction.InternalReference = internalReference.String
		}
		if providerMerchantID.Valid {
			transaction.ProviderMerchantID = providerMerchantID.String
		}
		if providerOrderID.Valid {
			transaction.ProviderOrderID = providerOrderID.String
		}
		if previousStatus.Valid {
			transaction.PreviousStatus = previousStatus.String
		}
		if customerReference.Valid {
			transaction.CustomerReference = customerReference.String
		}
		if customerUsername.Valid {
			transaction.CustomerUsername = customerUsername.String
		}
		if customerBankAccount.Valid {
			transaction.CustomerBankAccount = customerBankAccount.String
		}
		if customerBankName.Valid {
			transaction.CustomerBankName = customerBankName.String
		}
		if callbackURL.Valid {
			transaction.CallbackURL = callbackURL.String
		}
		if returnURL.Valid {
			transaction.ReturnURL = returnURL.String
		}
		if paymentURL.Valid {
			transaction.PaymentURL = paymentURL.String
		}
		if qrCode.Valid {
			transaction.QRCode = qrCode.String
		}
		if qrText.Valid {
			transaction.QRText = qrText.String
		}
		if qrImageURL.Valid {
			transaction.QRImageURL = qrImageURL.String
		}
		if webhookStatus.Valid {
			transaction.WebhookStatus = webhookStatus.String
		}
		if description.Valid {
			transaction.Description = description.String
		}
		if errorCode.Valid {
			transaction.ErrorCode = errorCode.String
		}
		if errorMessage.Valid {
			transaction.ErrorMessage = errorMessage.String
		}
		if createdBy.Valid {
			transaction.CreatedBy = createdBy.String
		}
		if updatedBy.Valid {
			transaction.UpdatedBy = updatedBy.String
		}

		// Handle nullable numeric fields
		if feeAmount.Valid {
			transaction.FeeAmount = feeAmount.Float64
		}
		if netAmount.Valid {
			transaction.NetAmount = netAmount.Float64
		}

		// Handle nullable integer fields
		if webhookCount.Valid {
			transaction.WebhookCount = int(webhookCount.Int32)
		}

		// Parse JSON fields
		if metadataJSON != nil {
			json.Unmarshal(metadataJSON, &transaction.Metadata)
		}
		if providerResponseJSON != nil {
			json.Unmarshal(providerResponseJSON, &transaction.ProviderResponse)
		}

		transactions = append(transactions, &transaction)
	}

	response := &payment_gateway_transaction.TransactionListResponse{
		Transactions: transactions,
		Pagination: &payment_gateway_transaction.PaginationInfo{
			CurrentPage: filters.Page,
			PageSize:    filters.PageSize,
			Total:       totalCount,
			TotalPages:  totalPages,
		},
	}

	return response, nil
}
