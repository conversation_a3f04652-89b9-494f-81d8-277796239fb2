package postgres

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5/pgxpool"

	"blacking-api/internal/domain"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
)

type jaiJaiPayAPILogRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewJaiJaiPayAPILogRepository creates a new instance of JaiJaiPayAPILogRepository
func NewJaiJaiPayAPILogRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.JaiJaiPayAPILogRepository {
	return &jaiJaiPayAPILogRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *jaiJaiPayAPILogRepository) Create(ctx context.Context, apiLog *domain.JaiJaiPayAPILog) error {
	query := fmt.Sprintf(`
		INSERT INTO %s (
			request_id, api_category, method, endpoint, request_body, 
			request_headers, business_data, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
		RETURNING id, created_at, updated_at
	`, dbutil.TableName("jaijaipay_api_logs"))

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		apiLog.RequestID,
		apiLog.APICategory,
		apiLog.Method,
		apiLog.Endpoint,
		apiLog.RequestBody,
		apiLog.RequestHeaders,
		apiLog.BusinessData,
	).Scan(&apiLog.ID, &apiLog.CreatedAt, &apiLog.UpdatedAt)

	if err != nil {
		r.logger.WithError(err).Error("failed to create jaijaipay api log")
		return errors.NewDatabaseError("failed to create jaijaipay api log")
	}

	return nil
}

func (r *jaiJaiPayAPILogRepository) Update(ctx context.Context, apiLog *domain.JaiJaiPayAPILog) error {
	query := fmt.Sprintf(`
		UPDATE %s SET
			response_status_code = $2,
			response_body = $3,
			response_time_ms = $4,
			success = $5,
			error_message = $6,
			updated_at = NOW()
		WHERE id = $1
		RETURNING updated_at
	`, dbutil.TableName("jaijaipay_api_logs"))

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		apiLog.ID,
		apiLog.ResponseStatusCode,
		apiLog.ResponseBody,
		apiLog.ResponseTimeMs,
		apiLog.Success,
		apiLog.ErrorMessage,
	).Scan(&apiLog.UpdatedAt)

	if err != nil {
		r.logger.WithError(err).Error("failed to update jaijaipay api log")
		return errors.NewDatabaseError("failed to update jaijaipay api log")
	}

	return nil
}

func (r *jaiJaiPayAPILogRepository) GetByID(ctx context.Context, id int64) (*domain.JaiJaiPayAPILog, error) {
	query := fmt.Sprintf(`
		SELECT id, request_id, api_category, method, endpoint, request_body,
			   request_headers, response_status_code, response_body, response_time_ms,
			   success, error_message, business_data, created_at, updated_at
		FROM %s WHERE id = $1
	`, dbutil.TableName("jaijaipay_api_logs"))

	apiLog := &domain.JaiJaiPayAPILog{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&apiLog.ID,
		&apiLog.RequestID,
		&apiLog.APICategory,
		&apiLog.Method,
		&apiLog.Endpoint,
		&apiLog.RequestBody,
		&apiLog.RequestHeaders,
		&apiLog.ResponseStatusCode,
		&apiLog.ResponseBody,
		&apiLog.ResponseTimeMs,
		&apiLog.Success,
		&apiLog.ErrorMessage,
		&apiLog.BusinessData,
		&apiLog.CreatedAt,
		&apiLog.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to get jaijaipay api log by id")
		return nil, errors.NewNotFoundError("jaijaipay api log not found")
	}

	return apiLog, nil
}

func (r *jaiJaiPayAPILogRepository) GetByRequestID(ctx context.Context, requestID string) (*domain.JaiJaiPayAPILog, error) {
	query := fmt.Sprintf(`
		SELECT id, request_id, api_category, method, endpoint, request_body,
			   request_headers, response_status_code, response_body, response_time_ms,
			   success, error_message, business_data, created_at, updated_at
		FROM %s WHERE request_id = $1
	`, dbutil.TableName("jaijaipay_api_logs"))

	apiLog := &domain.JaiJaiPayAPILog{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, requestID).Scan(
		&apiLog.ID,
		&apiLog.RequestID,
		&apiLog.APICategory,
		&apiLog.Method,
		&apiLog.Endpoint,
		&apiLog.RequestBody,
		&apiLog.RequestHeaders,
		&apiLog.ResponseStatusCode,
		&apiLog.ResponseBody,
		&apiLog.ResponseTimeMs,
		&apiLog.Success,
		&apiLog.ErrorMessage,
		&apiLog.BusinessData,
		&apiLog.CreatedAt,
		&apiLog.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to get jaijaipay api log by request id")
		return nil, errors.NewNotFoundError("jaijaipay api log not found")
	}

	return apiLog, nil
}

func (r *jaiJaiPayAPILogRepository) GetByAPICategory(ctx context.Context, category string, limit, offset int) ([]*domain.JaiJaiPayAPILog, error) {
	query := fmt.Sprintf(`
		SELECT id, request_id, api_category, method, endpoint, request_body,
			   request_headers, response_status_code, response_body, response_time_ms,
			   success, error_message, business_data, created_at, updated_at
		FROM %s 
		WHERE api_category = $1 
		ORDER BY created_at DESC 
		LIMIT $2 OFFSET $3
	`, dbutil.TableName("jaijaipay_api_logs"))

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, category, limit, offset)
	if err != nil {
		r.logger.WithError(err).Error("failed to get jaijaipay api logs by category")
		return nil, errors.NewDatabaseError("failed to get jaijaipay api logs by category")
	}
	defer rows.Close()

	var apiLogs []*domain.JaiJaiPayAPILog
	for rows.Next() {
		apiLog := &domain.JaiJaiPayAPILog{}
		err := rows.Scan(
			&apiLog.ID,
			&apiLog.RequestID,
			&apiLog.APICategory,
			&apiLog.Method,
			&apiLog.Endpoint,
			&apiLog.RequestBody,
			&apiLog.RequestHeaders,
			&apiLog.ResponseStatusCode,
			&apiLog.ResponseBody,
			&apiLog.ResponseTimeMs,
			&apiLog.Success,
			&apiLog.ErrorMessage,
			&apiLog.BusinessData,
			&apiLog.CreatedAt,
			&apiLog.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan jaijaipay api log")
			return nil, errors.NewDatabaseError("failed to scan jaijaipay api log")
		}
		apiLogs = append(apiLogs, apiLog)
	}

	return apiLogs, nil
}

func (r *jaiJaiPayAPILogRepository) GetByOrderID(ctx context.Context, orderID string) ([]*domain.JaiJaiPayAPILog, error) {
	query := fmt.Sprintf(`
		SELECT id, request_id, api_category, method, endpoint, request_body,
			   request_headers, response_status_code, response_body, response_time_ms,
			   success, error_message, business_data, created_at, updated_at
		FROM %s 
		WHERE business_data->>'order_id' = $1 
		ORDER BY created_at DESC
	`, dbutil.TableName("jaijaipay_api_logs"))

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, orderID)
	if err != nil {
		r.logger.WithError(err).Error("failed to get jaijaipay api logs by order id")
		return nil, errors.NewDatabaseError("failed to get jaijaipay api logs by order id")
	}
	defer rows.Close()

	var apiLogs []*domain.JaiJaiPayAPILog
	for rows.Next() {
		apiLog := &domain.JaiJaiPayAPILog{}
		err := rows.Scan(
			&apiLog.ID,
			&apiLog.RequestID,
			&apiLog.APICategory,
			&apiLog.Method,
			&apiLog.Endpoint,
			&apiLog.RequestBody,
			&apiLog.RequestHeaders,
			&apiLog.ResponseStatusCode,
			&apiLog.ResponseBody,
			&apiLog.ResponseTimeMs,
			&apiLog.Success,
			&apiLog.ErrorMessage,
			&apiLog.BusinessData,
			&apiLog.CreatedAt,
			&apiLog.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan jaijaipay api log")
			return nil, errors.NewDatabaseError("failed to scan jaijaipay api log")
		}
		apiLogs = append(apiLogs, apiLog)
	}

	return apiLogs, nil
}

func (r *jaiJaiPayAPILogRepository) GetByTransactionID(ctx context.Context, transactionID string) ([]*domain.JaiJaiPayAPILog, error) {
	query := fmt.Sprintf(`
		SELECT id, request_id, api_category, method, endpoint, request_body,
			   request_headers, response_status_code, response_body, response_time_ms,
			   success, error_message, business_data, created_at, updated_at
		FROM %s 
		WHERE business_data->>'transaction_id' = $1 
		ORDER BY created_at DESC
	`, dbutil.TableName("jaijaipay_api_logs"))

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, transactionID)
	if err != nil {
		r.logger.WithError(err).Error("failed to get jaijaipay api logs by transaction id")
		return nil, errors.NewDatabaseError("failed to get jaijaipay api logs by transaction id")
	}
	defer rows.Close()

	var apiLogs []*domain.JaiJaiPayAPILog
	for rows.Next() {
		apiLog := &domain.JaiJaiPayAPILog{}
		err := rows.Scan(
			&apiLog.ID,
			&apiLog.RequestID,
			&apiLog.APICategory,
			&apiLog.Method,
			&apiLog.Endpoint,
			&apiLog.RequestBody,
			&apiLog.RequestHeaders,
			&apiLog.ResponseStatusCode,
			&apiLog.ResponseBody,
			&apiLog.ResponseTimeMs,
			&apiLog.Success,
			&apiLog.ErrorMessage,
			&apiLog.BusinessData,
			&apiLog.CreatedAt,
			&apiLog.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan jaijaipay api log")
			return nil, errors.NewDatabaseError("failed to scan jaijaipay api log")
		}
		apiLogs = append(apiLogs, apiLog)
	}

	return apiLogs, nil
}

func (r *jaiJaiPayAPILogRepository) GetByDateRange(ctx context.Context, startDate, endDate string, limit, offset int) ([]*domain.JaiJaiPayAPILog, error) {
	query := fmt.Sprintf(`
		SELECT id, request_id, api_category, method, endpoint, request_body,
			   request_headers, response_status_code, response_body, response_time_ms,
			   success, error_message, business_data, created_at, updated_at
		FROM %s 
		WHERE created_at >= $1 AND created_at <= $2 
		ORDER BY created_at DESC 
		LIMIT $3 OFFSET $4
	`, dbutil.TableName("jaijaipay_api_logs"))

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, startDate, endDate, limit, offset)
	if err != nil {
		r.logger.WithError(err).Error("failed to get jaijaipay api logs by date range")
		return nil, errors.NewDatabaseError("failed to get jaijaipay api logs by date range")
	}
	defer rows.Close()

	var apiLogs []*domain.JaiJaiPayAPILog
	for rows.Next() {
		apiLog := &domain.JaiJaiPayAPILog{}
		err := rows.Scan(
			&apiLog.ID,
			&apiLog.RequestID,
			&apiLog.APICategory,
			&apiLog.Method,
			&apiLog.Endpoint,
			&apiLog.RequestBody,
			&apiLog.RequestHeaders,
			&apiLog.ResponseStatusCode,
			&apiLog.ResponseBody,
			&apiLog.ResponseTimeMs,
			&apiLog.Success,
			&apiLog.ErrorMessage,
			&apiLog.BusinessData,
			&apiLog.CreatedAt,
			&apiLog.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan jaijaipay api log")
			return nil, errors.NewDatabaseError("failed to scan jaijaipay api log")
		}
		apiLogs = append(apiLogs, apiLog)
	}

	return apiLogs, nil
}

func (r *jaiJaiPayAPILogRepository) GetFailedCalls(ctx context.Context, category string, limit, offset int) ([]*domain.JaiJaiPayAPILog, error) {
	query := fmt.Sprintf(`
		SELECT id, request_id, api_category, method, endpoint, request_body,
			   request_headers, response_status_code, response_body, response_time_ms,
			   success, error_message, business_data, created_at, updated_at
		FROM %s 
		WHERE success = false AND api_category = $1 
		ORDER BY created_at DESC 
		LIMIT $2 OFFSET $3
	`, dbutil.TableName("jaijaipay_api_logs"))

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, category, limit, offset)
	if err != nil {
		r.logger.WithError(err).Error("failed to get failed jaijaipay api calls")
		return nil, errors.NewDatabaseError("failed to get failed jaijaipay api calls")
	}
	defer rows.Close()

	var apiLogs []*domain.JaiJaiPayAPILog
	for rows.Next() {
		apiLog := &domain.JaiJaiPayAPILog{}
		err := rows.Scan(
			&apiLog.ID,
			&apiLog.RequestID,
			&apiLog.APICategory,
			&apiLog.Method,
			&apiLog.Endpoint,
			&apiLog.RequestBody,
			&apiLog.RequestHeaders,
			&apiLog.ResponseStatusCode,
			&apiLog.ResponseBody,
			&apiLog.ResponseTimeMs,
			&apiLog.Success,
			&apiLog.ErrorMessage,
			&apiLog.BusinessData,
			&apiLog.CreatedAt,
			&apiLog.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan jaijaipay api log")
			return nil, errors.NewDatabaseError("failed to scan jaijaipay api log")
		}
		apiLogs = append(apiLogs, apiLog)
	}

	return apiLogs, nil
}

func (r *jaiJaiPayAPILogRepository) GetStatistics(ctx context.Context, category string, startDate, endDate string) (map[string]interface{}, error) {
	query := fmt.Sprintf(`
		SELECT 
			COUNT(*) as total_calls,
			COUNT(CASE WHEN success = true THEN 1 END) as successful_calls,
			COUNT(CASE WHEN success = false THEN 1 END) as failed_calls,
			AVG(response_time_ms) as avg_response_time,
			MIN(response_time_ms) as min_response_time,
			MAX(response_time_ms) as max_response_time
		FROM %s 
		WHERE api_category = $1 AND created_at >= $2 AND created_at <= $3
	`, dbutil.TableName("jaijaipay_api_logs"))

	var totalCalls, successfulCalls, failedCalls int64
	var avgResponseTime, minResponseTime, maxResponseTime *float64

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, category, startDate, endDate).Scan(
		&totalCalls,
		&successfulCalls,
		&failedCalls,
		&avgResponseTime,
		&minResponseTime,
		&maxResponseTime,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to get jaijaipay api statistics")
		return nil, errors.NewDatabaseError("failed to get jaijaipay api statistics")
	}

	stats := map[string]interface{}{
		"total_calls":       totalCalls,
		"successful_calls":  successfulCalls,
		"failed_calls":      failedCalls,
		"success_rate":      float64(successfulCalls) / float64(totalCalls) * 100,
		"avg_response_time": avgResponseTime,
		"min_response_time": minResponseTime,
		"max_response_time": maxResponseTime,
	}

	return stats, nil
}

func (r *jaiJaiPayAPILogRepository) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf(`DELETE FROM %s WHERE id = $1`, dbutil.TableName("jaijaipay_api_logs"))

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete jaijaipay api log")
		return errors.NewDatabaseError("failed to delete jaijaipay api log")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("jaijaipay api log not found")
	}

	return nil
}

func (r *jaiJaiPayAPILogRepository) DeleteOldLogs(ctx context.Context, olderThanDays int) (int64, error) {
	query := fmt.Sprintf(`
		DELETE FROM %s 
		WHERE created_at < NOW() - INTERVAL '%d days'
	`, dbutil.TableName("jaijaipay_api_logs"), olderThanDays)

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete old jaijaipay api logs")
		return 0, errors.NewDatabaseError("failed to delete old jaijaipay api logs")
	}

	return result.RowsAffected(), nil
}
