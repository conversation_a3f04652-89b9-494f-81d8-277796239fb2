package postgres

import (
	"context"
	"database/sql"
	"fmt"

	"blacking-api/internal/domain/member_group_type"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type MemberGroupTypeRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewMemberGroupTypeRepository creates a new member group type repository
func NewMemberGroupTypeRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.MemberGroupTypeRepository {
	return &MemberGroupTypeRepository{
		pool:   pool,
		logger: logger,
	}
}

// C<PERSON> creates a new member group type
func (r *MemberGroupTypeRepository) Create(ctx context.Context, mgt *member_group_type.MemberGroupType) error {
	query := `
		INSERT INTO member_group_types (
			name, position, show_in_lobby, badge_bg_color, badge_border_color, image1, image2, bg_image,
			vip_enabled, vip_personal_customer_service, vip_max_daily_withdraw, vip_event_participation,
			bonus_enabled, bonus_birthday, bonus_level_maintenance, bonus_level_upgrade, bonus_festival,
			cashback_enabled, cashback_sports, cashback_casino, cashback_fishing, cashback_slot, 
			cashback_lottery, cashback_card, cashback_other,
			upgrade_enable, upgrade_betting_amount, upgrade_deposit_amount, upgrade_calculation_type, upgrade_days, upgrade_condition_type,
			downgrade_enable, downgrade_betting_amount, downgrade_deposit_amount, downgrade_calculation_type, downgrade_days, downgrade_condition_type,
			status, created_at, updated_at
		)
		VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19,
			$20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40
		)
		RETURNING id
	`

	var lastInsertID int
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		mgt.Name, mgt.Position, mgt.ShowInLobby, mgt.BadgeBgColor, mgt.BadgeBorderColor, mgt.Image1, mgt.Image2, mgt.BgImage,
		mgt.VipEnabled, mgt.VipPersonalCustomerService, mgt.VipMaxDailyWithdraw, mgt.VipEventParticipation,
		mgt.BonusEnabled, mgt.BonusBirthday, mgt.BonusLevelMaintenance, mgt.BonusLevelUpgrade, mgt.BonusFestival,
		mgt.CashbackEnabled, mgt.CashbackSports, mgt.CashbackCasino, mgt.CashbackFishing, mgt.CashbackSlot,
		mgt.CashbackLottery, mgt.CashbackCard, mgt.CashbackOther,
		mgt.UpgradeEnable, mgt.UpgradeBettingAmount, mgt.UpgradeDepositAmount, mgt.UpgradeCalculationType, mgt.UpgradeDays, mgt.UpgradeConditionType,
		mgt.DowngradeEnable, mgt.DowngradeBettingAmount, mgt.DowngradeDepositAmount, mgt.DowngradeCalculationType, mgt.DowngradeDays, mgt.DowngradeConditionType,
		mgt.Status, mgt.CreatedAt, mgt.UpdatedAt,
	).Scan(&lastInsertID)

	if err != nil {
		r.logger.WithError(err).Error("failed to create member group type")
		return errors.NewDatabaseError("failed to create member group type")
	}
	updatePositionQuery := `UPDATE member_group_types SET position = id WHERE id = $1`
	_, err = dbutil.ExecWithSchema(ctx, r.pool, updatePositionQuery, lastInsertID)
	if err != nil {
		r.logger.WithError(err).Error("failed to set position user_role")
	}
	mgt.Position = lastInsertID

	mgt.ID = lastInsertID
	r.logger.WithField("member_group_type_id", mgt.ID).Info("member group type created successfully")
	return nil
}

// GetByID retrieves member group type by ID
func (r *MemberGroupTypeRepository) GetByID(ctx context.Context, id int) (*member_group_type.MemberGroupType, error) {
	query := `
		SELECT id, name, position, show_in_lobby, badge_bg_color, badge_border_color, image1, image2, bg_image,
			   vip_enabled, vip_personal_customer_service, vip_max_daily_withdraw, vip_event_participation,
			   bonus_enabled, bonus_birthday, bonus_level_maintenance, bonus_level_upgrade, bonus_festival,
			   cashback_enabled, cashback_sports, cashback_casino, cashback_fishing, cashback_slot,
			   cashback_lottery, cashback_card, cashback_other,
			   upgrade_enable, upgrade_betting_amount, upgrade_deposit_amount, upgrade_calculation_type, upgrade_days, upgrade_condition_type,
			   downgrade_enable, downgrade_betting_amount, downgrade_deposit_amount, downgrade_calculation_type, downgrade_days, downgrade_condition_type,
			   status, created_at, updated_at
		FROM member_group_types
		WHERE id = $1 AND status = 'active'
	`

	mgt := &member_group_type.MemberGroupType{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&mgt.ID, &mgt.Name, &mgt.Position, &mgt.ShowInLobby, &mgt.BadgeBgColor, &mgt.BadgeBorderColor, &mgt.Image1, &mgt.Image2, &mgt.BgImage,
		&mgt.VipEnabled, &mgt.VipPersonalCustomerService, &mgt.VipMaxDailyWithdraw, &mgt.VipEventParticipation,
		&mgt.BonusEnabled, &mgt.BonusBirthday, &mgt.BonusLevelMaintenance, &mgt.BonusLevelUpgrade, &mgt.BonusFestival,
		&mgt.CashbackEnabled, &mgt.CashbackSports, &mgt.CashbackCasino, &mgt.CashbackFishing, &mgt.CashbackSlot,
		&mgt.CashbackLottery, &mgt.CashbackCard, &mgt.CashbackOther,
		&mgt.UpgradeEnable, &mgt.UpgradeBettingAmount, &mgt.UpgradeDepositAmount, &mgt.UpgradeCalculationType, &mgt.UpgradeDays, &mgt.UpgradeConditionType,
		&mgt.DowngradeEnable, &mgt.DowngradeBettingAmount, &mgt.DowngradeDepositAmount, &mgt.DowngradeCalculationType, &mgt.DowngradeDays, &mgt.DowngradeConditionType,
		&mgt.Status, &mgt.CreatedAt, &mgt.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("member group type not found")
		}
		r.logger.WithError(err).Error("failed to get member group type by ID")
		return nil, errors.NewDatabaseError("failed to get member group type")
	}

	return mgt, nil
}

// GetByName retrieves member group type by name
func (r *MemberGroupTypeRepository) GetByName(ctx context.Context, name string) (*member_group_type.MemberGroupType, error) {
	query := `
		SELECT id, name, position, show_in_lobby, badge_bg_color, badge_border_color, image1, image2, bg_image,
			   vip_enabled, vip_personal_customer_service, vip_max_daily_withdraw, vip_event_participation,
			   bonus_enabled, bonus_birthday, bonus_level_maintenance, bonus_level_upgrade, bonus_festival,
			   cashback_enabled, cashback_sports, cashback_casino, cashback_fishing, cashback_slot,
			   cashback_lottery, cashback_card, cashback_other,
			   upgrade_betting_amount, upgrade_deposit_amount, upgrade_calculation_type, upgrade_days, upgrade_condition_type,
			   downgrade_betting_amount, downgrade_deposit_amount, downgrade_calculation_type, downgrade_days, downgrade_condition_type,
			   status, created_at, updated_at
		FROM member_group_types
		WHERE name = $1 AND status = 'active'
	`

	mgt := &member_group_type.MemberGroupType{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, name).Scan(
		&mgt.ID, &mgt.Name, &mgt.Position, &mgt.ShowInLobby, &mgt.BadgeBgColor, &mgt.BadgeBorderColor, &mgt.Image1, &mgt.Image2, &mgt.BgImage,
		&mgt.VipEnabled, &mgt.VipPersonalCustomerService, &mgt.VipMaxDailyWithdraw, &mgt.VipEventParticipation,
		&mgt.BonusEnabled, &mgt.BonusBirthday, &mgt.BonusLevelMaintenance, &mgt.BonusLevelUpgrade, &mgt.BonusFestival,
		&mgt.CashbackEnabled, &mgt.CashbackSports, &mgt.CashbackCasino, &mgt.CashbackFishing, &mgt.CashbackSlot,
		&mgt.CashbackLottery, &mgt.CashbackCard, &mgt.CashbackOther,
		&mgt.UpgradeBettingAmount, &mgt.UpgradeDepositAmount, &mgt.UpgradeCalculationType, &mgt.UpgradeDays, &mgt.UpgradeConditionType,
		&mgt.DowngradeBettingAmount, &mgt.DowngradeDepositAmount, &mgt.DowngradeCalculationType, &mgt.DowngradeDays, &mgt.DowngradeConditionType,
		&mgt.Status, &mgt.CreatedAt, &mgt.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("member group type not found")
		}
		r.logger.WithError(err).Error("failed to get member group type by name")
		return nil, errors.NewDatabaseError("failed to get member group type")
	}

	return mgt, nil
}

// Update updates an existing member group type
func (r *MemberGroupTypeRepository) Update(ctx context.Context, mgt *member_group_type.MemberGroupType) error {
	query := `
		UPDATE member_group_types
		SET name = $2, position = $3, show_in_lobby = $4, badge_bg_color = $5, badge_border_color = $6,
			image1 = $7, image2 = $8, bg_image = $9,
			vip_enabled = $10, vip_personal_customer_service = $11, vip_max_daily_withdraw = $12, vip_event_participation = $13,
			bonus_enabled = $14, bonus_birthday = $15, bonus_level_maintenance = $16, bonus_level_upgrade = $17, bonus_festival = $18,
			cashback_enabled = $19, cashback_sports = $20, cashback_casino = $21, cashback_fishing = $22, cashback_slot = $23,
			cashback_lottery = $24, cashback_card = $25, cashback_other = $26,
			upgrade_enable = $27, upgrade_betting_amount = $28, upgrade_deposit_amount = $29, upgrade_calculation_type = $30, upgrade_days = $31, upgrade_condition_type = $32,
			downgrade_enable = $33, downgrade_betting_amount = $34, downgrade_deposit_amount = $35, downgrade_calculation_type = $36, downgrade_days = $37, downgrade_condition_type = $38,
			updated_at = $39
		WHERE id = $1 AND status = 'active'
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		mgt.ID, mgt.Name, mgt.Position, mgt.ShowInLobby, mgt.BadgeBgColor, mgt.BadgeBorderColor, mgt.Image1, mgt.Image2, mgt.BgImage,
		mgt.VipEnabled, mgt.VipPersonalCustomerService, mgt.VipMaxDailyWithdraw, mgt.VipEventParticipation,
		mgt.BonusEnabled, mgt.BonusBirthday, mgt.BonusLevelMaintenance, mgt.BonusLevelUpgrade, mgt.BonusFestival,
		mgt.CashbackEnabled, mgt.CashbackSports, mgt.CashbackCasino, mgt.CashbackFishing, mgt.CashbackSlot,
		mgt.CashbackLottery, mgt.CashbackCard, mgt.CashbackOther,
		mgt.UpgradeEnable, mgt.UpgradeBettingAmount, mgt.UpgradeDepositAmount, mgt.UpgradeCalculationType, mgt.UpgradeDays, mgt.UpgradeConditionType,
		mgt.DowngradeEnable, mgt.DowngradeBettingAmount, mgt.DowngradeDepositAmount, mgt.DowngradeCalculationType, mgt.DowngradeDays, mgt.DowngradeConditionType,
		mgt.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to update member group type")
		return errors.NewDatabaseError("failed to update member group type")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("member group type not found")
	}

	r.logger.WithField("member_group_type_id", mgt.ID).Info("member group type updated successfully")
	return nil
}

// Delete soft deletes a member group type (sets status to inactive)
func (r *MemberGroupTypeRepository) Delete(ctx context.Context, id int) error {
	query := `
		UPDATE member_group_types
		SET status = 'inactive', updated_at = CURRENT_TIMESTAMP
		WHERE id = $1 AND status = 'active'
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete member group type")
		return errors.NewDatabaseError("failed to delete member group type")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("member group type not found")
	}

	r.logger.WithField("member_group_type_id", id).Info("member group type deleted successfully")
	return nil
}

// List retrieves member group types with pagination, search, and sorting
func (r *MemberGroupTypeRepository) List(ctx context.Context, limit, offset int, search, sortBy, sortOrder string) ([]*member_group_type.MemberGroupType, error) {
	// Build base query
	baseQuery := `
		SELECT id, name, position, show_in_lobby, badge_bg_color, badge_border_color, image1, image2, bg_image,
			   vip_enabled, vip_personal_customer_service, vip_max_daily_withdraw, vip_event_participation,
			   bonus_enabled, bonus_birthday, bonus_level_maintenance, bonus_level_upgrade, bonus_festival,
			   cashback_enabled, cashback_sports, cashback_casino, cashback_fishing, cashback_slot,
			   cashback_lottery, cashback_card, cashback_other,
			   upgrade_enable, upgrade_betting_amount, upgrade_deposit_amount, upgrade_calculation_type, upgrade_days, upgrade_condition_type,
			   downgrade_enable, downgrade_betting_amount, downgrade_deposit_amount, downgrade_calculation_type, downgrade_days, downgrade_condition_type,
			   status, created_at, updated_at
		FROM member_group_types
		WHERE status = 'active'
	`

	var args []interface{}
	argIndex := 1

	// Add search condition
	if search != "" {
		baseQuery += fmt.Sprintf(" AND name ILIKE $%d", argIndex)
		args = append(args, "%"+search+"%")
		argIndex++
	}

	// Add sorting
	validSortColumns := map[string]bool{
		"id":         true,
		"name":       true,
		"position":   true,
		"created_at": true,
		"updated_at": true,
	}

	if sortBy != "" && validSortColumns[sortBy] {
		if sortOrder == "desc" {
			baseQuery += fmt.Sprintf(" ORDER BY %s DESC", sortBy)
		} else {
			baseQuery += fmt.Sprintf(" ORDER BY %s ASC", sortBy)
		}
	} else {
		baseQuery += " ORDER BY position ASC" // Default sort by position
	}

	// Add pagination
	baseQuery += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, offset)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, baseQuery, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list member group types")
		return nil, errors.NewDatabaseError("failed to list member group types")
	}
	defer rows.Close()

	var memberGroupTypes []*member_group_type.MemberGroupType
	for rows.Next() {
		mgt := &member_group_type.MemberGroupType{}
		err := rows.Scan(
			&mgt.ID, &mgt.Name, &mgt.Position, &mgt.ShowInLobby, &mgt.BadgeBgColor, &mgt.BadgeBorderColor, &mgt.Image1, &mgt.Image2, &mgt.BgImage,
			&mgt.VipEnabled, &mgt.VipPersonalCustomerService, &mgt.VipMaxDailyWithdraw, &mgt.VipEventParticipation,
			&mgt.BonusEnabled, &mgt.BonusBirthday, &mgt.BonusLevelMaintenance, &mgt.BonusLevelUpgrade, &mgt.BonusFestival,
			&mgt.CashbackEnabled, &mgt.CashbackSports, &mgt.CashbackCasino, &mgt.CashbackFishing, &mgt.CashbackSlot,
			&mgt.CashbackLottery, &mgt.CashbackCard, &mgt.CashbackOther,
			&mgt.UpgradeEnable, &mgt.UpgradeBettingAmount, &mgt.UpgradeDepositAmount, &mgt.UpgradeCalculationType, &mgt.UpgradeDays, &mgt.UpgradeConditionType,
			&mgt.DowngradeEnable, &mgt.DowngradeBettingAmount, &mgt.DowngradeDepositAmount, &mgt.DowngradeCalculationType, &mgt.DowngradeDays, &mgt.DowngradeConditionType,
			&mgt.Status, &mgt.CreatedAt, &mgt.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan member group type")
			return nil, errors.NewDatabaseError("failed to scan member group type")
		}
		memberGroupTypes = append(memberGroupTypes, mgt)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating member group type rows")
		return nil, errors.NewDatabaseError("error iterating member group type rows")
	}

	return memberGroupTypes, nil
}

// Count returns total count of member group types with search filter
func (r *MemberGroupTypeRepository) Count(ctx context.Context, search string) (int64, error) {
	query := "SELECT COUNT(*) FROM member_group_types WHERE status = 'active'"
	var args []interface{}

	if search != "" {
		query += " AND name ILIKE $1"
		args = append(args, "%"+search+"%")
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count member group types")
		return 0, errors.NewDatabaseError("failed to count member group types")
	}

	return count, nil
}

// ListActive retrieves only active member group types
func (r *MemberGroupTypeRepository) ListActive(ctx context.Context) ([]*member_group_type.MemberGroupType, error) {
	query := `
		SELECT id, name, position, show_in_lobby, badge_bg_color, badge_border_color, image1, image2, bg_image,
			   vip_enabled, vip_personal_customer_service, vip_max_daily_withdraw, vip_event_participation,
			   bonus_enabled, bonus_birthday, bonus_level_maintenance, bonus_level_upgrade, bonus_festival,
			   cashback_enabled, cashback_sports, cashback_casino, cashback_fishing, cashback_slot,
			   cashback_lottery, cashback_card, cashback_other,
			   upgrade_betting_amount, upgrade_deposit_amount, upgrade_calculation_type, upgrade_days, upgrade_condition_type,
			   downgrade_betting_amount, downgrade_deposit_amount, downgrade_calculation_type, downgrade_days, downgrade_condition_type,
			   status, created_at, updated_at
		FROM member_group_types
		WHERE status = 'active'
		ORDER BY position ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to list active member group types")
		return nil, errors.NewDatabaseError("failed to list active member group types")
	}
	defer rows.Close()

	var memberGroupTypes []*member_group_type.MemberGroupType
	for rows.Next() {
		mgt := &member_group_type.MemberGroupType{}
		err := rows.Scan(
			&mgt.ID, &mgt.Name, &mgt.Position, &mgt.ShowInLobby, &mgt.BadgeBgColor, &mgt.BadgeBorderColor, &mgt.Image1, &mgt.Image2, &mgt.BgImage,
			&mgt.VipEnabled, &mgt.VipPersonalCustomerService, &mgt.VipMaxDailyWithdraw, &mgt.VipEventParticipation,
			&mgt.BonusEnabled, &mgt.BonusBirthday, &mgt.BonusLevelMaintenance, &mgt.BonusLevelUpgrade, &mgt.BonusFestival,
			&mgt.CashbackEnabled, &mgt.CashbackSports, &mgt.CashbackCasino, &mgt.CashbackFishing, &mgt.CashbackSlot,
			&mgt.CashbackLottery, &mgt.CashbackCard, &mgt.CashbackOther,
			&mgt.UpgradeEnable, &mgt.UpgradeBettingAmount, &mgt.UpgradeDepositAmount, &mgt.UpgradeCalculationType, &mgt.UpgradeDays, &mgt.UpgradeConditionType,
			&mgt.DowngradeEnable, &mgt.DowngradeBettingAmount, &mgt.DowngradeDepositAmount, &mgt.DowngradeCalculationType, &mgt.DowngradeDays, &mgt.DowngradeConditionType,
			&mgt.Status, &mgt.CreatedAt, &mgt.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan active member group type")
			return nil, errors.NewDatabaseError("failed to scan active member group type")
		}
		memberGroupTypes = append(memberGroupTypes, mgt)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating active member group type rows")
		return nil, errors.NewDatabaseError("error iterating active member group type rows")
	}

	return memberGroupTypes, nil
}

// ListForDropdown retrieves member group types for dropdown filter
func (r *MemberGroupTypeRepository) ListForDropdown(ctx context.Context) ([]*member_group_type.MemberGroupTypeDropdownResponse, error) {
	query := `
		SELECT id, name
		FROM member_group_types
		WHERE status = 'active'
		ORDER BY position ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to list member group types for dropdown")
		return nil, errors.NewDatabaseError("failed to list member group types for dropdown")
	}
	defer rows.Close()

	var dropdownItems []*member_group_type.MemberGroupTypeDropdownResponse
	for rows.Next() {
		item := &member_group_type.MemberGroupTypeDropdownResponse{}
		err := rows.Scan(&item.ID, &item.Name)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan member group type dropdown item")
			return nil, errors.NewDatabaseError("failed to scan member group type dropdown item")
		}
		dropdownItems = append(dropdownItems, item)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating member group type dropdown rows")
		return nil, errors.NewDatabaseError("error iterating member group type dropdown rows")
	}

	return dropdownItems, nil
}

// ReorderUp moves a member group type up in position
func (r *MemberGroupTypeRepository) ReorderUp(ctx context.Context, id int) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction")
		return errors.NewDatabaseError("failed to reorder member group type")
	}
	defer tx.Rollback(ctx)

	// Get current position
	var currentPosition int
	err = dbutil.TxQueryRowWithSchema(ctx, tx, "SELECT position FROM member_group_types WHERE id = $1 AND status = 'active'", id).Scan(&currentPosition)
	if err != nil {
		if err == pgx.ErrNoRows {
			return errors.NewNotFoundError("member group type not found")
		}
		r.logger.WithError(err).Error("failed to get current position")
		return errors.NewDatabaseError("failed to reorder member group type")
	}

	// Find the item above (with position < currentPosition, ordered by position DESC to get the closest one)
	var aboveID int
	var abovePosition int
	err = dbutil.TxQueryRowWithSchema(ctx, tx,
		"SELECT id, position FROM member_group_types WHERE position < $1 AND status = 'active' ORDER BY position DESC LIMIT 1",
		currentPosition).Scan(&aboveID, &abovePosition)
	if err != nil {
		if err == pgx.ErrNoRows {
			return errors.NewValidationError("cannot move up, already at top")
		}
		r.logger.WithError(err).Error("failed to find item above")
		return errors.NewDatabaseError("failed to reorder member group type")
	}

	// Swap positions
	_, err = dbutil.TxExecWithSchema(ctx, tx, "UPDATE member_group_types SET position = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2", abovePosition, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to update current item position")
		return errors.NewDatabaseError("failed to reorder member group type")
	}

	_, err = dbutil.TxExecWithSchema(ctx, tx, "UPDATE member_group_types SET position = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2", currentPosition, aboveID)
	if err != nil {
		r.logger.WithError(err).Error("failed to update above item position")
		return errors.NewDatabaseError("failed to reorder member group type")
	}

	if err := tx.Commit(ctx); err != nil {
		r.logger.WithError(err).Error("failed to commit transaction")
		return errors.NewDatabaseError("failed to reorder member group type")
	}

	r.logger.WithField("member_group_type_id", id).Info("member group type moved up successfully")
	return nil
}

// ReorderDown moves a member group type down in position
func (r *MemberGroupTypeRepository) ReorderDown(ctx context.Context, id int) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction")
		return errors.NewDatabaseError("failed to reorder member group type")
	}
	defer tx.Rollback(ctx)

	// Get current position
	var currentPosition int
	err = dbutil.TxQueryRowWithSchema(ctx, tx, "SELECT position FROM member_group_types WHERE id = $1 AND status = 'active'", id).Scan(&currentPosition)
	if err != nil {
		if err == pgx.ErrNoRows {
			return errors.NewNotFoundError("member group type not found")
		}
		r.logger.WithError(err).Error("failed to get current position")
		return errors.NewDatabaseError("failed to reorder member group type")
	}

	// Find the item below (with position > currentPosition, ordered by position ASC to get the closest one)
	var belowID int
	var belowPosition int
	err = dbutil.TxQueryRowWithSchema(ctx, tx,
		"SELECT id, position FROM member_group_types WHERE position > $1 AND status = 'active' ORDER BY position ASC LIMIT 1",
		currentPosition).Scan(&belowID, &belowPosition)
	if err != nil {
		if err == pgx.ErrNoRows {
			return errors.NewValidationError("cannot move down, already at bottom")
		}
		r.logger.WithError(err).Error("failed to find item below")
		return errors.NewDatabaseError("failed to reorder member group type")
	}

	// Swap positions
	_, err = dbutil.TxExecWithSchema(ctx, tx, "UPDATE member_group_types SET position = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2", belowPosition, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to update current item position")
		return errors.NewDatabaseError("failed to reorder member group type")
	}

	_, err = dbutil.TxExecWithSchema(ctx, tx, "UPDATE member_group_types SET position = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2", currentPosition, belowID)
	if err != nil {
		r.logger.WithError(err).Error("failed to update below item position")
		return errors.NewDatabaseError("failed to reorder member group type")
	}

	if err := tx.Commit(ctx); err != nil {
		r.logger.WithError(err).Error("failed to commit transaction")
		return errors.NewDatabaseError("failed to reorder member group type")
	}

	r.logger.WithField("member_group_type_id", id).Info("member group type moved down successfully")
	return nil
}

// GetNextPosition gets the next available position for new member group type
func (r *MemberGroupTypeRepository) GetNextPosition(ctx context.Context) (int, error) {
	var maxPosition sql.NullInt64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, "SELECT MAX(position) FROM member_group_types WHERE status = 'active'").Scan(&maxPosition)
	if err != nil {
		r.logger.WithError(err).Error("failed to get max position")
		return 0, errors.NewDatabaseError("failed to get next position")
	}

	if maxPosition.Valid {
		return int(maxPosition.Int64) + 1, nil
	}
	return 1, nil // First item
}
