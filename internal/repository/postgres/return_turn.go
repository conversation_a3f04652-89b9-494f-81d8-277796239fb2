package postgres

import (
	"blacking-api/internal/domain/return_turn"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type ReturnTurnRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewReturnTurnRepository creates a new return turn repository
func NewReturnTurnRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.ReturnTurnRepository {
	return &ReturnTurnRepository{
		pool:   pool,
		logger: logger,
	}
}

// GetDB returns the database pool
func (r *ReturnTurnRepository) GetDB() *pgxpool.Pool {
	return r.pool
}

// BeginTx begins a new transaction
func (r *ReturnTurnRepository) BeginTx(ctx context.Context) (pgx.Tx, error) {
	return r.pool.Begin(ctx)
}

// GetReturnSetting retrieves the current return turn setting
func (r *ReturnTurnRepository) GetReturnSetting(ctx context.Context) (*return_turn.ReturnTurnSettingResponse, error) {
	query := `
		SELECT id, return_percent, return_type_id, cut_type_id, min_loss_price, 
		       max_return_price, detail, is_enabled, credit_expire_days, created_at, updated_at
		FROM return_turn_setting
		WHERE id = 1
	`

	var setting return_turn.ReturnTurnSettingResponse
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query).Scan(
		&setting.ID,
		&setting.ReturnPercent,
		&setting.ReturnTypeID,
		&setting.CutTypeID,
		&setting.MinLossPrice,
		&setting.MaxReturnPrice,
		&setting.Detail,
		&setting.IsEnabled,
		&setting.CreditExpireDays,
		&setting.CreatedAt,
		&setting.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			// Create default setting if not exists
			defaultSetting := return_turn.ReturnTurnSetting{
				ID:               1,
				ReturnPercent:    0,
				ReturnTypeID:     1,
				CutTypeID:        1,
				MinLossPrice:     0,
				MaxReturnPrice:   0,
				Detail:           "",
				IsEnabled:        false,
				CreditExpireDays: 7,
			}
			_, err = r.CreateReturnSetting(ctx, defaultSetting)
			if err != nil {
				return nil, err
			}
			return r.GetReturnSetting(ctx)
		}
		r.logger.WithError(err).Error("failed to get promotion return setting")
		return nil, errors.NewDatabaseError("failed to get promotion return setting")
	}

	// Get calculate types
	setting.CalculateTypes, _ = r.GetPromotionCalculateTypes(ctx, setting.ID)
	setting.CacheExpiredAt = time.Now().Add(60 * time.Minute)

	return &setting, nil
}

// CreateReturnSetting creates a new promotion return setting
func (r *ReturnTurnRepository) CreateReturnSetting(ctx context.Context, body return_turn.ReturnTurnSetting) (*int64, error) {
	query := `
		INSERT INTO return_turn_setting 
		(id, return_percent, return_type_id, cut_type_id, min_loss_price, max_return_price, detail, is_enabled, credit_expire_days)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		ON CONFLICT (id) DO UPDATE SET
		return_percent = EXCLUDED.return_percent,
		return_type_id = EXCLUDED.return_type_id,
		cut_type_id = EXCLUDED.cut_type_id,
		min_loss_price = EXCLUDED.min_loss_price,
		max_return_price = EXCLUDED.max_return_price,
		detail = EXCLUDED.detail,
		is_enabled = EXCLUDED.is_enabled,
		credit_expire_days = EXCLUDED.credit_expire_days,
		updated_at = NOW()
		RETURNING id
	`

	var id int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		body.ID,
		body.ReturnPercent,
		body.ReturnTypeID,
		body.CutTypeID,
		body.MinLossPrice,
		body.MaxReturnPrice,
		body.Detail,
		body.IsEnabled,
		body.CreditExpireDays,
	).Scan(&id)

	if err != nil {
		r.logger.WithError(err).Error("failed to create promotion return setting")
		return nil, errors.NewDatabaseError("failed to create promotion return setting")
	}

	return &id, nil
}

// UpdateReturnSetting updates the promotion return setting
func (r *ReturnTurnRepository) UpdateReturnSetting(ctx context.Context, id int64, body return_turn.ReturnTurnSettingUpdateRequest) error {
	var setClauses []string
	var args []interface{}
	argIndex := 1

	if body.ReturnPercent != nil {
		setClauses = append(setClauses, fmt.Sprintf("return_percent = $%d", argIndex))
		args = append(args, *body.ReturnPercent)
		argIndex++
	}
	if body.ReturnTypeID != nil {
		setClauses = append(setClauses, fmt.Sprintf("return_type_id = $%d", argIndex))
		args = append(args, *body.ReturnTypeID)
		argIndex++
	}
	if body.CutTypeID != nil {
		setClauses = append(setClauses, fmt.Sprintf("cut_type_id = $%d", argIndex))
		args = append(args, *body.CutTypeID)
		argIndex++
	}
	if body.MinLossPrice != nil {
		setClauses = append(setClauses, fmt.Sprintf("min_loss_price = $%d", argIndex))
		args = append(args, *body.MinLossPrice)
		argIndex++
	}
	if body.MaxReturnPrice != nil {
		setClauses = append(setClauses, fmt.Sprintf("max_return_price = $%d", argIndex))
		args = append(args, *body.MaxReturnPrice)
		argIndex++
	}
	if body.Detail != nil {
		setClauses = append(setClauses, fmt.Sprintf("detail = $%d", argIndex))
		args = append(args, *body.Detail)
		argIndex++
	}
	if body.IsEnabled != nil {
		setClauses = append(setClauses, fmt.Sprintf("is_enabled = $%d", argIndex))
		args = append(args, *body.IsEnabled)
		argIndex++
	}
	if body.CreditExpireDays != nil {
		setClauses = append(setClauses, fmt.Sprintf("credit_expire_days = $%d", argIndex))
		args = append(args, *body.CreditExpireDays)
		argIndex++
	}

	if len(setClauses) == 0 {
		return nil
	}

	setClauses = append(setClauses, "updated_at = NOW()")
	args = append(args, id)

	query := fmt.Sprintf(`
		UPDATE return_turn_setting
		SET %s
		WHERE id = $%d
	`, strings.Join(setClauses, ", "), argIndex)

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to update promotion return setting")
		return errors.NewDatabaseError("failed to update promotion return setting")
	}

	// Update calculate types if provided
	if len(body.CalculateTypes) > 0 {
		return r.UpdatePromotionCalculateTypes(ctx, id, body.CalculateTypes)
	}

	return nil
}

// GetCalculatePlayTypes retrieves all calculate play types
func (r *ReturnTurnRepository) GetCalculatePlayTypes(ctx context.Context) ([]return_turn.CalculatePlayType, error) {
	query := `
		SELECT id, name, created_at
		FROM calculate_play_type
		ORDER BY id
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to get calculate play types")
		return nil, errors.NewDatabaseError("failed to get calculate play types")
	}
	defer rows.Close()

	var types []return_turn.CalculatePlayType
	for rows.Next() {
		var t return_turn.CalculatePlayType
		err := rows.Scan(&t.ID, &t.Name, &t.CreatedAt)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan calculate play type")
			continue
		}
		types = append(types, t)
	}

	return types, nil
}

// GetPromotionCalculateTypes retrieves calculate types for a return turn setting
func (r *ReturnTurnRepository) GetPromotionCalculateTypes(ctx context.Context, returnSettingId int64) ([]int64, error) {
	query := `
		SELECT calculate_type_id
		FROM return_turn_calculate_type
		WHERE return_setting_id = $1
		ORDER BY calculate_type_id
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, returnSettingId)
	if err != nil {
		r.logger.WithError(err).Error("failed to get promotion calculate types")
		return nil, errors.NewDatabaseError("failed to get promotion calculate types")
	}
	defer rows.Close()

	var typeIds []int64
	for rows.Next() {
		var id int64
		err := rows.Scan(&id)
		if err != nil {
			continue
		}
		typeIds = append(typeIds, id)
	}

	return typeIds, nil
}

// UpdatePromotionCalculateTypes updates the calculate types for a return turn setting
func (r *ReturnTurnRepository) UpdatePromotionCalculateTypes(ctx context.Context, returnSettingId int64, typeIds []int64) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		return errors.NewDatabaseError("failed to begin transaction")
	}
	defer tx.Rollback(ctx)

	// Delete existing types
	deleteQuery := `DELETE FROM return_turn_calculate_type WHERE return_setting_id = $1`
	_, err = dbutil.TxExecWithSchema(ctx, tx, deleteQuery, returnSettingId)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete existing calculate types")
		return errors.NewDatabaseError("failed to delete existing calculate types")
	}

	// Insert new types
	for _, typeId := range typeIds {
		insertQuery := `
			INSERT INTO return_turn_calculate_type (return_setting_id, calculate_type_id)
			VALUES ($1, $2)
		`
		_, err = dbutil.TxExecWithSchema(ctx, tx, insertQuery, returnSettingId, typeId)
		if err != nil {
			r.logger.WithError(err).Error("failed to insert calculate type")
			return errors.NewDatabaseError("failed to insert calculate type")
		}
	}

	return tx.Commit(ctx)
}

// GetCurrentReturnTransactionList retrieves current return transactions for a user
func (r *ReturnTurnRepository) GetCurrentReturnTransactionList(ctx context.Context, userId int64) ([]return_turn.ReturnTurnLoserResponse, error) {
	query := `
		SELECT 
			prl.id, prl.member_code, prl.member_code as username, '' as fullname,
			prl.status_id, prls.name as status_name,
			prl.daily_key, prl.of_date,
			prl.total_loss_amount, prl.total_loss_live_casino, prl.total_loss_slot, prl.total_loss_sport,
			prl.return_percent, prl.game_detail,
			prl.return_type_id, prl.cut_type_id, prct.name as cut_type_name,
			prl.min_loss_price, prl.max_return_price, prl.return_price_live_casino, prl.return_price_slot, prl.return_price_sport,
			prl.created_at + INTERVAL '7 days' as credit_expire_at,
			prl.calc_at, prl.take_at_live_casino, prl.taken_price_live_casino, prl.take_at_slot, prl.taken_price_slot, prl.taken_at_sport, prl.taken_price_sport,
			prl.created_at, prl.updated_at
		FROM return_turn_loser prl
		LEFT JOIN return_turn_loser_status prls ON prls.id = prl.status_id
		LEFT JOIN return_turn_cut_type prct ON prct.id = prl.cut_type_id
		WHERE prl.member_code = (SELECT COALESCE(game_username, username) FROM members WHERE id = $1) AND prl.status_id = $2
		ORDER BY prl.of_date DESC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, userId, return_turn.RETURN_TURN_STATUS_READY)
	if err != nil {
		r.logger.WithError(err).Error("failed to get current return transactions")
		return nil, errors.NewDatabaseError("failed to get current return transactions")
	}
	defer rows.Close()

	var transactions []return_turn.ReturnTurnLoserResponse
	for rows.Next() {
		var t return_turn.ReturnTurnLoserResponse
		var creditExpireAt sql.NullTime
		err := rows.Scan(
			&t.ID, &t.MemberCode, &t.Username, &t.Fullname,
			&t.StatusID, &t.StatusName,
			&t.DailyKey, &t.OfDate,
			&t.TotalLossAmount, &t.TotalLossLiveCasino, &t.TotalLossSlot, &t.TotalLossSport,
			&t.ReturnPercent, &t.GameDetail,
			&t.ReturnTypeID, &t.CutTypeID, &t.CutTypeName,
			&t.MinLossPrice, &t.MaxReturnPrice, &t.ReturnPriceLiveCasino, &t.ReturnPriceSlot, &t.ReturnPriceSport,
			&creditExpireAt,
			&t.CalcAt, &t.TakeAtLiveCasino, &t.TakenPriceLiveCasino, &t.TakeAtSlot, &t.TakenPriceSlot, &t.TakenAtSport, &t.TakenPriceSport,
			&t.CreatedAt, &t.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan return transaction")
			continue
		}

		if creditExpireAt.Valid {
			t.CreditExpireAt = creditExpireAt.Time.Format("2006-01-02 15:04:05")
		}

		// Set log status based on status_id
		switch t.StatusID {
		case return_turn.RETURN_TURN_STATUS_READY:
			t.LogStatus = "READY"
		case return_turn.RETURN_TURN_STATUS_TAKEN:
			t.LogStatus = "TAKEN"
		case return_turn.RETURN_TURN_STATUS_EXPIRED:
			t.LogStatus = "EXPIRED"
		default:
			t.LogStatus = "PENDING"
		}

		transactions = append(transactions, t)
	}

	return transactions, nil
}

// GetCurrentReturnTransactionListByCode retrieves current return transactions for a user by member code
func (r *ReturnTurnRepository) GetCurrentReturnTransactionListByCode(ctx context.Context, memberCode string) ([]return_turn.ReturnTurnLoserResponse, error) {
	query := `
		SELECT 
			prl.id, prl.member_code, prl.member_code as username, '' as fullname,
			prl.status_id, prls.name as status_name,
			prl.daily_key, prl.of_date::text,
			prl.total_loss_amount, prl.total_loss_live_casino, prl.total_loss_slot, prl.total_loss_sport,
			prl.return_percent, prl.game_detail,
			prl.return_type_id, prl.cut_type_id, prct.name as cut_type_name,
			prl.min_loss_price, prl.max_return_price, prl.return_price_live_casino, prl.return_price_slot, prl.return_price_sport,
			prl.created_at + INTERVAL '7 days' as credit_expire_at,
			prl.calc_at, prl.take_at_live_casino, prl.taken_price_live_casino, prl.take_at_slot, prl.taken_price_slot, prl.taken_at_sport, prl.taken_price_sport,
			prl.created_at, prl.updated_at
		FROM return_turn_loser prl
		LEFT JOIN return_turn_loser_status prls ON prls.id = prl.status_id
		LEFT JOIN return_turn_cut_type prct ON prct.id = prl.cut_type_id
		WHERE prl.member_code = $1 AND prl.status_id = $2
		ORDER BY prl.of_date DESC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, memberCode, return_turn.RETURN_TURN_STATUS_READY)
	if err != nil {
		r.logger.WithError(err).Error("failed to get current return transactions by code")
		return nil, errors.NewDatabaseError("failed to get current return transactions by code")
	}
	defer rows.Close()

	var transactions []return_turn.ReturnTurnLoserResponse
	for rows.Next() {
		var t return_turn.ReturnTurnLoserResponse
		var creditExpireAt sql.NullTime
		err := rows.Scan(
			&t.ID, &t.MemberCode, &t.Username, &t.Fullname,
			&t.StatusID, &t.StatusName,
			&t.DailyKey, &t.OfDate,
			&t.TotalLossAmount, &t.TotalLossLiveCasino, &t.TotalLossSlot, &t.TotalLossSport,
			&t.ReturnPercent, &t.GameDetail,
			&t.ReturnTypeID, &t.CutTypeID, &t.CutTypeName,
			&t.MinLossPrice, &t.MaxReturnPrice, &t.ReturnPriceLiveCasino, &t.ReturnPriceSlot, &t.ReturnPriceSport,
			&creditExpireAt,
			&t.CalcAt, &t.TakeAtLiveCasino, &t.TakenPriceLiveCasino, &t.TakeAtSlot, &t.TakenPriceSlot, &t.TakenAtSport, &t.TakenPriceSport,
			&t.CreatedAt, &t.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan return transaction")
			continue
		}

		if creditExpireAt.Valid {
			t.CreditExpireAt = creditExpireAt.Time.Format("2006-01-02 15:04:05")
		}

		// Set log status based on status_id
		switch t.StatusID {
		case return_turn.RETURN_TURN_STATUS_READY:
			t.LogStatus = "READY"
		case return_turn.RETURN_TURN_STATUS_TAKEN:
			t.LogStatus = "TAKEN"
		case return_turn.RETURN_TURN_STATUS_EXPIRED:
			t.LogStatus = "EXPIRED"
		default:
			t.LogStatus = "PENDING"
		}

		transactions = append(transactions, t)
	}

	return transactions, nil
}

// GetReturnTransactionList retrieves return transactions with pagination
func (r *ReturnTurnRepository) GetReturnTransactionList(ctx context.Context, req return_turn.ReturnTurnTransactionListRequest) ([]return_turn.ReturnTurnLoserResponse, int64, error) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	// Get member_code from members table first
	var memberCode string
	memberQuery := `SELECT COALESCE(game_username, username) FROM members WHERE id = $1`
	err := dbutil.QueryRowWithSchema(ctx, r.pool, memberQuery, req.MemberID).Scan(&memberCode)
	if err != nil {
		if err == pgx.ErrNoRows {
			r.logger.WithError(err).Errorf("member not found for ID: %d", req.MemberID)
			return []return_turn.ReturnTurnLoserResponse{}, 0, nil
		}
		r.logger.WithError(err).Error("failed to get member code")
		return nil, 0, errors.NewDatabaseError("failed to get member code")
	}

	// Filter by member_code directly like GetReturnTransactionListByCode
	conditions = append(conditions, fmt.Sprintf("prl.member_code = $%d", argIndex))
	args = append(args, memberCode)
	argIndex++

	if req.StatusID != nil {
		conditions = append(conditions, fmt.Sprintf("prl.status_id = $%d", argIndex))
		args = append(args, *req.StatusID)
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	// Count total
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM return_turn_loser prl
		%s
	`, whereClause)

	var total int64
	err = dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.WithError(err).Error("failed to count return transactions")
		return nil, 0, errors.NewDatabaseError("failed to count return transactions")
	}

	// Get data
	query := fmt.Sprintf(`
		SELECT 
			prl.id, prl.member_code, prl.member_code as username, '' as fullname,
			prl.status_id, prls.name as status_name,
			prl.daily_key, prl.of_date::text,
			prl.total_loss_amount, prl.total_loss_live_casino, prl.total_loss_slot, prl.total_loss_sport,
			prl.return_percent, prl.game_detail,
			prl.return_type_id, prl.cut_type_id, prct.name as cut_type_name,
			prl.min_loss_price, prl.max_return_price, prl.return_price_live_casino, prl.return_price_slot, prl.return_price_sport,
			prl.created_at + INTERVAL '7 days' as credit_expire_at,
			prl.calc_at, prl.take_at_live_casino, prl.taken_price_live_casino, prl.take_at_slot, prl.taken_price_slot, prl.taken_at_sport, prl.taken_price_sport,
			prl.created_at, prl.updated_at
		FROM return_turn_loser prl
		LEFT JOIN return_turn_loser_status prls ON prls.id = prl.status_id
		LEFT JOIN return_turn_cut_type prct ON prct.id = prl.cut_type_id
		%s
		ORDER BY prl.of_date DESC
		LIMIT $%d OFFSET $%d
	`, whereClause, argIndex, argIndex+1)

	args = append(args, req.Limit, (req.Page-1)*req.Limit)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get return transactions")
		return nil, 0, errors.NewDatabaseError("failed to get return transactions")
	}
	defer rows.Close()

	var transactions []return_turn.ReturnTurnLoserResponse
	for rows.Next() {
		var t return_turn.ReturnTurnLoserResponse
		var creditExpireAt sql.NullTime
		var takeAtLiveCasino, takeAtSlot, takenAtSport, calcAt, createdAt, updatedAt sql.NullTime
		err := rows.Scan(
			&t.ID, &t.MemberCode, &t.Username, &t.Fullname,
			&t.StatusID, &t.StatusName,
			&t.DailyKey, &t.OfDate,
			&t.TotalLossAmount, &t.TotalLossLiveCasino, &t.TotalLossSlot, &t.TotalLossSport,
			&t.ReturnPercent, &t.GameDetail,
			&t.ReturnTypeID, &t.CutTypeID, &t.CutTypeName,
			&t.MinLossPrice, &t.MaxReturnPrice, &t.ReturnPriceLiveCasino, &t.ReturnPriceSlot, &t.ReturnPriceSport,
			&creditExpireAt,
			&calcAt, &takeAtLiveCasino, &t.TakenPriceLiveCasino, &takeAtSlot, &t.TakenPriceSlot, &takenAtSport, &t.TakenPriceSport,
			&createdAt, &updatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan return transaction")
			continue
		}

		// Convert NullTime to appropriate types
		if takeAtLiveCasino.Valid {
			t.TakeAtLiveCasino = &takeAtLiveCasino.Time
		}
		if takeAtSlot.Valid {
			t.TakeAtSlot = &takeAtSlot.Time
		}
		if takenAtSport.Valid {
			t.TakenAtSport = &takenAtSport.Time
		}
		if calcAt.Valid {
			t.CalcAt = &calcAt.Time
		}
		if createdAt.Valid {
			t.CreatedAt = createdAt.Time
		}
		if updatedAt.Valid {
			t.UpdatedAt = &updatedAt.Time
		}

		if creditExpireAt.Valid {
			t.CreditExpireAt = creditExpireAt.Time.Format("2006-01-02 15:04:05")
		}

		// Set log status
		switch t.StatusID {
		case return_turn.RETURN_TURN_STATUS_READY:
			t.LogStatus = "READY"
		case return_turn.RETURN_TURN_STATUS_TAKEN:
			t.LogStatus = "TAKEN"
		case return_turn.RETURN_TURN_STATUS_EXPIRED:
			t.LogStatus = "EXPIRED"
		default:
			t.LogStatus = "PENDING"
		}

		transactions = append(transactions, t)
	}

	return transactions, total, nil
}

// GetReturnTransactionListByCode retrieves return transactions by member code with pagination
func (r *ReturnTurnRepository) GetReturnTransactionListByCode(ctx context.Context, memberCode string, req return_turn.ReturnTurnTransactionListRequest) ([]return_turn.ReturnTurnLoserResponse, int64, error) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	// Filter by member code directly
	conditions = append(conditions, fmt.Sprintf("prl.member_code = $%d", argIndex))
	args = append(args, memberCode)
	argIndex++

	if req.StatusID != nil {
		conditions = append(conditions, fmt.Sprintf("prl.status_id = $%d", argIndex))
		args = append(args, *req.StatusID)
		argIndex++
	}

	var whereClause string
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// Count query
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*) 
		FROM return_turn_loser prl 
		%s
	`, whereClause)

	var total int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.WithError(err).Error("failed to count return transactions by code")
		return nil, 0, errors.NewDatabaseError("failed to count return transactions by code")
	}

	// Main query
	query := fmt.Sprintf(`
		SELECT 
			prl.id, prl.member_code, prl.member_code as username, '' as fullname,
			prl.status_id, prls.name as status_name,
			prl.daily_key, prl.of_date::text,
			prl.total_loss_amount, prl.total_loss_live_casino, prl.total_loss_slot, prl.total_loss_sport,
			prl.return_percent, prl.game_detail,
			prl.return_type_id, prl.cut_type_id, prct.name as cut_type_name,
			prl.min_loss_price, prl.max_return_price, prl.return_price_live_casino, prl.return_price_slot, prl.return_price_sport,
			prl.created_at + INTERVAL '7 days' as credit_expire_at,
			prl.calc_at, prl.take_at_live_casino, prl.taken_price_live_casino, prl.take_at_slot, prl.taken_price_slot, prl.taken_at_sport, prl.taken_price_sport,
			prl.created_at, prl.updated_at
		FROM return_turn_loser prl
		LEFT JOIN return_turn_loser_status prls ON prls.id = prl.status_id
		LEFT JOIN return_turn_cut_type prct ON prct.id = prl.cut_type_id
		%s
		ORDER BY prl.of_date DESC
		LIMIT $%d OFFSET $%d
	`, whereClause, argIndex, argIndex+1)

	args = append(args, req.Limit, (req.Page-1)*req.Limit)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get return transactions by code")
		return nil, 0, errors.NewDatabaseError("failed to get return transactions by code")
	}
	defer rows.Close()

	var transactions []return_turn.ReturnTurnLoserResponse
	for rows.Next() {
		var t return_turn.ReturnTurnLoserResponse
		var creditExpireAt sql.NullTime
		var takeAtLiveCasino, takeAtSlot, takenAtSport, calcAt, createdAt, updatedAt sql.NullTime
		err := rows.Scan(
			&t.ID, &t.MemberCode, &t.Username, &t.Fullname,
			&t.StatusID, &t.StatusName,
			&t.DailyKey, &t.OfDate,
			&t.TotalLossAmount, &t.TotalLossLiveCasino, &t.TotalLossSlot, &t.TotalLossSport,
			&t.ReturnPercent, &t.GameDetail,
			&t.ReturnTypeID, &t.CutTypeID, &t.CutTypeName,
			&t.MinLossPrice, &t.MaxReturnPrice, &t.ReturnPriceLiveCasino, &t.ReturnPriceSlot, &t.ReturnPriceSport,
			&creditExpireAt,
			&calcAt, &takeAtLiveCasino, &t.TakenPriceLiveCasino, &takeAtSlot, &t.TakenPriceSlot, &takenAtSport, &t.TakenPriceSport,
			&createdAt, &updatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan return transaction by code")
			continue
		}

		// Convert NullTime to appropriate types
		if takeAtLiveCasino.Valid {
			t.TakeAtLiveCasino = &takeAtLiveCasino.Time
		}
		if takeAtSlot.Valid {
			t.TakeAtSlot = &takeAtSlot.Time
		}
		if takenAtSport.Valid {
			t.TakenAtSport = &takenAtSport.Time
		}
		if calcAt.Valid {
			t.CalcAt = &calcAt.Time
		}
		if createdAt.Valid {
			t.CreatedAt = createdAt.Time // CreatedAt is time.Time, not *time.Time
		}
		if updatedAt.Valid {
			t.UpdatedAt = &updatedAt.Time
		}

		if creditExpireAt.Valid {
			t.CreditExpireAt = creditExpireAt.Time.Format("2006-01-02 15:04:05")
		}

		// Set log status
		switch t.StatusID {
		case return_turn.RETURN_TURN_STATUS_READY:
			t.LogStatus = "READY"
		case return_turn.RETURN_TURN_STATUS_TAKEN:
			t.LogStatus = "TAKEN"
		case return_turn.RETURN_TURN_STATUS_EXPIRED:
			t.LogStatus = "EXPIRED"
		default:
			t.LogStatus = "PENDING"
		}

		transactions = append(transactions, t)
	}

	return transactions, total, nil
}

// GetReturnTransactionByDailyKey retrieves a transaction by daily key
func (r *ReturnTurnRepository) GetReturnTransactionByDailyKey(ctx context.Context, dailyKey string) (*return_turn.ReturnTurnLoser, error) {
	query := `
		SELECT id, member_code, status_id, daily_key, of_date,
		       total_loss_amount, total_loss_live_casino, total_loss_slot, total_loss_sport,
		       return_percent, game_detail, return_type_id, cut_type_id,
		       min_loss_price, max_return_price, return_price_live_casino, return_price_slot, return_price_sport,
		       calc_at, take_at_live_casino, taken_price_live_casino, created_at, updated_at
		FROM return_turn_loser
		WHERE daily_key = $1
	`

	var t return_turn.ReturnTurnLoser
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, dailyKey).Scan(
		&t.ID, &t.MemberCode, &t.StatusID, &t.DailyKey, &t.OfDate,
		&t.TotalLossAmount, &t.TotalLossLiveCasino, &t.TotalLossSlot, &t.TotalLossSport,
		&t.ReturnPercent, &t.GameDetail, &t.ReturnTypeID, &t.CutTypeID,
		&t.MinLossPrice, &t.MaxReturnPrice, &t.ReturnPriceLiveCasino, &t.ReturnPriceSlot, &t.ReturnPriceSport,
		&t.CalcAt, &t.TakeAtLiveCasino, &t.TakenPriceLiveCasino, &t.CreatedAt, &t.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).Error("failed to get return transaction by daily key")
		return nil, errors.NewDatabaseError("failed to get return transaction")
	}

	return &t, nil
}

// CreateReturnTransaction creates a new return transaction
func (r *ReturnTurnRepository) CreateReturnTransaction(ctx context.Context, body return_turn.ReturnTurnLoserCreateBody) (*int64, error) {
	query := `
		INSERT INTO return_turn_loser (
			member_code, status_id, daily_key, of_date,
			total_loss_amount, total_loss_live_casino, total_loss_slot, total_loss_sport,
			return_percent, game_detail, return_type_id, cut_type_id,
			min_loss_price, max_return_price, return_price_live_casino, return_price_slot, return_price_sport
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17
		) RETURNING id
	`

	var id int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		body.MemberCode, body.StatusID, body.DailyKey, body.OfDate,
		body.TotalLossAmount, body.TotalLossLiveCasino, body.TotalLossSlot, body.TotalLossSport,
		body.ReturnPercent, body.GameDetail, body.ReturnTypeID, body.CutTypeID,
		body.MinLossPrice, body.MaxReturnPrice, body.ReturnPriceLiveCasino, body.ReturnPriceSlot, body.ReturnPriceSport,
	).Scan(&id)

	if err != nil {
		r.logger.WithError(err).Error("failed to create return transaction")
		return nil, errors.NewDatabaseError("failed to create return transaction")
	}

	return &id, nil
}

// UpdateCalcReturnTransaction updates calculation data for a return transaction
func (r *ReturnTurnRepository) UpdateCalcReturnTransaction(ctx context.Context, id int64, body return_turn.ReturnTurnLoserCalcBody) error {
	query := `
		UPDATE return_turn_loser
		SET status_id = $1, return_price_live_casino = $2, return_price_slot = $3, return_price_sport = $4, calc_at = $5, updated_at = NOW()
		WHERE id = $6
	`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		body.StatusID, body.ReturnPriceLiveCasino, body.ReturnPriceSlot, body.ReturnPriceSport, body.CalcAt, id,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to update calc return transaction")
		return errors.NewDatabaseError("failed to update calc return transaction")
	}

	return nil
}

// UpdateTakeReturnTransaction updates a transaction when user takes the return
func (r *ReturnTurnRepository) UpdateTakeReturnTransaction(ctx context.Context, id int64, body return_turn.ReturnTurnLoserUpdateBody) error {
	query := `
		UPDATE return_turn_loser
		SET status_id = $1, 
		    take_at_live_casino = $2, taken_price_live_casino = $3,
		    take_at_slot = $2, taken_price_slot = $4,
		    taken_at_sport = $2, taken_price_sport = $5,
		    updated_at = $6
		WHERE id = $7
	`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		body.StatusID, body.TakeAt, body.TakenPriceLiveCasino, body.TakenPriceSlot, body.TakenPriceSport, body.UpdatedAt, id,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to update take return transaction")
		return errors.NewDatabaseError("failed to update take return transaction")
	}

	return nil
}

// UpdateExpiredReturnTransaction updates a transaction to expired status
func (r *ReturnTurnRepository) UpdateExpiredReturnTransaction(ctx context.Context, id int64, body return_turn.ReturnTurnLoserUpdateBody) error {
	query := `
		UPDATE return_turn_loser
		SET status_id = $1, updated_at = $2
		WHERE id = $3
	`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		body.StatusID, body.UpdatedAt, id,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to update expired return transaction")
		return errors.NewDatabaseError("failed to update expired return transaction")
	}

	return nil
}

// IncreaseUserCredit increases user credit balance
func (r *ReturnTurnRepository) IncreaseUserCredit(ctx context.Context, tx pgx.Tx, body return_turn.UserTransactionCreateRequest) (*return_turn.UserTransactionCreateResponse, error) {
	// Get current balance
	var currentBalance float64
	getBalanceQuery := `SELECT credit_balance FROM members WHERE id = $1`
	err := dbutil.TxQueryRowWithSchema(ctx, tx, getBalanceQuery, body.MemberID).Scan(&currentBalance)
	if err != nil {
		r.logger.WithError(err).Error("failed to get user balance")
		return nil, errors.NewDatabaseError("failed to get user balance")
	}

	newBalance := currentBalance + body.Amount

	// Update balance
	updateQuery := `UPDATE members SET credit_balance = $1 WHERE id = $2`
	_, err = dbutil.TxExecWithSchema(ctx, tx, updateQuery, newBalance, body.MemberID)
	if err != nil {
		r.logger.WithError(err).Error("failed to update user balance")
		return nil, errors.NewDatabaseError("failed to update user balance")
	}

	// Create transaction record
	insertQuery := `
		INSERT INTO user_transactions (
			member_id, user_transaction_type_id, user_transaction_status_id,
			amount, balance_before, balance_after, detail, created_by_admin_id
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		RETURNING id, created_at
	`

	var response return_turn.UserTransactionCreateResponse
	err = dbutil.TxQueryRowWithSchema(ctx, tx, insertQuery,
		body.MemberID, body.UserTransactionTypeID, body.UserTransactionStatusID,
		body.Amount, currentBalance, newBalance, body.Detail, body.CreatedByAdminID,
	).Scan(&response.ID, &response.CreatedAt)

	if err != nil {
		r.logger.WithError(err).Error("failed to create user transaction")
		return nil, errors.NewDatabaseError("failed to create user transaction")
	}

	response.MemberID = body.MemberID
	response.Amount = body.Amount
	response.BalanceBefore = currentBalance
	response.BalanceAfter = newBalance

	return &response, nil
}

// GetUserBalance gets user balance
func (r *ReturnTurnRepository) GetUserBalance(ctx context.Context, memberID int64) (float64, error) {
	query := `SELECT credit_balance FROM members WHERE id = $1`

	var balance float64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, memberID).Scan(&balance)
	if err != nil {
		if err == pgx.ErrNoRows {
			return 0, errors.NewNotFoundError("user not found")
		}
		r.logger.WithError(err).Error("failed to get user balance")
		return 0, errors.NewDatabaseError("failed to get user balance")
	}

	return balance, nil
}

// GetDailyTotalUserPlaylogList gets daily play log totals
func (r *ReturnTurnRepository) GetDailyTotalUserPlaylogList(ctx context.Context, statementDate string) ([]return_turn.PlaylogTotalAmount, error) {
	query := `
		SELECT 
			member_id,
			SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as total_loss_amount,
			SUM(CASE WHEN amount < 0 AND game_type = 'LIVE_CASINO' THEN ABS(amount) ELSE 0 END) as total_loss_live_casino,
			SUM(CASE WHEN amount < 0 AND game_type = 'SLOT' THEN ABS(amount) ELSE 0 END) as total_loss_slot,
			SUM(CASE WHEN amount < 0 AND game_type = 'SPORT' THEN ABS(amount) ELSE 0 END) as total_loss_sport
		FROM play_log
		WHERE DATE(created_at) = $1
		GROUP BY member_id
		HAVING SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) > 0
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, statementDate)
	if err != nil {
		r.logger.WithError(err).Error("failed to get daily play log")
		return nil, errors.NewDatabaseError("failed to get daily play log")
	}
	defer rows.Close()

	var results []return_turn.PlaylogTotalAmount
	for rows.Next() {
		var p return_turn.PlaylogTotalAmount
		err := rows.Scan(&p.MemberCode, &p.TotalLossAmount, &p.TotalLossLiveCasino, &p.TotalLossSlot, &p.TotalLossSport)
		if err != nil {
			continue
		}
		results = append(results, p)
	}

	return results, nil
}

// GetWeeklyTotalUserPlaylogList gets weekly play log totals
func (r *ReturnTurnRepository) GetWeeklyTotalUserPlaylogList(ctx context.Context, statementDate string) ([]return_turn.PlaylogTotalAmount, error) {
	query := `
		SELECT 
			member_id,
			SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as total_loss_amount,
			SUM(CASE WHEN amount < 0 AND game_type = 'LIVE_CASINO' THEN ABS(amount) ELSE 0 END) as total_loss_live_casino,
			SUM(CASE WHEN amount < 0 AND game_type = 'SLOT' THEN ABS(amount) ELSE 0 END) as total_loss_slot,
			SUM(CASE WHEN amount < 0 AND game_type = 'SPORT' THEN ABS(amount) ELSE 0 END) as total_loss_sport
		FROM play_log
		WHERE created_at >= $1::date - INTERVAL '6 days' AND created_at < $1::date + INTERVAL '1 day'
		GROUP BY member_id
		HAVING SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) > 0
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, statementDate)
	if err != nil {
		r.logger.WithError(err).Error("failed to get weekly play log")
		return nil, errors.NewDatabaseError("failed to get weekly play log")
	}
	defer rows.Close()

	var results []return_turn.PlaylogTotalAmount
	for rows.Next() {
		var p return_turn.PlaylogTotalAmount
		err := rows.Scan(&p.MemberCode, &p.TotalLossAmount, &p.TotalLossLiveCasino, &p.TotalLossSlot, &p.TotalLossSport)
		if err != nil {
			continue
		}
		results = append(results, p)
	}

	return results, nil
}

// CheckDailyLoser checks if return turn loser data exists for a date
func (r *ReturnTurnRepository) CheckDailyLoser(ctx context.Context, statementDate string) (*return_turn.CronPlayLogCheckResponse, error) {
	query := `
		SELECT 
			COUNT(*) as count,
			MIN(DATE(created_at)) as min_date,
			MAX(DATE(created_at)) as max_date
		FROM return_turn_loser
		WHERE DATE(created_at) = $1
	`

	var response return_turn.CronPlayLogCheckResponse
	var minDate, maxDate sql.NullTime

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, statementDate).Scan(
		&response.Count, &minDate, &maxDate,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to check daily play log")
		return nil, errors.NewDatabaseError("failed to check daily play log")
	}

	response.HasData = response.Count > 0
	if minDate.Valid {
		response.MinDate = minDate.Time.Format("2006-01-02")
	}
	if maxDate.Valid {
		response.MaxDate = maxDate.Time.Format("2006-01-02")
	}

	return &response, nil
}

// GetReturnHistoryMemberList gets return history member list
func (r *ReturnTurnRepository) GetReturnHistoryMemberList(ctx context.Context, req return_turn.ReturnTurnHistoryUserListRequest) ([]return_turn.ReturnTurnHistoryUserListResponse, int64, error) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	// Date filtering
	if req.DateType != "" && req.DateType != "all" {
		switch req.DateType {
		case "today":
			conditions = append(conditions, fmt.Sprintf("DATE(prl.of_date) = CURRENT_DATE"))
		case "yesterday":
			conditions = append(conditions, fmt.Sprintf("DATE(prl.of_date) = CURRENT_DATE - INTERVAL '1 day'"))
		case "last_week":
			conditions = append(conditions, fmt.Sprintf("DATE(prl.of_date) >= CURRENT_DATE - INTERVAL '7 days'"))
		case "this_month":
			conditions = append(conditions, fmt.Sprintf("DATE_TRUNC('month', prl.of_date::date) = DATE_TRUNC('month', CURRENT_DATE)"))
		case "last_month":
			conditions = append(conditions, fmt.Sprintf("DATE_TRUNC('month', prl.of_date::date) = DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month')"))
		}
	} else if req.FromDate != "" && req.ToDate != "" {
		conditions = append(conditions, fmt.Sprintf("prl.of_date BETWEEN $%d AND $%d", argIndex, argIndex+1))
		args = append(args, req.FromDate, req.ToDate)
		argIndex += 2
	}

	// Search filtering
	if req.Search != "" {
		conditions = append(conditions, fmt.Sprintf("prl.member_code ILIKE $%d", argIndex))
		args = append(args, "%"+req.Search+"%")
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	// Count query
	countQuery := fmt.Sprintf(`
		SELECT COUNT(DISTINCT prl.member_code)
		FROM return_turn_loser prl
		%s
	`, whereClause)

	var total int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.WithError(err).Error("failed to count return history members")
		return nil, 0, errors.NewDatabaseError("failed to count return history members")
	}

	// Data query
	query := fmt.Sprintf(`
		SELECT 
			0 as id,
			m.id as member_id,
			prl.member_code,
			COALESCE(m.username, prl.member_code) as username,
			CONCAT(COALESCE(m.first_name, ''), ' ', COALESCE(m.last_name, '')) as fullname,
			SUM(prl.total_loss_amount) * -1 as total_loss_amount,
			SUM(prl.taken_price_live_casino + prl.taken_price_slot + prl.taken_price_sport) as total_taken_price
		FROM return_turn_loser prl
		LEFT JOIN members m ON (m.username = prl.member_code OR m.game_username = prl.member_code)
		%s
		GROUP BY prl.member_code, m.id, m.username, m.first_name, m.last_name
		ORDER BY total_loss_amount DESC
		LIMIT $%d OFFSET $%d
	`, whereClause, argIndex, argIndex+1)

	args = append(args, req.Limit, (req.Page-1)*req.Limit)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get return history member list")
		return nil, 0, errors.NewDatabaseError("failed to get return history member list")
	}
	defer rows.Close()

	var users []return_turn.ReturnTurnHistoryUserListResponse
	for rows.Next() {
		var u return_turn.ReturnTurnHistoryUserListResponse
		err := rows.Scan(&u.ID, &u.MemberID, &u.MemberCode, &u.Username, &u.Fullname, &u.TotalLossAmount, &u.TotalTakenPrice)
		if err != nil {
			continue
		}
		users = append(users, u)
	}

	return users, total, nil
}

// GetReturnHistoryMemberSummary gets return history summary
func (r *ReturnTurnRepository) GetReturnHistoryMemberSummary(ctx context.Context, req return_turn.ReturnTurnHistoryUserListRequest) (*return_turn.ReturnTurnHistoryUserSummaryResponse, error) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	// Date filtering
	if req.DateType != "" && req.DateType != "all" {
		switch req.DateType {
		case "today":
			conditions = append(conditions, fmt.Sprintf("DATE(prl.of_date) = CURRENT_DATE"))
		case "yesterday":
			conditions = append(conditions, fmt.Sprintf("DATE(prl.of_date) = CURRENT_DATE - INTERVAL '1 day'"))
		case "last_week":
			conditions = append(conditions, fmt.Sprintf("DATE(prl.of_date) >= CURRENT_DATE - INTERVAL '7 days'"))
		case "this_month":
			conditions = append(conditions, fmt.Sprintf("DATE_TRUNC('month', prl.of_date::date) = DATE_TRUNC('month', CURRENT_DATE)"))
		case "last_month":
			conditions = append(conditions, fmt.Sprintf("DATE_TRUNC('month', prl.of_date::date) = DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month')"))
		}
	} else if req.FromDate != "" && req.ToDate != "" {
		conditions = append(conditions, fmt.Sprintf("prl.of_date BETWEEN $%d AND $%d", argIndex, argIndex+1))
		args = append(args, req.FromDate, req.ToDate)
		argIndex += 2
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	query := fmt.Sprintf(`
		SELECT 
			COALESCE(SUM(prl.total_loss_amount), 0) * -1 as total_loss_amount,
			COALESCE(SUM(prl.taken_price_live_casino + prl.taken_price_slot + prl.taken_price_sport), 0) as total_taken_price
		FROM return_turn_loser prl
		%s
	`, whereClause)

	var response return_turn.ReturnTurnHistoryUserSummaryResponse
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(
		&response.TotalLossAmount,
		&response.TotalTakenPrice,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to get return history summary")
		return nil, errors.NewDatabaseError("failed to get return history summary")
	}

	response.DateType = req.DateType
	response.FromDate = req.FromDate
	response.ToDate = req.ToDate

	return &response, nil
}

// GetReturnHistoryLogList gets return history log list
func (r *ReturnTurnRepository) GetReturnHistoryLogList(ctx context.Context, req return_turn.ReturnTurnHistoryListRequest) ([]return_turn.ReturnTurnLoserResponse, int64, error) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	if req.MemberID != nil {
		// Get member_code from members table first
		var memberCode string
		memberQuery := `SELECT COALESCE(game_username, username) FROM members WHERE id = $1`
		err := dbutil.QueryRowWithSchema(ctx, r.pool, memberQuery, *req.MemberID).Scan(&memberCode)
		if err != nil {
			if err == pgx.ErrNoRows {
				r.logger.WithError(err).Errorf("member not found for ID: %d", *req.MemberID)
				return []return_turn.ReturnTurnLoserResponse{}, 0, nil
			}
			r.logger.WithError(err).Error("failed to get member code for history log")
			return nil, 0, errors.NewDatabaseError("failed to get member code")
		}

		// Filter by member_code directly
		conditions = append(conditions, fmt.Sprintf("prl.member_code = $%d", argIndex))
		args = append(args, memberCode)
		argIndex++
	}

	if req.FromDate != "" && req.ToDate != "" {
		conditions = append(conditions, fmt.Sprintf("prl.of_date BETWEEN $%d AND $%d", argIndex, argIndex+1))
		args = append(args, req.FromDate, req.ToDate)
		argIndex += 2
	}

	if req.StatusID != nil && *req.StatusID > 0 {
		conditions = append(conditions, fmt.Sprintf("prl.status_id = $%d", argIndex))
		args = append(args, *req.StatusID)
		argIndex++
	}

	var whereClause string
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// Count query
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*) 
		FROM return_turn_loser prl 
		%s
	`, whereClause)

	var total int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.WithError(err).Error("failed to count return history logs")
		return nil, 0, errors.NewDatabaseError("failed to count return history logs")
	}

	// Main query - exactly same structure as GetReturnTransactionListByCode
	query := fmt.Sprintf(`
		SELECT 
			prl.id, prl.member_code, prl.member_code as username, '' as fullname,
			prl.status_id, prls.name as status_name,
			prl.daily_key, prl.of_date::text,
			prl.total_loss_amount, prl.total_loss_live_casino, prl.total_loss_slot, prl.total_loss_sport,
			prl.return_percent, prl.game_detail,
			prl.return_type_id, prl.cut_type_id, prct.name as cut_type_name,
			prl.min_loss_price, prl.max_return_price, prl.return_price_live_casino, prl.return_price_slot, prl.return_price_sport,
			prl.created_at + INTERVAL '7 days' as credit_expire_at,
			prl.calc_at, prl.take_at_live_casino, prl.taken_price_live_casino, prl.take_at_slot, prl.taken_price_slot, prl.taken_at_sport, prl.taken_price_sport,
			prl.created_at, prl.updated_at
		FROM return_turn_loser prl
		LEFT JOIN return_turn_loser_status prls ON prls.id = prl.status_id
		LEFT JOIN return_turn_cut_type prct ON prct.id = prl.cut_type_id
		%s
		ORDER BY prl.of_date DESC
		LIMIT $%d OFFSET $%d
	`, whereClause, argIndex, argIndex+1)

	args = append(args, req.Limit, (req.Page-1)*req.Limit)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get return history logs")
		return nil, 0, errors.NewDatabaseError("failed to get return history logs")
	}
	defer rows.Close()

	var logs []return_turn.ReturnTurnLoserResponse
	for rows.Next() {
		var l return_turn.ReturnTurnLoserResponse
		var creditExpireAt sql.NullTime
		var takeAtLiveCasino, takeAtSlot, takenAtSport, calcAt, createdAt, updatedAt sql.NullTime

		// Exact same scan structure as GetReturnTransactionListByCode
		err := rows.Scan(
			&l.ID, &l.MemberCode, &l.Username, &l.Fullname,
			&l.StatusID, &l.StatusName,
			&l.DailyKey, &l.OfDate,
			&l.TotalLossAmount, &l.TotalLossLiveCasino, &l.TotalLossSlot, &l.TotalLossSport,
			&l.ReturnPercent, &l.GameDetail,
			&l.ReturnTypeID, &l.CutTypeID, &l.CutTypeName,
			&l.MinLossPrice, &l.MaxReturnPrice, &l.ReturnPriceLiveCasino, &l.ReturnPriceSlot, &l.ReturnPriceSport,
			&creditExpireAt,
			&calcAt, &takeAtLiveCasino, &l.TakenPriceLiveCasino, &takeAtSlot, &l.TakenPriceSlot, &takenAtSport, &l.TakenPriceSport,
			&createdAt, &updatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan return history log row")
			continue
		}

		// Convert NullTime to appropriate types - same as GetReturnTransactionListByCode
		if takeAtLiveCasino.Valid {
			l.TakeAtLiveCasino = &takeAtLiveCasino.Time
		}
		if takeAtSlot.Valid {
			l.TakeAtSlot = &takeAtSlot.Time
		}
		if takenAtSport.Valid {
			l.TakenAtSport = &takenAtSport.Time
		}
		if calcAt.Valid {
			l.CalcAt = &calcAt.Time
		}
		if createdAt.Valid {
			l.CreatedAt = createdAt.Time // CreatedAt is time.Time, not *time.Time
		}
		if updatedAt.Valid {
			l.UpdatedAt = &updatedAt.Time
		}

		if creditExpireAt.Valid {
			l.CreditExpireAt = creditExpireAt.Time.Format("2006-01-02 15:04:05")
		}

		// Set log status
		switch l.StatusID {
		case return_turn.RETURN_TURN_STATUS_READY:
			l.LogStatus = "READY"
		case return_turn.RETURN_TURN_STATUS_TAKEN:
			l.LogStatus = "TAKEN"
		case return_turn.RETURN_TURN_STATUS_EXPIRED:
			l.LogStatus = "EXPIRED"
		default:
			l.LogStatus = "PENDING"
		}

		logs = append(logs, l)
	}

	return logs, total, nil
}

// GetCustomerPromotionList gets customer promotion list
func (r *ReturnTurnRepository) GetCustomerPromotionList(ctx context.Context, req return_turn.CustomerPromotionListRequest) ([]return_turn.CustomerPromotionListResponse, int64, error) {
	// This would query from promotion_web_user table
	// Implementation depends on actual promotion system structure
	return []return_turn.CustomerPromotionListResponse{}, 0, nil
}

// CancelCustomerPromotion cancels a customer promotion
func (r *ReturnTurnRepository) CancelCustomerPromotion(ctx context.Context, statementId int64, adminId int64) error {
	// This would update promotion_web_user status
	// Implementation depends on actual promotion system structure
	return nil
}

// GetExpiredReturnTransactions gets expired return transactions
func (r *ReturnTurnRepository) GetExpiredReturnTransactions(ctx context.Context, expireDays int) ([]return_turn.ReturnTurnLoser, error) {
	query := `
		SELECT id, member_code, status_id, daily_key, of_date,
		       total_loss_amount, total_loss_live_casino, total_loss_slot, total_loss_sport,
		       return_percent, game_detail, return_type_id, cut_type_id,
		       min_loss_price, max_return_price, return_price_live_casino,
		       calc_at, take_at_live_casino, taken_price_live_casino, created_at, updated_at
		FROM return_turn_loser
		WHERE status_id = $1 
		  AND created_at < NOW() - INTERVAL '%d days'
	`

	query = fmt.Sprintf(query, expireDays)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, return_turn.RETURN_TURN_STATUS_READY)
	if err != nil {
		r.logger.WithError(err).Error("failed to get expired return transactions")
		return nil, errors.NewDatabaseError("failed to get expired return transactions")
	}
	defer rows.Close()

	var transactions []return_turn.ReturnTurnLoser
	for rows.Next() {
		var t return_turn.ReturnTurnLoser
		err := rows.Scan(
			&t.ID, &t.MemberCode, &t.StatusID, &t.DailyKey, &t.OfDate,
			&t.TotalLossAmount, &t.TotalLossLiveCasino, &t.TotalLossSlot, &t.TotalLossSport,
			&t.ReturnPercent, &t.GameDetail, &t.ReturnTypeID, &t.CutTypeID,
			&t.MinLossPrice, &t.MaxReturnPrice, &t.ReturnPriceLiveCasino,
			&t.CalcAt, &t.TakeAtLiveCasino, &t.TakenPriceLiveCasino, &t.CreatedAt, &t.UpdatedAt,
		)
		if err != nil {
			continue
		}
		transactions = append(transactions, t)
	}

	return transactions, nil
}

// GetReturnTurnCutTypes retrieves all return turn cut types (ระยะเวลาคิดยอดเสีย)
func (r *ReturnTurnRepository) GetReturnTurnCutTypes(ctx context.Context) ([]return_turn.ReturnTurnCutType, error) {
	query := `
		SELECT id, name
		FROM return_turn_cut_type
		ORDER BY id
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to get return turn cut types")
		return nil, errors.NewDatabaseError("failed to get return turn cut types")
	}
	defer rows.Close()

	var types []return_turn.ReturnTurnCutType
	for rows.Next() {
		var t return_turn.ReturnTurnCutType
		err := rows.Scan(&t.ID, &t.Name)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan return turn cut type")
			continue
		}
		types = append(types, t)
	}

	return types, nil
}

// GetReturnTurnLoserTypes retrieves all return turn loser types (เงื่อนไขคืนยอดเสีย)
func (r *ReturnTurnRepository) GetReturnTurnLoserTypes(ctx context.Context) ([]return_turn.ReturnTurnLoserType, error) {
	query := `
		SELECT id, name
		FROM return_turn_loser_type
		ORDER BY id
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to get return turn loser types")
		return nil, errors.NewDatabaseError("failed to get return turn loser types")
	}
	defer rows.Close()

	var types []return_turn.ReturnTurnLoserType
	for rows.Next() {
		var t return_turn.ReturnTurnLoserType
		err := rows.Scan(&t.ID, &t.Name)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan return turn loser type")
			continue
		}
		types = append(types, t)
	}

	return types, nil
}
