package postgres

import (
	"context"
	"fmt"

	"blacking-api/internal/domain/commission_group"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type CommissionGroupRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewCommissionGroupRepository creates a new commission group repository
func NewCommissionGroupRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.CommissionGroupRepository {
	return &CommissionGroupRepository{
		pool:   pool,
		logger: logger,
	}
}

// Create creates a new commission group
func (r *CommissionGroupRepository) Create(ctx context.Context, cg *commission_group.CommissionGroup) error {
	// Start transaction to handle default setting
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction")
		return errors.NewDatabaseError("failed to create commission group")
	}
	defer tx.Rollback(ctx)

	// If this is set as default, unset all other defaults first
	if cg.IsDefault {
		if err := r.unsetAllDefaultsInTx(ctx, tx); err != nil {
			return err
		}
	}

	query := `
		INSERT INTO commission_groups (
			name, is_default, turnover_sports, turnover_casino, turnover_fishing, 
			turnover_slot, turnover_lottery, turnover_card, turnover_other,
			status, created_at, updated_at
		)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
		RETURNING id
	`

	var lastInsertID int
	err = dbutil.TxQueryRowWithSchema(ctx, tx, query,
		cg.Name, cg.IsDefault, cg.TurnoverSports, cg.TurnoverCasino, cg.TurnoverFishing,
		cg.TurnoverSlot, cg.TurnoverLottery, cg.TurnoverCard, cg.TurnoverOther,
		cg.Status, cg.CreatedAt, cg.UpdatedAt,
	).Scan(&lastInsertID)

	if err != nil {
		r.logger.WithError(err).Error("failed to create commission group")
		return errors.NewDatabaseError("failed to create commission group")
	}

	cg.ID = lastInsertID

	if err := tx.Commit(ctx); err != nil {
		r.logger.WithError(err).Error("failed to commit transaction")
		return errors.NewDatabaseError("failed to create commission group")
	}

	r.logger.WithField("commission_group_id", cg.ID).Info("commission group created successfully")
	return nil
}

// GetByID retrieves commission group by ID
func (r *CommissionGroupRepository) GetByID(ctx context.Context, id int) (*commission_group.CommissionGroup, error) {
	query := `
		SELECT id, name, is_default, turnover_sports, turnover_casino, turnover_fishing,
			   turnover_slot, turnover_lottery, turnover_card, turnover_other,
			   status, created_at, updated_at
		FROM commission_groups
		WHERE id = $1 AND status = 'active'
	`

	cg := &commission_group.CommissionGroup{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&cg.ID, &cg.Name, &cg.IsDefault, &cg.TurnoverSports, &cg.TurnoverCasino, &cg.TurnoverFishing,
		&cg.TurnoverSlot, &cg.TurnoverLottery, &cg.TurnoverCard, &cg.TurnoverOther,
		&cg.Status, &cg.CreatedAt, &cg.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("commission group not found")
		}
		r.logger.WithError(err).Error("failed to get commission group by ID")
		return nil, errors.NewDatabaseError("failed to get commission group")
	}

	return cg, nil
}

// GetByName retrieves commission group by name
func (r *CommissionGroupRepository) GetByName(ctx context.Context, name string) (*commission_group.CommissionGroup, error) {
	query := `
		SELECT id, name, is_default, turnover_sports, turnover_casino, turnover_fishing,
			   turnover_slot, turnover_lottery, turnover_card, turnover_other,
			   status, created_at, updated_at
		FROM commission_groups
		WHERE name = $1 AND status = 'active'
	`

	cg := &commission_group.CommissionGroup{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, name).Scan(
		&cg.ID, &cg.Name, &cg.IsDefault, &cg.TurnoverSports, &cg.TurnoverCasino, &cg.TurnoverFishing,
		&cg.TurnoverSlot, &cg.TurnoverLottery, &cg.TurnoverCard, &cg.TurnoverOther,
		&cg.Status, &cg.CreatedAt, &cg.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("commission group not found")
		}
		r.logger.WithError(err).Error("failed to get commission group by name")
		return nil, errors.NewDatabaseError("failed to get commission group")
	}

	return cg, nil
}

// GetDefault retrieves the default commission group
func (r *CommissionGroupRepository) GetDefault(ctx context.Context) (*commission_group.CommissionGroup, error) {
	query := `
		SELECT id, name, is_default, turnover_sports, turnover_casino, turnover_fishing,
			   turnover_slot, turnover_lottery, turnover_card, turnover_other,
			   status, created_at, updated_at
		FROM commission_groups
		WHERE is_default = true AND status = 'active'
		LIMIT 1
	`

	cg := &commission_group.CommissionGroup{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query).Scan(
		&cg.ID, &cg.Name, &cg.IsDefault, &cg.TurnoverSports, &cg.TurnoverCasino, &cg.TurnoverFishing,
		&cg.TurnoverSlot, &cg.TurnoverLottery, &cg.TurnoverCard, &cg.TurnoverOther,
		&cg.Status, &cg.CreatedAt, &cg.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("default commission group not found")
		}
		r.logger.WithError(err).Error("failed to get default commission group")
		return nil, errors.NewDatabaseError("failed to get default commission group")
	}

	return cg, nil
}

// Update updates an existing commission group
func (r *CommissionGroupRepository) Update(ctx context.Context, cg *commission_group.CommissionGroup) error {
	// Start transaction to handle default setting
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction")
		return errors.NewDatabaseError("failed to update commission group")
	}
	defer tx.Rollback(ctx)

	// If this is set as default, unset all other defaults first
	if cg.IsDefault {
		if err := r.unsetAllDefaultsInTx(ctx, tx); err != nil {
			return err
		}
	}

	query := `
		UPDATE commission_groups
		SET name = $2, is_default = $3, turnover_sports = $4, turnover_casino = $5, turnover_fishing = $6,
			turnover_slot = $7, turnover_lottery = $8, turnover_card = $9, turnover_other = $10,
			updated_at = $11
		WHERE id = $1 AND status = 'active'
	`

	result, err := dbutil.TxExecWithSchema(ctx, tx, query,
		cg.ID, cg.Name, cg.IsDefault, cg.TurnoverSports, cg.TurnoverCasino, cg.TurnoverFishing,
		cg.TurnoverSlot, cg.TurnoverLottery, cg.TurnoverCard, cg.TurnoverOther,
		cg.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to update commission group")
		return errors.NewDatabaseError("failed to update commission group")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("commission group not found")
	}

	if err := tx.Commit(ctx); err != nil {
		r.logger.WithError(err).Error("failed to commit transaction")
		return errors.NewDatabaseError("failed to update commission group")
	}

	r.logger.WithField("commission_group_id", cg.ID).Info("commission group updated successfully")
	return nil
}

// Delete soft deletes a commission group (sets status to inactive)
func (r *CommissionGroupRepository) Delete(ctx context.Context, id int) error {
	// Check if this is the default group
	cg, err := r.GetByID(ctx, id)
	if err != nil {
		return err
	}

	if cg.IsDefault {
		return errors.NewValidationError("cannot delete default commission group")
	}

	query := `
		UPDATE commission_groups
		SET status = 'inactive', updated_at = CURRENT_TIMESTAMP
		WHERE id = $1 AND status = 'active'
	`

	commandTag, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete commission group")
		return errors.NewDatabaseError("failed to delete commission group")
	}

	if commandTag.RowsAffected() == 0 {
		return errors.NewNotFoundError("commission group not found")
	}

	r.logger.WithField("commission_group_id", id).Info("commission group deleted successfully")
	return nil
}

// List retrieves commission groups with pagination, search, and sorting
func (r *CommissionGroupRepository) List(ctx context.Context, limit, offset int, search, sortBy, sortOrder string) ([]*commission_group.CommissionGroup, error) {
	// Build base query
	baseQuery := `
		SELECT id, name, is_default, turnover_sports, turnover_casino, turnover_fishing,
			   turnover_slot, turnover_lottery, turnover_card, turnover_other,
			   status, created_at, updated_at
		FROM commission_groups
		WHERE status = 'active'
	`

	var args []interface{}
	argIndex := 1

	// Add search condition
	if search != "" {
		baseQuery += fmt.Sprintf(" AND name ILIKE $%d", argIndex)
		args = append(args, "%"+search+"%")
		argIndex++
	}

	// Add sorting
	validSortColumns := map[string]bool{
		"id":         true,
		"name":       true,
		"is_default": true,
		"created_at": true,
		"updated_at": true,
	}

	if sortBy != "" && validSortColumns[sortBy] {
		if sortOrder == "desc" {
			baseQuery += fmt.Sprintf(" ORDER BY %s DESC", sortBy)
		} else {
			baseQuery += fmt.Sprintf(" ORDER BY %s ASC", sortBy)
		}
	} else {
		baseQuery += " ORDER BY is_default DESC, created_at DESC" // Default first, then by created_at
	}

	// Add pagination
	baseQuery += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, offset)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, baseQuery, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list commission groups")
		return nil, errors.NewDatabaseError("failed to list commission groups")
	}
	defer rows.Close()

	var commissionGroups []*commission_group.CommissionGroup
	for rows.Next() {
		cg := &commission_group.CommissionGroup{}
		err := rows.Scan(
			&cg.ID, &cg.Name, &cg.IsDefault, &cg.TurnoverSports, &cg.TurnoverCasino, &cg.TurnoverFishing,
			&cg.TurnoverSlot, &cg.TurnoverLottery, &cg.TurnoverCard, &cg.TurnoverOther,
			&cg.Status, &cg.CreatedAt, &cg.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan commission group")
			return nil, errors.NewDatabaseError("failed to scan commission group")
		}
		commissionGroups = append(commissionGroups, cg)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating commission group rows")
		return nil, errors.NewDatabaseError("error iterating commission group rows")
	}

	return commissionGroups, nil
}

// Count returns total count of commission groups with search filter
func (r *CommissionGroupRepository) Count(ctx context.Context, search string) (int64, error) {
	query := "SELECT COUNT(*) FROM commission_groups WHERE status = 'active'"
	var args []interface{}

	if search != "" {
		query += " AND name ILIKE $1"
		args = append(args, "%"+search+"%")
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count commission groups")
		return 0, errors.NewDatabaseError("failed to count commission groups")
	}

	return count, nil
}

// ListActive retrieves only active commission groups
func (r *CommissionGroupRepository) ListActive(ctx context.Context) ([]*commission_group.CommissionGroup, error) {
	query := `
		SELECT id, name, is_default, turnover_sports, turnover_casino, turnover_fishing,
			   turnover_slot, turnover_lottery, turnover_card, turnover_other,
			   status, created_at, updated_at
		FROM commission_groups
		WHERE status = 'active'
		ORDER BY is_default DESC, name ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to list active commission groups")
		return nil, errors.NewDatabaseError("failed to list active commission groups")
	}
	defer rows.Close()

	var commissionGroups []*commission_group.CommissionGroup
	for rows.Next() {
		cg := &commission_group.CommissionGroup{}
		err := rows.Scan(
			&cg.ID, &cg.Name, &cg.IsDefault, &cg.TurnoverSports, &cg.TurnoverCasino, &cg.TurnoverFishing,
			&cg.TurnoverSlot, &cg.TurnoverLottery, &cg.TurnoverCard, &cg.TurnoverOther,
			&cg.Status, &cg.CreatedAt, &cg.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan active commission group")
			return nil, errors.NewDatabaseError("failed to scan active commission group")
		}
		commissionGroups = append(commissionGroups, cg)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating active commission group rows")
		return nil, errors.NewDatabaseError("error iterating active commission group rows")
	}

	return commissionGroups, nil
}

// ListForDropdown retrieves commission groups for dropdown filter
func (r *CommissionGroupRepository) ListForDropdown(ctx context.Context) ([]*commission_group.CommissionGroupDropdownResponse, error) {
	query := `
		SELECT id, name
		FROM commission_groups
		WHERE status = 'active'
		ORDER BY is_default DESC, name ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to list commission groups for dropdown")
		return nil, errors.NewDatabaseError("failed to list commission groups for dropdown")
	}
	defer rows.Close()

	var dropdownItems []*commission_group.CommissionGroupDropdownResponse
	for rows.Next() {
		item := &commission_group.CommissionGroupDropdownResponse{}
		err := rows.Scan(&item.ID, &item.Name)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan commission group dropdown item")
			return nil, errors.NewDatabaseError("failed to scan commission group dropdown item")
		}
		dropdownItems = append(dropdownItems, item)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating commission group dropdown rows")
		return nil, errors.NewDatabaseError("error iterating commission group dropdown rows")
	}

	return dropdownItems, nil
}

// SetDefault sets a commission group as default (and unsets others)
func (r *CommissionGroupRepository) SetDefault(ctx context.Context, id int) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction")
		return errors.NewDatabaseError("failed to set default commission group")
	}
	defer tx.Rollback(ctx)

	// Unset all defaults first
	if err := r.unsetAllDefaultsInTx(ctx, tx); err != nil {
		return err
	}

	// Set the specified group as default
	query := `
		UPDATE commission_groups
		SET is_default = true, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1 AND status = 'active'
	`

	result, err := dbutil.TxExecWithSchema(ctx, tx, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to set default commission group")
		return errors.NewDatabaseError("failed to set default commission group")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("commission group not found")
	}

	if err := tx.Commit(ctx); err != nil {
		r.logger.WithError(err).Error("failed to commit transaction")
		return errors.NewDatabaseError("failed to set default commission group")
	}

	r.logger.WithField("commission_group_id", id).Info("commission group set as default successfully")
	return nil
}

// UnsetAllDefaults unsets all commission groups as default
func (r *CommissionGroupRepository) UnsetAllDefaults(ctx context.Context) error {
	query := `
		UPDATE commission_groups
		SET is_default = false, updated_at = CURRENT_TIMESTAMP
		WHERE is_default = true AND status = 'active'
	`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to unset all default commission groups")
		return errors.NewDatabaseError("failed to unset all default commission groups")
	}

	r.logger.Info("all commission groups unset as default successfully")
	return nil
}

// unsetAllDefaultsInTx unsets all commission groups as default within a transaction
func (r *CommissionGroupRepository) unsetAllDefaultsInTx(ctx context.Context, tx pgx.Tx) error {
	query := `
		UPDATE commission_groups
		SET is_default = false, updated_at = CURRENT_TIMESTAMP
		WHERE is_default = true AND status = 'active'
	`

	_, err := dbutil.TxExecWithSchema(ctx, tx, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to unset all default commission groups in transaction")
		return errors.NewDatabaseError("failed to unset all default commission groups")
	}

	return nil
}
