package postgres

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"blacking-api/internal/domain/member"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type MemberRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewMemberRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.MemberRepository {
	return &MemberRepository{
		pool:   pool,
		logger: logger,
	}
}

type MemberFilter struct {
	Search        string  `json:"search"`          // firstname, lastname, phone contain
	Username      string  `json:"username"`        // username contain
	FirstName     string  `json:"first_name"`      // first name contain
	LastName      string  `json:"last_name"`       // last name contain
	BankN<PERSON>ber    string  `json:"bank_number"`     // bank number contain
	Member<PERSON>roupID *int    `json:"member_group_id"` // exact match
	PartnerID     *int    `json:"partner_id"`      // refer_user_id exact match
	ReferUserName string  `json:"refer_user_name"` // refer user name contain
	CreatedBy     *int    `json:"created_by"`      // created_by exact match
	CreatedAt     *string `json:"created_at"`      // date YYYY-MM-DD
	BirthDate     *string `json:"birth_date"`      // date YYYY-MM-DD
}

func (r *MemberRepository) Create(ctx context.Context, m *member.Member, clientIP string) error {
	// Set register IP from parameter
	m.RegisterIP = &clientIP

	// Convert refer_user_id to string if not nil
	var referUserIDStr *string
	if m.ReferUserID != nil {
		str := strconv.Itoa(*m.ReferUserID)
		referUserIDStr = &str
	}

	// Insert without username (it will be set later by AG API)
	query := `
		INSERT INTO members (
			username, password, first_name, last_name, phone, gender, bank_code, bank_number, remark, created_by,
			login_status, operate_status, register_status, balance, refer_user_id, refer_code, register_refer_code,
			is_enable, register_ip, member_group_id, referral_group_id, is_partner, show_partner_info,
			channel_id, platform_id, partner_remark, status, delete_by, created_at, updated_at
		)
		VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16,
			$17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30
		)
		RETURNING id
	`
	var lastInsertID int
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		m.Username, m.Password, m.FirstName, m.LastName, m.Phone, m.Gender, m.BankCode, m.BankNumber, m.Remark, m.CreatedBy,
		m.LoginStatus, m.OperateStatus, m.RegisterStatus, m.Balance, referUserIDStr, m.ReferCode, m.RegisterReferCode,
		m.IsEnable, m.RegisterIP, m.MemberGroupID, m.ReferralGroupID, m.IsPartner, m.ShowPartnerInfo,
		m.ChannelID, m.PlatformID, m.PartnerRemark, m.Status, m.DeleteBy, m.CreatedAt, m.UpdatedAt,
	)

	if err := row.Scan(&lastInsertID); err != nil {
		return err
	}

	m.ID = lastInsertID
	// Username will be set by AG API response
	return nil
}

func (r *MemberRepository) GetByID(ctx context.Context, id string) (*member.Member, error) {
	// Debug logging
	r.logger.WithFields(map[string]interface{}{
		"member_id": id,
		"operation": "GetByID",
		"id_type":   fmt.Sprintf("%T", id),
	}).Info("DEBUG: GetByID called with parameters")

	query := `
		SELECT m.id, m.username, m.password, m.game_username, m.game_password, m.first_name, m.last_name,
			   m.phone, m.gender, m.tw_username, m.line_id, m.bank_code, m.bank_number, m.avatar,
			   m.login_status, m.operate_status, m.register_status, m.balance, m.refer_user_id, m.refer_code, m.register_refer_code,
			   m.last_online, m.is_enable, m.register_ip, m.last_login_ip, m.session_id,
			   m.twofa_secret, m.twofa_status, m.twofa_verify_count, m.is_partner, m.member_group_id,
			   m.referral_group_id, m.show_partner_info, m.channel_id, m.platform_id, m.partner_remark,
			   m.status, m.delete_by, m.follow_up_tag, m.follow_up_status, m.contacted_by, m.last_contact_at, m.remark,
			   m.created_at, m.updated_at,
			   COALESCE(b.short_name, '') as bank_short_name
		FROM members m
		LEFT JOIN banking b ON m.bank_code = b.code
		WHERE m.id = $1 AND m.status != 'inactive'
	`

	r.logger.WithFields(map[string]interface{}{
		"query":     query,
		"member_id": id,
	}).Info("DEBUG: About to execute query")

	m := &member.Member{}
	var bankShortName string
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&m.ID, &m.Username, &m.Password, &m.GameUsername, &m.GamePassword, &m.FirstName, &m.LastName,
		&m.Phone, &m.Gender, &m.TwUsername, &m.LineID, &m.BankCode, &m.BankNumber, &m.Avatar,
		&m.LoginStatus, &m.OperateStatus, &m.RegisterStatus, &m.Balance, &m.ReferUserID, &m.ReferCode, &m.RegisterReferCode,
		&m.LastOnline, &m.IsEnable, &m.RegisterIP, &m.LastLoginIP, &m.SessionID,
		&m.TwofaSecret, &m.TwofaStatus, &m.TwofaVerifyCount, &m.IsPartner, &m.MemberGroupID,
		&m.ReferralGroupID, &m.ShowPartnerInfo, &m.ChannelID, &m.PlatformID, &m.PartnerRemark,
		&m.Status, &m.DeleteBy, &m.FollowUpTag, &m.FollowUpStatus, &m.ContactedBy, &m.LastContactAt, &m.Remark,
		&m.CreatedAt, &m.UpdatedAt,
		&bankShortName,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			r.logger.WithField("member_id", id).Error("member not found in database")
			return nil, errors.NewNotFoundError("member not found")
		}
		r.logger.WithError(err).WithFields(map[string]interface{}{
			"member_id":    id,
			"query":        query,
			"error_type":   fmt.Sprintf("%T", err),
			"error_detail": err.Error(),
		}).Error("detailed error getting member by ID")
		return nil, errors.NewDatabaseError("failed to get member")
	}

	// Set bank short name if available
	if bankShortName != "" {
		m.BankShortName = &bankShortName
	}

	return m, nil
}
func (r *MemberRepository) GetByPhone(ctx context.Context, phone string) (*member.Member, error) {
	query := `
		SELECT id, username, password, game_username, game_password, first_name, last_name,
			   phone, gender, tw_username, line_id, bank_code, bank_number, avatar,
			   login_status, operate_status, register_status, balance, refer_user_id, refer_code, register_refer_code,
			   last_online, is_enable, register_ip, last_login_ip, session_id,
			   twofa_secret, twofa_status, twofa_verify_count, is_partner, member_group_id,
			   referral_group_id, show_partner_info, channel_id, platform_id, partner_remark,
			   status, delete_by, created_at, updated_at
		FROM members
		WHERE phone = $1 AND status != 'inactive'
	`

	m := &member.Member{}
	var referUserIDStr *string
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, phone).Scan(
		&m.ID, &m.Username, &m.Password, &m.GameUsername, &m.GamePassword, &m.FirstName, &m.LastName,
		&m.Phone, &m.Gender, &m.TwUsername, &m.LineID, &m.BankCode, &m.BankNumber, &m.Avatar,
		&m.LoginStatus, &m.OperateStatus, &m.RegisterStatus, &m.Balance, &referUserIDStr, &m.ReferCode, &m.RegisterReferCode,
		&m.LastOnline, &m.IsEnable, &m.RegisterIP, &m.LastLoginIP, &m.SessionID,
		&m.TwofaSecret, &m.TwofaStatus, &m.TwofaVerifyCount, &m.IsPartner, &m.MemberGroupID,
		&m.ReferralGroupID, &m.ShowPartnerInfo, &m.ChannelID, &m.PlatformID, &m.PartnerRemark,
		&m.Status, &m.DeleteBy, &m.CreatedAt, &m.UpdatedAt,
	)

	// Convert string back to int if not nil
	if referUserIDStr != nil {
		if referUserID, err := strconv.Atoi(*referUserIDStr); err == nil {
			m.ReferUserID = &referUserID
		}
	}

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("member not found")
		}
		r.logger.WithError(err).WithField("phone", phone).Error("failed to get member by phone")
		return nil, errors.NewDatabaseError("failed to get member")
	}

	return m, nil
}

func (r *MemberRepository) GetByUsername(ctx context.Context, username string) (*member.Member, error) {
	query := `
		SELECT id, username, password, game_username, game_password, first_name, last_name,
			   phone, gender, tw_username, line_id, bank_code, bank_number, avatar,
			   login_status, operate_status, register_status, balance, refer_user_id, refer_code, register_refer_code,
			   last_online, is_enable, register_ip, last_login_ip, session_id,
			   twofa_secret, twofa_status, twofa_verify_count, is_partner, member_group_id,
			   referral_group_id, show_partner_info, channel_id, platform_id, partner_remark,
			   status, delete_by, created_at, updated_at
		FROM members
		WHERE username = $1 AND status != 'inactive'
	`

	m := &member.Member{}
	var referUserIDStr *string
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, username).Scan(
		&m.ID, &m.Username, &m.Password, &m.GameUsername, &m.GamePassword, &m.FirstName, &m.LastName,
		&m.Phone, &m.Gender, &m.TwUsername, &m.LineID, &m.BankCode, &m.BankNumber, &m.Avatar,
		&m.LoginStatus, &m.OperateStatus, &m.RegisterStatus, &m.Balance, &referUserIDStr, &m.ReferCode, &m.RegisterReferCode,
		&m.LastOnline, &m.IsEnable, &m.RegisterIP, &m.LastLoginIP, &m.SessionID,
		&m.TwofaSecret, &m.TwofaStatus, &m.TwofaVerifyCount, &m.IsPartner, &m.MemberGroupID,
		&m.ReferralGroupID, &m.ShowPartnerInfo, &m.ChannelID, &m.PlatformID, &m.PartnerRemark,
		&m.Status, &m.DeleteBy, &m.CreatedAt, &m.UpdatedAt,
	)

	// Convert string back to int if not nil
	if referUserIDStr != nil {
		if referUserID, err := strconv.Atoi(*referUserIDStr); err == nil {
			m.ReferUserID = &referUserID
		}
	}

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("member not found")
		}
		r.logger.WithError(err).WithField("username", username).Error("failed to get member by username")
		return nil, errors.NewDatabaseError("failed to get member")
	}

	return m, nil
}

func (r *MemberRepository) GetByGameUsername(ctx context.Context, gameUsername string) (*member.Member, error) {
	query := `
		SELECT id, username, password, game_username, game_password, first_name, last_name,
			   phone, gender, tw_username, line_id, bank_code, bank_number, avatar,
			   login_status, operate_status, register_status, balance, refer_user_id, refer_code, register_refer_code,
			   last_online, is_enable, register_ip, last_login_ip, session_id,
			   twofa_secret, twofa_status, twofa_verify_count, is_partner, member_group_id,
			   referral_group_id, show_partner_info, channel_id, platform_id, partner_remark,
			   status, delete_by, created_at, updated_at
		FROM members
		WHERE game_username = $1 AND status != 'inactive'
	`

	m := &member.Member{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, gameUsername).Scan(
		&m.ID, &m.Username, &m.Password, &m.GameUsername, &m.GamePassword, &m.FirstName, &m.LastName,
		&m.Phone, &m.Gender, &m.TwUsername, &m.LineID, &m.BankCode, &m.BankNumber, &m.Avatar,
		&m.LoginStatus, &m.OperateStatus, &m.RegisterStatus, &m.Balance, &m.ReferUserID, &m.ReferCode, &m.RegisterReferCode,
		&m.LastOnline, &m.IsEnable, &m.RegisterIP, &m.LastLoginIP, &m.SessionID,
		&m.TwofaSecret, &m.TwofaStatus, &m.TwofaVerifyCount, &m.IsPartner, &m.MemberGroupID,
		&m.ReferralGroupID, &m.ShowPartnerInfo, &m.ChannelID, &m.PlatformID, &m.PartnerRemark,
		&m.Status, &m.DeleteBy, &m.CreatedAt, &m.UpdatedAt,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("member not found")
		}
		r.logger.WithError(err).WithField("game_username", gameUsername).Error("failed to get member by game username")
		return nil, errors.NewDatabaseError("failed to get member")
	}

	return m, nil
}

// GetByReferCode retrieves a member by refer code
func (r *MemberRepository) GetByReferCode(ctx context.Context, referCode string) (*member.Member, error) {
	var m member.Member
	query := `
		SELECT id, username, password, game_username, game_password, first_name, last_name,
			   phone, gender, tw_username, line_id, bank_code, bank_number, avatar,
			   login_status, operate_status, register_status, balance, refer_user_id, refer_code, register_refer_code,
			   last_online, is_enable, register_ip, last_login_ip, session_id,
			   twofa_secret, twofa_status, twofa_verify_count, is_partner, member_group_id,
			   referral_group_id, show_partner_info, channel_id, platform_id, partner_remark,
			   status, delete_by, created_at, updated_at
		FROM members
		WHERE refer_code = $1 AND status != 'inactive'
	`

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, referCode).Scan(
		&m.ID, &m.Username, &m.Password, &m.GameUsername, &m.GamePassword, &m.FirstName, &m.LastName,
		&m.Phone, &m.Gender, &m.TwUsername, &m.LineID, &m.BankCode, &m.BankNumber, &m.Avatar,
		&m.LoginStatus, &m.OperateStatus, &m.RegisterStatus, &m.Balance, &m.ReferUserID, &m.ReferCode, &m.RegisterReferCode,
		&m.LastOnline, &m.IsEnable, &m.RegisterIP, &m.LastLoginIP, &m.SessionID,
		&m.TwofaSecret, &m.TwofaStatus, &m.TwofaVerifyCount, &m.IsPartner, &m.MemberGroupID,
		&m.ReferralGroupID, &m.ShowPartnerInfo, &m.ChannelID, &m.PlatformID, &m.PartnerRemark,
		&m.Status, &m.DeleteBy, &m.CreatedAt, &m.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	return &m, nil
}

func (r *MemberRepository) Update(ctx context.Context, m *member.Member) error {
	// Convert refer_user_id to string if not nil
	var referUserIDStr *string
	if m.ReferUserID != nil {
		str := strconv.Itoa(*m.ReferUserID)
		referUserIDStr = &str
	}

	query := `
		UPDATE members
		SET username = $2, password = $3, game_username = $4, game_password = $5,
			first_name = $6, last_name = $7, phone = $8, gender = $9, tw_username = $10, line_id = $11,
			bank_code = $12, bank_number = $13, avatar = $14, login_status = $15,
			operate_status = $16, register_status = $17, balance = $18, refer_user_id = $19,
			last_online = $20, is_enable = $21, last_login_ip = $22, session_id = $23,
			twofa_secret = $24, twofa_status = $25, twofa_verify_count = $26,
			status = $27, follow_up_tag = $28, follow_up_status = $29, 
			contacted_by = $30, last_contact_at = $31, remark = $32, member_group_id = $33,
			referral_group_id = $34, updated_at = $35
		WHERE id = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		m.ID, m.Username, m.Password, m.GameUsername, m.GamePassword,
		m.FirstName, m.LastName, m.Phone, m.Gender, m.TwUsername, m.LineID,
		m.BankCode, m.BankNumber, m.Avatar, m.LoginStatus,
		m.OperateStatus, m.RegisterStatus, m.Balance, referUserIDStr,
		m.LastOnline, m.IsEnable, m.LastLoginIP, m.SessionID,
		m.TwofaSecret, m.TwofaStatus, m.TwofaVerifyCount,
		m.Status, m.FollowUpTag, m.FollowUpStatus, m.ContactedBy, m.LastContactAt, m.Remark, m.MemberGroupID,
		m.ReferralGroupID, m.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).WithField("member_id", m.ID).Error("failed to update member")
		return errors.NewDatabaseError("failed to update member")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("member not found")
	}

	return nil
}

func (r *MemberRepository) Delete(ctx context.Context, id string) error {
	query := `
		UPDATE members
		SET status = 'inactive', updated_at = CURRENT_TIMESTAMP
		WHERE id = $1 AND status != 'inactive'
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).WithField("member_id", id).Error("failed to delete member")
		return errors.NewDatabaseError("failed to delete member")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("member not found")
	}

	r.logger.WithField("member_id", id).Info("member deleted successfully")
	return nil
}

func (r *MemberRepository) List(ctx context.Context, limit, offset int, search string) ([]*member.Member, error) {
	var query string
	var args []interface{}

	if search != "" {
		query = `
			SELECT id, username, password, game_username, game_password, first_name, last_name,
				   phone, gender, tw_username, line_id, bank_code, bank_number, avatar,
				   login_status, operate_status, register_status, balance, refer_user_id, refer_code, register_refer_code,
				   last_online, is_enable, register_ip, last_login_ip, session_id,
				   twofa_secret, twofa_status, twofa_verify_count, is_partner, member_group_id,
				   referral_group_id, show_partner_info, channel_id, platform_id, partner_remark,
				   status, delete_by, created_at, updated_at
			FROM members
			WHERE (username ILIKE '%' || $1 || '%' OR game_username ILIKE '%' || $1 || '%'
				   OR first_name ILIKE '%' || $1 || '%' OR last_name ILIKE '%' || $1 || '%')
				   AND status = 'active'
			ORDER BY created_at DESC
			LIMIT $2 OFFSET $3
		`
		args = []interface{}{search, limit, offset}
	} else {
		query = `
			SELECT id, username, password, game_username, game_password, first_name, last_name,
				   phone, gender, tw_username, line_id, bank_code, bank_number, avatar,
				   login_status, operate_status, register_status, balance, refer_user_id, refer_code, register_refer_code,
				   last_online, is_enable, register_ip, last_login_ip, session_id,
				   twofa_secret, twofa_status, twofa_verify_count, is_partner, member_group_id,
				   referral_group_id, show_partner_info, channel_id, platform_id, partner_remark,
				   status, delete_by, created_at, updated_at
			FROM members
			WHERE status = 'active'
			ORDER BY created_at DESC
			LIMIT $1 OFFSET $2
		`
		args = []interface{}{limit, offset}
	}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list members")
		return nil, errors.NewDatabaseError("failed to list members")
	}
	defer rows.Close()

	var members []*member.Member
	for rows.Next() {
		m := &member.Member{}
		var referUserIDStr *string
		err := rows.Scan(
			&m.ID, &m.Username, &m.Password, &m.GameUsername, &m.GamePassword, &m.FirstName, &m.LastName,
			&m.Phone, &m.Gender, &m.TwUsername, &m.LineID, &m.BankCode, &m.BankNumber, &m.Avatar,
			&m.LoginStatus, &m.OperateStatus, &m.RegisterStatus, &m.Balance, &referUserIDStr, &m.ReferCode, &m.RegisterReferCode,
			&m.LastOnline, &m.IsEnable, &m.RegisterIP, &m.LastLoginIP, &m.SessionID,
			&m.TwofaSecret, &m.TwofaStatus, &m.TwofaVerifyCount, &m.IsPartner, &m.MemberGroupID,
			&m.ReferralGroupID, &m.ShowPartnerInfo, &m.ChannelID, &m.PlatformID, &m.PartnerRemark,
			&m.Status, &m.DeleteBy, &m.CreatedAt, &m.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan member row")
			return nil, errors.NewDatabaseError("failed to scan member")
		}

		// Convert string back to int if not nil
		if referUserIDStr != nil {
			if referUserID, err := strconv.Atoi(*referUserIDStr); err == nil {
				m.ReferUserID = &referUserID
			}
		}

		members = append(members, m)
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating member rows")
		return nil, errors.NewDatabaseError("failed to list members")
	}

	return members, nil
}

func (r *MemberRepository) Count(ctx context.Context, search string) (int64, error) {
	var query string
	var args []interface{}

	if search != "" {
		query = `
			SELECT COUNT(*)
			FROM members
			WHERE (username ILIKE '%' || $1 || '%' OR game_username ILIKE '%' || $1 || '%'
				   OR first_name ILIKE '%' || $1 || '%' OR last_name ILIKE '%' || $1 || '%')
				   AND status = 'active'
		`
		args = []interface{}{search}
	} else {
		query = `
			SELECT COUNT(*)
			FROM members
			WHERE status = 'active'
		`
		args = []interface{}{}
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count members")
		return 0, errors.NewDatabaseError("failed to count members")
	}

	return count, nil
}

// ListPartners retrieves partners (members with is_partner = true) with pagination and search by first_name
func (r *MemberRepository) ListPartners(ctx context.Context, limit, offset int, search string) ([]*member.Member, error) {
	var query string
	var args []interface{}

	if search != "" {
		query = `
			SELECT m.id, m.username, m.password, m.game_username, m.game_password, m.first_name, m.last_name, m.phone, m.gender,
				   m.tw_username, m.line_id, m.bank_code, m.bank_number, m.avatar, m.login_status, m.operate_status,
				   m.register_status, m.balance, m.refer_user_id, m.refer_code, m.register_refer_code, m.last_online, m.is_enable, m.register_ip, m.last_login_ip,
				   m.session_id, m.twofa_secret, m.twofa_status, m.twofa_verify_count, m.is_partner, m.member_group_id,
				   m.referral_group_id, m.show_partner_info, m.channel_id, m.platform_id, m.partner_remark, m.status, m.delete_by, m.created_at, m.updated_at,
				   COALESCE(c.name, '') as channel_name
			FROM members m
			LEFT JOIN channels c ON m.channel_id = c.id AND c.status = 'active'
			WHERE m.is_partner = true AND m.status = 'active' AND m.first_name ILIKE $1
			ORDER BY m.created_at DESC
			LIMIT $2 OFFSET $3
		`
		args = []interface{}{"%" + search + "%", limit, offset}
	} else {
		query = `
			SELECT m.id, m.username, m.password, m.game_username, m.game_password, m.first_name, m.last_name, m.phone, m.gender,
				   m.tw_username, m.line_id, m.bank_code, m.bank_number, m.avatar, m.login_status, m.operate_status,
				   m.register_status, m.balance, m.refer_user_id, m.refer_code, m.register_refer_code, m.last_online, m.is_enable, m.register_ip, m.last_login_ip,
				   m.session_id, m.twofa_secret, m.twofa_status, m.twofa_verify_count, m.is_partner, m.member_group_id,
				   m.referral_group_id, m.show_partner_info, m.channel_id, m.platform_id, m.partner_remark, m.status, m.delete_by, m.created_at, m.updated_at,
				   COALESCE(c.name, '') as channel_name
			FROM members m
			LEFT JOIN channels c ON m.channel_id = c.id AND c.status = 'active'
			WHERE m.is_partner = true AND m.status = 'active'
			ORDER BY m.created_at DESC
			LIMIT $1 OFFSET $2
		`
		args = []interface{}{limit, offset}
	}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list partners")
		return nil, errors.NewDatabaseError("failed to list partners")
	}
	defer rows.Close()

	var partners []*member.Member
	for rows.Next() {
		m := &member.Member{}
		var channelName string
		err := rows.Scan(
			&m.ID, &m.Username, &m.Password, &m.GameUsername, &m.GamePassword,
			&m.FirstName, &m.LastName, &m.Phone, &m.Gender, &m.TwUsername,
			&m.LineID, &m.BankCode, &m.BankNumber, &m.Avatar, &m.LoginStatus,
			&m.OperateStatus, &m.RegisterStatus, &m.Balance, &m.ReferUserID, &m.ReferCode, &m.RegisterReferCode,
			&m.LastOnline, &m.IsEnable, &m.RegisterIP, &m.LastLoginIP,
			&m.SessionID, &m.TwofaSecret, &m.TwofaStatus, &m.TwofaVerifyCount,
			&m.IsPartner, &m.MemberGroupID, &m.ReferralGroupID, &m.ShowPartnerInfo,
			&m.ChannelID, &m.PlatformID, &m.PartnerRemark, &m.Status, &m.DeleteBy, &m.CreatedAt, &m.UpdatedAt,
			&channelName, // Add channel_name from JOIN
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan partner")
			return nil, errors.NewDatabaseError("failed to scan partner")
		}

		// Set channel name if available
		if channelName != "" {
			m.ChannelName = &channelName
		}

		partners = append(partners, m)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating partner rows")
		return nil, errors.NewDatabaseError("error iterating partner rows")
	}

	return partners, nil
}

// CountPartners returns total count of partners with search filter
func (r *MemberRepository) CountPartners(ctx context.Context, search string) (int64, error) {
	var query string
	var args []interface{}

	if search != "" {
		query = `
			SELECT COUNT(*)
			FROM members
			WHERE is_partner = true AND status = 'active' AND first_name ILIKE $1
		`
		args = []interface{}{"%" + search + "%"}
	} else {
		query = `
			SELECT COUNT(*)
			FROM members
			WHERE is_partner = true AND status = 'active'
		`
		args = []interface{}{}
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count partners")
		return 0, errors.NewDatabaseError("failed to count partners")
	}

	return count, nil
}

func (r *MemberRepository) ListWithFilter(ctx context.Context, limit, offset int, filter *member.MemberFilter) ([]*member.Member, error) {
	baseQuery := `
		SELECT m.id, m.username, m.password, m.game_username, m.game_password, m.first_name, m.last_name,
			   m.phone, m.gender, m.tw_username, m.line_id, m.bank_code, m.bank_number, m.avatar,
			   m.login_status, m.operate_status, m.register_status, m.balance, m.refer_user_id, m.refer_code, m.register_refer_code,
			   m.last_online, m.is_enable, m.register_ip, m.last_login_ip, m.session_id,
			   m.twofa_secret, m.twofa_status, m.twofa_verify_count, m.is_partner, m.member_group_id,
			   m.referral_group_id, m.show_partner_info, m.channel_id, m.platform_id, m.partner_remark,
			   m.follow_up_tag, m.follow_up_status, m.contacted_by, m.last_contact_at, m.remark,
			   m.status, m.delete_by, m.created_at, m.updated_at, m.birth_date,
			   COALESCE(mg.name, '') as member_group_name,
			   COALESCE(cg.name, '') as commission_group_name,
			   COALESCE(rg.name, '') as referral_group_name,
			   COALESCE(ch.name, '') as channel_name,
			   CASE 
			     WHEN m.created_by = 0 OR m.created_by IS NULL THEN 'System'
			     ELSE COALESCE(u.username, 'System')
			   END as created_by_username
		FROM members m
		LEFT JOIN member_groups mg ON m.member_group_id = mg.id AND mg.status = 'active'
		LEFT JOIN commission_groups cg ON mg.commission_group_id = cg.id AND cg.status = 'active'
		LEFT JOIN referral_groups rg ON m.referral_group_id = rg.id AND rg.status = 'active'
		LEFT JOIN channels ch ON m.channel_id = ch.id AND ch.status = 'active'
		LEFT JOIN users u ON m.created_by = u.id
	`

	var conditions []string
	var args []interface{}
	argIndex := 1

	// Status filter - default: active, suspended, banned (exclude inactive)
	statusFilter := filter.Status
	if len(statusFilter) == 0 {
		// Default: show all except inactive
		statusFilter = []member.Status{member.StatusActive, member.StatusSuspended, member.StatusBanned}
	}

	// Create status IN clause
	if len(statusFilter) > 0 {
		placeholders := make([]string, len(statusFilter))
		for i, status := range statusFilter {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, string(status))
			argIndex++
		}
		conditions = append(conditions, fmt.Sprintf("m.status IN (%s)", strings.Join(placeholders, ", ")))
	}

	// 1. Search in full_name (first_name + last_name), username, phone
	if filter.Search != "" {
		conditions = append(conditions, fmt.Sprintf("(CONCAT(COALESCE(m.first_name, ''), ' ', COALESCE(m.last_name, '')) ILIKE $%d OR m.username ILIKE $%d OR m.phone ILIKE $%d)", argIndex, argIndex, argIndex))
		args = append(args, "%"+filter.Search+"%")
		argIndex++
	}

	// 2. Username contain
	if filter.Username != "" {
		conditions = append(conditions, fmt.Sprintf("m.username ILIKE $%d", argIndex))
		args = append(args, "%"+filter.Username+"%")
		argIndex++
	}

	// 3. Full name contain (first_name + last_name)
	if filter.FullName != "" {
		conditions = append(conditions, fmt.Sprintf("CONCAT(COALESCE(m.first_name, ''), ' ', COALESCE(m.last_name, '')) ILIKE $%d", argIndex))
		args = append(args, "%"+filter.FullName+"%")
		argIndex++
	}

	// 4. Bank number contain
	if filter.BankNumber != "" {
		conditions = append(conditions, fmt.Sprintf("m.bank_number ILIKE $%d", argIndex))
		args = append(args, "%"+filter.BankNumber+"%")
		argIndex++
	}

	// 5. Member group ID exact match
	if filter.MemberGroupID != nil {
		conditions = append(conditions, fmt.Sprintf("m.member_group_id = $%d", argIndex))
		args = append(args, *filter.MemberGroupID)
		argIndex++
	}

	// 6. Refer User ID exact match
	if filter.ReferUserID != nil {
		conditions = append(conditions, fmt.Sprintf("m.refer_user_id = $%d", argIndex))
		args = append(args, *filter.ReferUserID)
		argIndex++
	}

	// 6.1. Partner ID (alias for refer_user_id for backward compatibility)
	if filter.PartnerID != nil {
		conditions = append(conditions, fmt.Sprintf("m.refer_user_id = $%d", argIndex))
		args = append(args, *filter.PartnerID)
		argIndex++
	}

	// 7. Refer user name contain
	if filter.ReferUserName != "" {
		conditions = append(conditions, fmt.Sprintf("(refer_user.first_name ILIKE $%d OR refer_user.last_name ILIKE $%d OR refer_user.username ILIKE $%d)", argIndex, argIndex, argIndex))
		args = append(args, "%"+filter.ReferUserName+"%")
		argIndex++
	}

	// 8. Created by exact match
	if filter.CreatedBy != nil {
		conditions = append(conditions, fmt.Sprintf("m.created_by = $%d", argIndex))
		args = append(args, *filter.CreatedBy)
		argIndex++
	}

	// 9. Created at date match
	if filter.CreatedAt != nil {
		conditions = append(conditions, fmt.Sprintf("DATE(m.created_at) = $%d", argIndex))
		args = append(args, *filter.CreatedAt)
		argIndex++
	}

	// 10. Start datetime filter
	if filter.StartDateTime != nil {
		conditions = append(conditions, fmt.Sprintf("m.created_at >= $%d", argIndex))
		args = append(args, *filter.StartDateTime)
		argIndex++
	}

	// 11. End datetime filter
	if filter.EndDateTime != nil {
		conditions = append(conditions, fmt.Sprintf("m.created_at <= $%d", argIndex))
		args = append(args, *filter.EndDateTime)
		argIndex++
	}

	// 12. Last online start datetime filter
	if filter.LastOnlineStartDateTime != nil {
		conditions = append(conditions, fmt.Sprintf("m.last_online >= $%d", argIndex))
		args = append(args, *filter.LastOnlineStartDateTime)
		argIndex++
	}

	// 13. Last online end datetime filter
	if filter.LastOnlineEndDateTime != nil {
		conditions = append(conditions, fmt.Sprintf("m.last_online <= $%d", argIndex))
		args = append(args, *filter.LastOnlineEndDateTime)
		argIndex++
	}

	// 14. Birth date match
	if filter.BirthDate != nil {
		conditions = append(conditions, fmt.Sprintf("DATE(m.birth_date) = $%d", argIndex))
		args = append(args, *filter.BirthDate)
		argIndex++
	}

	// 15. Channel ID filter
	if filter.ChannelID != nil {
		conditions = append(conditions, fmt.Sprintf("m.channel_id = $%d", argIndex))
		args = append(args, *filter.ChannelID)
		argIndex++
	}

	// 16. Platform ID filter
	if filter.PlatformID != nil {
		conditions = append(conditions, fmt.Sprintf("m.platform_id = $%d", argIndex))
		args = append(args, *filter.PlatformID)
		argIndex++
	}

	// 17. Follow-up tag filter
	if filter.FollowUpTag != "" && filter.FollowUpTag != "all" {
		conditions = append(conditions, fmt.Sprintf("m.follow_up_tag = $%d", argIndex))
		args = append(args, filter.FollowUpTag)
		argIndex++
	}

	// 18. Follow-up status filter
	if filter.FollowUpStatus != "" && filter.FollowUpStatus != "all" {
		conditions = append(conditions, fmt.Sprintf("m.follow_up_status = $%d", argIndex))
		args = append(args, filter.FollowUpStatus)
		argIndex++
	}

	// 19. Contacted by filter
	if filter.ContactedBy != nil {
		conditions = append(conditions, fmt.Sprintf("m.contacted_by = $%d", argIndex))
		args = append(args, *filter.ContactedBy)
		argIndex++
	}

	// Build final query
	query := baseQuery
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	// Add ORDER BY clause with default values
	orderBy := "m.id"
	orderDir := "DESC"

	if filter.OrderBy != "" {
		switch filter.OrderBy {
		case "id":
			orderBy = "m.id"
		case "balance":
			orderBy = "m.balance"
		case "created_at":
			orderBy = "m.created_at"
		case "username":
			orderBy = "m.username"
		default:
			orderBy = "m.id" // fallback to default
		}
	}

	if filter.OrderDir != "" {
		switch strings.ToUpper(filter.OrderDir) {
		case "ASC":
			orderDir = "ASC"
		case "DESC":
			orderDir = "DESC"
		default:
			orderDir = "DESC" // fallback to default
		}
	}

	query += fmt.Sprintf(" ORDER BY %s %s", orderBy, orderDir)
	query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, offset)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list members with filter")
		return nil, errors.NewDatabaseError("failed to list members")
	}
	defer rows.Close()

	var members []*member.Member
	for rows.Next() {
		m := &member.Member{}
		var deleteBy *int
		var memberGroupName, commissionGroupName, referralGroupName, channelName, createdByUsername string
		err := rows.Scan(
			&m.ID, &m.Username, &m.Password, &m.GameUsername, &m.GamePassword, &m.FirstName, &m.LastName,
			&m.Phone, &m.Gender, &m.TwUsername, &m.LineID, &m.BankCode, &m.BankNumber, &m.Avatar,
			&m.LoginStatus, &m.OperateStatus, &m.RegisterStatus, &m.Balance, &m.ReferUserID, &m.ReferCode, &m.RegisterReferCode,
			&m.LastOnline, &m.IsEnable, &m.RegisterIP, &m.LastLoginIP, &m.SessionID,
			&m.TwofaSecret, &m.TwofaStatus, &m.TwofaVerifyCount, &m.IsPartner, &m.MemberGroupID,
			&m.ReferralGroupID, &m.ShowPartnerInfo, &m.ChannelID, &m.PlatformID, &m.PartnerRemark,
			&m.FollowUpTag, &m.FollowUpStatus, &m.ContactedBy, &m.LastContactAt, &m.Remark,
			&m.Status, &deleteBy, &m.CreatedAt, &m.UpdatedAt, &m.BirthDate,
			&memberGroupName, &commissionGroupName, &referralGroupName, &channelName, &createdByUsername,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan member row")
			return nil, errors.NewDatabaseError("failed to scan member")
		}

		// Set delete_by if not nil
		if deleteBy != nil {
			m.DeleteBy = deleteBy
		}

		// Set group names
		if memberGroupName != "" {
			m.MemberGroupName = &memberGroupName
		}
		if commissionGroupName != "" {
			m.CommissionGroupName = &commissionGroupName
		}
		if referralGroupName != "" {
			m.ReferralGroupName = &referralGroupName
		}
		if channelName != "" {
			m.ChannelName = &channelName
		}
		// Always set created_by_username (either admin username or "System")
		m.CreatedByUsername = &createdByUsername

		members = append(members, m)
	}

	return members, nil
}

func (r *MemberRepository) CountWithFilter(ctx context.Context, filter *member.MemberFilter) (int64, error) {
	baseQuery := `SELECT COUNT(*) FROM members m`

	var conditions []string
	var args []interface{}
	argIndex := 1

	// Status filter - default: active, suspended, banned (exclude inactive)
	statusFilter := filter.Status
	if len(statusFilter) == 0 {
		// Default: show all except inactive
		statusFilter = []member.Status{member.StatusActive, member.StatusSuspended, member.StatusBanned}
	}

	// Create status IN clause
	if len(statusFilter) > 0 {
		placeholders := make([]string, len(statusFilter))
		for i, status := range statusFilter {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, string(status))
			argIndex++
		}
		conditions = append(conditions, fmt.Sprintf("m.status IN (%s)", strings.Join(placeholders, ", ")))
	}

	// Apply filters same as ListWithFilter
	if filter.Search != "" {
		conditions = append(conditions, fmt.Sprintf("(CONCAT(COALESCE(m.first_name, ''), ' ', COALESCE(m.last_name, '')) ILIKE $%d OR m.username ILIKE $%d OR m.phone ILIKE $%d)", argIndex, argIndex, argIndex))
		args = append(args, "%"+filter.Search+"%")
		argIndex++
	}

	if filter.Username != "" {
		conditions = append(conditions, fmt.Sprintf("m.username ILIKE $%d", argIndex))
		args = append(args, "%"+filter.Username+"%")
		argIndex++
	}

	if filter.FullName != "" {
		conditions = append(conditions, fmt.Sprintf("CONCAT(COALESCE(m.first_name, ''), ' ', COALESCE(m.last_name, '')) ILIKE $%d", argIndex))
		args = append(args, "%"+filter.FullName+"%")
		argIndex++
	}

	if filter.BankNumber != "" {
		conditions = append(conditions, fmt.Sprintf("m.bank_number ILIKE $%d", argIndex))
		args = append(args, "%"+filter.BankNumber+"%")
		argIndex++
	}

	if filter.MemberGroupID != nil {
		conditions = append(conditions, fmt.Sprintf("m.member_group_id = $%d", argIndex))
		args = append(args, *filter.MemberGroupID)
		argIndex++
	}

	if filter.ReferUserID != nil {
		conditions = append(conditions, fmt.Sprintf("m.refer_user_id = $%d", argIndex))
		args = append(args, *filter.ReferUserID)
		argIndex++
	}

	if filter.PartnerID != nil {
		conditions = append(conditions, fmt.Sprintf("m.refer_user_id = $%d", argIndex))
		args = append(args, *filter.PartnerID)
		argIndex++
	}

	if filter.CreatedBy != nil {
		conditions = append(conditions, fmt.Sprintf("m.created_by = $%d", argIndex))
		args = append(args, *filter.CreatedBy)
		argIndex++
	}

	if filter.CreatedAt != nil {
		conditions = append(conditions, fmt.Sprintf("DATE(m.created_at) = $%d", argIndex))
		args = append(args, *filter.CreatedAt)
		argIndex++
	}

	// Start datetime filter
	if filter.StartDateTime != nil {
		conditions = append(conditions, fmt.Sprintf("m.created_at >= $%d", argIndex))
		args = append(args, *filter.StartDateTime)
		argIndex++
	}

	// End datetime filter
	if filter.EndDateTime != nil {
		conditions = append(conditions, fmt.Sprintf("m.created_at <= $%d", argIndex))
		args = append(args, *filter.EndDateTime)
		argIndex++
	}

	// Last online start datetime filter
	if filter.LastOnlineStartDateTime != nil {
		conditions = append(conditions, fmt.Sprintf("m.last_online >= $%d", argIndex))
		args = append(args, *filter.LastOnlineStartDateTime)
		argIndex++
	}

	// Last online end datetime filter
	if filter.LastOnlineEndDateTime != nil {
		conditions = append(conditions, fmt.Sprintf("m.last_online <= $%d", argIndex))
		args = append(args, *filter.LastOnlineEndDateTime)
		argIndex++
	}

	// Channel ID filter
	if filter.ChannelID != nil {
		conditions = append(conditions, fmt.Sprintf("m.channel_id = $%d", argIndex))
		args = append(args, *filter.ChannelID)
		argIndex++
	}

	// Platform ID filter
	if filter.PlatformID != nil {
		conditions = append(conditions, fmt.Sprintf("m.platform_id = $%d", argIndex))
		args = append(args, *filter.PlatformID)
		argIndex++
	}

	query := baseQuery
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count members with filter")
		return 0, errors.NewDatabaseError("failed to count members with filter")
	}

	return count, nil
}

// UpdateLoginInfo updates member's last login information
func (r *MemberRepository) UpdateLoginInfo(ctx context.Context, memberID int, clientIP, userAgent, device string) error {
	query := `
		UPDATE members 
		SET last_login_ip = $2,
		    last_login_user_agent = $3,
		    last_login_device = $4,
		    last_online = CURRENT_TIMESTAMP,
		    updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, memberID, clientIP, userAgent, device)
	if err != nil {
		r.logger.WithError(err).WithField("member_id", memberID).Error("failed to update member login info")
		return errors.NewDatabaseError("failed to update member login info")
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return errors.NewNotFoundError("member not found")
	}

	r.logger.WithField("member_id", memberID).Info("member login info updated successfully")
	return nil
}

// GetStatusCounts returns count of members for each status (including 0 counts)
func (r *MemberRepository) GetStatusCounts(ctx context.Context) ([]member.MemberStatusCount, error) {
	query := `
		WITH status_values AS (
			SELECT 'active' as status
			UNION ALL SELECT 'suspended'
			UNION ALL SELECT 'banned'
		)
		SELECT 
			sv.status,
			COALESCE(COUNT(m.status), 0) as count
		FROM status_values sv
		LEFT JOIN members m ON m.status = sv.status
		GROUP BY sv.status
		ORDER BY 
			CASE sv.status
				WHEN 'active' THEN 1
				WHEN 'suspended' THEN 2
				WHEN 'banned' THEN 3
			END
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to get member status counts")
		return nil, errors.NewDatabaseError("failed to get member status counts")
	}
	defer rows.Close()

	var statusCounts []member.MemberStatusCount
	for rows.Next() {
		var statusCount member.MemberStatusCount
		var statusStr string

		if err := rows.Scan(&statusStr, &statusCount.Count); err != nil {
			r.logger.WithError(err).Error("failed to scan member status count")
			return nil, errors.NewDatabaseError("failed to scan member status count")
		}

		statusCount.Status = member.Status(statusStr)
		statusCount.Label = member.GetStatusLabel(statusCount.Status)
		statusCounts = append(statusCounts, statusCount)
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating member status counts")
		return nil, errors.NewDatabaseError("error iterating member status counts")
	}

	r.logger.WithField("counts", len(statusCounts)).Info("member status counts retrieved successfully")
	return statusCounts, nil
}
