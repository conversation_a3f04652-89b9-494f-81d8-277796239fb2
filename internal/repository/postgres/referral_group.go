package postgres

import (
	"context"
	"fmt"

	"blacking-api/internal/domain/referral_group"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type ReferralGroupRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewReferralGroupRepository creates a new referral group repository
func NewReferralGroupRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.ReferralGroupRepository {
	return &ReferralGroupRepository{
		pool:   pool,
		logger: logger,
	}
}

// Create creates a new referral group
func (r *ReferralGroupRepository) Create(ctx context.Context, rg *referral_group.ReferralGroup) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction")
		return errors.NewDatabaseError("failed to create referral group")
	}
	defer tx.Rollback(ctx)

	// If this is set as default, unset all other defaults first
	if rg.IsDefault {
		if err := r.unsetAllDefaultsInTx(ctx, tx); err != nil {
			return err
		}
	}

	query := `
		INSERT INTO referral_groups (name, is_default,
			turnover_sports, turnover_casino, turnover_fishing, turnover_slot, turnover_lottery, turnover_card,
			status, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
		RETURNING id
	`

	var lastInsertID int
	err = dbutil.TxQueryRowWithSchema(ctx, tx, query,
		rg.Name, rg.IsDefault,
		rg.TurnoverSports, rg.TurnoverCasino, rg.TurnoverFishing, rg.TurnoverSlot, rg.TurnoverLottery, rg.TurnoverCard,
		rg.Status, rg.CreatedAt, rg.UpdatedAt,
	).Scan(&lastInsertID)

	if err != nil {
		r.logger.WithError(err).Error("failed to create referral group")
		return errors.NewDatabaseError("failed to create referral group")
	}

	rg.ID = lastInsertID

	if err := tx.Commit(ctx); err != nil {
		r.logger.WithError(err).Error("failed to commit transaction")
		return errors.NewDatabaseError("failed to create referral group")
	}

	r.logger.WithField("referral_group_id", rg.ID).Info("referral group created successfully")
	return nil
}

// GetByID retrieves referral group by ID
func (r *ReferralGroupRepository) GetByID(ctx context.Context, id int) (*referral_group.ReferralGroup, error) {
	query := `
		SELECT id, name, is_default,
			turnover_sports, turnover_casino, turnover_fishing, turnover_slot, turnover_lottery, turnover_card,
			status, created_at, updated_at
		FROM referral_groups
		WHERE id = $1
	`

	rg := &referral_group.ReferralGroup{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&rg.ID, &rg.Name, &rg.IsDefault,
		&rg.TurnoverSports, &rg.TurnoverCasino, &rg.TurnoverFishing, &rg.TurnoverSlot, &rg.TurnoverLottery, &rg.TurnoverCard,
		&rg.Status, &rg.CreatedAt, &rg.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("referral group not found")
		}
		r.logger.WithError(err).Error("failed to get referral group by ID")
		return nil, errors.NewDatabaseError("failed to get referral group")
	}

	return rg, nil
}

// GetDefault retrieves the default referral group
func (r *ReferralGroupRepository) GetDefault(ctx context.Context) (*referral_group.ReferralGroup, error) {
	query := `
		SELECT id, name, is_default,
			turnover_sports, turnover_casino, turnover_fishing, turnover_slot, turnover_lottery, turnover_card,
			status, created_at, updated_at
		FROM referral_groups
		WHERE is_default = true AND status = 'active'
		LIMIT 1
	`

	rg := &referral_group.ReferralGroup{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query).Scan(
		&rg.ID, &rg.Name, &rg.IsDefault,
		&rg.TurnoverSports, &rg.TurnoverCasino, &rg.TurnoverFishing, &rg.TurnoverSlot, &rg.TurnoverLottery, &rg.TurnoverCard,
		&rg.Status, &rg.CreatedAt, &rg.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("default referral group not found")
		}
		r.logger.WithError(err).Error("failed to get default referral group")
		return nil, errors.NewDatabaseError("failed to get default referral group")
	}

	return rg, nil
}

// Update updates an existing referral group
func (r *ReferralGroupRepository) Update(ctx context.Context, rg *referral_group.ReferralGroup) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction")
		return errors.NewDatabaseError("failed to update referral group")
	}
	defer tx.Rollback(ctx)

	// If this is set as default, unset all other defaults first
	if rg.IsDefault {
		if err := r.unsetAllDefaultsInTx(ctx, tx); err != nil {
			return err
		}
	}

	query := `
		UPDATE referral_groups
		SET name = $2, is_default = $3,
			turnover_sports = $4, turnover_casino = $5, turnover_fishing = $6,
			turnover_slot = $7, turnover_lottery = $8, turnover_card = $9, updated_at = $10
		WHERE id = $1 AND status = 'active'
	`

	result, err := dbutil.TxExecWithSchema(ctx, tx, query,
		rg.ID, rg.Name, rg.IsDefault,
		rg.TurnoverSports, rg.TurnoverCasino, rg.TurnoverFishing, rg.TurnoverSlot, rg.TurnoverLottery, rg.TurnoverCard,
		rg.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to update referral group")
		return errors.NewDatabaseError("failed to update referral group")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("referral group not found")
	}

	if err := tx.Commit(ctx); err != nil {
		r.logger.WithError(err).Error("failed to commit transaction")
		return errors.NewDatabaseError("failed to update referral group")
	}

	r.logger.WithField("referral_group_id", rg.ID).Info("referral group updated successfully")
	return nil
}

// Delete soft deletes a referral group (sets status to inactive)
func (r *ReferralGroupRepository) Delete(ctx context.Context, id int) error {
	// Check if this is the default group
	rg, err := r.GetByID(ctx, id)
	if err != nil {
		return err
	}

	if rg.IsDefault {
		return errors.NewValidationError("cannot delete default referral group")
	}

	query := `
		UPDATE referral_groups
		SET status = 'inactive', updated_at = CURRENT_TIMESTAMP
		WHERE id = $1 AND status = 'active'
	`

	commandTag, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete referral group")
		return errors.NewDatabaseError("failed to delete referral group")
	}

	if commandTag.RowsAffected() == 0 {
		return errors.NewNotFoundError("referral group not found")
	}

	r.logger.WithField("referral_group_id", id).Info("referral group deleted successfully")
	return nil
}

// List retrieves referral groups with pagination, search, and sorting
func (r *ReferralGroupRepository) List(ctx context.Context, limit, offset int, search, sortBy, sortOrder string) ([]*referral_group.ReferralGroup, error) {
	baseQuery := `
		SELECT id, name, is_default,
			turnover_sports, turnover_casino, turnover_fishing, turnover_slot, turnover_lottery, turnover_card,
			status, created_at, updated_at
		FROM referral_groups
		WHERE status = 'active'
	`

	var args []interface{}
	argIndex := 1

	// Add search condition
	if search != "" {
		baseQuery += fmt.Sprintf(" AND name ILIKE $%d", argIndex)
		args = append(args, "%"+search+"%")
		argIndex++
	}

	// Add sorting
	validSortColumns := map[string]bool{
		"id":         true,
		"code":       true,
		"name":       true,
		"is_default": true,
		"created_at": true,
		"updated_at": true,
	}

	if sortBy != "" && validSortColumns[sortBy] {
		if sortOrder == "desc" {
			baseQuery += fmt.Sprintf(" ORDER BY %s DESC", sortBy)
		} else {
			baseQuery += fmt.Sprintf(" ORDER BY %s ASC", sortBy)
		}
	} else {
		baseQuery += " ORDER BY is_default DESC, created_at DESC" // Default first, then by created_at
	}

	// Add pagination
	baseQuery += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, offset)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, baseQuery, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list referral groups")
		return nil, errors.NewDatabaseError("failed to list referral groups")
	}
	defer rows.Close()

	var referralGroups []*referral_group.ReferralGroup
	for rows.Next() {
		rg := &referral_group.ReferralGroup{}
		err := rows.Scan(
			&rg.ID, &rg.Name, &rg.IsDefault,
			&rg.TurnoverSports, &rg.TurnoverCasino, &rg.TurnoverFishing, &rg.TurnoverSlot, &rg.TurnoverLottery, &rg.TurnoverCard,
			&rg.Status, &rg.CreatedAt, &rg.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan referral group")
			return nil, errors.NewDatabaseError("failed to scan referral group")
		}
		referralGroups = append(referralGroups, rg)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating referral group rows")
		return nil, errors.NewDatabaseError("error iterating referral group rows")
	}

	return referralGroups, nil
}

// Count returns total count of referral groups with search filter
func (r *ReferralGroupRepository) Count(ctx context.Context, search string) (int64, error) {
	query := "SELECT COUNT(*) FROM referral_groups WHERE status = 'active'"
	var args []interface{}

	if search != "" {
		query += " AND name ILIKE $1"
		args = append(args, "%"+search+"%")
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count referral groups")
		return 0, errors.NewDatabaseError("failed to count referral groups")
	}

	return count, nil
}

// ListActive retrieves only active referral groups
func (r *ReferralGroupRepository) ListActive(ctx context.Context) ([]*referral_group.ReferralGroup, error) {
	query := `
		SELECT id, name, is_default,
			turnover_sports, turnover_casino, turnover_fishing, turnover_slot, turnover_lottery, turnover_card,
			status, created_at, updated_at
		FROM referral_groups
		WHERE status = 'active'
		ORDER BY is_default DESC, name ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to list active referral groups")
		return nil, errors.NewDatabaseError("failed to list active referral groups")
	}
	defer rows.Close()

	var referralGroups []*referral_group.ReferralGroup
	for rows.Next() {
		rg := &referral_group.ReferralGroup{}
		err := rows.Scan(
			&rg.ID, &rg.Name, &rg.IsDefault,
			&rg.TurnoverSports, &rg.TurnoverCasino, &rg.TurnoverFishing, &rg.TurnoverSlot, &rg.TurnoverLottery, &rg.TurnoverCard,
			&rg.Status, &rg.CreatedAt, &rg.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan active referral group")
			return nil, errors.NewDatabaseError("failed to scan active referral group")
		}
		referralGroups = append(referralGroups, rg)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating active referral group rows")
		return nil, errors.NewDatabaseError("error iterating active referral group rows")
	}

	return referralGroups, nil
}

// ListForDropdown retrieves unique referral group names for dropdown filter
func (r *ReferralGroupRepository) ListForDropdown(ctx context.Context) ([]*referral_group.DropdownItem, error) {
	query := `
		SELECT id, name
		FROM referral_groups
		WHERE status = 'active'
		ORDER BY is_default DESC, name ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to list referral groups for dropdown")
		return nil, errors.NewDatabaseError("failed to list referral groups for dropdown")
	}
	defer rows.Close()

	var items []*referral_group.DropdownItem
	for rows.Next() {
		item := &referral_group.DropdownItem{}
		err := rows.Scan(&item.ID, &item.Name)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan referral group dropdown item")
			return nil, errors.NewDatabaseError("failed to scan referral group dropdown item")
		}
		items = append(items, item)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating referral group dropdown rows")
		return nil, errors.NewDatabaseError("error iterating referral group dropdown rows")
	}

	return items, nil
}

// SetDefault sets a referral group as default (and unsets others)
func (r *ReferralGroupRepository) SetDefault(ctx context.Context, id int) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction")
		return errors.NewDatabaseError("failed to set default referral group")
	}
	defer tx.Rollback(ctx)

	// Unset all defaults first
	if err := r.unsetAllDefaultsInTx(ctx, tx); err != nil {
		return err
	}

	// Set the specified group as default
	query := `
		UPDATE referral_groups
		SET is_default = true, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1 AND status = 'active'
	`

	result, err := dbutil.TxExecWithSchema(ctx, tx, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to set default referral group")
		return errors.NewDatabaseError("failed to set default referral group")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("referral group not found")
	}

	if err := tx.Commit(ctx); err != nil {
		r.logger.WithError(err).Error("failed to commit transaction")
		return errors.NewDatabaseError("failed to set default referral group")
	}

	r.logger.WithField("referral_group_id", id).Info("referral group set as default successfully")
	return nil
}

// UnsetAllDefaults unsets all referral groups as default
func (r *ReferralGroupRepository) UnsetAllDefaults(ctx context.Context) error {
	query := `
		UPDATE referral_groups
		SET is_default = false, updated_at = CURRENT_TIMESTAMP
		WHERE is_default = true AND status = 'active'
	`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to unset all default referral groups")
		return errors.NewDatabaseError("failed to unset all default referral groups")
	}

	r.logger.Info("all referral groups unset as default successfully")
	return nil
}

// Helper methods for transaction operations

// unsetAllDefaultsInTx unsets all referral groups as default within a transaction
func (r *ReferralGroupRepository) unsetAllDefaultsInTx(ctx context.Context, tx pgx.Tx) error {
	query := `
		UPDATE referral_groups
		SET is_default = false, updated_at = CURRENT_TIMESTAMP
		WHERE is_default = true AND status = 'active'
	`

	_, err := dbutil.TxExecWithSchema(ctx, tx, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to unset all default referral groups in transaction")
		return errors.NewDatabaseError("failed to unset all default referral groups")
	}

	return nil
}
