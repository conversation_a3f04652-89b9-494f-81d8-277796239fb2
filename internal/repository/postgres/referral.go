package postgres

import (
	"context"
	"strings"
	"time"

	"blacking-api/internal/domain/referral"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
)

type ReferralRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewReferralRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.ReferralRepository {
	return &ReferralRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *ReferralRepository) GetOverview(memberID int) (*referral.OverviewResponse, error) {
	ctx := context.Background()

	var overview referral.OverviewResponse

	// 1. total_referrals = member -> downline_count
	// 2. referral_view_count = member -> referral_view_count
	// 3. commission_balance = member -> commission_balance
	memberQuery := `
		SELECT 
			COALESCE(downline_count, 0) as total_referrals,
			COALESCE(referral_view_count, 0) as referral_view_count,
			COALESCE(commission_balance, 0) as commission_balance
		FROM members 
		WHERE id = $1
	`

	err := dbutil.QueryRowWithSchema(ctx, r.pool, memberQuery, memberID).Scan(
		&overview.TotalReferrals,
		&overview.ReferralViewCount,
		&overview.CommissionBalance,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			// Member not found, return zero values
			overview.TotalReferrals = 0
			overview.ReferralViewCount = 0
			overview.CommissionBalance = 0
		} else {
			r.logger.WithError(err).Error("failed to get member overview data")
			return nil, errors.NewDatabaseError("failed to get member overview data")
		}
	}

	// 4. today_downline_register_count = count downline ที่สมัครวันนี้
	todayCountQuery := `
		SELECT COUNT(*) 
		FROM members 
		WHERE refer_user_id = $1 
		AND DATE(created_at) = CURRENT_DATE
	`

	err = dbutil.QueryRowWithSchema(ctx, r.pool, todayCountQuery, memberID).Scan(&overview.TodayDownlineRegisterCount)
	if err != nil {
		r.logger.WithError(err).Error("failed to get today downline register count")
		overview.TodayDownlineRegisterCount = 0
	}

	// 5. commission_growth_percentage = compare sum(referral_transactions last month) vs sum(referral_transactions this month)
	// Only include commission type transactions (positive amounts)
	growthQuery := `
		SELECT 
			COALESCE(SUM(CASE 
				WHEN DATE_TRUNC('month', created_at) = DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month') 
				AND type = 'commission' AND amount > 0
				THEN amount 
				ELSE 0 
			END), 0) as last_month_total,
			COALESCE(SUM(CASE 
				WHEN DATE_TRUNC('month', created_at) = DATE_TRUNC('month', CURRENT_DATE) 
				AND type = 'commission' AND amount > 0
				THEN amount 
				ELSE 0 
			END), 0) as this_month_total
		FROM referral_transactions 
		WHERE member_id = $1 
		AND created_at >= DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month')
		AND type = 'commission'
	`

	var lastMonthTotal, thisMonthTotal float64
	err = dbutil.QueryRowWithSchema(ctx, r.pool, growthQuery, memberID).Scan(
		&lastMonthTotal,
		&thisMonthTotal,
	)
	if err != nil {
		if err != pgx.ErrNoRows {
			r.logger.WithError(err).Error("failed to get referral transaction growth data")
		}
		lastMonthTotal = 0
		thisMonthTotal = 0
	}

	// Calculate growth percentage
	var growthPercent float64
	if lastMonthTotal > 0 {
		growthPercent = ((thisMonthTotal - lastMonthTotal) / lastMonthTotal) * 100
	} else if thisMonthTotal > 0 {
		growthPercent = 100.0 // If no last month data but have this month, it's 100% growth
	}

	// Set commission growth percentage
	overview.CommissionGrowthPercentage = growthPercent

	return &overview, nil
}

func (r *ReferralRepository) GetDownlines(memberID int) ([]referral.ReferralMember, error) {
	ctx := context.Background()

	query := `
		SELECT 
			id,
			username,
			phone,
			COALESCE(downline_count, 0) as downline_count,
			created_at,
			status
		FROM members 
		WHERE refer_user_id = $1
		ORDER BY created_at DESC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, memberID)
	if err != nil {
		r.logger.WithError(err).Error("failed to get downlines")
		return nil, errors.NewDatabaseError("failed to get downlines")
	}
	defer rows.Close()

	var downlines []referral.ReferralMember
	for rows.Next() {
		var downline referral.ReferralMember
		err := rows.Scan(
			&downline.MemberID,
			&downline.Username,
			&downline.Phone,
			&downline.DownlineCount,
			&downline.JoinedAt,
			&downline.Status,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan downline")
			continue
		}
		downlines = append(downlines, downline)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating downline rows")
		return nil, errors.NewDatabaseError("error retrieving downline data")
	}

	return downlines, nil
}

func (r *ReferralRepository) GetDownlinesPaginated(memberID int, page, limit int) ([]referral.ReferralMember, int, error) {
	ctx := context.Background()

	// Validate pagination parameters
	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = 20
	}
	if limit > 1000 {
		limit = 1000 // Maximum limit for performance
	}

	// First get the total count
	countQuery := `
		SELECT COUNT(*) 
		FROM members 
		WHERE refer_user_id = $1
	`

	var totalCount int
	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, memberID).Scan(&totalCount)
	if err != nil {
		r.logger.WithError(err).Error("failed to get downlines count")
		return nil, 0, errors.NewDatabaseError("failed to get downlines count")
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Get paginated downlines
	query := `
		SELECT 
			id,
			username,
			phone,
			COALESCE(downline_count, 0) as downline_count,
			created_at,
			status
		FROM members 
		WHERE refer_user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, memberID, limit, offset)
	if err != nil {
		r.logger.WithError(err).Error("failed to get paginated downlines")
		return nil, 0, errors.NewDatabaseError("failed to get paginated downlines")
	}
	defer rows.Close()

	var downlines []referral.ReferralMember
	for rows.Next() {
		var downline referral.ReferralMember
		err := rows.Scan(
			&downline.MemberID,
			&downline.Username,
			&downline.Phone,
			&downline.DownlineCount,
			&downline.JoinedAt,
			&downline.Status,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan downline")
			continue
		}
		downlines = append(downlines, downline)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating downline rows")
		return nil, 0, errors.NewDatabaseError("error retrieving downline data")
	}

	return downlines, totalCount, nil
}

func (r *ReferralRepository) GetIncomeData(memberID int, months int) (*referral.IncomeResponse, error) {
	ctx := context.Background()

	// 1. Get total income from commission balance + commission growth percentage from GetOverview
	var overview referral.OverviewResponse
	memberQuery := `
		SELECT 
			COALESCE(commission_balance, 0) as commission_balance
		FROM members 
		WHERE id = $1
	`

	err := dbutil.QueryRowWithSchema(ctx, r.pool, memberQuery, memberID).Scan(&overview.CommissionBalance)
	if err != nil {
		if err == pgx.ErrNoRows {
			overview.CommissionBalance = 0
		} else {
			r.logger.WithError(err).Error("failed to get member commission balance")
			return nil, errors.NewDatabaseError("failed to get member commission balance")
		}
	}

	// Get commission growth percentage (same logic from GetOverview)
	growthQuery := `
		SELECT 
			COALESCE(SUM(CASE 
				WHEN DATE_TRUNC('month', created_at) = DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month') 
				AND type = 'commission' AND amount > 0
				THEN amount 
				ELSE 0 
			END), 0) as last_month_total,
			COALESCE(SUM(CASE 
				WHEN DATE_TRUNC('month', created_at) = DATE_TRUNC('month', CURRENT_DATE) 
				AND type = 'commission' AND amount > 0
				THEN amount 
				ELSE 0 
			END), 0) as this_month_total
		FROM referral_transactions 
		WHERE member_id = $1 
		AND created_at >= DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month')
		AND type = 'commission'
	`

	var lastMonthTotal, thisMonthTotal float64
	err = dbutil.QueryRowWithSchema(ctx, r.pool, growthQuery, memberID).Scan(&lastMonthTotal, &thisMonthTotal)
	if err != nil && err != pgx.ErrNoRows {
		r.logger.WithError(err).Error("failed to get commission growth data")
		// Continue without growth percentage
	}

	var commissionGrowthPercentage float64
	if lastMonthTotal > 0 {
		commissionGrowthPercentage = ((thisMonthTotal - lastMonthTotal) / lastMonthTotal) * 100
	} else if thisMonthTotal > 0 {
		commissionGrowthPercentage = 100.0
	}

	// 2. Get recent transactions with Thai translations for type and status
	transactionQuery := `
		SELECT 
			rt.id,
			COALESCE(m.username, '') as member_username,
			COALESCE(m.refer_code, '') as refer_code,
			rt.type,
			rt.amount,
			rt.game_category,
			rt.created_at as transaction_date,
			rt.status,
			rt.remark,
			rt.created_by_admin,
			rt.created_by_member,
			CASE 
				WHEN rt.created_by_admin IS NOT NULL THEN 'admin'
				WHEN rt.created_by_member IS NOT NULL THEN 'member' 
				ELSE 'system'
			END as creator_type,
			-- Thai translations for type
			CASE 
				WHEN rt.type = 'commission' THEN 'ค่าคอมมิชชั่น'
				WHEN rt.type = 'withdraw' THEN 'ถอนเงิน'
				WHEN rt.type = 'adjustment' THEN 'ปรับปรุง'
				WHEN rt.type = 'bonus' THEN 'โบนัส'
				ELSE rt.type
			END as type_thai,
			-- Thai translations for status  
			CASE 
				WHEN rt.status = 'pending' THEN 'รอดำเนินการ'
				WHEN rt.status = 'success' THEN 'สำเร็จ'
				WHEN rt.status = 'cancel' THEN 'ยกเลิก'
				ELSE rt.status
			END as status_thai
		FROM referral_transactions rt
		LEFT JOIN members m ON rt.downline_member_id = m.id
		WHERE rt.member_id = $1
		ORDER BY rt.id DESC
		LIMIT 10
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, transactionQuery, memberID)
	if err != nil {
		r.logger.WithError(err).Error("failed to get recent transactions")
		return nil, errors.NewDatabaseError("failed to get recent transactions")
	}
	defer rows.Close()

	var recentTransactions []referral.IncomeTransaction
	for rows.Next() {
		var transaction referral.IncomeTransaction
		var typeThai, statusThai string
		var remark *string                       // Handle NULL remark
		var createdByAdmin, createdByMember *int // Handle NULL admin/member IDs
		var transactionDate *time.Time           // Handle NULL transaction date

		err := rows.Scan(
			&transaction.ID,
			&transaction.MemberUsername,
			&transaction.ReferCode,
			&transaction.Type,
			&transaction.Amount,
			&transaction.GameCategory,
			&transactionDate,
			&transaction.Status,
			&remark,
			&createdByAdmin,
			&createdByMember,
			&transaction.CreatorType,
			&typeThai,
			&statusThai,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan transaction")
			continue
		}

		// Set nullable fields properly
		transaction.Remark = remark
		transaction.CreatedByAdmin = createdByAdmin
		transaction.CreatedByMember = createdByMember

		// Handle NULL transaction date
		if transactionDate != nil {
			transaction.TransactionDate = *transactionDate
		} else {
			// Use zero time if NULL
			transaction.TransactionDate = time.Time{}
		}

		// Override type and status with Thai translations
		transaction.Type = typeThai
		transaction.Status = statusThai

		recentTransactions = append(recentTransactions, transaction)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating transaction rows")
		return nil, errors.NewDatabaseError("error retrieving transaction data")
	}

	// Prepare response with updated structure
	response := &referral.IncomeResponse{
		TotalIncome:                overview.CommissionBalance,
		CommissionGrowthPercentage: commissionGrowthPercentage,
		RecentTransactions:         recentTransactions,
		// Note: monthly_income is removed as requested
	}

	return response, nil
}

func (r *ReferralRepository) GetIncomeDataPaginated(memberID int, req referral.IncomeRequest) (*referral.IncomeResponse, error) {
	ctx := context.Background()

	// Validate pagination parameters
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10 // Default limit for transactions
	}
	if req.Limit > 100 {
		req.Limit = 100 // Maximum limit for performance
	}

	// Validate months parameter
	if req.Months < 1 || req.Months > 24 {
		req.Months = 12 // Default to 12 months
	}

	// 1. Get total income from commission balance + commission growth percentage from GetOverview
	var overview referral.OverviewResponse
	memberQuery := `
		SELECT 
			COALESCE(commission_balance, 0) as commission_balance
		FROM members 
		WHERE id = $1
	`

	err := dbutil.QueryRowWithSchema(ctx, r.pool, memberQuery, memberID).Scan(&overview.CommissionBalance)
	if err != nil {
		if err == pgx.ErrNoRows {
			overview.CommissionBalance = 0
		} else {
			r.logger.WithError(err).Error("failed to get member commission balance")
			return nil, errors.NewDatabaseError("failed to get member commission balance")
		}
	}

	// Get commission growth percentage (same logic from GetOverview)
	growthQuery := `
		SELECT 
			COALESCE(SUM(CASE 
				WHEN DATE_TRUNC('month', created_at) = DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month') 
				AND type = 'commission' AND amount > 0
				THEN amount 
				ELSE 0 
			END), 0) as last_month_total,
			COALESCE(SUM(CASE 
				WHEN DATE_TRUNC('month', created_at) = DATE_TRUNC('month', CURRENT_DATE) 
				AND type = 'commission' AND amount > 0
				THEN amount 
				ELSE 0 
			END), 0) as this_month_total
		FROM referral_transactions 
		WHERE member_id = $1 
		AND created_at >= DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month')
		AND type = 'commission'
	`

	var lastMonthTotal, thisMonthTotal float64
	err = dbutil.QueryRowWithSchema(ctx, r.pool, growthQuery, memberID).Scan(&lastMonthTotal, &thisMonthTotal)
	if err != nil && err != pgx.ErrNoRows {
		r.logger.WithError(err).Error("failed to get commission growth data")
		// Continue without growth percentage
	}

	var commissionGrowthPercentage float64
	if lastMonthTotal > 0 {
		commissionGrowthPercentage = ((thisMonthTotal - lastMonthTotal) / lastMonthTotal) * 100
	} else if thisMonthTotal > 0 {
		commissionGrowthPercentage = 100.0
	}

	// 2. Get total count of transactions for pagination
	countQuery := `
		SELECT COUNT(*)
		FROM referral_transactions rt
		WHERE rt.member_id = $1
	`

	var totalCount int
	err = dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, memberID).Scan(&totalCount)
	if err != nil {
		r.logger.WithError(err).Error("failed to get transaction count")
		return nil, errors.NewDatabaseError("failed to get transaction count")
	}

	// Calculate pagination metadata
	totalPages := (totalCount + req.Limit - 1) / req.Limit
	pagination := &referral.PaginationMeta{
		Page:       req.Page,
		Limit:      req.Limit,
		TotalPages: totalPages,
		TotalCount: totalCount,
		HasNext:    req.Page < totalPages,
		HasPrev:    req.Page > 1,
	}

	// Calculate offset
	offset := (req.Page - 1) * req.Limit

	// 3. Get paginated recent transactions with Thai translations for type and status
	transactionQuery := `
		SELECT 
			rt.id,
			COALESCE(m.username, '') as member_username,
			COALESCE(m.refer_code, '') as refer_code,
			rt.type,
			rt.amount,
			rt.game_category,
			rt.created_at as transaction_date,
			rt.status,
			rt.remark,
			rt.created_by_admin,
			rt.created_by_member,
			CASE 
				WHEN rt.created_by_admin IS NOT NULL THEN 'admin'
				WHEN rt.created_by_member IS NOT NULL THEN 'member' 
				ELSE 'system'
			END as creator_type,
			-- Thai translations for type
			CASE 
				WHEN rt.type = 'commission' THEN 'ค่าคอมมิชชั่น'
				WHEN rt.type = 'withdraw' THEN 'ถอนเงิน'
				WHEN rt.type = 'adjustment' THEN 'ปรับปรุง'
				WHEN rt.type = 'bonus' THEN 'โบนัส'
				ELSE rt.type
			END as type_thai,
			-- Thai translations for status  
			CASE 
				WHEN rt.status = 'pending' THEN 'รอดำเนินการ'
				WHEN rt.status = 'success' THEN 'สำเร็จ'
				WHEN rt.status = 'cancel' THEN 'ยกเลิก'
				ELSE rt.status
			END as status_thai
		FROM referral_transactions rt
		LEFT JOIN members m ON rt.downline_member_id = m.id
		WHERE rt.member_id = $1
		ORDER BY rt.id DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, transactionQuery, memberID, req.Limit, offset)
	if err != nil {
		r.logger.WithError(err).Error("failed to get paginated recent transactions")
		return nil, errors.NewDatabaseError("failed to get paginated recent transactions")
	}
	defer rows.Close()

	var recentTransactions []referral.IncomeTransaction
	for rows.Next() {
		var transaction referral.IncomeTransaction
		var typeThai, statusThai string
		var remark *string                       // Handle NULL remark
		var createdByAdmin, createdByMember *int // Handle NULL admin/member IDs
		var transactionDate *time.Time           // Handle NULL transaction date

		err := rows.Scan(
			&transaction.ID,
			&transaction.MemberUsername,
			&transaction.ReferCode,
			&transaction.Type,
			&transaction.Amount,
			&transaction.GameCategory,
			&transactionDate,
			&transaction.Status,
			&remark,
			&createdByAdmin,
			&createdByMember,
			&transaction.CreatorType,
			&typeThai,
			&statusThai,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan transaction")
			continue
		}

		// Set nullable fields properly
		transaction.Remark = remark
		transaction.CreatedByAdmin = createdByAdmin
		transaction.CreatedByMember = createdByMember

		// Handle NULL transaction date
		if transactionDate != nil {
			transaction.TransactionDate = *transactionDate
		} else {
			// Use zero time if NULL
			transaction.TransactionDate = time.Time{}
		}

		// Override type and status with Thai translations
		transaction.Type = typeThai
		transaction.Status = statusThai

		recentTransactions = append(recentTransactions, transaction)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating transaction rows")
		return nil, errors.NewDatabaseError("error retrieving transaction data")
	}

	// Prepare response with pagination
	response := &referral.IncomeResponse{
		TotalIncome:                overview.CommissionBalance,
		CommissionGrowthPercentage: commissionGrowthPercentage,
		RecentTransactions:         recentTransactions,
		Pagination:                 pagination,
	}

	return response, nil
}

func (r *ReferralRepository) GetByMemberID(memberID int) ([]referral.Register, error) {
	ctx := context.Background()

	query := `
		SELECT id, refer_user_id, register_refer_code, commission, member_id, 
			   refer_down_line_count, created_at, updated_at
		FROM referral_registers 
		WHERE member_id = $1
		ORDER BY created_at DESC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, memberID)
	if err != nil {
		r.logger.WithError(err).Error("failed to get registers by member id")
		return nil, errors.NewDatabaseError("failed to get registers by member id")
	}
	defer rows.Close()

	var registers []referral.Register
	for rows.Next() {
		var register referral.Register
		err := rows.Scan(
			&register.ID,
			&register.ReferUserID,
			&register.RegisterReferCode,
			&register.Commission,
			&register.MemberID,
			&register.ReferDownLineCount,
			&register.CreatedAt,
			&register.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan register")
			continue
		}
		registers = append(registers, register)
	}

	return registers, nil
}

func (r *ReferralRepository) Create(register *referral.Register) error {
	ctx := context.Background()

	query := `
		INSERT INTO referral_registers (id, refer_user_id, register_refer_code, commission, 
							  member_id, refer_down_line_count, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
	`

	if register.ID == uuid.Nil {
		register.ID = uuid.New()
	}

	now := time.Now()
	register.CreatedAt = now
	register.UpdatedAt = now

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		register.ID,
		register.ReferUserID,
		register.RegisterReferCode,
		register.Commission,
		register.MemberID,
		register.ReferDownLineCount,
		register.CreatedAt,
		register.UpdatedAt,
	)

	if err != nil {
		if pgErr, ok := err.(*pgconn.PgError); ok {
			switch pgErr.Code {
			case "23505": // unique_violation
				return errors.NewValidationError("register already exists")
			case "23503": // foreign_key_violation
				return errors.NewValidationError("invalid foreign key reference")
			}
		}
		r.logger.WithError(err).Error("failed to create register")
		return errors.NewDatabaseError("failed to create register")
	}

	return nil
}

func (r *ReferralRepository) GetRegisterByMemberID(memberID uuid.UUID) (*referral.Register, error) {
	ctx := context.Background()

	query := `
		SELECT id, refer_user_id, register_refer_code, commission, member_id,
			   refer_down_line_count, created_at, updated_at
		FROM referral_registers 
		WHERE member_id = $1
	`

	var register referral.Register
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, memberID).Scan(
		&register.ID,
		&register.ReferUserID,
		&register.RegisterReferCode,
		&register.Commission,
		&register.MemberID,
		&register.ReferDownLineCount,
		&register.CreatedAt,
		&register.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("register not found")
		}
		r.logger.WithError(err).Error("failed to get register by member id")
		return nil, errors.NewDatabaseError("failed to get register by member id")
	}

	return &register, nil
}

func (r *ReferralRepository) IncrementReferralView(memberID int) error {
	ctx := context.Background()

	query := `
		UPDATE members 
		SET referral_view_count = COALESCE(referral_view_count, 0) + 1 
		WHERE id = $1
	`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query, memberID)
	if err != nil {
		r.logger.WithError(err).WithField("member_id", memberID).Error("failed to increment referral view count")
		return errors.NewDatabaseError("failed to increment referral view count")
	}

	r.logger.WithField("member_id", memberID).Info("referral view count incremented successfully")
	return nil
}

func (r *ReferralRepository) IncrementReferralViewByCode(referralCode string) error {
	ctx := context.Background()

	// First find the member by referral code from referral_registers table
	// Then increment the view count in members table
	query := `
		UPDATE members 
		SET referral_view_count = COALESCE(referral_view_count, 0) + 1 
		WHERE refer_code = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, referralCode)
	if err != nil {
		r.logger.WithError(err).WithField("referral_code", referralCode).Error("failed to increment referral view count by code")
		return errors.NewDatabaseError("failed to increment referral view count by code")
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		r.logger.WithField("referral_code", referralCode).Warn("referral code not found")
		return errors.NewNotFoundError("referral code not found")
	}

	r.logger.WithField("referral_code", referralCode).Info("referral view count incremented successfully by code")
	return nil
}

func (r *ReferralRepository) IncrementDownlineCount(memberID int) error {
	ctx := context.Background()

	query := `
		UPDATE members 
		SET downline_count = COALESCE(downline_count, 0) + 1 
		WHERE id = $1
	`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query, memberID)
	if err != nil {
		r.logger.WithError(err).WithField("member_id", memberID).Error("failed to increment downline count")
		return errors.NewDatabaseError("failed to increment downline count")
	}

	r.logger.WithField("member_id", memberID).Info("downline count incremented successfully")
	return nil
}

func (r *ReferralRepository) GetCommissionBalance(memberID int) (float64, error) {
	ctx := context.Background()

	query := `
		SELECT COALESCE(commission_balance, 0) 
		FROM members 
		WHERE id = $1
	`

	var balance float64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, memberID).Scan(&balance)
	if err != nil {
		if err == pgx.ErrNoRows {
			return 0.0, nil // Member not found, return 0 balance
		}
		r.logger.WithError(err).WithField("member_id", memberID).Error("failed to get commission balance")
		return 0.0, errors.NewDatabaseError("failed to get commission balance")
	}

	return balance, nil
}

func (r *ReferralRepository) CreateWithdrawTransaction(memberID int, transaction *referral.ReferralTransaction) (int, error) {
	ctx := context.Background()

	// Begin transaction
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction")
		return 0, errors.NewDatabaseError("failed to begin transaction")
	}
	defer tx.Rollback(ctx)

	// Insert referral transaction
	insertQuery := `
		INSERT INTO referral_transactions (
			member_id, type, amount, balance_before, balance_after,
			referral_start_date, referral_end_date, status, remark,
			created_by_member, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
		RETURNING id
	`

	var transactionID int
	err = dbutil.TxQueryRowWithSchema(ctx, tx, insertQuery,
		transaction.MemberID,
		transaction.Type,
		transaction.Amount,
		transaction.BalanceBefore,
		transaction.BalanceAfter,
		transaction.ReferralStartDate,
		transaction.ReferralEndDate,
		transaction.Status,
		transaction.Remark,
		memberID, // created_by_member = member_id ที่ทำการถอน
		transaction.CreatedAt,
		transaction.UpdatedAt,
	).Scan(&transactionID)

	if err != nil {
		r.logger.WithError(err).WithField("member_id", memberID).Error("failed to insert referral transaction")
		return 0, errors.NewDatabaseError("failed to insert referral transaction")
	}

	// Update member commission balance
	updateQuery := `
		UPDATE members 
		SET commission_balance = $1 
		WHERE id = $2
	`

	_, err = dbutil.TxExecWithSchema(ctx, tx, updateQuery, transaction.BalanceAfter, memberID)
	if err != nil {
		r.logger.WithError(err).WithField("member_id", memberID).Error("failed to update commission balance")
		return 0, errors.NewDatabaseError("failed to update commission balance")
	}

	// Commit transaction
	err = tx.Commit(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to commit transaction")
		return 0, errors.NewDatabaseError("failed to commit transaction")
	}

	r.logger.WithFields(map[string]interface{}{
		"transaction_id":  transactionID,
		"member_id":       memberID,
		"withdraw_amount": -transaction.Amount, // Amount is negative for withdraw
		"new_balance":     transaction.BalanceAfter,
	}).Info("withdraw transaction created and balance updated successfully")

	return transactionID, nil
}

func (r *ReferralRepository) GetYesterdayCommission(memberID int) (float64, error) {
	ctx := context.Background()

	query := `
		SELECT COALESCE(SUM(amount), 0) 
		FROM referral_transactions 
		WHERE member_id = $1 
		AND type = 'commission' 
		AND amount > 0
		AND DATE(referral_start_date) = CURRENT_DATE - INTERVAL '1 day'
		AND DATE(referral_end_date) = CURRENT_DATE - INTERVAL '1 day'
	`

	var yesterdayCommission float64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, memberID).Scan(&yesterdayCommission)
	if err != nil {
		if err == pgx.ErrNoRows {
			return 0.0, nil // No commission yesterday
		}
		r.logger.WithError(err).WithField("member_id", memberID).Error("failed to get yesterday commission")
		return 0.0, errors.NewDatabaseError("failed to get yesterday commission")
	}

	return yesterdayCommission, nil
}

func (r *ReferralRepository) GetPendingCommissions(memberID, page, limit int) ([]referral.PendingCommissionTransaction, int, float64, map[string]float64, map[string]float64, error) {
	ctx := context.Background()

	offset := (page - 1) * limit

	// Query for pending commission transactions for specific member
	query := `
		SELECT 
			rt.id,
			rt.member_id,
			m.username as member_username,
			rt.downline_member_id,
			dm.username as downline_username,
			rt.type,
			rt.amount,
			rt.game_category,
			rt.referral_start_date,
			rt.referral_end_date,
			rt.status,
			rt.remark,
			rt.created_at
		FROM referral_transactions rt
		INNER JOIN members m ON rt.member_id = m.id
		LEFT JOIN members dm ON rt.downline_member_id = dm.id
		WHERE rt.member_id = $1 AND rt.status = 'pending' AND rt.type = 'commission'
		ORDER BY rt.created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, memberID, limit, offset)
	if err != nil {
		r.logger.WithError(err).Error("failed to get pending commissions")
		return nil, 0, 0, nil, nil, errors.NewDatabaseError("failed to get pending commissions")
	}
	defer rows.Close()

	var pendingCommissions []referral.PendingCommissionTransaction
	for rows.Next() {
		var commission referral.PendingCommissionTransaction
		err := rows.Scan(
			&commission.ID,
			&commission.MemberID,
			&commission.MemberUsername,
			&commission.DownlineMemberID,
			&commission.DownlineUsername,
			&commission.Type,
			&commission.Amount,
			&commission.GameCategory,
			&commission.ReferralStartDate,
			&commission.ReferralEndDate,
			&commission.Status,
			&commission.Remark,
			&commission.CreatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan pending commission")
			continue
		}
		pendingCommissions = append(pendingCommissions, commission)
	}

	// Get total count and sum for pagination for specific member
	statsQuery := `
		SELECT COUNT(*), COALESCE(SUM(amount), 0)
		FROM referral_transactions 
		WHERE member_id = $1 AND status = 'pending' AND type = 'commission'
	`

	var totalCount int
	var totalAmount float64
	err = dbutil.QueryRowWithSchema(ctx, r.pool, statsQuery, memberID).Scan(&totalCount, &totalAmount)
	if err != nil {
		r.logger.WithError(err).Error("failed to get pending commission stats")
		return nil, 0, 0, nil, nil, errors.NewDatabaseError("failed to get pending commission stats")
	}

	// Get sum by game category
	categoryQuery := `
		SELECT 
			COALESCE(game_category, 'unknown') as category,
			COALESCE(SUM(amount), 0) as total
		FROM referral_transactions 
		WHERE member_id = $1 AND status = 'pending' AND type = 'commission'
		GROUP BY game_category
	`

	categoryRows, err := dbutil.QueryWithSchema(ctx, r.pool, categoryQuery, memberID)
	if err != nil {
		r.logger.WithError(err).Error("failed to get pending commission by category")
		return nil, 0, 0, nil, nil, errors.NewDatabaseError("failed to get pending commission by category")
	}
	defer categoryRows.Close()

	// Initialize with standard categories set to 0
	totalByCategory := map[string]float64{
		"Slot":        0.0,
		"Live-Casino": 0.0,
		"Sport":       0.0,
	}

	// Update with actual values from database
	for categoryRows.Next() {
		var category string
		var total float64
		err := categoryRows.Scan(&category, &total)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan category total")
			continue
		}
		
		// Normalize category and accumulate totals
		normalizedCategory := normalizeGameCategory(category)
		if normalizedCategory != "" {
			totalByCategory[normalizedCategory] += total
		}
	}

	// Fixed commission percent by category (using standard format)
	commissionPercentByCategory := map[string]float64{
		"Slot":        1.5,
		"Live-Casino": 1.5,
		"Sport":       1.5,
	}

	return pendingCommissions, totalCount, totalAmount, commissionPercentByCategory, totalByCategory, nil
}

func (r *ReferralRepository) ApproveCommissions(memberID int) (int, float64, []int, error) {
	ctx := context.Background()

	// Begin transaction
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction")
		return 0, 0, nil, errors.NewDatabaseError("failed to begin transaction")
	}
	defer tx.Rollback(ctx)

	// Get pending commission transactions for specific member
	query := `
		SELECT 
			id, member_id, amount
		FROM referral_transactions 
		WHERE member_id = $1 AND status = 'pending' AND type = 'commission'
	`

	rows, err := dbutil.TxQueryWithSchema(ctx, tx, query, memberID)
	if err != nil {
		r.logger.WithError(err).Error("failed to get pending commissions for approval")
		return 0, 0, nil, errors.NewDatabaseError("failed to get pending commissions")
	}
	defer rows.Close()

	type pendingCommission struct {
		ID       int
		MemberID int
		Amount   float64
	}

	var commissionsToApprove []pendingCommission
	var totalAmount float64
	for rows.Next() {
		var commission pendingCommission
		err := rows.Scan(&commission.ID, &commission.MemberID, &commission.Amount)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan pending commission")
			continue
		}
		commissionsToApprove = append(commissionsToApprove, commission)
		totalAmount += commission.Amount
	}
	rows.Close()

	if len(commissionsToApprove) == 0 {
		return 0, 0, []int{}, nil
	}

	// Calculate total commission for this member
	var totalCommission float64
	for _, commission := range commissionsToApprove {
		totalCommission += commission.Amount
	}

	// Get current balance for the member
	var currentBalance float64
	balanceQuery := `SELECT COALESCE(commission_balance, 0) FROM members WHERE id = $1`
	err = dbutil.TxQueryRowWithSchema(ctx, tx, balanceQuery, memberID).Scan(&currentBalance)
	if err != nil {
		r.logger.WithError(err).WithField("member_id", memberID).Error("failed to get member balance")
		return 0, 0, nil, errors.NewDatabaseError("failed to get member balance")
	}

	// Calculate new balance
	newBalance := currentBalance + totalCommission

	// Update referral transactions with balance_before and balance_after for this member
	updateTransactionQuery := `
		UPDATE referral_transactions 
		SET status = 'success', 
			approved_datetime = NOW(), 
			balance_before = $1,
			balance_after = $2,
			updated_at = NOW()
		WHERE member_id = $3 AND status = 'pending' AND type = 'commission'
	`

	_, err = dbutil.TxExecWithSchema(ctx, tx, updateTransactionQuery, currentBalance, newBalance, memberID)
	if err != nil {
		r.logger.WithError(err).Error("failed to approve commission transactions")
		return 0, 0, nil, errors.NewDatabaseError("failed to approve commission transactions")
	}

	// Update member commission balance
	updateBalanceQuery := `
		UPDATE members 
		SET commission_balance = $1, updated_at = NOW()
		WHERE id = $2
	`

	_, err = dbutil.TxExecWithSchema(ctx, tx, updateBalanceQuery, newBalance, memberID)
	if err != nil {
		r.logger.WithError(err).WithField("member_id", memberID).Error("failed to update member commission balance")
		return 0, 0, nil, errors.NewDatabaseError("failed to update member commission balance")
	}

	// Commit transaction
	err = tx.Commit(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to commit approve commission transaction")
		return 0, 0, nil, errors.NewDatabaseError("failed to commit approve commission transaction")
	}

	r.logger.WithFields(map[string]interface{}{
		"approved_count":     len(commissionsToApprove),
		"total_amount":       totalCommission,
		"member_id":          memberID,
	}).Info("commission transactions approved successfully")

	return len(commissionsToApprove), totalCommission, []int{memberID}, nil
}

// normalizeGameCategory normalizes game category names to handle different case variations
func normalizeGameCategory(category string) string {
	if category == "" {
		return ""
	}
	
	// Convert to lowercase for comparison
	lower := strings.ToLower(category)
	
	switch lower {
	case "slot":
		return "Slot"
	case "live-casino", "live_casino", "livecasino", "casino":
		return "Live-Casino"  
	case "sport", "sports":
		return "Sport"
	default:
		// Return original if no match found
		return category
	}
}
