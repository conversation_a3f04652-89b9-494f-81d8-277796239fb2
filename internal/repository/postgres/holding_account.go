package postgres

import (
	"blacking-api/internal/domain/holding_account"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"fmt"
	"log"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type HoldingAccount struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewHoldingAccountRepository(pool *pgxpool.Pool, logger logger.Logger) *HoldingAccount {
	return &HoldingAccount{
		pool:   pool,
		logger: logger,
	}
}

func (r *HoldingAccount) Create(ctx context.Context, req *holding_account.HoldingAccountRequest) error {
	query := `INSERT INTO holding_account (account_name, account_name_display, account_number, banking_id, phone_number)
		VALUES ($1, $2, $3, $4, $5)
	`
	_, err := dbutil.ExecWithSchema(
		ctx, r.pool, query,
		req.AccountName,
		req.AccountNameDisplay,
		req.AccountNumber,
		req.BankingID,
		req.PhoneNumber,
	)
	if err != nil {
		r.logger.Error("Failed to create holding account", "error", err)
		return err
	}

	return nil
}

func (r *HoldingAccount) FindAll(ctx context.Context, limit int, offset int, whereClause string, args []interface{}) ([]*holding_account.HoldingAccount, int64, error) {
	var holdingAccounts []*holding_account.HoldingAccount
	var total int64
	var err error

	argLast := len(args)
	cteQuery := fmt.Sprintf(`
		WITH counted_data AS (
			SELECT 
				ha.id, 
				ha.banking_id, 
				ha.account_name, 
				ha.account_name_display, 
				ha.account_number, 
				ha.phone_number,
 				ha.active,
				ha.inactive,
				ha.created_at,
				ha.updated_at,
				b.name as banking_name,
				b.image_url as banking_image_url,
				COUNT(*) OVER() as total_count
			FROM holding_account ha
			LEFT JOIN banking b ON ha.banking_id = b.id
			%s
			WHERE inactive = false
			LIMIT $%v OFFSET $%v
		)
		SELECT 
			id, 
			banking_id, 
			account_name, 
			account_name_display, 
			account_number, 
			phone_number,
			active,
			inactive,
			created_at,
			updated_at,
			banking_name,
			banking_image_url,
			total_count
		FROM counted_data
	`, whereClause, argLast+1, argLast+2)
	rows, err := dbutil.QueryWithSchema(ctx, r.pool, cteQuery, append(args, limit, offset)...)
	if err != nil {
		r.logger.WithError(err).Error("failed to find all withdraw accounts")
		return nil, 0, err
	}
	defer rows.Close()

	for rows.Next() {
		var withdrawAccount holding_account.HoldingAccount
		if err := rows.Scan(
			&withdrawAccount.ID,
			&withdrawAccount.BankingID,
			&withdrawAccount.AccountName,
			&withdrawAccount.AccountNameDisplay,
			&withdrawAccount.AccountNumber,
			&withdrawAccount.PhoneNumber,
			&withdrawAccount.Active,
			&withdrawAccount.Inactive,
			&withdrawAccount.CreatedAt,
			&withdrawAccount.UpdatedAt,
			&withdrawAccount.BankingName,
			&withdrawAccount.BankingImageUrl,
			&total,
		); err != nil {
			r.logger.WithError(err).Error("failed to scan holding account")
			return nil, 0, err
		}
		holdingAccounts = append(holdingAccounts, &withdrawAccount)
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating holding account rows")
		return nil, 0, err
	}

	return holdingAccounts, total, nil
}

func (r *HoldingAccount) FindByID(ctx context.Context, id int64) (*holding_account.HoldingAccount, error) {
	holdingAccount := holding_account.HoldingAccount{}
	query := `
		SELECT 
			ha.id, 
			ha.banking_id, 
			ha.account_name, 
			ha.account_name_display, 
			ha.account_number, 
			ha.phone_number,
			ha.active,
			ha.inactive,
			ha.created_at,
			ha.updated_at
		FROM holding_account ha
		WHERE ha.id = $1 AND ha.inactive = false
	`
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)
	if err := row.Scan(
		&holdingAccount.ID,
		&holdingAccount.BankingID,
		&holdingAccount.AccountName,
		&holdingAccount.AccountNameDisplay,
		&holdingAccount.AccountNumber,
		&holdingAccount.PhoneNumber,
		&holdingAccount.Active,
		&holdingAccount.Inactive,
		&holdingAccount.CreatedAt,
		&holdingAccount.UpdatedAt,
	); err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("holding account not found")
		}
		log.Printf("error iterating holding account rows: %v", err)
		r.logger.WithError(err).Error("failed to find holding account by ID")
		return nil, errors.NewDatabaseError("failed to find holding account by ID")
	}

	return &holdingAccount, nil
}

func (r *HoldingAccount) FindByAccountNumberDuplicate(ctx context.Context, accountNumber string) (bool, error) {
	query := `SELECT COUNT(*) FROM holding_account WHERE account_number = $1 AND inactive = false`
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, accountNumber)

	var count int
	if err := row.Scan(&count); err != nil {
		r.logger.WithError(err).Error("failed to check duplicate account number")
		return false, err
	}

	return count > 0, nil
}

func (r *HoldingAccount) FindByAccountNumberDuplicateAndIdNot(ctx context.Context, accountNumber string, id int64) (bool, error) {
	query := `SELECT COUNT(*) FROM holding_account WHERE account_number = $1 AND id != $2 AND inactive = false`
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, accountNumber, id)

	var count int
	if err := row.Scan(&count); err != nil {
		r.logger.WithError(err).Error("failed to check duplicate account number excluding specific ID")
		return false, err
	}

	return count > 0, nil
}

func (r *HoldingAccount) Update(ctx context.Context, id int64, req *holding_account.HoldingAccountRequest) error {
	query := `
		UPDATE holding_account
		SET banking_id = $1, account_name = $2, account_name_display = $3, account_number = $4, phone_number = $5
		WHERE id = $6 AND inactive = false
	`
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		req.BankingID,
		req.AccountName,
		req.AccountNameDisplay,
		req.AccountNumber,
		req.PhoneNumber,
		id,
	)
	if err != nil {
		r.logger.WithError(err).Error("failed to update holding account")
		return errors.NewDatabaseError("failed to update holding account")
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when updating holding account")
		return errors.NewNotFoundError("holding account not found")
	}

	return nil
}

func (r *HoldingAccount) Active(ctx context.Context, id int64, status bool) error {
	query := `
		UPDATE holding_account
		SET active = $1, inactive = false
		WHERE id = $2 AND inactive = false
	`
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, status, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to update holding account active status")
		return errors.NewDatabaseError("failed to update holding account active status")
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when updating holding account active status")
		return errors.NewNotFoundError("holding account not found")
	}

	return nil
}

func (r *HoldingAccount) Delete(ctx context.Context, id int64) error {
	query := `
		UPDATE holding_account
		SET inactive = true
		WHERE id = $1 AND inactive = false
	`
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete holding account")
		return errors.NewDatabaseError("failed to delete holding account")
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when deleting holding account")
		return errors.NewNotFoundError("holding account not found")
	}

	return nil
}
