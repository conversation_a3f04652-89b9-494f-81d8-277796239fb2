package interfaces

import (
	"blacking-api/internal/domain/member"
	"context"
)

type MemberRepository interface {
	Create(ctx context.Context, member *member.Member, clientIP string) error
	GetByID(ctx context.Context, id string) (*member.Member, error)
	GetByUsername(ctx context.Context, username string) (*member.Member, error)
	GetByPhone(ctx context.Context, phone string) (*member.Member, error)
	GetByGameUsername(ctx context.Context, gameUsername string) (*member.Member, error)
	GetByReferCode(ctx context.Context, referCode string) (*member.Member, error)
	Update(ctx context.Context, member *member.Member) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, limit, offset int, search string) ([]*member.Member, error)
	ListWithFilter(ctx context.Context, limit, offset int, filter *member.MemberFilter) ([]*member.Member, error)

	Count(ctx context.Context, search string) (int64, error)
	CountWithFilter(ctx context.Context, filter *member.MemberFilter) (int64, error)

	// Login tracking methods
	UpdateLoginInfo(ctx context.Context, memberID int, clientIP, userAgent, device string) error

	// Status count methods
	GetStatusCounts(ctx context.Context) ([]member.MemberStatusCount, error)

	// Partner-specific methods
	// ListPartners retrieves partners (members with show_partner_info = true) with pagination and search by first_name
	ListPartners(ctx context.Context, limit, offset int, search string) ([]*member.Member, error)

	// CountPartners returns total count of partners with search filter
	CountPartners(ctx context.Context, search string) (int64, error)
}
