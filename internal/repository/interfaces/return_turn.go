package interfaces

import (
	"blacking-api/internal/domain/return_turn"
	"context"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type ReturnTurnRepository interface {
	// Settings
	GetReturnSetting(ctx context.Context) (*return_turn.ReturnTurnSettingResponse, error)
	CreateReturnSetting(ctx context.Context, body return_turn.ReturnTurnSetting) (*int64, error)
	UpdateReturnSetting(ctx context.Context, id int64, body return_turn.ReturnTurnSettingUpdateRequest) error

	// Calculate Types
	GetCalculatePlayTypes(ctx context.Context) ([]return_turn.CalculatePlayType, error)
	GetPromotionCalculateTypes(ctx context.Context, promotionId int64) ([]int64, error)
	UpdatePromotionCalculateTypes(ctx context.Context, promotionId int64, typeIds []int64) error

	// Reference Data
	GetReturnTurnCutTypes(ctx context.Context) ([]return_turn.ReturnTurnCutType, error)
	GetReturnTurnLoserTypes(ctx context.Context) ([]return_turn.ReturnTurnLoserType, error)

	// Transactions
	GetCurrentReturnTransactionList(ctx context.Context, userId int64) ([]return_turn.ReturnTurnLoserResponse, error)
	GetCurrentReturnTransactionListByCode(ctx context.Context, memberCode string) ([]return_turn.ReturnTurnLoserResponse, error)
	GetReturnTransactionList(ctx context.Context, req return_turn.ReturnTurnTransactionListRequest) ([]return_turn.ReturnTurnLoserResponse, int64, error)
	GetReturnTransactionListByCode(ctx context.Context, memberCode string, req return_turn.ReturnTurnTransactionListRequest) ([]return_turn.ReturnTurnLoserResponse, int64, error)
	GetReturnTransactionByDailyKey(ctx context.Context, dailyKey string) (*return_turn.ReturnTurnLoser, error)
	CreateReturnTransaction(ctx context.Context, body return_turn.ReturnTurnLoserCreateBody) (*int64, error)
	UpdateCalcReturnTransaction(ctx context.Context, id int64, body return_turn.ReturnTurnLoserCalcBody) error
	UpdateTakeReturnTransaction(ctx context.Context, id int64, body return_turn.ReturnTurnLoserUpdateBody) error
	UpdateExpiredReturnTransaction(ctx context.Context, id int64, body return_turn.ReturnTurnLoserUpdateBody) error

	// User Credit Operations
	IncreaseUserCredit(ctx context.Context, tx pgx.Tx, body return_turn.UserTransactionCreateRequest) (*return_turn.UserTransactionCreateResponse, error)
	GetUserBalance(ctx context.Context, userID int64) (float64, error)

	// Play Log
	GetDailyTotalUserPlaylogList(ctx context.Context, statementDate string) ([]return_turn.PlaylogTotalAmount, error)
	GetWeeklyTotalUserPlaylogList(ctx context.Context, statementDate string) ([]return_turn.PlaylogTotalAmount, error)
	CheckDailyLoser(ctx context.Context, statementDate string) (*return_turn.CronPlayLogCheckResponse, error)

	// History Reports
	GetReturnHistoryMemberList(ctx context.Context, req return_turn.ReturnTurnHistoryUserListRequest) ([]return_turn.ReturnTurnHistoryUserListResponse, int64, error)
	GetReturnHistoryMemberSummary(ctx context.Context, req return_turn.ReturnTurnHistoryUserListRequest) (*return_turn.ReturnTurnHistoryUserSummaryResponse, error)
	GetReturnHistoryLogList(ctx context.Context, req return_turn.ReturnTurnHistoryListRequest) ([]return_turn.ReturnTurnLoserResponse, int64, error)

	// Customer Promotion Management
	GetCustomerPromotionList(ctx context.Context, req return_turn.CustomerPromotionListRequest) ([]return_turn.CustomerPromotionListResponse, int64, error)
	CancelCustomerPromotion(ctx context.Context, statementId int64, adminId int64) error

	// Expired Transactions
	GetExpiredReturnTransactions(ctx context.Context, expireDays int) ([]return_turn.ReturnTurnLoser, error)

	// Database access
	GetDB() *pgxpool.Pool
	BeginTx(ctx context.Context) (pgx.Tx, error)
}
