package interfaces

import (
	"context"

	"blacking-api/internal/domain/customer_call_log"
)

// CustomerCallLogRepository defines the interface for customer call log repository
type CustomerCallLogRepository interface {
	// Create creates a new customer call log
	Create(ctx context.Context, callLog *customer_call_log.CustomerCallLog) error

	// GetByID retrieves a customer call log by ID
	GetByID(ctx context.Context, id int) (*customer_call_log.CustomerCallLog, error)

	// Update updates an existing customer call log
	Update(ctx context.Context, callLog *customer_call_log.CustomerCallLog) error

	// Delete soft deletes a customer call log
	Delete(ctx context.Context, id int) error

	// List retrieves customer call logs with pagination
	List(ctx context.Context, limit, offset int) ([]*customer_call_log.CustomerCallLog, error)

	// ListWithFilter retrieves customer call logs with filter and pagination
	ListWithFilter(ctx context.Context, limit, offset int, filter *customer_call_log.CustomerCallLogFilter) ([]*customer_call_log.CustomerCallLog, error)

	// Count returns total count of customer call logs
	Count(ctx context.Context) (int64, error)

	// CountWithFilter returns count of customer call logs with filter
	CountWithFilter(ctx context.Context, filter *customer_call_log.CustomerCallLogFilter) (int64, error)

	// CountByMemberID returns count of call logs for a specific member
	CountByMemberID(ctx context.Context, memberID int) (int64, error)

	// GetLatestByMemberID retrieves the latest call log for a specific member
	GetLatestByMemberID(ctx context.Context, memberID int) (*customer_call_log.CustomerCallLog, error)
}
