package interfaces

import (
	"blacking-api/internal/domain/referral"
	"github.com/google/uuid"
)

type ReferralRepository interface {
	GetOverview(memberID int) (*referral.OverviewResponse, error)
	GetDownlines(memberID int) ([]referral.ReferralMember, error) // Get downlines list - legacy without pagination
	GetDownlinesPaginated(memberID int, page, limit int) ([]referral.ReferralMember, int, error) // Get downlines with pagination
	GetIncomeData(memberID int, months int) (*referral.IncomeResponse, error) // Legacy without pagination
	GetIncomeDataPaginated(memberID int, req referral.IncomeRequest) (*referral.IncomeResponse, error) // With pagination
	GetByMemberID(memberID int) ([]referral.Register, error)
	Create(register *referral.Register) error
	GetRegisterByMemberID(memberID uuid.UUID) (*referral.Register, error)
	IncrementReferralView(memberID int) error
	IncrementReferralViewByCode(referralCode string) error
	IncrementDownlineCount(memberID int) error
	GetCommissionBalance(memberID int) (float64, error)
	CreateWithdrawTransaction(memberID int, transaction *referral.ReferralTransaction) (int, error)
	GetYesterdayCommission(memberID int) (float64, error) // Get yesterday's commission from referral_transactions
	GetPendingCommissions(memberID, page, limit int) ([]referral.PendingCommissionTransaction, int, float64, map[string]float64, map[string]float64, error) // Get pending commissions for specific member with commission rates, total amount, and totals by category
	ApproveCommissions(memberID int) (int, float64, []int, error) // Approve pending commissions for specific member and return approved count, total amount, and updated member IDs
}

type ReferralFAQRepository interface {
	GetActiveFAQsByCategory(category string) ([]referral.FAQ, error)
}
