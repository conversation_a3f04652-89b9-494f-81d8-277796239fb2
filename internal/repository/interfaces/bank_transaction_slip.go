package interfaces

import (
	"blacking-api/internal/domain/bank_transaction_slip"
	"context"
)

type BankTransactionSlipRepository interface {
	// Create operations
	Create(ctx context.Context, slip *bank_transaction_slip.BankTransactionSlip) (*bank_transaction_slip.BankTransactionSlip, error)

	// Read operations
	GetByID(ctx context.Context, id int64) (*bank_transaction_slip.BankTransactionSlip, error)
	GetByTransactionID(ctx context.Context, transactionID int64) (*bank_transaction_slip.BankTransactionSlip, error)
	GetList(ctx context.Context, filter *bank_transaction_slip.FilterRequest) ([]*bank_transaction_slip.BankTransactionSlip, int64, error)
	GetByMemberID(ctx context.Context, memberID int64, filter *bank_transaction_slip.FilterRequest) ([]*bank_transaction_slip.BankTransactionSlip, int64, error)

	// Update operations
	Update(ctx context.Context, id int64, slip *bank_transaction_slip.UpdateRequest) error
	UpdateStatus(ctx context.Context, id int64, status int, remark string) error

	// Delete operations
	Delete(ctx context.Context, id int64) error

	// Utility operations
	CountByStatus(ctx context.Context, status int) (int64, error)
	CountByMemberID(ctx context.Context, memberID int64) (int64, error)
}
