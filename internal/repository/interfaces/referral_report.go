package interfaces

import (
	"context"

	"blacking-api/internal/domain/report"
)

type ReferralReportRepository interface {
	// GetReferralReportSummary returns referral report summary grouped by member_id
	GetReferralReportSummary(ctx context.Context, filter report.ReferralReportFilter) ([]report.ReferralReportSummary, int, error)
	
	// GetReferralReportDetail returns detailed referral transactions for a specific member
	GetReferralReportDetail(ctx context.Context, memberID int, filter report.ReferralReportFilter) ([]report.ReferralReportDetail, int, error)
	
	// GetReferralReportDetailGrouped returns grouped detail by downline_member_id for a specific member
	GetReferralReportDetailGrouped(ctx context.Context, memberID int, filter report.ReferralReportFilter) ([]report.ReferralReportDetailGrouped, int, error)
	
	// GetReferralReportSummaryForExport returns all summary records without pagination for CSV export
	GetReferralReportSummaryForExport(ctx context.Context, filter report.ReferralReportFilter) ([]report.ReferralReportSummary, error)
	
	// GetReferralReportDetailForExport returns all detail records without pagination for CSV export
	GetReferralReportDetailForExport(ctx context.Context, memberID int, filter report.ReferralReportFilter) ([]report.ReferralReportDetail, error)
	
	// GetReferralReportDetailGroupedForExport returns all grouped detail records without pagination for CSV export
	GetReferralReportDetailGroupedForExport(ctx context.Context, memberID int, filter report.ReferralReportFilter) ([]report.ReferralReportDetailGrouped, error)
	
	// GetReportSummaryStatistics returns overall statistics for the report
	GetReportSummaryStatistics(ctx context.Context, filter report.ReferralReportFilter) (report.ReportSummary, error)
	
	// GetDetailReportSummaryStatistics returns statistics for detail report
	GetDetailReportSummaryStatistics(ctx context.Context, memberID int, filter report.ReferralReportFilter) (report.DetailReportSummary, error)
	
	// GetCommissionByGameCategory returns commission transactions filtered by game category
	GetCommissionByGameCategory(ctx context.Context, filter report.CommissionByGameCategoryFilter) ([]report.ReferralReportDetail, int, report.CategoryCommissionSummary, error)
}