package interfaces

import (
	"blacking-api/internal/domain/statement_status"
	"blacking-api/internal/helper"
	"context"
	"github.com/jackc/pgx/v5"
)

type StatementStatusRepository interface {
	Create(ctx context.Context, req *statement_status.CreateStatementStatusRequest) (*statement_status.StatementStatus, error)
	CreateWithTx(ctx context.Context, tx pgx.Tx, req *statement_status.CreateStatementStatusRequest) (*statement_status.StatementStatus, error)
	Update(ctx context.Context, id int64, req *statement_status.UpdateStatementStatusRequest) (*statement_status.StatementStatus, error)
	UpdateWithTx(ctx context.Context, tx pgx.Tx, id int64, req *statement_status.UpdateStatementStatusRequest) (*statement_status.StatementStatus, error)
	Delete(ctx context.Context, id int64) error
	DeleteWithTx(ctx context.Context, tx pgx.Tx, id int64) error
	SoftDelete(ctx context.Context, id int64) error
	SoftDeleteWithTx(ctx context.Context, tx pgx.Tx, id int64) error
	Restore(ctx context.Context, id int64) error
	RestoreWithTx(ctx context.Context, tx pgx.Tx, id int64) error
	GetByID(ctx context.Context, id int64) (*statement_status.StatementStatus, error)
	GetByIDWithTx(ctx context.Context, tx pgx.Tx, id int64) (*statement_status.StatementStatus, error)
	GetByName(ctx context.Context, name string) (*statement_status.StatementStatus, error)
	GetByNameWithTx(ctx context.Context, tx pgx.Tx, name string) (*statement_status.StatementStatus, error)
	GetAll(ctx context.Context, filter *statement_status.StatementStatusFilter, pagination *helper.Pagination) ([]statement_status.StatementStatus, int64, error)
	GetAllWithTx(ctx context.Context, tx pgx.Tx, filter *statement_status.StatementStatusFilter, pagination *helper.Pagination) ([]statement_status.StatementStatus, int64, error)
}
