package interfaces

import (
	"blacking-api/internal/domain/system_setting"
	"context"
)

// SystemSettingRepository defines the interface for system setting repository operations
type SystemSettingRepository interface {
	// GetBy<PERSON>ey retrieves a system setting by key
	Get<PERSON>y<PERSON>ey(ctx context.Context, key string) (*system_setting.SystemSetting, error)

	// Update updates a system setting
	Update(ctx context.Context, setting *system_setting.SystemSetting) error

	// Create creates a new system setting
	Create(ctx context.Context, setting *system_setting.SystemSetting) error

	// Upsert creates or updates a system setting
	Upsert(ctx context.Context, setting *system_setting.SystemSetting) error

	// List retrieves all system settings
	List(ctx context.Context) ([]*system_setting.SystemSetting, error)

	// Delete deletes a system setting by key
	Delete(ctx context.Context, key string) error
}
