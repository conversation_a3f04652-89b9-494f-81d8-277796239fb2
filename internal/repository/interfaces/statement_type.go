package interfaces

import (
	"blacking-api/internal/domain/statement_type"
	"blacking-api/internal/helper"
	"context"
	"github.com/jackc/pgx/v5"
)

type StatementTypeRepository interface {
	Create(ctx context.Context, req *statement_type.CreateStatementTypeRequest) (*statement_type.StatementType, error)
	CreateWithTx(ctx context.Context, tx pgx.Tx, req *statement_type.CreateStatementTypeRequest) (*statement_type.StatementType, error)
	Update(ctx context.Context, id int64, req *statement_type.UpdateStatementTypeRequest) (*statement_type.StatementType, error)
	UpdateWithTx(ctx context.Context, tx pgx.Tx, id int64, req *statement_type.UpdateStatementTypeRequest) (*statement_type.StatementType, error)
	Delete(ctx context.Context, id int64) error
	DeleteWithTx(ctx context.Context, tx pgx.Tx, id int64) error
	SoftDelete(ctx context.Context, id int64) error
	SoftDeleteWithTx(ctx context.Context, tx pgx.Tx, id int64) error
	Restore(ctx context.Context, id int64) error
	RestoreWithTx(ctx context.Context, tx pgx.Tx, id int64) error
	GetByID(ctx context.Context, id int64) (*statement_type.StatementType, error)
	GetByIDWithTx(ctx context.Context, tx pgx.Tx, id int64) (*statement_type.StatementType, error)
	GetByName(ctx context.Context, name string) (*statement_type.StatementType, error)
	GetByNameWithTx(ctx context.Context, tx pgx.Tx, name string) (*statement_type.StatementType, error)
	GetAll(ctx context.Context, filter *statement_type.StatementTypeFilter, pagination *helper.Pagination) ([]statement_type.StatementType, int64, error)
	GetAllWithTx(ctx context.Context, tx pgx.Tx, filter *statement_type.StatementTypeFilter, pagination *helper.Pagination) ([]statement_type.StatementType, int64, error)
}
