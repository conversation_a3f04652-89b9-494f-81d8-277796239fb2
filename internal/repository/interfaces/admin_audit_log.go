package interfaces

import (
	"blacking-api/internal/domain/admin_audit_log"
	"context"
)

// AdminAuditLogRepository defines the interface for admin audit log repository operations
type AdminAuditLogRepository interface {
	// Create creates a new admin audit log entry
	Create(ctx context.Context, auditLog *admin_audit_log.AdminAuditLog) error

	// GetByID retrieves an admin audit log by ID
	GetByID(ctx context.Context, id int) (*admin_audit_log.AdminAuditLog, error)

	// List retrieves admin audit logs with pagination and filters
	List(ctx context.Context, limit, offset int, filter *admin_audit_log.AdminAuditLogFilter) ([]*admin_audit_log.AdminAuditLog, error)

	// Count returns the total count of admin audit logs with filters
	Count(ctx context.Context, filter *admin_audit_log.AdminAuditLogFilter) (int64, error)

	// DeleteOldEntries deletes audit log entries older than specified days
	DeleteOldEntries(ctx context.Context, daysToKeep int) (int64, error)
}