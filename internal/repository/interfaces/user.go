package interfaces

import (
	"blacking-api/internal/domain/user"
	"context"
)

type UserRepository interface {
	Create(ctx context.Context, user *user.User) error
	GetByID(ctx context.Context, id string) (*user.User, error)
	GetByUsername(ctx context.Context, username string) (*user.User, error)
	Update(ctx context.Context, user *user.User) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, limit, offset int, search string, userRoleID *int) ([]*user.User, error)
	Count(ctx context.Context, search string, userRoleID *int) (int64, error)

	// Dropdown methods
	GetForDropdown(ctx context.Context) ([]user.UserDropdownItem, error)
}
