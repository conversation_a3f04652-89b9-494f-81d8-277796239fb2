package interfaces

import (
	"context"

	"blacking-api/internal/domain/customer_sms_log"
)

// CustomerSMSLogRepository defines the interface for customer SMS log repository
type CustomerSMSLogRepository interface {
	// Create creates a new customer SMS log
	Create(ctx context.Context, smsLog *customer_sms_log.CustomerSMSLog) error

	// GetByID retrieves a customer SMS log by ID
	GetByID(ctx context.Context, id int) (*customer_sms_log.CustomerSMSLog, error)

	// Update updates an existing customer SMS log
	Update(ctx context.Context, smsLog *customer_sms_log.CustomerSMSLog) error

	// Delete soft deletes a customer SMS log
	Delete(ctx context.Context, id int) error

	// List retrieves customer SMS logs with pagination
	List(ctx context.Context, limit, offset int) ([]*customer_sms_log.CustomerSMSLog, error)

	// ListWithFilter retrieves customer SMS logs with filter and pagination
	ListWithFilter(ctx context.Context, limit, offset int, filter *customer_sms_log.CustomerSMSLogFilter) ([]*customer_sms_log.CustomerSMSLog, error)

	// Count returns total count of customer SMS logs
	Count(ctx context.Context) (int64, error)

	// CountWithFilter returns count of customer SMS logs with filter
	CountWithFilter(ctx context.Context, filter *customer_sms_log.CustomerSMSLogFilter) (int64, error)

	// CountByMemberID returns count of SMS logs for a specific member
	CountByMemberID(ctx context.Context, memberID int) (int64, error)

	// GetLatestByMemberID retrieves the latest SMS log for a specific member
	GetLatestByMemberID(ctx context.Context, memberID int) (*customer_sms_log.CustomerSMSLog, error)
}
