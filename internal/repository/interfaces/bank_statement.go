package interfaces

import (
	"blacking-api/internal/domain/bank_statement"
	"blacking-api/internal/helper"
	"context"
	"github.com/jackc/pgx/v5"
)

type BankStatementRepository interface {
	Create(ctx context.Context, req *bank_statement.CreateBankStatementRequest) (*bank_statement.BankStatement, error)
	CreateWithTx(ctx context.Context, tx pgx.Tx, req *bank_statement.CreateBankStatementRequest) (*bank_statement.BankStatement, error)
	Update(ctx context.Context, id int64, req *bank_statement.UpdateBankStatementRequest) (*bank_statement.BankStatement, error)
	UpdateWithTx(ctx context.Context, tx pgx.Tx, id int64, req *bank_statement.UpdateBankStatementRequest) (*bank_statement.BankStatement, error)
	Delete(ctx context.Context, id int64) error
	DeleteWithTx(ctx context.Context, tx pgx.Tx, id int64) error
	GetByID(ctx context.Context, id int64) (*bank_statement.BankStatement, error)
	GetByIDWithTx(ctx context.Context, tx pgx.Tx, id int64) (*bank_statement.BankStatement, error)
	GetByExternalID(ctx context.Context, externalID int64) (*bank_statement.BankStatement, error)
	GetByExternalIDWithTx(ctx context.Context, tx pgx.Tx, externalID int64) (*bank_statement.BankStatement, error)
	GetAll(ctx context.Context, filter *bank_statement.BankStatementFilter, pagination *helper.Pagination) ([]bank_statement.BankStatement, int64, error)
	GetAllWithTx(ctx context.Context, tx pgx.Tx, filter *bank_statement.BankStatementFilter, pagination *helper.Pagination) ([]bank_statement.BankStatement, int64, error)
	GetByAccountID(ctx context.Context, accountID int64, pagination *helper.Pagination) ([]bank_statement.BankStatement, int64, error)
	GetByAccountIDWithTx(ctx context.Context, tx pgx.Tx, accountID int64, pagination *helper.Pagination) ([]bank_statement.BankStatement, int64, error)
}
