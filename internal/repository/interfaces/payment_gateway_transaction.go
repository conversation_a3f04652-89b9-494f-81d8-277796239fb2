package interfaces

import (
	"blacking-api/internal/domain/payment_gateway_transaction"
	"context"
)

type PaymentGatewayTransactionRepository interface {
	// Transaction methods
	Create(ctx context.Context, transaction *payment_gateway_transaction.PaymentGatewayTransaction) error
	GetByTransactionID(ctx context.Context, transactionID string) (*payment_gateway_transaction.PaymentGatewayTransaction, error)
	GetTransactions(ctx context.Context, filters *payment_gateway_transaction.TransactionFilters) (*payment_gateway_transaction.TransactionListResponse, error)
	UpdateStatus(ctx context.Context, transactionID string, status string, metadata map[string]interface{}) error
	ExistsByTransactionID(ctx context.Context, transactionID string) (bool, error)

	// Webhook methods
	CreateWebhookLog(ctx context.Context, webhook *payment_gateway_transaction.PaymentGatewayWebhook) error
	GetWebhooksByTransactionID(ctx context.Context, transactionID string) ([]*payment_gateway_transaction.PaymentGatewayWebhook, error)
	UpdateWebhookProcessingStatus(ctx context.Context, webhookID int64, status string, error string) error
}
