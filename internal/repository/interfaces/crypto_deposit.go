package interfaces

import (
	"blacking-api/internal/domain/crypto_deposit"
	"context"
)

type CryptoDepositRepository interface {
	// Blockchain Networks
	GetSupportedNetworks(ctx context.Context) ([]*crypto_deposit.BlockchainNetwork, error)
	GetNetworkByChainID(ctx context.Context, chainID int) (*crypto_deposit.BlockchainNetwork, error)

	// Backend Wallets
	GetBackendWallets(ctx context.Context) ([]*crypto_deposit.BackendWallet, error)
	GetBackendWalletByChainID(ctx context.Context, chainID int) (*crypto_deposit.BackendWallet, error)
	UpdateBackendWalletBalance(ctx context.Context, walletAddress string, ethBalance, tokenBalance float64) error

	// Chain Tokens for rate conversion
	GetChainTokenByNetworkAndContract(ctx context.Context, networkID int64, contract string) (*crypto_deposit.ChainToken, error)
	GetChainTokenByChainIDAndContract(ctx context.Context, chainID int, contract string) (*crypto_deposit.ChainToken, error)
	ListChainTokens(ctx context.Context) ([]*crypto_deposit.ChainToken, error)
	UpdateChainTokenRate(ctx context.Context, id int64, rate float64) error

	// Crypto Deposit Logs
	CreateDepositLog(ctx context.Context, log *crypto_deposit.CryptoDepositLog) error
	GetDepositLogs(ctx context.Context, transactionID string) ([]*crypto_deposit.CryptoDepositLog, error)

	// Payment Gateway Transaction - Crypto specific methods
	GetCryptoDepositByTransactionID(ctx context.Context, transactionID string) (*crypto_deposit.CryptoDepositResponse, error)
	ListCryptoDeposits(ctx context.Context, filters *crypto_deposit.CryptoDepositFilters) ([]*crypto_deposit.CryptoDepositSummary, int64, error)
	UpdateCryptoDepositStep1(ctx context.Context, transactionID string, req *crypto_deposit.UpdateStep1Request) error
	UpdateCryptoDepositStep2(ctx context.Context, transactionID string, req *crypto_deposit.UpdateStep2Request) error
}
