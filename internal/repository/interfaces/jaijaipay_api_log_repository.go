package interfaces

import (
	"context"

	"blacking-api/internal/domain"
)

// JaiJaiPayAPILogRepository defines the interface for JaiJaiPay API log operations
type JaiJaiPayAPILogRepository interface {
	// Create creates a new API log entry
	Create(ctx context.Context, apiLog *domain.JaiJaiPayAPILog) error

	// Update updates an existing API log entry (typically after receiving response)
	Update(ctx context.Context, apiLog *domain.JaiJaiPayAPILog) error

	// GetByID retrieves an API log by its ID
	GetByID(ctx context.Context, id int64) (*domain.JaiJaiPayAPILog, error)

	// GetByRequestID retrieves an API log by request ID
	GetByRequestID(ctx context.Context, requestID string) (*domain.JaiJaiPayAPILog, error)

	// GetByAPICategory retrieves API logs by category with pagination
	GetByAPICategory(ctx context.Context, category string, limit, offset int) ([]*domain.JaiJaiPayAPILog, error)

	// GetByOrderID retrieves API logs related to a specific order
	GetByOrderID(ctx context.Context, orderID string) ([]*domain.JaiJaiPayAPILog, error)

	// GetByTransactionID retrieves API logs related to a specific transaction
	GetByTransactionID(ctx context.Context, transactionID string) ([]*domain.JaiJaiPayAPILog, error)

	// GetByDateRange retrieves API logs within a date range
	GetByDateRange(ctx context.Context, startDate, endDate string, limit, offset int) ([]*domain.JaiJaiPayAPILog, error)

	// GetFailedCalls retrieves failed API calls for retry purposes
	GetFailedCalls(ctx context.Context, category string, limit, offset int) ([]*domain.JaiJaiPayAPILog, error)

	// GetStatistics retrieves API call statistics
	GetStatistics(ctx context.Context, category string, startDate, endDate string) (map[string]interface{}, error)

	// Delete removes an API log (for cleanup purposes)
	Delete(ctx context.Context, id int64) error

	// DeleteOldLogs removes old API logs (for maintenance)
	DeleteOldLogs(ctx context.Context, olderThanDays int) (int64, error)
}
