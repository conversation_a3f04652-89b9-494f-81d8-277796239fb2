package interfaces

import (
	"blacking-api/internal/domain/promotion_web"
	"context"
	"io"
)

type PromotionWebRepository interface {
	// Basic CRUD operations
	CreatePromotionWeb(ctx context.Context, req promotion_web.PromotionWebCreateRequest) (int64, error)
	GetPromotionWebList(ctx context.Context, req promotion_web.PromotionWebGetListRequest) ([]promotion_web.PromotionWebGetListResponse, int64, error)
	GetPromotionWebById(ctx context.Context, id int64) (*promotion_web.PromotionWebGetByIdResponse, error)
	UpdatePromotionWeb(ctx context.Context, req promotion_web.PromotionWebUpdateRequest) error
	DeletePromotionWeb(ctx context.Context, req promotion_web.DeletePromotionWebRequest) error
	CancelPromotionWeb(ctx context.Context, req promotion_web.CancelPromotionWebRequest) error

	// User promotion operations
	GetUserPromotionWebList(ctx context.Context, req promotion_web.PromotionWebUserGetListRequest) ([]promotion_web.PromotionWebUserGetListResponse, int64, error)
	CreateUserPromotion(ctx context.Context, req promotion_web.CollectPromotionRequest) (int64, error)
	CancelPromotionWebUserById(ctx context.Context, req promotion_web.CancelPromotionWebUserById) error
	GetUserPromotionWebByUserId(ctx context.Context, userId int64) (*promotion_web.PromotionWebUserByUserIdResponse, error)
	GetPromotionWebUserById(ctx context.Context, req promotion_web.GetPromotionWebUserById) (*promotion_web.GetPromotionWebUserByIdResponse, error)
	PromotionWebUserGetListByUserId(ctx context.Context, req promotion_web.PromotionWebUserGetListByUserIdRequest) ([]promotion_web.PromotionWebUserGetListByUserIdResponse, int64, error)
	GetUserPromotionsByUserID(ctx context.Context, userID int64) ([]promotion_web.ShowPromotionForUserResponse, error)

	// Option/lookup operations
	GetPromotionTypes(ctx context.Context) ([]promotion_web.PromotionWebTypeResponse, error)
	GetPromotionStatuses(ctx context.Context) ([]promotion_web.PromotionWebStatusResponse, error)
	GetPromotionBonusConditions(ctx context.Context) ([]promotion_web.PromotionWebBonusConditionResponse, error)
	GetPromotionBonusTypes(ctx context.Context) ([]promotion_web.PromotionWebBonusTypeResponse, error)
	GetPromotionTurnoverTypes(ctx context.Context) ([]promotion_web.PromotionWebTurnoverTypeResponse, error)
	GetPromotionDateTypes(ctx context.Context) ([]promotion_web.PromotionWebDateTypeResponse, error)

	// Lock credit operations
	CreateLockCredit(ctx context.Context, req promotion_web.LockCreditPromotionCreateRequest) (int64, error)
	GetLockCreditWithdrawList(ctx context.Context, req promotion_web.GetLockCreditWithdrawListRequest) ([]promotion_web.GetLockCreditWithdrawListResponse, int64, error)
	UnlockCreditWithdraw(ctx context.Context, id int64, adminID int64) error
	CheckLockedCredit(ctx context.Context, userID int64) (bool, error)

	// Public operations
	GetPublicPromotions(ctx context.Context) ([]promotion_web.ShowPromotionForUserResponse, error)
	GetPublicPromotionByID(ctx context.Context, id int64) (*promotion_web.PromotionWebGetByIdResponse, error)

	// Business logic helpers
	ValidatePromotionEligibility(ctx context.Context, userID, promotionID int64) error
	UpdateExpiredPromotions(ctx context.Context) error
	GetPromotionWebUserToCancel(ctx context.Context, PromotionWebId int64) ([]promotion_web.GetPromotionWebIdToCancel, error)
	ExpiredPromotionWebUserByIds(ctx context.Context, req promotion_web.CancelPromotionWebUserByPromotionWebId) error
	PromotionConfirmUpdatePromotionWebUser(ctx context.Context, confirmId int64, promotionWebUserId int64) error

	// Utility operations
	GetExpiredPromotionWeb(ctx context.Context, today string) ([]promotion_web.PromotionWebExpired, error)
	PromotionWebGetSildeListOnlyActive(ctx context.Context) ([]promotion_web.PromotionWebGetSildeListOnlyActive, error)
	UpdatePromotionWebPriorityOrderCreate(ctx context.Context, id int64) error
	SortPromotionWebPriorityOrder(ctx context.Context, req promotion_web.DragSortRequest) error

	// File upload operations
	UploadImageToCloudflare(ctx context.Context, pathUpload string, filename string, fileReader io.Reader) (*promotion_web.CloudFlareUploadCreateBody, error)

	// Additional methods from migration document
	GetPromotionSlideList(ctx context.Context) ([]promotion_web.PromotionSlideResponse, error)
	GetPromotionSummary(ctx context.Context, req promotion_web.PromotionSummaryRequest) (*promotion_web.PromotionSummaryResponse, error)
	GetUserPromotionSummary(ctx context.Context, req promotion_web.UserPromotionSummaryRequest) (*promotion_web.UserPromotionSummaryResponse, error)
	GetPromotionByHiddenURL(ctx context.Context, userID int64, hiddenURL string) (*promotion_web.GetPromotionByHiddenURLResponse, error)
	GetUserTurnoverSummary(ctx context.Context, userPromotionID int64) (*promotion_web.UserTurnoverSummaryResponse, error)
	CheckUserHasPromotionType(ctx context.Context, userID int64, promotionTypeID int64) (bool, error)
	CheckUserFirstDeposit(ctx context.Context, userID int64) (bool, error)
	GetUserLockedAmount(ctx context.Context, userID int64) (float64, error)
	GetUserCollectedPromotions(ctx context.Context, userID int64, req promotion_web.GetUserCollectedPromotionsRequest) ([]promotion_web.GetUserCollectedPromotionsResponse, int64, error)
	GetUserPromotionByID(ctx context.Context, userID int64, promotionID int64) (*promotion_web.GetPromotionByHiddenURLResponse, error)
}
