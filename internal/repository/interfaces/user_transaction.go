package interfaces

import (
	"blacking-api/internal/domain/user_transaction"
	"context"
)

type UserTransactionRepository interface {
	// Create operations
	Create(ctx context.Context, transaction *user_transaction.UserTransaction) (*user_transaction.UserTransaction, error)

	// Read operations
	GetByID(ctx context.Context, id int64) (*user_transaction.UserTransaction, error)

	// Pagination operations for different transaction types
	GetDepositPage(ctx context.Context, req *user_transaction.UserTransactionDepositPageRequest) ([]*user_transaction.UserTransaction, int64, error)
	GetWithdrawPage(ctx context.Context, req *user_transaction.UserTransactionWithdrawPageRequest) ([]*user_transaction.UserTransaction, int64, error)
	GetTransferPage(ctx context.Context, req *user_transaction.UserTransactionTransferPageRequest) ([]*user_transaction.UserTransaction, int64, error)

	// Member-specific transaction operations
	GetDepositsByMemberID(ctx context.Context, memberID int, req *user_transaction.UserTransactionDepositByUserRequest) ([]*user_transaction.UserTransaction, int64, error)
	GetWithdrawsByMemberID(ctx context.Context, memberID int, req *user_transaction.UserTransactionWithdrawByUserRequest) ([]*user_transaction.UserTransaction, int64, error)

	// Update operations
	UpdateStatus(ctx context.Context, id int64, req *user_transaction.UpdateUserTransactionStatusRequest) error

	// Utility operations
	GenerateRefID(ctx context.Context) (string, error)
	GetMemberByPhoneOrCode(ctx context.Context, phoneOrMemberCode string) (int, error)
}
