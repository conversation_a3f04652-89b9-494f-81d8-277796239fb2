package interfaces

import (
	"context"

	"blacking-api/internal/domain/refresh_token"
)

type RefreshTokenRepository interface {
	Create(ctx context.Context, rt *refresh_token.RefreshToken) error
	GetByToken(ctx context.Context, token string) (*refresh_token.RefreshToken, error)
	DeleteByToken(ctx context.Context, token string) error
	DeleteByUserID(ctx context.Context, userID int, userType refresh_token.UserType) error
	DeleteExpired(ctx context.Context) error
	GetByUserID(ctx context.Context, userID int, userType refresh_token.UserType) ([]*refresh_token.RefreshToken, error)
}
