package interfaces

import (
	"blacking-api/internal/domain/deposit_account"
	"context"
)

type DepositAccountRepository interface {
	Create(ctx context.Context, req *deposit_account.DepositAccountRequest) error
	FindAll(ctx context.Context, limit int, offset int, whereClause string, args []interface{}) ([]*deposit_account.DepositAccount, int64, error)
	FindByID(ctx context.Context, id int64) (*deposit_account.DepositAccount, error)
	FindIdExists(ctx context.Context, id int64) (bool, error)
	FindDepositAccountSettingAlgorithmByID(ctx context.Context, id int64) (*deposit_account.DepositAccount, error)
	FindByAccountNumberDuplicate(ctx context.Context, accountNumber string) (bool, error)
	FindByAccountNumberDuplicateAndIdNot(ctx context.Context, accountNumber string, id int64) (bool, error)
	Update(ctx context.Context, id int64, req *deposit_account.DepositAccountUpdateRequest) error
	UpdateAutoBot(ctx context.Context, id int64, autoBotID int64) error
	UpdateAlgorithm(ctx context.Context, id int64, req *deposit_account.DepositAccountSettingAlgorithm) error
	Active(ctx context.Context, id int64, status bool) error
	Delete(ctx context.Context, id int64) error
	GetAccountTransferTypes(ctx context.Context) ([]*deposit_account.AccountTransferType, error)
}
