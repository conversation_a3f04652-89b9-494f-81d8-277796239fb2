package interfaces

import (
	"blacking-api/internal/domain/member_audit_log"
	"context"
)

// MemberAuditLogRepository defines the interface for member audit log repository operations
type MemberAuditLogRepository interface {
	// Create creates a new member audit log entry
	Create(ctx context.Context, auditLog *member_audit_log.MemberAuditLog) error

	// List retrieves all audit logs with pagination and optional filters (deprecated - use ListWithFilter)
	List(ctx context.Context, limit, offset int, username string, action string) ([]*member_audit_log.MemberAuditLog, error)

	// Count returns the total count of audit logs with optional filters (deprecated - use CountWithFilter)
	Count(ctx context.Context, username string, action string) (int64, error)

	// ListWithFilter retrieves audit logs with pagination and advanced filters
	ListWithFilter(ctx context.Context, limit, offset int, filter *member_audit_log.MemberAuditLogFilter) ([]*member_audit_log.MemberAuditLog, error)

	// CountWithFilter returns the total count of audit logs with advanced filters
	CountWithFilter(ctx context.Context, filter *member_audit_log.MemberAuditLogFilter) (int64, error)
}
