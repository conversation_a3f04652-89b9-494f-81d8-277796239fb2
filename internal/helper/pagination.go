package helper

const (
	DefaultPageSize = 10
	MaxPageSize     = 100
	MinPageSize     = 1
)

type PaginationParams struct {
	Page   int
	Limit  int
	Offset int
}

// Pagination provides a generic pagination structure
type Pagination struct {
	Page  int `json:"page"`
	Limit int `json:"limit"`
}

// GetLimit returns the limit for database queries
func (p *Pagination) GetLimit() int {
	if p == nil || p.Limit <= 0 {
		return DefaultPageSize
	}
	if p.Limit > MaxPageSize {
		return MaxPageSize
	}
	return p.Limit
}

// GetOffset returns the offset for database queries
func (p *Pagination) GetOffset() int {
	if p == nil || p.Page <= 0 {
		return 0
	}
	return (p.Page - 1) * p.GetLimit()
}

func UnlimitPagination(page, limit *int) error {

	if *page <= 0 {
		*page = 1
	}

	if *limit < 0 {
		*limit = 0
	}

	// Remove the page decrement - this was causing pagination issues
	// The page number should remain as-is since CalculatePagination
	// will handle the offset calculation correctly
	return nil
}

func CalculateTotalPages(total int64, limit int) int64 {
	if limit <= 0 {
		return 0
	}
	if total == 0 {
		return 1
	}
	totalPages := total / int64(limit)
	if total%int64(limit) > 0 {
		totalPages++
	}
	return totalPages
}

func CalculatePagination(requestPage, requestLimit int) PaginationParams {
	page := max(1, requestPage)

	// Use the requested limit, but apply min/max constraints
	limit := requestLimit
	if limit <= 0 {
		limit = DefaultPageSize
	}
	if limit > MaxPageSize {
		limit = MaxPageSize
	}

	offset := (page - 1) * limit

	return PaginationParams{
		Page:   page,
		Limit:  limit,
		Offset: offset,
	}
}
