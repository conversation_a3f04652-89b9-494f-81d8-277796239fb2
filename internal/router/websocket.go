package router

import (
	"github.com/gin-gonic/gin"
)

// SetupWebSocketTestRoutes sets up WebSocket test routes
func SetupWebSocketTestRoutes(rg *gin.RouterGroup, handlers *Handlers) {
	websocket := rg.Group("/websocket")
	{
		// WebSocket test endpoints
		websocket.POST("/test", handlers.WebSocketTestHandler.TestWebSocketConnectionSimple)
		websocket.GET("/info", handlers.WebSocketTestHandler.GetWebSocketInfo)
	}
}
