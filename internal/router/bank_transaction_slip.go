package router

import (
	"github.com/gin-gonic/gin"
)

func SetupBankTransactionSlipRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Bank transaction slip endpoints
	bankSlip := r.Group("/bank-transaction-slip")
	{
		// Apply authentication middleware for all bank transaction slip endpoints
		bankSlip.Use(handlers.JwtMiddleware.MiddlewareFunc())

		// POST endpoints for creating slips
		bankSlip.POST("", handlers.BankTransactionSlipHandler.Create)

		// GET endpoints for retrieving slips
		bankSlip.GET("", handlers.BankTransactionSlipHandler.GetList)
		bankSlip.GET("/:id", handlers.BankTransactionSlipHandler.GetByID)
		bankSlip.GET("/transaction/:transactionId", handlers.BankTransactionSlipHandler.GetByTransactionID)
		bankSlip.GET("/member/:memberId", handlers.BankTransactionSlipHandler.GetByMemberID)
		bankSlip.GET("/status-counts", handlers.BankTransactionSlipHandler.GetStatusCounts)

		// PUT endpoints for updating slips
		bankSlip.PUT("/:id", handlers.BankTransactionSlipHandler.Update)

		// PATCH endpoints for partial updates
		bankSlip.PATCH("/:id/status", handlers.BankTransactionSlipHandler.UpdateStatus)

		// DELETE endpoints
		bankSlip.DELETE("/:id", handlers.BankTransactionSlipHandler.Delete)
	}
}
