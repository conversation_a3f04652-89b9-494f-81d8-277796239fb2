package router

import (
	"github.com/gin-gonic/gin"
)

// SetupCryptoDepositRoutes sets up all crypto deposit related routes
func SetupCryptoDepositRoutes(router *gin.Engine, handlers *Handlers) {
	// Crypto deposit routes - requires member authentication
	cryptoGroup := router.Group("/api/v1/payment-gateway/crypto")
	cryptoGroup.Use(handlers.MemberJwtMiddleware.MiddlewareFunc())
	{
		// Configuration endpoint
		cryptoGroup.GET("/config", handlers.CryptoDepositHandler.GetConfiguration)

		// Conversion and rate endpoints
		cryptoGroup.GET("/convert", handlers.CryptoDepositHandler.CalculateConversion)
		cryptoGroup.GET("/tokens", handlers.CryptoDepositHandler.ListChainTokens)
		cryptoGroup.PUT("/tokens/:tokenId/rate", handlers.CryptoDepositHandler.UpdateChainTokenRate)

		// Deposit lifecycle endpoints
		cryptoGroup.POST("/initiate", handlers.CryptoDepositHandler.InitiateDeposit)
		cryptoGroup.PUT("/:transactionId/step1", handlers.CryptoDepositHandler.UpdateStep1)
		cryptoGroup.PUT("/:transactionId/step2", handlers.CryptoDepositHandler.UpdateStep2)
		cryptoGroup.GET("/:transactionId", handlers.CryptoDepositHandler.GetDepositStatus)

		// List and logs endpoints
		cryptoGroup.GET("/deposits", handlers.CryptoDepositHandler.ListDeposits)
		cryptoGroup.GET("/:transactionId/logs", handlers.CryptoDepositHandler.GetDepositLogs)

		// Backend wallet management (for monitoring/admin)
		cryptoGroup.PUT("/wallets/:walletAddress/balance", handlers.CryptoDepositHandler.UpdateBackendWalletBalance)
	}
}
