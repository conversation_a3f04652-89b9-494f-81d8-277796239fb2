package router

import (
	"github.com/gin-gonic/gin"
)

func SetupMemberRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Member endpoints (Admin access)
	members := r.Group("/members")
	{
		members.Use(handlers.JwtMiddleware.MiddlewareFunc()) // Admin JWT for managing members
		members.POST("", handlers.MemberHandler.CreateMember)
		members.GET("", handlers.MemberHandler.ListMembersWithFilter)
		members.GET("/status-counts", handlers.MemberHandler.GetMemberStatusCounts)
		members.GET("/:id", handlers.MemberHandler.GetMember)
		members.PUT("/:id", handlers.MemberHandler.UpdateMember)
		members.POST("/:id/delete", handlers.MemberHandler.DeleteMember)
		members.POST("/:id/change-password", handlers.MemberHandler.ChangeMemberPassword)
		members.POST("/:id/change-partner", handlers.MemberHandler.ChangeMemberPartner)
		members.POST("/:id/update-bank-info", handlers.MemberHandler.UpdateMemberBankInfo)
		members.PATCH("/:id/suspend", handlers.MemberHandler.SuspendMember)
		members.GET("/username/:username", handlers.MemberHandler.GetMemberByUsername)
		members.GET("/game-username/:game_username", handlers.MemberHandler.GetMemberByGameUsername)
		members.GET("/:id/game-providers", handlers.MemberHandler.GetGameProviders)
		members.GET("/:id/game-providers/:provider", handlers.MemberHandler.GetGameProviderInfo)
		members.GET("/:id/game-lists/:provider", handlers.MemberHandler.GetGameLists)
		members.POST("/:id/backfill-game-username", handlers.MemberHandler.BackfillGameUsername)
	}
}

func SetupMemberAreaRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Member protected endpoints (Member JWT required)
	memberArea := r.Group("/member")
	{
		memberArea.Use(handlers.MemberJwtMiddleware.MiddlewareFunc())
		// Member-specific endpoints
		memberArea.GET("/me", handlers.MemberHandler.GetCurrentUser)
		// memberArea.PUT("/profile", handlers.MemberHandler.UpdateProfile)
	}
}
