package router

import (
	"github.com/gin-gonic/gin"
)

func SetupPromotionWebRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Admin promotion web routes (requires admin authentication)
	adminPromotionWeb := r.Group("/promotion-web")
	adminPromotionWeb.Use(handlers.JwtMiddleware.MiddlewareFunc())    // JWT auth middleware
	adminPromotionWeb.Use(handlers.AuthMiddleware.RequireAdminRole()) // Admin role middleware
	{
		// Basic CRUD operations
		adminPromotionWeb.POST("", handlers.PromotionWebHandler.CreatePromotionWeb)
		adminPromotionWeb.GET("", handlers.PromotionWebHandler.GetPromotionWebList)
		adminPromotionWeb.GET("/:id", handlers.PromotionWebHandler.GetPromotionWebById)
		adminPromotionWeb.PUT("/:id", handlers.PromotionWebHandler.UpdatePromotionWeb)
		adminPromotionWeb.DELETE("/:id", handlers.PromotionWebHandler.DeletePromotionWeb)

		// Promotion web specific operations
		adminPromotionWeb.POST("/:id/cancel", handlers.PromotionWebHandler.CancelPromotionWeb)
		adminPromotionWeb.GET("/:id/users-to-cancel", handlers.PromotionWebHandler.GetPromotionWebUserToCancel)

		// Priority order management
		adminPromotionWeb.PUT("/:id/priority-order", handlers.PromotionWebHandler.UpdatePromotionWebPriorityOrder)
		adminPromotionWeb.POST("/sort-priority", handlers.PromotionWebHandler.SortPromotionWebPriorityOrder)

		// File upload
		adminPromotionWeb.POST("/upload-image", handlers.PromotionWebHandler.UploadImageToCloudflare)

		// User promotion management
		adminPromotionWeb.GET("/users", handlers.PromotionWebHandler.GetUserPromotionWebList)
		adminPromotionWeb.GET("/users/:id", handlers.PromotionWebHandler.GetPromotionWebUserById)
		adminPromotionWeb.POST("/users/:id/cancel", handlers.PromotionWebHandler.CancelPromotionWebUserById)

		// Option/lookup endpoints
		adminPromotionWeb.GET("/options/types", handlers.PromotionWebHandler.GetPromotionTypes)
		adminPromotionWeb.GET("/options/statuses", handlers.PromotionWebHandler.GetPromotionStatuses)
		adminPromotionWeb.GET("/options/bonus-conditions", handlers.PromotionWebHandler.GetPromotionBonusConditions)
		adminPromotionWeb.GET("/options/bonus-types", handlers.PromotionWebHandler.GetPromotionBonusTypes)
		adminPromotionWeb.GET("/options/turnover-types", handlers.PromotionWebHandler.GetPromotionTurnoverTypes)
		adminPromotionWeb.GET("/options/date-types", handlers.PromotionWebHandler.GetPromotionDateTypes)

		// Additional admin operations from migration document
		adminPromotionWeb.GET("/slide-list", handlers.PromotionWebHandler.GetPromotionSlideList)
		adminPromotionWeb.PUT("/sort-priority-order", handlers.PromotionWebHandler.SortPromotionPriorityOrder)
		adminPromotionWeb.GET("/summary", handlers.PromotionWebHandler.GetPromotionSummary)
		adminPromotionWeb.GET("/user-summary", handlers.PromotionWebHandler.GetUserPromotionSummary)

		// File upload
		adminPromotionWeb.POST("/upload/cover", handlers.PromotionWebHandler.UploadPromotionCover)

		// Business logic endpoints
		adminPromotionWeb.POST("/check-user-promotion", handlers.PromotionWebHandler.CheckUserPromotionForDeposit)
		adminPromotionWeb.POST("/check-promotion-withdraw", handlers.PromotionWebHandler.CheckPromotionWithdraw)
	}

	// User promotion web routes (requires user authentication)
	userPromotionWeb := r.Group("/promotion-web/user")
	userPromotionWeb.Use(handlers.MemberJwtMiddleware.MiddlewareFunc()) // Member JWT auth middleware
	userPromotionWeb.Use(handlers.AuthMiddleware.RequireMemberRole())   // Member role middleware
	{
		// User promotion operations
		userPromotionWeb.POST("/collect", handlers.PromotionWebHandler.CollectPromotion)
		userPromotionWeb.GET("/my-promotions", handlers.PromotionWebHandler.GetUserPromotions)
		userPromotionWeb.GET("/list/:userId", handlers.PromotionWebHandler.PromotionWebUserGetListByUserId)

		// Additional user operations from migration document
		userPromotionWeb.GET("/show/:id", handlers.PromotionWebHandler.GetUserPromotionByID)
		userPromotionWeb.GET("/link/:hiddenUrlLink", handlers.PromotionWebHandler.GetPromotionByHiddenURL)
		userPromotionWeb.GET("/list-collected", handlers.PromotionWebHandler.GetUserCollectedPromotions)
		userPromotionWeb.GET("/turnover-summary/:id", handlers.PromotionWebHandler.GetUserTurnoverSummary)
	}

	// Public promotion web routes (no authentication required)
	publicPromotionWeb := r.Group("/promotion-web/public")
	{
		// Public promotion operations
		publicPromotionWeb.GET("", handlers.PromotionWebHandler.GetPublicPromotions)
		publicPromotionWeb.GET("/:id", handlers.PromotionWebHandler.GetPublicPromotionByID)
		publicPromotionWeb.GET("/slides/active", handlers.PromotionWebHandler.GetActivePromotionWebSlides)
	}

	// Lock credit routes (admin only)
	lockCredit := r.Group("/lock-credit")
	lockCredit.Use(handlers.JwtMiddleware.MiddlewareFunc()) // JWT auth middleware
	//lockCredit.Use(handlers.AuthMiddleware.RequireAdminRole()) // Admin role middleware
	{
		lockCredit.POST("", handlers.PromotionWebHandler.CreateLockCredit)
		lockCredit.GET("/withdraw-list", handlers.PromotionWebHandler.GetLockCreditWithdrawList)
		lockCredit.PUT("/withdraw-unlock/:id", handlers.PromotionWebHandler.UnlockCreditWithdraw)
	}

	// User lock credit routes (user authentication)
	userLockCredit := r.Group("/user/lock-credit")
	userLockCredit.Use(handlers.MemberJwtMiddleware.MiddlewareFunc()) // Member JWT auth middleware
	//userLockCredit.Use(handlers.AuthMiddleware.RequireMemberRole()) // Member role middleware
	{
		userLockCredit.GET("/withdraw-check", handlers.PromotionWebHandler.CheckLockCreditWithdraw)
	}

	//// Legacy routes for backward compatibility
	//promotionWebs := r.Group("/promotion-webs")
	//{
	//	promotionWebs.Use(handlers.JwtMiddleware.MiddlewareFunc())
	//
	//	// Basic CRUD operations (legacy)
	//	promotionWebs.POST("", handlers.PromotionWebHandler.CreatePromotionWeb)
	//	promotionWebs.GET("", handlers.PromotionWebHandler.GetPromotionWebList)
	//	promotionWebs.GET("/:id", handlers.PromotionWebHandler.GetPromotionWebById)
	//	promotionWebs.PUT("/:id", handlers.PromotionWebHandler.UpdatePromotionWeb)
	//	promotionWebs.DELETE("/:id", handlers.PromotionWebHandler.DeletePromotionWeb)
	//}
}
