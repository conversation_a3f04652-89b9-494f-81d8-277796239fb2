package router

import (
	"github.com/gin-gonic/gin"
)

func SetupMemberAuthRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Member authentication endpoints
	memberAuth := r.Group("/member-auth")
	{
		// Registration mode endpoint
		memberAuth.GET("/register/mode", handlers.MemberRegistrationHandler.GetRegistrationMode)

		// Unified registration endpoint (handles both direct and OTP-based registration)
		memberAuth.POST("/register", handlers.MemberRegistrationHandler.Register)

		// OTP-based registration (2-step process before /register)
		memberAuth.POST("/register/send-otp", handlers.MemberRegistrationHandler.SendRegistrationOTP)
		memberAuth.POST("/register/verify-otp", handlers.MemberRegistrationHandler.VerifyRegistrationOTP)

		memberAuth.POST("/login", handlers.MemberJwtMiddleware.LoginHandler)
		memberAuth.POST("/logout", handlers.MemberJwtMiddleware.LogoutHandler)
		memberAuth.POST("/refresh-token", handlers.RefreshTokenHandler.RefreshToken)
		memberAuth.POST("/invalidate-tokens", handlers.RefreshTokenHandler.InvalidateAllTokens)

		// Game provider endpoint
		memberAuth.POST("/game", handlers.MemberHandler.GetGameProviderData)
	}
}
