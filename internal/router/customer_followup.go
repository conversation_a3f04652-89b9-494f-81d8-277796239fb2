package router

import (
	"github.com/gin-gonic/gin"
)

func SetupCustomerFollowUpRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Customer Follow-Up routes (admin only)
	customerFollowUp := r.Group("/customer-followup")
	customerFollowUp.Use(handlers.JwtMiddleware.MiddlewareFunc())
	{
		// List customers for follow-up
		customerFollowUp.GET("", handlers.CustomerFollowUpHandler.ListCustomerFollowUp)

		// List old customers for follow-up (based on last_online)
		customerFollowUp.GET("/search-old-customers", handlers.CustomerFollowUpHandler.ListOldCustomerFollowUp)

		// Export old customers as CSV
		customerFollowUp.GET("/export-old-customers-csv", handlers.CustomerFollowUpHandler.ExportOldCustomerFollowUpCSV)

		// Track customer actions
		customerFollowUp.POST("/track-call", handlers.CustomerFollowUpHandler.TrackCustomerByCall)
		customerFollowUp.POST("/track-sms", handlers.CustomerFollowUpHandler.TrackCustomerBySMS)

		// Update member follow-up info
		customerFollowUp.PATCH("/update-status", handlers.CustomerFollowUpHandler.UpdateFollowUpStatus)
		customerFollowUp.PATCH("/update-tag", handlers.CustomerFollowUpHandler.UpdateFollowUpTag)
		customerFollowUp.PATCH("/update-remark", handlers.CustomerFollowUpHandler.UpdateMemberRemark)

		// Get options and export
		customerFollowUp.GET("/options", handlers.CustomerFollowUpHandler.GetFollowUpOptions)
		customerFollowUp.GET("/export-csv", handlers.CustomerFollowUpHandler.ExportCustomerFollowUpCSV)
	}
}
