package router

import (
	"github.com/gin-gonic/gin"
)

// SetupReturnTurnRoutes sets up all return turn related routes
func SetupReturnTurnRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Use handler from the Handlers struct
	returnTurnHandler := handlers.ReturnTurnHandler

	// Setup admin routes
	adminGroup := r.Group("/admin")
	adminGroup.Use(handlers.JwtMiddleware.MiddlewareFunc())
	adminGroup.Use(handlers.AuthMiddleware.RequireAdminRole())
	{
		// Return turn settings
		returnTurn := adminGroup.Group("/return-turn/setting")
		{
			returnTurn.GET("", returnTurnHandler.GetReturnSetting)
			returnTurn.PATCH("", returnTurnHandler.UpdateReturnSetting)

		}

		// Reference data
		reference := adminGroup.Group("/return-turn")
		{
			reference.GET("/cut-types", returnTurnHandler.GetReturnTurnCutTypes)
			reference.GET("/loser-types", returnTurnHandler.GetReturnTurnLoserTypes)
			reference.GET("/calculate-play-types", returnTurnHandler.GetCalculatePlayTypes)
		}

		// History reports
		history := adminGroup.Group("/return-turn/history")
		{
			history.GET("/member-list", returnTurnHandler.GetReturnHistoryMemberList)
			history.GET("/member-summary", returnTurnHandler.GetReturnHistoryMemberSummary)
			history.GET("/log-list", returnTurnHandler.GetReturnHistoryLogList)
		}

		// Customer promotion management
		//customerPromotion := adminGroup.Group("/customer-promotion")
		//{
		//	customerPromotion.GET("/list", returnTurnHandler.GetCustomerPromotionList)
		//	customerPromotion.POST("/cancel", returnTurnHandler.CancelCustomerPromotion)
		//}
	}

	// Setup web/user routes
	webGroup := r.Group("/web")
	webGroup.Use(handlers.MemberJwtMiddleware.MiddlewareFunc())
	webGroup.Use(handlers.AuthMiddleware.RequireMemberRole())
	{
		// Return turn loser endpoints
		returnLoser := webGroup.Group("/return-turn-loser")
		{
			returnLoser.GET("/current", returnTurnHandler.GetUserCurrentReturnDetail)
			returnLoser.POST("/take", returnTurnHandler.TakeUserReturnAmount)
			returnLoser.GET("/list", returnTurnHandler.GetUserReturnHistoryList)
		}

		// Promotions open list
		//promotions := webGroup.Group("/promotions")
		//{
		//	promotions.GET("/open-list/promotion-list", returnTurnHandler.GetPromotionOpenList)
		//}
	}

	// Setup cron routes (usually protected with special auth or IP restriction)
	cronGroup := r.Group("/cron")
	{
		cronReturnLoser := cronGroup.Group("/return-turn-loser")
		{
			cronReturnLoser.GET("/cut-daily", returnTurnHandler.CronCutReturnLossDaily)
			cronReturnLoser.GET("/cut-date", returnTurnHandler.CronCutReturnLossByDate)
		}
	}
}
