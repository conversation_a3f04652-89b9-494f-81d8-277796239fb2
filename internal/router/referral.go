package router

import (
	"github.com/gin-gonic/gin"
)

func SetupReferralAPIRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Referral endpoints (Member JWT required)
	referrals := r.Group("/referrals")
	{
		referrals.Use(handlers.MemberJwtMiddleware.MiddlewareFunc())

		// API 1: ภาพรวม - Overview statistics
		referrals.GET("/overview", handlers.ReferralHandler.GetOverview)

		// API 2: สมาชิกภายใต้ referral เรา - Referral members list
		referrals.GET("/members", handlers.ReferralHandler.GetReferralMembers)

		// API 3: รายได้ - Income data and history
		referrals.GET("/income", handlers.ReferralHandler.GetIncomeData)

		// API 3.1: รายได้ตาม game category - Commission by game category
		referrals.GET("/commission-by-game-category", handlers.ReferralReportHandler.GetCommissionByGameCategory)

		// API 4: ข้อมูล tutorial และ FAQ
		referrals.GET("/tutorial-faq", handlers.ReferralHandler.GetTutorialAndFAQ)

		// API 5: ถอน commission balance
		referrals.POST("/withdraw", handlers.ReferralHandler.WithdrawCommission)

		// API 6: ดึงค่า pending commission พร้อม commission percent
		referrals.GET("/pending-commissions", handlers.ReferralHandler.GetPendingCommissions)

		// API 7: approve pending commission
		referrals.POST("/approve-commissions", handlers.ReferralHandler.ApproveCommissions)
	}

	// Public referral endpoints (no authentication required)
	referralsPublic := r.Group("/referrals")
	{
		// API 5: นับ view referral link (public endpoint)
		referralsPublic.POST("/view/:referral_code", handlers.ReferralHandler.IncrementReferralView)
	}
}
