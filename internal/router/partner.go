package router

import (
	"github.com/gin-gonic/gin"
)

func SetupPartnerRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Partner management (Admin access only)
	partners := r.Group("/partners")
	{
		partners.Use(handlers.JwtMiddleware.MiddlewareFunc())
		partners.POST("", handlers.PartnerHandler.CreatePartner)
		partners.GET("", handlers.PartnerHandler.ListPartners)
		partners.GET("/dropdown", handlers.PartnerHandler.ListPartnersForDropdown)
		partners.GET("/:id", handlers.PartnerHandler.GetPartnerByID)
		partners.PUT("/:id", handlers.PartnerHandler.UpdatePartner)
		partners.POST("/:id/delete", handlers.PartnerHandler.DeletePartner)
		partners.PATCH("/:id/suspend", handlers.PartnerHandler.SuspendPartner)
		partners.PUT("/:id/password", handlers.PartnerHandler.ChangePartnerPassword)
	}
}
