package router

import (
	"github.com/gin-gonic/gin"
)

func SetupLanguageRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Language endpoints (no auth required for dropdown data)
	languages := r.Group("/languages")
	{
		languages.GET("", handlers.LanguageHandler.GetSupportedLanguages)
		languages.GET("/active", handlers.LanguageHandler.GetActiveLanguages)
		languages.GET("/:code", handlers.LanguageHandler.GetLanguageByCode)
		languages.POST("/validate", handlers.LanguageHandler.ValidateLanguageCode)
	}
}

func SetupBannerRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Banner endpoints
	banners := r.Group("/banners")
	{
		banners.Use(handlers.JwtMiddleware.MiddlewareFunc())
		banners.POST("", handlers.BannerHandler.CreateBanner)
		banners.GET("", handlers.BannerHandler.ListBanners)
		banners.GET("/:id", handlers.BannerHandler.GetBannerByID)
		banners.PUT("/:id", handlers.BannerHandler.UpdateBanner)
		banners.DELETE("/:id", handlers.BannerHandler.DeleteBanner)
		banners.PUT("/reorder", handlers.BannerHandler.ReorderBanners)
		banners.POST("/upload", handlers.BannerHandler.FileUpload)
		banners.DELETE("/file", handlers.BannerHandler.DeleteFile)
	}
}
