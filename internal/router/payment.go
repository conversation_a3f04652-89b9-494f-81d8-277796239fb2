package router

import (
	"github.com/gin-gonic/gin"
)

func SetupPaymentRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Payment Method endpoints
	paymentMethod := r.Group("/payment-method")
	{
		paymentMethod.POST("", handlers.PaymentMethodHandler.CreatePaymentMethod)
		paymentMethod.GET("", handlers.PaymentMethodHandler.ListPaymentMethods)
		paymentMethod.GET("/:id", handlers.PaymentMethodHandler.GetPaymentMethodByID)
		paymentMethod.PUT("/:id", handlers.PaymentMethodHandler.UpdatePaymentMethod)
		paymentMethod.PUT("/:id/status", handlers.PaymentMethodHandler.UpdatePaymentMethodStatus)
		paymentMethod.DELETE("/:id", handlers.PaymentMethodHandler.DeletePaymentMethod)
	}
}
