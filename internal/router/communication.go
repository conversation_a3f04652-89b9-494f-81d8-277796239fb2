package router

import (
	"github.com/gin-gonic/gin"
)

func SetupCommunicationRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// SMS Provider Name endpoints
	smsProviderName := r.Group("/sms-provider-name")
	smsProviderName.Use(handlers.JwtMiddleware.MiddlewareFunc())
	smsProviderName.Use(handlers.AuthMiddleware.RequireAdminRole())
	{
		smsProviderName.GET("", handlers.SMSProviderNameHandler.ListSMSProviderNames)
	}

	// SMS Provider endpoints
	smsProvider := r.Group("/sms-provider")
	smsProvider.Use(handlers.JwtMiddleware.MiddlewareFunc())
	smsProvider.Use(handlers.AuthMiddleware.RequireAdminRole())
	{
		smsProvider.POST("", handlers.SMSProviderHandler.CreateSMSProvider)
		smsProvider.GET("", handlers.SMSProviderHandler.GetSMSProviders)
		smsProvider.GET("/:id", handlers.SMSProviderHandler.GetSMSProviderByID)
		smsProvider.PUT("/:id", handlers.SMSProviderHandler.UpdateSMSProvider)
		smsProvider.PUT("/:id/status", handlers.SMSProviderHandler.UpdateSMSProviderStatus)
		smsProvider.DELETE("/:id", handlers.SMSProviderHandler.DeleteSMSProvider)
	}

	// Contact endpoints
	contact := r.Group("/contact")
	contact.Use(handlers.JwtMiddleware.MiddlewareFunc())
	contact.Use(handlers.AuthMiddleware.RequireAdminRole())
	{
		contact.POST("", handlers.ContactHandler.CreateContact)
		contact.POST("/upload-image", handlers.ContactHandler.UploadImage)
		contact.GET("", handlers.ContactHandler.GetContacts)
		contact.GET("/:id", handlers.ContactHandler.GetContactByID)
		contact.PUT("/:id", handlers.ContactHandler.UpdateContact)
		contact.PUT("/:id/status", handlers.ContactHandler.UpdateContactStatus)
		contact.DELETE("/:id", handlers.ContactHandler.DeleteContact)
	}
}
