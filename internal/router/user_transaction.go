package router

import (
	"github.com/gin-gonic/gin"
)

func SetupUserTransactionRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Common transaction endpoints (requires either admin or member authentication)
	transaction := r.Group("/transaction")
	transaction.Use(handlers.AuthMiddleware.RequireAdminOrMemberRole()) // Admin OR Member authentication
	{
		// File upload for transaction slips - accessible by both admin and member
		transaction.POST("/upload-slip", handlers.UserTransactionHandler.UploadSlip)
	}

	// Admin transaction endpoints (requires admin authentication)
	adminTransaction := r.Group("/transaction/admin")
	adminTransaction.Use(handlers.JwtMiddleware.MiddlewareFunc())    // JWT auth middleware
	adminTransaction.Use(handlers.AuthMiddleware.RequireAdminRole()) // Admin role middleware
	{
		// Admin can create all types of transactions
		adminTransaction.POST("/deposit", handlers.UserTransactionHandler.CreateDeposit)
		adminTransaction.POST("/withdraw", handlers.UserTransactionHandler.CreateWithdraw)
		adminTransaction.POST("/transfer", handlers.UserTransactionHandler.CreateTransfer)

		// Admin can view all transactions with pagination
		adminTransaction.GET("/deposit/page", handlers.UserTransactionHandler.GetDepositPage)
		adminTransaction.GET("/withdraw/page", handlers.UserTransactionHandler.GetWithdrawPage)
		adminTransaction.GET("/transfer/page", handlers.UserTransactionHandler.GetTransferPage)

		// Admin can view individual transactions
		adminTransaction.GET("/deposit/:id", handlers.UserTransactionHandler.GetDepositByID)
		adminTransaction.GET("/withdraw/:id", handlers.UserTransactionHandler.GetWithdrawByID)
		adminTransaction.GET("/transfer/:id", handlers.UserTransactionHandler.GetTransferByID)

		// Admin can view member-specific transaction lists
		adminTransaction.GET("/deposit/member/:memberId/page", handlers.UserTransactionHandler.GetDepositsByMemberID)
		adminTransaction.GET("/withdraw/member/:memberId/page", handlers.UserTransactionHandler.GetWithdrawsByMemberID)

		// Admin can update transaction statuses
		adminTransaction.PUT("/deposit/status/:id", handlers.UserTransactionHandler.UpdateDepositStatus)
		adminTransaction.PUT("/withdraw/status/:id", handlers.UserTransactionHandler.UpdateWithdrawStatus)
		adminTransaction.PUT("/transfer/status/:id", handlers.UserTransactionHandler.UpdateTransferStatus)
	}

	// Member transaction endpoints (requires member authentication)
	memberTransaction := r.Group("/transaction/member")
	memberTransaction.Use(handlers.MemberJwtMiddleware.MiddlewareFunc()) // Member JWT auth middleware
	memberTransaction.Use(handlers.AuthMiddleware.RequireMemberRole())   // Member role middleware
	{
		// Members can create their own transactions
		memberTransaction.POST("/web/deposit", handlers.UserTransactionHandler.CreateWebDeposit)
		memberTransaction.POST("/web/withdraw", handlers.UserTransactionHandler.CreateWebWithdraw)
	}

	// Legacy/Backward compatibility routes (will be deprecated)
	// Keep the old routes temporarily for backward compatibility
	//transaction := r.Group("/transaction")
	//transaction.Use(handlers.JwtMiddleware.MiddlewareFunc()) // Requires admin JWT
	//{
	//	// Legacy endpoints - these should be migrated to /admin/transaction
	//	transaction.POST("/deposit", handlers.UserTransactionHandler.CreateDeposit)
	//	transaction.POST("/withdraw", handlers.UserTransactionHandler.CreateWithdraw)
	//	transaction.POST("/transfer", handlers.UserTransactionHandler.CreateTransfer)
	//	transaction.POST("/web/deposit", handlers.UserTransactionHandler.CreateWebDeposit)
	//
	//	transaction.GET("/deposit/page", handlers.UserTransactionHandler.GetDepositPage)
	//	transaction.GET("/withdraw/page", handlers.UserTransactionHandler.GetWithdrawPage)
	//	transaction.GET("/transfer/page", handlers.UserTransactionHandler.GetTransferPage)
	//
	//	transaction.GET("/deposit/:id", handlers.UserTransactionHandler.GetDepositByID)
	//	transaction.GET("/withdraw/:id", handlers.UserTransactionHandler.GetWithdrawByID)
	//	transaction.GET("/transfer/:id", handlers.UserTransactionHandler.GetTransferByID)
	//
	//	transaction.GET("/deposit/member/:memberId/page", handlers.UserTransactionHandler.GetDepositsByMemberID)
	//	transaction.GET("/withdraw/member/:memberId/page", handlers.UserTransactionHandler.GetWithdrawsByMemberID)
	//
	//	transaction.PUT("/deposit/status/:id", handlers.UserTransactionHandler.UpdateDepositStatus)
	//	transaction.PUT("/withdraw/status/:id", handlers.UserTransactionHandler.UpdateWithdrawStatus)
	//	transaction.PUT("/transfer/status/:id", handlers.UserTransactionHandler.UpdateTransferStatus)
	//}
}
