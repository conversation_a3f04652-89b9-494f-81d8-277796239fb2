package router

import (
	"blacking-api/internal/handler/http"

	"github.com/gin-gonic/gin"
)

func SetupPaymentGatewayRoutes(router *gin.RouterGroup, paymentGatewayHandler *http.PaymentGatewayHandler) {
	// Payment Gateway API group
	paymentGateway := router.Group("/payment-gateway")
	{
		// Provider management routes
		providers := paymentGateway.Group("/providers")
		{
			providers.GET("", paymentGatewayHandler.GetProviders)
			providers.POST("", paymentGatewayHandler.CreateProvider)
			providers.GET("/active", paymentGatewayHandler.GetActiveProviders)
			providers.PUT("/:id/status", paymentGatewayHandler.UpdateProviderStatus)
		}

		// Transaction routes
		paymentGateway.GET("/transactions", paymentGatewayHandler.GetTransactions)
		paymentGateway.GET("/transactions/:id", paymentGatewayHandler.GetTransactionByID)

		// JaiJaiPay specific routes
		jaijaipay := paymentGateway.Group("/jaijaipay")
		{
			jaijaipay.POST("/test", paymentGatewayHandler.TestJaiJaiPayConnection)
			jaijaipay.GET("/balance", paymentGatewayHandler.GetJaiJaiPayBalance)
			jaijaipay.POST("/deposits", paymentGatewayHandler.CreateJaiJaiPayDeposit)
			jaijaipay.POST("/webhooks/resend", paymentGatewayHandler.ResendJaiJaiPayWebhook)
		}
	}
}
