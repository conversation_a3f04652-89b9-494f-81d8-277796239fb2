package router

import (
	"blacking-api/internal/handler/http"
	"blacking-api/internal/service"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

func SetupDashboardRoutes(router *gin.RouterGroup, logger logger.Logger) {
	dashboardService := service.NewDashboardService(logger)
	dashboardHandler := http.NewDashboardHandler(dashboardService)

	dashboard := router.Group("/dashboard")
	{
		dashboard.GET("", dashboardHandler.GetDashboard)
		dashboard.GET("/bot", dashboardHandler.GetBotSuccess)
	}
}
