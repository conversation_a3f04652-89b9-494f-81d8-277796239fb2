package router

import (
	"github.com/gin-gonic/gin"
)

func SetupUserRoutes(r *gin.RouterGroup, handlers *Handlers) {
	users := r.Group("/users")
	{
		// Public endpoints (no middleware required)
		users.POST("/login", handlers.JwtMiddleware.LoginHandler)

		// Refresh token endpoints (no middleware needed)
		users.POST("/refresh-token", handlers.RefreshTokenHandler.RefreshToken)

		// Protected endpoints (require authentication)
		users.Use(handlers.JwtMiddleware.MiddlewareFunc())
		users.POST("/logout", handlers.JwtMiddleware.LogoutHandler)
		users.POST("/invalidate-tokens", handlers.RefreshTokenHandler.InvalidateAllTokens)
		users.GET("/me", handlers.UserHandler.GetCurrentAdmin)
		users.POST("", handlers.UserHandler.CreateUser)
		users.GET("", handlers.UserHandler.ListUsers)
		users.GET("/:id", handlers.UserHandler.GetUser)
		users.PUT("/:id", handlers.UserHandler.UpdateUser)
		users.DELETE("/:id", handlers.UserHandler.DeleteUser)
		users.PATCH("/:id/activate", handlers.UserHandler.ActivateUser)
		users.PATCH("/:id/deactivate", handlers.UserHandler.DeactivateUser)
		users.PATCH("/:id/suspend", handlers.UserHandler.SuspendUser)
		users.PATCH("/:id/password", handlers.UserHandler.ChangePassword)
		users.GET("/dropdown", handlers.UserHandler.GetUsersForDropdown)
	}
}
