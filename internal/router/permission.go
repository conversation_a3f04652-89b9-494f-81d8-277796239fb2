package router

import (
	"github.com/gin-gonic/gin"
)

func SetupPermissionRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Permission endpoints
	permissions := r.Group("/permissions")
	{
		permissions.Use(handlers.JwtMiddleware.MiddlewareFunc())
		// Get permission matrix (all permissions grouped by groups)
		permissions.GET("/matrix", handlers.PermissionHandler.GetPermissionMatrix)
		// Get all permissions with search and pagination
		permissions.GET("", handlers.PermissionHandler.GetAllPermissions)
		// Get permissions by group
		permissions.GET("/groups/:group_id/permissions", handlers.PermissionHandler.GetPermissionsByGroup)

		// Current user permission checks
		permissions.GET("/check/:permission_key", handlers.PermissionHandler.CheckUserPermission)
		permissions.GET("/my-permissions", handlers.PermissionHandler.CheckMyPermissions)

		// User role permission management
		userRolePermissions := permissions.Group("/user-roles")
		{
			// Get user role permissions matrix
			userRolePermissions.GET("/:user_role_id", handlers.PermissionHandler.GetUserRolePermissions)
			// Bulk update user role permissions
			userRolePermissions.PUT("/:user_role_id/bulk-update", handlers.PermissionHandler.BulkUpdateUserRolePermissions)
		}
	}
}
