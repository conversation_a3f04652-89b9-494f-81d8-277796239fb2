package router

import (
	jwt "github.com/appleboy/gin-jwt/v2"
	"github.com/gin-gonic/gin"

	httpHandler "blacking-api/internal/handler/http"
	"blacking-api/internal/middleware"
)

// SetupJaiJaiPayRoutes sets up all JaiJaiPay API routes
func SetupJaiJaiPayRoutes(
	router *gin.Engine,
	handler *httpHandler.JaiJaiPayHandler,
	authMiddleware *middleware.AuthMiddleware,
	userJWT *jwt.GinJWTMiddleware,
	memberJWT *jwt.GinJWTMiddleware,
) {
	// JaiJaiPay API routes group with v1 versioning
	jaijaipayGroup := router.Group("/api/v1/jaijai")

	// Apply member JWT middleware only (no role check for now)
	jaijaipayGroup.Use(memberJWT.MiddlewareFunc())

	// Webhook endpoint (no authentication required - external service)
	webhookGroup := router.Group("/api/v1/jaijai")
	{
		webhookGroup.POST("/webhook", handler.HandleWebhook) // POST /api/v1/jaijai/webhook
	}

	// Deposits routes
	depositsGroup := jaijaipayGroup.Group("/deposits")
	{
		depositsGroup.POST("", handler.CreateDeposit)                // POST /api/jaijaipay/deposits
		depositsGroup.GET("", handler.ListDeposits)                  // GET /api/jaijaipay/deposits
		depositsGroup.GET("/:transactionId", handler.GetDepositByID) // GET /api/jaijaipay/deposits/:transactionId
		depositsGroup.POST("/cancel", handler.CancelDeposit)         // POST /api/jaijaipay/deposits/cancel
	}

	// Withdrawals routes
	withdrawalsGroup := jaijaipayGroup.Group("/withdrawals")
	{
		withdrawalsGroup.POST("", handler.CreateWithdrawal)                // POST /api/jaijaipay/withdrawals
		withdrawalsGroup.GET("", handler.ListWithdrawals)                  // GET /api/jaijaipay/withdrawals
		withdrawalsGroup.GET("/:transactionId", handler.GetWithdrawalByID) // GET /api/jaijaipay/withdrawals/:transactionId
	}

	// Balance routes
	balanceGroup := jaijaipayGroup.Group("/balance")
	{
		balanceGroup.GET("", handler.GetBalance) // GET /api/jaijaipay/balance
	}

	// Fees routes
	feesGroup := jaijaipayGroup.Group("/fees")
	{
		feesGroup.GET("/preview", handler.GetFeePreview) // GET /api/jaijaipay/fees/preview
	}

	// Analytics routes
	analyticsGroup := jaijaipayGroup.Group("/analytics")
	{
		analyticsGroup.GET("/transactions", handler.GetTransactionAnalytics) // GET /api/jaijaipay/analytics/transactions
	}

	// Webhooks routes
	webhooksGroup := jaijaipayGroup.Group("/webhooks")
	{
		webhooksGroup.POST("/resend", handler.ResendWebhook) // POST /api/jaijaipay/webhooks/resend
	}

	// API Logs routes (for debugging and monitoring)
	logsGroup := jaijaipayGroup.Group("/logs")
	{
		logsGroup.GET("/order/:orderId", handler.GetAPILogsByOrderID)                   // GET /api/jaijaipay/logs/order/:orderId
		logsGroup.GET("/transaction/:transactionId", handler.GetAPILogsByTransactionID) // GET /api/jaijaipay/logs/transaction/:transactionId
	}
}
