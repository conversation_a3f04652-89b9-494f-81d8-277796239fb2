package router

import (
	"github.com/gin-gonic/gin"
)

func SetupSystemRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Public Theme Settings endpoints (no authentication required)
	r.GET("/system/theme", handlers.ThemeSettingHandler.GetTheme)

	// Public SEO Settings endpoint (no authentication required)
	r.GET("/system/settings/seo", handlers.SystemSettingHandler.GetSEOSettings)

	// Allowed IP endpoints
	allowedIPs := r.Group("/allowed-ips")
	{
		allowedIPs.Use(handlers.JwtMiddleware.MiddlewareFunc())
		allowedIPs.GET("", handlers.AllowedIPHandler.ListAllowedIPs)
		allowedIPs.POST("/sync", handlers.AllowedIPHandler.SyncIPs)
		allowedIPs.GET("/check/:ip", handlers.AllowedIPHandler.CheckIPAllowed)
		allowedIPs.GET("/stats", handlers.AllowedIPHandler.GetAllowedIPsStats)
	}

	// System Setting endpoints (protected)
	systemSettings := r.Group("/system")
	{
		systemSettings.Use(handlers.JwtMiddleware.MiddlewareFunc())
		systemSettings.GET("/login-attempt-limit", handlers.SystemSettingHandler.GetLoginAttemptLimit)
		systemSettings.PUT("/login-attempt-limit", handlers.SystemSettingHandler.UpdateLoginAttemptLimit)
		systemSettings.GET("/settings", handlers.SystemSettingHandler.ListSystemSettings)
		systemSettings.GET("/settings/:key", handlers.SystemSettingHandler.GetSystemSetting)
		systemSettings.PUT("/settings/:key", handlers.SystemSettingHandler.UpdateSystemSetting)

		// General Settings endpoints
		systemSettings.GET("/settings/general", handlers.SystemSettingHandler.GetGeneralSettings)
		systemSettings.PUT("/settings/general", handlers.SystemSettingHandler.UpdateGeneralSettings)

		// SEO Settings endpoints (UPDATE only - GET is public)
		systemSettings.PUT("/settings/seo", handlers.SystemSettingHandler.UpdateSEOSettings)

		// siteImage Settings endpoints
		systemSettings.GET("/settings/site-image", handlers.SystemSettingHandler.GetSiteImageSettings)
		systemSettings.PUT("/settings/site-image", handlers.SystemSettingHandler.UpdateSiteImageSettings)

		// Commission Settings endpoints
		systemSettings.GET("/settings/commission", handlers.SystemSettingHandler.GetCommissionSettings)
		systemSettings.PUT("/settings/commission", handlers.SystemSettingHandler.UpdateCommissionSettings)

		// Referral Settings endpoints
		systemSettings.GET("/settings/referral", handlers.SystemSettingHandler.GetReferralSettings)
		systemSettings.PUT("/settings/referral", handlers.SystemSettingHandler.UpdateReferralSettings)

		// Member Level Settings endpoints
		systemSettings.GET("/settings/member-level", handlers.SystemSettingHandler.GetMemberLevelSettings)
		systemSettings.PUT("/settings/member-level", handlers.SystemSettingHandler.UpdateMemberLevelSettings)

		// File upload endpoints
		systemSettings.POST("/upload", handlers.SystemSettingHandler.FileUpload)
		systemSettings.DELETE("/file", handlers.SystemSettingHandler.DeleteFile)

		// Theme Settings endpoints (admin only - for saving themes)
		systemSettings.POST("/theme", handlers.ThemeSettingHandler.SaveTheme)

		// Options endpoints for dropdowns
		systemSettings.GET("/otp-options", handlers.OTPHandler.GetOTPOptions)
		systemSettings.GET("/player-attempt-limit-options", handlers.SystemSettingHandler.GetPlayerAttemptLimitOptions)
		systemSettings.GET("/timeout-options", handlers.SystemSettingHandler.GetTimeoutOptions)
		systemSettings.GET("/referral-commission-options", handlers.SystemSettingHandler.GetReferralCommissionOptions)

		// FAQ
		systemSettings.POST("/settings/faq", handlers.FAQHandler.CreateFAQ)
		systemSettings.GET("/settings/faq", handlers.FAQHandler.ListFAQs)
		systemSettings.GET("/settings/faq/:id", handlers.FAQHandler.GetFAQ)
		systemSettings.PUT("/settings/faq/:id", handlers.FAQHandler.UpdateFAQ)
		systemSettings.DELETE("/settings/faq/:id", handlers.FAQHandler.DeleteFAQ)
		systemSettings.PATCH("/settings/faq/reorder", handlers.FAQHandler.ReorderFAQs)
	}
}
