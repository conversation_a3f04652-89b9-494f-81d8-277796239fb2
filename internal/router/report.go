package router

import (
	"github.com/gin-gonic/gin"
)

func SetupReportRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Login Reports (Admin access only)
	reports := r.Group("/reports")
	{
		reports.Use(handlers.JwtMiddleware.MiddlewareFunc())

		// Member login reports
		reports.GET("/member-login", handlers.LoginReportHandler.GetMemberLoginReport)
		reports.GET("/member-login/download", handlers.LoginReportHandler.DownloadMemberLoginReport)
		reports.GET("/member-login/duplicate-ips", handlers.LoginReportHandler.GetDuplicateIPReport)

		// Admin login reports
		reports.GET("/admin-login", handlers.LoginReportHandler.GetAdminLoginReport)
		reports.GET("/admin-login/download", handlers.LoginReportHandler.DownloadAdminLoginReport)

		// Filter options for dropdowns
		reports.GET("/filter-options/:type", handlers.LoginReportHandler.GetFilterOptions)

		// Summary reports
		reports.GET("/summary-bet", handlers.SummaryReportHandler.GetSummaryReportsWithFilter)
		reports.GET("/summary-bet/:id", handlers.SummaryReportHandler.GetSummaryReportDetail)

		// Summary bet by member reports
		reports.GET("/summary-bet-by-member", handlers.SummaryBetByMemberHandler.GetSummaryBetByMemberWithFilter)
		reports.GET("/summary-bet-by-member/:member_id", handlers.SummaryBetByMemberHandler.GetSummaryBetByMemberDetail)

		// Referral reports
		reports.GET("/referral/summary", handlers.ReferralReportHandler.GetReferralReportSummary)
		reports.GET("/referral/summary/export", handlers.ReferralReportHandler.ExportReferralReportSummaryCSV)
		reports.GET("/referral/detail/:member_id", handlers.ReferralReportHandler.GetReferralReportDetail)
		reports.GET("/referral/detail/:member_id/export", handlers.ReferralReportHandler.ExportReferralReportDetailCSV)
	}
}
