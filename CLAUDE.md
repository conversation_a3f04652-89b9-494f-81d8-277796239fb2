# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**Blacking API** is a production-ready Go REST API built with clean architecture principles, featuring:
- User and member management systems with JWT authentication
- Banking and financial operations
- Promotion management system with AI integration
- Multi-tier architecture with repository pattern
- PostgreSQL with PGX v5 native driver
- Schema-aware database operations for multi-tenant support

## Architecture

### Directory Structure
```
blacking-api/
├── cmd/                     # Application entry points
│   ├── migration/          # Database migration tool
│   ├── server/             # Main API server
│   ├── worker/             # Background worker
│   └── test-agapi/         # AG API testing tool
├── internal/
│   ├── app/                # Application layer
│   ├── config/             # Configuration management (Viper)
│   ├── domain/             # Business entities (54 domains)
│   ├── handler/http/       # HTTP handlers (REST APIs)
│   ├── helper/             # Helper utilities (encryption, pagination)
│   ├── middleware/         # HTTP middleware (auth, CORS, logging)
│   ├── repository/         # Data access layer (45+ implementations)
│   │   ├── interfaces/     # Repository interfaces
│   │   ├── postgres/       # PostgreSQL implementations (PGX v5)
│   │   └── aws_s3/         # AWS S3 implementations
│   ├── router/             # Route definitions (27 modular files)
│   ├── service/            # Business logic services
│   └── storage/            # File storage (local, S3)
├── infrastructure/
│   └── database/           # Database connection (PGX pool)
├── migrations/             # Database migrations (Go-based)
│   ├── list/              # Migration files (23 migrations)
│   └── seeders/           # Database seeders
├── pkg/                    # Shared utilities
│   ├── auth/               # Authentication utilities
│   ├── dbutil/             # Database utilities with schema support
│   ├── errors/             # Custom error types
│   ├── logger/             # Structured logging (Logrus)
│   ├── sms/                # SMS provider utilities
│   ├── jaijaipay/          # JaiJaiPay payment gateway integration
│   ├── agapi/              # AG API client integration
│   └── useragent/          # User agent parsing
├── docs/                   # API documentation (Swagger/OpenAPI)
├── scripts/               # Build and deployment scripts
├── acceptance-test/       # API acceptance tests
├── postman/               # Postman collections
└── test/                  # Test utilities and fixtures
```

### Key Technologies
- **Framework**: Gin HTTP framework
- **Database**: PostgreSQL with PGX v5 native driver (DigitalOcean Managed)
- **Connection Pooling**: pgxpool.Pool with schema-aware operations
- **Authentication**: JWT tokens with gin-jwt middleware (unified for users/members)
- **File Storage**: AWS S3 integration with CloudFront CDN
- **SMS**: ThSMS provider integration
- **Migrations**: Custom Go-based migration system
- **Logging**: Structured JSON logging with Logrus
- **Configuration**: Viper with multi-environment support
- **API Documentation**: Swagger/OpenAPI specifications
- **Validation**: Go Playground validator

## Database Architecture

### Schema-Aware Database Operations
The project uses a sophisticated schema management system that supports multiple PostgreSQL schemas:

```go
// Always use dbutil functions for database operations
dbutil.QueryRowWithSchema(ctx, pool, query, args...)
dbutil.QueryWithSchema(ctx, pool, query, args...)
dbutil.ExecWithSchema(ctx, pool, query, args...)

// For transactions
dbutil.TxExecWithSchema(ctx, tx, query, args...)
dbutil.TxQueryRowWithSchema(ctx, tx, query, args...)
dbutil.TxQueryWithSchema(ctx, tx, query, args...)

// For table name helpers
dbutil.TableName("table_name") // Returns "schema.table_name" or "table_name"
```

### Repository Pattern
All repositories follow this consistent pattern:
```go
type Repository struct {
    pool   *pgxpool.Pool
    logger logger.Logger
}

func NewRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.Repository {
    return &Repository{
        pool:   pool,
        logger: logger,
    }
}
```

### Database Connection
- **Driver**: PGX v5 native PostgreSQL driver
- **Provider**: DigitalOcean Managed PostgreSQL
- **Connection Pooling**: pgxpool.Pool (Max: 25, Min: ~10)
- **Schema Support**: Environment-driven via `DATABASE_SCHEMA`
- **Migration System**: Custom Go-based with dry-run and validation

## Build and Development

### Essential Commands
```bash
# Build applications
make build                   # Build all binaries
go build -o bin/server cmd/server/main.go

# Development
make run ENV=local          # Run server locally
make dev-start              # Start full dev environment
make dev-stop               # Stop development environment

# Database operations
make migrate-up ENV=local   # Run migrations
make migrate-status         # Check migration status
make migrate-create name=table_name  # Create new migration
make migrate-seed           # Run database seeders

# Testing and quality
make test                   # Run all tests
make test-coverage          # Run tests with coverage
make lint                   # Run linter
make fmt                    # Format code
make check                  # Run all quality checks

# Docker operations
make docker-run             # Start with Docker Compose
make docker-build           # Build Docker image
make docker-logs            # View container logs
```

### Environment Configuration
Configuration uses Viper with environment-specific files:
- `local.yaml` - Local development
- `development.yaml` - Development environment
- `staging.yaml` - Staging environment
- `production.yaml` - Production environment

Key environment variables:
```bash
# Database Configuration
DATABASE_HOST=your-db-host
DATABASE_PORT=5432
DATABASE_NAME=your-db-name
DATABASE_USERNAME=your-username
DATABASE_PASSWORD=your-password
DATABASE_SSLMODE=require
DATABASE_SCHEMA=irichdev        # Critical for schema-aware operations

# Server Configuration
SERVER_HOST=localhost
SERVER_PORT=8080
SERVER_MODE=debug

# AWS S3 Configuration
AWS_S3_BUCKET_NAME=blacking-images
AWS_S3_BUCKET_ACCESS_KEY_ID=your-access-key
AWS_S3_BUCKET_SECRET_ACCESS_KEY=your-secret-key
AWS_S3_BUCKET_REGION=ap-southeast-1
AWS_S3_CLOUDFRONT_URL=https://cdn.irich.info

# JaiJaiPay Configuration
JAIJAIPAY_API_KEY=your-api-key
JAIJAIPAY_SECRET_KEY=your-secret-key
JAIJAIPAY_BASE_URL=https://api.jaijaipay.com/api/v1

# Other
JWT_SECRET=your-jwt-secret
LOG_LEVEL=info
```

## Development Guidelines

### Database Operations
- **Always use `dbutil` functions** - Never use direct pool/tx calls
- **Schema awareness** - All queries automatically handle schema prefixes
- **Error handling** - Use `pgx.ErrNoRows` and `*pgconn.PgError`
- **Transactions** - Use `TxExecWithSchema`, `TxQueryRowWithSchema`, `TxQueryWithSchema`

### Repository Development Template
```go
func (r *Repository) Create(ctx context.Context, entity *domain.Entity) error {
    query := `INSERT INTO table_name (...) VALUES (...) RETURNING id`
    err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&entity.ID)
    if err != nil {
        r.logger.WithError(err).Error("failed to create entity")
        return errors.NewDatabaseError("failed to create entity")
    }
    return nil
}

// For transactions
func (r *Repository) TransactionalOperation(ctx context.Context) error {
    tx, err := r.pool.Begin(ctx)
    if err != nil {
        return errors.NewDatabaseError("failed to begin transaction")
    }
    defer tx.Rollback(ctx)

    // Use TxExecWithSchema for schema-aware transaction operations
    _, err = dbutil.TxExecWithSchema(ctx, tx, query, args...)
    if err != nil {
        return errors.NewDatabaseError("failed to execute query")
    }

    return tx.Commit(ctx)
}
```

### Error Handling Patterns
```go
if err != nil {
    if err == pgx.ErrNoRows {
        return nil, errors.NewNotFoundError("entity not found")
    }
    if pgErr, ok := err.(*pgconn.PgError); ok {
        switch pgErr.Code {
        case "23505": // unique_violation
            return errors.NewValidationError("duplicate entry")
        case "23503": // foreign_key_violation
            return errors.NewValidationError("foreign key constraint")
        }
    }
    return errors.NewDatabaseError("database operation failed")
}
```

## Router Architecture

Routes are organized into 27 modular files for maintainability:
- `router.go` - Main router setup
- `member-auth.go` - Member authentication
- `user.go` - User management
- `banking.go` - Banking operations
- `promotion_web.go` - Promotion management
- `payment.go` - Payment processing
- And 21 other specialized route modules

## Recent Major Updates

### PostgreSQL Schema Management (COMPLETED - August 2025)
- **Replaced problematic schema path functions** with fully qualified table names
- **Added schema-aware transaction functions** for all repository operations
- **40+ table names supported** automatically by `processQueryWithSchema()`
- **61 direct transaction calls** converted to use `dbutil.TxXxxWithSchema()`
- **Zero compilation errors** and full backward compatibility

### PGX v5 Migration (COMPLETED - July 2025)
- **45+ repository files** migrated from database/sql to PGX v5
- **20-30% performance improvement** with native PostgreSQL protocol
- **Better connection pooling** and error handling
- **Transaction handling** updated to use context-aware methods

### Multi-Schema Support
- **Environment-driven schema selection** via `DATABASE_SCHEMA`
- **Automatic table name qualification** in all SQL queries
- **Connection pool compatibility** with DigitalOcean/PgBouncer
- **Backward compatibility** maintained for existing code

## Current Project Scale
- **336 Go files** across the entire codebase
- **54 domain entities** with complete business logic
- **45+ PostgreSQL repositories** with schema-aware operations
- **27 modular router files** for organized API endpoints
- **23 database migrations** with comprehensive schema support
- **Production-ready** with zero compilation errors

## Testing and Quality

### Test Commands
```bash
go test ./...                    # Run all unit tests
go test -tags=integration ./...  # Integration tests
make test-race                   # Race condition detection
make benchmark                   # Performance benchmarks
make api-test                    # API endpoint testing (requires running server)
make load-test                   # Load testing with k6
```

### Database Testing
- Use `pool.Ping(ctx)` to test connections
- Test transactions with `pool.Begin(ctx)`
- Verify schema with `SELECT current_schema()`
- All dbutil functions include comprehensive test coverage

## Performance Optimizations

### Connection Pool Settings
```go
poolConfig.MaxConns = 25                    // Maximum connections
poolConfig.MinConns = 10                    // Minimum idle connections
poolConfig.MaxConnLifetime = time.Hour      // 1 hour
poolConfig.MaxConnIdleTime = 30 * time.Minute  // 30 minutes
poolConfig.HealthCheckPeriod = 5 * time.Minute // 5 minutes
```

### PGX v5 Benefits
- 20-30% faster than database/sql
- Lower memory usage with native protocol
- Better connection pooling with pgxpool
- Improved error handling with detailed PostgreSQL error information

## Important Notes

### Database Schema Management
- All database operations are schema-aware
- Use `DATABASE_SCHEMA` environment variable for non-public schemas
- Never use direct `pool.Exec`, `pool.Query`, or `pool.QueryRow` calls
- Always use `dbutil` functions for consistency and schema support
- **CRITICAL**: When adding new tables via migrations, **ALWAYS update the table list in `pkg/dbutil/schema.go`**
  - Add table names to the `tables` slice in `processQueryWithSchema()` function
  - Include both singular and plural forms if used in queries
  - **Failure to do this will cause 500 errors** as dbutil cannot properly qualify table names with schema
  - Example: If you create `blockchain_networks` table, add `"blockchain_networks"` to the tables list
  - The system will automatically prefix queries with the schema name (e.g., `irichdev.blockchain_networks`)

### Migration System
- Go-based migrations with validation and dry-run support
- Automatic timestamp prefixing for migration files
- Support for up/down migrations with rollback capability
- Seeder system for initial data population
- **After creating new migrations**: Register the migration struct in `cmd/migration/main.go` in the `getMigrationsList()` function
- **Migration workflow**:
  1. Create migration file in `migrations/list/XX_migration_name.go`
  2. Register migration in `cmd/migration/main.go` getMigrationsList()
  3. Add new table names to `pkg/dbutil/schema.go` tables list
  4. Run `make migrate-up ENV=local`

### Security and Best Practices
- Password hashing with bcrypt
- Input validation on all endpoints
- SQL injection prevention with prepared statements
- Structured error responses (no sensitive data leakage)
- JWT token-based authentication for users and members

---

**Last Updated**: August 31, 2025  
**Build Status**: ✅ PASSING - All compilation errors resolved  
**Database**: ✅ PGX v5 with schema-aware operations + Extended crypto support  
**Architecture**: ✅ Clean Architecture with dependency injection  
**Current Scale**: 340+ Go files, 55 domains, 47+ repositories, 28 routers, 38 migrations

## Recent Updates

### Crypto 2-Step Deposit System (COMPLETED - August 2025)
- **Complete blockchain integration** with 2-step gasless deposit flow
- **5 blockchain networks supported** (Base Sepolia, Polygon, Ethereum mainnet/testnet)
- **Fixed conversion rates** stored in database (33.5 THB per USDC, configurable)
- **Frontend-driven flow** where frontend executes both steps and reports to backend
- **Credit integration** - automatic member credit update after successful deposit via Agent API
- **Conversion information** included in responses (token amount → THB credit calculation)
- **Extended currency support** - database column extended to support USDT, USDC (4+ characters)
- **Comprehensive APIs** with 11 endpoints for full deposit lifecycle management
- **Schema-aware operations** with proper table registration in dbutil

### Database Schema Improvements (August 31, 2025)
- **Extended currency column** from VARCHAR(3) to VARCHAR(10) for crypto token support
- **Fixed crypto deposit failures** caused by 4-character currency codes (USDT, USDC)
- **Backward compatible** with existing 3-character codes (THB, USD)

### JaiJaiPay Integration Fixes (August 31, 2025)
- **Added ID field** to DepositResponse struct for frontend redirect functionality
- **Resolved compilation errors** in JaiJaiPay service field access
- **Enhanced response structure** to support client-side transaction tracking