package seeders

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type SeedGameProviders struct{}

func (s *SeedGameProviders) GetName() string {
	return "SeedGameProviders"
}

func (s *SeedGameProviders) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	log.Printf("Seeder: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Check if game providers already exist
	var count int
	err = con.QueryRow(ctx, "SELECT COUNT(*) FROM game_providers").Scan(&count)
	if err != nil {
		panic(fmt.Sprintf("Failed to check existing game providers: %v", err))
	}

	if count > 0 {
		log.Printf("Seeder: Game providers already exist (%d records), skipping seed", count)
		return
	}

	log.Printf("Seeder: Inserting game providers...")

	// Sports providers
	sportsProviders := []map[string]interface{}{
		{"provider_code": "RWB", "provider_name": "RWB Sports", "category": "sports", "company_percentage": 85.00},
		{"provider_code": "SABA_SPORTS", "provider_name": "Saba Sports", "category": "sports", "company_percentage": 89.00},
		{"provider_code": "SBOBET", "provider_name": "SBOBET", "category": "sports", "company_percentage": 88.50},
	}

	// Cards providers
	cardsProviders := []map[string]interface{}{
		{"provider_code": "BG_CASINO", "provider_name": "BG Casino", "category": "cards", "company_percentage": 92.00},
		{"provider_code": "DREAM_GAMING", "provider_name": "Dream Gaming", "category": "cards", "company_percentage": 92.00},
		{"provider_code": "HRG", "provider_name": "HRG Casino", "category": "cards", "company_percentage": 85.00},
		{"provider_code": "MLIVE", "provider_name": "MLive Casino", "category": "cards", "company_percentage": 85.00},
		{"provider_code": "SA_CASINO", "provider_name": "SA Gaming", "category": "cards", "company_percentage": 92.50},
		{"provider_code": "SEXY_GAMING", "provider_name": "Sexy Gaming", "category": "cards", "company_percentage": 92.00},
		{"provider_code": "SVCASINO", "provider_name": "SV Casino", "category": "cards", "company_percentage": 85.00},
		{"provider_code": "WINFINITY", "provider_name": "Winfinity Casino", "category": "cards", "company_percentage": 85.00},
		{"provider_code": "WM_CASINO", "provider_name": "WM Casino", "category": "cards", "company_percentage": 93.00},
	}

	// Slots providers
	slotsProviders := []map[string]interface{}{
		{"provider_code": "ACE_WIN", "provider_name": "Ace Win", "category": "slots", "company_percentage": 93.00},
		{"provider_code": "ADVANTPLAY", "provider_name": "ADVANTPLAY", "category": "slots", "company_percentage": 92.00},
		{"provider_code": "AMEBA", "provider_name": "AMEBA Gaming", "category": "slots", "company_percentage": 92.00},
		{"provider_code": "BNG", "provider_name": "BNG Gaming", "category": "slots", "company_percentage": 85.00},
		{"provider_code": "BTG", "provider_name": "Big Time Gaming", "category": "slots", "company_percentage": 85.00},
		{"provider_code": "EVOPLAY", "provider_name": "EVOPLAY", "category": "slots", "company_percentage": 90.00},
		{"provider_code": "FACHAI", "provider_name": "FACHAI Gaming", "category": "slots", "company_percentage": 88.00},
		{"provider_code": "HACKSAW", "provider_name": "Hacksaw Gaming", "category": "slots", "company_percentage": 92.00},
		{"provider_code": "IDG", "provider_name": "IDG Gaming", "category": "slots", "company_percentage": 85.00},
		{"provider_code": "JILI", "provider_name": "JILI JILI", "category": "slots", "company_percentage": 92.50},
		{"provider_code": "JOKER", "provider_name": "Joker Gaming", "category": "slots", "company_percentage": 92.50},
		{"provider_code": "KAGAMING", "provider_name": "KA Gaming", "category": "slots", "company_percentage": 90.00},
		{"provider_code": "KINGMAKER", "provider_name": "King Maker", "category": "slots", "company_percentage": 88.00},
		{"provider_code": "LADYLUCK", "provider_name": "Lady Luck", "category": "slots", "company_percentage": 85.00},
		{"provider_code": "MGGAMING", "provider_name": "MG Gaming", "category": "slots", "company_percentage": 92.00},
		{"provider_code": "MIMI_NETENT", "provider_name": "MIMI NETENT", "category": "slots", "company_percentage": 85.00},
		{"provider_code": "NOLIMIT_CITY", "provider_name": "Nolimit City", "category": "slots", "company_percentage": 90.00},
		{"provider_code": "OC_TOPLAY", "provider_name": "OC TOPLAY", "category": "slots", "company_percentage": 91.50},
		{"provider_code": "ONE_GAME", "provider_name": "One Game", "category": "slots", "company_percentage": 85.00},
		{"provider_code": "PGSOFT", "provider_name": "PG Soft", "category": "slots", "company_percentage": 93.00},
		{"provider_code": "PLAY8_GAMING", "provider_name": "PLAY8 GAMING", "category": "slots", "company_percentage": 85.00},
		{"provider_code": "PLAYNGO", "provider_name": "Play'n GO", "category": "slots", "company_percentage": 90.00},
		{"provider_code": "PLAYSTAR", "provider_name": "PlayStar", "category": "slots", "company_percentage": 87.50},
	}

	// Combine all providers
	allProviders := append(append(sportsProviders, cardsProviders...), slotsProviders...)

	// Insert all providers
	for _, provider := range allProviders {
		query := `
			INSERT INTO game_providers (provider_code, provider_name, category, company_percentage, status, created_at, updated_at)
			VALUES ($1, $2, $3, $4, 'active', NOW(), NOW())
		`

		_, err := con.Exec(ctx, query,
			provider["provider_code"],
			provider["provider_name"],
			provider["category"],
			provider["company_percentage"],
		)
		if err != nil {
			panic(fmt.Sprintf("Failed to insert game provider %s: %v", provider["provider_code"], err))
		}

		log.Printf("Seeder: Inserted game provider: %s (%s)", provider["provider_name"], provider["provider_code"])
	}

	log.Printf("Seeder: Successfully inserted %d game providers", len(allProviders))
}

func (s *SeedGameProviders) Down(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Delete all seeded game providers
	query := `DELETE FROM game_providers WHERE provider_code IN (
		'RWB', 'SABA_SPORTS', 'SBOBET',
		'BG_CASINO', 'DREAM_GAMING', 'HRG', 'MLIVE', 'SA_CASINO', 'SEXY_GAMING', 'SVCASINO', 'WINFINITY', 'WM_CASINO',
		'ACE_WIN', 'ADVANTPLAY', 'AMEBA', 'BNG', 'BTG', 'EVOPLAY', 'FACHAI', 'HACKSAW', 'IDG', 'JILI', 'JOKER',
		'KAGAMING', 'KINGMAKER', 'LADYLUCK', 'MGGAMING', 'MIMI_NETENT', 'NOLIMIT_CITY', 'OC_TOPLAY', 'ONE_GAME',
		'PGSOFT', 'PLAY8_GAMING', 'PLAYNGO', 'PLAYSTAR'
	)`

	_, err = con.Exec(ctx, query)
	if err != nil {
		panic(fmt.Sprintf("Failed to delete seeded game providers: %v", err))
	}

	log.Printf("Seeder: Successfully removed seeded game providers")
}
