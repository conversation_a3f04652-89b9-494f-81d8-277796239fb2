package seeders

import (
	"context"
	"os"

	"github.com/jackc/pgx/v5"
	"github.com/shopspring/decimal"
)

type SeedCompanyAgent struct{}

func (s *SeedCompanyAgent) GetName() string {
	return "SeedCompanyAgent"
}

func (s *SeedCompanyAgent) Up(con pgx.Tx) {
	ctx := context.Background()
	schema := os.Getenv("DATABASE_SCHEMA")
	if schema == "" {
		schema = "public"
	}

	// Set search path for schema
	_, err := con.Exec(ctx, "SET search_path TO "+schema)
	if err != nil {
		panic(err)
	}

	// Check if company agent already exists
	var count int
	err = con.QueryRow(ctx, `SELECT COUNT(*) FROM agents WHERE line_code = 'bk1'`).Scan(&count)
	if err != nil {
		panic(err)
	}

	// Skip if company agent already exists
	if count > 0 {
		return
	}

	// Insert company agent with line code bk1 and unlimited credit
	query := `
		INSERT INTO agents (
			line_code, 
			parent_code, 
			username, 
			password, 
			full_name, 
			email, 
			phone, 
			agent_type, 
			commission_percent, 
			commission_remaining, 
			credit_limit, 
			current_credit, 
			status, 
			level_depth,
			created_at,
			updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, NOW(), NOW()
		)
	`

	// Company agent data
	lineCode := "bk1"
	username := "company"
	password := "$2a$10$UOXhQH3rXKF5K5K5K5K5K5Ku4.aKLvQ2P1vQ2P1vQ2P1vQ2P1vQ2O" // bcrypt hash for "company123"
	fullName := "Company Root Agent"
	email := "<EMAIL>"
	phone := "+66000000000"
	agentType := "Company"
	commissionPercent := decimal.NewFromFloat(100.0)
	commissionRemaining := decimal.NewFromFloat(100.0)
	creditLimit := decimal.NewFromFloat(*********.99) // Unlimited credit (very high limit)
	currentCredit := decimal.NewFromFloat(0.0)
	status := "active"
	levelDepth := 0

	_, err = con.Exec(ctx, query,
		lineCode,            // $1
		nil,                 // $2 - parent_code (null for company)
		username,            // $3
		password,            // $4
		fullName,            // $5
		email,               // $6
		phone,               // $7
		agentType,           // $8
		commissionPercent,   // $9
		commissionRemaining, // $10
		creditLimit,         // $11
		currentCredit,       // $12
		status,              // $13
		levelDepth,          // $14
	)

	if err != nil {
		panic(err)
	}

	// Create hierarchy record for company agent (root level)
	hierarchyQuery := `
		INSERT INTO agent_hierarchy (
			agent_code,
			ancestor_code,
			path,
			depth_level,
			created_at,
			updated_at
		) VALUES (
			$1, $2, $3, $4, NOW(), NOW()
		)
	`

	// Company agent hierarchy record (self-reference with depth 0)
	_, err = con.Exec(ctx, hierarchyQuery,
		lineCode, // $1 - agent_code
		lineCode, // $2 - ancestor_code (self for root)
		lineCode, // $3 - path (just the line code for root)
		0,        // $4 - depth_level (0 for self-reference)
	)

	if err != nil {
		panic(err)
	}
}

func (s *SeedCompanyAgent) Down(con pgx.Tx) {
	ctx := context.Background()
	schema := os.Getenv("DATABASE_SCHEMA")
	if schema == "" {
		schema = "public"
	}

	// Set search path for schema
	_, err := con.Exec(ctx, "SET search_path TO "+schema)
	if err != nil {
		panic(err)
	}

	// Remove hierarchy records first (due to foreign key constraints)
	_, err = con.Exec(ctx, `DELETE FROM agent_hierarchy WHERE agent_code = 'bk1'`)
	if err != nil {
		panic(err)
	}

	// Remove company agent
	_, err = con.Exec(ctx, `DELETE FROM agents WHERE line_code = 'bk1'`)
	if err != nil {
		panic(err)
	}
}
