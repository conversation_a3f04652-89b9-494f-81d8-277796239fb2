package seeders

import (
	"context"
	"fmt"
	"os"

	"github.com/jackc/pgx/v5"
)

type SeedReferralGroups struct{}

func (s *SeedReferralGroups) GetName() string {
	return "SeedReferralGroups"
}

func (s *SeedReferralGroups) Up(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment
	schemaName := "public"
	if envSchema := os.Getenv("DATABASE_SCHEMA"); envSchema != "" {
		schemaName = envSchema
	}

	// Set search path to use the correct schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Errorf("failed to set search_path: %w", err))
	}

	// Insert default referral groups
	_, err = con.Exec(ctx, `
		INSERT INTO referral_groups (
			id,
			is_default,
			name,
			turnover_sports,
			turnover_casino,
			turnover_fishing,
			turnover_slot,
			turnover_lottery,
			turnover_card,
			status,
			created_at,
			updated_at
		)
		VALUES
		(1, true, 'default', 75.50, 80.00, 70.25, 85.00, 60.00, 78.50, 'active', '2025-07-29 16:07:48.791235+00', '2025-07-29 16:07:48.791235+00')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Reset sequence to continue from the highest ID
	_, err = con.Exec(ctx, `
		SELECT setval('referral_groups_id_seq', (SELECT MAX(id) FROM referral_groups))
	`)
	if err != nil {
		panic(err)
	}
}

func (s *SeedReferralGroups) Down(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment
	schemaName := "public"
	if envSchema := os.Getenv("DATABASE_SCHEMA"); envSchema != "" {
		schemaName = envSchema
	}

	// Set search path to use the correct schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Errorf("failed to set search_path: %w", err))
	}

	// Delete referral groups
	_, err = con.Exec(ctx, `DELETE FROM referral_groups WHERE name = 'default'`)
	if err != nil {
		panic(err)
	}
}
