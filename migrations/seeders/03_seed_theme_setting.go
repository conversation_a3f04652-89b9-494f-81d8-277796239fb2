package seeders

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/jackc/pgx/v5"
)

type SeedThemeSetting struct{}

func (s *SeedThemeSetting) GetName() string {
	return "SeedThemeSetting"
}

func (s *SeedThemeSetting) Up(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment
	schemaName := "public"
	if envSchema := os.Getenv("DATABASE_SCHEMA"); envSchema != "" {
		schemaName = envSchema
	}

	// Set search path to use the correct schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Errorf("failed to set search_path: %w", err))
	}

	log.Printf("Seeder: Starting theme_setting seeding...")

	// Insert theme setting data
	query := `
		INSERT INTO theme_setting (id, theme_value, updated_at, updated_by)
		VALUES ($1, $2, $3, $4)
		ON CONFLICT (id) DO UPDATE SET
			theme_value = EXCLUDED.theme_value,
			updated_at = EXCLUDED.updated_at,
			updated_by = EXCLUDED.updated_by
	`

	now := time.Now()
	_, err = con.Exec(ctx, query, 1, "theme-a", now, 1)
	if err != nil {
		panic(fmt.Errorf("failed to insert theme_setting data: %w", err))
	}

	// Reset sequence to continue from the highest ID
	_, err = con.Exec(ctx, `
		SELECT setval('theme_setting_id_seq', (SELECT COALESCE(MAX(id), 1) FROM theme_setting))
	`)
	if err != nil {
		log.Printf("Warning: failed to reset sequence (table might not have sequence): %v", err)
	}

	log.Printf("Seeder: Theme setting seeding completed successfully!")
}

func (s *SeedThemeSetting) Down(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment
	schemaName := "public"
	if envSchema := os.Getenv("DATABASE_SCHEMA"); envSchema != "" {
		schemaName = envSchema
	}

	// Set search path to use the correct schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Errorf("failed to set search_path: %w", err))
	}

	log.Printf("Seeder: Rolling back theme_setting seeding...")

	// Delete the theme setting data
	_, err = con.Exec(ctx, `DELETE FROM theme_setting WHERE id = 1`)
	if err != nil {
		panic(fmt.Errorf("failed to delete theme_setting data: %w", err))
	}

	log.Printf("Seeder: Theme setting rollback completed successfully!")
}
