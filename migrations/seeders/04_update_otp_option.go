package seeders

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type UpdateOTPOption struct{}

func (s *UpdateOTPOption) GetName() string {
	return "UpdateOTPOption"
}

func (s *UpdateOTPOption) Up(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment
	schemaName := "public"
	if envSchema := os.Getenv("DATABASE_SCHEMA"); envSchema != "" {
		schemaName = envSchema
	}

	// Set search path to use the correct schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Errorf("failed to set search_path: %w", err))
	}

	log.Printf("Seeder: Updating OTP option from 'sms' to 'none'...")

	// Update otp_option from 'sms' to 'none'
	query := `
		UPDATE system_settings 
		SET value = $1, updated_at = NOW()
		WHERE key = $2 AND value = $3
	`

	result, err := con.Exec(ctx, query, "none", "otp_option", "sms")
	if err != nil {
		panic(fmt.Errorf("failed to update otp_option: %w", err))
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected > 0 {
		log.Printf("Seeder: Successfully updated otp_option to 'none' (%d rows affected)", rowsAffected)
	} else {
		log.Printf("Seeder: No rows updated - otp_option may already be 'none' or not exist")
	}

	log.Printf("Seeder: OTP option update completed successfully!")
}

func (s *UpdateOTPOption) Down(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment
	schemaName := "public"
	if envSchema := os.Getenv("DATABASE_SCHEMA"); envSchema != "" {
		schemaName = envSchema
	}

	// Set search path to use the correct schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Errorf("failed to set search_path: %w", err))
	}

	log.Printf("Seeder: Rolling back OTP option from 'none' to 'sms'...")

	// Rollback otp_option from 'none' to 'sms'
	query := `
		UPDATE system_settings 
		SET value = $1, updated_at = NOW()
		WHERE key = $2 AND value = $3
	`

	result, err := con.Exec(ctx, query, "sms", "otp_option", "none")
	if err != nil {
		panic(fmt.Errorf("failed to rollback otp_option: %w", err))
	}

	rowsAffected := result.RowsAffected()
	log.Printf("Seeder: Rollback completed - otp_option set back to 'sms' (%d rows affected)", rowsAffected)
}
