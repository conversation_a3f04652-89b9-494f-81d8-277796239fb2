package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type CreateJaiJaiPayAPILogs struct{}

func (m *CreateJaiJaiPayAPILogs) GetName() string {
	return "CreateJaiJaiPayAPILogs"
}

func (m *CreateJaiJaiPayAPILogs) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Create jaijaipay_api_logs table
	query := fmt.Sprintf(`
		CREATE TABLE %s.jaijaipay_api_logs (
			id SERIAL PRIMARY KEY,
			request_id VARCHAR(255) NOT NULL UNIQUE,
			api_category VARCHAR(50) NOT NULL,
			method VARCHAR(10) NOT NULL,
			endpoint VARCHAR(255) NOT NULL,
			request_body JSONB,
			request_headers JSONB,
			response_status_code INTEGER,
			response_body JSONB,
			response_time_ms INTEGER,
			success BOOLEAN DEFAULT false,
			error_message TEXT,
			
			-- Business context (flexible JSONB for different API types)
			business_data JSONB,
			
			-- Common fields
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
	`, schemaName)

	if _, err := con.Exec(ctx, query); err != nil {
		log.Fatalf("Error creating jaijaipay_api_logs table: %v", err)
	}

	// Create indexes for better performance
	indexes := []string{
		fmt.Sprintf("CREATE INDEX idx_jaijaipay_api_logs_request_id ON %s.jaijaipay_api_logs(request_id);", schemaName),
		fmt.Sprintf("CREATE INDEX idx_jaijaipay_api_logs_api_category ON %s.jaijaipay_api_logs(api_category);", schemaName),
		fmt.Sprintf("CREATE INDEX idx_jaijaipay_api_logs_created_at ON %s.jaijaipay_api_logs(created_at);", schemaName),
		fmt.Sprintf("CREATE INDEX idx_jaijaipay_api_logs_success ON %s.jaijaipay_api_logs(success);", schemaName),
		fmt.Sprintf("CREATE INDEX idx_jaijaipay_api_logs_business_data_gin ON %s.jaijaipay_api_logs USING gin(business_data);", schemaName),
	}

	for _, indexQuery := range indexes {
		if _, err := con.Exec(ctx, indexQuery); err != nil {
			log.Fatalf("Error creating index: %v", err)
		}
	}

	fmt.Println("✅ Successfully created jaijaipay_api_logs table with indexes")
}

func (m *CreateJaiJaiPayAPILogs) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	query := fmt.Sprintf("DROP TABLE IF EXISTS %s.jaijaipay_api_logs CASCADE;", schemaName)
	if _, err := con.Exec(ctx, query); err != nil {
		log.Fatalf("Error dropping jaijaipay_api_logs table: %v", err)
	}

	fmt.Println("✅ Successfully dropped jaijaipay_api_logs table")
}
