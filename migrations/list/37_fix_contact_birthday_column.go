package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type FixContactBirthdayColumn struct{}

func (m *FixContactBirthdayColumn) GetName() string {
	return "37_fix_contact_birthday_column"
}

func (m *FixContactBirthdayColumn) Up(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Rename the column from 'brithday' to 'birthday' to fix the typo
	_, err = con.Exec(ctx, `
		ALTER TABLE contact 
		RENAME COLUMN brithday TO birthday
	`)
	if err != nil {
		// If the column is already renamed, just continue
		log.Printf("Note: Column might already be renamed or not exist: %v", err)

		// Check if birthday column already exists
		var columnExists bool
		checkQuery := `
			SELECT EXISTS (
				SELECT 1 
				FROM information_schema.columns 
				WHERE table_schema = $1 
				AND table_name = 'contact' 
				AND column_name = 'birthday'
			)
		`
		err = con.QueryRow(ctx, checkQuery, schemaName).Scan(&columnExists)
		if err != nil {
			panic(fmt.Sprintf("Failed to check if birthday column exists: %v", err))
		}

		if !columnExists {
			// If birthday doesn't exist, add it
			_, err = con.Exec(ctx, `
				ALTER TABLE contact 
				ADD COLUMN IF NOT EXISTS birthday BOOLEAN NOT NULL DEFAULT FALSE
			`)
			if err != nil {
				panic(fmt.Sprintf("Failed to add birthday column: %v", err))
			}
			log.Printf("Migration: Added birthday column to contact table")
		} else {
			log.Printf("Migration: birthday column already exists")
		}
	} else {
		log.Printf("Migration: Renamed column from brithday to birthday in contact table")
	}

	log.Println("Migration 37_fix_contact_birthday_column completed successfully")
}

func (m *FixContactBirthdayColumn) Down(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Rename the column back from 'birthday' to 'brithday' (restore the typo)
	_, err = con.Exec(ctx, `
		ALTER TABLE contact 
		RENAME COLUMN birthday TO brithday
	`)
	if err != nil {
		log.Printf("Warning: Failed to rename column back to brithday: %v", err)
	} else {
		log.Printf("Migration: Renamed column from birthday back to brithday in contact table")
	}

	log.Println("Migration rollback 37_fix_contact_birthday_column completed")
}
