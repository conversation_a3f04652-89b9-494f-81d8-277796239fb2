package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type CreateAdminAuditLog struct{}

func (m *CreateAdminAuditLog) GetName() string {
	return "CreateAdminAuditLog"
}

func (m *CreateAdminAuditLog) Up(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("failed to set search_path: %v", err))
	}

	// Create admin_audit_log table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS admin_audit_log (
			id SERIAL PRIMARY KEY,
			user_id INTEGER NOT NULL,
			username VARCHAR(255) NOT NULL,
			method VARCHAR(10) NOT NULL,
			path VARCHAR(500) NOT NULL,
			request_body TEXT,
			response_status INTEGER,
			ip_address INET,
			user_agent TEXT,
			created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("failed to create admin_audit_log table: %v", err))
	}

	// Create indexes separately
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_admin_audit_log_user_id ON admin_audit_log (user_id)",
		"CREATE INDEX IF NOT EXISTS idx_admin_audit_log_username ON admin_audit_log (username)",
		"CREATE INDEX IF NOT EXISTS idx_admin_audit_log_path ON admin_audit_log (path)",
		"CREATE INDEX IF NOT EXISTS idx_admin_audit_log_created_at ON admin_audit_log (created_at)",
		"CREATE INDEX IF NOT EXISTS idx_admin_audit_log_method ON admin_audit_log (method)",
	}

	for _, indexSQL := range indexes {
		_, err = con.Exec(ctx, indexSQL)
		if err != nil {
			panic(fmt.Sprintf("failed to create index: %v", err))
		}
	}

	log.Println("✅ Created admin_audit_log table successfully")
}

func (m *CreateAdminAuditLog) Down(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	log.Printf("Migration Down: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("failed to set search_path: %v", err))
	}

	// Drop admin_audit_log table
	_, err = con.Exec(ctx, "DROP TABLE IF EXISTS admin_audit_log")
	if err != nil {
		panic(fmt.Sprintf("failed to drop admin_audit_log table: %v", err))
	}

	log.Println("✅ Dropped admin_audit_log table successfully")
}
