package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type FixBugMemberGroup struct{}

func (m *FixBugMemberGroup) GetName() string {
	return "FixBugMemberGroup"
}

func (m *FixBugMemberGroup) Up(con pgx.Tx) {
	ctx := context.Background()

	// schema
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}
	log.Printf("Migration(FixBugMemberGroup): Using schema '%s'", schemaName)

	// set search_path
	if _, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName)); err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}
	log.Printf("Migration(FixBugMemberGroup): search_path set")

	// ===== add columns: upgrade_enable, downgrade_enable =====
	if _, err := con.Exec(ctx, `
		ALTER TABLE member_group_types 
		ADD COLUMN IF NOT EXISTS upgrade_enable BOOLEAN NOT NULL DEFAULT true;
	`); err != nil {
		panic(fmt.Sprintf("Failed to add upgrade_enable: %v", err))
	}

	if _, err := con.Exec(ctx, `
		ALTER TABLE member_group_types 
		ADD COLUMN IF NOT EXISTS downgrade_enable BOOLEAN NOT NULL DEFAULT true;
	`); err != nil {
		panic(fmt.Sprintf("Failed to add downgrade_enable: %v", err))
	}

	// ===== reset calculate_min_deposit by drop & add (DECIMAL(15,2) NOT NULL DEFAULT 0.00) =====
	// ⚠️ จะทำให้ค่าที่เคยอยู่ในคอลัมน์นี้หายไปทั้งหมด
	if _, err := con.Exec(ctx, `
		ALTER TABLE member_groups 
		DROP COLUMN IF EXISTS calculate_min_deposit;
	`); err != nil {
		panic(fmt.Sprintf("Failed to drop calculate_min_deposit: %v", err))
	}

	if _, err := con.Exec(ctx, `
		ALTER TABLE member_groups 
		ADD COLUMN calculate_min_deposit DECIMAL(15, 2) NOT NULL DEFAULT 0.00;
	`); err != nil {
		panic(fmt.Sprintf("Failed to add calculate_min_deposit as DECIMAL(15,2): %v", err))
	}

	log.Printf("Migration(FixBugMemberGroup): Up done")
}

func (m *FixBugMemberGroup) Down(con pgx.Tx) {
	ctx := context.Background()

	// schema
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}
	if _, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName)); err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// ===== remove upgrade_enable, downgrade_enable =====
	if _, err := con.Exec(ctx, `
		ALTER TABLE member_group_types 
		DROP COLUMN IF EXISTS upgrade_enable;
	`); err != nil {
		panic(fmt.Sprintf("Failed to drop upgrade_enable: %v", err))
	}

	if _, err := con.Exec(ctx, `
		ALTER TABLE member_group_types 
		DROP COLUMN IF EXISTS downgrade_enable;
	`); err != nil {
		panic(fmt.Sprintf("Failed to drop downgrade_enable: %v", err))
	}

	// ===== revert calculate_min_deposit to BOOLEAN by drop & add =====
	// เลือกให้เป็น NOT NULL DEFAULT false (ถ้าอยากให้ nullable ก็เปลี่ยนบรรทัดล่างนี้ได้)
	if _, err := con.Exec(ctx, `
		ALTER TABLE member_groups 
		DROP COLUMN IF EXISTS calculate_min_deposit;
	`); err != nil {
		panic(fmt.Sprintf("Failed to drop calculate_min_deposit: %v", err))
	}

	if _, err := con.Exec(ctx, `
		ALTER TABLE member_groups 
		ADD COLUMN calculate_min_deposit BOOLEAN NOT NULL DEFAULT false;
	`); err != nil {
		panic(fmt.Sprintf("Failed to add calculate_min_deposit as BOOLEAN: %v", err))
	}

	log.Printf("Migration(FixBugMemberGroup): Down done")
}
