package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type CreateBankTransactionSlipTable struct{}

func (m *CreateBankTransactionSlipTable) GetName() string {
	return "17_create_bank_transaction_slip_table"
}

func (m *CreateBankTransactionSlipTable) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schema := os.Getenv("DATABASE_SCHEMA")
	if schema == "" {
		schema = "public"
	}

	queries := []string{
		// Set search path
		fmt.Sprintf("SET search_path TO %s", schema),

		// Create bank_transaction_slip table
		`CREATE TABLE IF NOT EXISTS bank_transaction_slip (
			id BIGSERIAL PRIMARY KEY,
			member_id BIGINT NOT NULL,
			status INT NOT NULL DEFAULT 1,
			transaction_id BIGINT NOT NULL,
			slip_url TEXT,
			raw_qr_code TEXT,
			from_account_number VARCHAR(50),
			from_account_name VARCHAR(255),
			from_bank_name VARCHAR(100),
			to_account_number VARCHAR(50),
			to_account_name VARCHAR(255),
			amount DECIMAL(15,2) NOT NULL,
			transaction_date TIMESTAMP NOT NULL,
			remark TEXT,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP,
			
			-- Foreign key constraints
			CONSTRAINT fk_bank_slip_member 
				FOREIGN KEY (member_id) 
				REFERENCES members(id) 
				ON DELETE CASCADE,
				
			CONSTRAINT fk_bank_slip_transaction 
				FOREIGN KEY (transaction_id) 
				REFERENCES user_transaction(id) 
				ON DELETE CASCADE,
				
			-- Check constraints
			CONSTRAINT chk_bank_slip_status 
				CHECK (status IN (1, 2, 3, 4)),
				
			CONSTRAINT chk_bank_slip_amount 
				CHECK (amount > 0)
		)`,

		// Create indexes for better query performance
		`CREATE INDEX idx_bank_slip_member_id ON bank_transaction_slip(member_id)`,
		`CREATE INDEX idx_bank_slip_status ON bank_transaction_slip(status)`,
		`CREATE INDEX idx_bank_slip_transaction_id ON bank_transaction_slip(transaction_id)`,
		`CREATE INDEX idx_bank_slip_transaction_date ON bank_transaction_slip(transaction_date)`,
		`CREATE INDEX idx_bank_slip_created_at ON bank_transaction_slip(created_at DESC)`,

		// Add composite index for common queries
		`CREATE INDEX idx_bank_slip_member_status ON bank_transaction_slip(member_id, status)`,

		// Add comments for documentation
		`COMMENT ON TABLE bank_transaction_slip IS 'Stores bank transaction slips for member deposits and withdrawals'`,
		`COMMENT ON COLUMN bank_transaction_slip.id IS 'Primary key'`,
		`COMMENT ON COLUMN bank_transaction_slip.member_id IS 'Foreign key to members table'`,
		`COMMENT ON COLUMN bank_transaction_slip.status IS 'Transaction slip status: 1=Pending, 2=Approved, 3=Rejected, 4=Cancelled'`,
		`COMMENT ON COLUMN bank_transaction_slip.transaction_id IS 'Foreign key to user_transaction table'`,
		`COMMENT ON COLUMN bank_transaction_slip.slip_url IS 'URL or path to the transaction slip image/document'`,
		`COMMENT ON COLUMN bank_transaction_slip.raw_qr_code IS 'Raw QR code data if applicable'`,
		`COMMENT ON COLUMN bank_transaction_slip.from_account_number IS 'Source bank account number'`,
		`COMMENT ON COLUMN bank_transaction_slip.from_account_name IS 'Source bank account holder name'`,
		`COMMENT ON COLUMN bank_transaction_slip.from_bank_name IS 'Source bank name'`,
		`COMMENT ON COLUMN bank_transaction_slip.to_account_number IS 'Destination bank account number'`,
		`COMMENT ON COLUMN bank_transaction_slip.to_account_name IS 'Destination bank account holder name'`,
		`COMMENT ON COLUMN bank_transaction_slip.amount IS 'Transaction amount'`,
		`COMMENT ON COLUMN bank_transaction_slip.transaction_date IS 'Date and time of the transaction'`,
		`COMMENT ON COLUMN bank_transaction_slip.remark IS 'Additional remarks or notes'`,
		`COMMENT ON COLUMN bank_transaction_slip.created_at IS 'Record creation timestamp'`,
		`COMMENT ON COLUMN bank_transaction_slip.updated_at IS 'Record last update timestamp'`,
	}

	for _, query := range queries {
		if _, err := con.Exec(ctx, query); err != nil {
			log.Printf("Error executing query: %s\nError: %v", query, err)
			panic(err)
		}
	}

	log.Println("Migration 17: Created bank_transaction_slip table successfully")
}

func (m *CreateBankTransactionSlipTable) Down(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schema := os.Getenv("DATABASE_SCHEMA")
	if schema == "" {
		schema = "public"
	}

	queries := []string{
		// Set search path
		fmt.Sprintf("SET search_path TO %s", schema),

		// Drop indexes
		`DROP INDEX IF EXISTS idx_bank_slip_member_status`,
		`DROP INDEX IF EXISTS idx_bank_slip_created_at`,
		`DROP INDEX IF EXISTS idx_bank_slip_transaction_date`,
		`DROP INDEX IF EXISTS idx_bank_slip_transaction_id`,
		`DROP INDEX IF EXISTS idx_bank_slip_status`,
		`DROP INDEX IF EXISTS idx_bank_slip_member_id`,

		// Drop table
		`DROP TABLE IF EXISTS bank_transaction_slip`,
	}

	for _, query := range queries {
		if _, err := con.Exec(ctx, query); err != nil {
			log.Printf("Error executing rollback query: %s\nError: %v", query, err)
			panic(err)
		}
	}

	log.Println("Migration 17: Rolled back bank_transaction_slip table successfully")
}
