package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type CreateUserTransactionStatus struct{}

func (m *CreateUserTransactionStatus) GetName() string {
	return "CreateUserTransactionStatus"
}

func (m *CreateUserTransactionStatus) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Creating user transaction status table")

	// Create user_transaction_status table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS user_transaction_status (
			id INTEGER PRIMARY KEY,
			name VARCHAR(100) NOT NULL,
			detail VARCHAR(500) NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert transaction statuses
	log.Printf("Migration: Inserting user transaction status data")
	_, err = con.Exec(ctx, `
		INSERT INTO user_transaction_status (id, name, detail, created_at) VALUES
		(1, 'WEBSITE_DEPOSIT', 'ฝากเว็บ', '2025-08-02 09:00:00'),
		(2, 'WAITING_FOR_TRANSFER', 'รอโอน', '2025-08-02 09:00:00'),
		(3, 'WAITING_FOR_VERIFICATION', 'รอตรวจสอบ', '2025-08-02 09:00:00'),
		(4, 'SLIP_PENDING', 'สลิปรอดำเนินการ', '2025-08-02 09:00:00'),
		(5, 'IN_PROGRESS', 'กำลังดำเนินการ', '2025-08-02 09:00:00'),
		(6, 'OVER_LIMIT', 'เกินวงเงิน', '2025-08-02 09:00:00'),
		(7, 'VIP_APPROVED', 'อนุมัติ VIP', '2025-08-02 09:00:00'),
		(8, 'WAITING_FOR_MATCHING', 'รอจับคู่', '2025-08-02 09:00:00'),
		(9, 'IN_QUEUE', 'รอคิว', '2025-08-02 09:00:00'),
		(10, 'DUPLICATE', 'ทำซ้ำ', '2025-08-02 09:00:00'),
		(11, 'SUCCESS', 'สำเร็จ', '2025-08-02 09:00:00'),
		(12, 'CANCELLED', 'ยกเลิก', '2025-08-02 09:00:00')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to insert user transaction status data: %v", err))
	}

	log.Printf("Migration: User transaction status data inserted successfully")
}

func (m *CreateUserTransactionStatus) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	// Drop table
	_, err = con.Exec(ctx, "DROP TABLE IF EXISTS user_transaction_status")
	if err != nil {
		panic(err)
	}
}
