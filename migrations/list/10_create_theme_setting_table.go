package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type CreateThemeSettingTable struct{}

func (m *CreateThemeSettingTable) GetName() string {
	return "CreateThemeSettingTable"
}

func (m *CreateThemeSettingTable) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public" // default schema
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	log.Printf("Migration: Setting search_path to '%s'", schemaName)
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}
	log.Printf("Migration: search_path set successfully")

	// Verify current schema
	var currentSchema string
	err = con.QueryRow(ctx, "SELECT current_schema()").Scan(&currentSchema)
	if err == nil {
		log.Printf("Migration: Current schema is '%s'", currentSchema)
	}

	// Create theme_setting table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS theme_setting (
			id SERIAL PRIMARY KEY,
			theme_value VARCHAR(255) NOT NULL,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_by INT NOT NULL,
			FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE RESTRICT
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create theme_setting table: %v", err))
	}

	log.Printf("Migration: theme_setting table created successfully")

	// Create trigger to automatically update updated_at column
	_, err = con.Exec(ctx, `
		CREATE OR REPLACE FUNCTION update_theme_setting_updated_at()
		RETURNS TRIGGER AS $$
		BEGIN
			NEW.updated_at = CURRENT_TIMESTAMP;
			RETURN NEW;
		END;
		$$ language 'plpgsql';
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create update function: %v", err))
	}

	_, err = con.Exec(ctx, `
		DROP TRIGGER IF EXISTS update_theme_setting_updated_at_trigger ON theme_setting;
		CREATE TRIGGER update_theme_setting_updated_at_trigger
		BEFORE UPDATE ON theme_setting
		FOR EACH ROW
		EXECUTE FUNCTION update_theme_setting_updated_at();
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create trigger: %v", err))
	}

	log.Printf("Migration: theme_setting triggers created successfully")
}

func (m *CreateThemeSettingTable) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	// Drop trigger and function
	_, err = con.Exec(ctx, `DROP TRIGGER IF EXISTS update_theme_setting_updated_at_trigger ON theme_setting`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop trigger: %v", err))
	}

	_, err = con.Exec(ctx, `DROP FUNCTION IF EXISTS update_theme_setting_updated_at()`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop function: %v", err))
	}

	// Drop table
	_, err = con.Exec(ctx, `DROP TABLE IF EXISTS theme_setting`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop theme_setting table: %v", err))
	}

	log.Printf("Migration: theme_setting table and triggers dropped successfully")
}
