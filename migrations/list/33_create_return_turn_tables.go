package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type CreateReturnTurns struct{}

func (m *CreateReturnTurns) GetName() string {
	return "27_create_return_turn_tables"
}

func (m *CreateReturnTurns) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Creating return turns")

	// Create return_turn_setting table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS return_turn_setting (
			id BIGSERIAL PRIMARY KEY,
			return_percent DECIMAL(10,2) DEFAULT 0.00,
			return_type_id BIGINT DEFAULT 1,
			cut_type_id BIGINT DEFAULT 1,
			min_loss_price DECIMAL(10,2) DEFAULT 0.00,
			max_return_price DECIMAL(10,2) DEFAULT 0.00,
			detail TEXT,
			is_enabled BOOLEAN DEFAULT false,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create return_turn_setting table: %v", err))
	}
	log.Println("Created return_turn_setting table successfully")

	// Create return_turn_loser table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS return_turn_loser (
			id BIGSERIAL PRIMARY KEY,
			member_id BIGINT NOT NULL,
			status_id BIGINT DEFAULT 1,
			daily_key VARCHAR(255) UNIQUE,
			of_date DATE NOT NULL,
			total_loss_amount DECIMAL(10,2) DEFAULT 0.00,
			total_loss_live_casino DECIMAL(10,2) DEFAULT 0.00,
			total_loss_slot DECIMAL(10,2) DEFAULT 0.00,
			total_loss_sport DECIMAL(10,2) DEFAULT 0.00,
			return_percent DECIMAL(10,2) DEFAULT 0.00,
			game_detail VARCHAR(50),
			return_type_id BIGINT DEFAULT 1,
			cut_type_id BIGINT DEFAULT 1,
			min_loss_price DECIMAL(10,2) DEFAULT 0.00,
			max_return_price DECIMAL(10,2) DEFAULT 0.00,
			return_price DECIMAL(10,2) DEFAULT 0.00,
			calc_at TIMESTAMP,
			take_at TIMESTAMP,
			taken_price DECIMAL(10,2) DEFAULT 0.00,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create return_turn_loser table: %v", err))
	}
	log.Println("Created return_turn_loser table successfully")

	// Create indexes
	_, err = con.Exec(ctx, `
		CREATE INDEX idx_return_turn_loser_status_id ON return_turn_loser(status_id);
		CREATE INDEX idx_return_turn_loser_member_id ON return_turn_loser(member_id);
		CREATE UNIQUE INDEX idx_return_turn_loser_daily_key ON return_turn_loser(daily_key);
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create indexes: %v", err))
	}
	log.Println("Created indexes successfully")

	// Create calculate_play_type table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS calculate_play_type (
			id BIGINT PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create calculate_play_type table: %v", err))
	}
	log.Println("Created calculate_play_type table successfully")

	// Insert initial data for calculate_play_type
	_, err = con.Exec(ctx, `
		INSERT INTO calculate_play_type (id, name) VALUES
		(1, 'Live-Casino'),
		(2, 'Slot'),
		(3, 'Sport')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to insert calculate_play_type data: %v", err))
	}
	log.Println("Inserted calculate_play_type data successfully")

	// Create return_turn_calculate_type junction table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS return_turn_calculate_type (
			id BIGSERIAL PRIMARY KEY,
			return_setting_id BIGINT NOT NULL,
			calculate_type_id BIGINT NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (return_setting_id) REFERENCES return_turn_setting(id) ON DELETE CASCADE,
			FOREIGN KEY (calculate_type_id) REFERENCES calculate_play_type(id),
			UNIQUE (return_setting_id, calculate_type_id)
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create return_turn_calculate_type table: %v", err))
	}
	log.Println("Created return_turn_calculate_type table successfully")

	// Create return_turn_loser_status reference table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS return_turn_loser_status (
			id BIGINT PRIMARY KEY,
			name VARCHAR(255) NOT NULL
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create return_turn_loser_status table: %v", err))
	}
	log.Println("Created return_turn_loser_status table successfully")

	// Insert status values
	_, err = con.Exec(ctx, `
		INSERT INTO return_turn_loser_status (id, name) VALUES
		(1, 'PENDING'),
		(2, 'READY'),
		(3, 'TAKEN'),
		(4, 'EXPIRED')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to insert return_turn_loser_status data: %v", err))
	}
	log.Println("Inserted return_turn_loser_status data successfully")

	// Create return_turn_cut_type reference table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS return_turn_cut_type (
			id BIGINT PRIMARY KEY,
			name VARCHAR(255) NOT NULL
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create return_turn_cut_type table: %v", err))
	}
	log.Println("Created return_turn_cut_type table successfully")

	// Insert cut type values
	_, err = con.Exec(ctx, `
		INSERT INTO return_turn_cut_type (id, name) VALUES
		(1, 'รายวัน'),
		(2, 'รายสัปดาห์')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to insert return_turn_cut_type data: %v", err))
	}
	log.Println("Inserted return_turn_cut_type data successfully")

	// Create return_turn_loser_type reference table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS return_turn_loser_type (
			id BIGINT PRIMARY KEY,
			name VARCHAR(255) NOT NULL
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create return_turn_loser_type table: %v", err))
	}
	log.Println("Created return_turn_loser_type table successfully")

	// Insert return type values
	_, err = con.Exec(ctx, `
		INSERT INTO return_turn_loser_type (id, name) VALUES
		(1, 'คืนยอดเสียเมื่อยอดเสียเกิน')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to insert return_turn_loser_type data: %v", err))
	}
	log.Println("Inserted return_turn_loser_type data successfully")

	// Create play_log table if not exists (for tracking user play activities)
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS play_log (
			id BIGSERIAL PRIMARY KEY,
			user_id BIGINT NOT NULL,
			game_type VARCHAR(50) NOT NULL,
			amount DECIMAL(10,2) NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		// This table might already exist, so don't panic
		log.Printf("Note: play_log table might already exist: %v", err)
	} else {
		log.Println("Created play_log table successfully")
	}

	// Create index for play_log if not exists
	_, err = con.Exec(ctx, `
		CREATE INDEX IF NOT EXISTS idx_play_log_user_id ON play_log(user_id);
		CREATE INDEX IF NOT EXISTS idx_play_log_created_at ON play_log(created_at);
		CREATE INDEX IF NOT EXISTS idx_play_log_game_type ON play_log(game_type);
	`)
	if err != nil {
		log.Printf("Note: Some play_log indexes might already exist: %v", err)
	}

	// Insert default return_turn_setting if not exists
	_, err = con.Exec(ctx, `
		INSERT INTO return_turn_setting (id, return_percent, return_type_id, cut_type_id, min_loss_price, max_return_price, detail, is_enabled, created_at, updated_at)
		VALUES (1, 5.00, 1, 1, 500.00, 1000.00, '<p>คืนยอดเทิร์น</p>', true, '2025-08-26 00:00:00', '2025-08-26 00:00:00')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to insert default return_turn_setting: %v", err))
	}
	log.Println("Inserted default return_turn_setting successfully")

	// Insert default calculate_play_type relations for return_turn_setting
	_, err = con.Exec(ctx, `
		INSERT INTO return_turn_calculate_type (return_setting_id, calculate_type_id)
		VALUES 
			(1, 1), -- Live-Casino
			(1, 2), -- Slot
			(1, 3)  -- Sport
		ON CONFLICT (return_setting_id, calculate_type_id) DO NOTHING
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to insert return_turn_calculate_type relations: %v", err))
	}
	log.Println("Inserted return_turn_calculate_type relations successfully")

	log.Println("Migration 27_create_return_turns completed successfully")
}

func (m *CreateReturnTurns) Down(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Rolling back return turns")

	// Drop tables in reverse order due to foreign key constraints
	tables := []string{
		"return_turn_calculate_type",
		"return_turn_loser",
		"return_turn_setting",
		"calculate_play_type",
		"return_turn_loser_status",
		"return_turn_cut_type",
		"return_turn_loser_type",
		// Note: Not dropping play_log as it might be used by other systems
	}

	for _, table := range tables {
		_, err = con.Exec(ctx, fmt.Sprintf("DROP TABLE IF EXISTS %s CASCADE", table))
		if err != nil {
			log.Printf("Warning: Failed to drop table %s: %v", table, err)
		} else {
			log.Printf("Dropped table %s successfully", table)
		}
	}

	log.Println("Migration rollback completed")
}
