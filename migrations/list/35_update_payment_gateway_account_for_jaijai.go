package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type UpdatePaymentGatewayAccountForJaiJai struct{}

func (m *UpdatePaymentGatewayAccountForJaiJai) GetName() string {
	return "UpdatePaymentGatewayAccountForJaiJai"
}

func (m *UpdatePaymentGatewayAccountForJaiJai) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Updating payment_gateway_account table for JaiJai compatibility")

	// Add columns needed for JaiJai API configuration
	_, err = con.Exec(ctx, `
		ALTER TABLE payment_gateway_account 
		ADD COLUMN IF NOT EXISTS api_key VARCHAR(255) NULL,
		ADD COLUMN IF NOT EXISTS base_url VARCHAR(500) NULL,
		ADD COLUMN IF NOT EXISTS timeout_seconds INT DEFAULT 30,
		ADD COLUMN IF NOT EXISTS max_retries INT DEFAULT 3,
		ADD COLUMN IF NOT EXISTS retry_delay_seconds INT DEFAULT 1,
		ADD COLUMN IF NOT EXISTS enable_request_log BOOLEAN DEFAULT TRUE,
		ADD COLUMN IF NOT EXISTS log_response_body BOOLEAN DEFAULT FALSE,
		ADD COLUMN IF NOT EXISTS enable_debug BOOLEAN DEFAULT FALSE
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add JaiJai config columns: %v", err))
	}

	// Check if withdraw_splitting exists and rename to withdraw_split for consistency
	var columnExists bool
	err = con.QueryRow(ctx, `
		SELECT EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name = 'payment_gateway_account' 
			AND column_name = 'withdraw_splitting'
		)
	`).Scan(&columnExists)

	if err == nil && columnExists {
		_, err = con.Exec(ctx, `
			ALTER TABLE payment_gateway_account 
			RENAME COLUMN withdraw_splitting TO withdraw_split
		`)
		if err != nil {
			log.Printf("Warning: Could not rename withdraw_splitting column: %v", err)
		} else {
			log.Printf("Successfully renamed withdraw_splitting to withdraw_split")
		}
	} else {
		log.Printf("Column withdraw_splitting does not exist or withdraw_split already exists")
	}

	// Add indexes for better performance
	_, err = con.Exec(ctx, `
		CREATE INDEX IF NOT EXISTS idx_payment_gateway_account_provider ON payment_gateway_account (provider);
		CREATE INDEX IF NOT EXISTS idx_payment_gateway_account_active ON payment_gateway_account (active);
		CREATE INDEX IF NOT EXISTS idx_payment_gateway_account_code ON payment_gateway_account (code);
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create indexes: %v", err))
	}

	// Check if default JaiJai configuration exists before inserting
	var existsCount int
	err = con.QueryRow(ctx, "SELECT COUNT(*) FROM payment_gateway_account WHERE provider = 'jaijaipay'").Scan(&existsCount)
	if err != nil {
		log.Printf("Warning: Could not check existing JaiJai config: %v", err)
	} else if existsCount == 0 {
		// Insert default JaiJai configuration
		_, err = con.Exec(ctx, `
			INSERT INTO payment_gateway_account (
				account_name, code, provider, merchant_code, secret_key, api_key, base_url,
				timeout_seconds, max_retries, retry_delay_seconds, enable_request_log, 
				log_response_body, enable_debug, is_deposit, is_withdraw, is_transfer, 
				active, inactive, minimum_withdraw, maximum_withdraw, withdraw_split,
				maximum_withdraw_per_transaction, maximum_split_withdraw_per_transaction
			) VALUES (
				'JaiJaiPay Default', 'JAIJAI_DEFAULT', 'jaijaipay', '', '', '', 
				'https://api.jaijaipay.com/api/v1', 30, 3, 1, TRUE, FALSE, FALSE,
				TRUE, TRUE, TRUE, FALSE, TRUE, 100.00, 500000.00, FALSE, 50000.00, 25000.00
			)
		`)
		if err != nil {
			log.Printf("Warning: Could not insert default JaiJai config: %v", err)
		} else {
			log.Printf("Default JaiJai configuration inserted successfully")
		}
	} else {
		log.Printf("JaiJai configuration already exists, skipping default insert")
	}

	log.Printf("Migration: payment_gateway_account table updated successfully for JaiJai compatibility")
}

func (m *UpdatePaymentGatewayAccountForJaiJai) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	// Remove added columns
	_, err = con.Exec(ctx, `
		ALTER TABLE payment_gateway_account 
		DROP COLUMN IF EXISTS api_key,
		DROP COLUMN IF EXISTS base_url,
		DROP COLUMN IF EXISTS timeout_seconds,
		DROP COLUMN IF EXISTS max_retries,
		DROP COLUMN IF EXISTS retry_delay_seconds,
		DROP COLUMN IF EXISTS enable_request_log,
		DROP COLUMN IF EXISTS log_response_body,
		DROP COLUMN IF EXISTS enable_debug
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to remove JaiJai config columns: %v", err))
	}

	// Rename column back
	_, err = con.Exec(ctx, `
		ALTER TABLE payment_gateway_account 
		RENAME COLUMN withdraw_split TO withdraw_splitting
	`)
	// Ignore error if column doesn't exist

	// Drop indexes
	_, err = con.Exec(ctx, `
		DROP INDEX IF EXISTS idx_payment_gateway_account_provider;
		DROP INDEX IF EXISTS idx_payment_gateway_account_active;
		DROP INDEX IF EXISTS idx_payment_gateway_account_code;
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop indexes: %v", err))
	}

	// Remove default JaiJai configuration
	_, err = con.Exec(ctx, `
		DELETE FROM payment_gateway_account WHERE code = 'JAIJAI_DEFAULT'
	`)
	if err != nil {
		log.Printf("Warning: Could not remove default JaiJai config: %v", err)
	}
}
