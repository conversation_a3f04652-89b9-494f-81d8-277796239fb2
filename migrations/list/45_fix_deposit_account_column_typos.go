package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type FixDepositAccountColumnTypos struct{}

func (m *FixDepositAccountColumnTypos) GetName() string {
	return "FixDepositAccountColumnTypos"
}

func (m *FixDepositAccountColumnTypos) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public" // default schema
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	log.Printf("Migration: Setting search_path to '%s'", schemaName)
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Rename columns to fix typos
	log.Println("Migration: Renaming amount_triger_tranfer to amount_trigger_transfer")
	_, err = con.Exec(ctx, `ALTER TABLE deposit_account RENAME COLUMN amount_triger_tranfer TO amount_trigger_transfer`)
	if err != nil {
		panic(fmt.Sprintf("Failed to rename amount_triger_tranfer column: %v", err))
	}
	log.Println("Migration: amount_triger_tranfer renamed successfully")

	log.Println("Migration: Renaming amount_tranfer to amount_transfer")
	_, err = con.Exec(ctx, `ALTER TABLE deposit_account RENAME COLUMN amount_tranfer TO amount_transfer`)
	if err != nil {
		panic(fmt.Sprintf("Failed to rename amount_tranfer column: %v", err))
	}
	log.Println("Migration: amount_tranfer renamed successfully")

	log.Println("Migration: Renaming account_tranfer_id to account_transfer_id")
	_, err = con.Exec(ctx, `ALTER TABLE deposit_account RENAME COLUMN account_tranfer_id TO account_transfer_id`)
	if err != nil {
		panic(fmt.Sprintf("Failed to rename account_tranfer_id column: %v", err))
	}
	log.Println("Migration: account_tranfer_id renamed successfully")

	log.Println("Migration: Renaming account_tranfer_type_id to account_transfer_type_id")
	_, err = con.Exec(ctx, `ALTER TABLE deposit_account RENAME COLUMN account_tranfer_type_id TO account_transfer_type_id`)
	if err != nil {
		panic(fmt.Sprintf("Failed to rename account_tranfer_type_id column: %v", err))
	}
	log.Println("Migration: account_tranfer_type_id renamed successfully")

	// Update foreign key constraint name if needed
	log.Println("Migration: Updating foreign key constraint name")
	_, err = con.Exec(ctx, `
		DO $$
		BEGIN
			-- Drop old constraint if exists
			IF EXISTS (
				SELECT 1 FROM information_schema.table_constraints 
				WHERE constraint_name = 'fk_deposit_account_tranfer_type' 
				AND table_name = 'deposit_account'
			) THEN
				ALTER TABLE deposit_account DROP CONSTRAINT fk_deposit_account_tranfer_type;
			END IF;
			
			-- Add new constraint if column exists and constraint doesn't exist
			IF EXISTS (
				SELECT 1 FROM information_schema.columns 
				WHERE table_name = 'deposit_account' 
				AND column_name = 'account_transfer_type_id'
			) AND NOT EXISTS (
				SELECT 1 FROM information_schema.table_constraints 
				WHERE constraint_name = 'fk_deposit_account_transfer_type' 
				AND table_name = 'deposit_account'
			) THEN
				ALTER TABLE deposit_account 
				ADD CONSTRAINT fk_deposit_account_transfer_type 
				FOREIGN KEY (account_transfer_type_id) 
				REFERENCES account_tranfer_type(id);
			END IF;
		END $$;
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to update foreign key constraint: %v", err))
	}
	log.Println("Migration: Foreign key constraint updated successfully")

	log.Println("Migration: FixDepositAccountColumnTypos completed successfully")
}

func (m *FixDepositAccountColumnTypos) Down(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public" // default schema
	}

	log.Printf("Migration: Using schema '%s' for rollback", schemaName)

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Revert foreign key constraint name
	log.Println("Migration: Reverting foreign key constraint name")
	_, err = con.Exec(ctx, `
		DO $$
		BEGIN
			-- Drop new constraint if exists
			IF EXISTS (
				SELECT 1 FROM information_schema.table_constraints 
				WHERE constraint_name = 'fk_deposit_account_transfer_type' 
				AND table_name = 'deposit_account'
			) THEN
				ALTER TABLE deposit_account DROP CONSTRAINT fk_deposit_account_transfer_type;
			END IF;
			
			-- Add old constraint if column exists
			IF EXISTS (
				SELECT 1 FROM information_schema.columns 
				WHERE table_name = 'deposit_account' 
				AND column_name = 'account_tranfer_type_id'
			) THEN
				ALTER TABLE deposit_account 
				ADD CONSTRAINT fk_deposit_account_tranfer_type 
				FOREIGN KEY (account_tranfer_type_id) 
				REFERENCES account_tranfer_type(id);
			END IF;
		END $$;
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to revert foreign key constraint: %v", err))
	}

	// Revert column names back to original typos
	log.Println("Migration: Reverting account_transfer_type_id to account_tranfer_type_id")
	_, err = con.Exec(ctx, `ALTER TABLE deposit_account RENAME COLUMN account_transfer_type_id TO account_tranfer_type_id`)
	if err != nil {
		panic(fmt.Sprintf("Failed to revert account_transfer_type_id column: %v", err))
	}

	log.Println("Migration: Reverting account_transfer_id to account_tranfer_id")
	_, err = con.Exec(ctx, `ALTER TABLE deposit_account RENAME COLUMN account_transfer_id TO account_tranfer_id`)
	if err != nil {
		panic(fmt.Sprintf("Failed to revert account_transfer_id column: %v", err))
	}

	log.Println("Migration: Reverting amount_transfer to amount_tranfer")
	_, err = con.Exec(ctx, `ALTER TABLE deposit_account RENAME COLUMN amount_transfer TO amount_tranfer`)
	if err != nil {
		panic(fmt.Sprintf("Failed to revert amount_transfer column: %v", err))
	}

	log.Println("Migration: Reverting amount_trigger_transfer to amount_triger_tranfer")
	_, err = con.Exec(ctx, `ALTER TABLE deposit_account RENAME COLUMN amount_trigger_transfer TO amount_triger_tranfer`)
	if err != nil {
		panic(fmt.Sprintf("Failed to revert amount_trigger_transfer column: %v", err))
	}

	log.Println("Migration: FixDepositAccountColumnTypos rollback completed successfully")
}
