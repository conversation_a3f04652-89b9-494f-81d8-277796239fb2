package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type UpdateWithdrawalApprovalsUserRoleArray struct{}

func (m *UpdateWithdrawalApprovalsUserRoleArray) GetName() string {
	return "UpdateWithdrawalApprovalsUserRoleArray"
}

func (m *UpdateWithdrawalApprovalsUserRoleArray) Up(con pgx.Tx) {
	ctx := context.Background()

	// schema
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}
	log.Printf("Migration(UpdateWithdrawalApprovalsUserRoleArray): Using schema '%s'", schemaName)

	// set search_path
	if _, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName)); err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}
	log.Printf("Migration(UpdateWithdrawalApprovalsUserRoleArray): search_path set")

	// ===== Add user_role_ids column as JSONB array =====
	if _, err := con.Exec(ctx, `
		ALTER TABLE member_group_withdrawal_approvals 
		ADD COLUMN IF NOT EXISTS user_role_ids JSONB;
	`); err != nil {
		panic(fmt.Sprintf("Failed to add user_role_ids column: %v", err))
	}

	// ===== Migrate existing data: convert single user_role_id to array format =====
	if _, err := con.Exec(ctx, `
		UPDATE member_group_withdrawal_approvals 
		SET user_role_ids = jsonb_build_array(user_role_id)
		WHERE user_role_id IS NOT NULL AND user_role_ids IS NULL;
	`); err != nil {
		panic(fmt.Sprintf("Failed to migrate existing data: %v", err))
	}

	// ===== Make the new column NOT NULL =====
	if _, err := con.Exec(ctx, `
		ALTER TABLE member_group_withdrawal_approvals 
		ALTER COLUMN user_role_ids SET NOT NULL;
	`); err != nil {
		panic(fmt.Sprintf("Failed to set user_role_ids as NOT NULL: %v", err))
	}

	// ===== Add constraint to ensure user_role_ids is always an array =====
	if _, err := con.Exec(ctx, `
		ALTER TABLE member_group_withdrawal_approvals 
		ADD CONSTRAINT check_user_role_ids_is_array 
		CHECK (jsonb_typeof(user_role_ids) = 'array');
	`); err != nil {
		panic(fmt.Sprintf("Failed to add array constraint: %v", err))
	}

	// ===== Add comment =====
	if _, err := con.Exec(ctx, `
		COMMENT ON COLUMN member_group_withdrawal_approvals.user_role_ids 
		IS 'Array of user role IDs that can approve withdrawals in this range';
	`); err != nil {
		panic(fmt.Sprintf("Failed to add column comment: %v", err))
	}

	// ===== Drop the old user_role_id column =====
	if _, err := con.Exec(ctx, `
		ALTER TABLE member_group_withdrawal_approvals 
		DROP COLUMN IF EXISTS user_role_id;
	`); err != nil {
		panic(fmt.Sprintf("Failed to drop old user_role_id column: %v", err))
	}

	log.Printf("Migration(UpdateWithdrawalApprovalsUserRoleArray): Up done")
}

func (m *UpdateWithdrawalApprovalsUserRoleArray) Down(con pgx.Tx) {
	ctx := context.Background()

	// schema
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}
	if _, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName)); err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// ===== Re-add user_role_id column =====
	if _, err := con.Exec(ctx, `
		ALTER TABLE member_group_withdrawal_approvals 
		ADD COLUMN IF NOT EXISTS user_role_id INT;
	`); err != nil {
		panic(fmt.Sprintf("Failed to re-add user_role_id column: %v", err))
	}

	// ===== Migrate data back from array to single value (take first element) =====
	if _, err := con.Exec(ctx, `
		UPDATE member_group_withdrawal_approvals 
		SET user_role_id = CAST(user_role_ids->0 AS INT)
		WHERE user_role_ids IS NOT NULL AND jsonb_array_length(user_role_ids) > 0;
	`); err != nil {
		panic(fmt.Sprintf("Failed to migrate data back to user_role_id: %v", err))
	}

	// ===== Make user_role_id NOT NULL =====
	if _, err := con.Exec(ctx, `
		ALTER TABLE member_group_withdrawal_approvals 
		ALTER COLUMN user_role_id SET NOT NULL;
	`); err != nil {
		panic(fmt.Sprintf("Failed to set user_role_id as NOT NULL: %v", err))
	}

	// ===== Remove constraint =====
	if _, err := con.Exec(ctx, `
		ALTER TABLE member_group_withdrawal_approvals 
		DROP CONSTRAINT IF EXISTS check_user_role_ids_is_array;
	`); err != nil {
		panic(fmt.Sprintf("Failed to drop array constraint: %v", err))
	}

	// ===== Remove user_role_ids column =====
	if _, err := con.Exec(ctx, `
		ALTER TABLE member_group_withdrawal_approvals 
		DROP COLUMN IF EXISTS user_role_ids;
	`); err != nil {
		panic(fmt.Sprintf("Failed to drop user_role_ids column: %v", err))
	}

	log.Printf("Migration(UpdateWithdrawalApprovalsUserRoleArray): Down done")
}
