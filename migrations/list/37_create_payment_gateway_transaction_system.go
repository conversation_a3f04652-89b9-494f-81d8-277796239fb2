package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type CreatePaymentGatewayTransactionSystem struct{}

func (m *CreatePaymentGatewayTransactionSystem) GetName() string {
	return "CreatePaymentGatewayTransactionSystem"
}

func (m *CreatePaymentGatewayTransactionSystem) Up(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Creating payment gateway transaction system tables")

	// 1. Main Transaction Table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS payment_gateway_transactions (
			-- Primary Information
			id                          BIGSERIAL PRIMARY KEY,
			transaction_id              VARCHAR(255) NOT NULL UNIQUE,
			internal_reference          VARCHAR(255),
			
			-- Gateway Information
			payment_gateway_account_id  BIGINT NOT NULL,
			provider                    VARCHAR(50) NOT NULL,
			provider_merchant_id        VARCHAR(255),
			provider_order_id           VARCHAR(255),
			
			-- Transaction Details  
			transaction_type            VARCHAR(20) NOT NULL CHECK (transaction_type IN ('DEPOSIT', 'WITHDRAW', 'TRANSFER')),
			amount                      DECIMAL(15,2) NOT NULL CHECK (amount >= 0),
			currency                    VARCHAR(3) NOT NULL DEFAULT 'THB',
			fee_amount                  DECIMAL(15,2) DEFAULT 0 CHECK (fee_amount >= 0),
			net_amount                  DECIMAL(15,2),
			
			-- Status Tracking
			status                      VARCHAR(50) NOT NULL DEFAULT 'PENDING',
			previous_status             VARCHAR(50),
			status_updated_at           TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
			
			-- Customer Information
			customer_reference          VARCHAR(255),
			customer_username           VARCHAR(255),
			customer_bank_account       VARCHAR(255),
			customer_bank_name          VARCHAR(255),
			
			-- URLs and Callbacks
			callback_url                TEXT,
			return_url                  TEXT,
			payment_url                 TEXT,
			
			-- Transaction Lifecycle
			initiated_at                TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
			expires_at                  TIMESTAMP WITH TIME ZONE,
			completed_at                TIMESTAMP WITH TIME ZONE,
			failed_at                   TIMESTAMP WITH TIME ZONE,
			
			-- Webhook Information
			webhook_received_at         TIMESTAMP WITH TIME ZONE,
			webhook_count               INTEGER DEFAULT 0 CHECK (webhook_count >= 0),
			last_webhook_at             TIMESTAMP WITH TIME ZONE,
			webhook_status              VARCHAR(50),
			
			-- Metadata and Additional Information
			description                 TEXT,
			metadata                    JSONB,
			provider_response           JSONB,
			error_code                  VARCHAR(50),
			error_message               TEXT,
			
			-- Audit Fields
			created_at                  TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
			updated_at                  TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
			created_by                  VARCHAR(255),
			updated_by                  VARCHAR(255),
			
			-- Foreign Key Constraint
			CONSTRAINT fk_payment_gateway_account 
				FOREIGN KEY (payment_gateway_account_id) 
				REFERENCES payment_gateway_account(id)
				ON DELETE RESTRICT
		);
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create payment_gateway_transactions table: %v", err))
	}

	// 2. Webhook Log Table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS payment_gateway_webhooks (
			-- Primary Information
			id                          BIGSERIAL PRIMARY KEY,
			transaction_id              VARCHAR(255) NOT NULL,
			
			-- Webhook Details
			webhook_event               VARCHAR(100) NOT NULL,
			webhook_payload             JSONB NOT NULL,
			webhook_signature           VARCHAR(500),
			webhook_headers             JSONB,
			
			-- Processing Information
			is_signature_valid          BOOLEAN DEFAULT FALSE,
			processing_status           VARCHAR(50) DEFAULT 'PENDING' CHECK (processing_status IN ('PENDING', 'PROCESSED', 'FAILED', 'IGNORED')),
			processing_error            TEXT,
			processed_at                TIMESTAMP WITH TIME ZONE,
			
			-- Provider Information
			provider                    VARCHAR(50) NOT NULL,
			provider_request_id         VARCHAR(255),
			provider_timestamp          TIMESTAMP WITH TIME ZONE,
			
			-- HTTP Information
			http_method                 VARCHAR(10) DEFAULT 'POST',
			http_status_code            INTEGER,
			user_agent                  TEXT,
			ip_address                  VARCHAR(45),
			
			-- Audit Fields
			received_at                 TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
			
			-- Foreign Key Constraint
			CONSTRAINT fk_transaction_webhook 
				FOREIGN KEY (transaction_id) 
				REFERENCES payment_gateway_transactions(transaction_id)
				ON DELETE CASCADE
		);
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create payment_gateway_webhooks table: %v", err))
	}

	// 3. API Logs Table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS payment_gateway_api_logs (
			-- Primary Information
			id                          BIGSERIAL PRIMARY KEY,
			transaction_id              VARCHAR(255),
			
			-- API Call Information
			api_endpoint                VARCHAR(255) NOT NULL,
			http_method                 VARCHAR(10) NOT NULL,
			request_payload             JSONB,
			response_payload            JSONB,
			
			-- Provider Information
			provider                    VARCHAR(50) NOT NULL,
			payment_gateway_account_id  BIGINT,
			
			-- Status and Timing
			http_status_code            INTEGER,
			request_duration_ms         INTEGER CHECK (request_duration_ms >= 0),
			retry_count                 INTEGER DEFAULT 0 CHECK (retry_count >= 0),
			is_successful               BOOLEAN DEFAULT FALSE,
			
			-- Error Information
			error_code                  VARCHAR(100),
			error_message               TEXT,
			error_details               JSONB,
			
			-- Headers and Security
			request_headers             JSONB,
			response_headers            JSONB,
			request_signature           VARCHAR(500),
			
			-- Audit Fields
			requested_at                TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
			requested_by                VARCHAR(255),
			
			-- Foreign Key Constraints
			CONSTRAINT fk_transaction_api 
				FOREIGN KEY (transaction_id) 
				REFERENCES payment_gateway_transactions(transaction_id)
				ON DELETE SET NULL,
			CONSTRAINT fk_gateway_api 
				FOREIGN KEY (payment_gateway_account_id) 
				REFERENCES payment_gateway_account(id)
				ON DELETE SET NULL
		);
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create payment_gateway_api_logs table: %v", err))
	}

	// 4. Transaction Status History Table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS payment_gateway_transaction_status_history (
			-- Primary Information  
			id                          BIGSERIAL PRIMARY KEY,
			transaction_id              VARCHAR(255) NOT NULL,
			
			-- Status Change Information
			from_status                 VARCHAR(50),
			to_status                   VARCHAR(50) NOT NULL,
			status_reason               VARCHAR(255),
			status_details              JSONB,
			
			-- Change Source
			changed_by_type             VARCHAR(20) NOT NULL CHECK (changed_by_type IN ('WEBHOOK', 'API', 'ADMIN', 'SYSTEM', 'AUTO')),
			changed_by_reference        VARCHAR(255),
			changed_by_user             VARCHAR(255),
			
			-- Provider Information  
			provider                    VARCHAR(50) NOT NULL,
			provider_reference          VARCHAR(255),
			
			-- Timing
			changed_at                  TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
			
			-- Foreign Key Constraint
			CONSTRAINT fk_transaction_status 
				FOREIGN KEY (transaction_id) 
				REFERENCES payment_gateway_transactions(transaction_id)
				ON DELETE CASCADE
		);
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create payment_gateway_transaction_status_history table: %v", err))
	}

	// 5. Create Indexes
	log.Printf("Creating indexes for payment gateway transaction tables...")

	indexes := []string{
		// payment_gateway_transactions indexes
		"CREATE INDEX IF NOT EXISTS idx_pgt_transaction_id ON payment_gateway_transactions(transaction_id)",
		"CREATE INDEX IF NOT EXISTS idx_pgt_provider ON payment_gateway_transactions(provider)",
		"CREATE INDEX IF NOT EXISTS idx_pgt_status ON payment_gateway_transactions(status)",
		"CREATE INDEX IF NOT EXISTS idx_pgt_customer_reference ON payment_gateway_transactions(customer_reference)",
		"CREATE INDEX IF NOT EXISTS idx_pgt_created_at ON payment_gateway_transactions(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_pgt_webhook_received ON payment_gateway_transactions(webhook_received_at)",
		"CREATE INDEX IF NOT EXISTS idx_pgt_gateway_provider ON payment_gateway_transactions(payment_gateway_account_id, provider)",
		"CREATE INDEX IF NOT EXISTS idx_pgt_type_status ON payment_gateway_transactions(transaction_type, status)",

		// payment_gateway_webhooks indexes
		"CREATE INDEX IF NOT EXISTS idx_pgw_transaction_id ON payment_gateway_webhooks(transaction_id)",
		"CREATE INDEX IF NOT EXISTS idx_pgw_webhook_event ON payment_gateway_webhooks(webhook_event)",
		"CREATE INDEX IF NOT EXISTS idx_pgw_provider ON payment_gateway_webhooks(provider)",
		"CREATE INDEX IF NOT EXISTS idx_pgw_received_at ON payment_gateway_webhooks(received_at)",
		"CREATE INDEX IF NOT EXISTS idx_pgw_processing_status ON payment_gateway_webhooks(processing_status)",

		// payment_gateway_api_logs indexes
		"CREATE INDEX IF NOT EXISTS idx_pgl_transaction_id ON payment_gateway_api_logs(transaction_id)",
		"CREATE INDEX IF NOT EXISTS idx_pgl_provider ON payment_gateway_api_logs(provider)",
		"CREATE INDEX IF NOT EXISTS idx_pgl_endpoint ON payment_gateway_api_logs(api_endpoint)",
		"CREATE INDEX IF NOT EXISTS idx_pgl_requested_at ON payment_gateway_api_logs(requested_at)",
		"CREATE INDEX IF NOT EXISTS idx_pgl_gateway_id ON payment_gateway_api_logs(payment_gateway_account_id)",
		"CREATE INDEX IF NOT EXISTS idx_pgl_status_success ON payment_gateway_api_logs(http_status_code, is_successful)",

		// payment_gateway_transaction_status_history indexes
		"CREATE INDEX IF NOT EXISTS idx_pgsh_transaction_id ON payment_gateway_transaction_status_history(transaction_id)",
		"CREATE INDEX IF NOT EXISTS idx_pgsh_status_change ON payment_gateway_transaction_status_history(from_status, to_status)",
		"CREATE INDEX IF NOT EXISTS idx_pgsh_changed_at ON payment_gateway_transaction_status_history(changed_at)",
		"CREATE INDEX IF NOT EXISTS idx_pgsh_changed_by ON payment_gateway_transaction_status_history(changed_by_type, changed_by_user)",
	}

	for _, indexSQL := range indexes {
		_, err := con.Exec(ctx, indexSQL)
		if err != nil {
			log.Printf("Warning: Failed to create index: %v", err)
		}
	}

	// 6. Create trigger for updating updated_at timestamp
	_, err = con.Exec(ctx, `
		CREATE OR REPLACE FUNCTION update_payment_gateway_transaction_updated_at()
		RETURNS TRIGGER AS $$
		BEGIN
			NEW.updated_at = NOW();
			RETURN NEW;
		END;
		$$ language 'plpgsql';
	`)
	if err != nil {
		log.Printf("Warning: Failed to create trigger function: %v", err)
	}

	_, err = con.Exec(ctx, `
		DROP TRIGGER IF EXISTS trigger_update_payment_gateway_transaction_updated_at ON payment_gateway_transactions;
		CREATE TRIGGER trigger_update_payment_gateway_transaction_updated_at
			BEFORE UPDATE ON payment_gateway_transactions
			FOR EACH ROW EXECUTE FUNCTION update_payment_gateway_transaction_updated_at();
	`)
	if err != nil {
		log.Printf("Warning: Failed to create trigger: %v", err)
	}

	log.Printf("Migration: Payment gateway transaction system created successfully")
}

func (m *CreatePaymentGatewayTransactionSystem) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	// Drop tables in reverse order (respecting foreign keys)
	tables := []string{
		"payment_gateway_transaction_status_history",
		"payment_gateway_api_logs",
		"payment_gateway_webhooks",
		"payment_gateway_transactions",
	}

	for _, table := range tables {
		_, err := con.Exec(ctx, fmt.Sprintf("DROP TABLE IF EXISTS %s CASCADE", table))
		if err != nil {
			log.Printf("Warning: Could not drop table %s: %v", table, err)
		}
	}

	// Drop trigger and function
	_, err = con.Exec(ctx, "DROP FUNCTION IF EXISTS update_payment_gateway_transaction_updated_at() CASCADE")
	if err != nil {
		log.Printf("Warning: Could not drop trigger function: %v", err)
	}

	log.Printf("Migration: Payment gateway transaction system dropped")
}
