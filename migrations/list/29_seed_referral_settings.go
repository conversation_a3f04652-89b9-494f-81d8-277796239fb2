package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type SeedReferralSettings struct{}

func (m *SeedReferralSettings) GetName() string {
	return "SeedReferralSettings"
}

func (m *SeedReferralSettings) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public" // default schema
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	log.Printf("Migration: Setting search_path to '%s'", schemaName)
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}
	log.Printf("Migration: search_path set successfully")

	// Verify current schema
	var currentSchema string
	err = con.QueryRow(ctx, "SELECT current_schema()").Scan(&currentSchema)
	if err == nil {
		log.Printf("Migration: Current schema is '%s'", currentSchema)
	}

	// Seed referral settings with default values
	referralSettings := []struct {
		key         string
		value       string
		description string
	}{
		{
			key:         "referral_commission_percent",
			value:       "5",
			description: "เปอร์เซ็นต์ค่าคอมมิชชั่น referral (default: 5%)",
		},
		{
			key:         "referral_commission_minimum_withdraw",
			value:       "500",
			description: "ยอดขั้นต่ำในการถอนค่าคอมมิชชั่น referral (default: 500 บาท)",
		},
		{
			key:         "referral_commission_every",
			value:       "day",
			description: "ความถี่ในการคำนวณค่าคอมมิชชั่น referral (day/week/month, default: day)",
		},
	}

	for _, setting := range referralSettings {
		// Check if setting already exists
		var count int
		err = con.QueryRow(ctx, "SELECT COUNT(*) FROM system_settings WHERE key = $1", setting.key).Scan(&count)
		if err != nil {
			panic(fmt.Sprintf("Failed to check existing setting for key %s: %v", setting.key, err))
		}

		if count == 0 {
			// Insert new setting
			_, err = con.Exec(ctx, `
				INSERT INTO system_settings (key, value, description, created_at, updated_at)
				VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
			`, setting.key, setting.value, setting.description)
			if err != nil {
				panic(fmt.Sprintf("Failed to insert referral setting %s: %v", setting.key, err))
			}
			log.Printf("Migration: Inserted referral setting '%s' with value '%s'", setting.key, setting.value)
		} else {
			log.Printf("Migration: Referral setting '%s' already exists, skipping", setting.key)
		}
	}

	log.Printf("Migration: Referral settings seeded successfully")
}

func (m *SeedReferralSettings) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	// Remove referral settings
	referralKeys := []string{
		"referral_commission_percent",
		"referral_commission_minimum_withdraw",
		"referral_commission_every",
	}

	for _, key := range referralKeys {
		_, err = con.Exec(ctx, "DELETE FROM system_settings WHERE key = $1", key)
		if err != nil {
			panic(fmt.Sprintf("Failed to delete referral setting %s: %v", key, err))
		}
		log.Printf("Migration: Removed referral setting '%s'", key)
	}

	log.Printf("Migration: Referral settings removed successfully")
}
