package list

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
)

// AlterReferUserIdToInt migration struct
type AlterReferUserIdToInt struct{}

// GetName returns the migration name
func (m *AlterReferUserIdToInt) GetName() string {
	return "12_alter_refer_user_id_to_int"
}

// Up executes the migration
func (m *AlterReferUserIdToInt) Up(con pgx.Tx) {
	ctx := context.Background()
	err := migration12AlterReferUserIdToInt(ctx, con)
	if err != nil {
		panic(err)
	}
}

// Down rolls back the migration
func (m *AlterReferUserIdToInt) Down(con pgx.Tx) {
	ctx := context.Background()
	fmt.Println("Rolling back Migration 12: Alter refer_user_id column to INT type")

	// Drop foreign key constraint
	_, err := con.Exec(ctx, `
		ALTER TABLE members 
		DROP CONSTRAINT IF EXISTS fk_members_refer_user_id
	`)
	if err != nil {
		panic(fmt.Errorf("failed to drop foreign key constraint for refer_user_id: %v", err))
	}

	// Drop index
	_, err = con.Exec(ctx, `
		DROP INDEX IF EXISTS idx_members_refer_user_id
	`)
	if err != nil {
		panic(fmt.Errorf("failed to drop index on refer_user_id: %v", err))
	}

	// Alter column type back to VARCHAR
	_, err = con.Exec(ctx, `
		ALTER TABLE members 
		ALTER COLUMN refer_user_id TYPE VARCHAR(50) USING refer_user_id::VARCHAR(50)
	`)
	if err != nil {
		panic(fmt.Errorf("failed to alter refer_user_id column type back to VARCHAR: %v", err))
	}

	fmt.Println("Migration 12 rollback completed: refer_user_id column reverted to VARCHAR")
}

func migration12AlterReferUserIdToInt(ctx context.Context, con pgx.Tx) error {
	fmt.Println("Running Migration 12: Alter refer_user_id column to INT type")

	// First, update any existing string values to NULL if they can't be converted to INT
	// This handles cases where refer_user_id might have non-numeric values
	_, err := con.Exec(ctx, `
		UPDATE members 
		SET refer_user_id = NULL 
		WHERE refer_user_id IS NOT NULL 
		AND refer_user_id !~ '^[0-9]+$'
	`)
	if err != nil {
		return fmt.Errorf("failed to cleanup invalid refer_user_id values: %v", err)
	}

	// Alter the column type from VARCHAR to INT
	_, err = con.Exec(ctx, `
		ALTER TABLE members 
		ALTER COLUMN refer_user_id TYPE INT USING refer_user_id::INT
	`)
	if err != nil {
		return fmt.Errorf("failed to alter refer_user_id column type: %v", err)
	}

	// Add foreign key constraint to ensure refer_user_id references valid members
	_, err = con.Exec(ctx, `
		ALTER TABLE members 
		ADD CONSTRAINT fk_members_refer_user_id 
		FOREIGN KEY (refer_user_id) REFERENCES members(id) ON DELETE SET NULL
	`)
	if err != nil {
		return fmt.Errorf("failed to add foreign key constraint for refer_user_id: %v", err)
	}

	// Create index for better performance on refer_user_id lookups
	_, err = con.Exec(ctx, `
		CREATE INDEX IF NOT EXISTS idx_members_refer_user_id ON members (refer_user_id)
	`)
	if err != nil {
		return fmt.Errorf("failed to create index on refer_user_id: %v", err)
	}

	fmt.Println("Migration 12 completed: refer_user_id column altered to INT with foreign key constraint")
	return nil
}
