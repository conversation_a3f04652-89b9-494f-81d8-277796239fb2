package list

import (
	"context"
	"fmt"
	"github.com/jackc/pgx/v5"
	"log"
	"os"
)

type CreateBankingSystem struct{}

func (m *CreateBankingSystem) GetName() string {
	return "CreateBankingSystem"
}

func (m *CreateBankingSystem) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Creating banking system tables")

	// Create banking table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS banking (
			id SERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			short_name VARCHAR(50) NOT NULL,
			image_url VARCHAR(255) NULL,
			code VARCHAR(50) NOT NULL UNIQUE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert banking data
	_, err = con.Exec(ctx, `
		INSERT INTO banking (name, short_name, image_url, code) VALUES
		('ธนาคารกรุงเทพ', 'BBL', '', '002'),
		('ธนาคารกสิกรไทย', 'KBANK', '', '004'),
		('ธนาคารกรุงไทย', 'KTB', '', '006'),
		('ธนาคารทหารไทยธนชาต', 'TTB', '', '011'),
		('ธนาคารไทยพาณิชย์', 'SCB', '', '014'),
		('ธนาคารกรุงศรีอยุธยา', 'BAY', '', '025'),
		('ธนาคารเกียรตินาคินภัทร', 'KKP', '', '069'),
		('ธนาคารซีไอเอ็มบีไทย', 'CIMBT', '', '022'),
		('ธนาคารทิสโก้', 'TISCO', '', '067'),
		('ธนาคารยูโอบี', 'UOBT', '', '024'),
		('ธนาคารไทยเครดิตเพื่อรายย่อย', 'TCD', '', '071'),
		('ธนาคารแลนด์ แอนด์ เฮ้าส์', 'LHFG', '', '073'),
		('ธนาคารเพื่อการเกษตรและสหกรณ์การเกษตร', 'BAAC', '', '034'),
		('ธนาคารเพื่อการส่งออกและนำเข้าแห่งประเทศไทย', 'EXIM', '', '035'),
		('ธนาคารออมสิน', 'GSB', '', '030'),
		('ธนาคารอาคารสงเคราะห์', 'GHB', '', '033'),
		('True Money', 'TMN', '', 'TMN')
		ON CONFLICT (code) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create payment_method table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS payment_method (
			id SERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL UNIQUE,
			minimum_deposit FLOAT NOT NULL DEFAULT 20.00,
			maximum_deposit FLOAT NOT NULL DEFAULT 150000.00,
			fee FLOAT NOT NULL DEFAULT 0.00,
			is_fee BOOLEAN NOT NULL DEFAULT false,
			active BOOLEAN NOT NULL DEFAULT false,
			is_lobby BOOLEAN NOT NULL DEFAULT false,
			inactive BOOLEAN NOT NULL DEFAULT false,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert payment method data
	_, err = con.Exec(ctx, `
		INSERT INTO payment_method (name) VALUES
		('ปกติ'),
		('ทศนิยม'),
		('True Wallet'),
		('QR Payment')
		ON CONFLICT (name) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create auto_bot table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS auto_bot (
			id SERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL UNIQUE
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert auto bot data
	_, err = con.Exec(ctx, `
		INSERT INTO auto_bot (name) VALUES
		('ปิดใช้งาน'),
		('เปิดใช้งาน'),
		('พักบอท'),
		('ตรวจสอบ')
		ON CONFLICT (name) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create algorithm table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS algorithm (
			id SERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL UNIQUE
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert algorithm data
	_, err = con.Exec(ctx, `
		INSERT INTO algorithm (name) VALUES
		('WEB'),
		('SMS'),
		('APP'),
		('API'),
		('BANKING')
		ON CONFLICT (name) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create deposit_account table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS deposit_account (
			id SERIAL PRIMARY KEY,
			banking_id INTEGER NOT NULL,
			payment_method_id INTEGER NOT NULL,
			algorithm_id INTEGER NULL,
			auto_bot_id INTEGER NOT NULL,
			account_name VARCHAR(255) NOT NULL,
			account_name_display VARCHAR(255) NOT NULL,
			account_number VARCHAR(255) NOT NULL,
			phone_number VARCHAR(10) NOT NULL,
			auto_transfer BOOLEAN NOT NULL DEFAULT FALSE,
			push_bullet_nickname VARCHAR(255) NULL,
			push_bullet_token VARCHAR(255) NULL,
			active BOOLEAN NOT NULL DEFAULT FALSE,
			inactive BOOLEAN NOT NULL DEFAULT FALSE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_deposit_account_banking
			FOREIGN KEY (banking_id) REFERENCES banking(id),
			CONSTRAINT fk_deposit_account_payment_method
			FOREIGN KEY (payment_method_id) REFERENCES payment_method(id),
			CONSTRAINT fk_deposit_account_algorithm
			FOREIGN KEY (algorithm_id) REFERENCES algorithm(id),
			CONSTRAINT fk_deposit_account_auto_bot
			FOREIGN KEY (auto_bot_id) REFERENCES auto_bot(id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create withdraw_account table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS withdraw_account (
			id SERIAL PRIMARY KEY,
			banking_id INT NOT NULL,
			algorithm_id INT NULL,
			auto_bot_id INTEGER NOT NULL,
			account_name VARCHAR(255) NOT NULL,
			account_name_display VARCHAR(255) NOT NULL,
			account_number VARCHAR(255) NOT NULL,
			phone_number VARCHAR(10) NOT NULL,
			minimum_withdraw FLOAT NOT NULL,
			maximum_withdraw FLOAT NOT NULL,
			withdraw_splitting BOOLEAN NOT NULL DEFAULT FALSE,
			maximum_withdraw_per_transaction FLOAT NOT NULL,
			maximum_split_withdraw_per_transaction FLOAT NOT NULL,
			limit_auto_transfer FLOAT NOT NULL,
			push_bullet_token VARCHAR(255) NULL,
			push_bullet_nickname VARCHAR(255) NULL,
			identification_number VARCHAR(255) NULL,
			laser_id VARCHAR(255) NULL,
			pin VARCHAR(255) NULL,
			encryption_key VARCHAR(255) NULL,
			device_id VARCHAR(255) NULL,
			file_json_url VARCHAR(255) NULL,
			birthday DATE NULL,
			active BOOLEAN NOT NULL DEFAULT FALSE,
			inactive BOOLEAN NOT NULL DEFAULT FALSE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_withdraw_account_banking
			FOREIGN KEY (banking_id) REFERENCES banking(id),
			CONSTRAINT fk_withdraw_account_algorithm
			FOREIGN KEY (algorithm_id) REFERENCES algorithm(id),
			CONSTRAINT fk_withdraw_account_auto_bot
			FOREIGN KEY (auto_bot_id) REFERENCES auto_bot(id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create holding_account table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS holding_account (
			id SERIAL PRIMARY KEY,
			banking_id INTEGER NOT NULL,
			account_name VARCHAR(255) NOT NULL,
			account_name_display VARCHAR(255) NOT NULL,
			account_number VARCHAR(255) NOT NULL,
			phone_number VARCHAR(10) NOT NULL,
			active BOOLEAN NOT NULL DEFAULT FALSE,
			inactive BOOLEAN NOT NULL DEFAULT FALSE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_holding_account_banking
			FOREIGN KEY (banking_id) REFERENCES banking(id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create payment_gateway_account table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS payment_gateway_account (
			id SERIAL PRIMARY KEY,
			account_name VARCHAR(255) NOT NULL,
			code VARCHAR(255) NOT NULL,
			provider VARCHAR(255) NOT NULL,
			merchant_code VARCHAR(255) NOT NULL,
			secret_key VARCHAR(255) NOT NULL,
			secret_key_two VARCHAR(255) NULL,
			first_username VARCHAR(255) NULL,
			second_username VARCHAR(255) NULL,
			first_password VARCHAR(255) NULL,
			second_password VARCHAR(255) NULL,
			minimum_withdraw FLOAT NULL,
			maximum_withdraw FLOAT NULL,
			withdraw_splitting BOOLEAN NOT NULL DEFAULT FALSE,
			maximum_withdraw_per_transaction FLOAT NULL,
			maximum_split_withdraw_per_transaction FLOAT NULL,
			is_deposit BOOLEAN NOT NULL DEFAULT FALSE,
			is_withdraw BOOLEAN NOT NULL DEFAULT FALSE,
			is_transfer BOOLEAN NOT NULL DEFAULT FALSE,
			active BOOLEAN NOT NULL DEFAULT FALSE,
			inactive BOOLEAN NOT NULL DEFAULT FALSE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	log.Printf("Migration: Banking system tables created successfully")
}

func (m *CreateBankingSystem) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	// Drop tables in reverse order
	tables := []string{
		"DROP TABLE IF EXISTS payment_gateway_account",
		"DROP TABLE IF EXISTS holding_account",
		"DROP TABLE IF EXISTS withdraw_account",
		"DROP TABLE IF EXISTS deposit_account",
		"DROP TABLE IF EXISTS algorithm",
		"DROP TABLE IF EXISTS auto_bot",
		"DROP TABLE IF EXISTS payment_method",
		"DROP TABLE IF EXISTS banking",
	}

	for _, dropSQL := range tables {
		_, err := con.Exec(ctx, dropSQL)
		if err != nil {
			panic(err)
		}
	}
}
