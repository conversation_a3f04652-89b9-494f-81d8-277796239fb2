package list

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/jackc/pgx/v5"
)

type SeedStatementStatusData struct{}

func (m *SeedStatementStatusData) GetName() string {
	return "21_seed_statement_status_data"
}

func (m *SeedStatementStatusData) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schema := os.Getenv("DATABASE_SCHEMA")
	if schema == "" {
		schema = "public"
	}

	createdAt := time.Date(2023, 9, 19, 3, 33, 10, 0, time.UTC)
	updatedAt1 := time.Date(2023, 9, 19, 3, 37, 18, 0, time.UTC)
	updatedAt2 := time.Date(2023, 9, 19, 3, 38, 13, 0, time.UTC)
	updatedAt3 := time.Date(2023, 9, 19, 3, 38, 30, 0, time.UTC)

	queries := []string{
		// Set search path
		fmt.Sprintf("SET search_path TO %s", schema),
	}

	for _, query := range queries {
		if _, err := con.Exec(ctx, query); err != nil {
			log.Printf("Error executing query: %s\nError: %v", query, err)
			panic(err)
		}
	}

	// Insert seed data
	insertQuery := `
		INSERT INTO statement_status (id, name, label_th, label_en, created_at, updated_at, deleted_at)
		VALUES 
			(1, 'PENDING', 'รอตรวจสอบ', 'pending', $1, $2, NULL),
			(2, 'CONFIRMED', 'ตรวจสอบแล้ว', 'confirmed', $1, $3, NULL),
			(3, 'IGNORED', 'ไม่สนใจ', 'ignored', $1, $4, NULL)
		ON CONFLICT (id) DO NOTHING
	`

	if _, err := con.Exec(ctx, insertQuery, createdAt, updatedAt1, updatedAt2, updatedAt3); err != nil {
		log.Printf("Error inserting statement_status data: %v", err)
		panic(err)
	}

	// Reset sequence
	resetSeq := `SELECT setval('statement_status_id_seq', (SELECT MAX(id) FROM statement_status), true)`
	if _, err := con.Exec(ctx, resetSeq); err != nil {
		log.Printf("Error resetting statement_status sequence: %v", err)
		panic(err)
	}

	log.Println("Migration 21: Seeded statement_status data successfully")
}

func (m *SeedStatementStatusData) Down(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schema := os.Getenv("DATABASE_SCHEMA")
	if schema == "" {
		schema = "public"
	}

	queries := []string{
		// Set search path
		fmt.Sprintf("SET search_path TO %s", schema),

		// Delete seeded data
		`DELETE FROM statement_status WHERE id IN (1, 2, 3)`,
	}

	for _, query := range queries {
		if _, err := con.Exec(ctx, query); err != nil {
			log.Printf("Error executing rollback query: %s\nError: %v", query, err)
			panic(err)
		}
	}

	log.Println("Migration 21: Rolled back statement_status data successfully")
}
