package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type AddQRCodeToPaymentGatewayTransactions struct{}

func (m *AddQRCodeToPaymentGatewayTransactions) GetName() string {
	return "AddQRCodeToPaymentGatewayTransactions"
}

func (m *AddQRCodeToPaymentGatewayTransactions) Up(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Adding QR code columns to payment_gateway_transactions table")

	// Add QR code related columns
	_, err = con.Exec(ctx, `
		ALTER TABLE payment_gateway_transactions 
		ADD COLUMN IF NOT EXISTS qr_code TEXT,
		ADD COLUMN IF NOT EXISTS qr_text TEXT,
		ADD COLUMN IF NOT EXISTS qr_image_url TEXT
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add QR code columns: %v", err))
	}

	log.Printf("Migration: QR code columns added successfully")
}

func (m *AddQRCodeToPaymentGatewayTransactions) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	// Remove QR code columns
	_, err = con.Exec(ctx, `
		ALTER TABLE payment_gateway_transactions 
		DROP COLUMN IF EXISTS qr_code,
		DROP COLUMN IF EXISTS qr_text,
		DROP COLUMN IF EXISTS qr_image_url
	`)
	if err != nil {
		log.Printf("Warning: Could not remove QR code columns: %v", err)
	}

	log.Printf("Migration: QR code columns removed")
}
