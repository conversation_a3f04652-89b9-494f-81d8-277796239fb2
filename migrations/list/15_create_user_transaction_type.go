package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type CreateUserTransactionType struct{}

func (m *CreateUserTransactionType) GetName() string {
	return "CreateUserTransactionType"
}

func (m *CreateUserTransactionType) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Creating user transaction type table")

	// Create user_transaction_type table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS user_transaction_type (
			id SERIAL PRIMARY KEY,
			name VARCHAR(100) NOT NULL UNIQUE,
			detail TEXT,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert initial data
	log.Printf("Migration: Inserting initial user transaction type data")
	_, err = con.Exec(ctx, `
		INSERT INTO user_transaction_type (id, name, detail, created_at) VALUES
		(1, 'DEPOSIT', 'ฝาก', '2025-08-02 09:00:00'),
		(2, 'WITHDRAW', 'ถอน', '2025-08-02 09:00:00'),
		(3, 'BONUS', 'โบนัส', '2025-08-02 09:00:00'),
		(4, 'PROMOTION_RETURN_LOSS', 'แจกโบนัสฟรี คืนยอดเสีย', '2025-08-02 09:00:00'),
		(5, 'AFFILIATE_INCOME', 'โบนัสรายได้แนะนำเพื่อน', '2025-08-02 09:00:00'),
		(6, 'ALLIANCE_INCOME', 'โบนัสรายได้พันธมิตร', '2025-08-02 09:00:00'),
		(7, 'TAKE_CREDIT_BACK', 'ดึงเครดิตกลับ', '2025-08-02 09:00:00'),
		(8, 'CREDIT_TYPE_DAILY_ACTIVITY_BONUS', 'โบนัสกิจกรรมรายวัน', '2025-08-02 09:00:00'),
		(9, 'CREDIT_TPYE_LUCKY_WHEEL', 'เครดิตจากกิจกรรมกงล้อนำโชค', '2025-08-02 09:00:00'),
		(10, 'CREDIT_TYPE_PROMOTION_WEB', 'โปรโมชั่นเว็บ', '2025-08-02 09:00:00'),
		(11, 'COUPON_CASH', 'คูปองเงินสด', '2025-08-02 09:00:00'),
		(12, 'CREDIT_TYPE_LOTTERY', 'เครดิตลอตเตอรี่', '2025-08-02 09:00:00'),
		(13, 'PROMOTION_RETURN_TURN', 'แจกโบนัสฟรี คืนยอดcommission', '2025-08-02 09:00:00'),
		(14, 'CREDIT_TYPE_CANCEL_CREDIT', 'ยกเลิกเติมเครดิต', '2025-08-02 09:00:00')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to insert user transaction type data: %v", err))
	}

	log.Printf("Migration: User transaction type data inserted successfully")
}

func (m *CreateUserTransactionType) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	// Drop table
	_, err = con.Exec(ctx, "DROP TABLE IF EXISTS user_transaction_type")
	if err != nil {
		panic(err)
	}
}
