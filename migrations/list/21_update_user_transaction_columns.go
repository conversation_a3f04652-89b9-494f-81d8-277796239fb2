package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type UpdateUserTransactionColumns struct{}

func (m *UpdateUserTransactionColumns) GetName() string {
	return "16_update_user_transaction_columns"
}

func (m *UpdateUserTransactionColumns) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schema := os.Getenv("DATABASE_SCHEMA")
	if schema == "" {
		schema = "public"
	}

	queries := []string{
		// Set search path
		fmt.Sprintf("SET search_path TO %s", schema),

		// Rename columns from create_ to credit_
		`ALTER TABLE user_transaction RENAME COLUMN create_before TO credit_before`,
		`ALTER TABLE user_transaction RENAME COLUMN create_after TO credit_after`,
		`ALTER TABLE user_transaction RENAME COLUMN create_back TO credit_back`,

		// Drop existing foreign key constraint for user_id
		`ALTER TABLE user_transaction DROP CONSTRAINT IF EXISTS user_transaction_user_id_fkey`,

		// Rename user_id to member_id
		`ALTER TABLE user_transaction RENAME COLUMN user_id TO member_id`,

		// Add foreign key constraint for member_id to members table
		`ALTER TABLE user_transaction 
		 ADD CONSTRAINT user_transaction_member_id_fkey 
		 FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE`,

		// Add foreign key constraint for confirm_admin_id to users table
		`ALTER TABLE user_transaction 
		 ADD CONSTRAINT user_transaction_confirm_admin_id_fkey 
		 FOREIGN KEY (confirm_admin_id) REFERENCES users(id) ON DELETE SET NULL`,

		// Update indexes
		`DROP INDEX IF EXISTS idx_user_transaction_user_id`,
		`CREATE INDEX idx_user_transaction_member_id ON user_transaction(member_id)`,
		`CREATE INDEX idx_user_transaction_confirm_admin_id ON user_transaction(confirm_admin_id)`,

		// Add comments for documentation
		`COMMENT ON COLUMN user_transaction.credit_before IS 'Member credit balance before transaction'`,
		`COMMENT ON COLUMN user_transaction.credit_after IS 'Member credit balance after transaction'`,
		`COMMENT ON COLUMN user_transaction.credit_back IS 'Credit amount returned/refunded'`,
		`COMMENT ON COLUMN user_transaction.member_id IS 'Foreign key to members table'`,
		`COMMENT ON COLUMN user_transaction.confirm_admin_id IS 'Foreign key to users table for admin who confirmed'`,
	}

	for _, query := range queries {
		if _, err := con.Exec(ctx, query); err != nil {
			log.Printf("Error executing query: %s\nError: %v", query, err)
			panic(err)
		}
	}

	log.Println("Migration 16: Updated user_transaction columns and foreign keys successfully")
}

func (m *UpdateUserTransactionColumns) Down(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schema := os.Getenv("DATABASE_SCHEMA")
	if schema == "" {
		schema = "public"
	}

	queries := []string{
		// Set search path
		fmt.Sprintf("SET search_path TO %s", schema),

		// Remove comments
		`COMMENT ON COLUMN user_transaction.credit_before IS NULL`,
		`COMMENT ON COLUMN user_transaction.credit_after IS NULL`,
		`COMMENT ON COLUMN user_transaction.credit_back IS NULL`,
		`COMMENT ON COLUMN user_transaction.member_id IS NULL`,
		`COMMENT ON COLUMN user_transaction.confirm_admin_id IS NULL`,

		// Drop new indexes
		`DROP INDEX IF EXISTS idx_user_transaction_member_id`,
		`DROP INDEX IF EXISTS idx_user_transaction_confirm_admin_id`,

		// Restore old index
		`CREATE INDEX idx_user_transaction_user_id ON user_transaction(member_id)`,

		// Drop foreign key constraints
		`ALTER TABLE user_transaction DROP CONSTRAINT IF EXISTS user_transaction_confirm_admin_id_fkey`,
		`ALTER TABLE user_transaction DROP CONSTRAINT IF EXISTS user_transaction_member_id_fkey`,

		// Rename member_id back to user_id
		`ALTER TABLE user_transaction RENAME COLUMN member_id TO user_id`,

		// Add back original foreign key constraint for user_id
		`ALTER TABLE user_transaction 
		 ADD CONSTRAINT user_transaction_user_id_fkey 
		 FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE`,

		// Rename columns back from credit_ to create_
		`ALTER TABLE user_transaction RENAME COLUMN credit_before TO create_before`,
		`ALTER TABLE user_transaction RENAME COLUMN credit_after TO create_after`,
		`ALTER TABLE user_transaction RENAME COLUMN credit_back TO create_back`,
	}

	for _, query := range queries {
		if _, err := con.Exec(ctx, query); err != nil {
			log.Printf("Error executing rollback query: %s\nError: %v", query, err)
			panic(err)
		}
	}

	log.Println("Migration 16: Rolled back user_transaction column changes successfully")
}
