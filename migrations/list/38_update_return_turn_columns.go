package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type UpdateReturnTurnColumns struct{}

func (m *UpdateReturnTurnColumns) GetName() string {
	return "38_update_return_turn_columns"
}

func (m *UpdateReturnTurnColumns) Up(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Rename existing columns to live_casino variants
	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_loser 
		RENAME COLUMN return_price TO return_price_live_casino
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to rename return_price column: %v", err))
	}
	log.Printf("✅ Renamed return_price to return_price_live_casino")

	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_loser 
		RENAME COLUMN take_at TO take_at_live_casino
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to rename take_at column: %v", err))
	}
	log.Printf("✅ Renamed take_at to take_at_live_casino")

	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_loser 
		RENAME COLUMN taken_price TO taken_price_live_casino
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to rename taken_price column: %v", err))
	}
	log.Printf("✅ Renamed taken_price to taken_price_live_casino")

	// Add new columns for slot and sport
	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_loser 
		ADD COLUMN return_price_slot DECIMAL(15,2) DEFAULT 0.00,
		ADD COLUMN take_at_slot TIMESTAMP NULL,
		ADD COLUMN taken_price_slot DECIMAL(15,2) DEFAULT 0.00,
		ADD COLUMN return_price_sport DECIMAL(15,2) DEFAULT 0.00,
		ADD COLUMN taken_at_sport TIMESTAMP NULL,
		ADD COLUMN taken_price_sport DECIMAL(15,2) DEFAULT 0.00
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add new columns: %v", err))
	}
	log.Printf("✅ Added new columns for slot and sport game types")

	log.Println("✅ Migration 38_update_return_turn_columns completed successfully")
}

func (m *UpdateReturnTurnColumns) Down(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Remove new columns
	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_loser 
		DROP COLUMN IF EXISTS return_price_slot,
		DROP COLUMN IF EXISTS take_at_slot,
		DROP COLUMN IF EXISTS taken_price_slot,
		DROP COLUMN IF EXISTS return_price_sport,
		DROP COLUMN IF EXISTS taken_at_sport,
		DROP COLUMN IF EXISTS taken_price_sport
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to remove new columns: %v", err))
	}
	log.Printf("✅ Removed new slot and sport columns")

	// Rename columns back to original names
	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_loser 
		RENAME COLUMN return_price_live_casino TO return_price
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to rename return_price_live_casino back: %v", err))
	}
	log.Printf("✅ Renamed return_price_live_casino back to return_price")

	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_loser 
		RENAME COLUMN take_at_live_casino TO take_at
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to rename take_at_live_casino back: %v", err))
	}
	log.Printf("✅ Renamed take_at_live_casino back to take_at")

	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_loser 
		RENAME COLUMN taken_price_live_casino TO taken_price
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to rename taken_price_live_casino back: %v", err))
	}
	log.Printf("✅ Renamed taken_price_live_casino back to taken_price")

	log.Println("✅ Migration rollback 38_update_return_turn_columns completed successfully")
}
