package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type AddCreditExpireDaysToReturnTurn struct{}

func (m *AddCreditExpireDaysToReturnTurn) GetName() string {
	return "36_add_credit_expire_days_to_return_turn"
}

func (m *AddCreditExpireDaysToReturnTurn) Up(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Add credit_expire_days column to return_turn_setting table
	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_setting 
		ADD COLUMN IF NOT EXISTS credit_expire_days INTEGER DEFAULT 7
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add credit_expire_days column: %v", err))
	}
	log.Printf("Migration: Added credit_expire_days column to return_turn_setting table with default value 7")

	// Update existing rows to have credit_expire_days = 7
	_, err = con.Exec(ctx, `
		UPDATE return_turn_setting 
		SET credit_expire_days = 7 
		WHERE credit_expire_days IS NULL
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to update credit_expire_days values: %v", err))
	}
	log.Printf("Migration: Updated existing rows with credit_expire_days = 7")

	log.Println("Migration 36_add_credit_expire_days_to_return_turn completed successfully")
}

func (m *AddCreditExpireDaysToReturnTurn) Down(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Drop credit_expire_days column from return_turn_setting table
	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_setting 
		DROP COLUMN IF EXISTS credit_expire_days
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop credit_expire_days column: %v", err))
	}
	log.Printf("Migration: Dropped credit_expire_days column from return_turn_setting table")

	log.Println("Migration rollback 36_add_credit_expire_days_to_return_turn completed successfully")
}
