package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type AddIconUrlToBlockchainNetworks struct{}

func (m *AddIconUrlToBlockchainNetworks) GetName() string {
	return "AddIconUrlToBlockchainNetworks"
}

func (m *AddIconUrlToBlockchainNetworks) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Adding icon_url column to blockchain_networks table")

	// Add icon_url column as nullable VARCHAR(500)
	_, err = con.Exec(ctx, `
		ALTER TABLE blockchain_networks 
		ADD COLUMN IF NOT EXISTS icon_url VARCHAR(500)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add icon_url column to blockchain_networks: %v", err))
	}

	log.Printf("Migration completed: icon_url column added to blockchain_networks successfully")
}

func (m *AddIconUrlToBlockchainNetworks) Down(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration rollback: Removing icon_url column from blockchain_networks table")

	// Remove icon_url column
	_, err = con.Exec(ctx, `
		ALTER TABLE blockchain_networks 
		DROP COLUMN IF EXISTS icon_url
	`)
	if err != nil {
		log.Printf("Warning: Failed to drop icon_url column: %v", err)
	}

	log.Printf("Migration rollback completed")
}
