package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type CreateInitTables struct{}

func (m *CreateInitTables) GetName() string {
	return "CreateInitTables"
}

func (m *CreateInitTables) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public" // default schema
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Create schema if not exists (except public)
	if schemaName != "public" {
		log.Printf("Migration: Creating schema '%s'", schemaName)
		_, err := con.Exec(ctx, fmt.Sprintf("CREATE SCHEMA IF NOT EXISTS %s", schemaName))
		if err != nil {
			panic(fmt.Sprintf("Failed to create schema %s: %v", schemaName, err))
		}
		log.Printf("Migration: Schema '%s' created successfully", schemaName)
	}

	// Set search_path to use the specified schema
	log.Printf("Migration: Setting search_path to '%s'", schemaName)
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}
	log.Printf("Migration: search_path set successfully")

	// Verify current schema
	var currentSchema string
	err = con.QueryRow(ctx, "SELECT current_schema()").Scan(&currentSchema)
	if err == nil {
		log.Printf("Migration: Current schema is '%s'", currentSchema)
	}

	// ===== USER SYSTEM TABLES =====

	// Create user_roles table for agent/player roles
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS user_roles (
			id SERIAL PRIMARY KEY,
			position INT NULL,
			name VARCHAR(255) NULL,
			display_name VARCHAR(255) NULL,
			is_2fa BOOLEAN DEFAULT FALSE,
			is_lock_ip BOOLEAN DEFAULT FALSE,
			is_enable BOOLEAN DEFAULT TRUE,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// ===== PERMISSION SYSTEM TABLES =====

	// Create permission_groups table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS permission_groups (
			id SERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			key VARCHAR(100) NOT NULL UNIQUE,
			description TEXT NULL,
			position INT NULL,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create permissions table with hierarchical structure
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS permissions (
			id SERIAL PRIMARY KEY,
			group_id INT NOT NULL,
			parent_id INT NULL,
			name VARCHAR(255) NOT NULL,
			key VARCHAR(100) NOT NULL UNIQUE,
			description TEXT NULL,
			position INT NULL,
			level INT NOT NULL DEFAULT 1,
			supports_create BOOLEAN NOT NULL DEFAULT TRUE,
			supports_view BOOLEAN NOT NULL DEFAULT TRUE,
			supports_edit BOOLEAN NOT NULL DEFAULT TRUE,
			supports_delete BOOLEAN NOT NULL DEFAULT TRUE,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (group_id) REFERENCES permission_groups(id) ON DELETE CASCADE,
			FOREIGN KEY (parent_id) REFERENCES permissions(id) ON DELETE CASCADE
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create user_role_permissions table with key-based relationships for permissions
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS user_role_permissions (
			id SERIAL PRIMARY KEY,
			user_role_id INT NOT NULL,
			permission_key VARCHAR(100) NOT NULL,
			can_create BOOLEAN NOT NULL DEFAULT FALSE,
			can_view BOOLEAN NOT NULL DEFAULT FALSE,
			can_edit BOOLEAN NOT NULL DEFAULT FALSE,
			can_delete BOOLEAN NOT NULL DEFAULT FALSE,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (user_role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
			FOREIGN KEY (permission_key) REFERENCES permissions(key) ON DELETE CASCADE,
			UNIQUE(user_role_id, permission_key)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create users table (for agents/players)
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS users (
			id SERIAL PRIMARY KEY,
			username VARCHAR(50) UNIQUE NOT NULL,
			first_name VARCHAR(100) NOT NULL,
			last_name VARCHAR(100) NOT NULL,
			password VARCHAR(255) NOT NULL,
			user_role_name VARCHAR(50) NULL,
			user_role_id INT NOT NULL,
			agent_code VARCHAR(50) UNIQUE,
			user_type VARCHAR(20) DEFAULT 'agent' CHECK (user_type IN ('agent', 'player')),
			last_online TIMESTAMP NULL,
			last_login TIMESTAMP NULL,
			is_enable BOOLEAN DEFAULT FALSE,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
			FOREIGN KEY (user_role_id) REFERENCES user_roles(id) ON DELETE RESTRICT
		)
	`)
	if err != nil {
		panic(err)
	}

	// ===== MEMBER SYSTEM TABLES =====

	// Create member_group_types table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS member_group_types (
			id SERIAL PRIMARY KEY,
			position INT NULL,
			name VARCHAR(50) NOT NULL,
			show_in_lobby BOOLEAN DEFAULT FALSE,
			badge_bg_color VARCHAR(7) NOT NULL,
			badge_border_color VARCHAR(7) NOT NULL,
			image1 TEXT,
			image2 TEXT,
			bg_image TEXT,
			vip_enabled BOOLEAN DEFAULT FALSE,
			vip_personal_customer_service BOOLEAN DEFAULT FALSE,
			vip_max_daily_withdraw BOOLEAN DEFAULT FALSE,
			vip_event_participation BOOLEAN DEFAULT FALSE,
			bonus_enabled BOOLEAN DEFAULT FALSE,
			bonus_birthday BOOLEAN DEFAULT FALSE,
			bonus_level_maintenance BOOLEAN DEFAULT FALSE,
			bonus_level_upgrade BOOLEAN DEFAULT FALSE,
			bonus_festival BOOLEAN DEFAULT FALSE,
			cashback_enabled BOOLEAN DEFAULT FALSE,
			cashback_sports BOOLEAN DEFAULT FALSE,
			cashback_casino BOOLEAN DEFAULT FALSE,
			cashback_fishing BOOLEAN DEFAULT FALSE,
			cashback_slot BOOLEAN DEFAULT FALSE,
			cashback_lottery BOOLEAN DEFAULT FALSE,
			cashback_card BOOLEAN DEFAULT FALSE,
			cashback_other BOOLEAN DEFAULT FALSE,
			upgrade_betting_amount DECIMAL(15, 2) DEFAULT 0.00,
			upgrade_deposit_amount DECIMAL(15, 2) DEFAULT 0.00,
			upgrade_calculation_type VARCHAR(10) DEFAULT 'day',
			upgrade_days INT DEFAULT 1,
			upgrade_condition_type VARCHAR(30) DEFAULT 'betting_amount',
			downgrade_betting_amount DECIMAL(15, 2) DEFAULT 0.00,
			downgrade_deposit_amount DECIMAL(15, 2) DEFAULT 0.00,
			downgrade_calculation_type VARCHAR(10) DEFAULT 'day',
			downgrade_days INT DEFAULT 1,
			downgrade_condition_type VARCHAR(30) DEFAULT 'betting_amount',
			status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create member_groups table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS member_groups (
			id SERIAL PRIMARY KEY,
			code VARCHAR(20) UNIQUE NOT NULL,
			name VARCHAR(100) NOT NULL,
			member_group_type_id INT NULL,
			commission_group_id INT NOT NULL,
			min_deposit DECIMAL(15, 2) DEFAULT 0.00,
			min_withdraw DECIMAL(15, 2) DEFAULT 0.00,
			max_deposit DECIMAL(15, 2) DEFAULT 0.00,
			is_default BOOLEAN DEFAULT FALSE,
			is_vip BOOLEAN DEFAULT FALSE,
			daily_withdraw_limit INT NULL ,
			daily_withdraw_amount_limit DECIMAL(15, 2) NULL,
			deposit_turnover_type VARCHAR(20) DEFAULT 'nil',
			deposit_turnover_amount DECIMAL(15, 2) DEFAULT 0.00 ,
			deposit_turnover_release_type VARCHAR(20) DEFAULT 'fixed' ,
			deposit_turnover_release_amount DECIMAL(15, 2) DEFAULT 0.00,
			calculate_min_deposit BOOLEAN DEFAULT FALSE,
			image TEXT,
			status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (member_group_type_id) REFERENCES member_group_types(id) ON DELETE RESTRICT
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create members table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS members (
			id SERIAL PRIMARY KEY,
			username VARCHAR(50) UNIQUE NOT NULL,
			password VARCHAR(255) NULL,
			game_username VARCHAR(50) NULL,
			game_password VARCHAR(255) NULL,
			first_name VARCHAR(100) NULL,
			last_name VARCHAR(100) NULL,
			phone VARCHAR(50) NULL,
			gender VARCHAR(10) CHECK (gender IN ('male', 'female', 'other')) NULL,
			tw_username VARCHAR(50) NULL,
			line_id VARCHAR(50) NULL,
			bank_code VARCHAR(50) NULL,
			bank_number VARCHAR(50) NULL,
			avatar VARCHAR(255) NULL,
			login_status BOOLEAN DEFAULT FALSE,
			operate_status BOOLEAN DEFAULT FALSE,
			register_status BOOLEAN DEFAULT FALSE,
			balance FLOAT NOT NULL DEFAULT 0,
			refer_code VARCHAR(8) NOT NULL DEFAULT '',
			refer_user_id VARCHAR(255) NULL,
			last_online TIMESTAMP NULL,
			is_enable BOOLEAN DEFAULT FALSE,
			register_refer_code VARCHAR(8) NULL DEFAULT '',
			register_ip VARCHAR(100) NULL,
			last_login_ip VARCHAR(50) NULL,
			last_login_user_agent TEXT NULL,
			last_login_device TEXT NULL,
			address_province VARCHAR(255) NULL,
			address_amphoe VARCHAR(255) NULL,
			address_district VARCHAR(255) NULL,
			address_postcode VARCHAR(255) NULL,
			address_detail VARCHAR(255) NULL,
			birth_date DATE NULL,
			session_id VARCHAR(255) NULL,
			twofa_secret VARCHAR(255) NULL,
			twofa_status INT NULL,
			twofa_verify_count INT NULL,
			member_group_id INT NULL,
			referral_group_id INT NULL,
			show_partner_info BOOLEAN DEFAULT FALSE,
			platform_id VARCHAR(50) NULL,
			is_partner BOOLEAN DEFAULT FALSE,
			partner_remark TEXT NULL,
			channel_id INT NULL,
			delete_by VARCHAR(255) NOT NULL,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'banned')),
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (member_group_id) REFERENCES member_groups(id) ON DELETE RESTRICT
		)
	`)
	if err != nil {
		panic(err)
	}

	// ===== SYSTEM TABLES =====

	// Create user_audit_logs table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS user_audit_logs (
			id SERIAL PRIMARY KEY,
			user_id int NOT NULL,
			username VARCHAR(50) NOT NULL,
			action VARCHAR(50) NOT NULL,
			old_values JSONB NULL,
			new_values JSONB NULL,
			changed_by int NOT NULL,
			changed_by_name VARCHAR(50) NOT NULL,
			changed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create allowed_ips table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS allowed_ips (
			id VARCHAR(36) PRIMARY KEY,
			ip_address VARCHAR(50) NOT NULL UNIQUE,
			description TEXT NULL,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create system_settings table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS system_settings (
			id SERIAL PRIMARY KEY,
			key VARCHAR(100) NOT NULL UNIQUE,
			value TEXT NOT NULL,
			description TEXT NULL,
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create user_2fa table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS user_2fa (
			user_id INT PRIMARY KEY,
			secret_key VARCHAR(64) NOT NULL,
			is_enabled BOOLEAN NOT NULL DEFAULT false,
			backup_codes TEXT[] NULL,
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create login_attempts table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS login_attempts (
			id SERIAL PRIMARY KEY,
			username VARCHAR(50) NOT NULL,
			ip_address VARCHAR(50) NOT NULL,
			success BOOLEAN NOT NULL DEFAULT false,
			user_agent TEXT NULL,
			device_type VARCHAR(50) NULL,
			browser VARCHAR(100) NULL,
			browser_version VARCHAR(50) NULL,
			os VARCHAR(100) NULL,
			os_version VARCHAR(50) NULL,
			platform VARCHAR(50) NULL,
			is_mobile BOOLEAN DEFAULT FALSE,
			is_tablet BOOLEAN DEFAULT FALSE,
			is_desktop BOOLEAN DEFAULT FALSE,
			is_admin BOOLEAN DEFAULT FALSE,
			is_member BOOLEAN DEFAULT FALSE,
			is_cleared BOOLEAN DEFAULT FALSE,
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create banners table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS banners (
			id SERIAL PRIMARY KEY,
			position INT NULL,
			name VARCHAR(255) NULL,
			type VARCHAR(20) NOT NULL CHECK (type IN ('image', 'link')),
			link_url TEXT NULL,
			image_url TEXT NULL,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			deleted_by_username VARCHAR(20) NULL,
			deleted_at TIMESTAMP NULL,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create languages table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS languages (
			id SERIAL PRIMARY KEY,
			code VARCHAR(10) UNIQUE NOT NULL,
			name VARCHAR(100) NOT NULL,
			native_name VARCHAR(100) NULL,
			is_default BOOLEAN DEFAULT FALSE,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert default system settings
	_, err = con.Exec(ctx, `
		INSERT INTO system_settings (key, value, description) VALUES
		('max_login_attempts', '0', 'Maximum number of failed login attempts allowed. Set to 0 for unlimited attempts.'),
		('enable_deposit', 'true', 'Enable deposit functionality'),
		('enable_deposit_remark', '', 'Remark for deposit setting'),
		('enable_withdraw', 'true', 'Enable withdraw functionality'),
		('enable_withdraw_remark', '', 'Remark for withdraw setting'),
		('enable_game', 'true', 'Enable game functionality'),
		('enable_game_remark', '', 'Remark for game setting'),
		('enable_truemoney', 'false', 'Enable Truemoney payment'),
		('enable_truemoney_remark', '', 'Remark for Truemoney setting'),
		('enable_member_register', 'true', 'Enable member registration'),
		('enable_member_register_remark', '', 'Remark for member registration setting'),
		('enable_slip_verify', 'false', 'Enable slip verification'),
		('enable_slip_verify_remark', '', 'Remark for slip verification'),
		('enable_payment_gateway_only', 'false', 'Use payment gateway only'),
		('enable_payment_gateway_only_remark', '', 'Remark for payment gateway only'),
		('enable_hide_deposit_channel', 'false', 'Hide deposit channels'),
		('enable_hide_deposit_channel_remark', '', 'Remark for hide deposit channel'),
		('enable_show_deposit_decimal', 'false', 'Show decimal places in deposit'),
		('enable_show_deposit_decimal_remark', '', 'Remark for show deposit decimal'),
		('enable_withdraw_pincode', 'false', 'Require pincode for withdraw'),
		('enable_withdraw_pincode_remark', '', 'Remark for withdraw pincode'),
		('player_attempt_limit', '0', 'Maximum player attempts (0 = unlimited)'),
		('timeout_deposit', '15', 'Deposit timeout in minutes'),
		('timeout_withdraw', '15', 'Withdraw timeout in minutes'),
		('enable_gg_analytics', 'false', 'Enable Google Analytics'),
		('gg_analytics_key', '', 'Google Analytics tracking ID'),
		('enable_fb_pixel_id', 'false', 'Enable Facebook Pixel'),
		('fb_pixel_id', '', 'Facebook Pixel ID'),
		('enable_setting_username', 'false', 'Allow username changes'),
		('enable_setting_username_remark', '', 'Remark for username setting'),
		('enable_setting_gender', 'false', 'Allow gender setting'),
		('enable_setting_gender_remark', '', 'Remark for gender setting'),
		('otp_option', 'none', 'OTP method (none, sms, email, etc.)'),
		('enable_otp_bypass', 'false', 'Allow OTP bypass'),
		('enable_otp_bypass_remark', '', 'Remark for OTP bypass'),
		('captcha_config_public', '', 'reCAPTCHA public key'),
		('captcha_config_private', '', 'reCAPTCHA private key'),
		('admin_lang', 'th', 'Admin interface language'),
		('player_lang', 'th', 'Player interface language'),
		('seo_site_title', '', 'Website site title'),
		('seo_title', '', 'SEO page title'),
		('seo_meta_description', '', 'SEO meta description'),
		('seo_favicon', '', 'Website favicon URL'),
		('seo_featured_image', '', 'SEO featured image URL')
		ON CONFLICT (key) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// ===== CREATE INDEXES =====
	indexes := []string{
		// User indexes
		"CREATE INDEX IF NOT EXISTS idx_users_id ON users (id)",
		"CREATE INDEX IF NOT EXISTS idx_users_status ON users (status)",
		"CREATE INDEX IF NOT EXISTS idx_users_username ON users (username)",
		"CREATE INDEX IF NOT EXISTS idx_users_user_role_id ON users (user_role_id)",
		"CREATE INDEX IF NOT EXISTS idx_users_agent_code ON users (agent_code)",
		"CREATE INDEX IF NOT EXISTS idx_users_user_type ON users (user_type)",
		"CREATE INDEX IF NOT EXISTS idx_users_created_at ON users (created_at)",

		// User audit logs indexes
		"CREATE INDEX IF NOT EXISTS idx_user_audit_logs_user_id ON user_audit_logs (user_id)",
		"CREATE INDEX IF NOT EXISTS idx_user_audit_logs_action ON user_audit_logs (action)",
		"CREATE INDEX IF NOT EXISTS idx_user_audit_logs_changed_by ON user_audit_logs (changed_by)",
		"CREATE INDEX IF NOT EXISTS idx_user_audit_logs_changed_at ON user_audit_logs (changed_at DESC)",
		"CREATE INDEX IF NOT EXISTS idx_user_audit_logs_user_action ON user_audit_logs (user_id, action)",

		// Allowed IPs indexes
		"CREATE INDEX IF NOT EXISTS idx_allowed_ips_ip_address ON allowed_ips (ip_address)",
		"CREATE INDEX IF NOT EXISTS idx_allowed_ips_status ON allowed_ips (status)",
		"CREATE INDEX IF NOT EXISTS idx_allowed_ips_created_at ON allowed_ips (created_at DESC)",

		// System settings indexes
		"CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings (key)",

		// User 2FA indexes
		"CREATE INDEX IF NOT EXISTS idx_user_2fa_is_enabled ON user_2fa (is_enabled)",
		"CREATE INDEX IF NOT EXISTS idx_user_2fa_created_at ON user_2fa (created_at DESC)",

		// Login attempts indexes
		"CREATE INDEX IF NOT EXISTS idx_login_attempts_username ON login_attempts (username)",
		"CREATE INDEX IF NOT EXISTS idx_login_attempts_success ON login_attempts (success)",
		"CREATE INDEX IF NOT EXISTS idx_login_attempts_created_at ON login_attempts (created_at DESC)",
		"CREATE INDEX IF NOT EXISTS idx_login_attempts_username_success ON login_attempts (username, success)",
		"CREATE INDEX IF NOT EXISTS idx_login_attempts_username_created_at ON login_attempts (username, created_at DESC)",

		// Banners indexes
		"CREATE INDEX IF NOT EXISTS idx_banners_status ON banners (status)",
		"CREATE INDEX IF NOT EXISTS idx_banners_type ON banners (type)",
		"CREATE INDEX IF NOT EXISTS idx_banners_position ON banners (position)",
		"CREATE INDEX IF NOT EXISTS idx_banners_created_at ON banners (created_at)",
		"CREATE INDEX IF NOT EXISTS idx_banners_deleted_at ON banners (deleted_at)",

		// Permission indexes
		"CREATE INDEX IF NOT EXISTS idx_permission_groups_key ON permission_groups (key)",
		"CREATE INDEX IF NOT EXISTS idx_permission_groups_position ON permission_groups (position)",
		"CREATE INDEX IF NOT EXISTS idx_permission_groups_status ON permission_groups (status)",
		"CREATE INDEX IF NOT EXISTS idx_permissions_key ON permissions (key)",
		"CREATE INDEX IF NOT EXISTS idx_permissions_group_id ON permissions (group_id)",
		"CREATE INDEX IF NOT EXISTS idx_permissions_parent_id ON permissions (parent_id)",
		"CREATE INDEX IF NOT EXISTS idx_permissions_position ON permissions (position)",
		"CREATE INDEX IF NOT EXISTS idx_permissions_level ON permissions (level)",
		"CREATE INDEX IF NOT EXISTS idx_permissions_status ON permissions (status)",
		"CREATE INDEX IF NOT EXISTS idx_user_role_permissions_user_role_id ON user_role_permissions (user_role_id)",
		"CREATE INDEX IF NOT EXISTS idx_user_role_permissions_permission_key ON user_role_permissions (permission_key)",
		"CREATE INDEX IF NOT EXISTS idx_user_role_permissions_status ON user_role_permissions (status)",

		// System indexes
		"CREATE INDEX IF NOT EXISTS idx_languages_code ON languages (code)",
		"CREATE INDEX IF NOT EXISTS idx_languages_status ON languages (status)",
	}

	for _, indexSQL := range indexes {
		_, err = con.Exec(ctx, indexSQL)
		if err != nil {
			panic(err)
		}
	}
}

func (m *CreateInitTables) Down(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Drop tables in reverse order (considering dependencies)
	tables := []string{
		"DROP TABLE IF EXISTS members CASCADE",
		"DROP TABLE IF EXISTS member_groups CASCADE",
		"DROP TABLE IF EXISTS member_group_types CASCADE",
		"DROP TABLE IF EXISTS users CASCADE",
		"DROP TABLE IF EXISTS user_role_permissions CASCADE",
		"DROP TABLE IF EXISTS user_roles CASCADE",
		"DROP TABLE IF EXISTS permissions CASCADE",
		"DROP TABLE IF EXISTS permission_groups CASCADE",
		"DROP TABLE IF EXISTS languages CASCADE",
		"DROP TABLE IF EXISTS allowed_ips CASCADE",
		"DROP TABLE IF EXISTS banners CASCADE",
		"DROP TABLE IF EXISTS login_attempts CASCADE",
		"DROP TABLE IF EXISTS system_settings CASCADE",
		"DROP TABLE IF EXISTS user_2fa CASCADE",
		"DROP TABLE IF EXISTS user_audit_logs CASCADE",
	}

	for _, dropSQL := range tables {
		_, err := con.Exec(ctx, dropSQL)
		if err != nil {
			panic(err)
		}
	}

	// Drop schema if not public and if empty
	if schemaName != "public" {
		_, err = con.Exec(ctx, fmt.Sprintf("DROP SCHEMA IF EXISTS %s", schemaName))
		// Ignore error if schema is not empty or doesn't exist
	}
}
