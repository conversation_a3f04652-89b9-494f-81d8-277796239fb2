package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type CreateCryptoDepositSystem struct{}

func (m *CreateCryptoDepositSystem) GetName() string {
	return "CreateCryptoDepositSystem"
}

func (m *CreateCryptoDepositSystem) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Creating crypto deposit system")

	// Insert crypto payment gateway account (check if exists first)
	var existingCount int
	err = con.QueryRow(ctx, `SELECT COUNT(*) FROM payment_gateway_account WHERE code = 'CRYPTO'`).Scan(&existingCount)
	if err != nil {
		panic(fmt.Sprintf("Failed to check existing crypto account: %v", err))
	}

	if existingCount == 0 {
		_, err = con.Exec(ctx, `
			INSERT INTO payment_gateway_account (
				account_name,
				code,
				provider,
				merchant_code,
				api_key,
				secret_key,
				base_url,
				timeout_seconds,
				max_retries,
				retry_delay_seconds,
				enable_request_log,
				log_response_body,
				enable_debug,
				is_deposit,
				is_withdraw,
				is_transfer,
				active,
				inactive,
				created_at,
				updated_at
			) VALUES (
				'Crypto',
				'CRYPTO',
				'BLOCKCHAIN',
				'BLOCKCHAIN',
				'',
				'',
				'https://api.thirdweb.com/v1',
				60,
				3,
				2,
				true,
				false,
				false,
				true,
				false,
				false,
				true,
				false,
				CURRENT_TIMESTAMP,
				CURRENT_TIMESTAMP
			)
		`)
		if err != nil {
			panic(fmt.Sprintf("Failed to insert crypto payment gateway account: %v", err))
		}
		log.Printf("Crypto payment gateway account created successfully")
	} else {
		log.Printf("Crypto payment gateway account already exists")
	}

	// Create blockchain_networks table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS blockchain_networks (
			id SERIAL PRIMARY KEY,
			chain_id INTEGER NOT NULL UNIQUE,
			network_name VARCHAR(50) NOT NULL,
			network_type VARCHAR(20) DEFAULT 'testnet',
			rpc_url VARCHAR(500),
			explorer_url VARCHAR(500),
			native_currency_symbol VARCHAR(10) DEFAULT 'ETH',
			is_active BOOLEAN DEFAULT TRUE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create blockchain_networks table: %v", err))
	}

	// Insert default networks
	_, err = con.Exec(ctx, `
		INSERT INTO blockchain_networks (chain_id, network_name, network_type, rpc_url, explorer_url, native_currency_symbol) VALUES
		(84532, 'Base Sepolia', 'testnet', 'https://sepolia.base.org', 'https://sepolia-explorer.base.org', 'ETH'),
		(137, 'Polygon Mainnet', 'mainnet', 'https://polygon-rpc.com', 'https://polygonscan.com', 'MATIC'),
		(80001, 'Polygon Mumbai', 'testnet', 'https://rpc-mumbai.maticvigil.com', 'https://mumbai.polygonscan.com', 'MATIC'),
		(1, 'Ethereum Mainnet', 'mainnet', 'https://mainnet.infura.io', 'https://etherscan.io', 'ETH'),
		(11155111, 'Ethereum Sepolia', 'testnet', 'https://sepolia.infura.io', 'https://sepolia.etherscan.io', 'ETH')
		ON CONFLICT (chain_id) DO NOTHING
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to insert blockchain networks: %v", err))
	}

	// Create backend_wallets table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS backend_wallets (
			id SERIAL PRIMARY KEY,
			wallet_address VARCHAR(42) NOT NULL UNIQUE,
			chain_id INTEGER NOT NULL,
			wallet_name VARCHAR(100),
			current_eth_balance DECIMAL(18,8) DEFAULT 0,
			current_token_balance DECIMAL(18,8) DEFAULT 0,
			minimum_eth_threshold DECIMAL(18,8) DEFAULT 0.01,
			maximum_token_threshold DECIMAL(18,8) DEFAULT 1000,
			last_balance_check TIMESTAMP,
			is_active BOOLEAN DEFAULT TRUE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			
			CONSTRAINT fk_backend_wallets_chain 
			FOREIGN KEY (chain_id) REFERENCES blockchain_networks(chain_id)
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create backend_wallets table: %v", err))
	}

	// Insert default backend wallets
	_, err = con.Exec(ctx, `
		INSERT INTO backend_wallets (wallet_address, chain_id, wallet_name) VALUES
		('******************************************', 84532, 'Base Sepolia Backend'),
		('******************************************', 137, 'Polygon Mainnet Backend')
		ON CONFLICT (wallet_address) DO NOTHING
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to insert backend wallets: %v", err))
	}

	// Create crypto_deposit_logs table for detailed logging
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS crypto_deposit_logs (
			id SERIAL PRIMARY KEY,
			transaction_id VARCHAR(255) NOT NULL,
			step_number INTEGER NOT NULL CHECK (step_number IN (1, 2)),
			event_type VARCHAR(50) NOT NULL,
			transaction_hash VARCHAR(66),
			block_number BIGINT,
			gas_used BIGINT,
			gas_price_gwei DECIMAL(10,2),
			event_data JSONB,
			error_details TEXT,
			timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create crypto_deposit_logs table: %v", err))
	}

	// Add blockchain_data column to payment_gateway_transactions if not exists
	_, err = con.Exec(ctx, `
		ALTER TABLE payment_gateway_transactions 
		ADD COLUMN IF NOT EXISTS blockchain_data JSONB
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add blockchain_data column: %v", err))
	}

	// Create chain_tokens table for conversion rates
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS chain_tokens (
			id SERIAL PRIMARY KEY,
			blockchain_network_id INTEGER NOT NULL,
			token_contract VARCHAR(42) NOT NULL,
			token_symbol VARCHAR(10) NOT NULL,
			token_decimals INTEGER DEFAULT 18,
			rate_to_thb DECIMAL(10,4) NOT NULL DEFAULT 33.0000,
			is_active BOOLEAN DEFAULT TRUE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			
			CONSTRAINT fk_chain_tokens_network 
			FOREIGN KEY (blockchain_network_id) REFERENCES blockchain_networks(id),
			
			UNIQUE(blockchain_network_id, token_contract)
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create chain_tokens table: %v", err))
	}

	// Insert default token rates
	_, err = con.Exec(ctx, `
		INSERT INTO chain_tokens (blockchain_network_id, token_contract, token_symbol, token_decimals, rate_to_thb) 
		SELECT 
			bn.id,
			CASE 
				WHEN bn.chain_id = 84532 THEN '0x6897630c6634260393641746450aCfE14655f767'
				WHEN bn.chain_id = 137 THEN '0x2791Bca1f2de4661ED88A30C99A7a9449Aa84174'
				WHEN bn.chain_id = 80001 THEN '0x9c3C9283D3e44854697Cd22D3Faa240Cfb032889'
				WHEN bn.chain_id = 1 THEN '0xA0b86a33E6441c5a35fD3A27e11D98a6A03e81B8'
				WHEN bn.chain_id = 11155111 THEN '0x1c7D4B196Cb0C7B01d743Fbc6116a902379C7238'
			END as token_contract,
			'USDC' as token_symbol,
			CASE 
				WHEN bn.chain_id = 137 THEN 6
				ELSE 18
			END as token_decimals,
			33.5000 as rate_to_thb
		FROM blockchain_networks bn
		WHERE bn.chain_id IN (84532, 137, 80001, 1, 11155111)
		ON CONFLICT (blockchain_network_id, token_contract) DO NOTHING
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to insert default chain tokens: %v", err))
	}

	// Create indexes for better performance
	_, err = con.Exec(ctx, `
		CREATE INDEX IF NOT EXISTS idx_payment_gateway_transactions_provider 
		ON payment_gateway_transactions(provider)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create provider index: %v", err))
	}

	_, err = con.Exec(ctx, `
		CREATE INDEX IF NOT EXISTS idx_payment_gateway_transactions_blockchain_data 
		ON payment_gateway_transactions USING GIN (blockchain_data)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create blockchain_data index: %v", err))
	}

	_, err = con.Exec(ctx, `
		CREATE INDEX IF NOT EXISTS idx_payment_gateway_transactions_customer_username 
		ON payment_gateway_transactions(customer_username)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create customer_username index: %v", err))
	}

	_, err = con.Exec(ctx, `
		CREATE INDEX IF NOT EXISTS idx_chain_tokens_network_contract 
		ON chain_tokens(blockchain_network_id, token_contract)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create chain_tokens network_contract index: %v", err))
	}

	_, err = con.Exec(ctx, `
		CREATE INDEX IF NOT EXISTS idx_chain_tokens_active 
		ON chain_tokens(is_active)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create chain_tokens active index: %v", err))
	}

	_, err = con.Exec(ctx, `
		CREATE INDEX IF NOT EXISTS idx_crypto_deposit_logs_transaction_id 
		ON crypto_deposit_logs(transaction_id)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create crypto_deposit_logs transaction_id index: %v", err))
	}

	_, err = con.Exec(ctx, `
		CREATE INDEX IF NOT EXISTS idx_crypto_deposit_logs_timestamp 
		ON crypto_deposit_logs(timestamp DESC)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create indexes: %v", err))
	}

	log.Printf("Migration completed: Crypto deposit system created successfully")
}

func (m *CreateCryptoDepositSystem) Down(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration rollback: Dropping crypto deposit system")

	// Drop indexes
	_, err = con.Exec(ctx, `
		DROP INDEX IF EXISTS idx_payment_gateway_transactions_provider;
		DROP INDEX IF EXISTS idx_payment_gateway_transactions_blockchain_data;
		DROP INDEX IF EXISTS idx_payment_gateway_transactions_customer_username;
	`)
	if err != nil {
		log.Printf("Warning: Failed to drop indexes: %v", err)
	}

	// Remove blockchain_data column
	_, err = con.Exec(ctx, `
		ALTER TABLE payment_gateway_transactions 
		DROP COLUMN IF EXISTS blockchain_data
	`)
	if err != nil {
		log.Printf("Warning: Failed to drop blockchain_data column: %v", err)
	}

	// Drop tables in reverse order
	_, err = con.Exec(ctx, `
		DROP TABLE IF EXISTS crypto_deposit_logs;
		DROP TABLE IF EXISTS chain_tokens;
		DROP TABLE IF EXISTS backend_wallets;
		DROP TABLE IF EXISTS blockchain_networks;
	`)
	if err != nil {
		log.Printf("Warning: Failed to drop tables: %v", err)
	}

	// Remove crypto payment gateway account
	_, err = con.Exec(ctx, `
		DELETE FROM payment_gateway_account WHERE code = 'CRYPTO'
	`)
	if err != nil {
		log.Printf("Warning: Failed to remove crypto payment gateway account: %v", err)
	}

	log.Printf("Migration rollback completed")
}
