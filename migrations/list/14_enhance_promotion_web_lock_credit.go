package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type EnhancePromotionWebLockCredit struct{}

func (m *EnhancePromotionWebLockCredit) GetName() string {
	return "EnhancePromotionWebLockCredit"
}

func (m *EnhancePromotionWebLockCredit) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Enhancing promotion web lock credit system")

	// Create user_withdraw_lock_credit_type table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS user_withdraw_lock_credit_type (
			id BIGSERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			label_th VARCHAR(255),
			label_en VARCHAR(255),
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create user_withdraw_lock_credit_type table: %v", err))
	}

	// Insert default withdraw lock credit types
	_, err = con.Exec(ctx, `
		INSERT INTO user_withdraw_lock_credit_type (id, name, label_th, label_en) VALUES
		(1, 'PROMOTION', 'โปรโมชั่น', 'Promotion')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to insert user_withdraw_lock_credit_type data: %v", err))
	}

	// Create lock_credit_withdraw table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS lock_credit_withdraw (
			id BIGSERIAL PRIMARY KEY,
			user_id BIGINT NOT NULL,
			ref_id BIGINT,
			detail TEXT,
			user_withdraw_lock_credit_type_id BIGINT NOT NULL,
			credit_more_than DECIMAL(15,2),
			allow_withdraw_amount DECIMAL(15,2),
			withdraw_amount DECIMAL(15,2),
			pull_credit_amount DECIMAL(15,2),
			is_locked BOOLEAN DEFAULT TRUE,
			is_pull_credit BOOLEAN DEFAULT FALSE,
			approved_by_id BIGINT,
			approved_at TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_lock_credit_withdraw_user_type 
				FOREIGN KEY (user_withdraw_lock_credit_type_id) 
				REFERENCES user_withdraw_lock_credit_type(id)
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create lock_credit_withdraw table: %v", err))
	}

	// Update existing promotion_web_lock_credit table to match migration specs
	_, err = con.Exec(ctx, `
		ALTER TABLE promotion_web_lock_credit 
		ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add updated_at to promotion_web_lock_credit: %v", err))
	}

	// Check if table needs to be renamed
	_, err = con.Exec(ctx, `
		DO $$
		BEGIN
			IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'promotion_web_lock_credit') THEN
				ALTER TABLE promotion_web_lock_credit RENAME TO lock_credit_promotion;
			END IF;
		END $$;
	`)
	if err != nil {
		log.Printf("Warning: Could not rename promotion_web_lock_credit to lock_credit_promotion: %v", err)
	}

	// Update lock_credit_promotion table structure (using member_id instead of user_id)
	_, err = con.Exec(ctx, `
		ALTER TABLE lock_credit_promotion 
		ADD COLUMN IF NOT EXISTS promotion_web_user_id BIGINT
	`)
	if err != nil {
		log.Printf("Warning: Could not add promotion_web_user_id column: %v", err)
	}

	// Create indexes for performance (using correct column names)
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_lock_credit_promotion_member_id ON lock_credit_promotion(member_id)",
		"CREATE INDEX IF NOT EXISTS idx_lock_credit_promotion_promotion_web_id ON lock_credit_promotion(promotion_web_id)",
		"CREATE INDEX IF NOT EXISTS idx_lock_credit_withdraw_user_id ON lock_credit_withdraw(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_lock_credit_withdraw_ref_id ON lock_credit_withdraw(ref_id)",
		"CREATE INDEX IF NOT EXISTS idx_lock_credit_withdraw_type_id ON lock_credit_withdraw(user_withdraw_lock_credit_type_id)",
	}

	for _, indexSQL := range indexes {
		_, err = con.Exec(ctx, indexSQL)
		if err != nil {
			log.Printf("Warning: Could not create index: %s, error: %v", indexSQL, err)
		}
	}

	// Create triggers for updated_at timestamps
	_, err = con.Exec(ctx, `
		CREATE OR REPLACE FUNCTION update_updated_at_column()
		RETURNS TRIGGER AS $$
		BEGIN
			NEW.updated_at = CURRENT_TIMESTAMP;
			RETURN NEW;
		END;
		$$ LANGUAGE plpgsql;
	`)
	if err != nil {
		log.Printf("Warning: Could not create update_updated_at_column function: %v", err)
	}

	// Create triggers one by one (IF NOT EXISTS not supported in triggers)
	_, err = con.Exec(ctx, `
		DROP TRIGGER IF EXISTS update_user_withdraw_lock_credit_type_updated_at ON user_withdraw_lock_credit_type;
		CREATE TRIGGER update_user_withdraw_lock_credit_type_updated_at
			BEFORE UPDATE ON user_withdraw_lock_credit_type
			FOR EACH ROW
			EXECUTE FUNCTION update_updated_at_column()
	`)
	if err != nil {
		log.Printf("Warning: Could not create trigger for user_withdraw_lock_credit_type: %v", err)
	}

	_, err = con.Exec(ctx, `
		DROP TRIGGER IF EXISTS update_lock_credit_withdraw_updated_at ON lock_credit_withdraw;
		CREATE TRIGGER update_lock_credit_withdraw_updated_at
			BEFORE UPDATE ON lock_credit_withdraw
			FOR EACH ROW
			EXECUTE FUNCTION update_updated_at_column()
	`)
	if err != nil {
		log.Printf("Warning: Could not create trigger for lock_credit_withdraw: %v", err)
	}

	_, err = con.Exec(ctx, `
		DROP TRIGGER IF EXISTS update_lock_credit_promotion_updated_at ON lock_credit_promotion;
		CREATE TRIGGER update_lock_credit_promotion_updated_at
			BEFORE UPDATE ON lock_credit_promotion
			FOR EACH ROW
			EXECUTE FUNCTION update_updated_at_column()
	`)
	if err != nil {
		log.Printf("Warning: Could not create trigger for lock_credit_promotion: %v", err)
	}

	log.Printf("Migration: Enhanced promotion web lock credit system successfully")
}

func (m *EnhancePromotionWebLockCredit) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Rolling back promotion web lock credit enhancements")

	// Drop triggers
	triggers := []string{
		"DROP TRIGGER IF EXISTS update_lock_credit_promotion_updated_at ON lock_credit_promotion",
		"DROP TRIGGER IF EXISTS update_lock_credit_withdraw_updated_at ON lock_credit_withdraw",
		"DROP TRIGGER IF EXISTS update_user_withdraw_lock_credit_type_updated_at ON user_withdraw_lock_credit_type",
	}

	for _, triggerSQL := range triggers {
		_, err = con.Exec(ctx, triggerSQL)
		if err != nil {
			log.Printf("Warning: Could not drop trigger: %v", err)
		}
	}

	// Drop tables in reverse order
	tables := []string{
		"DROP TABLE IF EXISTS lock_credit_withdraw CASCADE",
		"DROP TABLE IF EXISTS user_withdraw_lock_credit_type CASCADE",
	}

	for _, dropSQL := range tables {
		_, err := con.Exec(ctx, dropSQL)
		if err != nil {
			log.Printf("Warning: Could not drop table: %v", err)
		}
	}

	// Rename lock_credit_promotion back to promotion_web_lock_credit
	_, err = con.Exec(ctx, `
		ALTER TABLE lock_credit_promotion RENAME TO promotion_web_lock_credit
	`)
	if err != nil {
		log.Printf("Warning: Could not rename lock_credit_promotion back: %v", err)
	}

	log.Printf("Migration: Rollback completed")
}
