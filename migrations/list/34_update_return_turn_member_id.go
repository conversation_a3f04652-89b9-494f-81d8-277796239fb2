package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type UpdateReturnTurnMemberId struct{}

func (m *UpdateReturnTurnMemberId) GetName() string {
	return "28_update_return_turn_member_id"
}

func (m *UpdateReturnTurnMemberId) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Updating return_turn_loser table to use member_id")

	// Check if the table exists and has user_id column
	var columnExists bool
	err = con.QueryRow(ctx, `
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.columns 
			WHERE table_schema = $1
			AND table_name = 'return_turn_loser'
			AND column_name = 'user_id'
		)
	`, schemaName).Scan(&columnExists)

	if err != nil {
		log.Printf("Warning: Failed to check column existence: %v", err)
	}

	if columnExists {
		// Rename user_id to member_id
		_, err = con.Exec(ctx, `
			ALTER TABLE return_turn_loser 
			RENAME COLUMN user_id TO member_id
		`)
		if err != nil {
			log.Printf("Warning: Failed to rename user_id to member_id: %v", err)
			// Column might already be renamed, continue
		} else {
			log.Println("Renamed user_id to member_id successfully")
		}

		// Drop old index and create new one
		_, err = con.Exec(ctx, `
			DROP INDEX IF EXISTS idx_return_turn_loser_user_id;
			CREATE INDEX IF NOT EXISTS idx_return_turn_loser_member_id ON return_turn_loser(member_id);
		`)
		if err != nil {
			log.Printf("Warning: Failed to update indexes: %v", err)
		} else {
			log.Println("Updated indexes successfully")
		}
	} else {
		log.Println("Column user_id does not exist or already renamed, skipping rename")
	}

	// Update play_log table if it exists
	var playLogExists bool
	err = con.QueryRow(ctx, `
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.tables 
			WHERE table_schema = $1
			AND table_name = 'play_log'
		)
	`, schemaName).Scan(&playLogExists)

	if playLogExists {
		// Check if play_log has user_id column
		var playLogColumnExists bool
		err = con.QueryRow(ctx, `
			SELECT EXISTS (
				SELECT 1 
				FROM information_schema.columns 
				WHERE table_schema = $1
				AND table_name = 'play_log'
				AND column_name = 'user_id'
			)
		`, schemaName).Scan(&playLogColumnExists)

		if playLogColumnExists {
			_, err = con.Exec(ctx, `
				ALTER TABLE play_log 
				RENAME COLUMN user_id TO member_id
			`)
			if err != nil {
				log.Printf("Warning: Failed to rename play_log.user_id to member_id: %v", err)
			} else {
				log.Println("Renamed play_log.user_id to member_id successfully")

				// Update indexes for play_log
				_, err = con.Exec(ctx, `
					DROP INDEX IF EXISTS idx_play_log_user_id;
					CREATE INDEX IF NOT EXISTS idx_play_log_member_id ON play_log(member_id);
				`)
				if err != nil {
					log.Printf("Warning: Failed to update play_log indexes: %v", err)
				} else {
					log.Println("Updated play_log indexes successfully")
				}
			}
		}
	}

	// Update user_transactions table if it exists and uses user_id
	var userTransactionsExists bool
	err = con.QueryRow(ctx, `
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.tables 
			WHERE table_schema = $1
			AND table_name = 'user_transactions'
		)
	`, schemaName).Scan(&userTransactionsExists)

	if userTransactionsExists {
		// Check if user_transactions has user_id column
		var userTransColumnExists bool
		err = con.QueryRow(ctx, `
			SELECT EXISTS (
				SELECT 1 
				FROM information_schema.columns 
				WHERE table_schema = $1
				AND table_name = 'user_transactions'
				AND column_name = 'user_id'
			)
		`, schemaName).Scan(&userTransColumnExists)

		if userTransColumnExists {
			_, err = con.Exec(ctx, `
				ALTER TABLE user_transactions 
				RENAME COLUMN user_id TO member_id
			`)
			if err != nil {
				log.Printf("Warning: Failed to rename user_transactions.user_id to member_id: %v", err)
			} else {
				log.Println("Renamed user_transactions.user_id to member_id successfully")
			}
		}
	}

	log.Println("Migration 28_update_return_turn_member_id completed successfully")
}

func (m *UpdateReturnTurnMemberId) Down(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Rolling back return_turn_loser table to use user_id")

	// Check if the table exists and has member_id column
	var columnExists bool
	err = con.QueryRow(ctx, `
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.columns 
			WHERE table_schema = $1
			AND table_name = 'return_turn_loser'
			AND column_name = 'member_id'
		)
	`, schemaName).Scan(&columnExists)

	if columnExists {
		// Rename member_id back to user_id
		_, err = con.Exec(ctx, `
			ALTER TABLE return_turn_loser 
			RENAME COLUMN member_id TO user_id
		`)
		if err != nil {
			log.Printf("Warning: Failed to rename member_id back to user_id: %v", err)
		} else {
			log.Println("Renamed member_id back to user_id successfully")
		}

		// Drop new index and create old one
		_, err = con.Exec(ctx, `
			DROP INDEX IF EXISTS idx_return_turn_loser_member_id;
			CREATE INDEX IF NOT EXISTS idx_return_turn_loser_user_id ON return_turn_loser(user_id);
		`)
		if err != nil {
			log.Printf("Warning: Failed to rollback indexes: %v", err)
		} else {
			log.Println("Rolled back indexes successfully")
		}
	}

	// Rollback play_log table if needed
	var playLogExists bool
	err = con.QueryRow(ctx, `
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.tables 
			WHERE table_schema = $1
			AND table_name = 'play_log'
		)
	`, schemaName).Scan(&playLogExists)

	if playLogExists {
		var playLogColumnExists bool
		err = con.QueryRow(ctx, `
			SELECT EXISTS (
				SELECT 1 
				FROM information_schema.columns 
				WHERE table_schema = $1
				AND table_name = 'play_log'
				AND column_name = 'member_id'
			)
		`, schemaName).Scan(&playLogColumnExists)

		if playLogColumnExists {
			_, err = con.Exec(ctx, `
				ALTER TABLE play_log 
				RENAME COLUMN member_id TO user_id
			`)
			if err != nil {
				log.Printf("Warning: Failed to rename play_log.member_id back to user_id: %v", err)
			} else {
				log.Println("Renamed play_log.member_id back to user_id successfully")

				// Rollback indexes for play_log
				_, err = con.Exec(ctx, `
					DROP INDEX IF EXISTS idx_play_log_member_id;
					CREATE INDEX IF NOT EXISTS idx_play_log_user_id ON play_log(user_id);
				`)
				if err != nil {
					log.Printf("Warning: Failed to rollback play_log indexes: %v", err)
				}
			}
		}
	}

	// Rollback user_transactions table if needed
	var userTransactionsExists bool
	err = con.QueryRow(ctx, `
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.tables 
			WHERE table_schema = $1
			AND table_name = 'user_transactions'
		)
	`, schemaName).Scan(&userTransactionsExists)

	if userTransactionsExists {
		var userTransColumnExists bool
		err = con.QueryRow(ctx, `
			SELECT EXISTS (
				SELECT 1 
				FROM information_schema.columns 
				WHERE table_schema = $1
				AND table_name = 'user_transactions'
				AND column_name = 'member_id'
			)
		`, schemaName).Scan(&userTransColumnExists)

		if userTransColumnExists {
			_, err = con.Exec(ctx, `
				ALTER TABLE user_transactions 
				RENAME COLUMN member_id TO user_id
			`)
			if err != nil {
				log.Printf("Warning: Failed to rename user_transactions.member_id back to user_id: %v", err)
			} else {
				log.Println("Renamed user_transactions.member_id back to user_id successfully")
			}
		}
	}

	log.Println("Migration rollback completed")
}
