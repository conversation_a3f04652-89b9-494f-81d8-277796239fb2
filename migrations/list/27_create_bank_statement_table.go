package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type CreateBankStatementTable struct{}

func (m *CreateBankStatementTable) GetName() string {
	return "22_create_bank_statement_table"
}

func (m *CreateBankStatementTable) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schema := os.Getenv("DATABASE_SCHEMA")
	if schema == "" {
		schema = "public"
	}

	queries := []string{
		// Set search path
		fmt.Sprintf("SET search_path TO %s", schema),

		// Create bank_statement table
		`CREATE TABLE IF NOT EXISTS bank_statement (
			id BIGSERIAL PRIMARY KEY,
			account_id BIGINT,
			external_id BIGINT,
			amount NUMERIC(14,2),
			statement_type_id BIGINT REFERENCES statement_type(id),
			statement_status_id BIGINT REFERENCES statement_status(id),
			from_bank_id BIGINT,
			from_account_number VARCHAR(255),
			transfer_at TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)`,

		// Create indexes
		`CREATE INDEX idx_bank_statement_account_id ON bank_statement(account_id)`,
		`CREATE INDEX idx_bank_statement_external_id ON bank_statement(external_id)`,
		`CREATE INDEX idx_bank_statement_statement_type_id ON bank_statement(statement_type_id)`,
		`CREATE INDEX idx_bank_statement_statement_status_id ON bank_statement(statement_status_id)`,
		`CREATE INDEX idx_bank_statement_from_bank_id ON bank_statement(from_bank_id)`,
		`CREATE INDEX idx_bank_statement_transfer_at ON bank_statement(transfer_at)`,
	}

	for _, query := range queries {
		if _, err := con.Exec(ctx, query); err != nil {
			log.Printf("Error executing query: %s\nError: %v", query, err)
			panic(err)
		}
	}

	log.Println("Migration 22: Created bank_statement table successfully")
}

func (m *CreateBankStatementTable) Down(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schema := os.Getenv("DATABASE_SCHEMA")
	if schema == "" {
		schema = "public"
	}

	queries := []string{
		// Set search path
		fmt.Sprintf("SET search_path TO %s", schema),

		// Drop indexes
		`DROP INDEX IF EXISTS idx_bank_statement_transfer_at`,
		`DROP INDEX IF EXISTS idx_bank_statement_from_bank_id`,
		`DROP INDEX IF EXISTS idx_bank_statement_statement_status_id`,
		`DROP INDEX IF EXISTS idx_bank_statement_statement_type_id`,
		`DROP INDEX IF EXISTS idx_bank_statement_external_id`,
		`DROP INDEX IF EXISTS idx_bank_statement_account_id`,

		// Drop table
		`DROP TABLE IF EXISTS bank_statement CASCADE`,
	}

	for _, query := range queries {
		if _, err := con.Exec(ctx, query); err != nil {
			log.Printf("Error executing rollback query: %s\nError: %v", query, err)
			panic(err)
		}
	}

	log.Println("Migration 22: Rolled back bank_statement table successfully")
}
