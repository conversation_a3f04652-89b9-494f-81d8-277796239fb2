package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type CreateUserTransactionTable struct{}

func (m *CreateUserTransactionTable) GetName() string {
	return "15_create_user_transaction_table"
}

func (m *CreateUserTransactionTable) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Creating user_transaction table")

	// Create user_transaction table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS user_transaction (
			id BIGSERIAL PRIMARY KEY,
			direction_id INTEGER NOT NULL,
			type_id INTEGER NOT NULL,
			user_id INTEGER NOT NULL,
			banking_id INTEGER NOT NULL,
			transfer_banking_id INTEGER NULL,
			ref_id VARCHAR(255) NOT NULL,
			detail TEXT NULL,
			date TIMESTAMP NOT NULL,
			credit_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
			create_before DECIMAL(15,2) NOT NULL DEFAULT 0.00,
			create_after DECIMAL(15,2) NOT NULL DEFAULT 0.00,
			create_back DECIMAL(15,2) NOT NULL DEFAULT 0.00,
			bonus_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
			transfer_at TIMESTAMP NULL,
			confirm_admin_id INTEGER NULL,
			promotion_id BIGINT NULL,
			status_id INTEGER NOT NULL DEFAULT 1,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_user_transaction_direction 
				FOREIGN KEY (direction_id) REFERENCES user_transaction_direction(id),
			CONSTRAINT fk_user_transaction_type 
				FOREIGN KEY (type_id) REFERENCES user_transaction_type(id),
			CONSTRAINT fk_user_transaction_status 
				FOREIGN KEY (status_id) REFERENCES user_transaction_status(id),
			CONSTRAINT fk_user_transaction_user 
				FOREIGN KEY (user_id) REFERENCES members(id),
			CONSTRAINT fk_user_transaction_banking 
				FOREIGN KEY (banking_id) REFERENCES banking(id),
			CONSTRAINT fk_user_transaction_transfer_banking 
				FOREIGN KEY (transfer_banking_id) REFERENCES banking(id),
			CONSTRAINT fk_user_transaction_confirm_admin 
				FOREIGN KEY (confirm_admin_id) REFERENCES users(id),
			CONSTRAINT fk_user_transaction_promotion 
				FOREIGN KEY (promotion_id) REFERENCES promotion_web(id)
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create user_transaction table: %v", err))
	}

	// Create indexes for better performance
	log.Printf("Migration: Creating indexes for user_transaction table")

	_, err = con.Exec(ctx, `
		CREATE INDEX IF NOT EXISTS idx_user_transaction_user_id ON user_transaction(user_id);
		CREATE INDEX IF NOT EXISTS idx_user_transaction_direction_id ON user_transaction(direction_id);
		CREATE INDEX IF NOT EXISTS idx_user_transaction_type_id ON user_transaction(type_id);
		CREATE INDEX IF NOT EXISTS idx_user_transaction_status_id ON user_transaction(status_id);
		CREATE INDEX IF NOT EXISTS idx_user_transaction_banking_id ON user_transaction(banking_id);
		CREATE INDEX IF NOT EXISTS idx_user_transaction_date ON user_transaction(date);
		CREATE INDEX IF NOT EXISTS idx_user_transaction_created_at ON user_transaction(created_at);
		CREATE INDEX IF NOT EXISTS idx_user_transaction_ref_id ON user_transaction(ref_id);
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create indexes for user_transaction table: %v", err))
	}

	// Create trigger for updated_at
	_, err = con.Exec(ctx, `
		CREATE OR REPLACE FUNCTION update_user_transaction_updated_at()
		RETURNS TRIGGER AS $$
		BEGIN
			NEW.updated_at = CURRENT_TIMESTAMP;
			RETURN NEW;
		END;
		$$ language 'plpgsql';

		CREATE TRIGGER trigger_user_transaction_updated_at
			BEFORE UPDATE ON user_transaction
			FOR EACH ROW
			EXECUTE FUNCTION update_user_transaction_updated_at();
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create trigger for user_transaction table: %v", err))
	}

	log.Printf("Migration: User transaction table created successfully")
}

func (m *CreateUserTransactionTable) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	// Drop trigger and function
	_, err = con.Exec(ctx, `
		DROP TRIGGER IF EXISTS trigger_user_transaction_updated_at ON user_transaction;
		DROP FUNCTION IF EXISTS update_user_transaction_updated_at();
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop trigger: %v", err))
	}

	// Drop table
	_, err = con.Exec(ctx, "DROP TABLE IF EXISTS user_transaction")
	if err != nil {
		panic(fmt.Sprintf("Failed to drop user_transaction table: %v", err))
	}
}
