package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type FixWithdrawSplitColumn struct{}

func (m *FixWithdrawSplitColumn) GetName() string {
	return "FixWithdrawSplitColumn"
}

func (m *FixWithdrawSplitColumn) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Fixing withdraw_splitting column name")

	// Check if withdraw_splitting exists and rename to withdraw_split
	var columnExists bool
	err = con.QueryRow(ctx, `
		SELECT EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name = 'payment_gateway_account' 
			AND column_name = 'withdraw_splitting'
		)
	`).Scan(&columnExists)

	if err == nil && columnExists {
		_, err = con.Exec(ctx, `
			ALTER TABLE payment_gateway_account 
			RENAME COLUMN withdraw_splitting TO withdraw_split
		`)
		if err != nil {
			log.Printf("Warning: Could not rename withdraw_splitting column: %v", err)
		} else {
			log.Printf("Successfully renamed withdraw_splitting to withdraw_split")
		}
	} else {
		log.Printf("Column withdraw_splitting does not exist or withdraw_split already exists")
	}

	log.Printf("Migration: withdraw_split column name fixed successfully")
}

func (m *FixWithdrawSplitColumn) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	// Rename back to withdraw_splitting
	_, err = con.Exec(ctx, `
		ALTER TABLE payment_gateway_account 
		RENAME COLUMN withdraw_split TO withdraw_splitting
	`)
	if err != nil {
		log.Printf("Warning: Could not rename withdraw_split back to withdraw_splitting: %v", err)
	}
}
