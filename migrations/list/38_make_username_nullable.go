package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type MakeUsernameNullable struct{}

func (m *MakeUsernameNullable) GetName() string {
	return "MakeUsernameNullable"
}

func (m *MakeUsernameNullable) Up(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Drop the unique constraint first
	_, err = con.Exec(ctx, `
		ALTER TABLE members 
		DROP CONSTRAINT IF EXISTS members_username_key
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop username unique constraint: %v", err))
	}
	log.Printf("Migration: Dropped username unique constraint")

	// Make username column nullable
	_, err = con.Exec(ctx, `
		ALTER TABLE members 
		ALTER COLUMN username DROP NOT NULL
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to make username nullable: %v", err))
	}
	log.Printf("Migration: Made username column nullable")

	// Add unique constraint back but with a condition to ignore NULL values
	// This allows multiple NULL values but ensures uniqueness for non-NULL values
	_, err = con.Exec(ctx, `
		CREATE UNIQUE INDEX members_username_unique_idx 
		ON members (username) 
		WHERE username IS NOT NULL
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create conditional unique index on username: %v", err))
	}
	log.Printf("Migration: Created conditional unique index on username")
}

func (m *MakeUsernameNullable) Down(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Drop the conditional unique index
	_, err = con.Exec(ctx, `
		DROP INDEX IF EXISTS members_username_unique_idx
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop conditional unique index: %v", err))
	}

	// Note: We can't easily revert username to NOT NULL if there are NULL values
	// This would require updating all NULL usernames first
	// For now, just add back the regular unique constraint
	_, err = con.Exec(ctx, `
		ALTER TABLE members 
		ADD CONSTRAINT members_username_key UNIQUE (username)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add back username unique constraint: %v", err))
	}

	// Make username NOT NULL again (this will fail if there are NULL values)
	_, err = con.Exec(ctx, `
		ALTER TABLE members 
		ALTER COLUMN username SET NOT NULL
	`)
	if err != nil {
		// Don't panic here as this might fail if there are NULL values
		// Just log the warning
		log.Printf("Warning: Could not set username back to NOT NULL, there might be NULL values: %v", err)
	}
}
