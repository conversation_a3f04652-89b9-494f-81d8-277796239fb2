package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type AlterReturnTurnMemberCode struct{}

func (m *AlterReturnTurnMemberCode) GetName() string {
	return "AlterReturnTurnMemberCode"
}

func (m *AlterReturnTurnMemberCode) Up(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Drop the foreign key constraint first
	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_loser 
		DROP CONSTRAINT IF EXISTS return_turn_loser_member_id_fkey
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop foreign key constraint: %v", err))
	}
	log.Printf("Migration: Dropped foreign key constraint")

	// Drop the index on member_id if exists
	_, err = con.Exec(ctx, `
		DROP INDEX IF EXISTS idx_return_turn_loser_member_id
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop index on member_id: %v", err))
	}
	log.Printf("Migration: Dropped index on member_id")

	// Rename column from member_id to member_code
	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_loser 
		RENAME COLUMN member_id TO member_code
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to rename column: %v", err))
	}
	log.Printf("Migration: Renamed member_id to member_code")

	// Change the data type from bigint to varchar
	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_loser 
		ALTER COLUMN member_code TYPE VARCHAR(100) USING member_code::VARCHAR
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to change column type: %v", err))
	}
	log.Printf("Migration: Changed member_code type to VARCHAR(100)")

	// Add NOT NULL constraint
	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_loser 
		ALTER COLUMN member_code SET NOT NULL
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to set NOT NULL constraint: %v", err))
	}
	log.Printf("Migration: Set NOT NULL constraint on member_code")

	// Create index on member_code for performance
	_, err = con.Exec(ctx, `
		CREATE INDEX idx_return_turn_loser_member_code ON return_turn_loser(member_code)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create index on member_code: %v", err))
	}
	log.Printf("Migration: Created index on member_code")

	// Update daily_key to use member_code instead of member_id
	_, err = con.Exec(ctx, `
		UPDATE return_turn_loser 
		SET daily_key = CONCAT(member_code, '_', of_date)
		WHERE daily_key IS NOT NULL
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to update daily_key: %v", err))
	}
	log.Printf("Migration: Updated daily_key to use member_code")
}

func (m *AlterReturnTurnMemberCode) Down(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Drop the index on member_code
	_, err = con.Exec(ctx, `
		DROP INDEX IF EXISTS idx_return_turn_loser_member_code
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop index on member_code: %v", err))
	}

	// Change the data type back to bigint (this will fail if non-numeric values exist)
	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_loser 
		ALTER COLUMN member_code TYPE BIGINT USING (
			CASE 
				WHEN member_code ~ '^\d+$' THEN member_code::BIGINT
				ELSE 0 -- Default value for non-numeric codes
			END
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to change column type back to bigint: %v", err))
	}

	// Rename column back from member_code to member_id
	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_loser 
		RENAME COLUMN member_code TO member_id
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to rename column back: %v", err))
	}

	// Add NOT NULL constraint
	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_loser 
		ALTER COLUMN member_id SET NOT NULL
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to set NOT NULL constraint: %v", err))
	}

	// Create index on member_id
	_, err = con.Exec(ctx, `
		CREATE INDEX idx_return_turn_loser_member_id ON return_turn_loser(member_id)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create index on member_id: %v", err))
	}

	// Add back the foreign key constraint (assuming members table exists)
	_, err = con.Exec(ctx, `
		ALTER TABLE return_turn_loser 
		ADD CONSTRAINT return_turn_loser_member_id_fkey 
		FOREIGN KEY (member_id) REFERENCES members(id)
	`)
	if err != nil {
		// Log warning but don't fail if foreign key can't be added
		// This might happen if members table doesn't exist
		log.Printf("Warning: Could not add foreign key constraint: %v", err)
	}

	// Update daily_key back to use member_id
	_, err = con.Exec(ctx, `
		UPDATE return_turn_loser 
		SET daily_key = CONCAT(member_id, '_', of_date)
		WHERE daily_key IS NOT NULL
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to update daily_key: %v", err))
	}
}
