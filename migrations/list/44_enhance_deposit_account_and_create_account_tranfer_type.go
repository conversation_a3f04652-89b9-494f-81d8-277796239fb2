package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type EnhanceDepositAccountAndCreateAccountTranferType struct{}

func (m *EnhanceDepositAccountAndCreateAccountTranferType) GetName() string {
	return "EnhanceDepositAccountAndCreateAccountTranferType"
}

func (m *EnhanceDepositAccountAndCreateAccountTranferType) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public" // default schema
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	log.Printf("Migration: Setting search_path to '%s'", schemaName)
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Create account_tranfer_type table
	log.Println("Migration: Creating account_tranfer_type table")
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS account_tranfer_type (
			id SERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create account_tranfer_type table: %v", err))
	}
	log.Println("Migration: account_tranfer_type table created successfully")

	// Insert initial data into account_tranfer_type
	log.Println("Migration: Inserting initial data into account_tranfer_type")
	_, err = con.Exec(ctx, `
		INSERT INTO account_tranfer_type (id, name) VALUES 
		(1, 'บัญชีถอน'),
		(2, 'บัญชีพัก')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to insert initial data into account_tranfer_type: %v", err))
	}
	log.Println("Migration: Initial data inserted into account_tranfer_type successfully")

	// Add new columns to deposit_account table
	log.Println("Migration: Adding new columns to deposit_account table")

	// Add is_qr_payment column
	_, err = con.Exec(ctx, `
		ALTER TABLE deposit_account 
		ADD COLUMN IF NOT EXISTS is_qr_payment BOOLEAN DEFAULT FALSE
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add is_qr_payment column: %v", err))
	}
	log.Println("Migration: is_qr_payment column added successfully")

	// Add amount_triger_tranfer column
	_, err = con.Exec(ctx, `
		ALTER TABLE deposit_account 
		ADD COLUMN IF NOT EXISTS amount_triger_tranfer DECIMAL(15,2)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add amount_triger_tranfer column: %v", err))
	}
	log.Println("Migration: amount_triger_tranfer column added successfully")

	// Add amount_tranfer column
	_, err = con.Exec(ctx, `
		ALTER TABLE deposit_account 
		ADD COLUMN IF NOT EXISTS amount_tranfer DECIMAL(15,2)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add amount_tranfer column: %v", err))
	}
	log.Println("Migration: amount_tranfer column added successfully")

	// Add account_tranfer_id column
	_, err = con.Exec(ctx, `
		ALTER TABLE deposit_account 
		ADD COLUMN IF NOT EXISTS account_tranfer_id INTEGER
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add account_tranfer_id column: %v", err))
	}
	log.Println("Migration: account_tranfer_id column added successfully")

	// Add account_tranfer_type_id column with foreign key
	_, err = con.Exec(ctx, `
		ALTER TABLE deposit_account 
		ADD COLUMN IF NOT EXISTS account_tranfer_type_id INTEGER
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add account_tranfer_type_id column: %v", err))
	}
	log.Println("Migration: account_tranfer_type_id column added successfully")

	// Add foreign key constraint for account_tranfer_type_id
	_, err = con.Exec(ctx, `
		DO $$
		BEGIN
			IF NOT EXISTS (
				SELECT 1 FROM information_schema.table_constraints 
				WHERE constraint_name = 'fk_deposit_account_tranfer_type' 
				AND table_name = 'deposit_account'
			) THEN
				ALTER TABLE deposit_account 
				ADD CONSTRAINT fk_deposit_account_tranfer_type 
				FOREIGN KEY (account_tranfer_type_id) 
				REFERENCES account_tranfer_type(id);
			END IF;
		END $$;
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add foreign key constraint: %v", err))
	}
	log.Println("Migration: Foreign key constraint added successfully")

	log.Println("Migration: EnhanceDepositAccountAndCreateAccountTranferType completed successfully")
}

func (m *EnhanceDepositAccountAndCreateAccountTranferType) Down(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public" // default schema
	}

	log.Printf("Migration: Using schema '%s' for rollback", schemaName)

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Remove foreign key constraint
	log.Println("Migration: Removing foreign key constraint")
	_, err = con.Exec(ctx, `
		ALTER TABLE deposit_account 
		DROP CONSTRAINT IF EXISTS fk_deposit_account_tranfer_type
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to remove foreign key constraint: %v", err))
	}

	// Remove columns from deposit_account table
	log.Println("Migration: Removing columns from deposit_account table")
	_, err = con.Exec(ctx, `
		ALTER TABLE deposit_account 
		DROP COLUMN IF EXISTS is_qr_payment,
		DROP COLUMN IF EXISTS amount_triger_tranfer,
		DROP COLUMN IF EXISTS amount_tranfer,
		DROP COLUMN IF EXISTS account_tranfer_id,
		DROP COLUMN IF EXISTS account_tranfer_type_id
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to remove columns from deposit_account: %v", err))
	}

	// Drop account_tranfer_type table
	log.Println("Migration: Dropping account_tranfer_type table")
	_, err = con.Exec(ctx, `DROP TABLE IF EXISTS account_tranfer_type`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop account_tranfer_type table: %v", err))
	}

	log.Println("Migration: EnhanceDepositAccountAndCreateAccountTranferType rollback completed successfully")
}
