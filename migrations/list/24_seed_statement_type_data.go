package list

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/jackc/pgx/v5"
)

type SeedStatementTypeData struct{}

func (m *SeedStatementTypeData) GetName() string {
	return "19_seed_statement_type_data"
}

func (m *SeedStatementTypeData) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schema := os.Getenv("DATABASE_SCHEMA")
	if schema == "" {
		schema = "public"
	}

	createdAt := time.Date(2023, 9, 19, 2, 39, 23, 0, time.UTC)

	queries := []string{
		// Set search path
		fmt.Sprintf("SET search_path TO %s", schema),
	}

	for _, query := range queries {
		if _, err := con.Exec(ctx, query); err != nil {
			log.Printf("Error executing query: %s\nError: %v", query, err)
			panic(err)
		}
	}

	// Insert seed data
	insertQuery := `
		INSERT INTO statement_type (id, name, label_th, label_en, created_at, updated_at, deleted_at)
		VALUES 
			(1, 'TRANSFER_IN', 'โอนเข้า', 'transfer_in', $1, NULL, NULL),
			(2, 'TRANSFER_OUT', 'โอนออก', 'transfer_out', $1, NULL, NULL)
		ON CONFLICT (id) DO NOTHING
	`

	if _, err := con.Exec(ctx, insertQuery, createdAt); err != nil {
		log.Printf("Error inserting statement_type data: %v", err)
		panic(err)
	}

	// Reset sequence
	resetSeq := `SELECT setval('statement_type_id_seq', (SELECT MAX(id) FROM statement_type), true)`
	if _, err := con.Exec(ctx, resetSeq); err != nil {
		log.Printf("Error resetting statement_type sequence: %v", err)
		panic(err)
	}

	log.Println("Migration 19: Seeded statement_type data successfully")
}

func (m *SeedStatementTypeData) Down(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schema := os.Getenv("DATABASE_SCHEMA")
	if schema == "" {
		schema = "public"
	}

	queries := []string{
		// Set search path
		fmt.Sprintf("SET search_path TO %s", schema),

		// Delete seeded data
		`DELETE FROM statement_type WHERE id IN (1, 2)`,
	}

	for _, query := range queries {
		if _, err := con.Exec(ctx, query); err != nil {
			log.Printf("Error executing rollback query: %s\nError: %v", query, err)
			panic(err)
		}
	}

	log.Println("Migration 19: Rolled back statement_type data successfully")
}
