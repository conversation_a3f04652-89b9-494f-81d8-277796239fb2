package list

import (
	"context"
	"fmt"
	"github.com/jackc/pgx/v5"
	"log"
	"os"
)

type CreatePromotions struct{}

func (m *CreatePromotions) GetName() string {
	return "CreatePromotions"
}

func (m *CreatePromotions) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Creating promotion system tables")

	// ================================
	// LOOKUP/REFERENCE TABLES (No dependencies)
	// ================================

	// Create promotion_web_type table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_web_type (
			id BIGSERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			label_th VARCHAR(255) NULL,
			label_en VARCHAR(255) NULL
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert promotion_web_type data
	_, err = con.Exec(ctx, `
		INSERT INTO promotion_web_type (id, name, label_th, label_en) VALUES
		(1, 'NEW_MEMBER_FREE', 'โปรโมชั่นสมัครใหม่แจกฟรี', 'new member free'),
		(2, 'NEW_MEMBER_CONDITION', 'โปรโมชั่นสมัครใหม่ตามมเงื่อนไข', 'new member condition'),
		(3, 'DEPOSIT_MINIMUM_PER_DAY', 'โปรโมชั่นฝากขั้นต่ำต่อวัน', 'deposit minimum per day'),
		(4, 'FIRST_DEPOSIT', 'โปรโมชั่นฝากครั้งแรก', 'first deposit'),
		(5, 'DEPOSIT_PER_DAY', 'โปรโมชั่นฝากทั้งวัน', 'deposit per day'),
		(6, 'DEPOSIT_BY_TIME', 'โปรโมชั่นฝากตามช่วงเวลา', 'deposit by time'),
		(7, 'FIRST_DEPOSIT_OF_DAY', 'โปรโมชั้นฝากครั้งแรกของวัน', 'first deposit of the day')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_web_status table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_web_status (
			id BIGSERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			label_th VARCHAR(255) NULL,
			label_en VARCHAR(255) NULL
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert promotion_web_status data
	_, err = con.Exec(ctx, `
		INSERT INTO promotion_web_status (id, name, label_th, label_en) VALUES
		(1, 'DISABLE_WEB', 'ปิดการแสดงผลหน้าเว็บไซต์แต่เปิดใช้งานอยู่', 'disactive web'),
		(2, 'ACTIVE', 'ใช้งาน', 'active'),
		(3, 'CANCELED', 'ยกเลิก', 'canceled'),
		(4, 'SHOW_ONLY', 'เปิดแสดงแต่ไม่ใช้งาน', 'show only'),
		(5, 'ONLY_URL', 'เฉพาะลูกค้าที่เข้าจากลิ้งก์', 'use link')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_web_date_type table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_web_date_type (
			id BIGSERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			label_th VARCHAR(255) NULL,
			label_en VARCHAR(255) NULL
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert promotion_web_date_type data
	_, err = con.Exec(ctx, `
		INSERT INTO promotion_web_date_type (id, name, label_th, label_en) VALUES
		(1, 'FIXED_DATE', 'เริ่มต้น-สิ้นสุด', 'fixed date'),
		(2, 'NON_FIXED_DATE', 'ไม่กำหนดระยะเวลา', 'non fixed date')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_web_bonus_condition table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_web_bonus_condition (
			id BIGSERIAL PRIMARY KEY,
			syntax VARCHAR(10) NULL,
			name VARCHAR(255) NOT NULL,
			label_th VARCHAR(255) NULL,
			label_en VARCHAR(255) NULL
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert promotion_web_bonus_condition data
	_, err = con.Exec(ctx, `
		INSERT INTO promotion_web_bonus_condition (id, syntax, name, label_th, label_en) VALUES
		(1, '>=', 'MORE_THAN_OR_EQUAL', 'มากกว่าหรือเท่ากับ', 'more than or equal'),
		(2, '<=', 'LESS_THAN_OR_EQUAL', 'น้อยกว่าหรือเท่ากับ', 'less than or equal')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_web_bonus_type table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_web_bonus_type (
			id BIGSERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			label_th VARCHAR(255) NULL,
			label_en VARCHAR(255) NULL
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert promotion_web_bonus_type data
	_, err = con.Exec(ctx, `
		INSERT INTO promotion_web_bonus_type (id, name, label_th, label_en) VALUES
		(1, 'PERCENT', 'เปอร์เซ็นต์', 'percent'),
		(2, 'FIXED RATE', 'จำนวนเงิน', 'fixed rate')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_web_turnover_type table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_web_turnover_type (
			id BIGSERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			label_th VARCHAR(255) NULL,
			label_en VARCHAR(255) NULL
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert promotion_web_turnover_type data
	_, err = con.Exec(ctx, `
		INSERT INTO promotion_web_turnover_type (id, name, label_th, label_en) VALUES
		(1, 'ALL', 'ทุกเกม', 'all'),
		(2, 'SPORT', 'กีฬา', 'sport'),
		(3, 'CASINO', 'คาสิโน', 'casino'),
		(4, 'SLOT', 'สล็อต', 'slot'),
		(5, 'P2P', 'P2P', 'p2p'),
		(6, 'LOTTERY', 'ลอตเตอรี่', 'lottery'),
		(7, 'FINANCIAL', 'การเงิน', 'financial')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_web_user_status table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_web_user_status (
			id BIGSERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			label_th VARCHAR(255) NULL,
			label_en VARCHAR(255) NULL
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert promotion_web_user_status data
	_, err = con.Exec(ctx, `
		INSERT INTO promotion_web_user_status (id, name, label_th, label_en) VALUES
		(1, 'ON_PROCESS', 'รอผ่านเงื่อนไข', 'on process'),
		(2, 'SUCCESS', 'สำเร็จ', 'success'),
		(3, 'CANCELED', 'ยกเลิก', 'canceled'),
		(4, 'WITHDRAW_ON_PROCESS', 'อยู่ระหว่างเงื่อนไขถอน', 'withdraw on process')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_web_register_member_status table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_web_register_member_status (
			id BIGSERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			label_th VARCHAR(255) NOT NULL,
			label_en VARCHAR(255) NOT NULL,
			deleted_at TIMESTAMP NULL
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert promotion_web_register_member_status data
	_, err = con.Exec(ctx, `
		INSERT INTO promotion_web_register_member_status (id, name, label_th, label_en, deleted_at) VALUES
		(1, 'REGISTER_PENDING', 'รอการยืนยัน', 'register pending', NULL),
		(2, 'REGISTER_CONFIRM', 'ยืนยัน', 'register confirm', NULL),
		(3, 'REGISTER_REJECT', 'ปฏิเสธ', 'register reject', NULL)
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_return_turn_type table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_return_turn_type (
			id BIGINT PRIMARY KEY,
			name VARCHAR(255) NOT NULL DEFAULT '',
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert promotion_return_turn_type data
	_, err = con.Exec(ctx, `
		INSERT INTO promotion_return_turn_type (id, name, created_at) VALUES
		(1, 'คืนยอดเสียเมื่อยอดเทิร์นเกิน', '2023-10-06 08:15:30')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_return_turn_status table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_return_turn_status (
			id BIGINT PRIMARY KEY,
			name VARCHAR(255) NOT NULL DEFAULT '',
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert promotion_return_turn_status data
	_, err = con.Exec(ctx, `
		INSERT INTO promotion_return_turn_status (id, name, created_at) VALUES
		(1, 'PENDING', '2023-10-09 06:52:18'),
		(2, 'READY', '2023-10-09 06:52:18'),
		(3, 'TAKEN', '2023-10-09 06:52:18'),
		(4, 'EXPIRED', '2023-10-09 06:52:18')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_return_turn_cut_type table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_return_turn_cut_type (
			id BIGINT PRIMARY KEY,
			name VARCHAR(255) NOT NULL DEFAULT '',
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert promotion_return_turn_cut_type data
	_, err = con.Exec(ctx, `
		INSERT INTO promotion_return_turn_cut_type (id, name, created_at) VALUES
		(1, 'รายวัน', '2023-10-06 08:15:31'),
		(2, 'รายสัปดาห์', '2023-10-06 08:15:31')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_return_loser_type table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_return_loser_type (
			id BIGINT PRIMARY KEY,
			name VARCHAR(255) NOT NULL DEFAULT '',
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert promotion_return_loser_type data
	_, err = con.Exec(ctx, `
		INSERT INTO promotion_return_loser_type (id, name, created_at) VALUES
		(1, 'คืนยอดเสียเมื่อยอดเสียเกิน', '2023-10-06 08:15:30')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_return_loser_status table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_return_loser_status (
			id BIGINT PRIMARY KEY,
			name VARCHAR(255) NOT NULL DEFAULT '',
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert promotion_return_loser_status data
	_, err = con.Exec(ctx, `
		INSERT INTO promotion_return_loser_status (id, name, created_at) VALUES
		(1, 'PENDING', '2023-10-09 06:52:18'),
		(2, 'READY', '2023-10-09 06:52:18'),
		(3, 'TAKEN', '2023-10-09 06:52:18'),
		(4, 'EXPIRED', '2023-10-09 06:52:18')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_return_cut_type table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_return_cut_type (
			id BIGINT PRIMARY KEY,
			name VARCHAR(255) NOT NULL DEFAULT '',
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert promotion_return_cut_type data
	_, err = con.Exec(ctx, `
		INSERT INTO promotion_return_cut_type (id, name, created_at) VALUES
		(1, 'รายวัน', '2023-10-06 08:15:31'),
		(2, 'รายสัปดาห์', '2024-05-21 06:50:11')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// ================================
	// MAIN PROMOTION TABLE (depends on lookup tables)
	// ================================

	// Create promotion_web table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_web (
			id BIGSERIAL PRIMARY KEY,
			priority_order BIGINT NOT NULL DEFAULT 0,
			promotion_web_type_id BIGINT NOT NULL,
			promotion_web_status_id BIGINT NOT NULL,
			condition_detail VARCHAR(255) NULL,
			image_url VARCHAR(255) NULL,
			name VARCHAR(255) NOT NULL,
			short_description VARCHAR(255) NOT NULL,
			description TEXT NOT NULL,
			promotion_web_date_type_id BIGINT NOT NULL,
			start_date DATE NULL,
			end_date DATE NULL,
			free_bonus_amount DECIMAL(10,2) NULL,
			privilege_per_day INTEGER NULL,
			able_withdraw_morethan DECIMAL(10,2) NULL,
			promotion_web_bonus_condition_id BIGINT NULL,
			bonus_condition_amount DECIMAL(10,2) NULL,
			promotion_web_bonus_type_id BIGINT NULL,
			bonus_type_amount DECIMAL(10,2) NULL,
			bonus_type_amount_max DECIMAL(10,2) NULL,
			able_withdraw_pertime DECIMAL(10,2) NULL,
			promotion_web_turnover_type_id BIGINT NULL,
			turnover_amount DECIMAL(10,2) NULL,
			monday BOOLEAN NULL,
			tuesday BOOLEAN NULL,
			wednesday BOOLEAN NULL,
			thursday BOOLEAN NULL,
			friday BOOLEAN NULL,
			saturday BOOLEAN NULL,
			sunday BOOLEAN NULL,
			time_start TIME NULL,
			time_end TIME NULL,
			hidden_url_link VARCHAR(255) NOT NULL DEFAULT '',
			pg_hard_credit_free_spin INTEGER NOT NULL DEFAULT 0,
			pg_hard_code_game_free_spin VARCHAR(255) NOT NULL DEFAULT '',
			pg_hard_is_active BOOLEAN NOT NULL DEFAULT FALSE,
			created_by_user_id BIGINT NULL,
			updated_by_user_id BIGINT NULL,
			canceled_by_user_id BIGINT NULL,
			deleted_by_user_id BIGINT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			deleted_at TIMESTAMP NULL,
			CONSTRAINT fk_promotion_web_type FOREIGN KEY (promotion_web_type_id) REFERENCES promotion_web_type(id),
			CONSTRAINT fk_promotion_web_status FOREIGN KEY (promotion_web_status_id) REFERENCES promotion_web_status(id),
			CONSTRAINT fk_promotion_web_bonus_condition FOREIGN KEY (promotion_web_bonus_condition_id) REFERENCES promotion_web_bonus_condition(id),
			CONSTRAINT fk_promotion_web_bonus_type FOREIGN KEY (promotion_web_bonus_type_id) REFERENCES promotion_web_bonus_type(id),
			CONSTRAINT fk_promotion_web_turnover_type FOREIGN KEY (promotion_web_turnover_type_id) REFERENCES promotion_web_turnover_type(id),
			CONSTRAINT fk_promotion_web_date_type FOREIGN KEY (promotion_web_date_type_id) REFERENCES promotion_web_date_type(id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create indexes for promotion_web
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_promotion_web_type_id ON promotion_web (promotion_web_type_id)",
		"CREATE INDEX IF NOT EXISTS idx_promotion_web_status_id ON promotion_web (promotion_web_status_id)",
		"CREATE INDEX IF NOT EXISTS idx_promotion_web_bonus_condition_id ON promotion_web (promotion_web_bonus_condition_id)",
		"CREATE INDEX IF NOT EXISTS idx_promotion_web_bonus_type_id ON promotion_web (promotion_web_bonus_type_id)",
		"CREATE INDEX IF NOT EXISTS idx_promotion_web_turnover_type_id ON promotion_web (promotion_web_turnover_type_id)",
		"CREATE INDEX IF NOT EXISTS idx_promotion_web_date_type_id ON promotion_web (promotion_web_date_type_id)",
	}

	for _, indexSQL := range indexes {
		_, err = con.Exec(ctx, indexSQL)
		if err != nil {
			panic(err)
		}
	}

	// ================================
	// DEPENDENT TABLES (depend on promotion_web and members)
	// ================================

	// Create promotion_web_user table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_web_user (
			id BIGSERIAL PRIMARY KEY,
			promotion_web_id BIGINT NOT NULL,
			member_id BIGINT NOT NULL,
			promotion_web_user_status_id BIGINT NOT NULL,
			bonus_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			turnover_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			turnover_amount_current DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_promotion_web_user_promotion FOREIGN KEY (promotion_web_id) REFERENCES promotion_web(id),
			CONSTRAINT fk_promotion_web_user_member FOREIGN KEY (member_id) REFERENCES members(id),
			CONSTRAINT fk_promotion_web_user_status FOREIGN KEY (promotion_web_user_status_id) REFERENCES promotion_web_user_status(id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_web_user_confirm table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_web_user_confirm (
			id BIGSERIAL PRIMARY KEY,
			promotion_web_id BIGINT NOT NULL,
			member_id BIGINT NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_promotion_web_user_confirm_promotion FOREIGN KEY (promotion_web_id) REFERENCES promotion_web(id),
			CONSTRAINT fk_promotion_web_user_confirm_member FOREIGN KEY (member_id) REFERENCES members(id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_web_register_member table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_web_register_member (
			id BIGSERIAL PRIMARY KEY,
			promotion_web_id BIGINT NOT NULL,
			member_id BIGINT NOT NULL,
			promotion_web_register_member_status_id BIGINT NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_promotion_web_register_member_promotion FOREIGN KEY (promotion_web_id) REFERENCES promotion_web(id),
			CONSTRAINT fk_promotion_web_register_member_member FOREIGN KEY (member_id) REFERENCES members(id),
			CONSTRAINT fk_promotion_web_register_member_status FOREIGN KEY (promotion_web_register_member_status_id) REFERENCES promotion_web_register_member_status(id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_web_pg_hard_game table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_web_pg_hard_game (
			id BIGSERIAL PRIMARY KEY,
			promotion_web_id BIGINT NOT NULL,
			game_code VARCHAR(255) NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_promotion_web_pg_hard_game_promotion FOREIGN KEY (promotion_web_id) REFERENCES promotion_web(id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_web_pg_hard_game_user table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_web_pg_hard_game_user (
			id BIGSERIAL PRIMARY KEY,
			promotion_web_id BIGINT NOT NULL,
			member_id BIGINT NOT NULL,
			game_code VARCHAR(255) NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_promotion_web_pg_hard_game_user_promotion FOREIGN KEY (promotion_web_id) REFERENCES promotion_web(id),
			CONSTRAINT fk_promotion_web_pg_hard_game_user_member FOREIGN KEY (member_id) REFERENCES members(id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_web_lock_credit table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_web_lock_credit (
			id BIGSERIAL PRIMARY KEY,
			promotion_web_id BIGINT NOT NULL,
			member_id BIGINT NOT NULL,
			amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_promotion_web_lock_credit_promotion FOREIGN KEY (promotion_web_id) REFERENCES promotion_web(id),
			CONSTRAINT fk_promotion_web_lock_credit_member FOREIGN KEY (member_id) REFERENCES members(id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_web_user_log table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_web_user_log (
			id BIGSERIAL PRIMARY KEY,
			promotion_web_id BIGINT NOT NULL,
			member_id BIGINT NOT NULL,
			action VARCHAR(255) NOT NULL,
			description TEXT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_promotion_web_user_log_promotion FOREIGN KEY (promotion_web_id) REFERENCES promotion_web(id),
			CONSTRAINT fk_promotion_web_user_log_member FOREIGN KEY (member_id) REFERENCES members(id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// ================================
	// PROMOTION RETURN SYSTEM TABLES
	// ================================

	// Create promotion_return_turn_setting table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_return_turn_setting (
			id BIGSERIAL PRIMARY KEY,
			promotion_return_turn_type_id BIGINT NOT NULL,
			promotion_return_turn_cut_type_id BIGINT NOT NULL,
			name VARCHAR(255) NOT NULL DEFAULT '',
			description TEXT NOT NULL,
			is_active BOOLEAN NOT NULL DEFAULT TRUE,
			min_turn_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			max_turn_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			return_percent DECIMAL(5,2) NOT NULL DEFAULT 0.00,
			max_return_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			turnover_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_promotion_return_turn_setting_type FOREIGN KEY (promotion_return_turn_type_id) REFERENCES promotion_return_turn_type(id),
			CONSTRAINT fk_promotion_return_turn_setting_cut_type FOREIGN KEY (promotion_return_turn_cut_type_id) REFERENCES promotion_return_turn_cut_type(id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_return_setting table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_return_setting (
			id BIGSERIAL PRIMARY KEY,
			promotion_return_loser_type_id BIGINT NOT NULL,
			promotion_return_cut_type_id BIGINT NOT NULL,
			name VARCHAR(255) NOT NULL DEFAULT '',
			description TEXT NOT NULL,
			is_active BOOLEAN NOT NULL DEFAULT TRUE,
			min_lose_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			max_lose_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			return_percent DECIMAL(5,2) NOT NULL DEFAULT 0.00,
			max_return_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			turnover_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_promotion_return_setting_loser_type FOREIGN KEY (promotion_return_loser_type_id) REFERENCES promotion_return_loser_type(id),
			CONSTRAINT fk_promotion_return_setting_cut_type FOREIGN KEY (promotion_return_cut_type_id) REFERENCES promotion_return_cut_type(id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_return_turn table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_return_turn (
			id BIGSERIAL PRIMARY KEY,
			promotion_return_turn_setting_id BIGINT NOT NULL,
			promotion_return_turn_status_id BIGINT NOT NULL,
			member_id BIGINT NOT NULL,
			turn_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			return_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			turnover_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			turnover_amount_current DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			expired_at TIMESTAMP NULL,
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_promotion_return_turn_setting FOREIGN KEY (promotion_return_turn_setting_id) REFERENCES promotion_return_turn_setting(id),
			CONSTRAINT fk_promotion_return_turn_status FOREIGN KEY (promotion_return_turn_status_id) REFERENCES promotion_return_turn_status(id),
			CONSTRAINT fk_promotion_return_turn_member FOREIGN KEY (member_id) REFERENCES members(id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create promotion_return_loser table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS promotion_return_loser (
			id BIGSERIAL PRIMARY KEY,
			promotion_return_setting_id BIGINT NOT NULL,
			promotion_return_loser_status_id BIGINT NOT NULL,
			member_id BIGINT NOT NULL,
			lose_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			return_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			turnover_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			turnover_amount_current DECIMAL(10,2) NOT NULL DEFAULT 0.00,
			expired_at TIMESTAMP NULL,
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_promotion_return_loser_setting FOREIGN KEY (promotion_return_setting_id) REFERENCES promotion_return_setting(id),
			CONSTRAINT fk_promotion_return_loser_status FOREIGN KEY (promotion_return_loser_status_id) REFERENCES promotion_return_loser_status(id),
			CONSTRAINT fk_promotion_return_loser_member FOREIGN KEY (member_id) REFERENCES members(id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create additional indexes for performance
	additionalIndexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_promotion_web_user_promotion_id ON promotion_web_user (promotion_web_id)",
		"CREATE INDEX IF NOT EXISTS idx_promotion_web_user_member_id ON promotion_web_user (member_id)",
		"CREATE INDEX IF NOT EXISTS idx_promotion_web_user_status_id ON promotion_web_user (promotion_web_user_status_id)",
		"CREATE INDEX IF NOT EXISTS idx_promotion_web_user_confirm_promotion_id ON promotion_web_user_confirm (promotion_web_id)",
		"CREATE INDEX IF NOT EXISTS idx_promotion_web_user_confirm_member_id ON promotion_web_user_confirm (member_id)",
		"CREATE INDEX IF NOT EXISTS idx_promotion_web_register_member_promotion_id ON promotion_web_register_member (promotion_web_id)",
		"CREATE INDEX IF NOT EXISTS idx_promotion_web_register_member_member_id ON promotion_web_register_member (member_id)",
		"CREATE INDEX IF NOT EXISTS idx_promotion_web_lock_credit_promotion_id ON promotion_web_lock_credit (promotion_web_id)",
		"CREATE INDEX IF NOT EXISTS idx_promotion_web_lock_credit_member_id ON promotion_web_lock_credit (member_id)",
		"CREATE INDEX IF NOT EXISTS idx_promotion_return_turn_member_id ON promotion_return_turn (member_id)",
		"CREATE INDEX IF NOT EXISTS idx_promotion_return_turn_status_id ON promotion_return_turn (promotion_return_turn_status_id)",
		"CREATE INDEX IF NOT EXISTS idx_promotion_return_loser_member_id ON promotion_return_loser (member_id)",
		"CREATE INDEX IF NOT EXISTS idx_promotion_return_loser_status_id ON promotion_return_loser (promotion_return_loser_status_id)",
	}

	for _, indexSQL := range additionalIndexes {
		_, err = con.Exec(ctx, indexSQL)
		if err != nil {
			panic(err)
		}
	}

	log.Printf("Migration: Created all promotion system tables successfully")
}

func (m *CreatePromotions) Down(con pgx.Tx) {
	ctx := context.Background()

	// Drop tables in reverse order (considering dependencies)
	tables := []string{
		"DROP TABLE IF EXISTS promotion_return_loser CASCADE",
		"DROP TABLE IF EXISTS promotion_return_turn CASCADE",
		"DROP TABLE IF EXISTS promotion_return_setting CASCADE",
		"DROP TABLE IF EXISTS promotion_return_turn_setting CASCADE",
		"DROP TABLE IF EXISTS promotion_web_user_log CASCADE",
		"DROP TABLE IF EXISTS promotion_web_lock_credit CASCADE",
		"DROP TABLE IF EXISTS promotion_web_pg_hard_game_user CASCADE",
		"DROP TABLE IF EXISTS promotion_web_pg_hard_game CASCADE",
		"DROP TABLE IF EXISTS promotion_web_register_member CASCADE",
		"DROP TABLE IF EXISTS promotion_web_user_confirm CASCADE",
		"DROP TABLE IF EXISTS promotion_web_user CASCADE",
		"DROP TABLE IF EXISTS promotion_web CASCADE",
		"DROP TABLE IF EXISTS promotion_return_cut_type CASCADE",
		"DROP TABLE IF EXISTS promotion_return_loser_status CASCADE",
		"DROP TABLE IF EXISTS promotion_return_loser_type CASCADE",
		"DROP TABLE IF EXISTS promotion_return_turn_cut_type CASCADE",
		"DROP TABLE IF EXISTS promotion_return_turn_status CASCADE",
		"DROP TABLE IF EXISTS promotion_return_turn_type CASCADE",
		"DROP TABLE IF EXISTS promotion_web_register_member_status CASCADE",
		"DROP TABLE IF EXISTS promotion_web_user_status CASCADE",
		"DROP TABLE IF EXISTS promotion_web_turnover_type CASCADE",
		"DROP TABLE IF EXISTS promotion_web_bonus_type CASCADE",
		"DROP TABLE IF EXISTS promotion_web_bonus_condition CASCADE",
		"DROP TABLE IF EXISTS promotion_web_date_type CASCADE",
		"DROP TABLE IF EXISTS promotion_web_status CASCADE",
		"DROP TABLE IF EXISTS promotion_web_type CASCADE",
	}

	for _, dropSQL := range tables {
		_, err := con.Exec(ctx, dropSQL)
		if err != nil {
			panic(err)
		}
	}
}
