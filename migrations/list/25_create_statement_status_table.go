package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type CreateStatementStatusTable struct{}

func (m *CreateStatementStatusTable) GetName() string {
	return "20_create_statement_status_table"
}

func (m *CreateStatementStatusTable) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schema := os.Getenv("DATABASE_SCHEMA")
	if schema == "" {
		schema = "public"
	}

	queries := []string{
		// Set search path
		fmt.Sprintf("SET search_path TO %s", schema),

		// Create statement_status table
		`CREATE TABLE IF NOT EXISTS statement_status (
			id BIGSERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL UNIQUE,
			label_th VARCHAR(255),
			label_en VARCHAR(255),
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT NULL,
			deleted_at TIMESTAMP DEFAULT NULL
		)`,

		// Create indexes
		`CREATE INDEX idx_statement_status_name ON statement_status(name)`,
		`CREATE INDEX idx_statement_status_deleted_at ON statement_status(deleted_at)`,
	}

	for _, query := range queries {
		if _, err := con.Exec(ctx, query); err != nil {
			log.Printf("Error executing query: %s\nError: %v", query, err)
			panic(err)
		}
	}

	log.Println("Migration 20: Created statement_status table successfully")
}

func (m *CreateStatementStatusTable) Down(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schema := os.Getenv("DATABASE_SCHEMA")
	if schema == "" {
		schema = "public"
	}

	queries := []string{
		// Set search path
		fmt.Sprintf("SET search_path TO %s", schema),

		// Drop indexes
		`DROP INDEX IF EXISTS idx_statement_status_deleted_at`,
		`DROP INDEX IF EXISTS idx_statement_status_name`,

		// Drop table
		`DROP TABLE IF EXISTS statement_status CASCADE`,
	}

	for _, query := range queries {
		if _, err := con.Exec(ctx, query); err != nil {
			log.Printf("Error executing rollback query: %s\nError: %v", query, err)
			panic(err)
		}
	}

	log.Println("Migration 20: Rolled back statement_status table successfully")
}
