package list

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
)

// CreateCustomerFollowUpSystem migration struct
type CreateCustomerFollowUpSystem struct{}

// GetName returns the migration name
func (m *CreateCustomerFollowUpSystem) GetName() string {
	return "14_CreateCustomerFollowUpSystem"
}

// Up executes the migration
func (m *CreateCustomerFollowUpSystem) Up(con pgx.Tx) {
	ctx := context.Background()
	err := migration14CreateCustomerFollowUpSystem(ctx, con)
	if err != nil {
		panic(err)
	}
}

// Down rolls back the migration
func (m *CreateCustomerFollowUpSystem) Down(con pgx.Tx) {
	ctx := context.Background()
	fmt.Println("Rolling back Migration 14: Create customer follow-up system")

	// Drop tables in reverse order
	_, err := con.Exec(ctx, "DROP TABLE IF EXISTS customer_sms_logs CASCADE;")
	if err != nil {
		panic(err)
	}

	_, err = con.Exec(ctx, "DROP TABLE IF EXISTS customer_call_logs CASCADE;")
	if err != nil {
		panic(err)
	}

	// Remove columns from members table
	_, err = con.Exec(ctx, "ALTER TABLE members DROP COLUMN IF EXISTS follow_up_tag;")
	if err != nil {
		panic(err)
	}

	_, err = con.Exec(ctx, "ALTER TABLE members DROP COLUMN IF EXISTS follow_up_status;")
	if err != nil {
		panic(err)
	}

	_, err = con.Exec(ctx, "ALTER TABLE members DROP COLUMN IF EXISTS contacted_by;")
	if err != nil {
		panic(err)
	}

	_, err = con.Exec(ctx, "ALTER TABLE members DROP COLUMN IF EXISTS last_contact_at;")
	if err != nil {
		panic(err)
	}

	fmt.Println("Migration 14 rolled back successfully")
}

func migration14CreateCustomerFollowUpSystem(ctx context.Context, con pgx.Tx) error {
	fmt.Println("Running Migration 14: Create customer follow-up system")

	// Add new columns to members table
	fmt.Println("Adding new columns to members table...")
	_, err := con.Exec(ctx, `
		ALTER TABLE members 
		ADD COLUMN follow_up_tag VARCHAR(50),
		ADD COLUMN follow_up_status VARCHAR(20) DEFAULT 'not_contacted',
		ADD COLUMN contacted_by INTEGER REFERENCES users(id),
		ADD COLUMN last_contact_at TIMESTAMP;
	`)
	if err != nil {
		return fmt.Errorf("failed to add columns to members table: %w", err)
	}

	// Create customer_call_logs table
	fmt.Println("Creating customer_call_logs table...")
	_, err = con.Exec(ctx, `
		CREATE TABLE customer_call_logs (
			id SERIAL PRIMARY KEY,
			member_id INTEGER NOT NULL REFERENCES members(id) ON DELETE CASCADE,
			admin_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
			admin_name VARCHAR(255) NOT NULL,
			notes TEXT,
			call_duration INTEGER, -- duration in seconds
			call_status VARCHAR(20) DEFAULT 'completed', -- completed, missed, busy, no_answer
			created_at TIMESTAMP NOT NULL DEFAULT NOW(),
			updated_at TIMESTAMP NOT NULL DEFAULT NOW()
		);
	`)
	if err != nil {
		return fmt.Errorf("failed to create customer_call_logs table: %w", err)
	}

	// Create customer_sms_logs table
	fmt.Println("Creating customer_sms_logs table...")
	_, err = con.Exec(ctx, `
		CREATE TABLE customer_sms_logs (
			id SERIAL PRIMARY KEY,
			member_id INTEGER NOT NULL REFERENCES members(id) ON DELETE CASCADE,
			admin_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
			admin_name VARCHAR(255) NOT NULL,
			message_content TEXT NOT NULL,
			sms_status VARCHAR(20) DEFAULT 'sent', -- sent, failed, delivered, read
			provider_response JSON, -- response from SMS provider
			created_at TIMESTAMP NOT NULL DEFAULT NOW(),
			updated_at TIMESTAMP NOT NULL DEFAULT NOW()
		);
	`)
	if err != nil {
		return fmt.Errorf("failed to create customer_sms_logs table: %w", err)
	}

	// Create indexes for better performance
	fmt.Println("Creating indexes...")
	_, err = con.Exec(ctx, `
		CREATE INDEX idx_members_follow_up_tag ON members(follow_up_tag);
		CREATE INDEX idx_members_follow_up_status ON members(follow_up_status);
		CREATE INDEX idx_members_contacted_by ON members(contacted_by);
		CREATE INDEX idx_members_last_contact_at ON members(last_contact_at);
		
		CREATE INDEX idx_customer_call_logs_member_id ON customer_call_logs(member_id);
		CREATE INDEX idx_customer_call_logs_admin_id ON customer_call_logs(admin_id);
		CREATE INDEX idx_customer_call_logs_created_at ON customer_call_logs(created_at);
		
		CREATE INDEX idx_customer_sms_logs_member_id ON customer_sms_logs(member_id);
		CREATE INDEX idx_customer_sms_logs_admin_id ON customer_sms_logs(admin_id);
		CREATE INDEX idx_customer_sms_logs_created_at ON customer_sms_logs(created_at);
	`)
	if err != nil {
		return fmt.Errorf("failed to create indexes: %w", err)
	}

	// Add comments for documentation
	fmt.Println("Adding table comments...")
	_, err = con.Exec(ctx, `
		COMMENT ON COLUMN members.follow_up_tag IS 'Customer follow-up tag: cut_off, call_later, not_convenient, not_interested, no_money, no_answer, waiting_transfer';
		COMMENT ON COLUMN members.follow_up_status IS 'Follow-up status: contacted, unreachable, not_contacted';
		COMMENT ON COLUMN members.contacted_by IS 'Admin ID who last contacted this member';
		COMMENT ON COLUMN members.last_contact_at IS 'Last contact timestamp';
		
		COMMENT ON TABLE customer_call_logs IS 'Logs of customer phone calls for follow-up tracking';
		COMMENT ON TABLE customer_sms_logs IS 'Logs of customer SMS messages for follow-up tracking';
	`)
	if err != nil {
		return fmt.Errorf("failed to add comments: %w", err)
	}

	fmt.Println("Migration 14 completed successfully")
	return nil
}
