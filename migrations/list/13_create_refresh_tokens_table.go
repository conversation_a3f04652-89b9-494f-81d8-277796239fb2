package list

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
)

// CreateRefreshTokensTable migration struct
type CreateRefreshTokensTable struct{}

// GetName returns the migration name
func (m *CreateRefreshTokensTable) GetName() string {
	return "13_create_refresh_tokens_table"
}

// Up executes the migration
func (m *CreateRefreshTokensTable) Up(con pgx.Tx) {
	ctx := context.Background()
	err := migration13CreateRefreshTokensTable(ctx, con)
	if err != nil {
		panic(err)
	}
}

// Down rolls back the migration
func (m *CreateRefreshTokensTable) Down(con pgx.Tx) {
	ctx := context.Background()
	fmt.Println("Rolling back Migration 13: Create refresh_tokens table")

	// Drop table
	_, err := con.Exec(ctx, `DROP TABLE IF EXISTS refresh_tokens`)
	if err != nil {
		panic(fmt.Errorf("failed to drop refresh_tokens table: %v", err))
	}

	fmt.Println("Migration 13 rollback completed: refresh_tokens table dropped")
}

func migration13CreateRefreshTokensTable(ctx context.Context, con pgx.Tx) error {
	fmt.Println("Running Migration 13: Create refresh_tokens table")

	// Create refresh_tokens table
	_, err := con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS refresh_tokens (
			id SERIAL PRIMARY KEY,
			token VARCHAR(255) NOT NULL UNIQUE,
			user_id INT NULL,
			member_id INT NULL,
			user_type VARCHAR(10) NOT NULL CHECK (user_type IN ('admin', 'member')),
			expires_at TIMESTAMPTZ NOT NULL,
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_refresh_tokens_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
			CONSTRAINT fk_refresh_tokens_member_id FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
			CONSTRAINT check_user_or_member CHECK (
				(user_type = 'admin' AND user_id IS NOT NULL AND member_id IS NULL) OR
				(user_type = 'member' AND member_id IS NOT NULL AND user_id IS NULL)
			)
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create refresh_tokens table: %v", err)
	}

	// Create indexes
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_token ON refresh_tokens (token)",
		"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user_id ON refresh_tokens (user_id)",
		"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_member_id ON refresh_tokens (member_id)",
		"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_expires_at ON refresh_tokens (expires_at)",
		"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user_type ON refresh_tokens (user_type)",
	}

	for _, indexSQL := range indexes {
		_, err = con.Exec(ctx, indexSQL)
		if err != nil {
			return fmt.Errorf("failed to create index: %v", err)
		}
	}

	fmt.Println("Migration 13 completed: refresh_tokens table created with indexes")
	return nil
}
