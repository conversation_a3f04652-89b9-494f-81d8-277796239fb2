package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type AddGameCategoryApprovedDatetimeToReferralTransaction struct{}

func (m *AddGameCategoryApprovedDatetimeToReferralTransaction) GetName() string {
	return "AddGameCategoryApprovedDatetimeToReferralTransaction"
}

func (m *AddGameCategoryApprovedDatetimeToReferralTransaction) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public" // default schema
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	log.Printf("Migration: Setting search_path to '%s'", schemaName)
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}
	log.Printf("Migration: search_path set successfully")

	// Verify current schema
	var currentSchema string
	err = con.QueryRow(ctx, "SELECT current_schema()").Scan(&currentSchema)
	if err == nil {
		log.Printf("Migration: Current schema is '%s'", currentSchema)
	}

	// Rename table from referral_transaction to referral_transactions
	log.Printf("Migration: Renaming referral_transaction table to referral_transactions")
	_, err = con.Exec(ctx, `ALTER TABLE referral_transaction RENAME TO referral_transactions`)
	if err != nil {
		panic(fmt.Sprintf("Failed to rename table to referral_transactions: %v", err))
	}
	log.Printf("Migration: Table renamed to referral_transactions successfully")

	// Add game_category column (optional)
	log.Printf("Migration: Adding game_category column to referral_transactions table")
	_, err = con.Exec(ctx, `
		ALTER TABLE referral_transactions 
		ADD COLUMN IF NOT EXISTS game_category VARCHAR(50) NULL
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add game_category column: %v", err))
	}
	log.Printf("Migration: game_category column added successfully")

	// Add approved_datetime column (optional)
	log.Printf("Migration: Adding approved_datetime column to referral_transactions table")
	_, err = con.Exec(ctx, `
		ALTER TABLE referral_transactions 
		ADD COLUMN IF NOT EXISTS approved_datetime TIMESTAMP NULL
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add approved_datetime column: %v", err))
	}
	log.Printf("Migration: approved_datetime column added successfully")

	// Create index for game_category
	_, err = con.Exec(ctx, `CREATE INDEX IF NOT EXISTS idx_referral_transactions_game_category ON referral_transactions(game_category)`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create game_category index: %v", err))
	}

	// Create index for approved_datetime
	_, err = con.Exec(ctx, `CREATE INDEX IF NOT EXISTS idx_referral_transactions_approved_datetime ON referral_transactions(approved_datetime)`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create approved_datetime index: %v", err))
	}

	log.Printf("Migration: AddGameCategoryApprovedDatetimeToReferralTransaction completed successfully")
}

func (m *AddGameCategoryApprovedDatetimeToReferralTransaction) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	// Drop indexes first
	_, err = con.Exec(ctx, `DROP INDEX IF EXISTS idx_referral_transactions_approved_datetime`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop approved_datetime index: %v", err))
	}

	_, err = con.Exec(ctx, `DROP INDEX IF EXISTS idx_referral_transactions_game_category`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop game_category index: %v", err))
	}

	// Remove approved_datetime column
	log.Printf("Migration: Removing approved_datetime column from referral_transactions table")
	_, err = con.Exec(ctx, `
		ALTER TABLE referral_transactions 
		DROP COLUMN IF EXISTS approved_datetime
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to remove approved_datetime column: %v", err))
	}
	log.Printf("Migration: approved_datetime column removed successfully")

	// Remove game_category column
	log.Printf("Migration: Removing game_category column from referral_transactions table")
	_, err = con.Exec(ctx, `
		ALTER TABLE referral_transactions 
		DROP COLUMN IF EXISTS game_category
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to remove game_category column: %v", err))
	}
	log.Printf("Migration: game_category column removed successfully")

	// Rename table back from referral_transactions to referral_transaction
	log.Printf("Migration: Renaming referral_transactions table back to referral_transaction")
	_, err = con.Exec(ctx, `ALTER TABLE referral_transactions RENAME TO referral_transaction`)
	if err != nil {
		panic(fmt.Sprintf("Failed to rename table back to referral_transaction: %v", err))
	}
	log.Printf("Migration: Table renamed back to referral_transaction successfully")

	log.Printf("Migration: AddGameCategoryApprovedDatetimeToReferralTransaction rollback completed successfully")
}