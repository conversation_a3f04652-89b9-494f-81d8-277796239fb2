package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type CreateReferralRegisterTable struct{}

func (m *CreateReferralRegisterTable) GetName() string {
	return "CreateReferralRegisterTable"
}

func (m *CreateReferralRegisterTable) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public" // default schema
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	log.Printf("Migration: Setting search_path to '%s'", schemaName)
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}
	log.Printf("Migration: search_path set successfully")

	// Verify current schema
	var currentSchema string
	err = con.QueryRow(ctx, "SELECT current_schema()").Scan(&currentSchema)
	if err == nil {
		log.Printf("Migration: Current schema is '%s'", currentSchema)
	}

	// Create referral_transaction table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS referral_transaction (
			id SERIAL PRIMARY KEY,
			member_id INT NOT NULL,
			downline_member_id INT DEFAULT NULL,
			type VARCHAR(20) NOT NULL DEFAULT '',
			amount DECIMAL(15, 2) NOT NULL DEFAULT 0.00,
			balance_before DECIMAL(15, 2) NOT NULL DEFAULT 0.00,
			balance_after DECIMAL(15, 2) NOT NULL DEFAULT 0.00,
			referral_start_date TIMESTAMP NULL,
			referral_end_date TIMESTAMP NULL,
			created_by_admin INT DEFAULT NULL,
			created_by_member INT DEFAULT NULL,
			status VARCHAR(20) NOT NULL DEFAULT 'pending',
			remark TEXT,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create referral_transaction table: %v", err))
	}

	log.Printf("Migration: referral_transaction table created successfully")

	// Create indexes for better query performance
	_, err = con.Exec(ctx, `CREATE INDEX IF NOT EXISTS idx_referral_transaction_member_id ON referral_transaction(member_id)`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create member_id index: %v", err))
	}

	_, err = con.Exec(ctx, `CREATE INDEX IF NOT EXISTS idx_referral_transaction_downline_member_id ON referral_transaction(downline_member_id)`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create downline_member_id index: %v", err))
	}

	_, err = con.Exec(ctx, `CREATE INDEX IF NOT EXISTS idx_referral_transaction_created_at ON referral_transaction(created_at)`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create created_at index: %v", err))
	}

	_, err = con.Exec(ctx, `CREATE INDEX IF NOT EXISTS idx_referral_transaction_start_date ON referral_transaction(referral_start_date)`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create referral_start_date index: %v", err))
	}

	_, err = con.Exec(ctx, `CREATE INDEX IF NOT EXISTS idx_referral_transaction_end_date ON referral_transaction(referral_end_date)`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create referral_end_date index: %v", err))
	}

	_, err = con.Exec(ctx, `CREATE INDEX IF NOT EXISTS idx_referral_transaction_created_by_admin ON referral_transaction(created_by_admin)`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create created_by_admin index: %v", err))
	}

	_, err = con.Exec(ctx, `CREATE INDEX IF NOT EXISTS idx_referral_transaction_created_by_member ON referral_transaction(created_by_member)`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create created_by_member index: %v", err))
	}

	_, err = con.Exec(ctx, `CREATE INDEX IF NOT EXISTS idx_referral_transaction_status ON referral_transaction(status)`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create status index: %v", err))
	}

	_, err = con.Exec(ctx, `CREATE INDEX IF NOT EXISTS idx_referral_transaction_type ON referral_transaction(type)`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create type index: %v", err))
	}

	_, err = con.Exec(ctx, `CREATE INDEX IF NOT EXISTS idx_referral_transaction_amount ON referral_transaction(amount)`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create amount index: %v", err))
	}

	_, err = con.Exec(ctx, `CREATE INDEX IF NOT EXISTS idx_referral_transaction_balance_before ON referral_transaction(balance_before)`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create balance_before index: %v", err))
	}

	_, err = con.Exec(ctx, `CREATE INDEX IF NOT EXISTS idx_referral_transaction_balance_after ON referral_transaction(balance_after)`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create balance_after index: %v", err))
	}

	log.Printf("Migration: referral_transaction indexes created successfully")

	// Alter members table to add referral fields
	log.Printf("Migration: Adding referral fields to members table")

	// Add commission_balance column
	_, err = con.Exec(ctx, `
		ALTER TABLE members 
		ADD COLUMN IF NOT EXISTS commission_balance DECIMAL(15, 2) NOT NULL DEFAULT 0.00
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add commission_balance column: %v", err))
	}

	// Add downline_count column
	_, err = con.Exec(ctx, `
		ALTER TABLE members 
		ADD COLUMN IF NOT EXISTS downline_count INTEGER NOT NULL DEFAULT 0
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add downline_count column: %v", err))
	}

	// Add referral_view_count column
	_, err = con.Exec(ctx, `
		ALTER TABLE members 
		ADD COLUMN IF NOT EXISTS referral_view_count INTEGER NOT NULL DEFAULT 0
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add referral_view_count column: %v", err))
	}

	// Add last_commission_cal column
	_, err = con.Exec(ctx, `
		ALTER TABLE members 
		ADD COLUMN IF NOT EXISTS last_commission_cal TIMESTAMP
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add last_commission_cal column: %v", err))
	}

	// Add next_commission_cal column
	_, err = con.Exec(ctx, `
		ALTER TABLE members 
		ADD COLUMN IF NOT EXISTS next_commission_cal TIMESTAMP
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add next_commission_cal column: %v", err))
	}

	// Create indexes for new columns
	_, err = con.Exec(ctx, `CREATE INDEX IF NOT EXISTS idx_members_commission_balance ON members(commission_balance)`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create commission_balance index: %v", err))
	}

	_, err = con.Exec(ctx, `CREATE INDEX IF NOT EXISTS idx_members_downline_count ON members(downline_count)`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create downline_count index: %v", err))
	}

	// Create indexes for commission calculation dates
	_, err = con.Exec(ctx, `CREATE INDEX IF NOT EXISTS idx_members_next_commission_cal ON members(next_commission_cal)`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create next_commission_cal index: %v", err))
	}

	log.Printf("Migration: Member referral fields added successfully")
}

func (m *CreateReferralRegisterTable) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	// Drop member indexes first
	_, err = con.Exec(ctx, `DROP INDEX IF EXISTS idx_members_next_commission_cal`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop members next_commission_cal index: %v", err))
	}

	_, err = con.Exec(ctx, `DROP INDEX IF EXISTS idx_members_downline_count`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop members downline_count index: %v", err))
	}

	_, err = con.Exec(ctx, `DROP INDEX IF EXISTS idx_members_commission_balance`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop members commission_balance index: %v", err))
	}

	// Drop member columns
	_, err = con.Exec(ctx, `ALTER TABLE members DROP COLUMN IF EXISTS next_commission_cal`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop next_commission_cal column: %v", err))
	}

	_, err = con.Exec(ctx, `ALTER TABLE members DROP COLUMN IF EXISTS last_commission_cal`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop last_commission_cal column: %v", err))
	}

	_, err = con.Exec(ctx, `ALTER TABLE members DROP COLUMN IF EXISTS referral_view_count`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop referral_view_count column: %v", err))
	}

	_, err = con.Exec(ctx, `ALTER TABLE members DROP COLUMN IF EXISTS downline_count`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop downline_count column: %v", err))
	}

	_, err = con.Exec(ctx, `ALTER TABLE members DROP COLUMN IF EXISTS commission_balance`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop commission_balance column: %v", err))
	}

	// Drop referral_transaction indexes
	_, err = con.Exec(ctx, `DROP INDEX IF EXISTS idx_referral_transaction_balance_after`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop balance_after index: %v", err))
	}

	_, err = con.Exec(ctx, `DROP INDEX IF EXISTS idx_referral_transaction_balance_before`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop balance_before index: %v", err))
	}

	_, err = con.Exec(ctx, `DROP INDEX IF EXISTS idx_referral_transaction_amount`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop amount index: %v", err))
	}

	_, err = con.Exec(ctx, `DROP INDEX IF EXISTS idx_referral_transaction_type`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop type index: %v", err))
	}

	_, err = con.Exec(ctx, `DROP INDEX IF EXISTS idx_referral_transaction_status`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop status index: %v", err))
	}

	_, err = con.Exec(ctx, `DROP INDEX IF EXISTS idx_referral_transaction_created_by_member`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop created_by_member index: %v", err))
	}

	_, err = con.Exec(ctx, `DROP INDEX IF EXISTS idx_referral_transaction_created_by_admin`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop created_by_admin index: %v", err))
	}

	_, err = con.Exec(ctx, `DROP INDEX IF EXISTS idx_referral_transaction_end_date`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop referral_end_date index: %v", err))
	}

	_, err = con.Exec(ctx, `DROP INDEX IF EXISTS idx_referral_transaction_start_date`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop referral_start_date index: %v", err))
	}

	_, err = con.Exec(ctx, `DROP INDEX IF EXISTS idx_referral_transaction_created_at`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop created_at index: %v", err))
	}

	_, err = con.Exec(ctx, `DROP INDEX IF EXISTS idx_referral_transaction_downline_member_id`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop downline_member_id index: %v", err))
	}

	_, err = con.Exec(ctx, `DROP INDEX IF EXISTS idx_referral_transaction_member_id`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop member_id index: %v", err))
	}

	// Drop the referral_transaction table
	_, err = con.Exec(ctx, `DROP TABLE IF EXISTS referral_transaction CASCADE`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop referral_transaction table: %v", err))
	}

	log.Printf("Migration: referral_transaction table and indexes dropped successfully")
}
