version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: blacking-postgres
    environment:
      POSTGRES_DB: blacking_db
      POSTGRES_USER: blacking_user
      POSTGRES_PASSWORD: blacking_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - blacking-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U blacking_user -d blacking_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: blacking-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - blacking-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Migration Service
  migration:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: blacking-migration
    environment:
      - APP_ENV=development
      - ENABLE_SEEDERS=true
      - DATABASE_SCHEMA=jumbo8888
    command: ["./startup.sh", "development"]
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - blacking-network
    volumes:
      - ./migrations:/app/migrations
      - ./configs:/app/configs

  # API Server
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: blacking-api
    environment:
      - APP_ENV=development
      - BLACKING_DATABASE_HOST=postgres
      - BLACKING_DATABASE_PORT=5432
      - BLACKING_DATABASE_USERNAME=blacking_user
      - BLACKING_DATABASE_PASSWORD=blacking_password
      - BLACKING_DATABASE_DBNAME=blacking_db
      - BLACKING_REDIS_HOST=redis
      - BLACKING_REDIS_PORT=6379
      - ENABLE_SEEDERS=true
      - DATABASE_SCHEMA=jumbo8888
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      migration:
        condition: service_completed_successfully
    networks:
      - blacking-network
    volumes:
      - ./configs:/app/configs
    restart: unless-stopped

  # Worker Service
  worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: blacking-worker
    environment:
      - APP_ENV=development
      - BLACKING_DATABASE_HOST=postgres
      - BLACKING_DATABASE_PORT=5432
      - BLACKING_DATABASE_USERNAME=blacking_user
      - BLACKING_DATABASE_PASSWORD=blacking_password
      - BLACKING_DATABASE_DBNAME=blacking_db
      - BLACKING_REDIS_HOST=redis
      - BLACKING_REDIS_PORT=6379
    command: ["./worker", "-env", "development"]
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - blacking-network
    volumes:
      - ./configs:/app/configs
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  blacking-network:
    driver: bridge