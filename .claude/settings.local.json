{"permissions": {"allow": ["Bash(go build:*)", "Bash(grep:*)", "<PERSON><PERSON>(go test:*)", "Bash(go install:*)", "Bash(rm:*)", "Bash(psql:*)", "Bash(find:*)", "<PERSON><PERSON>(go run:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(curl:*)", "Bash(./test-agapi)", "<PERSON><PERSON>(chmod:*)", "Bash(./test-agent-structure.sh:*)", "Bash(./test-member-registration.sh:*)", "Bash(lsof:*)", "Bash(kill:*)", "<PERSON><PERSON>(echo:*)", "Bash(unset:*)", "Bash(./test-docker-seeders.sh:*)", "<PERSON><PERSON>(make:*)", "<PERSON><PERSON>(source .env)", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SELECT table_name FROM information_schema.tables WHERE table_schema = ''irichdev'' AND table_name = ''banners'';\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SELECT COUNT(*) FROM irichdev.banners;\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SELECT id, position, name, type, link_url, image_url, status, deleted_by_username, deleted_at, created_at, updated_at FROM irichdev.banners WHERE status = ''active'' ORDER BY COALESCE(position, 999999), created_at DESC LIMIT 10 OFFSET 0;\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SELECT table_name FROM information_schema.tables WHERE table_schema = ''irichdev'' ORDER BY table_name;\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SELECT column_name, data_type, is_nullable, column_default FROM information_schema.columns WHERE table_schema = ''irichdev'' AND table_name = ''payment_gateway_account'' ORDER BY ordinal_position;\")", "<PERSON><PERSON>(env)", "Bash(PGPASSWORD=zt6fsDPKegYKRxeP psql -h blacking-dev-1.cluster-ctiwc8mygfg5.ap-southeast-1.rds.amazonaws.com -p 5432 -U superadmin -d blacking -c \"SET search_path TO irichdev; SELECT id, account_name, code, provider, merchant_code, secret_key, secret_key_two, COALESCE(api_key, '''') as api_key, COALESCE(base_url, '''') as base_url, first_username, second_username, first_password, second_password, minimum_withdraw, maximum_withdraw, withdraw_splitting, maximum_withdraw_per_transaction, maximum_split_withdraw_per_transaction, COALESCE(timeout_seconds, 30) as timeout_seconds, COALESCE(max_retries, 3) as max_retries, COALESCE(retry_delay_seconds, 1) as retry_delay_seconds, COALESCE(enable_request_log, true) as enable_request_log, COALESCE(log_response_body, false) as log_response_body, COALESCE(enable_debug, false) as enable_debug, is_deposit, is_withdraw, is_transfer, active, inactive, created_at, updated_at FROM payment_gateway_account ORDER BY created_at DESC;\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SELECT table_name FROM information_schema.tables WHERE table_schema = ''irichdev'' AND table_name = ''payment_gateway_account'';\")", "Bash(./bin/server:*)", "<PERSON><PERSON>(timeout:*)", "Bash(GO_LOG_LEVEL=debug ./bin/server)", "<PERSON><PERSON>(gtimeout:*)", "<PERSON><PERSON>(pkill:*)", "Bash(./bin/migrate:*)", "Bash(GO_LOG_LEVEL=debug ./cmd/test-agapi/main.go)", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SET search_path TO irichdev; SELECT id, username, game_username, phone, status, created_at FROM members WHERE id = 33;\")", "Bash(go get:*)", "Bash(go mod:*)", "<PERSON><PERSON>(go clean:*)", "Bash(./bin/websocket-test:*)", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SELECT column_name, data_type, is_nullable, column_default FROM information_schema.columns WHERE table_schema = ''irichdev'' AND table_name = ''payment_gateway_transactions'' ORDER BY ordinal_position;\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SELECT column_name, data_type, is_nullable FROM information_schema.columns WHERE table_schema = ''irichdev'' AND table_name = ''payment_gateway_transactions'' ORDER BY ordinal_position;\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SELECT COUNT(*) FROM irichdev.payment_gateway_transactions WHERE transaction_type = ''WITHDRAW'';\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SELECT id, transaction_id, customer_username, customer_reference, amount, status, created_at FROM irichdev.payment_gateway_transactions WHERE transaction_type = ''WITHDRAW'' LIMIT 2;\")", "<PERSON>sh(swag init:*)", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SELECT table_name FROM information_schema.tables WHERE table_schema = ''irichdev'' AND table_name IN (''blockchain_networks'', ''chain_tokens'', ''backend_wallets'');\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SELECT table_name FROM information_schema.tables WHERE table_schema = ''irichdev'' AND table_name IN (''blockchain_networks'', ''chain_tokens'', ''backend_wallets'');\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SET search_path TO irichdev; SELECT table_name FROM information_schema.tables WHERE table_schema = ''irichdev'' AND table_name IN (''blockchain_networks'', ''chain_tokens'', ''backend_wallets'', ''crypto_deposit_logs'');\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SET search_path TO irichdev; SELECT * FROM chain_tokens ORDER BY blockchain_network_id;\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SET search_path TO irichdev; SELECT code, account_name, provider FROM payment_gateway_account WHERE code = ''CRYPTO'';\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev; \nSELECT ''blockchain_networks'' as table_name, COUNT(*) as count FROM blockchain_networks WHERE is_active = true\nUNION ALL\nSELECT ''chain_tokens'' as table_name, COUNT(*) as count FROM chain_tokens WHERE is_active = true\nUNION ALL  \nSELECT ''backend_wallets'' as table_name, COUNT(*) as count FROM backend_wallets WHERE is_active = true\nUNION ALL\nSELECT ''payment_gateway_account'' as table_name, COUNT(*) as count FROM payment_gateway_account WHERE code = ''CRYPTO'';\n\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev; \nSELECT id, chain_id, network_name, network_type, rpc_url, explorer_url, \n       native_currency_symbol, is_active, created_at, updated_at\nFROM blockchain_networks \nWHERE is_active = true\nORDER BY chain_id;\n\")", "Bash(docker build:*)", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SET search_path TO irichdev; SELECT column_name, data_type FROM information_schema.columns WHERE table_name = ''promotion_web_lock_credit'' ORDER BY ordinal_position;\")", "WebFetch(domain:api.irich.info)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(jq:*)", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SET search_path TO irichdev; SELECT column_name, data_type, is_nullable FROM information_schema.columns WHERE table_name = ''blockchain_networks'' ORDER BY ordinal_position;\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"SET search_path TO irichdev; SELECT id, code, account_name, provider, active, is_deposit, is_withdraw FROM payment_gateway_account WHERE code = ''CRYPTO'';\")", "Bash(PGPASSWORD=zt6fsDPKegYKRxeP psql -h blacking-dev-1.cluster-ctiwc8mygfg5.ap-southeast-1.rds.amazonaws.com -p 5432 -U superadmin -d blacking -c \"SET search_path TO irichdev; SELECT id, account_name, code, provider, merchant_code, secret_key, secret_key_two, COALESCE(api_key, '''') as api_key, COALESCE(base_url, '''') as base_url, first_username, second_username, first_password, second_password, minimum_withdraw, maximum_withdraw, withdraw_splitting, maximum_withdraw_per_transaction, maximum_split_withdraw_per_transaction, COALESCE(timeout_seconds, 30) as timeout_seconds, COALESCE(max_retries, 3) as max_retries, COALESCE(retry_delay_seconds, 1) as retry_delay_seconds, COALESCE(enable_request_log, true) as enable_request_log, COALESCE(log_response_body, false) as log_response_body, COALESCE(enable_debug, false) as enable_debug, is_deposit, is_withdraw, is_transfer, active, inactive, created_at, updated_at FROM payment_gateway_account WHERE code = ''CRYPTO'';\")", "Bash(PGPASSWORD=zt6fsDPKegYKRxeP psql -h blacking-dev-1.cluster-ctiwc8mygfg5.ap-southeast-1.rds.amazonaws.com -p 5432 -U superadmin -d blacking -c \"SELECT id, account_name, code, provider, merchant_code, secret_key, secret_key_two, COALESCE(api_key, '''') as api_key, COALESCE(base_url, '''') as base_url, first_username, second_username, first_password, second_password, minimum_withdraw, maximum_withdraw, withdraw_splitting, maximum_withdraw_per_transaction, maximum_split_withdraw_per_transaction, COALESCE(timeout_seconds, 30) as timeout_seconds, COALESCE(max_retries, 3) as max_retries, COALESCE(retry_delay_seconds, 1) as retry_delay_seconds, COALESCE(enable_request_log, true) as enable_request_log, COALESCE(log_response_body, false) as log_response_body, COALESCE(enable_debug, false) as enable_debug, is_deposit, is_withdraw, is_transfer, active, inactive, created_at, updated_at FROM irichdev.payment_gateway_account WHERE code = ''CRYPTO'';\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev; \nSELECT table_name FROM information_schema.tables \nWHERE table_schema = ''irichdev'' \nAND table_name IN (''blockchain_networks'', ''chain_tokens'', ''backend_wallets'', ''crypto_deposit_logs'', ''payment_gateway_account'')\nORDER BY table_name;\n\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev; \nSELECT ''blockchain_networks'' as table_name, COUNT(*) as count FROM blockchain_networks WHERE is_active = true\nUNION ALL\nSELECT ''chain_tokens'' as table_name, COUNT(*) as count FROM chain_tokens WHERE is_active = true\nUNION ALL  \nSELECT ''backend_wallets'' as table_name, COUNT(*) as count FROM backend_wallets WHERE is_active = true\nUNION ALL\nSELECT ''payment_gateway_account'' as table_name, COUNT(*) as count FROM payment_gateway_account WHERE code = ''CRYPTO'';\n\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev; \nSELECT * FROM backend_wallets WHERE is_active = true ORDER BY blockchain_network_id;\n\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev; \nSELECT column_name, data_type, is_nullable FROM information_schema.columns \nWHERE table_name = ''backend_wallets'' \nORDER BY ordinal_position;\n\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev; \nSELECT * FROM backend_wallets WHERE is_active = true ORDER BY chain_id;\n\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev; \nSELECT id, code, account_name, provider, active, is_deposit, is_withdraw FROM payment_gateway_account WHERE code = ''CRYPTO'';\n\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev; \nSELECT column_name, data_type, is_nullable FROM information_schema.columns \nWHERE table_name = ''payment_gateway_transactions'' \nORDER BY ordinal_position;\n\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev;\nSELECT COUNT(*) FROM payment_gateway_transactions WHERE transaction_type = ''DEPOSIT'';\n\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev;\nINSERT INTO payment_gateway_transactions (\n    transaction_id, internal_reference, payment_gateway_account_id, provider,\n    provider_merchant_id, provider_order_id, transaction_type, amount, currency,\n    fee_amount, net_amount, status, customer_reference, customer_username,\n    customer_bank_account, customer_bank_name, callback_url, return_url, payment_url,\n    qr_code, qr_text, qr_image_url, initiated_at, expires_at, description, metadata, \n    provider_response, blockchain_data, created_by\n) VALUES (\n    ''test_insert_debug'', NULL, 3, ''BLOCKCHAIN'',\n    ''BLOCKCHAIN'', NULL, ''DEPOSIT'', 15.0, ''USDT'',\n    NULL, NULL, ''INITIATED'', NULL, ''**********'',\n    NULL, NULL, NULL, NULL, NULL,\n    NULL, NULL, NULL, NOW(), NOW() + INTERVAL ''30 minutes'', ''Test insert'', ''{}'',\n    ''{}'', ''{\"\"test\"\": true}'', ''**********''\n) RETURNING id;\n\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev;\nSELECT column_name, data_type, character_maximum_length \nFROM information_schema.columns \nWHERE table_name = ''payment_gateway_transactions'' \nAND data_type = ''character varying''\nORDER BY ordinal_position;\n\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev;\nINSERT INTO payment_gateway_transactions (\n    transaction_id, payment_gateway_account_id, provider,\n    transaction_type, amount, currency, status, customer_username,\n    initiated_at, expires_at, description, metadata, \n    provider_response, blockchain_data, created_by\n) VALUES (\n    ''test_insert_debug2'', 3, ''BLOCKCHAIN'',\n    ''DEPOSIT'', 15.0, ''USD'',\n    ''INITIATED'', ''**********'',\n    NOW(), NOW() + INTERVAL ''30 minutes'', ''Test insert'', ''{}'',\n    ''{}'', ''{\"\"test\"\": true}'', ''**********''\n) RETURNING id;\n\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev;\nSELECT DISTINCT currency FROM payment_gateway_transactions ORDER BY currency;\n\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev;\nALTER TABLE payment_gateway_transactions ALTER COLUMN currency TYPE VARCHAR(10);\n\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev;\nSELECT column_name, data_type, is_nullable FROM information_schema.columns \nWHERE table_name = ''crypto_deposit_logs'' \nORDER BY ordinal_position;\n\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev;\nINSERT INTO crypto_deposit_logs (\n    transaction_id, step_number, event_type, event_data\n) VALUES (\n    ''test_log_debug'', 0, ''INITIATED'', ''{\"\"test\"\": true}''\n) RETURNING id, timestamp;\n\")", "Bash(PGPASSWORD=$DATABASE_PASSWORD psql -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USERNAME -d $DATABASE_DBNAME -c \"\nSET search_path TO irichdev;\nSELECT constraint_name, check_clause \nFROM information_schema.check_constraints \nWHERE constraint_name LIKE ''%crypto_deposit_logs%'';\n\")"], "deny": []}}