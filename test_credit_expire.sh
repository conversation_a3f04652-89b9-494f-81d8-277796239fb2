#!/bin/bash

# Test script for credit_expire_days field

echo "Testing Return Turn Settings API with credit_expire_days field"
echo "=============================================================="

# Start the server in the background
echo "Starting server..."
./bin/server -env=local &
SERVER_PID=$!

# Wait for server to start
sleep 3

# Get current settings
echo -e "\n1. Getting current return turn settings:"
curl -X GET http://localhost:8080/api/v1/admin/return-turn/setting \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" | jq '.'

# Update settings with credit_expire_days
echo -e "\n2. Updating return turn settings with credit_expire_days:"
curl -X PATCH http://localhost:8080/api/v1/admin/return-turn/setting \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "returnPercent": 10.5,
    "creditExpireDays": 14,
    "minLossPrice": 1000,
    "maxReturnPrice": 5000,
    "isEnabled": true
  }' | jq '.'

# Get updated settings
echo -e "\n3. Getting updated return turn settings:"
curl -X GET http://localhost:8080/api/v1/admin/return-turn/setting \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" | jq '.'

# Kill the server
echo -e "\n4. Stopping server..."
kill $SERVER_PID

echo -e "\nTest completed!"