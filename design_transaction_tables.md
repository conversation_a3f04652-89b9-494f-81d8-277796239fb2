# Payment Gateway Transaction Tables Design

## Overview
ออกแบบ table structure เพื่อเก็บ transaction log จากหลาย payment gateway และรองรับ webhook data ครบถ้วน

## Table Structure

### 1. payment_gateway_transactions (Main Transaction Table)

```sql
CREATE TABLE payment_gateway_transactions (
    -- Primary Information
    id                          BIGSERIAL PRIMARY KEY,
    transaction_id              VARCHAR(255) NOT NULL UNIQUE,           -- External transaction ID from provider
    internal_reference          VARCHAR(255),                           -- Internal reference (optional)
    
    -- Gateway Information
    payment_gateway_account_id  BIGINT NOT NULL,                        -- FK to payment_gateway_account
    provider                    VARCHAR(50) NOT NULL,                   -- jaijaipay, linepay, promptpay, etc.
    provider_merchant_id        VARCHAR(255),                           -- Provider's merchant ID
    provider_order_id           VARCHAR(255),                           -- Provider's order ID
    
    -- Transaction Details  
    transaction_type            VARCHAR(20) NOT NULL,                   -- DEPOSIT, WITHDRAW, TRANSFER
    amount                      DECIMAL(15,2) NOT NULL,                 -- Transaction amount
    currency                    VARCHAR(3) NOT NULL DEFAULT 'THB',     -- Currency code
    fee_amount                  DECIMAL(15,2) DEFAULT 0,                -- Fee charged
    net_amount                  DECIMAL(15,2),                          -- Amount after fees
    
    -- Status Tracking
    status                      VARCHAR(50) NOT NULL,                   -- PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED, EXPIRED
    previous_status             VARCHAR(50),                            -- Previous status for audit
    status_updated_at           TIMESTAMP WITH TIME ZONE,               -- When status was last updated
    
    -- Customer Information
    customer_reference          VARCHAR(255),                           -- Customer/Member reference
    customer_username           VARCHAR(255),                           -- Username if applicable
    customer_bank_account       VARCHAR(255),                           -- Bank account for withdrawals
    customer_bank_name          VARCHAR(255),                           -- Bank name
    
    -- URLs and Callbacks
    callback_url                TEXT,                                   -- Webhook callback URL
    return_url                  TEXT,                                   -- Return URL after payment
    payment_url                 TEXT,                                   -- Payment URL for deposits
    
    -- Transaction Lifecycle
    initiated_at                TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),  -- When transaction was created
    expires_at                  TIMESTAMP WITH TIME ZONE,                         -- Expiration time
    completed_at                TIMESTAMP WITH TIME ZONE,                         -- When completed
    failed_at                   TIMESTAMP WITH TIME ZONE,                         -- When failed
    
    -- Webhook Information
    webhook_received_at         TIMESTAMP WITH TIME ZONE,               -- When first webhook received
    webhook_count               INTEGER DEFAULT 0,                     -- Number of webhooks received
    last_webhook_at             TIMESTAMP WITH TIME ZONE,               -- Last webhook timestamp
    webhook_status              VARCHAR(50),                           -- Status from webhook
    
    -- Metadata and Additional Information
    description                 TEXT,                                   -- Transaction description
    metadata                    JSONB,                                  -- Additional metadata from provider
    provider_response           JSONB,                                  -- Full response from provider
    error_code                  VARCHAR(50),                            -- Error code if failed
    error_message               TEXT,                                   -- Error message if failed
    
    -- Audit Fields
    created_at                  TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at                  TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_by                  VARCHAR(255),                           -- User who created
    updated_by                  VARCHAR(255),                           -- User who last updated
    
    -- Indexes
    CONSTRAINT fk_payment_gateway_account FOREIGN KEY (payment_gateway_account_id) 
        REFERENCES payment_gateway_account(id),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_provider (provider),
    INDEX idx_status (status),
    INDEX idx_customer_reference (customer_reference),
    INDEX idx_created_at (created_at),
    INDEX idx_webhook_received (webhook_received_at),
    INDEX idx_gateway_provider (payment_gateway_account_id, provider)
);
```

### 2. payment_gateway_webhooks (Webhook Log Table)

```sql
CREATE TABLE payment_gateway_webhooks (
    -- Primary Information
    id                          BIGSERIAL PRIMARY KEY,
    transaction_id              VARCHAR(255) NOT NULL,                 -- FK to transaction_id in main table
    
    -- Webhook Details
    webhook_event               VARCHAR(100) NOT NULL,                 -- Event type (transaction.completed, etc.)
    webhook_payload             JSONB NOT NULL,                        -- Full webhook payload
    webhook_signature           VARCHAR(500),                          -- Webhook signature
    webhook_headers             JSONB,                                  -- HTTP headers received
    
    -- Processing Information
    is_signature_valid          BOOLEAN DEFAULT FALSE,                 -- Signature validation result
    processing_status           VARCHAR(50) DEFAULT 'PENDING',        -- PENDING, PROCESSED, FAILED, IGNORED
    processing_error            TEXT,                                  -- Processing error if any
    processed_at                TIMESTAMP WITH TIME ZONE,              -- When processed
    
    -- Provider Information
    provider                    VARCHAR(50) NOT NULL,                  -- Provider name
    provider_request_id         VARCHAR(255),                          -- Provider's request ID
    provider_timestamp          TIMESTAMP WITH TIME ZONE,              -- Timestamp from provider
    
    -- HTTP Information
    http_method                 VARCHAR(10) DEFAULT 'POST',            -- HTTP method
    http_status_code            INTEGER,                               -- Response status sent back
    user_agent                  TEXT,                                  -- User agent from webhook
    ip_address                  VARCHAR(45),                           -- Source IP address
    
    -- Audit Fields
    received_at                 TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_transaction_webhook (transaction_id),
    INDEX idx_webhook_event (webhook_event),
    INDEX idx_provider_webhook (provider),
    INDEX idx_received_at (received_at),
    INDEX idx_processing_status (processing_status),
    
    -- Foreign Key
    CONSTRAINT fk_transaction_webhook FOREIGN KEY (transaction_id) 
        REFERENCES payment_gateway_transactions(transaction_id)
        ON DELETE CASCADE
);
```

### 3. payment_gateway_api_logs (API Request/Response Log)

```sql  
CREATE TABLE payment_gateway_api_logs (
    -- Primary Information
    id                          BIGSERIAL PRIMARY KEY,
    transaction_id              VARCHAR(255),                          -- Related transaction ID (nullable)
    
    -- API Call Information
    api_endpoint                VARCHAR(255) NOT NULL,                 -- API endpoint called
    http_method                 VARCHAR(10) NOT NULL,                  -- GET, POST, PUT, etc.
    request_payload             JSONB,                                  -- Request body sent
    response_payload            JSONB,                                  -- Response body received
    
    -- Provider Information
    provider                    VARCHAR(50) NOT NULL,                  -- Provider name
    payment_gateway_account_id  BIGINT,                                -- Which config was used
    
    -- Status and Timing
    http_status_code            INTEGER,                               -- HTTP response status
    request_duration_ms         INTEGER,                               -- Request duration in milliseconds
    retry_count                 INTEGER DEFAULT 0,                     -- Number of retries made
    is_successful               BOOLEAN DEFAULT FALSE,                 -- Whether request was successful
    
    -- Error Information
    error_code                  VARCHAR(100),                          -- Provider error code
    error_message               TEXT,                                  -- Provider error message
    error_details               JSONB,                                  -- Additional error details
    
    -- Headers and Security
    request_headers             JSONB,                                  -- HTTP request headers (excluding sensitive)
    response_headers            JSONB,                                  -- HTTP response headers
    request_signature           VARCHAR(500),                          -- Request signature sent
    
    -- Audit Fields
    requested_at                TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    requested_by                VARCHAR(255),                          -- User/system who made request
    
    -- Indexes
    INDEX idx_transaction_api (transaction_id),
    INDEX idx_provider_api (provider),
    INDEX idx_endpoint (api_endpoint),
    INDEX idx_requested_at (requested_at),
    INDEX idx_gateway_api (payment_gateway_account_id),
    INDEX idx_status_success (http_status_code, is_successful)
);
```

### 4. payment_gateway_transaction_status_history (Status Change History)

```sql
CREATE TABLE payment_gateway_transaction_status_history (
    -- Primary Information  
    id                          BIGSERIAL PRIMARY KEY,
    transaction_id              VARCHAR(255) NOT NULL,                 -- FK to transaction_id in main table
    
    -- Status Change Information
    from_status                 VARCHAR(50),                           -- Previous status
    to_status                   VARCHAR(50) NOT NULL,                  -- New status
    status_reason               VARCHAR(255),                          -- Reason for change
    status_details              JSONB,                                  -- Additional details
    
    -- Change Source
    changed_by_type             VARCHAR(20) NOT NULL,                  -- WEBHOOK, API, ADMIN, SYSTEM, AUTO
    changed_by_reference        VARCHAR(255),                          -- Reference to who/what made change
    changed_by_user             VARCHAR(255),                          -- User who made change (if applicable)
    
    -- Provider Information  
    provider                    VARCHAR(50) NOT NULL,                  -- Provider name
    provider_reference          VARCHAR(255),                          -- Provider reference for this change
    
    -- Timing
    changed_at                  TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_transaction_status (transaction_id),
    INDEX idx_status_change (from_status, to_status),
    INDEX idx_changed_at (changed_at),
    INDEX idx_changed_by (changed_by_type, changed_by_user),
    
    -- Foreign Key
    CONSTRAINT fk_transaction_status FOREIGN KEY (transaction_id) 
        REFERENCES payment_gateway_transactions(transaction_id)
        ON DELETE CASCADE
);
```

## Key Design Features

### 🎯 **Generic Design:**
- รองรับหลาย payment gateway (JaiJaiPay, LinePay, PromptPay, bank bot, SMS)
- Flexible metadata storage ด้วย JSONB
- Provider-agnostic field structure

### 📊 **Comprehensive Logging:**
- ครบทุกข้อมูลจาก webhook
- API request/response logging
- Status change history
- Error tracking ละเอียด

### 🔐 **Security & Audit:**
- Signature validation logs
- IP address tracking  
- User audit trail
- Request/response logging (configurable)

### ⚡ **Performance:**
- ดี indexes สำหรับ query patterns ทั่วไป
- Foreign key relationships
- Timestamp-based partitioning ready

### 🔄 **Extensibility:**
- JSONB fields สำหรับข้อมูลเพิ่มเติม
- รองรับ provider ใหม่ๆ
- รองรับ transaction types ใหม่

## Usage Examples

### Query Recent Transactions:
```sql
SELECT t.*, pga.account_name as gateway_name
FROM payment_gateway_transactions t
JOIN payment_gateway_account pga ON t.payment_gateway_account_id = pga.id  
WHERE t.created_at >= NOW() - INTERVAL '24 hours'
ORDER BY t.created_at DESC;
```

### Get Transaction with Webhook History:
```sql
SELECT 
    t.*,
    w.webhook_event,
    w.received_at as webhook_received_at,
    w.processing_status
FROM payment_gateway_transactions t
LEFT JOIN payment_gateway_webhooks w ON t.transaction_id = w.transaction_id
WHERE t.transaction_id = 'a78853e1-2cc5-4e39-8bbd-6898c12cd21a';
```

### Track Status Changes:
```sql
SELECT * FROM payment_gateway_transaction_status_history 
WHERE transaction_id = 'a78853e1-2cc5-4e39-8bbd-6898c12cd21a'
ORDER BY changed_at ASC;
```

ออกแบบนี้ครบถ้วนสำหรับทุกความต้องการไหมครับ?