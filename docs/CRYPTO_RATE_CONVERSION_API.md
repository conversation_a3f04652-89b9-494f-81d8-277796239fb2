# Crypto Rate Conversion & Credit Update API Documentation

## Overview
This document describes the rate conversion system and credit update integration for the Crypto 2-Step Deposit API. The system uses fixed conversion rates stored in the database to convert cryptocurrency amounts to THB and automatically update member credits upon successful deposits.

## Base URL
```
https://your-api-domain.com/api/v1/payment-gateway/crypto
```

## Authentication
All endpoints require member JWT authentication.

```http
Authorization: Bearer <member_jwt_token>
```

---

## Database Schema

### Chain Tokens Table
Stores conversion rates for different tokens across blockchain networks.

```sql
CREATE TABLE chain_tokens (
    id SERIAL PRIMARY KEY,
    blockchain_network_id INTEGER NOT NULL,
    token_contract VARCHAR(42) NOT NULL,
    token_symbol VARCHAR(10) NOT NULL,
    token_decimals INTEGER DEFAULT 18,
    rate_to_thb DECIMAL(10,4) NOT NULL DEFAULT 33.0000,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_chain_tokens_network 
    FOREIGN KEY (blockchain_network_id) REFERENCES blockchain_networks(id),
    
    UNIQUE(blockchain_network_id, token_contract)
);
```

### Default Conversion Rates

| Network | Token | Contract Address | Rate (THB) | Decimals |
|---------|-------|------------------|------------|----------|
| Base Sepolia | USDC | ****************************************** | 33.5000 | 18 |
| Polygon Mainnet | USDC | ****************************************** | 33.5000 | 6 |
| Polygon Mumbai | USDC | ****************************************** | 33.5000 | 18 |
| Ethereum Mainnet | USDC | ****************************************** | 33.5000 | 6 |
| Ethereum Sepolia | USDC | ****************************************** | 33.5000 | 18 |

---

## Rate Conversion API

### 1. Calculate Token to THB Conversion

Calculates THB amount from token amount using stored conversion rates.

#### Endpoint
```http
GET /api/v1/payment-gateway/crypto/convert
```

#### Query Parameters
| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `chainId` | integer | Yes | Blockchain network chain ID | `84532` |
| `tokenContract` | string | Yes | Token contract address | `******************************************` |
| `amount` | number | Yes | Token amount to convert | `10.0` |

#### Example Request
```http
GET /api/v1/payment-gateway/crypto/convert?chainId=84532&tokenContract=******************************************&amount=10.0
Authorization: Bearer <member_jwt_token>
```

#### Response
```json
{
  "tokenAmount": 10.0,
  "rate": 33.5,
  "amountThb": 335.0,
  "tokenSymbol": "USDC",
  "chainId": 84532,
  "tokenContract": "******************************************"
}
```

#### Status Codes
- `200 OK` - Conversion calculated successfully
- `400 Bad Request` - Invalid parameters
- `404 Not Found` - Token not found for specified chain/contract
- `500 Internal Server Error` - Server error

---

### 2. List Available Chain Tokens

Retrieves all available tokens with their current conversion rates.

#### Endpoint
```http
GET /api/v1/payment-gateway/crypto/tokens
```

#### Response
```json
[
  {
    "id": 1,
    "blockchainNetworkId": 1,
    "tokenContract": "******************************************",
    "tokenSymbol": "USDC",
    "tokenDecimals": 18,
    "rateToThb": 33.5000,
    "isActive": true,
    "createdAt": "2025-08-30T10:00:00Z",
    "updatedAt": "2025-08-30T10:00:00Z"
  },
  {
    "id": 2,
    "blockchainNetworkId": 2,
    "tokenContract": "******************************************",
    "tokenSymbol": "USDC",
    "tokenDecimals": 6,
    "rateToThb": 33.5000,
    "isActive": true,
    "createdAt": "2025-08-30T10:00:00Z",
    "updatedAt": "2025-08-30T10:00:00Z"
  }
]
```

---

### 3. Update Token Conversion Rate (Admin Only)

Updates the conversion rate for a specific token.

#### Endpoint
```http
PUT /api/v1/payment-gateway/crypto/tokens/{tokenId}/rate
```

#### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `tokenId` | integer | Yes | Token ID from chain_tokens table |

#### Request Body
```json
{
  "rate": 34.0
}
```

#### Example Request
```http
PUT /api/v1/payment-gateway/crypto/tokens/1/rate
Authorization: Bearer <admin_jwt_token>
Content-Type: application/json

{
  "rate": 34.0
}
```

#### Response
```json
{
  "message": "Token conversion rate updated successfully"
}
```

#### Status Codes
- `200 OK` - Rate updated successfully
- `400 Bad Request` - Invalid request parameters
- `404 Not Found` - Token not found
- `500 Internal Server Error` - Server error

---

## Credit Update Integration

### Enhanced Step 2 Request

The Step 2 update request now supports automatic credit updates upon successful completion.

#### Updated Request Body
```json
{
  "transactionHash": "0xdef456ghi789...",
  "blockNumber": 12346,
  "gasUsed": 75000,
  "gasFeeEth": 0.002,
  "actualAmount": 10.0,
  "status": "confirmed",
  "initiatedAt": "2025-08-29T10:31:00Z",
  "confirmedAt": "2025-08-29T10:31:30Z",
  "creditUpdate": {
    "gameUsername": "player123",
    "amountThb": 335.0
  }
}
```

#### Credit Update Fields
| Field | Type | Required | Description | Example |
|-------|------|----------|-------------|---------|
| `gameUsername` | string | Yes | Member's game username | `"player123"` |
| `amountThb` | number | Yes | THB amount to credit (must be > 0) | `335.0` |

### Credit Update Process Flow

```
Frontend Calculates → Step 2 Completed → Credit Updated
     ↓                      ↓                  ↓
1. GET /convert       2. PUT /step2        3. processCreditUpdate()
   10 USDT × 33.5        with creditUpdate     player123 + 335 THB
   = 335 THB             gameUsername+THB      ✅ Credit Updated
```

---

## Complete Frontend Integration Example

### Complete Deposit Flow with Rate Conversion

```javascript
const useCrypto2StepDepositWithCredit = (memberToken, gameUsername) => {
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState('');
  const [error, setError] = useState(null);
  
  const executeDepositWithCredit = useCallback(async ({
    amount,           // Token amount (e.g., 10.0)
    currency,         // Token symbol (e.g., "USDC") 
    userWalletAddress,
    wallet,
    chainId,
    tokenContract
  }) => {
    setLoading(true);
    setError(null);
    
    try {
      // 1. Calculate conversion rate first
      setProgress('Calculating conversion rate...');
      const conversionResponse = await fetch(
        `/api/v1/payment-gateway/crypto/convert?chainId=${chainId}&tokenContract=${tokenContract}&amount=${amount}`,
        { headers: { 'Authorization': `Bearer ${memberToken}` }}
      );
      
      if (!conversionResponse.ok) throw new Error('Failed to calculate conversion');
      const conversion = await conversionResponse.json();
      
      console.log(`Conversion: ${amount} ${conversion.tokenSymbol} = ${conversion.amountThb} THB (Rate: ${conversion.rate})`);
      
      // 2. Get config and initiate deposit
      setProgress('Initializing deposit...');
      const config = await fetch('/api/v1/payment-gateway/crypto/config', {
        headers: { 'Authorization': `Bearer ${memberToken}` }
      }).then(r => r.json());
      
      const transactionId = crypto.randomUUID();
      const initResponse = await fetch('/api/v1/payment-gateway/crypto/initiate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${memberToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          transactionId,
          amount,
          currency,
          customerUsername: 'current-member',
          userWalletAddress,
          chainId,
          tokenContract
        })
      });
      
      if (!initResponse.ok) throw new Error('Failed to initiate deposit');
      const initData = await initResponse.json();
      
      // 3. Execute Step 1
      setProgress('Step 1: Transferring from your wallet to backend...');
      const step1StartTime = new Date();
      
      const step1Result = await transferERC20Example(
        wallet.smartAccount,
        initData.backendWalletAddress,
        amount
      );
      
      await fetch(`/api/v1/payment-gateway/crypto/${transactionId}/step1`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${memberToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          transactionHash: step1Result.transactionHash,
          blockNumber: step1Result.blockNumber,
          gasUsed: step1Result.gasUsed,
          actualAmount: parseFloat(step1Result.actualAmount),
          status: 'confirmed',
          initiatedAt: step1StartTime.toISOString(),
          confirmedAt: new Date().toISOString()
        })
      });
      
      // 4. Execute Step 2 with credit update
      setProgress('Step 2: Transferring to final recipient & updating credit...');
      const step2StartTime = new Date();
      
      const step2Result = await callThirdwebAPI(
        initData.backendWalletAddress,
        initData.finalRecipientAddress,
        step1Result.actualAmount
      );
      
      // *** ส่วนสำคัญ: ส่ง credit update ไปด้วย ***
      await fetch(`/api/v1/payment-gateway/crypto/${transactionId}/step2`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${memberToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          transactionHash: step2Result.transactionHash,
          blockNumber: step2Result.blockNumber,
          gasUsed: step2Result.gasUsed,
          gasFeeEth: parseFloat(step2Result.gasFeeEth),
          actualAmount: parseFloat(step2Result.actualAmount),
          status: 'confirmed',
          initiatedAt: step2StartTime.toISOString(),
          confirmedAt: new Date().toISOString(),
          thirdwebTransactionId: step2Result.id,
          // 🎯 Credit Update Integration
          creditUpdate: {
            gameUsername: gameUsername,        // จาก context หรือ props
            amountThb: conversion.amountThb    // จาก calculation ขั้นตอนที่ 1
          }
        })
      });
      
      setProgress('Deposit & credit update completed successfully!');
      
      return {
        success: true,
        transactionId,
        conversion,
        finalResult: await fetch(`/api/v1/payment-gateway/crypto/${transactionId}`, {
          headers: { 'Authorization': `Bearer ${memberToken}` }
        }).then(r => r.json())
      };
      
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [memberToken, gameUsername]);
  
  return {
    executeDepositWithCredit,
    loading,
    progress,
    error
  };
};

// Usage in component
const CryptoDepositComponent = ({ gameUsername }) => {
  const { executeDepositWithCredit, loading, progress, error } = useCrypto2StepDepositWithCredit(memberToken, gameUsername);
  
  const [tokenAmount, setTokenAmount] = useState('');
  const [conversion, setConversion] = useState(null);
  
  // Calculate conversion as user types
  const handleAmountChange = async (amount) => {
    setTokenAmount(amount);
    
    if (amount && parseFloat(amount) > 0) {
      try {
        const response = await fetch(
          `/api/v1/payment-gateway/crypto/convert?chainId=84532&tokenContract=******************************************&amount=${amount}`,
          { headers: { 'Authorization': `Bearer ${memberToken}` }}
        );
        const conversionData = await response.json();
        setConversion(conversionData);
      } catch (err) {
        console.error('Failed to calculate conversion:', err);
      }
    } else {
      setConversion(null);
    }
  };
  
  const handleDeposit = async () => {
    if (!tokenAmount || !conversion) return;
    
    try {
      const result = await executeDepositWithCredit({
        amount: parseFloat(tokenAmount),
        currency: 'USDC',
        userWalletAddress: wallet.address,
        wallet: wallet,
        chainId: 84532,
        tokenContract: '******************************************'
      });
      
      console.log('Deposit completed with credit update:', result);
    } catch (err) {
      console.error('Deposit failed:', err);
    }
  };
  
  return (
    <div className="crypto-deposit-form">
      <h3>Crypto Deposit</h3>
      
      <div className="form-group">
        <label>Amount (USDC)</label>
        <input
          type="number"
          value={tokenAmount}
          onChange={(e) => handleAmountChange(e.target.value)}
          placeholder="Enter USDC amount"
          disabled={loading}
        />
      </div>
      
      {conversion && (
        <div className="conversion-preview">
          <p>📊 Conversion Preview:</p>
          <p><strong>{conversion.tokenAmount} {conversion.tokenSymbol}</strong> = <strong>{conversion.amountThb} THB</strong></p>
          <p>Rate: 1 {conversion.tokenSymbol} = {conversion.rate} THB</p>
          <p>Game Username: <strong>{gameUsername}</strong></p>
        </div>
      )}
      
      <button 
        onClick={handleDeposit} 
        disabled={loading || !conversion || !tokenAmount}
        className="deposit-button"
      >
        {loading ? 'Processing...' : `Deposit ${tokenAmount} USDC (${conversion?.amountThb} THB)`}
      </button>
      
      {loading && <div className="progress">📈 {progress}</div>}
      {error && <div className="error">❌ {error}</div>}
    </div>
  );
};
```

---

## Admin Rate Management

### 1. List All Token Rates

#### Endpoint
```http
GET /api/v1/payment-gateway/crypto/tokens
```

#### Response
```json
[
  {
    "id": 1,
    "blockchainNetworkId": 1,
    "tokenContract": "******************************************",
    "tokenSymbol": "USDC",
    "tokenDecimals": 18,
    "rateToThb": 33.5000,
    "isActive": true,
    "createdAt": "2025-08-30T10:00:00Z",
    "updatedAt": "2025-08-30T10:00:00Z"
  }
]
```

### 2. Update Token Rate

#### Endpoint
```http
PUT /api/v1/payment-gateway/crypto/tokens/{tokenId}/rate
```

#### Request Body
```json
{
  "rate": 34.0
}
```

#### Example: Update USDC Rate
```javascript
// Update Base Sepolia USDC rate from 33.5 to 34.0
await fetch('/api/v1/payment-gateway/crypto/tokens/1/rate', {
  method: 'PUT',
  headers: {
    'Authorization': `Bearer ${adminToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    rate: 34.0
  })
});
```

---

## Credit Update Process

### When Credit Update Happens

Credit update is automatically triggered when:
1. **Step 2 status** = `"confirmed"` 
2. **creditUpdate object** is provided in Step 2 request
3. **Both gameUsername and amountThb** are valid

### Credit Update Flow

```
Step 2 Completed → Validate Credit Data → Call processCreditUpdate() → Log Results
       ↓                    ↓                        ↓                    ↓
  status='confirmed'   gameUsername ✓           Update member         Success/Error
  creditUpdate ✓       amountThb > 0 ✓         game credit           logged
```

### Credit Update Request Format

```json
{
  "creditUpdate": {
    "gameUsername": "player123",    // Member's game account username
    "amountThb": 335.0             // Converted THB amount (from calculation)
  }
}
```

### Error Handling for Credit Updates

```javascript
// Credit update failures don't fail the blockchain transaction
// The deposit is still considered successful from blockchain perspective

// Step 2 Response indicates both blockchain and credit status
{
  "transactionId": "crypto_deposit_uuid_123456789",
  "step2Status": "confirmed",
  "overallStatus": "COMPLETED",           // ✅ Blockchain transaction successful
  "step2TransactionHash": "0xdef456...",
  "finalReceivedAmount": 10.0,
  "completionTimeSeconds": 75,
  "completedAt": "2025-08-29T10:31:30Z",
  "creditUpdateStatus": "success",        // ✅ Credit update successful
  // OR
  "creditUpdateStatus": "failed",         // ❌ Credit update failed
  "creditUpdateError": "Game username not found"
}
```

---

## Example Scenarios

### Scenario 1: Successful Deposit with Credit Update

```javascript
// User deposits 10 USDC on Base Sepolia
// Rate: 33.5 THB per USDC
// Expected THB: 10 × 33.5 = 335 THB

const depositFlow = async () => {
  // 1. Calculate conversion
  const conversion = await fetch('/api/v1/payment-gateway/crypto/convert?chainId=84532&tokenContract=******************************************&amount=10.0')
    .then(r => r.json());
  // Result: { tokenAmount: 10.0, rate: 33.5, amountThb: 335.0 }
  
  // 2. Execute Step 1 & 2 as normal...
  
  // 3. Step 2 with credit update
  await fetch(`/api/v1/payment-gateway/crypto/${transactionId}/step2`, {
    method: 'PUT',
    body: JSON.stringify({
      transactionHash: "0x...",
      actualAmount: 10.0,
      status: "confirmed",
      confirmedAt: new Date().toISOString(),
      creditUpdate: {
        gameUsername: "player123",
        amountThb: 335.0  // From conversion calculation
      }
    })
  });
  
  // Result: 
  // ✅ Blockchain: 10 USDC transferred successfully
  // ✅ Credit: player123 receives +335 THB in game account
};
```

### Scenario 2: Rate Update by Admin

```javascript
// Admin updates USDC rate from 33.5 to 34.0
const updateRate = async () => {
  await fetch('/api/v1/payment-gateway/crypto/tokens/1/rate', {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ rate: 34.0 })
  });
  
  // New conversions will use 34.0 THB per USDC
  // 10 USDC = 340 THB (instead of 335 THB)
};
```

---

## Database Queries for Monitoring

### Check Current Rates
```sql
SELECT 
  bn.network_name,
  bn.chain_id,
  ct.token_symbol,
  ct.token_contract,
  ct.rate_to_thb,
  ct.updated_at
FROM chain_tokens ct
INNER JOIN blockchain_networks bn ON ct.blockchain_network_id = bn.id
WHERE ct.is_active = true
ORDER BY bn.chain_id, ct.token_symbol;
```

### Monitor Crypto Deposits with Credit Updates
```sql
SELECT 
  pgt.transaction_id,
  pgt.customer_username,
  pgt.amount as token_amount,
  pgt.currency,
  pgt.status,
  pgt.blockchain_data->>'step1'->>'transaction_hash' as step1_hash,
  pgt.blockchain_data->>'step2'->>'transaction_hash' as step2_hash,
  pgt.blockchain_data->>'step2'->>'credit_update'->>'game_username' as game_username,
  pgt.blockchain_data->>'step2'->>'credit_update'->>'amount_thb' as thb_amount,
  pgt.created_at,
  pgt.completed_at
FROM payment_gateway_transactions pgt
WHERE pgt.provider = 'BLOCKCHAIN'
AND pgt.transaction_type = 'DEPOSIT'
AND pgt.status = 'COMPLETED'
ORDER BY pgt.completed_at DESC
LIMIT 10;
```

### Calculate Total Volume by Token
```sql
SELECT 
  pgt.currency,
  COUNT(*) as total_deposits,
  SUM(pgt.amount) as total_token_amount,
  AVG(CAST(pgt.blockchain_data->>'step2'->>'credit_update'->>'amount_thb' AS FLOAT)) as avg_thb_amount,
  SUM(CAST(pgt.blockchain_data->>'step2'->>'credit_update'->>'amount_thb' AS FLOAT)) as total_thb_amount
FROM payment_gateway_transactions pgt
WHERE pgt.provider = 'BLOCKCHAIN'
AND pgt.transaction_type = 'DEPOSIT'
AND pgt.status = 'COMPLETED'
AND pgt.created_at >= NOW() - INTERVAL '7 days'
GROUP BY pgt.currency
ORDER BY total_thb_amount DESC;
```

---

## Best Practices

### Rate Management
1. **Update rates regularly** - Monitor market prices and update accordingly
2. **Set reasonable spreads** - Consider market volatility when setting rates
3. **Log rate changes** - All rate updates are automatically logged with timestamps
4. **Test conversions** - Always test conversion calculations before rate updates

### Credit Integration
1. **Validate game usernames** - Ensure game username exists before processing
2. **Handle partial failures** - Blockchain success + credit failure is still a valid deposit
3. **Monitor credit updates** - Set up alerts for failed credit updates
4. **Audit trail** - All credit updates are logged with transaction references

### Performance Optimization
1. **Cache conversion rates** - Consider caching rates for frequently used tokens
2. **Batch operations** - Process multiple credit updates in batches if needed
3. **Index optimization** - Database indexes are created for common queries
4. **Monitor query performance** - Track slow queries and optimize as needed

---

## Error Scenarios

### Rate Conversion Errors
```json
// Token not found for chain/contract
{
  "error": "Not found",
  "message": "chain token not found for chain and contract"
}

// Invalid parameters
{
  "error": "Validation failed", 
  "message": "token amount must be greater than 0"
}
```

### Credit Update Errors
```json
// Game username validation failed
{
  "error": "Validation failed",
  "message": "game username is required for credit update"
}

// THB amount invalid
{
  "error": "Validation failed",
  "message": "THB amount must be greater than 0"
}
```

---

## Migration Instructions

### Running the Migration
```bash
# Run the new migration to create chain_tokens table
make migrate-up ENV=local

# Check migration status
make migrate-status ENV=local

# Verify tables were created
psql -d blacking -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'irichdev' AND table_name IN ('chain_tokens', 'blockchain_networks', 'backend_wallets', 'crypto_deposit_logs');"
```

### Verify Default Data
```sql
-- Check default conversion rates
SELECT 
  bn.network_name,
  bn.chain_id,
  ct.token_symbol,
  ct.rate_to_thb
FROM chain_tokens ct
INNER JOIN blockchain_networks bn ON ct.blockchain_network_id = bn.id
WHERE ct.is_active = true;

-- Expected results:
-- Base Sepolia (84532) | USDC | 33.5000
-- Polygon Mainnet (137) | USDC | 33.5000
-- Polygon Mumbai (80001) | USDC | 33.5000
-- Ethereum Mainnet (1) | USDC | 33.5000
-- Ethereum Sepolia (11155111) | USDC | 33.5000
```

---

## Summary

### Integration Points
1. **Rate Calculation**: `GET /convert` - คำนวณ THB จาก token amount
2. **Token Management**: `GET /tokens`, `PUT /tokens/{id}/rate` - จัดการ rates
3. **Credit Update**: ใน `PUT /step2` with `creditUpdate` object
4. **Audit Trail**: ทุก transaction และ rate change มี logs ครบถ้วน

### Process Summary
```
User Input: 10 USDT
     ↓
Rate Lookup: 33.5 THB/USDT (from chain_tokens table)
     ↓  
Calculation: 10 × 33.5 = 335 THB
     ↓
Step 1: User → Backend (10 USDT)
     ↓
Step 2: Backend → Final + Credit Update (player123 +335 THB)
     ↓
Complete: ✅ Blockchain + ✅ Credit Updated
```

**Ready for Production**: Frontend integration และ testing สามารถเริ่มได้เลย!

---

*Last updated: August 30, 2025*