# Crypto 2-Step Deposit API Documentation

## Overview
Crypto 2-Step Deposit API allows frontend applications to process cryptocurrency deposits through a secure 2-step blockchain transaction flow. The frontend initiates and executes both steps while the backend tracks and stores transaction details.

## Base URL
```
https://your-api-domain.com/api/v1/payment-gateway/crypto
```

## Authentication
All crypto deposit endpoints require member JWT authentication.

```http
Authorization: Bearer <member_jwt_token>
```

---

## Transaction Flow

The 2-step deposit process works as follows:

1. **Frontend initiates** deposit transaction via API
2. **Step 1**: User wallet → Backend wallet (gasless transfer)
3. **Frontend reports** Step 1 results to API
4. **Step 2**: Backend wallet → Final recipient (via thirdweb API)
5. **Frontend reports** Step 2 results to API
6. **Backend tracks** complete transaction lifecycle

```
Frontend → API → Database
   ↓        ↓        ↓
User    Record   Store
Wallet → Step 1 → Status
   ↓        ↓        ↓
Backend  Update  Update
Wallet → Step 2 → Status
   ↓        ↓        ↓
Final   Complete  Final
Recipient → → → → Status
```

---

## 1. Get Deposit Configuration

Retrieves configuration needed for crypto deposits including wallet addresses and supported networks.

### Endpoint
```http
GET /api/v1/payment-gateway/crypto/config
```

### Response
```json
{
  "backendWalletAddress": "******************************************",
  "finalRecipientAddress": "******************************************",
  "supportedNetworks": [
    {
      "chainId": 84532,
      "networkName": "Base Sepolia",
      "networkType": "testnet",
      "rpcUrl": "https://sepolia.base.org",
      "explorerUrl": "https://sepolia-explorer.base.org",
      "nativeCurrencySymbol": "ETH",
      "isActive": true,
      "tokens": [
        {
          "id": 1,
          "contractAddress": "******************************************",
          "symbol": "USDC",
          "decimals": 18,
          "rateToThb": 33.5,
          "isActive": true
        }
      ]
    },
    {
      "chainId": 137,
      "networkName": "Polygon Mainnet", 
      "networkType": "mainnet",
      "rpcUrl": "https://polygon-rpc.com",
      "explorerUrl": "https://polygonscan.com",
      "nativeCurrencySymbol": "MATIC",
      "isActive": true,
      "tokens": [
        {
          "id": 2,
          "contractAddress": "******************************************",
          "symbol": "USDC",
          "decimals": 6,
          "rateToThb": 33.5,
          "isActive": true
        }
      ]
    }
  ]
}
```

**Important Notes:**
- Each network includes its supported tokens with current conversion rates
- Use `tokens` array within each network for available deposit options
- `tokenContracts` array provides a flat list for backward compatibility
- Conversion rates are stored in database and can be updated by admins

---

## 2. Initiate Crypto Deposit

Creates a new crypto deposit transaction record and returns transaction ID for tracking.

### Endpoint
```http
POST /api/v1/payment-gateway/crypto/initiate
```

### Request Body
```json
{
  "transactionId": "crypto_deposit_uuid_123456789",
  "internalReference": "member_deposit_001",
  "amount": 100.50,
  "currency": "USDC",
  "customerUsername": "member123",
  "userWalletAddress": "******************************************",
  "chainId": 84532,
  "tokenContract": "******************************************",
  "frontendSessionId": "session_uuid_987654321"
}
```

### Request Parameters

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `transactionId` | string | Yes | Frontend-generated UUID for transaction tracking |
| `internalReference` | string | No | Internal reference for additional tracking |
| `amount` | number | Yes | Deposit amount in token units |
| `currency` | string | Yes | Token symbol (e.g., "USDC", "USDT") |
| `customerUsername` | string | Yes | Member username |
| `userWalletAddress` | string | Yes | User's wallet address (42 characters, starts with 0x) |
| `chainId` | integer | Yes | Blockchain network chain ID |
| `tokenContract` | string | Yes | Token contract address (42 characters, starts with 0x) |
| `frontendSessionId` | string | No | Frontend session ID for debugging |

### Response
```json
{
  "transactionId": "crypto_deposit_uuid_123456789",
  "status": "INITIATED",
  "backendWalletAddress": "******************************************",
  "finalRecipientAddress": "******************************************",
  "chainId": 84532,
  "tokenContract": "******************************************",
  "amount": "100.50",
  "currency": "USDC",
  "createdAt": "2025-08-29T10:30:00Z",
  "expiresAt": "2025-08-29T11:00:00Z"
}
```

### Status Codes
- `201 Created` - Deposit initiated successfully
- `400 Bad Request` - Invalid request parameters
- `401 Unauthorized` - Invalid or missing authentication
- `409 Conflict` - Transaction ID already exists
- `500 Internal Server Error` - Server error

---

## 3. Update Step 1 Status

Reports Step 1 transaction results (User wallet → Backend wallet).

### Endpoint
```http
PUT /api/v1/payment-gateway/crypto/{transactionId}/step1
```

### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `transactionId` | string | Yes | Transaction ID from initiation |

### Request Body
```json
{
  "transactionHash": "0xabc123def456...",
  "blockNumber": 12345,
  "gasUsed": 65000,
  "actualAmount": 100.50,
  "status": "confirmed",
  "initiatedAt": "2025-08-29T10:30:15Z",
  "confirmedAt": "2025-08-29T10:30:45Z",
  "errorMessage": null
}
```

### Request Parameters

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `transactionHash` | string | Yes | Blockchain transaction hash (66 characters, starts with 0x) |
| `blockNumber` | integer | No | Block number where transaction was mined |
| `gasUsed` | integer | No | Amount of gas used for transaction |
| `actualAmount` | number | Yes | Actual amount transferred (may differ from requested due to slippage) |
| `status` | string | Yes | Transaction status: "pending", "confirmed", "failed", "reverted" |
| `initiatedAt` | string (ISO 8601) | Yes | When transaction was initiated |
| `confirmedAt` | string (ISO 8601) | No | When transaction was confirmed (required if status is "confirmed") |
| `errorMessage` | string | No | Error message if transaction failed |

### Response
```json
{
  "transactionId": "crypto_deposit_uuid_123456789",
  "step1Status": "confirmed",
  "overallStatus": "STEP1_COMPLETED",
  "step1TransactionHash": "0xabc123def456...",
  "step1CompletedAt": "2025-08-29T10:30:45Z",
  "updatedAt": "2025-08-29T10:30:50Z"
}
```

### Status Codes
- `200 OK` - Step 1 updated successfully
- `400 Bad Request` - Invalid request parameters
- `404 Not Found` - Transaction not found
- `409 Conflict` - Step 1 already completed
- `500 Internal Server Error` - Server error

---

## 4. Update Step 2 Status

Reports Step 2 transaction results (Backend wallet → Final recipient).

### Endpoint
```http
PUT /api/v1/payment-gateway/crypto/{transactionId}/step2
```

### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `transactionId` | string | Yes | Transaction ID from initiation |

### Request Body
```json
{
  "transactionHash": "0xdef456ghi789...",
  "blockNumber": 12346,
  "gasUsed": 75000,
  "gasFeeEth": 0.002,
  "actualAmount": 100.50,
  "status": "confirmed",
  "initiatedAt": "2025-08-29T10:31:00Z",
  "confirmedAt": "2025-08-29T10:31:30Z",
  "errorMessage": null,
  "thirdwebTransactionId": "8fc4420d-b3d4-411f-968c-9466fa3f3491"
}
```

### Request Parameters

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `transactionHash` | string | Yes | Blockchain transaction hash |
| `blockNumber` | integer | No | Block number where transaction was mined |
| `gasUsed` | integer | No | Amount of gas used |
| `gasFeeEth` | number | No | Gas fee paid in ETH |
| `actualAmount` | number | Yes | Actual amount received by final recipient |
| `status` | string | Yes | Transaction status: "pending", "confirmed", "failed", "reverted" |
| `initiatedAt` | string (ISO 8601) | Yes | When Step 2 was initiated |
| `confirmedAt` | string (ISO 8601) | No | When Step 2 was confirmed |
| `errorMessage` | string | No | Error message if transaction failed |
| `thirdwebTransactionId` | string | No | ThirdWeb API transaction ID (if using ThirdWeb) |

### Response
```json
{
  "transactionId": "crypto_deposit_uuid_123456789",
  "step2Status": "confirmed",
  "overallStatus": "COMPLETED",
  "step2TransactionHash": "0xdef456ghi789...",
  "finalReceivedAmount": 100.50,
  "completionTimeSeconds": 75,
  "completedAt": "2025-08-29T10:31:30Z",
  "updatedAt": "2025-08-29T10:31:35Z"
}
```

### Status Codes
- `200 OK` - Step 2 updated successfully
- `400 Bad Request` - Invalid request parameters  
- `404 Not Found` - Transaction not found
- `409 Conflict` - Step 2 already completed or Step 1 not completed
- `500 Internal Server Error` - Server error

---

## 5. Get Deposit Status

Retrieves current status and details of a crypto deposit transaction.

### Endpoint
```http
GET /api/v1/payment-gateway/crypto/{transactionId}
```

### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `transactionId` | string | Yes | Transaction ID from initiation |

### Example Request
```http
GET /api/v1/payment-gateway/crypto/crypto_deposit_uuid_123456789
```

### Response
```json
{
  "id": 1001,
  "transactionId": "crypto_deposit_uuid_123456789",
  "internalReference": "member_deposit_001",
  "provider": "BLOCKCHAIN",
  "transactionType": "DEPOSIT",
  "amount": 100.50,
  "currency": "USDC",
  "overallStatus": "COMPLETED",
  "customerUsername": "member123",
  
  "wallets": {
    "userWallet": "******************************************",
    "backendWallet": "******************************************",
    "finalRecipient": "******************************************"
  },
  
  "network": {
    "chainId": 84532,
    "networkName": "Base Sepolia",
    "tokenContract": "******************************************",
    "tokenSymbol": "USDC"
  },
  
  "step1": {
    "transactionHash": "0xabc123def456...",
    "blockNumber": 12345,
    "gasUsed": 65000,
    "actualAmount": 100.50,
    "status": "confirmed",
    "initiatedAt": "2025-08-29T10:30:15Z",
    "confirmedAt": "2025-08-29T10:30:45Z"
  },
  
  "step2": {
    "transactionHash": "0xdef456ghi789...",
    "blockNumber": 12346,
    "gasUsed": 75000,
    "gasFeeEth": 0.002,
    "actualAmount": 100.50,
    "status": "confirmed",
    "initiatedAt": "2025-08-29T10:31:00Z",
    "confirmedAt": "2025-08-29T10:31:30Z",
    "thirdwebTransactionId": "8fc4420d-b3d4-411f-968c-9466fa3f3491"
  },
  
  "summary": {
    "finalReceivedAmount": 100.50,
    "completionTimeSeconds": 75,
    "totalGasCostEth": 0.002,
    "efficiencyPercentage": 100.00
  },
  
  "timestamps": {
    "createdAt": "2025-08-29T10:30:00Z",
    "updatedAt": "2025-08-29T10:31:35Z",
    "completedAt": "2025-08-29T10:31:30Z"
  }
}
```

---

## 6. List Crypto Deposits

Retrieves a paginated list of crypto deposit transactions.

### Endpoint
```http
GET /api/v1/payment-gateway/crypto/deposits
```

### Query Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `page` | integer | Page number (default: 1) |
| `limit` | integer | Items per page (default: 20, max: 100) |
| `status` | string | Filter by overall status |
| `step1Status` | string | Filter by Step 1 status |
| `step2Status` | string | Filter by Step 2 status |
| `startDate` | string | Start date filter (YYYY-MM-DD) |
| `endDate` | string | End date filter (YYYY-MM-DD) |
| `chainId` | integer | Filter by blockchain network |
| `currency` | string | Filter by token currency |
| `customerUsername` | string | Filter by customer username |

### Example Request
```http
GET /api/v1/payment-gateway/crypto/deposits?page=1&limit=10&status=COMPLETED&chainId=84532
```

### Response
```json
{
  "data": [
    {
      "id": 1001,
      "transactionId": "crypto_deposit_uuid_123456789",
      "amount": 100.50,
      "currency": "USDC",
      "overallStatus": "COMPLETED",
      "customerUsername": "member123",
      "chainId": 84532,
      "networkName": "Base Sepolia",
      "step1TransactionHash": "0xabc123def456...",
      "step2TransactionHash": "0xdef456ghi789...",
      "finalReceivedAmount": 100.50,
      "completionTimeSeconds": 75,
      "createdAt": "2025-08-29T10:30:00Z",
      "completedAt": "2025-08-29T10:31:30Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3
  }
}
```

---

## Transaction Statuses

### Overall Status
| Status | Description |
|--------|-------------|
| `INITIATED` | Deposit request created, ready for Step 1 |
| `STEP1_PENDING` | Step 1 transaction submitted to blockchain |
| `STEP1_COMPLETED` | Step 1 confirmed, ready for Step 2 |
| `STEP2_PENDING` | Step 2 transaction submitted to blockchain |
| `COMPLETED` | Both steps completed successfully |
| `FAILED` | Transaction failed at any step |
| `CANCELLED` | Transaction cancelled by user or system |

### Step Status
| Status | Description |
|--------|-------------|
| `pending` | Transaction submitted but not yet confirmed |
| `confirmed` | Transaction confirmed on blockchain |
| `failed` | Transaction failed due to network/contract error |
| `reverted` | Transaction reverted due to contract logic |

---

## Frontend Integration Guide

### Complete Integration Flow

```javascript
// 1. Get configuration with networks and tokens
const config = await fetch('/api/v1/payment-gateway/crypto/config', {
  headers: { 'Authorization': `Bearer ${memberToken}` }
}).then(r => r.json());

// Example: Find USDC token on Base Sepolia
const baseNetwork = config.supportedNetworks.find(n => n.chainId === 84532);
const usdcToken = baseNetwork?.tokens.find(t => t.symbol === 'USDC');
console.log(`USDC Rate: ${usdcToken.rateToThb} THB`); // 33.5 THB

// 2. Generate transaction ID and initiate
const transactionId = generateUUID();
const initResponse = await fetch('/api/v1/payment-gateway/crypto/initiate', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${memberToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    transactionId,
    amount: 100.50,
    currency: 'USDC',
    customerUsername: 'member123',
    userWalletAddress: wallet.address,
    chainId: 84532,
    tokenContract: '******************************************',
    frontendSessionId: sessionStorage.getItem('sessionId')
  })
});

const initData = await initResponse.json();
console.log('Deposit initiated:', initData.transactionId);

// 3. Execute Step 1: User → Backend
try {
  showProgress('Executing Step 1: User → Backend wallet...');
  const step1StartTime = new Date();
  
  const step1Result = await transferERC20Example(
    wallet.smartAccount,
    initData.backendWalletAddress,
    initData.amount
  );
  
  // Report Step 1 success
  await fetch(`/api/v1/payment-gateway/crypto/${transactionId}/step1`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${memberToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      transactionHash: step1Result.transactionHash,
      blockNumber: step1Result.blockNumber,
      gasUsed: step1Result.gasUsed,
      actualAmount: parseFloat(step1Result.actualAmount),
      status: 'confirmed',
      initiatedAt: step1StartTime.toISOString(),
      confirmedAt: new Date().toISOString()
    })
  });
  
  showProgress('Step 1 completed successfully!');
  
} catch (step1Error) {
  console.error('Step 1 failed:', step1Error);
  
  // Report Step 1 failure
  await fetch(`/api/v1/payment-gateway/crypto/${transactionId}/step1`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${memberToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      transactionHash: step1Error.transactionHash || null,
      actualAmount: 0,
      status: 'failed',
      initiatedAt: step1StartTime.toISOString(),
      errorMessage: step1Error.message
    })
  });
  
  throw new Error(`Step 1 failed: ${step1Error.message}`);
}

// 4. Execute Step 2: Backend → Final Recipient
try {
  showProgress('Executing Step 2: Backend → Final recipient...');
  const step2StartTime = new Date();
  
  const step2Result = await callThirdwebAPI(
    initData.backendWalletAddress,
    initData.finalRecipientAddress,
    step1Result.actualAmount
  );
  
  // Report Step 2 success
  await fetch(`/api/v1/payment-gateway/crypto/${transactionId}/step2`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${memberToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      transactionHash: step2Result.transactionHash,
      blockNumber: step2Result.blockNumber,
      gasUsed: step2Result.gasUsed,
      gasFeeEth: parseFloat(step2Result.gasFeeEth),
      actualAmount: parseFloat(step2Result.actualAmount),
      status: 'confirmed',
      initiatedAt: step2StartTime.toISOString(),
      confirmedAt: new Date().toISOString(),
      thirdwebTransactionId: step2Result.id
    })
  });
  
  showProgress('Step 2 completed successfully!');
  
} catch (step2Error) {
  console.error('Step 2 failed:', step2Error);
  
  // Report Step 2 failure
  await fetch(`/api/v1/payment-gateway/crypto/${transactionId}/step2`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${memberToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      transactionHash: step2Error.transactionHash || null,
      actualAmount: 0,
      status: 'failed',
      initiatedAt: step2StartTime.toISOString(),
      errorMessage: step2Error.message,
      thirdwebTransactionId: step2Error.transactionId
    })
  });
  
  throw new Error(`Step 2 failed: ${step2Error.message}`);
}

// 5. Get final status
const finalStatus = await fetch(`/api/v1/payment-gateway/crypto/${transactionId}`, {
  headers: { 'Authorization': `Bearer ${memberToken}` }
}).then(r => r.json());

if (finalStatus.overallStatus === 'COMPLETED') {
  showSuccess(`Deposit completed! 
    Amount: ${finalStatus.summary.finalReceivedAmount} ${finalStatus.currency}
    Total time: ${finalStatus.summary.completionTimeSeconds}s
    Efficiency: ${finalStatus.summary.efficiencyPercentage}%
  `);
} else {
  showError('Deposit failed. Please try again.');
}
```

### React Hook Example

```javascript
import { useState, useCallback } from 'react';

const useCrypto2StepDeposit = (memberToken) => {
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState('');
  const [error, setError] = useState(null);
  
  const executeDeposit = useCallback(async ({
    amount,
    currency,
    userWalletAddress,
    wallet,
    chainId,
    tokenContract
  }) => {
    setLoading(true);
    setError(null);
    setProgress('Initializing deposit...');
    
    try {
      // Get config and initiate
      const config = await fetch('/api/v1/payment-gateway/crypto/config', {
        headers: { 'Authorization': `Bearer ${memberToken}` }
      }).then(r => r.json());
      
      const transactionId = crypto.randomUUID();
      const initResponse = await fetch('/api/v1/payment-gateway/crypto/initiate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${memberToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          transactionId,
          amount,
          currency,
          customerUsername: 'current-member', // Get from context
          userWalletAddress,
          chainId,
          tokenContract
        })
      });
      
      if (!initResponse.ok) throw new Error('Failed to initiate deposit');
      const initData = await initResponse.json();
      
      // Execute Step 1
      setProgress('Step 1: Transferring from your wallet to backend...');
      const step1StartTime = new Date();
      
      const step1Result = await transferERC20Example(
        wallet.smartAccount,
        initData.backendWalletAddress,
        amount
      );
      
      await fetch(`/api/v1/payment-gateway/crypto/${transactionId}/step1`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${memberToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          transactionHash: step1Result.transactionHash,
          blockNumber: step1Result.blockNumber,
          gasUsed: step1Result.gasUsed,
          actualAmount: parseFloat(step1Result.actualAmount),
          status: 'confirmed',
          initiatedAt: step1StartTime.toISOString(),
          confirmedAt: new Date().toISOString()
        })
      });
      
      // Execute Step 2
      setProgress('Step 2: Transferring from backend to final recipient...');
      const step2StartTime = new Date();
      
      const step2Result = await callThirdwebAPI(
        initData.backendWalletAddress,
        initData.finalRecipientAddress,
        step1Result.actualAmount
      );
      
      await fetch(`/api/v1/payment-gateway/crypto/${transactionId}/step2`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${memberToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          transactionHash: step2Result.transactionHash,
          blockNumber: step2Result.blockNumber,
          gasUsed: step2Result.gasUsed,
          gasFeeEth: parseFloat(step2Result.gasFeeEth),
          actualAmount: parseFloat(step2Result.actualAmount),
          status: 'confirmed',
          initiatedAt: step2StartTime.toISOString(),
          confirmedAt: new Date().toISOString(),
          thirdwebTransactionId: step2Result.id
        })
      });
      
      // Get final result
      const finalResult = await fetch(`/api/v1/payment-gateway/crypto/${transactionId}`, {
        headers: { 'Authorization': `Bearer ${memberToken}` }
      }).then(r => r.json());
      
      setProgress('Deposit completed successfully!');
      return finalResult;
      
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [memberToken]);
  
  return {
    executeDeposit,
    loading,
    progress,
    error
  };
};

// Usage in component
const DepositComponent = () => {
  const { executeDeposit, loading, progress, error } = useCrypto2StepDeposit(memberToken);
  
  const handleDeposit = async () => {
    try {
      const result = await executeDeposit({
        amount: 100.50,
        currency: 'USDC',
        userWalletAddress: wallet.address,
        wallet: wallet,
        chainId: 84532,
        tokenContract: '******************************************'
      });
      
      console.log('Deposit completed:', result);
    } catch (err) {
      console.error('Deposit failed:', err);
    }
  };
  
  return (
    <div>
      <button onClick={handleDeposit} disabled={loading}>
        {loading ? 'Processing...' : 'Deposit USDC'}
      </button>
      {loading && <div>Progress: {progress}</div>}
      {error && <div>Error: {error}</div>}
    </div>
  );
};
```

---

## WebSocket Real-time Notifications

The API supports real-time WebSocket notifications for deposit status updates.

### WebSocket Connection
```
ws://your-domain.com/ws
```

### Deposit Events

```javascript
// Connect to WebSocket
const socket = io('ws://your-domain.com');

// Join member's channel
socket.emit('join', { channel_id: memberId });

// Listen for crypto deposit events
socket.on('crypto_deposit', (data) => {
  const { 
    channel_id, 
    transaction_id, 
    status, 
    step1_status, 
    step2_status,
    amount,
    currency 
  } = data;
  
  if (status === 'COMPLETED') {
    showNotification(`Crypto deposit completed! ${amount} ${currency}`);
  } else if (status === 'FAILED') {
    showNotification('Crypto deposit failed. Please try again.');
  }
});
```

---

## Error Responses

### Error Format
```json
{
  "error": "Step 1 update failed",
  "message": "Transaction hash validation failed",
  "errorCode": 4001,
  "details": {
    "transactionHash": "0xinvalid",
    "expectedChainId": 84532,
    "actualChainId": null
  },
  "requestId": "req_12345",
  "timestamp": "2025-08-29T10:30:00Z"
}
```

### Common Error Codes
| Code | Error | Description |
|------|-------|-------------|
| 4001 | Invalid Transaction Hash | Transaction hash format or validation failed |
| 4002 | Invalid Wallet Address | Wallet address format validation failed |
| 4003 | Transaction Not Found | Transaction ID not found in system |
| 4004 | Step Already Completed | Attempting to update completed step |
| 4005 | Step Prerequisite | Step 2 attempted before Step 1 completion |
| 4006 | Invalid Amount | Amount validation failed |
| 4007 | Unsupported Network | Chain ID not supported |
| 4008 | Transaction Expired | Transaction exceeded timeout limit |
| 5001 | ThirdWeb API Error | ThirdWeb service unavailable |
| 5002 | Blockchain Network Error | Network connectivity issues |

---

## Rate Limits

- 50 requests per minute per member
- 200 requests per hour per member
- Burst limit: 5 requests per second

---

## Testing

### Test Configuration
```json
{
  "chainId": 84532,
  "networkName": "Base Sepolia",
  "rpcUrl": "https://sepolia.base.org",
  "tokenContract": "******************************************",
  "backendWallet": "******************************************",
  "finalRecipient": "******************************************"
}
```

### Test Amounts
- `1.00` - Always succeeds (both steps)
- `999.00` - Fails at Step 1
- `888.00` - Succeeds Step 1, fails Step 2
- `50.00` - Takes 10 seconds per step (slow network simulation)

### Test Transaction Hashes
- `0x1111111111111111111111111111111111111111111111111111111111111111` - Always valid
- `0x2222222222222222222222222222222222222222222222222222222222222222` - Always fails
- `0x3333333333333333333333333333333333333333333333333333333333333333` - Pending for 30 seconds

---

## Best Practices

1. **Generate unique transaction IDs** - Use UUID4 or similar for each deposit
2. **Validate addresses** - Ensure all wallet addresses are valid 42-character hex strings
3. **Handle network delays** - Implement proper timeout and retry logic
4. **Report accurate status** - Always report actual blockchain transaction status
5. **Store session data** - Keep frontend session ID for debugging
6. **Monitor gas fees** - Alert users about high gas costs
7. **Implement proper error handling** - Handle all possible error scenarios
8. **Use progress indicators** - Show real-time progress to users
9. **Validate amounts** - Check token balance before initiating transfers
10. **Security considerations** - Never expose private keys in frontend code

---

## Support & Troubleshooting

### Debug Information
When reporting issues, include:
- Transaction ID
- Frontend session ID  
- User wallet address
- Chain ID and network
- Step 1 and Step 2 transaction hashes
- Error messages and timestamps

### Transaction Monitoring
Use blockchain explorers to verify transactions:
- Base Sepolia: https://sepolia-explorer.base.org
- Polygon: https://polygonscan.com
- Ethereum: https://etherscan.io

### Common Issues
1. **Step 1 pending forever** - Check user wallet ETH balance for gas
2. **Step 2 fails** - Check backend wallet ETH balance
3. **Invalid transaction hash** - Verify hash format and network
4. **Amount mismatch** - Check for slippage in DEX/AMM operations
5. **Network congestion** - Implement retry logic with exponential backoff

---

*Last updated: August 29, 2025*