# JaiJaiPay Withdrawal API Documentation

## Overview
JaiJaiPay Withdrawal API allows you to process withdrawal transactions from customer accounts to bank accounts. The API handles withdrawal requests, status tracking, and webhook notifications.

## Base URL
```
https://your-api-domain.com/api/v1/jaijai
```

## Authentication
All withdrawal endpoints require member JWT authentication.

```http
Authorization: Bearer <member_jwt_token>
```

---

## 1. <PERSON><PERSON> Withdrawal

Creates a new withdrawal transaction.

### Endpoint
```http
POST /api/v1/jaijai/withdrawals
```

### Request Body
```json
{
  "customerReference": "member123",
  "amount": 1000.00,
  "currency": "THB",
  "assetType": "FIAT",
  "bankCode": "SCB",
  "bankAccountNumber": "**********",
  "accountHolderName": "John Doe",
  "description": "Game winnings withdrawal",
  "webhookUrl": "https://your-domain.com/webhooks/jaijaipay",
  "metadata": {
    "memberID": "123",
    "gameSession": "abc456"
  }
}
```

### Request Parameters

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `customerReference` | string | Yes | Member ID or unique customer identifier |
| `amount` | number | Yes | Withdrawal amount (minimum based on gateway settings) |
| `currency` | string | Yes | Currency code (e.g., "THB") |
| `assetType` | string | Yes | Asset type ("FIAT" for regular bank transfers) |
| `bankCode` | string | Yes | Bank code (e.g., "SCB", "BBL", "KBANK") |
| `bankAccountNumber` | string | Yes | Destination bank account number |
| `accountHolderName` | string | Yes | Account holder name (must match bank records) |
| `description` | string | No | Transaction description |
| `webhookUrl` | string | No | URL to receive webhook notifications |
| `metadata` | object | No | Additional metadata for tracking |

### Response
```json
{
  "transactionId": "jjp_withdraw_abc123def456",
  "transactionReference": "WD20231215001",
  "amount": "1000.00",
  "status": "PENDING",
  "bankCode": "SCB",
  "bankAccountNumber": "**********",
  "accountHolderName": "John Doe",
  "createdAt": "2023-12-15T10:30:00Z",
  "orderId": "BLK_WD_20231215_103000_001",
  "customerReference": "member123",
  "currency": "THB",
  "assetType": "FIAT",
  "description": "Game winnings withdrawal",
  "webhookUrl": "https://your-domain.com/webhooks/jaijaipay",
  "merchantId": "merchant_123",
  "companyId": "company_456",
  "percentageCommissionRate": "2.5",
  "fixedCommissionAmount": "10.00",
  "totalCommissionAmount": "35.00",
  "metadata": {
    "memberID": "123",
    "gameSession": "abc456"
  }
}
```

### Status Codes
- `201 Created` - Withdrawal created successfully
- `400 Bad Request` - Invalid request parameters
- `401 Unauthorized` - Invalid or missing authentication
- `500 Internal Server Error` - Server error

---

## 2. List Withdrawals

Retrieves a paginated list of withdrawal transactions.

### Endpoint
```http
GET /api/v1/jaijai/withdrawals
```

### Query Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `page` | integer | Page number (default: 1) |
| `limit` | integer | Items per page (default: 20, max: 100) |
| `status` | string | Filter by status (PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED) |
| `startDate` | string | Start date filter (YYYY-MM-DD) |
| `endDate` | string | End date filter (YYYY-MM-DD) |
| `currency` | string | Filter by currency |
| `customerReferenceId` | string | Filter by customer reference |

### Example Request
```http
GET /api/v1/jaijai/withdrawals?page=1&limit=10&status=COMPLETED&startDate=2023-12-01&endDate=2023-12-15
```

### Response
```json
{
  "data": [
    {
      "transactionId": "jjp_withdraw_abc123def456",
      "transactionReference": "WD20231215001",
      "amount": "1000.00",
      "status": "COMPLETED",
      "bankCode": "SCB",
      "bankAccountNumber": "**********",
      "accountHolderName": "John Doe",
      "createdAt": "2023-12-15T10:30:00Z",
      "completedAt": "2023-12-15T10:45:00Z",
      "orderId": "BLK_WD_20231215_103000_001",
      "customerReference": "member123",
      "currency": "THB",
      "assetType": "FIAT",
      "description": "Game winnings withdrawal",
      "merchantId": "merchant_123",
      "companyId": "company_456",
      "totalCommissionAmount": "35.00"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3
  }
}
```

---

## 3. Get Withdrawal by ID

Retrieves detailed information about a specific withdrawal transaction.

### Endpoint
```http
GET /api/v1/jaijai/withdrawals/:transactionId
```

### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `transactionId` | string | Yes | JaiJaiPay transaction ID |

### Example Request
```http
GET /api/v1/jaijai/withdrawals/jjp_withdraw_abc123def456
```

### Response
```json
{
  "transactionId": "jjp_withdraw_abc123def456",
  "transactionReference": "WD20231215001",
  "amount": "1000.00",
  "status": "COMPLETED",
  "bankCode": "SCB",
  "bankAccountNumber": "**********",
  "accountHolderName": "John Doe",
  "createdAt": "2023-12-15T10:30:00Z",
  "completedAt": "2023-12-15T10:45:00Z",
  "orderId": "BLK_WD_20231215_103000_001",
  "customerReference": "member123",
  "currency": "THB",
  "assetType": "FIAT",
  "description": "Game winnings withdrawal",
  "webhookUrl": "https://your-domain.com/webhooks/jaijaipay",
  "merchantId": "merchant_123",
  "companyId": "company_456",
  "percentageCommissionRate": "2.5",
  "fixedCommissionAmount": "10.00",
  "totalCommissionAmount": "35.00",
  "metadata": {
    "memberID": "123",
    "gameSession": "abc456",
    "processedBy": "system",
    "verificationCode": "V123456"
  }
}
```

---

## Transaction Statuses

| Status | Description |
|--------|-------------|
| `PENDING` | Withdrawal request submitted, awaiting processing |
| `PROCESSING` | Withdrawal is being processed by the payment provider |
| `COMPLETED` | Withdrawal successfully completed |
| `FAILED` | Withdrawal failed (see error details in metadata) |
| `CANCELLED` | Withdrawal cancelled by user or system |

---

## Webhook Notifications

When a withdrawal status changes, JaiJaiPay will send webhook notifications to your configured endpoint.

### Webhook Payload
```json
{
  "event": "transaction.completed",
  "transactionId": "jjp_withdraw_abc123def456",
  "transactionReference": "WD20231215001",
  "status": "COMPLETED",
  "amount": "1000.00",
  "currency": "THB",
  "transactionType": "WITHDRAW",
  "companyId": "company_456",
  "merchantId": "merchant_123",
  "orderId": "BLK_WD_20231215_103000_001",
  "customerReference": "member123",
  "timestamp": "2023-12-15T10:45:00Z",
  "metadata": {
    "memberID": "123",
    "gameSession": "abc456"
  }
}
```

### Webhook Events
- `transaction.pending` - Withdrawal is pending
- `transaction.processing` - Withdrawal is being processed  
- `transaction.completed` - Withdrawal completed successfully
- `transaction.failed` - Withdrawal failed
- `transaction.cancelled` - Withdrawal cancelled

---

## WebSocket Real-time Notifications

The API supports real-time WebSocket notifications for withdrawal status updates. This allows frontend applications to receive instant updates when withdrawal status changes.

### WebSocket Connection

Connect to the WebSocket endpoint:
```
ws://your-domain.com/ws
```

### Withdrawal Events

The WebSocket client will emit `withdraw` events when withdrawal status changes:

```json
{
  "event": "withdraw",
  "data": {
    "channel_id": 123,
    "status": true
  }
}
```

### Event Data

| Field | Type | Description |
|-------|------|-------------|
| `channel_id` | integer | Member ID who initiated the withdrawal |
| `status` | boolean | `true` = withdrawal successful, `false` = withdrawal failed |

### Integration Example

#### JavaScript Client
```javascript
// Connect to WebSocket
const socket = io('ws://your-domain.com');

// Join member's channel to receive notifications
socket.emit('join', { channel_id: memberId });

// Listen for withdrawal notifications
socket.on('withdraw', (data) => {
  const { channel_id, status } = data;
  
  if (status) {
    console.log(`Withdrawal successful for member ${channel_id}`);
    // Update UI to show success
    showWithdrawalSuccess();
  } else {
    console.log(`Withdrawal failed for member ${channel_id}`);
    // Update UI to show failure
    showWithdrawalError();
  }
});

// Leave channel when done
socket.emit('leave', { channel_id: memberId });
```

#### React Hook Example
```javascript
import { useEffect, useState } from 'react';
import io from 'socket.io-client';

const useWithdrawalNotifications = (memberId) => {
  const [withdrawalStatus, setWithdrawalStatus] = useState(null);
  
  useEffect(() => {
    const socket = io('ws://your-domain.com');
    
    // Join member's channel
    socket.emit('join', { channel_id: memberId });
    
    // Listen for withdrawal events
    socket.on('withdraw', (data) => {
      if (data.channel_id === memberId) {
        setWithdrawalStatus({
          success: data.status,
          timestamp: new Date()
        });
      }
    });
    
    return () => {
      socket.emit('leave', { channel_id: memberId });
      socket.disconnect();
    };
  }, [memberId]);
  
  return withdrawalStatus;
};

// Usage in component
const WithdrawalComponent = ({ memberId }) => {
  const withdrawalStatus = useWithdrawalNotifications(memberId);
  
  useEffect(() => {
    if (withdrawalStatus) {
      if (withdrawalStatus.success) {
        showNotification('Withdrawal completed successfully!');
      } else {
        showNotification('Withdrawal failed. Please try again.');
      }
    }
  }, [withdrawalStatus]);
  
  return (
    <div>
      {/* Withdrawal UI */}
    </div>
  );
};
```

### Event Flow

1. **Frontend initiates withdrawal** via API call
2. **API processes withdrawal** through JaiJaiPay
3. **Webhook received** when JaiJaiPay updates status
4. **WebSocket event emitted** to member's channel
5. **Frontend receives notification** and updates UI

### WebSocket Events Summary

| Event | Direction | Purpose |
|-------|-----------|---------|
| `join` | Client → Server | Join member's notification channel |
| `leave` | Client → Server | Leave member's notification channel |
| `withdraw` | Server → Client | Withdrawal status update |
| `deposit` | Server → Client | Deposit status update (for reference) |

---

## Bank Codes

Supported Thai bank codes:

| Bank Code | Bank Name |
|-----------|-----------|
| `BBL` | Bangkok Bank |
| `KBANK` | Kasikorn Bank |
| `SCB` | Siam Commercial Bank |
| `TMB` | TMB Bank |
| `BAY` | Bank of Ayudhya (Krungsri) |
| `GSB` | Government Savings Bank |
| `KTB` | Krung Thai Bank |
| `TISCO` | Tisco Bank |
| `UOB` | United Overseas Bank |
| `CIMB` | CIMB Thai Bank |
| `LH` | Land and Houses Bank |
| `ICBC` | Industrial and Commercial Bank of China |
| `TCD` | Thai Credit Retail Bank |

---

## Error Responses

### Error Format
```json
{
  "error": "Withdrawal creation failed",
  "message": "Insufficient balance for withdrawal amount including fees",
  "errorCode": 4001,
  "details": {
    "requestedAmount": "1000.00",
    "availableBalance": "950.00",
    "totalFees": "35.00",
    "requiredAmount": "1035.00"
  },
  "requestId": "req_12345",
  "timestamp": "2023-12-15T10:30:00Z"
}
```

### Common Error Codes
| Code | Error | Description |
|------|-------|-------------|
| 4001 | Insufficient Balance | Not enough balance for withdrawal + fees |
| 4002 | Invalid Bank Account | Bank account validation failed |
| 4003 | Daily Limit Exceeded | Daily withdrawal limit exceeded |
| 4004 | Minimum Amount | Amount below minimum withdrawal threshold |
| 4005 | Maximum Amount | Amount exceeds maximum withdrawal limit |
| 4006 | Account Suspended | Customer account is suspended |
| 4007 | Invalid Currency | Unsupported currency |
| 5001 | Provider Error | JaiJaiPay service unavailable |
| 5002 | Network Error | Network connectivity issues |

---

## Rate Limits

- 100 requests per minute per member
- 1000 requests per hour per member  
- Burst limit: 10 requests per second

---

## Testing

### Test Bank Account
For testing withdrawals, use:
- Bank Code: `SCB`
- Account Number: `**********` 
- Account Holder: `Test Account`

### Test Amounts
- `100.00` - Always succeeds
- `999.00` - Always fails
- `500.00` - Takes 30 seconds to process

---

## Best Practices

1. **Always validate account holder name** - Ensure it matches the bank records
2. **Check balance before withdrawal** - Use the balance API to verify sufficient funds
3. **Handle webhooks properly** - Always return HTTP 200 for webhook requests
4. **Store transaction IDs** - Keep references to JaiJaiPay transaction IDs for support
5. **Monitor failed withdrawals** - Set up alerts for failed withdrawal transactions
6. **Use proper error handling** - Handle all possible error scenarios
7. **Implement retry logic** - For transient network errors only

## Example Integration Code

### JavaScript/Node.js
```javascript
// Create withdrawal
const withdrawal = await fetch('/api/v1/jaijai/withdrawals', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${memberToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    customerReference: memberId,
    amount: 1000.00,
    currency: 'THB',
    assetType: 'FIAT',
    bankCode: 'SCB',
    bankAccountNumber: '**********',
    accountHolderName: 'John Doe',
    description: 'Game winnings withdrawal',
    webhookUrl: 'https://your-domain.com/webhooks/jaijaipay'
  })
});

const result = await withdrawal.json();
console.log('Withdrawal created:', result.transactionId);
```

### PHP
```php
// Create withdrawal
$withdrawal = [
    'customerReference' => $memberId,
    'amount' => 1000.00,
    'currency' => 'THB',
    'assetType' => 'FIAT',
    'bankCode' => 'SCB',
    'bankAccountNumber' => '**********',
    'accountHolderName' => 'John Doe',
    'description' => 'Game winnings withdrawal',
    'webhookUrl' => 'https://your-domain.com/webhooks/jaijaipay'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, '/api/v1/jaijai/withdrawals');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($withdrawal));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $memberToken,
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$result = curl_exec($ch);
curl_close($ch);

$withdrawalResult = json_decode($result, true);
echo 'Withdrawal created: ' . $withdrawalResult['transactionId'];
```