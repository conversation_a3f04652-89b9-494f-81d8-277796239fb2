# 2-Step ERC20 Transfer Process Documentation

## Overview

ระบบนี้ใช้การโอน ERC20 token แบบ 2 ขั้นตอน โดยรวม gasless transfer กับ backend wallet management เพื่อให้ได้ transaction ID สำหรับ webhook tracking

## Architecture Flow

```
User Wallet → (Gasless) → Backend Wallet → (API Call) → Final Recipient
     ↓                           ↓                        ↓
  มี Token              รับ Token จาก User        ส่งต่อไปยังปลายทาง
  ไม่จ่าย Gas            จ่าย Gas Fee              ได้ Transaction ID
```

## ขั้นตอนการทำงานละเอียด

### Step 1: User Gasless Transfer

User โอน ERC20 token จาก smart account ไปยัง backend wallet แบบ gasless

```javascript
// User gasless transfer to backend wallet
const gaslessResult = await transferERC20Example(
  wallet.smartAccount,           // User's smart account
  backendWalletAddress,          // Backend wallet address  
  transferAmount                 // Amount to transfer
);
```

**ผลลัพธ์ Step 1:**
- ✅ User เสีย ERC20 token ตามจำนวนที่ระบุ
- ✅ ไม่มีการจ่าย gas fee (gasless via paymaster)
- ✅ Backend wallet ได้รับ token จาก user
- ✅ ได้ transaction hash สำหรับ track on-chain

### Step 2: Backend Wallet API Transfer

Backend wallet โอน token ต่อไปยัง final recipient ผ่าน thirdweb API

```javascript
// Backend wallet transfer via thirdweb API
const response = await fetch('https://api.thirdweb.com/v1/transactions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'x-secret-key': 'YOUR_SECRET_KEY'
  },
  body: JSON.stringify({
    chainId: 84532,
    from: backendWalletAddress,
    transactions: [{
      data: encodedTransferData,    // ERC20 transfer function encoded
      to: ERC20_CONTRACT_ADDRESS,   // ERC20 contract address
      value: "0"                    // No ETH transfer, only ERC20
    }]
  })
});
```

**ผลลัพธ์ Step 2:**
- ✅ Backend wallet โอน token ไปยัง final recipient
- ✅ ได้ transaction ID จาก thirdweb API
- ✅ Transaction ID สามารถใช้เช็ค webhook status ได้
- ✅ ได้ transaction hash สำหรับ track on-chain

## Configuration

### Network & Contract Settings

```javascript
// Chain configuration
const chainId = 84532; // Base Sepolia Testnet

// ERC20 Contract
const ERC20_CONTRACT_ADDRESS = "******************************************";

// Backend Wallet (must be configured in thirdweb dashboard)
const backendWalletAddress = "******************************************";
```

### API Configuration

```javascript
// Thirdweb API endpoint
const API_ENDPOINT = "https://api.thirdweb.com/v1/transactions";

// Authentication (store in environment variables)
const SECRET_KEY = "YOUR_SECRET_KEY";
```

## Data Encoding สำหรับ ERC20 Transfer

```javascript
/**
 * Encode ERC20 transfer function: transfer(address,uint256)
 */
function encodeTransferData(recipientAddress, transferAmount) {
  const functionSelector = "0xa9059cbb";  // keccak256("transfer(address,uint256)")[:4]
  
  // Remove 0x prefix and pad to 32 bytes (64 hex chars)
  const paddedAddress = recipientAddress.slice(2).padStart(64, '0');
  
  // Convert amount to hex and pad to 32 bytes
  const paddedAmount = parseInt(transferAmount).toString(16).padStart(64, '0');
  
  // Combine all parts
  const encodedData = functionSelector + paddedAddress + paddedAmount;
  
  return encodedData;
}
```

### Example Encoded Data

```javascript
// Input:
// recipientAddress = "0x9df32b1ef2342b193bd834c35ee74fc9d5a482bc"
// transferAmount = "100000000000000000" (0.1 token with 18 decimals)

// Output:
// "0xa9059cbb0000000000000000000000009df32b1ef2342b193bd834c35ee74fc9d5a482bc000000000000000000000000000000000000000000000000016345785d8a0000"
```

## Response Structure

### Complete Flow Response

```javascript
{
  step1_gasless: {
    transactionHash: "0xabc123...",
    status: "success",
    blockNumber: 12345,
    gasUsed: "0", // Gasless
    // ... other gasless transfer data
  },
  step2_backend: {
    result: {
      id: "8fc4420d-b3d4-411f-968c-9466fa3f3491", // ⭐ Transaction ID for webhook
      batchIndex: 0,
      clientId: "4f706a3633ab72a40b520249fba41678",
      chainId: "84532",
      from: "******************************************",
      transactionParams: [{
        to: "0x6897630c6634260393641746450acfe14655f767",
        data: "0xa9059cbb...",
        value: "0x0"
      }],
      transactionHash: "0xdef456...", // Backend transfer tx hash
      status: "COMPLETED",
      confirmedAt: "2025-08-29T20:30:23.443Z",
      confirmedAtBlockNumber: 12346,
      executionParams: {
        from: "******************************************",
        type: "EIP7702",
        chainId: 84532,
        idempotencyKey: "8fc4420d-b3d4-411f-968c-9466fa3f3491"
      },
      createdAt: "2025-08-29T20:30:23.443Z"
    }
  }
}
```

## Error Handling

### Step 1: Gasless Transfer Errors

```javascript
// Common Step 1 errors
try {
  const gaslessResult = await transferERC20Example(/*...*/);
} catch (error) {
  if (error.message.includes("insufficient balance")) {
    // User ไม่มี ERC20 token เพียงพอ
  } else if (error.message.includes("smart account")) {
    // Smart account connection issues
  } else {
    // Other gasless transfer errors
  }
}
```

### Step 2: Backend API Transfer Errors

```javascript
// Common Step 2 errors
const response = await fetch(/*...*/);
const result = await response.json();

if (!response.ok) {
  if (response.status === 401) {
    // API authentication failed
  } else if (result.message?.includes("insufficient balance")) {
    // Backend wallet ไม่มี token (step 1 may have failed)
  } else if (result.message?.includes("TRANSACTION_SIMULATION_FAILED")) {
    // Gas estimation or contract call failed
  } else {
    // Other API errors
  }
}
```

### Error Response Examples

```javascript
// Step 2 API Error Response
{
  "message": "server returned an error response: error code -32000...",
  "errorCode": "TRANSACTION_HASH_ERROR",
  "error": {
    "errorCode": "SIGNING_FAILED",
    "innerError": {
      "type": "VAULT_ERROR", 
      "message": "Enclave error: INVALID_INPUT - Invalid input..."
    }
  }
}
```

## Implementation Best Practices

### 1. Timing & Retry Logic

```javascript
// Wait for Step 1 confirmation before Step 2
await new Promise(resolve => setTimeout(resolve, 3000));

// Optional: Add retry logic for Step 2
async function retryBackendTransfer(maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = await callBackendAPI();
      return result;
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 2000 * (i + 1)));
    }
  }
}
```

### 2. Input Validation

```javascript
function validateInputs(wallet, recipientAddress, transferAmount) {
  // Check wallet connection
  if (!wallet?.smartAccount) {
    throw new Error("Smart account not connected");
  }
  
  // Validate recipient address
  if (!/^0x[a-fA-F0-9]{40}$/.test(recipientAddress)) {
    throw new Error("Invalid recipient address");
  }
  
  // Validate amount
  const amount = parseInt(transferAmount);
  if (isNaN(amount) || amount <= 0) {
    throw new Error("Invalid transfer amount");
  }
}
```

### 3. Security Considerations

```javascript
// Environment variables
const BACKEND_WALLET = process.env.BACKEND_WALLET_ADDRESS;
const SECRET_KEY = process.env.THIRDWEB_SECRET_KEY;
const CONTRACT_ADDRESS = process.env.ERC20_CONTRACT_ADDRESS;

// Never expose secret keys in frontend
// Store sensitive data in backend or environment variables
```

## Webhook Integration

### Using Transaction ID for Webhook Tracking

```javascript
// Extract transaction ID from Step 2 response
const transactionId = result.step2_backend.result.id;

// Use this ID to track webhook events
const webhookData = {
  transactionId: transactionId,
  status: "pending",
  webhookUrl: "https://your-webhook-endpoint.com/thirdweb-callback"
};

// Monitor webhook status
async function checkWebhookStatus(transactionId) {
  const response = await fetch(`https://api.thirdweb.com/v1/transaction/${transactionId}/status`);
  return response.json();
}
```

### Webhook Payload Example

```javascript
// Expected webhook payload from thirdweb
{
  "transactionId": "8fc4420d-b3d4-411f-968c-9466fa3f3491",
  "status": "COMPLETED",
  "transactionHash": "0xdef456...",
  "blockNumber": 12346,
  "timestamp": "2025-08-29T20:30:23.443Z",
  "chainId": 84532,
  "from": "******************************************",
  "to": "0x6897630c6634260393641746450acfe14655f767",
  "value": "0",
  "gasUsed": "65000"
}
```

## Benefits ของ Flow นี้

| Benefit | Description |
|---------|-------------|
| **User Experience** | User ไม่ต้องจ่าย gas fee สำหรับ step 1 |
| **Token Control** | User เสีย token ตามที่ควรจะเป็น (realistic UX) |
| **Transaction Tracking** | ได้ transaction ID สำหรับ webhook monitoring |
| **Backend Flexibility** | Backend wallet สามารถทำ logic เพิ่มเติมได้ก่อนส่งต่อ |
| **Error Recovery** | แต่ละ step สามารถ handle error แยกกันได้ |
| **Audit Trail** | ได้ transaction hash จากทั้ง 2 steps |

## Limitations & Trade-offs

| Limitation | Impact | Mitigation |
|------------|--------|------------|
| **2 Transactions** | ใช้เวลานานกว่าการโอนตรง | แสดง progress indicator |
| **Backend Dependency** | ต้องมี backend wallet เพื่อ relay | Monitor backend wallet balance |
| **Timing Complexity** | ต้อง wait ระหว่าง steps | Implement proper retry logic |
| **Gas Costs** | Backend wallet ต้องจ่าย gas สำหรับ step 2 | Monitor และ top up backend wallet |

## Monitoring & Maintenance

### Backend Wallet Health Check

```javascript
// Check backend wallet balance regularly
async function checkBackendWalletHealth() {
  const ethBalance = await getETHBalance(backendWalletAddress);
  const tokenBalance = await getERC20Balance(backendWalletAddress, contractAddress);
  
  if (ethBalance < minimumETHThreshold) {
    // Alert: Need to top up ETH for gas
  }
  
  if (tokenBalance > maximumTokenThreshold) {
    // Alert: Too many tokens accumulated, need to process
  }
}
```

### Transaction Monitoring

```javascript
// Monitor transaction success rates
const metrics = {
  step1_success_rate: 0.95,  // 95% gasless transfer success
  step2_success_rate: 0.98,  // 98% backend transfer success
  average_completion_time: 8000, // 8 seconds average
  webhook_delivery_rate: 0.99    // 99% webhook delivery success
};
```

---

## Quick Start Implementation

```javascript
async function implementTwoStepTransfer(wallet, recipientAddress, amount) {
  try {
    // Validate inputs
    validateInputs(wallet, recipientAddress, amount);
    
    // Step 1: User gasless transfer to backend
    console.log("Step 1: Transferring to backend wallet...");
    const step1Result = await transferERC20Example(
      wallet.smartAccount,
      BACKEND_WALLET_ADDRESS,
      amount
    );
    
    // Wait for confirmation
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Step 2: Backend transfer to final recipient
    console.log("Step 2: Backend transferring to recipient...");
    const step2Result = await callThirdwebAPI(
      BACKEND_WALLET_ADDRESS,
      recipientAddress,
      amount
    );
    
    // Return combined results
    return {
      success: true,
      transactionId: step2Result.result.id, // For webhook tracking
      step1: step1Result,
      step2: step2Result
    };
    
  } catch (error) {
    console.error("2-step transfer failed:", error);
    return {
      success: false,
      error: error.message
    };
  }
}
```