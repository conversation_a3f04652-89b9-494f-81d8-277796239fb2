# Return Turn API Examples

## Authentication Required
All `/admin/*` endpoints require Admin JWT token in the Authorization header.

## 1. <PERSON><PERSON> as Admin First
```bash
# Login to get admin JWT token
curl -X POST http://localhost:8080/api/v1/auth/admin/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "your_password"
  }'
```

Response:
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expire": "2024-01-16T10:00:00Z"
}
```

## 2. Get Return Turn Settings (with token)
```bash
# Use the token from login response
curl -X GET http://localhost:8080/api/v1/admin/return-turn/setting \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

## 3. Update Return Turn Settings
```bash
curl -X PATCH http://localhost:8080/api/v1/admin/return-turn/setting \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "returnPercent": 5.00,
    "returnTypeId": 1,
    "cutTypeId": 1,
    "minLossPrice": 500.00,
    "maxReturnPrice": 1000.00,
    "detail": "<p>คืนยอดเทิร์น 5%</p>",
    "isEnabled": true,
    "calculateTypes": [1, 2, 3]
  }'
```

## Common Error Responses

### 401 Unauthorized
```json
{
  "success": false,
  "error": "Unauthorized",
  "message": "Missing or invalid token"
}
```
**Solution**: Include valid JWT token in Authorization header

### 403 Forbidden
```json
{
  "success": false,
  "error": "Forbidden",
  "message": "Admin role required"
}
```
**Solution**: Login with admin account that has admin role

## Testing with HTTPie
```bash
# Install httpie first: pip install httpie

# Login
http POST localhost:8080/api/v1/auth/admin/login \
  username=admin password=your_password

# Get settings (save token to variable first)
export TOKEN="your_jwt_token_here"
http GET localhost:8080/api/v1/admin/return-turn/setting \
  "Authorization: Bearer $TOKEN"
```

## Testing with Postman

1. **Setup Environment Variables**:
   - `base_url`: http://localhost:8080
   - `admin_jwt_token`: (will be set after login)

2. **Login First**:
   - Use the Login request to get JWT token
   - In Tests tab, add script to save token:
   ```javascript
   if (pm.response.code === 200) {
     var jsonData = pm.response.json();
     pm.environment.set("admin_jwt_token", jsonData.token);
   }
   ```

3. **Call Protected Endpoints**:
   - Authorization header will automatically use: `Bearer {{admin_jwt_token}}`

## API Endpoints Summary

### Admin APIs (Require Admin JWT)
- `GET /api/v1/admin/return-turn/setting` - Get settings
- `PATCH /api/v1/admin/return-turn/setting` - Update settings
- `GET /api/v1/admin/return-turn/history/user-list` - User history list
- `GET /api/v1/admin/return-turn/history/user-summary` - User summary
- `GET /api/v1/admin/return-turn/history/log-list` - Transaction logs

### Web/User APIs (Require Member JWT)
- `GET /api/v1/web/return-turn-loser/current` - Current return detail
- `POST /api/v1/web/return-turn-loser/take` - Take return amount
- `GET /api/v1/web/return-turn-loser/list` - Return history

### Cron APIs (May require special auth)
- `GET /api/v1/cron/return-turn-loser/cut-daily` - Daily calculation
- `GET /api/v1/cron/return-turn-loser/cut-date?date=YYYY-MM-DD` - Calculate by date

## Troubleshooting

### Check if server is running
```bash
curl http://localhost:8080/health
```

### Check current user info (with token)
```bash
curl -X GET http://localhost:8080/api/v1/auth/current-user \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Verify token has admin role
Decode your JWT token at https://jwt.io to check the payload contains:
```json
{
  "identity": "admin_user_id",
  "role": "admin",
  "exp": **********
}
```