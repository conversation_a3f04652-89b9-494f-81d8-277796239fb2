# Golang WebSocket Client Documentation

## Table of Contents
- [Installation](#installation)
- [Project Structure](#project-structure)
- [Data Types](#data-types)
- [Configuration](#configuration)
- [WebSocket Client](#websocket-client)
- [Usage Examples](#usage-examples)
- [Environment Variables](#environment-variables)
- [Error Handling](#error-handling)
- [Testing](#testing)
- [Server Updates Required](#server-updates-required)

## Installation

### 1. สร้าง Go Module
```bash
mkdir websocket-client
cd websocket-client
go mod init websocket-client
go get github.com/gorilla/websocket
```

### 2. Project Structure
```
websocket-client/
├── main.go
├── client/
│   ├── websocket.go
│   └── types.go
├── config/
│   └── config.go
├── .env
├── README.md
└── go.mod
```

## Data Types

สร้างไฟล์ `client/types.go`:

```go
package client

// ข้อมูลสำหรับ deposit event
type GameSocketData struct {
    UserID      string `json:"userId"`
    MemberCode  string `json:"memberCode"`
    PhoneNumber string `json:"phoneNumber"`
    FullName    string `json:"fullName"`
    Amount      int    `json:"amount"`
    AlertType   string `json:"alertType"`
    BankAccount string `json:"bankAccount"`
}

// ข้อมูลสำหรับ withdraw event
type WithdrawData struct {
    UserID  string `json:"userId"`
    Amount  int    `json:"amount"`
    Message string `json:"message"`
}

// ข้อมูลสำหรับ admin dashboard update
type AdminCorpDashboard struct {
    List []interface{} `json:"list"`
}

// Socket.IO message format
type SocketIOMessage struct {
    Type string      `json:"type"`
    NSP  string      `json:"nsp"`
    Data interface{} `json:"data"`
}

// Event message wrapper
type EventMessage struct {
    Event string      `json:"event"`
    Data  interface{} `json:"data"`
}
```

## Configuration

สร้างไฟล์ `config/config.go`:

```go
package config

import (
    "os"
    "strconv"
    "time"
)

type Config struct {
    ServerURL        string
    ReconnectDelay   time.Duration
    MaxRetries       int
    PingInterval     time.Duration
    WriteTimeout     time.Duration
    ReadTimeout      time.Duration
    HandshakeTimeout time.Duration
}

func GetConfig() *Config {
    serverURL := getEnv("WEBSOCKET_SERVER_URL", "ws://localhost:3000/socket.io/")
    reconnectDelay := getEnvDuration("RECONNECT_DELAY", 5*time.Second)
    maxRetries := getEnvInt("MAX_RETRIES", 10)
    
    return &Config{
        ServerURL:        serverURL,
        ReconnectDelay:   reconnectDelay,
        MaxRetries:       maxRetries,
        PingInterval:     54 * time.Second,
        WriteTimeout:     10 * time.Second,
        ReadTimeout:      60 * time.Second,
        HandshakeTimeout: 10 * time.Second,
    }
}

func getEnv(key, defaultValue string) string {
    if value := os.Getenv(key); value != "" {
        return value
    }
    return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
    if value := os.Getenv(key); value != "" {
        if intValue, err := strconv.Atoi(value); err == nil {
            return intValue
        }
    }
    return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
    if value := os.Getenv(key); value != "" {
        if duration, err := time.ParseDuration(value); err == nil {
            return duration
        }
    }
    return defaultValue
}
```

## WebSocket Client

สร้างไฟล์ `client/websocket.go`:

```go
package client

import (
    "encoding/json"
    "fmt"
    "log"
    "net/url"
    "sync"
    "time"

    "github.com/gorilla/websocket"
)

type WSClient struct {
    conn           *websocket.Conn
    url            string
    connected      bool
    reconnectDelay time.Duration
    maxRetries     int
    retries        int
    mutex          sync.RWMutex
    done           chan struct{}
    send           chan []byte
    pingTicker     *time.Ticker
    
    // Event callbacks
    onConnect    func()
    onDisconnect func(error)
    onMessage    func([]byte)
    onError      func(error)
}

// สร้าง WebSocket client ใหม่
func NewWSClient(serverURL string) *WSClient {
    return &WSClient{
        url:            serverURL,
        reconnectDelay: 5 * time.Second,
        maxRetries:     10,
        done:           make(chan struct{}),
        send:           make(chan []byte, 256),
    }
}

// ตั้งค่า event callbacks
func (c *WSClient) OnConnect(callback func()) {
    c.onConnect = callback
}

func (c *WSClient) OnDisconnect(callback func(error)) {
    c.onDisconnect = callback
}

func (c *WSClient) OnMessage(callback func([]byte)) {
    c.onMessage = callback
}

func (c *WSClient) OnError(callback func(error)) {
    c.onError = callback
}

// เชื่อมต่อกับ WebSocket server
func (c *WSClient) Connect() error {
    u, err := url.Parse(c.url)
    if err != nil {
        return fmt.Errorf("invalid URL: %v", err)
    }

    dialer := websocket.DefaultDialer
    dialer.HandshakeTimeout = 10 * time.Second

    conn, _, err := dialer.Dial(u.String(), nil)
    if err != nil {
        if c.retries < c.maxRetries {
            c.retries++
            log.Printf("Connection failed, retrying in %v... (attempt %d/%d)", 
                c.reconnectDelay, c.retries, c.maxRetries)
            time.Sleep(c.reconnectDelay)
            return c.Connect()
        }
        return fmt.Errorf("connection failed after %d attempts: %v", c.maxRetries, err)
    }

    c.mutex.Lock()
    c.conn = conn
    c.connected = true
    c.retries = 0
    c.done = make(chan struct{})
    c.send = make(chan []byte, 256)
    c.mutex.Unlock()

    // Start goroutines
    go c.writePump()
    go c.readPump()
    go c.pingPump()

    if c.onConnect != nil {
        c.onConnect()
    }

    log.Println("Connected to WebSocket server")
    return nil
}

// อ่านข้อความจาก server
func (c *WSClient) readPump() {
    defer func() {
        c.disconnect(nil)
    }()

    c.conn.SetReadLimit(8192)
    c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
    c.conn.SetPongHandler(func(string) error {
        c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
        return nil
    })

    for {
        select {
        case <-c.done:
            return
        default:
            _, message, err := c.conn.ReadMessage()
            if err != nil {
                if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
                    log.Printf("WebSocket error: %v", err)
                    if c.onError != nil {
                        c.onError(err)
                    }
                }
                return
            }

            if c.onMessage != nil {
                c.onMessage(message)
            }
        }
    }
}

// ส่งข้อความไปยง server
func (c *WSClient) writePump() {
    defer func() {
        if c.conn != nil {
            c.conn.Close()
        }
    }()

    for {
        select {
        case message, ok := <-c.send:
            if !ok {
                c.conn.WriteMessage(websocket.CloseMessage, []byte{})
                return
            }

            c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
            if err := c.conn.WriteMessage(websocket.TextMessage, message); err != nil {
                log.Printf("Write error: %v", err)
                if c.onError != nil {
                    c.onError(err)
                }
                return
            }

        case <-c.done:
            return
        }
    }
}

// ส่ง ping เพื่อรักษาการเชื่อมต่อ
func (c *WSClient) pingPump() {
    c.pingTicker = time.NewTicker(54 * time.Second)
    defer c.pingTicker.Stop()

    for {
        select {
        case <-c.pingTicker.C:
            c.mutex.RLock()
            connected := c.connected
            c.mutex.RUnlock()

            if connected {
                c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
                if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
                    log.Printf("Ping error: %v", err)
                    if c.onError != nil {
                        c.onError(err)
                    }
                    return
                }
            }

        case <-c.done:
            return
        }
    }
}

// ตัดการเชื่อมต่อ
func (c *WSClient) disconnect(err error) {
    c.mutex.Lock()
    defer c.mutex.Unlock()

    if !c.connected {
        return
    }

    c.connected = false
    close(c.done)

    if c.pingTicker != nil {
        c.pingTicker.Stop()
    }

    if c.conn != nil {
        c.conn.Close()
    }

    if c.onDisconnect != nil {
        c.onDisconnect(err)
    }

    log.Println("Disconnected from WebSocket server")
}

// ส่งข้อความ JSON
func (c *WSClient) SendJSON(data interface{}) error {
    c.mutex.RLock()
    defer c.mutex.RUnlock()

    if !c.connected {
        return fmt.Errorf("not connected")
    }

    jsonData, err := json.Marshal(data)
    if err != nil {
        return fmt.Errorf("marshal error: %v", err)
    }

    select {
    case c.send <- jsonData:
        return nil
    default:
        return fmt.Errorf("send channel is full")
    }
}

// ส่งข้อมูล deposit
func (c *WSClient) SendDeposit(data GameSocketData) error {
    return c.SendJSON(data)
}

// ส่งข้อมูล withdraw
func (c *WSClient) SendWithdraw(data WithdrawData) error {
    return c.SendJSON(data)
}

// ส่งข้อมูล admin dashboard update
func (c *WSClient) SendAdminDashboardUpdate(data AdminCorpDashboard) error {
    return c.SendJSON(data)
}

// ปิดการเชื่อมต่อ
func (c *WSClient) Close() {
    c.disconnect(nil)
}

// ตรวจสอบสถานะการเชื่อมต่อ
func (c *WSClient) IsConnected() bool {
    c.mutex.RLock()
    defer c.mutex.RUnlock()
    return c.connected
}

// ตั้งค่า reconnect parameters
func (c *WSClient) SetReconnectConfig(delay time.Duration, maxRetries int) {
    c.reconnectDelay = delay
    c.maxRetries = maxRetries
}
```

## Usage Examples

### 1. Basic Usage (`main.go`)

```go
package main

import (
    "log"
    "os"
    "os/signal"
    "time"

    "websocket-client/client"
    "websocket-client/config"
)

func main() {
    cfg := config.GetConfig()
    wsClient := client.NewWSClient(cfg.ServerURL)

    // ตั้งค่า event handlers
    wsClient.OnConnect(func() {
        log.Println("✅ Connected to server!")
    })

    wsClient.OnDisconnect(func(err error) {
        if err != nil {
            log.Printf("❌ Disconnected with error: %v", err)
        } else {
            log.Println("📴 Disconnected from server")
        }
    })

    wsClient.OnMessage(func(message []byte) {
        log.Printf("📨 Received: %s", string(message))
    })

    wsClient.OnError(func(err error) {
        log.Printf("🚨 Error: %v", err)
    })

    // เชื่อมต่อ
    if err := wsClient.Connect(); err != nil {
        log.Fatal("Connection failed:", err)
    }

    // ส่งข้อมูล test
    go func() {
        time.Sleep(2 * time.Second)

        // ส่ง deposit
        depositData := client.GameSocketData{
            UserID:      "user001",
            MemberCode:  "MEM001",
            PhoneNumber: "**********",
            FullName:    "John Doe",
            Amount:      1500,
            AlertType:   "deposit",
            BankAccount: "**********",
        }

        if err := wsClient.SendDeposit(depositData); err != nil {
            log.Printf("Error sending deposit: %v", err)
        } else {
            log.Println("💰 Deposit sent successfully")
        }

        time.Sleep(3 * time.Second)

        // ส่ง withdraw
        withdrawData := client.WithdrawData{
            UserID:  "user001",
            Amount:  500,
            Message: "Withdrawal request",
        }

        if err := wsClient.SendWithdraw(withdrawData); err != nil {
            log.Printf("Error sending withdraw: %v", err)
        } else {
            log.Println("💸 Withdraw sent successfully")
        }

        time.Sleep(2 * time.Second)

        // ส่ง admin dashboard update
        dashboardData := client.AdminCorpDashboard{
            List: []interface{}{
                map[string]interface{}{"id": 1, "name": "Player 1", "balance": 1500},
                map[string]interface{}{"id": 2, "name": "Player 2", "balance": 2000},
            },
        }

        if err := wsClient.SendAdminDashboardUpdate(dashboardData); err != nil {
            log.Printf("Error sending dashboard update: %v", err)
        } else {
            log.Println("📊 Dashboard update sent successfully")
        }
    }()

    // รอ interrupt signal
    interrupt := make(chan os.Signal, 1)
    signal.Notify(interrupt, os.Interrupt)

    <-interrupt
    log.Println("🛑 Shutting down...")
    wsClient.Close()
}
```

### 2. Advanced Usage with Auto-Reconnection

```go
package main

import (
    "log"
    "os"
    "os/signal"
    "time"

    "websocket-client/client"
    "websocket-client/config"
)

type GameClient struct {
    wsClient  *client.WSClient
    cfg       *config.Config
    running   bool
}

func NewGameClient() *GameClient {
    return &GameClient{
        cfg:     config.GetConfig(),
        running: false,
    }
}

func (g *GameClient) Start() error {
    g.wsClient = client.NewWSClient(g.cfg.ServerURL)
    g.wsClient.SetReconnectConfig(g.cfg.ReconnectDelay, g.cfg.MaxRetries)
    g.running = true

    // Setup event handlers
    g.wsClient.OnConnect(g.onConnect)
    g.wsClient.OnDisconnect(g.onDisconnect)
    g.wsClient.OnMessage(g.onMessage)
    g.wsClient.OnError(g.onError)

    return g.wsClient.Connect()
}

func (g *GameClient) onConnect() {
    log.Println("🎮 Game client connected!")
}

func (g *GameClient) onDisconnect(err error) {
    log.Println("🔌 Game client disconnected")
    
    // Auto-reconnect
    if g.running {
        go g.reconnect()
    }
}

func (g *GameClient) onMessage(message []byte) {
    log.Printf("📩 Game message: %s", string(message))
    
    // Parse message and handle different event types
    // You can add JSON parsing here to handle specific events
}

func (g *GameClient) onError(err error) {
    log.Printf("⚠️ Game client error: %v", err)
}

func (g *GameClient) reconnect() {
    log.Println("🔄 Attempting to reconnect...")
    time.Sleep(g.cfg.ReconnectDelay)
    
    if err := g.wsClient.Connect(); err != nil {
        log.Printf("Reconnection failed: %v", err)
    }
}

// Business methods
func (g *GameClient) ProcessDeposit(userID, memberCode, phoneNumber, fullName, bankAccount string, amount int) error {
    data := client.GameSocketData{
        UserID:      userID,
        MemberCode:  memberCode,
        PhoneNumber: phoneNumber,
        FullName:    fullName,
        Amount:      amount,
        AlertType:   "deposit",
        BankAccount: bankAccount,
    }
    
    return g.wsClient.SendDeposit(data)
}

func (g *GameClient) ProcessWithdraw(userID string, amount int, message string) error {
    data := client.WithdrawData{
        UserID:  userID,
        Amount:  amount,
        Message: message,
    }
    
    return g.wsClient.SendWithdraw(data)
}

func (g *GameClient) UpdateDashboard(list []interface{}) error {
    data := client.AdminCorpDashboard{
        List: list,
    }
    
    return g.wsClient.SendAdminDashboardUpdate(data)
}

func (g *GameClient) Stop() {
    g.running = false
    if g.wsClient != nil {
        g.wsClient.Close()
    }
}

func (g *GameClient) IsConnected() bool {
    if g.wsClient == nil {
        return false
    }
    return g.wsClient.IsConnected()
}

func main() {
    gameClient := NewGameClient()
    
    if err := gameClient.Start(); err != nil {
        log.Fatal("Failed to start game client:", err)
    }

    // Simulate business operations
    go func() {
        time.Sleep(2 * time.Second)
        
        // Process deposit
        err := gameClient.ProcessDeposit(
            "user123", "MEM001", "**********", 
            "John Doe", "**********", 1000)
        if err != nil {
            log.Printf("Deposit error: %v", err)
        }

        time.Sleep(3 * time.Second)

        // Process withdraw
        err = gameClient.ProcessWithdraw("user123", 500, "Player withdrawal")
        if err != nil {
            log.Printf("Withdraw error: %v", err)
        }

        time.Sleep(2 * time.Second)

        // Update dashboard
        dashboardData := []interface{}{
            map[string]interface{}{"id": 1, "name": "Player 1", "balance": 1500},
            map[string]interface{}{"id": 2, "name": "Player 2", "balance": 2000},
        }
        
        err = gameClient.UpdateDashboard(dashboardData)
        if err != nil {
            log.Printf("Dashboard update error: %v", err)
        }
    }()

    // Health check routine
    go func() {
        ticker := time.NewTicker(30 * time.Second)
        defer ticker.Stop()
        
        for {
            select {
            case <-ticker.C:
                if gameClient.IsConnected() {
                    log.Println("💚 Connection health check: OK")
                } else {
                    log.Println("💔 Connection health check: FAILED")
                }
            }
        }
    }()

    // Handle shutdown
    interrupt := make(chan os.Signal, 1)
    signal.Notify(interrupt, os.Interrupt)

    <-interrupt
    log.Println("🛑 Shutting down game client...")
    gameClient.Stop()
}
```

## Environment Variables

สร้างไฟล์ `.env`:

```env
# WebSocket Server Configuration
WEBSOCKET_SERVER_URL=ws://localhost:3000/socket.io/

# Connection Settings
RECONNECT_DELAY=5s
MAX_RETRIES=10

# Logging
LOG_LEVEL=info
```

## การรัน

```bash
# Development
go run main.go

# Build
go build -o websocket-client main.go

# Run with custom server
WEBSOCKET_SERVER_URL=ws://production-server:3000/socket.io/ ./websocket-client

# Run with environment file
export $(cat .env | xargs) && go run main.go
```

## Error Handling

### Retry Mechanism

```go
func (g *GameClient) ProcessDepositWithRetry(data client.GameSocketData, maxRetries int) error {
    for i := 0; i < maxRetries; i++ {
        if err := g.wsClient.SendDeposit(data); err != nil {
            log.Printf("Deposit attempt %d failed: %v", i+1, err)
            if i < maxRetries-1 {
                time.Sleep(time.Second * time.Duration(i+1))
                continue
            }
            return fmt.Errorf("deposit failed after %d attempts: %v", maxRetries, err)
        }
        return nil
    }
    return fmt.Errorf("all retry attempts failed")
}
```

### Circuit Breaker Pattern

```go
type CircuitBreaker struct {
    failures    int
    maxFailures int
    timeout     time.Duration
    lastFailure time.Time
    state       string // "closed", "open", "half-open"
}

func (cb *CircuitBreaker) Call(fn func() error) error {
    if cb.state == "open" {
        if time.Since(cb.lastFailure) > cb.timeout {
            cb.state = "half-open"
        } else {
            return fmt.Errorf("circuit breaker is open")
        }
    }

    err := fn()
    if err != nil {
        cb.failures++
        cb.lastFailure = time.Now()
        if cb.failures >= cb.maxFailures {
            cb.state = "open"
        }
        return err
    }

    cb.failures = 0
    cb.state = "closed"
    return nil
}
```

## Testing

สร้างไฟล์ `client/websocket_test.go`:

```go
package client

import (
    "testing"
    "time"
)

func TestWebSocketConnection(t *testing.T) {
    wsClient := NewWSClient("ws://localhost:3000/socket.io/")
    
    connected := false
    wsClient.OnConnect(func() {
        connected = true
    })
    
    if err := wsClient.Connect(); err != nil {
        t.Fatalf("Connection failed: %v", err)
    }
    
    time.Sleep(1 * time.Second)
    
    if !connected {
        t.Error("Expected to be connected")
    }
    
    // Test sending data
    depositData := GameSocketData{
        UserID:      "test_user",
        MemberCode:  "TEST001",
        PhoneNumber: "**********",
        FullName:    "Test User",
        Amount:      100,
        AlertType:   "deposit",
        BankAccount: "**********",
    }
    
    if err := wsClient.SendDeposit(depositData); err != nil {
        t.Errorf("Failed to send deposit: %v", err)
    }
    
    wsClient.Close()
    
    time.Sleep(1 * time.Second)
    
    if wsClient.IsConnected() {
        t.Error("Expected to be disconnected")
    }
}

func TestReconnection(t *testing.T) {
    wsClient := NewWSClient("ws://invalid-server:3000/socket.io/")
    wsClient.SetReconnectConfig(1*time.Second, 3)
    
    err := wsClient.Connect()
    if err == nil {
        t.Error("Expected connection to fail for invalid server")
    }
}
```

รันเทส:
```bash
go test ./client -v
```

## Server Updates Required

ใน Node.js server คุณต้องเพิ่ม event handlers เหล่านี้ใน `src/server.ts`:

```typescript
// เพิ่มใน setupEventHandlers() method
socket.on('game-socket', (data: any) => {
  try {
    console.log('Received game-socket from Golang client:', data);
    
    // Validate data
    if (!data.userId || !data.amount) {
      Logger.error('Invalid game-socket data received');
      return;
    }
    
    // Emit to all clients
    this.io.emit('game-socket', data);
    Logger.info('Game-socket data broadcasted to all clients');
    
  } catch (error) {
    Logger.error('Error handling game-socket event:', error);
  }
});

socket.on('withdraw', (data: any) => {
  try {
    console.log('Received withdraw from Golang client:', data);
    
    if (!data.userId || !data.amount) {
      Logger.error('Invalid withdraw data received');
      return;
    }
    
    this.io.emit('withdraw', data);
    Logger.info('Withdraw data broadcasted to all clients');
    
  } catch (error) {
    Logger.error('Error handling withdraw event:', error);
  }
});

socket.on('update-admincorp-dashboard', (data: any) => {
  try {
    console.log('Received dashboard update from Golang client:', data);
    
    if (!data.list) {
      Logger.error('Invalid dashboard data received');
      return;
    }
    
    this.io.emit('update-admincorp-dashboard', data);
    Logger.info('Dashboard update broadcasted to all clients');
    
  } catch (error) {
    Logger.error('Error handling dashboard update event:', error);
  }
});
```

## Performance Tips

1. **Connection Pooling**: สำหรับ high-throughput applications
2. **Message Batching**: รวมข้อความหลายๆ อันส่งพร้อมกัน
3. **Compression**: ใช้ gzip compression สำหรับข้อความขนาดใหญ่
4. **Health Monitoring**: ติดตาม connection status และ metrics

## Production Deployment

```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN go build -o websocket-client main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/websocket-client .
COPY --from=builder /app/.env .

CMD ["./websocket-client"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  websocket-client:
    build: .
    environment:
      - WEBSOCKET_SERVER_URL=ws://websocket-server:3000/socket.io/
    depends_on:
      - websocket-server
    restart: unless-stopped
```

---

## 📞 Support

หากมีปัญหาหรือข้อสงสัย สามารถติดต่อได้ที่:
- GitHub Issues
- Email: <EMAIL>

Happy coding! 🚀