# JaiJaiPay API Integration

This document describes the JaiJaiPay API integration implemented in the Blacking API.

## Overview

The JaiJaiPay integration provides comprehensive payment processing capabilities including:
- **Deposits**: Create, list, retrieve, and cancel deposit transactions
- **Withdrawals**: Create, list, and retrieve withdrawal transactions  
- **Balance**: Get account balance and financial summaries
- **Fees**: Preview transaction fees and calculations
- **Analytics**: Retrieve transaction analytics and insights
- **Webhooks**: Resend webhook notifications and validate payloads
- **API Logging**: Complete audit trail of all API interactions

## Architecture

### Components

```
pkg/jaijaipay/          # JaiJaiPay client package
├── client.go           # HTTP client with logging integration
├── config.go           # Configuration management  
├── signature.go        # HMAC-SHA256 authentication
├── models.go           # Request/response models
├── deposits_service.go # Deposits API service
├── withdrawals_service.go # Withdrawals API service
├── balance_service.go  # Balance API service
├── fees_service.go     # Fees API service
├── analytics_service.go # Analytics API service
└── webhooks_service.go # Webhooks API service

internal/
├── service/jaijaipay_service.go    # Business logic layer
├── handler/http/jaijaipay_handler.go # HTTP handlers
├── router/jaijaipay.go             # Route definitions
└── repository/postgres/jaijaipay_api_log_repository.go # API logging

migrations/list/23_create_jaijaipay_api_logs.go # Database schema
```

## Configuration

### Environment Variables

Add to your `.env` file:

```bash
# JaiJaiPay API Configuration
JAIJAIPAY_API_KEY=your-api-key-here
JAIJAIPAY_SECRET_KEY=your-secret-key-here
```

### Configuration

All JaiJaiPay configuration is managed through environment variables:

```bash
# Required
JAIJAIPAY_API_KEY=your-api-key-here
JAIJAIPAY_SECRET_KEY=your-secret-key-here

# Optional (with defaults shown)
JAIJAIPAY_BASE_URL=https://api.jaijaipay.com/api/v1
JAIJAIPAY_TIMEOUT=30s
JAIJAIPAY_MAX_RETRIES=3
JAIJAIPAY_RETRY_DELAY=1s
JAIJAIPAY_ENABLE_REQUEST_LOG=true
JAIJAIPAY_LOG_RESPONSE_BODY=false  # Security: Don't log sensitive data
JAIJAIPAY_ENABLE_DEBUG=false
```

## API Endpoints

### Authentication
All requests require JWT authentication via the `RequireAuth()` middleware.

### Deposits
- `POST /api/v1/jaijai/deposits` - Create deposit
- `GET /api/v1/jaijai/deposits` - List deposits  
- `GET /api/v1/jaijai/deposits/:transactionId` - Get deposit by ID
- `POST /api/v1/jaijai/deposits/cancel` - Cancel deposit

### Withdrawals  
- `POST /api/v1/jaijai/withdrawals` - Create withdrawal
- `GET /api/v1/jaijai/withdrawals` - List withdrawals
- `GET /api/v1/jaijai/withdrawals/:transactionId` - Get withdrawal by ID

### Balance
- `GET /api/v1/jaijai/balance` - Get account balance

### Fees
- `GET /api/v1/jaijai/fees/preview` - Get fee preview

### Analytics
- `GET /api/v1/jaijai/analytics/transactions` - Get transaction analytics

### Webhooks
- `POST /api/v1/jaijai/webhooks/resend` - Resend webhook
- `POST /api/v1/jaijai/webhook` - **NEW: JaiJaiPay webhook endpoint (no auth required)**

### API Logs (Debugging)
- `GET /api/v1/jaijai/logs/order/:orderId` - Get API logs by order ID
- `GET /api/v1/jaijai/logs/transaction/:transactionId` - Get API logs by transaction ID

## Usage Examples

### Initialize Client (Programmatic)

```go
// Configuration
config := jaijaipay.Config{
    BaseURL:             "https://api.jaijaipay.com/api/v1",
    APIKey:              "your-api-key",
    SecretKey:           "your-secret-key", 
    Timeout:             30 * time.Second,
    MaxRetries:          3,
    RetryDelay:          1 * time.Second,
    EnableRequestLog:    true,
    LogResponseBody:     false,
    EnableDebug:         false,
}

// Initialize client
client, err := jaijaipay.NewClient(config, logger, apiLogRepo)
if err != nil {
    log.Fatal("Failed to create JaiJaiPay client:", err)
}

// Use client
deposit, err := client.Deposits.Create(ctx, &jaijaipay.CreateDepositRequest{
    OrderID:           "ORDER-123",
    CustomerReference: "user-456", 
    Amount:            100.00,
    Currency:          "THB",
    BankCode:          "KBANK",
    // ... other fields
})
```

### HTTP API Usage

#### Create Deposit
```bash
curl -X POST http://localhost:8080/api/v1/jaijai/deposits \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -H "Content-Type: application/json" \\
  -d '{
    "orderId": "ORDER-123",
    "customerReference": "user-456",
    "amount": 100.00,
    "currency": "THB",
    "assetType": "FIAT",
    "bankCode": "KBANK", 
    "bankAccountNumber": "**********",
    "accountHolderName": "John Doe",
    "description": "Test deposit",
    "webhookUrl": "https://your-domain.com/webhook"
  }'
```

#### List Deposits
```bash
curl -X GET "http://localhost:8080/api/v1/jaijai/deposits?page=1&limit=10&currency=THB" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Database Schema

### API Logs Table
All JaiJaiPay API calls are automatically logged to the `jaijaipay_api_logs` table:

```sql
CREATE TABLE jaijaipay_api_logs (
    id SERIAL PRIMARY KEY,
    request_id VARCHAR(255) NOT NULL UNIQUE,
    api_category VARCHAR(50) NOT NULL,
    method VARCHAR(10) NOT NULL,
    endpoint VARCHAR(255) NOT NULL,
    request_body JSONB,
    request_headers JSONB,
    response_status_code INTEGER,
    response_body JSONB,
    response_time_ms INTEGER,
    success BOOLEAN DEFAULT false,
    error_message TEXT,
    business_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Security Features

### HMAC-SHA256 Authentication
All requests are authenticated using HMAC-SHA256 signatures:
- **POST/PUT/PATCH**: `signature = HMAC-SHA256(timestamp + JSON_body, secret_key)`
- **GET/DELETE**: `signature = HMAC-SHA256(timestamp, secret_key)`

### Request Headers
```
x-api-key: your-api-key
x-signature: generated-hmac-signature  
x-timestamp: unix-timestamp
Content-Type: application/json
```

### Security Best Practices
- ✅ API keys stored in environment variables
- ✅ Signatures generated dynamically 
- ✅ Sensitive data not logged in production
- ✅ HTTPS required for all requests
- ✅ Request/response logging for audit trail

## Monitoring & Debugging  

### API Call Logging
Every API call is automatically logged with:
- Request/response details
- Performance metrics  
- Error information
- Business context (order ID, transaction ID, etc.)

### Logging Configuration
```yaml
jaijaipay:
  enable_request_log: true    # Log all API calls
  log_response_body: false    # Don't log sensitive response data
  enable_debug: false         # Debug mode for development
```

### Error Handling
- Automatic retry with exponential backoff
- Comprehensive error messages
- Request ID tracking for support
- Structured error responses

## Migration

### Database Migration
```bash
# Run migration to create API logs table
make migrate-up ENV=local
```

### Rollback Migration  
```bash
# Rollback if needed
make migrate-down ENV=local
```

## Testing

### Unit Tests
```bash
# Test JaiJaiPay package
go test ./pkg/jaijaipay/...

# Test services and handlers
go test ./internal/service/jaijaipay_service_test.go
go test ./internal/handler/http/jaijaipay_handler_test.go
```

### Integration Tests
```bash
# Test with real API (requires valid credentials)
JAIJAIPAY_API_KEY=test_key JAIJAIPAY_SECRET_KEY=test_secret go test -tags=integration ./...
```

## Development Guidelines

### Adding New Endpoints
1. Add models to `pkg/jaijaipay/models.go`
2. Create service method in appropriate service file
3. Add HTTP handler method
4. Update routes in `internal/router/jaijaipay.go`

### Error Handling
- Always use structured errors from `pkg/errors`
- Log errors with appropriate context
- Return user-friendly error messages
- Include request IDs for debugging

### Performance Considerations
- Use connection pooling for HTTP client
- Implement request timeouts
- Add retry logic with backoff
- Monitor API response times

## Troubleshooting

### Common Issues

#### 1. Authentication Errors
```
Error: API error -1901: Invalid signature
```
**Solution**: Verify API key and secret key are correct

#### 2. Timeout Errors
```  
Error: request timeout
```
**Solution**: Increase timeout in configuration or check network connectivity

#### 3. API Logs Not Appearing
**Check**: 
- `enable_request_log: true` in config
- Database migration completed
- Repository initialized correctly

### Debug Mode
Enable debug mode for detailed logging:
```yaml
jaijaipay:
  enable_debug: true
  log_response_body: true  # Development only
```

## Support

### Documentation
- [JaiJaiPay API Docs](https://docs.jaijaipay.com)
- [Swagger/OpenAPI Spec](docs/swagger.yaml)

### Logs & Monitoring
- API logs: `GET /api/v1/jaijai/logs/order/:orderId`
- System logs: Check application logs with `jaijaipay` tag
- Database: Query `jaijaipay_api_logs` table

---

**Last Updated**: August 26, 2025  
**Version**: 1.1.0  
**Status**: ✅ Production Ready

## Recent Updates (v1.1.0)

### ✅ NEW: Webhook Endpoint
- **Endpoint**: `POST /api/v1/jaijai/webhook`
- **Purpose**: Receives webhook notifications from JaiJaiPay
- **Authentication**: None required (external service)
- **Signature Validation**: HMAC-SHA256 via `X-JaiJaiPay-Signature` header

### ✅ API Versioning
- **Changed**: All JaiJaiPay endpoints now use `/api/v1/jaijai/*` prefix
- **Backward Compatibility**: Old `/api/jaijaipay/*` endpoints are deprecated

### Webhook Integration

JaiJaiPay will send webhook notifications to your configured endpoint for transaction status updates:

#### Webhook Events
- `transaction.completed` - Transaction successfully completed
- `transaction.cancelled` - Transaction cancelled
- `transaction.failed` - Transaction failed  
- `transaction.pending` - Transaction status changed to pending

#### Webhook Headers
```
X-JaiJaiPay-Signature: <hmac-sha256-signature>
Content-Type: application/json
User-Agent: JaiJaiPay-Webhook/1.0
```

#### Webhook Payload Example
```json
{
  "event": "transaction.completed",
  "transactionId": "616faf33-0ccb-44c4-a54c-256455d2753d",
  "transactionReference": "DEP-1756205111581-Q0ON6D",
  "status": "COMPLETED",
  "amount": "1.15",
  "currency": "THB",
  "transactionType": "DEPOSIT",
  "companyId": "4503945d-b9b5-4a1a-8392-d4b088492362",
  "merchantId": "0cb52394-3b3a-4e44-ae56-408bd6086419",
  "orderId": "DEP2051106589",
  "customerReference": "2",
  "metadata": {},
  "timestamp": "2025-08-26T11:15:11.571Z"
}
```

#### Webhook Configuration
Set your webhook URL in JaiJaiPay dashboard:
```
https://your-api-domain.com/api/v1/jaijai/webhook
```