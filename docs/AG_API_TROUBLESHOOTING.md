# AG API Integration Troubleshooting Guide

## Problem Analysis

The error "failed to register member with AG API" was caused by the AG API server not being available at `localhost:8081`.

## Root Cause

The AG API service (blacking-ag-api) is not running, resulting in "connection refused" errors when trying to call the API.

## Solution

### 1. Check AG API Server Status

Make sure the AG API server is running:
```bash
# Check if AG API is running on port 8081
curl http://localhost:8081/api/v1/health
```

### 2. Start AG API Server

Start the blacking-ag-api service before testing member registration.

### 3. Verify Configuration

Check your `.env` file has the correct AG API settings:
```bash
# AGENT Configuration
AGENT_BASE_URL=http://localhost:8081/api/v1
AGENT_SECRET_KEY=0a8128cc-8eba-49fd-bc39-392da791e3aa
AGENT_LINE_CODE=bk10
```

### 4. Test AG API Connectivity

Use the test tool to verify connectivity:
```bash
go build ./cmd/test-agapi
./test-agapi
```

## Improvements Made

### 1. Enhanced Error Handling
- Added detailed logging for AG API requests and responses
- Added connection status checking
- Improved error messages to distinguish between different failure types

### 2. Graceful Degradation
- Member registration now succeeds locally even if AG API is unavailable
- Connection failures are logged as warnings instead of errors
- No disruption to the main registration flow

### 3. Debug Tools
- Created test tool (`cmd/test-agapi/main.go`) for AG API debugging
- Added comprehensive logging for troubleshooting

## How It Works Now

1. **Normal Operation**: When AG API is available, members are registered both locally and with AG API
2. **Fallback Mode**: When AG API is unavailable, members are registered locally only with warning logs
3. **Error Logging**: All AG API interactions are logged with detailed information for debugging

## Error Types Handled

- `connection refused` - AG API server not running
- `dial tcp` - Network connectivity issues  
- `no such host` - DNS resolution problems
- `server is not available` - AG API server down

## Testing

Test both scenarios:

1. **AG API Available**: Start blacking-ag-api and test registration
2. **AG API Unavailable**: Stop blacking-ag-api and verify registration still works locally

## Monitoring

Check logs for AG API status:
- `INFO` level: Successful AG API registrations
- `WARN` level: AG API unavailable (fallback mode)
- `ERROR` level: AG API errors requiring attention