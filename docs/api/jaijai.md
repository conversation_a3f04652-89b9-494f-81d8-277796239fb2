# JaiJaiPay Client API Documentation

**Version:** 1.1.0  
**Last Updated:** August 26, 2025  
**Base URL:** `https://api.jaijaipay.com/api/v1`

## 🆕 Blacking API Integration

**Blacking API Base URL:** `/api/v1/jaijai/*`  
**Authentication:** JWT <PERSON> (Member Authentication)

### New Features in v1.1.0
- ✅ Webhook endpoint: `POST /api/v1/jaijai/webhook`
- ✅ All endpoints now use `/v1` versioning
- ✅ Integrated signature validation
- ✅ Comprehensive logging and monitoring

## Table of Contents

1. [Authentication](#authentication)
2. [Required Variables](#required-variables)
3. [Headers](#headers)
4. [Blacking API Endpoints](#blacking-api-endpoints)
5. [JaiJaiPay Direct API](#jaijaipay-direct-api)
    - [Deposits](#deposits)
    - [Withdrawals](#withdrawals)
    - [Account & Balance](#account--balance)
    - [Fees & Analytics](#fees--analytics)
    - [Webhooks](#webhooks)

---

## Authentication

JaiJaiPay API uses API Key authentication with HMAC-SHA256 signature for secure request verification.

### Generating Signature for POST Requests

```javascript
// Generate timestamp
const timestamp = Math.floor(Date.now() / 1000).toString();

// Get request body
const parseBody = (requestJSONBody) ? JSON.parse(requestBody) : '';

// Create payload for signature
const payload = timestamp + JSON.stringify(parseBody);

// Generate HMAC-SHA256 signature
const signature = CryptoJS.HmacSHA256(payload, secretKey).toString(CryptoJS.enc.Hex);
```

### Generating Signature for GET Requests

```javascript
// Generate timestamp
const timestamp = Math.floor(Date.now() / 1000).toString();

// Create payload for signature (timestamp only for GET)
const payload = timestamp;

// Generate HMAC-SHA256 signature
const signature = CryptoJS.HmacSHA256(payload, secretKey).toString(CryptoJS.enc.Hex);
```

---

## Required Variables

Set these environment variables:

- `base_url`: API base URL (e.g., https://api.jaijaipay.com/api/v1)
- `api_key`: Your API key
- `secret_key`: Your secret key

---

## Headers

All requests must include these headers:

- `x-api-key`: Your API key
- `x-signature`: HMAC-SHA256 signature
- `x-timestamp`: Unix timestamp
- `Content-Type`: `application/json` (for POST requests)

---

## Blacking API Endpoints

**Important:** Use these endpoints for frontend integration. All requests require JWT authentication.

### Base Configuration
```javascript
const config = {
  baseURL: 'https://your-api-domain.com',
  jaijaipayPrefix: '/api/v1/jaijai',
  authToken: 'Bearer YOUR_JWT_TOKEN'
};
```

### Deposits
- `POST /api/v1/jaijai/deposits` - Create deposit
- `GET /api/v1/jaijai/deposits` - List deposits
- `GET /api/v1/jaijai/deposits/{transactionId}` - Get deposit by ID
- `POST /api/v1/jaijai/deposits/cancel` - Cancel deposit

### Withdrawals
- `POST /api/v1/jaijai/withdrawals` - Create withdrawal
- `GET /api/v1/jaijai/withdrawals` - List withdrawals
- `GET /api/v1/jaijai/withdrawals/{transactionId}` - Get withdrawal by ID

### Balance & Fees
- `GET /api/v1/jaijai/balance` - Get account balance
- `GET /api/v1/jaijai/fees/preview` - Get fee preview

### Analytics
- `GET /api/v1/jaijai/analytics/transactions` - Get transaction analytics

### Webhooks (Internal)
- `POST /api/v1/jaijai/webhook` - **JaiJaiPay webhook endpoint (no auth)**
- `POST /api/v1/jaijai/webhooks/resend` - Resend webhook notification

### Example Usage
```javascript
// Create deposit
const response = await fetch('/api/v1/jaijai/deposits', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`
  },
  body: JSON.stringify({
    customerReference: 'user-123',
    amount: 100.00,
    currency: 'THB',
    assetType: 'FIAT',
    bankCode: 'KBANK',
    bankAccountNumber: '**********',
    accountHolderName: 'John Doe',
    description: 'Deposit',
    webhookUrl: 'https://your-api.com/api/v1/jaijai/webhook'
  })
});

const result = await response.json();
console.log('Deposit created:', result.transactionId);
```

---

## JaiJaiPay Direct API

**Note:** These are the direct JaiJaiPay API endpoints. Use Blacking API endpoints above for frontend integration.

### Deposits

#### Create Deposit

**Endpoint:** `POST /payments/client/deposits`

Creates a new deposit transaction with comprehensive details and webhook notifications.

**Request Body:**
```json
{
    "orderId": "ORDER-11334231223aa123a2334624",
    "customerReference": "user-********",
    "amount": 1,
    "currency": "THB",
    "assetType": "FIAT",
    "bankCode": "KBANK",
    "bankAccountNumber": "**********",
    "accountHolderName": "Montra Roma",
    "description": "Initial Transaction",
    "webhookUrl": "https://example.com/webhook",
    "metadata": {},
    "clientConfig": {
        "successUrl": "https://your-domain.com/payment/success",
        "failedUrl": "https://your-domain.com/payment/failed",
        "returnUrl": "https://your-domain.com/payment/return"
    }
}
```

**Response (201 Created):**
```json
{
    "transactionId": "2dc753f5-cf2b-41ff-a652-a53809a8fdce",
    "transactionReference": "DEP-*************-T1U6O0",
    "amount": "1.93",
    "originalAmount": "1.00",
    "status": "PENDING_PLATFORM_APPROVAL",
    "qrCode": "data:image/png;base64,iVBORw0KGgo...",
    "qrText": "00020101021229370016A000000677...",
    "qrImageUrl": "https://localhost:4000/api/v1/qr/2dc753f5-cf2b-41ff-a652-a53809a8fdce",
    "paymentUrl": "https://app.jaijaipay.com/pay/2dc753f5-cf2b-41ff-a652-a53809a8fdce",
    "expiresAt": "2025-08-23T14:56:12.374Z",
    "orderId": "ORDER-11334231223aa123a234624",
    "customerReference": "user-********",
    "bankCode": "KBANK",
    "bankAccountNumber": "**********",
    "accountHolderName": "Montra Roma",
    "currency": "THB",
    "assetType": "FIAT",
    "description": "Initial Transaction",
    "webhookUrl": "https://webhook-url.com",
    "merchantId": "b2ff3e9b-6f13-4162-a1d9-e8350743a4c3",
    "companyId": "a9cb3284-6c8b-48ef-94a3-6e52e1662418",
    "metadata": {},
    "createdAt": "2025-08-23T14:26:12.616Z",
    "qrPromptPay": {
        "qrCodeImage": "data:image/png;base64,iVBORw0KGgo...",
        "qrDataString": "00020101021229370016A000000677...",
        "businessRegistrationNumber": "*************",
        "metadata": {
            "size": 300,
            "format": "EMV QR Code PNG",
            "errorCorrectionLevel": "M",
            "logoIncluded": true,
            "dataLength": 82,
            "generatedAt": "2025-08-23T14:26:12.321Z"
        },
        "amountTracking": {
            "originalAmount": 1,
            "qrAmount": 1.93,
            "addedCents": 93,
            "precisionApplied": true,
            "currency": "THB"
        }
    }
}
```

#### List Deposits

**Endpoint:** `GET /payments/client/deposits`

Retrieves paginated list of deposit transactions with filtering and pagination support.

**Query Parameters:**
- `page`: Page number (starts from 1)
- `limit`: Number of records per page (max 100)
- `status`: Filter by transaction status
- `startDate`: Start date for date range filtering (ISO 8601)
- `endDate`: End date for date range filtering (ISO 8601)
- `currency`: Filter by specific currency
- `orderId`: Filter by specific order ID
- `customerReferenceId`: Filter by customer reference ID

**Example Request:** `GET /payments/client/deposits?page=1&currency=THB&limit=2`

**Response (200 OK):**
```json
{
    "data": [
        {
            "transactionId": "993468b8-4abc-4bda-944d-b4f6c8f4f01e",
            "transactionReference": "DEP-1755965837386",
            "amount": "500.04",
            "originalAmount": "500.00",
            "status": "COMPLETED",
            "currency": "THB",
            "createdAt": "2025-08-23T16:17:17.386Z",
            "completedAt": "2025-08-23T16:17:34.477Z"
        }
    ],
    "pagination": {
        "page": 1,
        "limit": 2,
        "total": 28,
        "totalPages": 14
    },
    "filters": {
        "companyId": "a9cb3284-6c8b-48ef-94a3-6e52e1662418",
        "merchantId": "b2ff3e9b-6f13-4162-a1d9-e8350743a4c3",
        "currency": "THB"
    }
}
```

#### Get Deposit by ID

**Endpoint:** `GET /payments/client/deposits/{transactionId}`

Retrieves detailed information about a specific deposit transaction.

**Path Parameters:**
- `transactionId`: Unique transaction identifier

**Response (200 OK):**
```json
{
    "transactionId": "2dc753f5-cf2b-41ff-a652-a53809a8fdce",
    "transactionReference": "DEP-*************",
    "amount": "1.93",
    "originalAmount": "1.00",
    "status": "COMPLETED",
    "currency": "THB",
    "createdAt": "2025-08-23T14:26:12.616Z",
    "qrPromptPay": {
        "qrCodeImage": "data:image/png;base64,iVBORw0KGgo...",
        "qrDataString": "00020101021229370016A000000677...",
        "amountTracking": {
            "originalAmount": 1,
            "qrAmount": 1.93,
            "addedCents": 93,
            "precisionApplied": true,
            "currency": "THB"
        }
    }
}
```

#### Cancel Deposit

**Endpoint:** `POST /payments/client/deposits/cancel`

Cancels a pending deposit transaction. Only transactions with pending status can be cancelled.

**Request Body:**
```json
{
    "transactionId": "26221e00-386d-461e-81ca-4d31118fed09"
}
```

**Response (201 Created):**
```json
{
    "transactionId": "26221e00-386d-461e-81ca-4d31118fed09",
    "status": "CANCELLED",
    "message": "Deposit cancelled successfully",
    "cancelledAt": "2025-08-23T18:16:52.746Z"
}
```

---

### Withdrawals

#### Create Withdrawal

**Endpoint:** `POST /payments/client/withdrawals`

Creates a new withdrawal transaction with automatic approval workflow.

**Request Body:**
```json
{
    "orderId": "ORDER-12312323423424113345624",
    "customerReference": "user12999",
    "amount": 20.00,
    "currency": "THB",
    "assetType": "FIAT",
    "bankCode": "KBANK",
    "bankAccountNumber": "**********",
    "accountHolderName": "Montra Roma",
    "description": "Test withdrawal from Enhanced Postman Collection",
    "webhookUrl": "https://example.com/webhook",
    "metadata": {
        "source": "postman-enhanced",
        "test": true,
        "version": "2.0.0"
    }
}
```

**Response (201 Created):**
```json
{
    "transactionId": "05f5d149-d8be-4411-b2d8-f1ba13a0a404",
    "transactionReference": "WDL-*************-CGUIGE",
    "amount": "20.00",
    "status": "PENDING_PLATFORM_APPROVAL",
    "bankCode": "KBANK",
    "bankAccountNumber": "**********",
    "accountHolderName": "Montra Roma",
    "createdAt": "2025-08-23T18:41:07.759Z",
    "orderId": "ORDER-12312323423424113345624",
    "customerReference": "user12999",
    "currency": "THB",
    "assetType": "FIAT",
    "percentageCommissionRate": "0.0000",
    "fixedCommissionAmount": "0.00",
    "totalCommissionAmount": "0.00"
}
```

#### List Withdrawals

**Endpoint:** `GET /payments/client/withdrawals`

Retrieves paginated list of withdrawal transactions.

**Query Parameters:**
- `page`: Page number (starts from 1)
- `limit`: Number of records per page
- `status`: Filter by transaction status
- `startDate`: Start date (ISO 8601)
- `endDate`: End date (ISO 8601)
- `currency`: Filter by currency
- `customerReferenceId`: Filter by customer reference

**Response (200 OK):**
```json
{
    "data": [
        {
            "transactionId": "05f5d149-d8be-4411-b2d8-f1ba13a0a404",
            "transactionReference": "WDL-*************",
            "amount": "20.00",
            "status": "PENDING_PLATFORM_APPROVAL",
            "createdAt": "2025-08-23T18:41:07.759Z",
            "currency": "THB"
        }
    ],
    "pagination": {
        "page": 1,
        "limit": 2,
        "total": 6,
        "totalPages": 3
    }
}
```

#### Get Withdrawal by ID

**Endpoint:** `GET /payments/client/withdrawals/{transactionId}`

Retrieves detailed information about a specific withdrawal transaction.

**Response (200 OK):**
```json
{
    "transactionId": "05f5d149-d8be-4411-b2d8-f1ba13a0a404",
    "transactionReference": "WDL-*************",
    "amount": "20.00",
    "status": "PENDING_PLATFORM_APPROVAL",
    "bankCode": "KBANK",
    "bankAccountNumber": "**********",
    "accountHolderName": "Montra Roma",
    "createdAt": "2025-08-23T18:41:07.759Z",
    "currency": "THB",
    "assetType": "FIAT",
    "percentageCommissionRate": "0.0000",
    "fixedCommissionAmount": "0.00",
    "totalCommissionAmount": "0.00"
}
```

---

### Account & Balance

#### Get Balance

**Endpoint:** `GET /payments/client/balance`

Retrieves current account balance and comprehensive financial summary for the authenticated merchant.

**Response (200 OK):**
```json
{
    "merchantId": "b2ff3e9b-6f13-4162-a1d9-e8350743a4c3",
    "companyId": "a9cb3284-6c8b-48ef-94a3-6e52e1662418",
    "results": [
        {
            "currency": "THB",
            "availableBalance": 58519.36,
            "deposit": {
                "fees": {
                    "percentage": 0,
                    "fixed": 0
                },
                "totalAmount": 59505.43,
                "totalFees": 575.63,
                "totalBalance": 58929.8
            },
            "withdrawal": {
                "fees": {
                    "percentage": 0,
                    "fixed": 0
                },
                "totalAmount": 400.44,
                "totalFees": 10,
                "totalBalance": 410.44
            }
        }
    ]
}
```

---

### Fees & Analytics

#### Get Fee Preview

**Endpoint:** `GET /payments/client/fees/preview`

Calculates estimated fees for a transaction before creation, including comprehensive fee breakdown and merchant impact analysis.

**Query Parameters:**
- `amount`: Transaction amount (required)
- `transactionType`: DEPOSIT or WITHDRAWAL (required)
- `currency`: Currency code (optional, defaults to THB)

**Example Request:** `GET /payments/client/fees/preview?amount=1000&transactionType=DEPOSIT&currency=THB`

**Response (200 OK):**
```json
{
    "amount": 1000,
    "currency": "THB",
    "transactionType": "DEPOSIT",
    "feeCalculation": {
        "percentageFeeRate": 0.025,
        "percentageFeeAmount": 0.25,
        "fixedFeeAmount": 0,
        "totalFeeAmount": 0.25,
        "feeBearer": "MERCHANT"
    },
    "amountBreakdown": {
        "customerPays": 1000,
        "merchantReceives": 999.75,
        "platformFee": 0.25
    },
    "estimatedProcessingTime": "Instant upon customer payment",
    "calculatedAt": "2025-08-23T18:47:32.363Z"
}
```

#### Get Transaction Analytics

**Endpoint:** `GET /payments/client/analytics/transactions`

Retrieves comprehensive transaction analytics and business insights for specified time period.

**Query Parameters:**
- `startDate`: Start date (YYYY-MM-DD, required)
- `endDate`: End date (YYYY-MM-DD, required)
- `currency`: Filter by currency (optional)
- `transactionType`: DEPOSIT or WITHDRAWAL (optional)
- `status`: Filter by transaction status (optional)

**Example Request:** `GET /payments/client/analytics/transactions?startDate=2025-08-01&endDate=2025-08-31&currency=THB`

**Response (200 OK):**
```json
{
    "reportPeriod": {
        "startDate": "2025-08-01",
        "endDate": "2025-08-31",
        "totalDays": 30
    },
    "companyId": "a9cb3284-6c8b-48ef-94a3-6e52e1662418",
    "merchantId": "b2ff3e9b-6f13-4162-a1d9-e8350743a4c3",
    "generatedAt": "2025-08-23T18:48:08.772Z",
    "summary": {
        "totalTransactions": 42,
        "totalVolume": 67344.4,
        "totalFees": 665.96,
        "netAmount": 66678.44,
        "averageTransactionSize": 1603.44,
        "uniqueCustomers": 8
    },
    "breakdown": {
        "deposits": {
            "count": 29,
            "totalAmount": 63521.86,
            "totalFees": 605.96,
            "averageAmount": 2190.41,
            "successRate": 37.93,
            "percentageOfTotal": 94.32
        },
        "withdrawals": {
            "count": 6,
            "totalAmount": 510.77,
            "totalFees": 60,
            "averageAmount": 85.13,
            "successRate": 16.67,
            "percentageOfTotal": 0.76
        }
    },
    "trends": {
        "volumeGrowth": 0,
        "transactionGrowth": 0,
        "averageTransactionSize": 1603.44,
        "feeGrowth": 0,
        "successRateChange": 35.71,
        "peakTransactionPeriod": "Afternoon (12PM-6PM)"
    },
    "performance": {
        "averageProcessingTime": 0.35,
        "fastestTransaction": 0.22,
        "slowestTransaction": 0.57,
        "onTimeCompletionRate": 100,
        "slaBreaches": 0
    },
    "profitability": {
        "totalRevenue": 64187.82,
        "totalCosts": 570.77,
        "grossProfit": 63677.05,
        "netProfit": 63617.05,
        "profitMarginPercentage": 99.11,
        "feeIncomePercentage": 1.04,
        "operationalEfficiency": 11145.83
    },
    "insights": {
        "peakHour": "16:00",
        "peakDay": "Saturday",
        "mostUsedCurrency": "THB",
        "largestTransaction": {
            "amount": "500.00",
            "transactionId": "993468b8-4abc-4bda-944d-b4f6c8f4f01e",
            "date": "2025-08-23"
        },
        "recommendedActions": [
            "Consider optimizing processing capacity during business hours",
            "Review and improve transaction success rate"
        ]
    }
}
```

---

### Webhooks

#### Resend Webhook

**Endpoint:** `POST /payments/client/webhooks/resend`

Resends webhook notifications for specific transactions. Useful for testing webhook endpoints or retrying failed deliveries.

**Important:** This API can only be used for transactions with status `COMPLETED`, `SLIP_COMPLETED`, or `FAILED`.

**Request Body:**
```json
{
    "transactionId": "2dc753f5-cf2b-41ff-a652-a53809a8fdce"
}
```

**Response (200 OK):**
```json
{
    "success": true,
    "message": "Callback resent successfully",
    "transactionId": "2dc753f5-cf2b-41ff-a652-a53809a8fdce",
    "resentAt": "2025-08-23T17:53:55.350Z",
    "warning": "WARNING: This webhook is for a COMPLETED transaction. Ensure your system handles duplicate notifications to prevent double-spending or double-processing.",
    "previousResendCount": 0
}
```

**Webhook Payload Example:**
```json
{
    "event": "transaction.webhook.resent",
    "transactionId": "2dc753f5-cf2b-41ff-a652-a53809a8fdce",
    "transactionReference": "2dc753f5-cf2b-41ff-a652-a53809a8fdce",
    "status": "COMPLETED",
    "amount": "1.00",
    "currency": "THB",
    "transactionType": "DEPOSIT",
    "companyId": "a9cb3284-6c8b-48ef-94a3-6e52e1662418",
    "merchantId": "b2ff3e9b-6f13-4162-a1d9-e8350743a4c3",
    "orderId": "ORDER-11334231223aa123a234624",
    "customerReference": "user-********",
    "metadata": {
        "webhookResent": true,
        "lastWebhookResentAt": "2025-08-23T17:53:47.091Z",
        "webhookResentCount": 1,
        "webhookResentBy": "api_key:d2277476-c9ae-45dd-b3c9-5695a7eb3136",
        "resendContext": {
            "isResent": true,
            "previousResendCount": 0,
            "resentBy": "api_key:d2277476-c9ae-45dd-b3c9-5695a7eb3136",
            "resentAt": "2025-08-23T17:53:47.818Z",
            "warning": "WARNING: This webhook is for a COMPLETED transaction. Ensure your system handles duplicate notifications to prevent double-spending or double-processing."
        }
    },
    "timestamp": "2025-08-23T17:53:47.945Z"
}
```

---

## Error Handling

The API uses standard HTTP status codes and returns detailed error information:

### Common Error Codes

- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Invalid or missing authentication
- `404 Not Found`: Resource not found
- `409 Conflict`: Duplicate transaction or resource conflict
- `500 Internal Server Error`: Server error

### Error Response Format

```json
{
    "errorCode": -1901,
    "message": "Failed to create deposit transaction",
    "details": {
        "operation": "createDeposit",
        "companyId": "a9cb3284-6c8b-48ef-94a3-6e52e1662418",
        "orderId": "ORDER-11334231223aa123a234624",
        "error": "Duplicate transaction detected - cannot retry with same orderId and customerReference."
    },
    "requestId": "req-7",
    "documentationUrl": "https://docs.jaijaipay.com/api/errors#error-1901",
    "timestamp": "2025-08-23T18:12:31.648Z"
}
```

---

## Rate Limits

- API requests are rate-limited per API key
- Default limits: 1000 requests per hour
- Webhook resend: Limited to prevent abuse

## Support

For technical support or API questions:
- Documentation: https://docs.jaijaipay.com
- Support: Contact your account manager

---

---

## Integration Resources

- [Frontend Integration Guide](./FRONTEND_INTEGRATION.md) - Complete guide for frontend developers
- [JaiJaiPay Integration Documentation](../JAIJAIPAY_INTEGRATION.md) - Backend integration details
- [Swagger API Documentation](../swagger.yaml) - OpenAPI specification

---

*This documentation covers both JaiJaiPay Direct API v2.0.0 and Blacking API Integration v1.1.0*