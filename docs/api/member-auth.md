# Member Authentication API

## Overview

The member authentication system provides JWT-based authentication for members with login, logout, and token refresh functionality.

## Authentication Endpoints

### 1. Member Login

Authenticate member and receive JWT token for accessing protected resources.

**Endpoint:** `POST /api/v1/member-auth/login`

#### Request Body

```json
{
  "username": "**********",
  "password": "12341234"
}
```

#### Request Parameters

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `username` | string | Yes | Member username (typically phone number) |
| `password` | string | Yes | Member password |

#### Response

**Success Response (200 OK):**

```json
{
  "status": "success",
  "message": "Member login successful",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expire": **********
}
```

**Error Response (401 Unauthorized):**

```json
{
  "code": 401,
  "message": "authentication failed"
}
```

**Error Response (401 Unauthorized - Account Disabled):**

```json
{
  "code": 401,
  "message": "member account is disabled"
}
```

**Error Response (400 Bad Request - Missing Fields):**

```json
{
  "code": 400,
  "message": "missing Username or Password"
}
```

### 2. Member Logout

Logout member and invalidate current JWT token.

**Endpoint:** `POST /api/v1/member-auth/logout`

#### Headers

```
Authorization: Bearer <jwt_token>
```

#### Response

**Success Response (200 OK):**

```json
{
  "status": "success",
  "message": "Member logout successful"
}
```

### 3. Refresh Token

Refresh JWT token to extend session without re-login.

**Endpoint:** `POST /api/v1/member-auth/refresh`

#### Headers

```
Authorization: Bearer <jwt_token>
```

#### Response

**Success Response (200 OK):**

```json
{
  "status": "success",
  "message": "Token refreshed successfully",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expire": **********
}
```

**Error Response (401 Unauthorized):**

```json
{
  "code": 401,
  "message": "token is expired"
}
```

## Token Information

- **Token Type:** JWT (JSON Web Token)
- **Token Duration:** 2 hours
- **Refresh Duration:** 2 hours
- **Token Location:** HTTP Cookie (`member_jwt`) and Authorization Header
- **Cookie Settings:**
  - Name: `member_jwt`
  - HttpOnly: `true`
  - SameSite: `Lax`

## cURL Examples

### Member Login

```bash
curl 'http://localhost:8080/api/v1/member-auth/login' \
  -H 'Content-Type: application/json' \
  --data-raw '{"username":"**********","password":"12341234"}'
```

### Member Logout

```bash
curl 'http://localhost:8080/api/v1/member-auth/logout' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -X POST
```

### Refresh Token

```bash
curl 'http://localhost:8080/api/v1/member-auth/refresh' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -X POST
```

## JWT Claims Structure

The JWT token contains the following claims:

```json
{
  "id": 123,
  "username": "**********",
  "is_enable": true,
  "login_time": **********,
  "exp": **********,
  "orig_iat": **********
}
```

| Field | Type | Description |
|-------|------|-------------|
| `id` | number | Member ID |
| `username` | string | Member username |
| `is_enable` | boolean | Member account status |
| `login_time` | number | Unix timestamp of login |
| `exp` | number | Token expiration time |
| `orig_iat` | number | Original token issue time |

## Authentication Flow

1. **Login** - Member provides username/password
2. **Validation** - System validates credentials against database
3. **Token Generation** - JWT token is generated and returned
4. **Protected Access** - Use token in Authorization header for protected endpoints
5. **Token Refresh** - Refresh token before expiration
6. **Logout** - Invalidate token when session ends

## Security Features

- **Password Validation** - Secure password verification
- **Account Status Check** - Disabled accounts cannot login
- **Token Expiration** - Short-lived tokens (2 hours)
- **HTTP-Only Cookies** - Secure cookie storage
- **Bearer Token Support** - Standard Authorization header
- **Automatic Refresh** - Token refresh capability

## Error Handling

All authentication errors return standard error format:

```json
{
  "code": <http_status_code>,
  "message": "<error_description>"
}
```

Common error scenarios:
- Invalid credentials (401)
- Missing username/password (400) 
- Account disabled (401)
- Token expired (401)
- Token invalid (401)

## Notes

- Member authentication is separate from admin user authentication
- Tokens are stored in HTTP-only cookies for web clients
- Authorization header is also supported for API clients
- Member accounts must have `is_enable` = `true` to login
- Username is typically the member's phone number
- Token refresh extends session without requiring re-login