# JaiJaiPay Frontend Integration Guide

**Version:** 1.1.0  
**Last Updated:** August 26, 2025  
**Target Audience:** Frontend Developers

This guide provides comprehensive instructions for integrating JaiJaiPay payment functionality into your frontend applications.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Authentication](#authentication)
3. [Deposit Flow](#deposit-flow)
4. [Withdrawal Flow](#withdrawal-flow)
5. [Payment Status Handling](#payment-status-handling)
6. [Webhook Integration](#webhook-integration)
7. [Error Handling](#error-handling)
8. [Best Practices](#best-practices)
9. [Code Examples](#code-examples)

---

## Quick Start

### Base Configuration

```javascript
const config = {
  apiBaseUrl: process.env.REACT_APP_API_BASE_URL, // Your API base URL
  jaijaipayEndpoint: '/api/v1/jaijai',
  authToken: null // Will be set after login
};
```

### API Helper Functions

```javascript
// API helper with authentication
class JaiJaiPayAPI {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.authToken = localStorage.getItem('authToken');
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.authToken}`,
      ...options.headers
    };

    const response = await fetch(url, {
      ...options,
      headers
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // Deposit methods
  async createDeposit(depositData) {
    return this.request('/api/v1/jaijai/deposits', {
      method: 'POST',
      body: JSON.stringify(depositData)
    });
  }

  async getDeposit(transactionId) {
    return this.request(`/api/v1/jaijai/deposits/${transactionId}`);
  }

  async listDeposits(params = {}) {
    const query = new URLSearchParams(params).toString();
    return this.request(`/api/v1/jaijai/deposits?${query}`);
  }

  async cancelDeposit(transactionId) {
    return this.request('/api/v1/jaijai/deposits/cancel', {
      method: 'POST',
      body: JSON.stringify({ transactionId })
    });
  }

  // Withdrawal methods
  async createWithdrawal(withdrawalData) {
    return this.request('/api/v1/jaijai/withdrawals', {
      method: 'POST',
      body: JSON.stringify(withdrawalData)
    });
  }

  async getWithdrawal(transactionId) {
    return this.request(`/api/v1/jaijai/withdrawals/${transactionId}`);
  }

  // Balance & Analytics
  async getBalance() {
    return this.request('/api/v1/jaijai/balance');
  }

  async getFeePreview(amount, transactionType, currency = 'THB') {
    const params = new URLSearchParams({
      amount: amount.toString(),
      transactionType,
      currency
    });
    return this.request(`/api/v1/jaijai/fees/preview?${params}`);
  }
}
```

---

## Authentication

All JaiJaiPay endpoints require JWT authentication obtained from your member login system.

```javascript
// Example login flow
async function loginAndSetToken(credentials) {
  try {
    const response = await fetch('/api/v1/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials)
    });
    
    const result = await response.json();
    const token = result.access_token;
    
    // Store token
    localStorage.setItem('authToken', token);
    
    // Initialize API client
    const jaijaipay = new JaiJaiPayAPI(process.env.REACT_APP_API_BASE_URL);
    return jaijaipay;
    
  } catch (error) {
    console.error('Login failed:', error);
    throw error;
  }
}
```

---

## Deposit Flow

### 1. Create Deposit Transaction

```javascript
// React component example
import React, { useState } from 'react';

function DepositForm({ jaijaipay, onSuccess }) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    amount: '',
    bankCode: 'KBANK',
    bankAccountNumber: '',
    accountHolderName: '',
    description: ''
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Get fee preview first
      const feePreview = await jaijaipay.getFeePreview(
        parseFloat(formData.amount), 
        'DEPOSIT'
      );

      // Show fee confirmation to user
      const confirmed = await showFeeConfirmation(feePreview);
      if (!confirmed) return;

      // Create deposit
      const depositData = {
        customerReference: getCurrentUserId(), // Your user ID system
        amount: parseFloat(formData.amount),
        currency: 'THB',
        assetType: 'FIAT',
        bankCode: formData.bankCode,
        bankAccountNumber: formData.bankAccountNumber,
        accountHolderName: formData.accountHolderName,
        description: formData.description,
        webhookUrl: `${process.env.REACT_APP_API_BASE_URL}/api/v1/jaijai/webhook`
      };

      const result = await jaijaipay.createDeposit(depositData);
      
      // Handle success
      onSuccess(result);
      
    } catch (error) {
      console.error('Deposit creation failed:', error);
      // Handle error (show to user)
    } finally {
      setLoading(false);
    }
  };

  // ... rest of component
}
```

### 2. Display QR Code for Payment

```javascript
function QRCodePayment({ depositResult }) {
  const [copied, setCopied] = useState(false);

  const handleCopyQR = () => {
    navigator.clipboard.writeText(depositResult.qrText);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="qr-payment">
      <h3>Scan QR Code to Pay</h3>
      
      {/* QR Code Image */}
      <div className="qr-code">
        <img 
          src={depositResult.qrCode} 
          alt="QR Code for Payment"
          style={{ maxWidth: '300px', height: 'auto' }}
        />
      </div>

      {/* Payment Details */}
      <div className="payment-details">
        <p><strong>Amount:</strong> ฿{depositResult.amount}</p>
        <p><strong>Original:</strong> ฿{depositResult.originalAmount}</p>
        <p><strong>Transaction ID:</strong> {depositResult.transactionId}</p>
        <p><strong>Expires:</strong> {new Date(depositResult.expiresAt).toLocaleString()}</p>
      </div>

      {/* Action Buttons */}
      <div className="actions">
        <button onClick={handleCopyQR}>
          {copied ? 'Copied!' : 'Copy QR Text'}
        </button>
        <a 
          href={depositResult.paymentUrl} 
          target="_blank" 
          rel="noopener noreferrer"
          className="payment-link-btn"
        >
          Open Payment Page
        </a>
      </div>

      {/* Bank Transfer Details */}
      <div className="bank-details">
        <h4>Bank Transfer Details</h4>
        <p><strong>Bank:</strong> {depositResult.bankCode}</p>
        <p><strong>Account:</strong> {depositResult.bankAccountNumber}</p>
        <p><strong>Name:</strong> {depositResult.accountHolderName}</p>
      </div>
    </div>
  );
}
```

---

## Withdrawal Flow

```javascript
function WithdrawalForm({ jaijaipay, onSuccess }) {
  const [formData, setFormData] = useState({
    amount: '',
    bankCode: 'KBANK',
    bankAccountNumber: '',
    accountHolderName: ''
  });

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      // Get fee preview
      const feePreview = await jaijaipay.getFeePreview(
        parseFloat(formData.amount), 
        'WITHDRAWAL'
      );

      // Show fee confirmation
      const confirmed = await showFeeConfirmation(feePreview);
      if (!confirmed) return;

      // Create withdrawal
      const withdrawalData = {
        customerReference: getCurrentUserId(),
        amount: parseFloat(formData.amount),
        currency: 'THB',
        assetType: 'FIAT',
        bankCode: formData.bankCode,
        bankAccountNumber: formData.bankAccountNumber,
        accountHolderName: formData.accountHolderName,
        description: 'Withdrawal request',
        webhookUrl: `${process.env.REACT_APP_API_BASE_URL}/api/v1/jaijai/webhook`
      };

      const result = await jaijaipay.createWithdrawal(withdrawalData);
      onSuccess(result);

    } catch (error) {
      console.error('Withdrawal creation failed:', error);
      // Handle error
    }
  };

  // ... rest of component
}
```

---

## Payment Status Handling

### Polling for Status Updates

```javascript
function useTransactionStatus(transactionId, type = 'deposit') {
  const [status, setStatus] = useState('PENDING');
  const [transaction, setTransaction] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!transactionId) return;

    const pollStatus = async () => {
      try {
        const jaijaipay = new JaiJaiPayAPI(process.env.REACT_APP_API_BASE_URL);
        
        let result;
        if (type === 'deposit') {
          result = await jaijaipay.getDeposit(transactionId);
        } else {
          result = await jaijaipay.getWithdrawal(transactionId);
        }

        setTransaction(result);
        setStatus(result.status);
        setLoading(false);

        // Stop polling if completed or failed
        if (['COMPLETED', 'CANCELLED', 'FAILED'].includes(result.status)) {
          return true; // Stop polling
        }

      } catch (error) {
        console.error('Status check failed:', error);
        setLoading(false);
      }
      return false; // Continue polling
    };

    // Initial check
    pollStatus();

    // Set up polling interval
    const interval = setInterval(async () => {
      const shouldStop = await pollStatus();
      if (shouldStop) {
        clearInterval(interval);
      }
    }, 5000); // Poll every 5 seconds

    return () => clearInterval(interval);
  }, [transactionId, type]);

  return { status, transaction, loading };
}

// Usage in component
function TransactionStatus({ transactionId, type }) {
  const { status, transaction, loading } = useTransactionStatus(transactionId, type);

  if (loading) return <div>Checking payment status...</div>;

  const getStatusColor = (status) => {
    switch (status) {
      case 'COMPLETED': return 'green';
      case 'FAILED': return 'red';
      case 'CANCELLED': return 'orange';
      default: return 'blue';
    }
  };

  const getStatusMessage = (status) => {
    switch (status) {
      case 'PENDING_PLATFORM_APPROVAL': return 'Waiting for payment...';
      case 'COMPLETED': return 'Payment completed successfully!';
      case 'FAILED': return 'Payment failed. Please try again.';
      case 'CANCELLED': return 'Payment was cancelled.';
      default: return `Status: ${status}`;
    }
  };

  return (
    <div className="transaction-status">
      <div 
        className="status-indicator" 
        style={{ color: getStatusColor(status) }}
      >
        {getStatusMessage(status)}
      </div>
      
      {transaction && (
        <div className="transaction-details">
          <p><strong>Amount:</strong> ฿{transaction.amount}</p>
          <p><strong>Transaction ID:</strong> {transaction.transactionId}</p>
          <p><strong>Reference:</strong> {transaction.transactionReference}</p>
        </div>
      )}

      {status === 'COMPLETED' && (
        <button onClick={() => window.location.reload()}>
          Refresh Balance
        </button>
      )}
    </div>
  );
}
```

---

## Webhook Integration

While webhooks are processed server-side, you can implement real-time updates using WebSockets or Server-Sent Events:

```javascript
// WebSocket connection for real-time updates
function useWebSocketUpdates(userId) {
  const [updates, setUpdates] = useState([]);

  useEffect(() => {
    const ws = new WebSocket(`${process.env.REACT_APP_WS_URL}/ws/user/${userId}`);

    ws.onmessage = (event) => {
      const update = JSON.parse(event.data);
      
      if (update.type === 'jaijaipay_webhook') {
        setUpdates(prev => [update, ...prev]);
        
        // Show notification
        if (update.data.event === 'transaction.completed') {
          showNotification('Payment completed successfully!', 'success');
        }
      }
    };

    return () => ws.close();
  }, [userId]);

  return updates;
}
```

---

## Error Handling

```javascript
// Comprehensive error handler
function handleJaiJaiPayError(error) {
  // Network errors
  if (!error.response) {
    return {
      title: 'Connection Error',
      message: 'Please check your internet connection and try again.',
      type: 'network'
    };
  }

  // HTTP errors
  const status = error.response.status;
  const data = error.response.data;

  switch (status) {
    case 400:
      return {
        title: 'Invalid Request',
        message: data.message || 'Please check your input and try again.',
        type: 'validation'
      };

    case 401:
      // Token expired - redirect to login
      localStorage.removeItem('authToken');
      window.location.href = '/login';
      return {
        title: 'Session Expired',
        message: 'Please log in again.',
        type: 'auth'
      };

    case 409:
      return {
        title: 'Duplicate Transaction',
        message: 'This transaction already exists. Please check your transaction history.',
        type: 'duplicate'
      };

    case 500:
      return {
        title: 'Server Error',
        message: 'Something went wrong on our end. Please try again later.',
        type: 'server'
      };

    default:
      return {
        title: 'Unknown Error',
        message: data.message || 'An unexpected error occurred.',
        type: 'unknown'
      };
  }
}

// Error display component
function ErrorMessage({ error, onRetry, onClose }) {
  if (!error) return null;

  return (
    <div className={`error-message error-${error.type}`}>
      <h4>{error.title}</h4>
      <p>{error.message}</p>
      <div className="error-actions">
        {onRetry && (
          <button onClick={onRetry} className="retry-btn">
            Try Again
          </button>
        )}
        <button onClick={onClose} className="close-btn">
          Close
        </button>
      </div>
    </div>
  );
}
```

---

## Best Practices

### 1. **Security**
- Always validate user input before sending to API
- Store JWT tokens securely (consider httpOnly cookies for production)
- Never expose sensitive data in client-side code
- Implement proper error handling without leaking internal details

### 2. **User Experience**
- Show loading states during API calls
- Provide clear error messages
- Implement proper form validation
- Show transaction progress and status updates

### 3. **Performance**
- Implement debouncing for fee preview calculations
- Cache balance and transaction data appropriately
- Use pagination for transaction lists
- Optimize QR code image display

### 4. **Monitoring**
- Log important user actions and errors
- Track transaction completion rates
- Monitor API response times
- Set up alerts for failed transactions

---

## Code Examples

### Complete Deposit Flow Component

```javascript
import React, { useState, useEffect } from 'react';
import { JaiJaiPayAPI } from './api/jaijaipay';

function DepositFlow() {
  const [step, setStep] = useState('form'); // form, qr, status
  const [depositResult, setDepositResult] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  const jaijaipay = new JaiJaiPayAPI(process.env.REACT_APP_API_BASE_URL);

  const handleDepositSubmit = async (formData) => {
    setLoading(true);
    setError(null);

    try {
      // Get fee preview
      const feePreview = await jaijaipay.getFeePreview(
        formData.amount, 
        'DEPOSIT'
      );

      // Confirm with user
      const confirmed = window.confirm(
        `Amount: ฿${formData.amount}\n` +
        `Fee: ฿${feePreview.feeCalculation.totalFeeAmount}\n` +
        `Total: ฿${feePreview.amountBreakdown.customerPays}\n\n` +
        'Proceed with deposit?'
      );

      if (!confirmed) return;

      // Create deposit
      const result = await jaijaipay.createDeposit({
        customerReference: getCurrentUserId(),
        amount: formData.amount,
        currency: 'THB',
        assetType: 'FIAT',
        bankCode: formData.bankCode,
        bankAccountNumber: formData.bankAccountNumber,
        accountHolderName: formData.accountHolderName,
        description: formData.description || 'Deposit',
        webhookUrl: `${process.env.REACT_APP_API_BASE_URL}/api/v1/jaijai/webhook`
      });

      setDepositResult(result);
      setStep('qr');

    } catch (error) {
      setError(handleJaiJaiPayError(error));
    } finally {
      setLoading(false);
    }
  };

  const renderStep = () => {
    switch (step) {
      case 'form':
        return (
          <DepositForm 
            onSubmit={handleDepositSubmit}
            loading={loading}
            error={error}
          />
        );
      
      case 'qr':
        return (
          <QRCodePayment 
            depositResult={depositResult}
            onStatusChange={() => setStep('status')}
          />
        );
      
      case 'status':
        return (
          <TransactionStatus 
            transactionId={depositResult.transactionId}
            type="deposit"
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="deposit-flow">
      <div className="progress-steps">
        <div className={step === 'form' ? 'active' : 'completed'}>1. Details</div>
        <div className={step === 'qr' ? 'active' : step === 'status' ? 'completed' : ''}>2. Payment</div>
        <div className={step === 'status' ? 'active' : ''}>3. Confirmation</div>
      </div>
      
      {renderStep()}
    </div>
  );
}

export default DepositFlow;
```

---

## Summary

This integration guide provides everything you need to implement JaiJaiPay functionality in your frontend application:

- ✅ **Complete API wrapper** with error handling
- ✅ **Deposit flow** with QR code display
- ✅ **Withdrawal flow** with fee preview
- ✅ **Real-time status updates** via polling or WebSockets
- ✅ **Comprehensive error handling**
- ✅ **Security best practices**
- ✅ **Performance optimizations**

For additional support or questions, refer to the main [JaiJaiPay Integration Documentation](../JAIJAIPAY_INTEGRATION.md).

---

**Last Updated:** August 26, 2025  
**Version:** 1.1.0  
**Status:** ✅ Ready for Production