# JaiJaiPay Webhook Integration Guide

**Version:** 1.1.0  
**Last Updated:** August 26, 2025  
**Endpoint:** `POST /api/v1/jaijai/webhook`

## Overview

JaiJaiPay webhooks provide real-time notifications about transaction status changes. This endpoint receives webhook calls from JaiJaiPay and processes them securely with signature validation.

## Webhook Configuration

### JaiJaiPay Dashboard Setup
Configure your webhook URL in the JaiJaiPay merchant dashboard:
```
https://your-api-domain.com/api/v1/jaijai/webhook
```

### Required Headers
JaiJaiPay will send these headers with each webhook:
```
X-JaiJaiPay-Signature: <hmac-sha256-signature>
Content-Type: application/json
User-Agent: JaiJaiPay-Webhook/1.0
```

## Webhook Events

JaiJaiPay sends webhooks for these transaction events:

### 1. Transaction Completed
**Event:** `transaction.completed`
**Description:** Payment has been successfully processed and confirmed

### 2. Transaction Cancelled  
**Event:** `transaction.cancelled`
**Description:** Transaction was cancelled by user or system

### 3. Transaction Failed
**Event:** `transaction.failed`
**Description:** Payment failed due to insufficient funds or technical issues

### 4. Transaction Pending
**Event:** `transaction.pending`
**Description:** Transaction status changed to pending (waiting for approval)

## Webhook Payload Structure

```json
{
  "event": "transaction.completed",
  "transactionId": "616faf33-0ccb-44c4-a54c-256455d2753d",
  "transactionReference": "DEP-1756205111581-Q0ON6D",
  "status": "COMPLETED",
  "amount": "1.15",
  "currency": "THB",
  "transactionType": "DEPOSIT",
  "companyId": "4503945d-b9b5-4a1a-8392-d4b088492362",
  "merchantId": "0cb52394-3b3a-4e44-ae56-408bd6086419",
  "orderId": "DEP2051106589",
  "customerReference": "2",
  "metadata": {},
  "timestamp": "2025-08-26T11:15:11.571Z"
}
```

### Payload Fields

| Field | Type | Description |
|-------|------|-------------|
| `event` | string | Type of event (see [Webhook Events](#webhook-events)) |
| `transactionId` | string | Unique JaiJaiPay transaction identifier |
| `transactionReference` | string | Human-readable transaction reference |
| `status` | string | Current transaction status |
| `amount` | string | Final transaction amount |
| `currency` | string | Currency code (e.g., "THB") |
| `transactionType` | string | "DEPOSIT" or "WITHDRAWAL" |
| `companyId` | string | JaiJaiPay company identifier |
| `merchantId` | string | JaiJaiPay merchant identifier |
| `orderId` | string | Your original order ID |
| `customerReference` | string | Your original customer reference |
| `metadata` | object | Additional data (can be null/empty) |
| `timestamp` | string | ISO 8601 timestamp of the event |

## Signature Validation

All webhooks are signed with HMAC-SHA256 for security verification.

### Validation Process

1. **Extract Signature**: Get signature from `X-JaiJaiPay-Signature` header
2. **Generate Expected**: Create HMAC-SHA256 hash of raw request body using your secret key
3. **Compare**: Use constant-time comparison to verify signatures match

### Example Validation (Node.js)

```javascript
const crypto = require('crypto');

function validateWebhookSignature(payload, signature, secretKey) {
  const expectedSignature = crypto
    .createHmac('sha256', secretKey)
    .update(payload)
    .digest('hex');
    
  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
}

// Usage
const isValid = validateWebhookSignature(
  requestBody,        // Raw request body as string
  receivedSignature,  // From X-JaiJaiPay-Signature header
  process.env.JAIJAIPAY_SECRET_KEY
);
```

## Response Requirements

### Success Response
Return HTTP 200 with JSON response:
```json
{
  "success": true,
  "message": "Webhook processed successfully",
  "transaction_id": "616faf33-0ccb-44c4-a54c-256455d2753d",
  "event": "transaction.completed"
}
```

### Error Response
For validation or processing errors:
```json
{
  "error": "Invalid webhook signature"
}
```
**HTTP Status:** 400 Bad Request

## Implementation Example

### Complete Webhook Handler

```javascript
// Express.js example
app.post('/api/v1/jaijai/webhook', async (req, res) => {
  try {
    // Get signature from header
    const signature = req.headers['x-jaijaipay-signature'];
    if (!signature) {
      return res.status(400).json({ error: 'Missing webhook signature' });
    }

    // Get raw body for signature validation
    const rawBody = JSON.stringify(req.body);
    
    // Validate signature
    const isValid = validateWebhookSignature(
      rawBody, 
      signature, 
      process.env.JAIJAIPAY_SECRET_KEY
    );
    
    if (!isValid) {
      console.error('Invalid webhook signature');
      return res.status(400).json({ error: 'Invalid webhook signature' });
    }

    // Parse payload
    const payload = req.body;
    
    console.log(`Received webhook: ${payload.event} for transaction ${payload.transactionId}`);

    // Process based on event type
    switch (payload.event) {
      case 'transaction.completed':
        await handleTransactionCompleted(payload);
        break;
        
      case 'transaction.cancelled':
        await handleTransactionCancelled(payload);
        break;
        
      case 'transaction.failed':
        await handleTransactionFailed(payload);
        break;
        
      case 'transaction.pending':
        await handleTransactionPending(payload);
        break;
        
      default:
        console.log(`Unhandled webhook event: ${payload.event}`);
    }

    // Return success response
    res.json({
      success: true,
      message: 'Webhook processed successfully',
      transaction_id: payload.transactionId,
      event: payload.event
    });

  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).json({
      error: 'Failed to process webhook',
      message: error.message
    });
  }
});

// Event handlers
async function handleTransactionCompleted(payload) {
  // Update user balance
  // Send notification to user
  // Update transaction status in database
  // Trigger any business logic
  
  console.log(`Transaction ${payload.transactionId} completed: ฿${payload.amount}`);
}

async function handleTransactionCancelled(payload) {
  // Update transaction status
  // Notify user of cancellation
  // Release any reserved funds
  
  console.log(`Transaction ${payload.transactionId} was cancelled`);
}

async function handleTransactionFailed(payload) {
  // Update transaction status  
  // Notify user of failure
  // Log for investigation
  
  console.log(`Transaction ${payload.transactionId} failed`);
}

async function handleTransactionPending(payload) {
  // Update status to pending
  // May need to wait for approval
  
  console.log(`Transaction ${payload.transactionId} is pending`);
}
```

## Best Practices

### 1. **Security**
- ✅ Always validate webhook signatures
- ✅ Use HTTPS endpoints only
- ✅ Store secret keys securely
- ✅ Log all webhook attempts for audit

### 2. **Reliability**  
- ✅ Return HTTP 200 for successful processing
- ✅ Implement idempotency (handle duplicate webhooks)
- ✅ Add retry logic with exponential backoff
- ✅ Use queues for time-consuming processing

### 3. **Monitoring**
- ✅ Log all webhook events
- ✅ Monitor webhook processing times
- ✅ Set up alerts for failed webhooks
- ✅ Track signature validation failures

### 4. **Error Handling**
- ✅ Handle malformed payloads gracefully
- ✅ Validate all required fields
- ✅ Return appropriate error codes
- ✅ Don't expose internal errors to JaiJaiPay

## Webhook Retry Policy

JaiJaiPay will retry failed webhooks with this policy:

1. **Immediate retry** - If webhook fails
2. **1 minute** - Second attempt  
3. **5 minutes** - Third attempt
4. **15 minutes** - Fourth attempt
5. **1 hour** - Fifth attempt
6. **6 hours** - Final attempt

**Total attempts:** 6  
**Max retry window:** 24 hours

### Retry Indicators
Webhooks include retry information in metadata:
```json
{
  "metadata": {
    "retryAttempt": 2,
    "isRetry": true,
    "originalTimestamp": "2025-08-26T10:45:11.571Z"
  }
}
```

## Testing Webhooks

### 1. **Webhook Resend API**
Use the resend API to test your webhook endpoint:

```bash
curl -X POST http://localhost:8080/api/v1/jaijai/webhooks/resend \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"transactionId": "616faf33-0ccb-44c4-a54c-256455d2753d"}'
```

### 2. **Local Testing with ngrok**
```bash
# Install ngrok
npm install -g ngrok

# Expose local server
ngrok http 3000

# Use ngrok URL in JaiJaiPay dashboard
# https://abc123.ngrok.io/api/v1/jaijai/webhook
```

### 3. **Mock Webhook for Development**
```javascript
// Test webhook locally
const mockWebhook = {
  event: 'transaction.completed',
  transactionId: 'test-transaction-123',
  transactionReference: 'DEP-TEST-123',
  status: 'COMPLETED',
  amount: '100.00',
  currency: 'THB',
  transactionType: 'DEPOSIT',
  orderId: 'ORDER-TEST-123',
  customerReference: 'user-test-456',
  timestamp: new Date().toISOString()
};

// Send to your local webhook
fetch('http://localhost:3000/api/v1/jaijai/webhook', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-JaiJaiPay-Signature': 'test-signature'
  },
  body: JSON.stringify(mockWebhook)
});
```

## Debugging Webhooks

### Common Issues

#### 1. **Invalid Signature**
```
Error: Invalid webhook signature
```
**Solutions:**
- Verify secret key is correct
- Check signature generation logic
- Ensure raw body is used for validation

#### 2. **Timeout Errors**
```
Error: Webhook timeout
```
**Solutions:**
- Optimize webhook processing time
- Use background jobs for heavy processing
- Return HTTP 200 quickly, process asynchronously

#### 3. **Missing Headers**
```
Error: Missing webhook signature
```
**Solutions:**
- Check JaiJaiPay configuration
- Verify endpoint URL is correct
- Ensure webhook is enabled in dashboard

### Debug Logging

```javascript
// Enhanced logging for debugging
app.post('/api/v1/jaijai/webhook', (req, res) => {
  console.log('=== Webhook Debug Info ===');
  console.log('Headers:', req.headers);
  console.log('Body:', req.body);
  console.log('Signature:', req.headers['x-jaijaipay-signature']);
  console.log('Timestamp:', new Date().toISOString());
  console.log('========================');
  
  // ... process webhook
});
```

## Production Checklist

Before deploying webhook handler to production:

- [ ] Webhook signature validation implemented
- [ ] HTTPS endpoint configured
- [ ] Error handling and logging in place
- [ ] Idempotency checks implemented
- [ ] Database transactions for atomic updates
- [ ] Monitoring and alerting configured
- [ ] Load testing completed
- [ ] Backup webhook endpoint configured
- [ ] Documentation for operations team
- [ ] Emergency contact information updated

## Support

### Getting Help
- **Technical Issues:** Check logs for specific error messages
- **Integration Questions:** Refer to [JaiJaiPay Integration Documentation](../JAIJAIPAY_INTEGRATION.md)
- **Frontend Integration:** See [Frontend Integration Guide](./FRONTEND_INTEGRATION.md)

### Monitoring Webhook Health
Monitor these metrics:
- Webhook processing time
- Success/failure rates  
- Signature validation failures
- Queue depths (if using async processing)

---

**Last Updated:** August 26, 2025  
**Version:** 1.1.0  
**Status:** ✅ Production Ready