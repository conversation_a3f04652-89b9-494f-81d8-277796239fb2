# Member Registration API

## Overview

The member registration system supports a 3-step process for secure user registration:

1. **Send OTP** - Send verification code to phone number
2. **Verify OTP** - Verify the received code  
3. **Complete Registration** - Complete registration with full user data

> **Note:** OTP validation is currently disabled for testing purposes.

## 1. Send Registration OTP

Send OTP verification code to phone number for member registration.

**Endpoint:** `POST /api/v1/member-auth/register/send-otp`

### Request Body

```json
{
  "phone": "**********"
}
```

### Response

**Success Response (200 OK):**

```json
{
  "success": true,
  "data": {
    "reference": "ABC123",
    "message": "OTP sent successfully",
    "expires_at": "2025-01-15T10:30:00Z"
  },
  "message": "OTP sent successfully"
}
```

## 2. Verify Registration OTP

Verify the OTP code sent to phone number during registration.

**Endpoint:** `POST /api/v1/member-auth/register/verify-otp`

### Request Body

```json
{
  "phone": "**********",
  "code": "123456"
}
```

### Response

**Success Response (201 Created):**

```json
{
  "success": true,
  "message": "OTP verified successfully"
}
```

## 3. Complete Registration

Complete member registration with full user data.

**Endpoint:** `POST /api/v1/member-auth/register/complete`

### Request Body

```json
{
  "phone": "string",
  "username": "string",
  "password": "string",
  "gender": "string",
  "bank_code": "string",
  "bank_number": "string",
  "register_refer_code": "string",
  "accept_terms": true
}
```

### Request Parameters

| Field | Type | Required | Validation | Description |
|-------|------|----------|------------|-------------|
| `phone` | string | Yes | - | Phone number |
| `username` | string | Yes | min: 3, max: 50 | Username for the account |
| `password` | string | Yes | min: 6 | Account password |
| `gender` | string | No | one of: male, female, other | User gender |
| `bank_code` | string | No | max: 50 | Bank code |
| `bank_number` | string | No | max: 50 | Bank account number |
| `register_refer_code` | string | No | length: 8 | Referral code (optional) |
| `accept_terms` | boolean | Yes | - | Must be true to accept terms |

### Response

**Success Response (201 Created):**

```json
{
  "success": true,
  "data": {
    "id": 123,
    "username": "testuser",
    "game_username": null,
    "first_name": null,
    "last_name": null,
    "phone": "**********",
    "gender": "male",
    "tw_username": null,
    "line_id": null,
    "bank_code": "SCB",
    "bank_number": "**********",
    "avatar": null,
    "login_status": true,
    "operate_status": true,
    "register_status": true,
    "balance": 0.00,
    "refer_user_id": null,
    "refer_code": "ABC12345",
    "register_refer_code": "DEF67890",
    "last_online": null
  },
  "message": "Registration completed successfully"
}
```

**Error Response (400 Bad Request):**

```json
{
  "success": false,
  "error": "Validation failed",
  "message": "invalid request body"
}
```

## Error Responses

**400 Bad Request - Validation Error:**

```json
{
  "success": false,
  "error": "VALIDATION_ERROR",
  "message": "phone already exists"
}
```

**400 Bad Request - Invalid OTP (when OTP is enabled):**

```json
{
  "success": false,
  "error": "NOT_FOUND", 
  "message": "no valid OTP found"
}
```

**500 Internal Server Error:**

```json
{
  "success": false,
  "error": "INTERNAL_ERROR",
  "message": "Internal server error"
}
```

## cURL Examples

### Complete Flow (when OTP is enabled):

```bash
# Step 1: Send OTP
curl 'http://localhost:8080/api/v1/member-auth/register/send-otp' \
  -H 'Content-Type: application/json' \
  --data-raw '{"phone":"**********"}'

# Step 2: Verify OTP (replace 123456 with actual OTP code)
curl 'http://localhost:8080/api/v1/member-auth/register/verify-otp' \
  -H 'Content-Type: application/json' \
  --data-raw '{"phone":"**********","code":"123456"}'

# Step 3: Complete registration
curl 'http://localhost:8080/api/v1/member-auth/register/complete' \
  -H 'Content-Type: application/json' \
  --data-raw '{"phone":"**********","username":"**********","password":"********","gender":"male","bank_code":"004","bank_number":"********12","accept_terms":true}'
```

### Direct Registration (OTP disabled for testing):

```bash
# Direct complete registration (OTP validation disabled)
curl 'http://localhost:8080/api/v1/member-auth/register/complete' \
  -H 'Content-Type: application/json' \
  --data-raw '{"phone":"**********","username":"**********","password":"********","gender":"male","bank_code":"004","bank_number":"********12","accept_terms":true}'
```

## Notes

- **OTP validation is currently disabled for testing purposes**
- The client IP address is automatically captured and logged
- Phone number is used as the actual username internally  
- The username field is validated but phone is used for login
- All validations are performed server-side
- Registration creates a new member with default settings
- Bank code and bank number are required fields
- Password must be at least 6 characters long
- Gender must be one of: "male", "female", "other"
- `accept_terms` must be `true` to proceed