# Payment Gateway API Integration Guide

This document provides comprehensive integration guidance for the Payment Gateway API system, which replaces the hardcoded JaiJaiPay configuration with a flexible, database-driven approach.

## Overview

The Payment Gateway API allows you to:
- Manage multiple payment gateway providers dynamically
- Configure JaiJaiPay settings through database instead of environment variables
- Switch between different payment gateways without code changes
- Support future payment gateway integrations

**Base URL:** `http://localhost:8080/api/v1/payment-gateway`

---

## Authentication

All Payment Gateway API endpoints require JWT authentication. Include the JWT token in the Authorization header:

```http
Authorization: Bearer <your-jwt-token>
```

---

## API Endpoints Reference

### Provider Management

#### 1. Get All Payment Gateway Providers

**GET** `/payment-gateway/providers`

Get a list of all configured payment gateway providers.

**Request:**
```http
GET /api/v1/payment-gateway/providers HTTP/1.1
Host: localhost:8080
Authorization: Bearer <jwt-token>
Content-Type: application/json
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "code": "JAIJAI_MAIN",
      "provider": "jaijaipay",
      "name": "JaiJaiPay Main Account",
      "active": true,
      "deposit": true,
      "withdraw": true,
      "transfer": false
    },
    {
      "id": 2,
      "code": "JAIJAI_BACKUP",
      "provider": "jaijaipay", 
      "name": "JaiJaiPay Backup Account",
      "active": false,
      "deposit": true,
      "withdraw": false,
      "transfer": false
    }
  ]
}
```

**Response Fields:**
- `id`: Provider unique identifier
- `code`: Provider code for internal reference
- `provider`: Provider type (jaijaipay, linepay, etc.)
- `name`: Display name
- `active`: Whether provider is currently active
- `deposit`: Supports deposit transactions
- `withdraw`: Supports withdraw transactions
- `transfer`: Supports transfer transactions

---

#### 2. Get Active Payment Gateway Providers

**GET** `/payment-gateway/providers/active`

Get only active payment gateway providers.

**Request:**
```http
GET /api/v1/payment-gateway/providers/active HTTP/1.1
Host: localhost:8080
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "code": "JAIJAI_MAIN",
      "provider": "jaijaipay",
      "name": "JaiJaiPay Main Account",
      "active": true,
      "deposit": true,
      "withdraw": true,
      "transfer": false
    }
  ]
}
```

---

#### 3. Create Payment Gateway Provider

**POST** `/payment-gateway/providers`

Create a new payment gateway provider configuration.

**Request:**
```http
POST /api/v1/payment-gateway/providers HTTP/1.1
Host: localhost:8080
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "accountName": "JaiJaiPay Production Account",
  "code": "JAIJAI_PROD_001",
  "provider": "jaijaipay",
  "merchantCode": "MERCHANT123",
  "secretKey": "your-secret-key-here",
  "apiKey": "your-api-key-here",
  "baseUrl": "https://api.jaijaipay.com/api/v1",
  "timeoutSeconds": 30,
  "maxRetries": 3,
  "retryDelaySeconds": 1,
  "enableRequestLog": true,
  "logResponseBody": false,
  "enableDebug": false,
  "minimumWithdraw": 100.0,
  "maximumWithdraw": 500000.0,
  "withdrawSplit": false,
  "maximumWithdrawPerTransaction": 50000.0,
  "maximumSplitWithdrawPerTransaction": 25000.0,
  "isDeposit": true,
  "isWithdraw": true,
  "isTransfer": false
}
```

**Request Fields:**
- `accountName`: Display name for the account *(required)*
- `code`: Unique identifier code *(required)*
- `provider`: Provider type (currently supports "jaijaipay") *(required)*
- `merchantCode`: Merchant code from provider *(required)*
- `secretKey`: Secret key from provider *(required)*
- `apiKey`: API key from provider *(optional, nullable)*
- `baseUrl`: Provider API base URL *(required for JaiJaiPay)*
- `timeoutSeconds`: Request timeout (5-300 seconds) *(optional, default: 30)*
- `maxRetries`: Maximum retry attempts (0-10) *(optional, default: 3)*
- `retryDelaySeconds`: Delay between retries (1-60 seconds) *(optional, default: 1)*
- `enableRequestLog`: Log API requests *(optional, default: true)*
- `logResponseBody`: Log response bodies (security risk) *(optional, default: false)*
- `enableDebug`: Enable debug mode *(optional, default: false)*
- `minimumWithdraw`: Minimum withdrawal amount *(optional)*
- `maximumWithdraw`: Maximum withdrawal amount *(optional)*
- `withdrawSplit`: Enable withdrawal splitting *(optional, default: false)*
- `maximumWithdrawPerTransaction`: Max per transaction *(optional)*
- `maximumSplitWithdrawPerTransaction`: Max per split transaction *(optional)*
- `isDeposit`: Support deposits *(required)*
- `isWithdraw`: Support withdrawals *(required)*
- `isTransfer`: Support transfers *(required)*

**Response:**
```json
{
  "success": true,
  "message": "Provider created successfully",
  "data": {
    "id": 3,
    "code": "JAIJAI_PROD_001",
    "provider": "jaijaipay",
    "name": "JaiJaiPay Production Account",
    "active": false
  }
}
```

**Error Response:**
```json
{
  "success": false,
  "message": "payment gateway with this code already exists"
}
```

---

#### 4. Update Provider Status

**PUT** `/payment-gateway/providers/{id}/status`

Update the active status of a payment gateway provider.

**Request:**
```http
PUT /api/v1/payment-gateway/providers/1/status HTTP/1.1
Host: localhost:8080
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "active": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Provider status updated successfully"
}
```

**Error Response:**
```json
{
  "success": false,
  "message": "Payment gateway provider not found"
}
```

---

### JaiJaiPay Operations

#### 1. Test JaiJaiPay Connection

**POST** `/payment-gateway/jaijaipay/test`

Test connection to JaiJaiPay using database configuration.

**Request:**
```http
POST /api/v1/payment-gateway/jaijaipay/test HTTP/1.1
Host: localhost:8080
Authorization: Bearer <jwt-token>
Content-Type: application/json
```

**Response:**
```json
{
  "success": true,
  "message": "JaiJaiPay client created successfully from database configuration",
  "data": {
    "baseUrl": "https://api.jaijaipay.com/api/v1",
    "configured": true,
    "provider": "jaijaipay",
    "description": "Connected using database configuration instead of environment variables"
  }
}
```

**Error Response:**
```json
{
  "success": false,
  "message": "No active JaiJaiPay configuration found"
}
```

---

#### 2. Get JaiJaiPay Balance

**GET** `/payment-gateway/jaijaipay/balance`

Get current balance from JaiJaiPay using database configuration.

**Request:**
```http
GET /api/v1/payment-gateway/jaijaipay/balance HTTP/1.1
Host: localhost:8080
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "success": true,
  "message": "Balance retrieved successfully",
  "data": {
    "balance": 25000.50,
    "currency": "THB",
    "availableBalance": 24500.50,
    "reservedBalance": 500.00,
    "lastUpdated": "2025-08-26T10:30:00Z"
  },
  "source": "Database configuration"
}
```

---

#### 3. Create JaiJaiPay Deposit

**POST** `/payment-gateway/jaijaipay/deposits`

Create a deposit transaction using JaiJaiPay via database configuration.

**Request:**
```http
POST /api/v1/payment-gateway/jaijaipay/deposits HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "amount": 1000.00,
  "currency": "THB",
  "memberUsername": "member001",
  "description": "Deposit from member001",
  "callbackUrl": "https://yoursite.com/callback/deposit",
  "returnUrl": "https://yoursite.com/return/deposit"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Deposit request processed via database configuration",
  "data": {
    "transactionId": "TXN202508260001",
    "amount": 1000.00,
    "currency": "THB",
    "status": "pending",
    "paymentUrl": "https://payment.jaijaipay.com/deposit/TXN202508260001",
    "expiresAt": "2025-08-26T11:30:00Z"
  },
  "source": "Database configuration (JaiJaiPay gateway)"
}
```

---

#### 4. Resend JaiJaiPay Webhook

**POST** `/payment-gateway/jaijaipay/webhooks/resend`

Resend webhook notification for a specific transaction using database configuration.

**Request:**
```http
POST /api/v1/payment-gateway/jaijaipay/webhooks/resend HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "transactionId": "061f735f-1a75-4141-897b-e9f189b0f1f2"
}
```

**Response (Success):**
```json
{
  "success": true,
  "message": "Webhook resent successfully",
  "data": {
    "event": "transaction.webhook.resent",
    "transactionId": "2dc753f5-cf2b-41ff-a652-a53809a8fdce",
    "transactionReference": "2dc753f5-cf2b-41ff-a652-a53809a8fdce",
    "status": "COMPLETED",
    "amount": "1.00",
    "currency": "THB",
    "transactionType": "DEPOSIT",
    "companyId": "a9cb3284-6c8b-48ef-94a3-6e52e1662418",
    "merchantId": "b2ff3e9b-6f13-4162-a1d9-e8350743a4c3",
    "orderId": "ORDER-11334231223aa123a234624",
    "customerReference": "user-01244322",
    "metadata": {
      "webhookResent": true,
      "lastWebhookResentAt": "2025-08-23T17:53:47.091Z",
      "webhookResentCount": 1,
      "webhookResentBy": "api_key:d2277476-c9ae-45dd-b3c9-5695a7eb3136",
      "resendContext": {
        "isResent": true,
        "previousResendCount": 0,
        "resentBy": "api_key:d2277476-c9ae-45dd-b3c9-5695a7eb3136",
        "resentAt": "2025-08-23T17:53:47.818Z",
        "warning": "WARNING: This webhook is for a COMPLETED transaction. Ensure your system handles duplicate notifications to prevent double-spending or double-processing."
      }
    },
    "timestamp": "2025-08-23T17:53:47.945Z"
  },
  "source": "Database configuration (JaiJaiPay gateway)"
}
```

**Response (Error - Transaction Not Found):**
```json
{
  "success": false,
  "message": "Failed to resend webhook",
  "error": "API error -1301: Transaction 061f735f-1a75-4141-897b-e9f189b0f1f2 not found not found. (request_id: req-1o2)"
}
```

**Response (Error - No Configuration):**
```json
{
  "success": false,
  "message": "No active JaiJaiPay configuration found"
}

**Response:**
```json
{
  "success": true,
  "message": "Deposit request processed via database configuration",
  "data": {
    "transactionId": "TXN202508260001",
    "amount": 1000.00,
    "currency": "THB",
    "status": "pending",
    "paymentUrl": "https://payment.jaijaipay.com/deposit/TXN202508260001",
    "expiresAt": "2025-08-26T11:30:00Z"
  },
  "source": "Database configuration (JaiJaiPay gateway)"
}
```

---

## Integration Examples

### 1. Setting up JaiJaiPay

```javascript
// 1. Create JaiJaiPay provider
const createProvider = async () => {
  const response = await fetch('/api/v1/payment-gateway/providers', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      accountName: "JaiJaiPay Main",
      code: "JAIJAI_MAIN",
      provider: "jaijaipay",
      merchantCode: "YOUR_MERCHANT_CODE",
      secretKey: "YOUR_SECRET_KEY",
      apiKey: "YOUR_API_KEY", // Optional - can be null or empty
      baseUrl: "https://api.jaijaipay.com/api/v1",
      timeoutSeconds: 30,
      maxRetries: 3,
      retryDelaySeconds: 1,
      enableRequestLog: true,
      logResponseBody: false,
      enableDebug: false,
      minimumWithdraw: 100.0,
      maximumWithdraw: 500000.0,
      withdrawSplit: false,
      maximumWithdrawPerTransaction: 50000.0,
      maximumSplitWithdrawPerTransaction: 25000.0,
      isDeposit: true,
      isWithdraw: true,
      isTransfer: false
    })
  });
  
  const result = await response.json();
  console.log('Provider created:', result);
  
  // 2. Activate the provider
  if (result.success) {
    await activateProvider(result.data.id);
  }
};

// 2. Activate provider
const activateProvider = async (providerId) => {
  const response = await fetch(`/api/v1/payment-gateway/providers/${providerId}/status`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      active: true
    })
  });
  
  const result = await response.json();
  console.log('Provider activated:', result);
};
```

### 2. Using JaiJaiPay Operations (Database Configuration)

```javascript
// Test connection using database configuration
const testConnection = async () => {
  const response = await fetch('/api/v1/payment-gateway/jaijaipay/test', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
      // No Authorization needed for test endpoint
    }
  });
  
  const result = await response.json();
  console.log('Database config test:', result);
  // Expected: "Connected using database configuration instead of environment variables"
};

// Get balance
const getBalance = async () => {
  const response = await fetch('/api/v1/payment-gateway/jaijaipay/balance', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  const result = await response.json();
  console.log('Current balance:', result.data.balance);
};

// Create deposit
const createDeposit = async (amount, memberUsername) => {
  const response = await fetch('/api/v1/payment-gateway/jaijaipay/deposits', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      amount: amount,
      currency: 'THB',
      memberUsername: memberUsername,
      description: `Deposit for ${memberUsername}`,
      callbackUrl: 'https://yoursite.com/callback/deposit',
      returnUrl: 'https://yoursite.com/return/deposit'
    })
  });
  
  const result = await response.json();
  console.log('Deposit created:', result);
  
  if (result.success) {
    // Redirect user to payment URL
    window.location.href = result.data.paymentUrl;
  }
};
```

### 3. Minimal Provider Configuration

```javascript
// Create provider with minimal required fields only
const createMinimalProvider = async () => {
  const response = await fetch('/api/v1/payment-gateway/providers', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      accountName: "Basic JaiJaiPay",
      code: "JAIJAI_BASIC",
      provider: "jaijaipay",
      merchantCode: "MERCHANT123",
      secretKey: "your-secret-key",
      // api_key is optional - can be omitted
      baseUrl: "https://api.jaijaipay.com/api/v1",
      isDeposit: true,
      isWithdraw: true,
      isTransfer: false
      // All other fields will use default values
    })
  });
  
  const result = await response.json();
  console.log('Minimal provider created:', result);
};
```

### 4. Managing Multiple Providers

```javascript
// Get all providers
const getAllProviders = async () => {
  const response = await fetch('/api/v1/payment-gateway/providers', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  const result = await response.json();
  return result.data;
};

// Switch active provider
const switchProvider = async (currentActiveId, newActiveId) => {
  // Deactivate current
  await fetch(`/api/v1/payment-gateway/providers/${currentActiveId}/status`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ active: false })
  });
  
  // Activate new
  await fetch(`/api/v1/payment-gateway/providers/${newActiveId}/status`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ active: true })
  });
  
  console.log('Provider switched successfully');
};
```

---

## Error Handling

### Common Error Codes

| HTTP Code | Description | Solution |
|-----------|-------------|----------|
| 400 | Bad Request - Invalid input data | Check request body validation |
| 401 | Unauthorized - Missing/invalid JWT | Include valid JWT token |
| 404 | Not Found - Resource not exists | Check resource ID/endpoint |
| 500 | Internal Server Error | Check server logs |

### Error Response Format

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information (in development)"
}
```

### Validation Errors

When creating a provider, validation errors include:

- `provider is required`
- `API key is required for JaiJaiPay`
- `secret key is required for JaiJaiPay`
- `base URL is required for JaiJaiPay`
- `timeout must be between 5 and 300 seconds`
- `max retries must be between 0 and 10`
- `payment gateway with this code already exists`

---

## Security Considerations

1. **Sensitive Data**: Never log response bodies that may contain sensitive information
2. **API Keys**: Store API keys and secret keys securely
3. **HTTPS**: Always use HTTPS in production
4. **JWT Tokens**: Implement proper token refresh mechanisms
5. **Rate Limiting**: Implement rate limiting for API endpoints

---

## Migration from Legacy JaiJaiPay

If you're migrating from the old hardcoded JaiJaiPay implementation:

### Step 1: Run Database Migration
```bash
make migrate-up ENV=local
```

### Step 2: Create JaiJaiPay Provider
Use your existing environment variables to create a database record:

```javascript
const migrateConfig = async () => {
  const response = await fetch('/api/v1/payment-gateway/providers', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      accountName: "JaiJaiPay Legacy Migration",
      code: "JAIJAI_MIGRATED",
      provider: "jaijaipay",
      merchantCode: process.env.JAIJAIPAY_MERCHANT_CODE || "",
      secretKey: process.env.JAIJAIPAY_SECRET_KEY,
      apiKey: process.env.JAIJAIPAY_API_KEY,
      baseUrl: process.env.JAIJAIPAY_BASE_URL || "https://api.jaijaipay.com/api/v1",
      timeoutSeconds: 30,
      maxRetries: 3,
      retryDelaySeconds: 1,
      enableRequestLog: true,
      logResponseBody: false,
      enableDebug: false,
      minimumWithdraw: 100.0,
      maximumWithdraw: 500000.0,
      withdrawSplit: false,
      maximumWithdrawPerTransaction: 50000.0,
      maximumSplitWithdrawPerTransaction: 25000.0,
      isDeposit: true,
      isWithdraw: true,
      isTransfer: false
    })
  });
  
  const result = await response.json();
  if (result.success) {
    // Activate the migrated configuration
    await activateProvider(result.data.id);
  }
};
```

### Step 3: Update API Calls
Replace old `/jaijai/*` endpoints with new `/payment-gateway/jaijaipay/*` endpoints.

### Step 4: Test Integration
Use the test endpoint to verify the configuration works correctly.

---

## Database Schema Notes

### Nullable Fields
The following fields support NULL values in the database:
- `api_key` - Can be empty for providers that don't require API keys
- `base_url` - Can be NULL, will use provider defaults
- `secret_key_two` - Optional secondary secret key
- `first_username` - Optional first username for dual-auth providers
- `second_username` - Optional second username for dual-auth providers  
- `first_password` - Optional first password for dual-auth providers
- `second_password` - Optional second password for dual-auth providers
- `timeout_seconds` - Default: 30 seconds
- `max_retries` - Default: 3 retries
- `retry_delay_seconds` - Default: 1 second
- `enable_request_log` - Default: true
- `log_response_body` - Default: false
- `enable_debug` - Default: false
- `withdraw_splitting` - Default: false

### Column Name Mapping
- Database column: `withdraw_splitting` → Struct field: `WithdrawSplit`
- All other fields map directly to their snake_case column names

---

## Recent Updates (August 26, 2025)

### ✅ Fixed Issues:
1. **Column Name Fix**: Resolved `withdraw_splitting` vs `withdraw_split` mismatch
2. **NULL Handling**: Added proper COALESCE for nullable fields
3. **API Endpoints**: All payment gateway provider endpoints now working correctly
4. **Database Schema**: Updated to support nullable `api_key` and other optional fields
5. **JaiJaiPay Integration**: **All `/deposits` endpoints now use database configuration instead of environment variables**

### 🔧 Technical Changes:
- Updated `GetAll()` method to use explicit column selection with COALESCE
- Fixed domain struct field mapping for `withdraw_splitting` column
- Added proper NULL value handling in repository queries
- Cleaned up duplicate column references
- **Migrated JaiJaiPayService to use PaymentGatewayService for database-driven configuration**
- **Updated all 12+ JaiJaiPay API methods** (CreateDeposit, ListDeposits, GetBalance, etc.) to use database config
- **Removed dependency on environment variables** for JaiJaiPay operations

### 📋 Migration Impact:
- **Old**: JaiJaiPay used hardcoded environment variables (JAIJAIPAY_API_KEY, JAIJAIPAY_SECRET_KEY)
- **New**: JaiJaiPay dynamically loads configuration from `payment_gateway_account` table
- **Benefit**: Multiple JaiJaiPay configurations, hot-swapping, and centralized management

### 🔄 Endpoint Changes:
**Legacy endpoints still work but now use database configuration:**
- `POST /api/v1/jaijai/deposits` - **Now uses database config instead of env vars**
- `GET /api/v1/jaijai/deposits` - **Now uses database config instead of env vars**
- `GET /api/v1/jaijai/balance` - **Now uses database config instead of env vars**
- `POST /api/v1/jaijai/withdrawals` - **Now uses database config instead of env vars**
- All other `/api/v1/jaijai/*` endpoints - **Now use database config**

**New endpoints for management:**
- `GET /api/v1/payment-gateway/providers` - List all providers
- `POST /api/v1/payment-gateway/providers` - Create new provider
- `PUT /api/v1/payment-gateway/providers/{id}/status` - Update provider status
- `POST /api/v1/payment-gateway/jaijaipay/test` - Test database configuration
- `POST /api/v1/payment-gateway/jaijaipay/webhooks/resend` - **Resend webhook for transaction**

---

## Support and Troubleshooting

### Common Issues

1. **No active JaiJaiPay configuration found**
   - Check if you have created a provider with `provider: "jaijaipay"`
   - Verify the provider is activated (`active: true`)

2. **Invalid API credentials**
   - Verify `apiKey` and `secretKey` are correct
   - Check `baseUrl` is the correct JaiJaiPay endpoint

3. **Connection timeout**
   - Adjust `timeoutSeconds` value
   - Check network connectivity to JaiJaiPay servers

4. **Rate limiting**
   - Implement proper retry logic using `maxRetries` and `retryDelaySeconds`
   - Monitor API usage patterns

### Debug Mode

Enable debug mode for troubleshooting:

```json
{
  "enableDebug": true,
  "enableRequestLog": true,
  "logResponseBody": true // Only enable in development
}
```

### Logs Location

Check application logs for detailed error information:
- Request/response logs when `enableRequestLog: true`
- Debug information when `enableDebug: true`
- Error details in structured JSON format

---

**Last Updated:** August 26, 2025 (22:30 ICT)  
**API Version:** v1  
**Status:** Production Ready ✅  
**Database Schema:** Updated with nullable `api_key` and fixed `withdraw_splitting` column

For additional support or questions, contact the development team or refer to the main API documentation.