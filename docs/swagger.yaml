basePath: /api/v1
definitions:
  bank_transaction_slip.CreateRequest:
    properties:
      amount:
        type: number
      fromAccountName:
        type: string
      fromAccountNumber:
        type: string
      fromBankName:
        type: string
      memberId:
        minimum: 1
        type: integer
      rawQrCode:
        type: string
      remark:
        type: string
      toAccountName:
        type: string
      toAccountNumber:
        type: string
      transactionDate:
        type: string
      transactionId:
        minimum: 1
        type: integer
    required:
    - amount
    - fromAccountName
    - fromAccountNumber
    - fromBankName
    - memberId
    - toAccountName
    - toAccountNumber
    - transactionDate
    - transactionId
    type: object
  bank_transaction_slip.Response:
    properties:
      amount:
        type: number
      createdAt:
        type: string
      fromAccountName:
        type: string
      fromAccountNumber:
        type: string
      fromBankName:
        type: string
      id:
        type: integer
      memberFullname:
        type: string
      memberId:
        type: integer
      memberUsername:
        type: string
      rawQrCode:
        type: string
      remark:
        type: string
      status:
        type: integer
      statusName:
        type: string
      toAccountName:
        type: string
      toAccountNumber:
        type: string
      transactionDate:
        type: string
      transactionId:
        type: integer
      updatedAt:
        type: string
    type: object
  bank_transaction_slip.UpdateRequest:
    properties:
      amount:
        type: number
      fromAccountName:
        type: string
      fromAccountNumber:
        type: string
      fromBankName:
        type: string
      rawQrCode:
        type: string
      remark:
        type: string
      toAccountName:
        type: string
      toAccountNumber:
        type: string
      transactionDate:
        type: string
    required:
    - amount
    - fromAccountName
    - fromAccountNumber
    - fromBankName
    - toAccountName
    - toAccountNumber
    - transactionDate
    type: object
  bank_transaction_slip.UpdateStatusRequest:
    properties:
      remark:
        type: string
      status:
        maximum: 4
        minimum: 1
        type: integer
    required:
    - status
    type: object
  channel.ChannelDropdownResponse:
    properties:
      id:
        type: integer
      name:
        type: string
      platform_id:
        type: string
      platform_name:
        type: string
    type: object
  common.ErrorResponse:
    properties:
      error:
        example: validation error
        type: string
      message:
        example: invalid parameters
        type: string
    type: object
  crypto_deposit.ChainToken:
    properties:
      blockchainNetworkId:
        type: integer
      createdAt:
        type: string
      id:
        type: integer
      isActive:
        type: boolean
      rateToThb:
        type: number
      tokenContract:
        type: string
      tokenDecimals:
        type: integer
      tokenSymbol:
        type: string
      updatedAt:
        type: string
    type: object
  crypto_deposit.ConversionCalculationResponse:
    properties:
      amountThb:
        example: 335
        type: number
      chainId:
        example: 84532
        type: integer
      rate:
        example: 33.5
        type: number
      tokenAmount:
        example: 10
        type: number
      tokenContract:
        example: ******************************************
        type: string
      tokenSymbol:
        example: USDC
        type: string
    type: object
  crypto_deposit.CreditUpdateRequest:
    properties:
      amountThb:
        example: 330
        type: number
      gameUsername:
        example: player123
        type: string
    required:
    - amountThb
    - gameUsername
    type: object
  crypto_deposit.CryptoDepositConfigResponse:
    properties:
      backendWalletAddress:
        example: ******************************************
        type: string
      finalRecipientAddress:
        example: ******************************************
        type: string
      supportedNetworks:
        items:
          $ref: '#/definitions/crypto_deposit.NetworkConfig'
        type: array
      tokenContracts:
        items:
          $ref: '#/definitions/crypto_deposit.TokenConfig'
        type: array
    type: object
  crypto_deposit.CryptoDepositListResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/crypto_deposit.CryptoDepositSummary'
        type: array
      pagination:
        $ref: '#/definitions/crypto_deposit.PaginationInfo'
    type: object
  crypto_deposit.CryptoDepositLog:
    properties:
      blockNumber:
        type: integer
      errorDetails:
        type: string
      eventData:
        additionalProperties: true
        type: object
      eventType:
        type: string
      gasPriceGwei:
        type: number
      gasUsed:
        type: integer
      id:
        type: integer
      stepNumber:
        type: integer
      timestamp:
        type: string
      transactionHash:
        type: string
      transactionId:
        type: string
    type: object
  crypto_deposit.CryptoDepositResponse:
    properties:
      amount:
        example: 100.5
        type: number
      currency:
        example: USDC
        type: string
      customerUsername:
        example: member123
        type: string
      id:
        example: 1001
        type: integer
      internalReference:
        example: member_deposit_001
        type: string
      network:
        $ref: '#/definitions/crypto_deposit.NetworkInfo'
      overallStatus:
        example: COMPLETED
        type: string
      provider:
        example: BLOCKCHAIN
        type: string
      step1:
        $ref: '#/definitions/crypto_deposit.StepInfo'
      step2:
        $ref: '#/definitions/crypto_deposit.StepInfo'
      summary:
        $ref: '#/definitions/crypto_deposit.TransactionSummary'
      timestamps:
        $ref: '#/definitions/crypto_deposit.TransactionTimestamps'
      transactionId:
        example: crypto_deposit_uuid_123456789
        type: string
      transactionType:
        example: DEPOSIT
        type: string
      wallets:
        $ref: '#/definitions/crypto_deposit.WalletInfo'
    type: object
  crypto_deposit.CryptoDepositSummary:
    properties:
      amount:
        example: 100.5
        type: number
      chainId:
        example: 84532
        type: integer
      completedAt:
        example: "2025-08-29T10:31:30Z"
        type: string
      completionTimeSeconds:
        example: 75
        type: integer
      createdAt:
        example: "2025-08-29T10:30:00Z"
        type: string
      currency:
        example: USDC
        type: string
      customerUsername:
        example: member123
        type: string
      finalReceivedAmount:
        example: 100.5
        type: number
      id:
        example: 1001
        type: integer
      networkName:
        example: Base Sepolia
        type: string
      overallStatus:
        example: COMPLETED
        type: string
      step1TransactionHash:
        example: 0xabc123def456...
        type: string
      step2TransactionHash:
        example: 0xdef456ghi789...
        type: string
      transactionId:
        example: crypto_deposit_uuid_123456789
        type: string
    type: object
  crypto_deposit.InitiateCryptoDepositRequest:
    properties:
      amount:
        example: 100.5
        type: number
      chainId:
        example: 84532
        type: integer
      currency:
        example: USDC
        type: string
      customerUsername:
        example: member123
        type: string
      frontendSessionId:
        example: session_uuid_987654321
        type: string
      internalReference:
        example: member_deposit_001
        type: string
      tokenContract:
        example: ******************************************
        type: string
      transactionId:
        example: crypto_deposit_uuid_123456789
        type: string
      userWalletAddress:
        example: ******************************************
        type: string
    required:
    - amount
    - chainId
    - currency
    - customerUsername
    - tokenContract
    - transactionId
    - userWalletAddress
    type: object
  crypto_deposit.InitiateDepositResponse:
    properties:
      amount:
        example: "100.50"
        type: string
      backendWalletAddress:
        example: ******************************************
        type: string
      chainId:
        example: 84532
        type: integer
      createdAt:
        example: "2025-08-29T10:30:00Z"
        type: string
      currency:
        example: USDC
        type: string
      expiresAt:
        example: "2025-08-29T11:00:00Z"
        type: string
      finalRecipientAddress:
        example: ******************************************
        type: string
      status:
        example: INITIATED
        type: string
      tokenContract:
        example: ******************************************
        type: string
      transactionId:
        example: crypto_deposit_uuid_123456789
        type: string
    type: object
  crypto_deposit.NetworkConfig:
    properties:
      chainId:
        example: 84532
        type: integer
      explorerUrl:
        example: https://sepolia-explorer.base.org
        type: string
      isActive:
        example: true
        type: boolean
      nativeCurrencySymbol:
        example: ETH
        type: string
      networkName:
        example: Base Sepolia
        type: string
      networkType:
        example: testnet
        type: string
      rpcUrl:
        example: https://sepolia.base.org
        type: string
      tokens:
        items:
          $ref: '#/definitions/crypto_deposit.TokenConfig'
        type: array
    type: object
  crypto_deposit.NetworkInfo:
    properties:
      chainId:
        example: 84532
        type: integer
      networkName:
        example: Base Sepolia
        type: string
      tokenContract:
        example: ******************************************
        type: string
      tokenSymbol:
        example: USDC
        type: string
    type: object
  crypto_deposit.PaginationInfo:
    properties:
      currentPage:
        example: 1
        type: integer
      pageSize:
        example: 10
        type: integer
      total:
        example: 25
        type: integer
      totalPages:
        example: 3
        type: integer
    type: object
  crypto_deposit.StepInfo:
    properties:
      actualAmount:
        example: 100.5
        type: number
      blockNumber:
        example: 12345
        type: integer
      confirmedAt:
        example: "2025-08-29T10:30:45Z"
        type: string
      gasFeeEth:
        example: 0.002
        type: number
      gasUsed:
        example: 65000
        type: integer
      initiatedAt:
        example: "2025-08-29T10:30:15Z"
        type: string
      status:
        example: confirmed
        type: string
      thirdwebTransactionId:
        example: 8fc4420d-b3d4-411f-968c-9466fa3f3491
        type: string
      transactionHash:
        example: 0xabc123def456...
        type: string
    type: object
  crypto_deposit.TokenConfig:
    properties:
      chainId:
        example: 84532
        type: integer
      contractAddress:
        example: ******************************************
        type: string
      decimals:
        example: 18
        type: integer
      id:
        example: 1
        type: integer
      isActive:
        example: true
        type: boolean
      rateToThb:
        example: 33.5
        type: number
      symbol:
        example: USDC
        type: string
    type: object
  crypto_deposit.TransactionSummary:
    properties:
      completionTimeSeconds:
        example: 75
        type: integer
      efficiencyPercentage:
        example: 100
        type: number
      finalReceivedAmount:
        example: 100.5
        type: number
      totalGasCostEth:
        example: 0.002
        type: number
    type: object
  crypto_deposit.TransactionTimestamps:
    properties:
      completedAt:
        example: "2025-08-29T10:31:30Z"
        type: string
      createdAt:
        example: "2025-08-29T10:30:00Z"
        type: string
      updatedAt:
        example: "2025-08-29T10:31:35Z"
        type: string
    type: object
  crypto_deposit.UpdateStep1Request:
    properties:
      actualAmount:
        example: 100.5
        type: number
      blockNumber:
        example: 12345
        type: integer
      confirmedAt:
        example: "2025-08-29T10:30:45Z"
        type: string
      errorMessage:
        type: string
      gasUsed:
        example: 65000
        type: integer
      initiatedAt:
        example: "2025-08-29T10:30:15Z"
        type: string
      status:
        enum:
        - pending
        - confirmed
        - failed
        - reverted
        example: confirmed
        type: string
      transactionHash:
        example: 0xabc123def456...
        type: string
    required:
    - actualAmount
    - initiatedAt
    - status
    - transactionHash
    type: object
  crypto_deposit.UpdateStep2Request:
    properties:
      actualAmount:
        example: 100.5
        type: number
      blockNumber:
        example: 12346
        type: integer
      confirmedAt:
        example: "2025-08-29T10:31:30Z"
        type: string
      creditUpdate:
        $ref: '#/definitions/crypto_deposit.CreditUpdateRequest'
      errorMessage:
        type: string
      gasFeeEth:
        example: 0.002
        type: number
      gasUsed:
        example: 75000
        type: integer
      initiatedAt:
        example: "2025-08-29T10:31:00Z"
        type: string
      status:
        enum:
        - pending
        - confirmed
        - failed
        - reverted
        example: confirmed
        type: string
      thirdwebTransactionId:
        example: 8fc4420d-b3d4-411f-968c-9466fa3f3491
        type: string
      transactionHash:
        example: 0xdef456ghi789...
        type: string
    required:
    - actualAmount
    - initiatedAt
    - status
    - transactionHash
    type: object
  crypto_deposit.UpdateStepResponse:
    properties:
      completedAt:
        example: "2025-08-29T10:31:30Z"
        type: string
      completionTimeSeconds:
        example: 75
        type: integer
      finalReceivedAmount:
        example: 100.5
        type: number
      overallStatus:
        example: STEP1_COMPLETED
        type: string
      step1CompletedAt:
        example: "2025-08-29T10:30:45Z"
        type: string
      step1Status:
        example: confirmed
        type: string
      step1TransactionHash:
        example: 0xabc123def456...
        type: string
      step2CompletedAt:
        example: "2025-08-29T10:31:30Z"
        type: string
      step2Status:
        example: confirmed
        type: string
      step2TransactionHash:
        example: 0xdef456ghi789...
        type: string
      transactionId:
        example: crypto_deposit_uuid_123456789
        type: string
      updatedAt:
        example: "2025-08-29T10:31:35Z"
        type: string
    type: object
  crypto_deposit.WalletInfo:
    properties:
      backendWallet:
        example: ******************************************
        type: string
      finalRecipient:
        example: ******************************************
        type: string
      userWallet:
        example: ******************************************
        type: string
    type: object
  customer_followup.CustomerFollowUpResponse:
    properties:
      balance:
        type: number
      call_count:
        description: Call and SMS counts
        type: integer
      channel_name:
        type: string
      contacted_by:
        type: integer
      contacted_by_name:
        type: string
      created_at:
        type: string
      first_name:
        type: string
      follow_up_status:
        type: string
      follow_up_tag:
        description: Follow-up specific info
        type: string
      id:
        description: Member basic info
        type: integer
      last_contact_at:
        type: string
      last_name:
        type: string
      last_online:
        type: string
      member_group_name:
        description: Joined info
        type: string
      partner_name:
        type: string
      phone:
        type: string
      platform_name:
        type: string
      remark:
        description: Additional info
        type: string
      sms_count:
        type: integer
      status:
        type: string
      username:
        type: string
    type: object
  customer_followup.TrackCustomerByCallRequest:
    properties:
      call_duration:
        minimum: 0
        type: integer
      call_status:
        enum:
        - completed
        - missed
        - busy
        - no_answer
        type: string
      member_id:
        minimum: 1
        type: integer
      notes:
        type: string
    required:
    - call_status
    - member_id
    type: object
  customer_followup.TrackCustomerBySMSRequest:
    properties:
      member_id:
        minimum: 1
        type: integer
      message_content:
        maxLength: 1000
        minLength: 1
        type: string
    required:
    - member_id
    - message_content
    type: object
  customer_followup.UpdateFollowUpStatusRequest:
    properties:
      follow_up_status:
        enum:
        - contacted
        - unreachable
        - not_contacted
        type: string
      member_id:
        minimum: 1
        type: integer
    required:
    - follow_up_status
    - member_id
    type: object
  customer_followup.UpdateFollowUpTagRequest:
    properties:
      follow_up_tag:
        enum:
        - cut_off
        - call_later
        - not_convenient
        - not_interested
        - no_money
        - no_answer
        - waiting_transfer
        type: string
      member_id:
        minimum: 1
        type: integer
    required:
    - follow_up_tag
    - member_id
    type: object
  customer_followup.UpdateMemberRemarkRequest:
    properties:
      member_id:
        minimum: 1
        type: integer
      remark:
        type: string
    required:
    - member_id
    type: object
  dashboard.ActiveBet:
    properties:
      active_bet:
        items:
          type: integer
        type: array
      active_bet_average:
        type: integer
      register:
        items:
          type: integer
        type: array
      register_average:
        type: integer
      register_deposit:
        items:
          type: integer
        type: array
      register_deposit_average:
        type: integer
      title:
        items:
          type: string
        type: array
    type: object
  dashboard.AdminApprove:
    properties:
      data:
        description: ตอนนี้เป็น array ว่าง
        items: {}
        type: array
      title:
        items:
          type: string
        type: array
    type: object
  dashboard.BotDepositData:
    properties:
      amount:
        items:
          type: number
        type: array
      count:
        items:
          type: integer
        type: array
      round_no:
        items:
          type: integer
        type: array
      sum_amount:
        type: number
      sum_count:
        type: integer
      title:
        items:
          type: string
        type: array
    type: object
  dashboard.BotSuccess:
    properties:
      admin_approve:
        $ref: '#/definitions/dashboard.AdminApprove'
      bot_deposit_hour:
        $ref: '#/definitions/dashboard.BotDepositData'
      bot_deposit_minute:
        $ref: '#/definitions/dashboard.BotDepositData'
      bot_success:
        $ref: '#/definitions/dashboard.BotSuccessData'
    type: object
  dashboard.BotSuccessData:
    properties:
      data:
        items:
          $ref: '#/definitions/dashboard.BotSuccessItem'
        type: array
      title:
        items:
          type: string
        type: array
    type: object
  dashboard.BotSuccessItem:
    properties:
      bank:
        type: string
      bank_id:
        type: integer
      count:
        type: integer
      minute:
        type: integer
      round_no:
        type: integer
    type: object
  dashboard.BotSuccessResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/dashboard.BotSuccess'
      message:
        type: string
      success:
        type: boolean
    type: object
  dashboard.CommissionAffiliate:
    properties:
      affiliate_level_1_total:
        type: number
      affiliate_level_2_total:
        type: number
      commission_total:
        type: number
      grouped_by_slug:
        additionalProperties:
          $ref: '#/definitions/dashboard.CommissionAffiliateGroup'
        type: object
    type: object
  dashboard.CommissionAffiliateGroup:
    properties:
      items:
        items:
          $ref: '#/definitions/dashboard.CommissionAffiliateItem'
        type: array
      name:
        type: string
      slug:
        type: string
      total_affiliate_level_1:
        type: number
      total_affiliate_level_2:
        type: number
      total_commission:
        type: number
    type: object
  dashboard.CommissionAffiliateItem:
    properties:
      affiliate_level_1:
        type: string
      affiliate_level_2:
        type: string
      commission:
        type: string
      game_type:
        $ref: '#/definitions/dashboard.GameType'
      game_type_id:
        type: integer
      provider:
        $ref: '#/definitions/dashboard.Provider'
      provider_id:
        type: integer
    type: object
  dashboard.DailyData:
    properties:
      active_bet:
        type: integer
      active_bet_percent:
        type: integer
      active_member:
        type: integer
      active_member_percent:
        type: number
      affiliate:
        type: string
      affiliate_percent:
        type: string
      agent_stats_app_active:
        type: integer
      agent_stats_app_active_percen:
        type: integer
      agent_stats_app_download:
        type: integer
      agent_stats_app_download_percen:
        type: integer
      balance:
        type: string
      balance_percent:
        type: string
      bonus:
        type: string
      bonus_percent:
        type: string
      commission:
        type: string
      commission_percent:
        type: string
      deposit:
        type: string
      deposit_credit:
        type: string
      deposit_credit_percent:
        type: string
      deposit_percent:
        type: string
      expense:
        type: string
      expense_percent:
        type: string
      first_deposit:
        type: integer
      first_deposit_percent:
        type: string
      jackpot:
        type: string
      jackpot_percent:
        type: string
      member_balance:
        type: string
      member_balance_percent:
        type: string
      new_member:
        type: integer
      new_member_percent:
        type: string
      note:
        type: string
      note_percent:
        type: string
      profit:
        type: string
      profit_percent:
        type: string
      revenue:
        type: string
      revenue_percent:
        type: string
      win_lose:
        type: string
      win_lose_percent:
        type: string
      withdraw:
        type: string
      withdraw_credit:
        type: string
      withdraw_credit_percent:
        type: string
      withdraw_finance:
        type: string
      withdraw_finance_percent:
        type: string
      withdraw_percent:
        type: string
      withdraw_sma:
        type: string
      withdraw_sma_percent:
        type: string
    type: object
  dashboard.Dashboard:
    properties:
      active_bets:
        $ref: '#/definitions/dashboard.ActiveBet'
      commission_affiliates:
        $ref: '#/definitions/dashboard.CommissionAffiliate'
      daily:
        items:
          $ref: '#/definitions/dashboard.DailyData'
        type: array
      game_types:
        $ref: '#/definitions/dashboard.GameTypeData'
      net_turns:
        $ref: '#/definitions/dashboard.NetTurn'
      partner_groups:
        items:
          $ref: '#/definitions/dashboard.PartnerGroup'
        type: array
      transactions:
        $ref: '#/definitions/dashboard.Transaction'
      turnovers:
        $ref: '#/definitions/dashboard.Turnover'
    type: object
  dashboard.DashboardResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/dashboard.Dashboard'
      message:
        type: string
      success:
        type: boolean
    type: object
  dashboard.GameType:
    properties:
      created_at:
        type: string
      deleted_at:
        type: string
      id:
        type: integer
      name:
        type: string
      slug:
        type: string
      updated_at:
        type: string
    type: object
  dashboard.GameTypeData:
    properties:
      grouped_by_slug:
        additionalProperties:
          $ref: '#/definitions/dashboard.GameTypeGroup'
        type: object
      turnover_total:
        type: number
      winLost_total:
        type: number
    type: object
  dashboard.GameTypeGroup:
    properties:
      items:
        items:
          $ref: '#/definitions/dashboard.GameTypeItem'
        type: array
      name:
        type: string
      slug:
        type: string
      total_net_turn:
        type: number
      total_turnover:
        type: number
      total_win_lose:
        type: number
    type: object
  dashboard.GameTypeItem:
    properties:
      game_type:
        $ref: '#/definitions/dashboard.GameType'
      game_type_id:
        type: integer
      net_turn:
        type: string
      provider:
        $ref: '#/definitions/dashboard.Provider'
      provider_id:
        type: integer
      turnover:
        type: string
      win_lose:
        type: string
    type: object
  dashboard.NetTurn:
    properties:
      netturn:
        items:
          type: number
        type: array
      percent:
        items:
          type: number
        type: array
      provider_id:
        items:
          type: integer
        type: array
      provider_name:
        items:
          type: string
        type: array
      title:
        items:
          type: string
        type: array
      total:
        type: number
    type: object
  dashboard.PartnerGroup:
    properties:
      first_deposit:
        type: string
      id:
        type: integer
      name:
        type: string
      qty:
        type: string
      title:
        type: string
      total_deposit:
        type: string
    type: object
  dashboard.Provider:
    properties:
      code:
        type: string
      created_at:
        type: string
      deleted_at:
        type: string
      game_type_id:
        type: integer
      id:
        type: integer
      logo:
        type: string
      name:
        type: string
      status:
        type: string
      updated_at:
        type: string
    type: object
  dashboard.Transaction:
    properties:
      deposit:
        items:
          type: integer
        type: array
      deposit_amount:
        items:
          type: number
        type: array
      deposit_count:
        type: integer
      deposit_total:
        type: number
      register_deposit:
        items:
          type: integer
        type: array
      register_deposit_average:
        type: integer
      title:
        items:
          type: string
        type: array
      withdraw:
        items:
          type: integer
        type: array
      withdraw_amount:
        items:
          type: number
        type: array
      withdraw_count:
        type: integer
      withdraw_total:
        type: number
    type: object
  dashboard.Turnover:
    properties:
      percent:
        items:
          type: number
        type: array
      provider_id:
        items:
          type: integer
        type: array
      provider_name:
        items:
          type: string
        type: array
      title:
        items:
          type: string
        type: array
      total:
        type: number
      turnover:
        items:
          type: number
        type: array
    type: object
  http.ChangePasswordRequest:
    properties:
      confirm_password:
        minLength: 8
        type: string
      new_password:
        minLength: 8
        type: string
    required:
    - confirm_password
    - new_password
    type: object
  http.ErrorResponse:
    properties:
      data: {}
      message:
        type: string
    type: object
  http.RefreshTokenRequest:
    properties:
      refresh_token:
        type: string
      user_type:
        enum:
        - admin
        - member
        type: string
    required:
    - refresh_token
    - user_type
    type: object
  http.RefreshTokenResponse:
    properties:
      access_token:
        type: string
      expires_in:
        description: seconds
        type: integer
      refresh_token:
        type: string
      token_type:
        type: string
    type: object
  jaijaipay.CancelDepositRequest:
    properties:
      transactionId:
        type: string
    type: object
  jaijaipay.CreateDepositRequest:
    type: object
  jaijaipay.CreateWithdrawalRequest:
    type: object
  member.ChangeMemberPartnerRequest:
    properties:
      action_remark:
        maxLength: 500
        type: string
      partner_id:
        minimum: 1
        type: integer
    type: object
  member.ChangeMemberPasswordRequest:
    properties:
      new_password:
        maxLength: 50
        minLength: 6
        type: string
    required:
    - new_password
    type: object
  member.CompleteRegistrationRequest:
    properties:
      accept_terms:
        type: boolean
      bank_code:
        maxLength: 10
        type: string
      bank_number:
        maxLength: 50
        type: string
      first_name:
        maxLength: 100
        minLength: 1
        type: string
      gender:
        enum:
        - male
        - female
        - other
        type: string
      last_name:
        maxLength: 100
        minLength: 1
        type: string
      password:
        minLength: 6
        type: string
      phone:
        type: string
      register_refer_code:
        type: string
      username:
        description: Registration data (username will be validated but phone will
          be used as actual username)
        maxLength: 50
        minLength: 3
        type: string
    required:
    - accept_terms
    - first_name
    - last_name
    - password
    - phone
    - username
    type: object
  member.CreateMemberRequest:
    properties:
      bank_code:
        maxLength: 10
        type: string
      bank_number:
        maxLength: 50
        minLength: 1
        type: string
      first_name:
        maxLength: 100
        type: string
      gender:
        enum:
        - male
        - female
        - other
        type: string
      last_name:
        maxLength: 100
        type: string
      member_group_id:
        minimum: 1
        type: integer
      partner_id:
        minimum: 1
        type: integer
      password:
        minLength: 6
        type: string
      phone:
        maxLength: 15
        minLength: 10
        type: string
      referral_group_id:
        minimum: 1
        type: integer
      remark:
        maxLength: 500
        type: string
    required:
    - bank_code
    - bank_number
    - gender
    - member_group_id
    - password
    - phone
    type: object
  member.DeleteMemberRequest:
    properties:
      action_remark:
        maxLength: 500
        type: string
    type: object
  member.GameProviderRequest:
    properties:
      phone:
        type: string
      provider:
        type: string
    required:
    - phone
    - provider
    type: object
  member.MemberResponse:
    properties:
      avatar:
        type: string
      balance:
        type: number
      bank_code:
        type: string
      bank_number:
        type: string
      bank_short_name:
        description: Bank short name from JOIN
        type: string
      birth_date:
        description: date YYYY-MM-DD
        type: string
      channel_id:
        type: integer
      channel_name:
        type: string
      commission_group_name:
        type: string
      contacted_by:
        description: Admin ID who last contacted
        type: integer
      created_at:
        type: string
      created_by_username:
        type: string
      first_name:
        type: string
      follow_up_status:
        description: contacted, unreachable, not_contacted
        type: string
      follow_up_tag:
        description: Customer follow-up fields
        type: string
      game_providers:
        additionalProperties:
          type: string
        description: Game data from external API
        type: object
      game_username:
        type: string
      gender:
        type: string
      id:
        type: integer
      is_enable:
        type: boolean
      is_partner:
        description: Partner-related fields
        type: boolean
      last_contact_at:
        description: Last contact timestamp
        type: string
      last_login_device:
        type: string
      last_login_ip:
        type: string
      last_login_user_agent:
        type: string
      last_name:
        type: string
      last_online:
        type: string
      line_id:
        type: string
      login_status:
        type: boolean
      member_group_id:
        type: integer
      member_group_name:
        description: Group names from JOIN
        type: string
      operate_status:
        type: boolean
      partner_remark:
        type: string
      phone:
        type: string
      platform_id:
        type: string
      refer_code:
        type: string
      refer_user_id:
        type: integer
      referral_group_id:
        type: integer
      referral_group_name:
        type: string
      register_ip:
        type: string
      register_refer_code:
        type: string
      register_status:
        type: boolean
      remark:
        type: string
      show_partner_info:
        type: boolean
      status:
        $ref: '#/definitions/member.Status'
      tw_username:
        type: string
      twofa_status:
        type: integer
      twofa_verify_count:
        type: integer
      updated_at:
        type: string
      username:
        type: string
    type: object
  member.MemberStatusCount:
    properties:
      count:
        type: integer
      label:
        description: Thai label for status
        type: string
      status:
        $ref: '#/definitions/member.Status'
    type: object
  member.MemberStatusCountsResponse:
    properties:
      status_counts:
        items:
          $ref: '#/definitions/member.MemberStatusCount'
        type: array
      total_count:
        description: sum of active + suspended + banned
        type: integer
    type: object
  member.PartnerDropdownResponse:
    properties:
      id:
        type: integer
      name:
        description: Combined first_name + last_name
        type: string
      refer_code:
        type: string
    type: object
  member.RegistrationOTPResponse:
    properties:
      expires_in:
        description: seconds
        type: integer
      message:
        type: string
      phone:
        type: string
      reference:
        type: string
    type: object
  member.Status:
    enum:
    - active
    - inactive
    - suspended
    - banned
    - all
    type: string
    x-enum-comments:
      StatusAll: for total count display
    x-enum-descriptions:
    - ""
    - ""
    - ""
    - ""
    - for total count display
    x-enum-varnames:
    - StatusActive
    - StatusInactive
    - StatusSuspended
    - StatusBanned
    - StatusAll
  member.SuspendMemberRequest:
    properties:
      action_remark:
        maxLength: 500
        type: string
    type: object
  member.UpdateBankInfoRequest:
    properties:
      bank_code:
        maxLength: 10
        type: string
      bank_number:
        maxLength: 50
        type: string
      first_name:
        maxLength: 100
        type: string
      last_name:
        maxLength: 100
        type: string
      title:
        maxLength: 200
        type: string
      tw_username:
        maxLength: 50
        type: string
    type: object
  member.UpdateMemberRequest:
    properties:
      avatar:
        maxLength: 255
        type: string
      bank_code:
        maxLength: 10
        type: string
      bank_number:
        maxLength: 50
        type: string
      first_name:
        maxLength: 100
        type: string
      game_password:
        minLength: 6
        type: string
      game_username:
        maxLength: 50
        minLength: 3
        type: string
      gender:
        enum:
        - male
        - female
        - other
        type: string
      is_enable:
        type: boolean
      last_name:
        maxLength: 100
        type: string
      line_id:
        maxLength: 50
        type: string
      login_status:
        type: boolean
      member_group_id:
        minimum: 1
        type: integer
      operate_status:
        type: boolean
      partner_id:
        minimum: 1
        type: integer
      password:
        description: Username      *string `json:"username" validate:"omitempty,min=3,max=50"`
        minLength: 6
        type: string
      phone:
        maxLength: 50
        type: string
      referral_group_id:
        minimum: 1
        type: integer
      tw_username:
        maxLength: 50
        type: string
    required:
    - member_group_id
    - referral_group_id
    type: object
  member.VerifyRegistrationOTPRequest:
    properties:
      code:
        type: string
      phone:
        type: string
      reference:
        type: string
    required:
    - code
    - phone
    - reference
    type: object
  member.VerifySendOTPRequest:
    properties:
      phone:
        maxLength: 10
        minLength: 10
        type: string
    type: object
  member_group.DepositTurnoverReleaseType:
    enum:
    - fixed
    - percentage
    type: string
    x-enum-varnames:
    - DepositTurnoverReleaseTypeFixed
    - DepositTurnoverReleaseTypePercentage
  member_group.DepositTurnoverType:
    enum:
    - nil
    - multiplier
    - percentage
    type: string
    x-enum-varnames:
    - DepositTurnoverTypeNil
    - DepositTurnoverTypeMultiplier
    - DepositTurnoverTypePercentage
  member_group.MemberGroupDepositAccount:
    properties:
      account_deposit_id:
        description: บัญชีฝาก
        type: integer
      created_at:
        type: string
      id:
        type: integer
      member_group_id:
        type: integer
    type: object
  member_group.MemberGroupResponse:
    properties:
      calculate_min_deposit:
        type: number
      code:
        type: string
      commission_group_id:
        type: integer
      commission_group_name:
        type: string
      created_at:
        type: string
      daily_withdraw_amount_limit:
        type: number
      daily_withdraw_limit:
        type: integer
      deposit_accounts:
        items:
          $ref: '#/definitions/member_group.MemberGroupDepositAccount'
        type: array
      deposit_turnover_amount:
        type: number
      deposit_turnover_release_amount:
        type: number
      deposit_turnover_release_type:
        $ref: '#/definitions/member_group.DepositTurnoverReleaseType'
      deposit_turnover_type:
        $ref: '#/definitions/member_group.DepositTurnoverType'
      id:
        type: integer
      image:
        type: string
      is_default:
        type: boolean
      is_vip:
        type: boolean
      max_deposit:
        type: number
      member_group_type_id:
        type: integer
      member_group_type_show_in_lobby:
        type: boolean
      min_deposit:
        type: number
      min_withdraw:
        type: number
      name:
        type: string
      status:
        $ref: '#/definitions/member_group.Status'
      updated_at:
        type: string
      withdrawal_approvals:
        items:
          $ref: '#/definitions/member_group.MemberGroupWithdrawalApproval'
        type: array
    type: object
  member_group.MemberGroupWithdrawalApproval:
    properties:
      created_at:
        type: string
      id:
        type: integer
      max_amount:
        description: จำนวนเงินถอนสูงสุด
        type: number
      member_group_id:
        type: integer
      min_amount:
        description: จำนวนเงินถอนขั้นต่ำ
        type: number
      order_index:
        description: ลำดับการอนุมัติ
        type: integer
      updated_at:
        type: string
      user_role_ids:
        description: บทบาทผู้อนุมัติ (array)
        items:
          type: integer
        type: array
    type: object
  member_group.Status:
    enum:
    - active
    - inactive
    type: string
    x-enum-varnames:
    - StatusActive
    - StatusInactive
  payment_gateway_account.PaymentGatewayAccountRequest:
    properties:
      accountName:
        maxLength: 255
        minLength: 1
        type: string
      apiKey:
        type: string
      baseUrl:
        type: string
      code:
        maxLength: 255
        minLength: 1
        type: string
      enableDebug:
        default: false
        type: boolean
      enableRequestLog:
        default: true
        type: boolean
      firstPassword:
        type: string
      firstUsername:
        type: string
      isDeposit:
        default: false
        type: boolean
      isTransfer:
        default: false
        type: boolean
      isWithdraw:
        default: false
        type: boolean
      logResponseBody:
        default: false
        type: boolean
      maxRetries:
        default: 3
        maximum: 10
        minimum: 0
        type: integer
      maximumSplitWithdrawPerTransaction:
        type: number
      maximumWithdraw:
        type: number
      maximumWithdrawPerTransaction:
        type: number
      merchantCode:
        maxLength: 255
        minLength: 1
        type: string
      minimumWithdraw:
        type: number
      provider:
        maxLength: 255
        minLength: 1
        type: string
      retryDelaySeconds:
        default: 1
        maximum: 60
        minimum: 1
        type: integer
      secondPassword:
        type: string
      secondUsername:
        type: string
      secretKey:
        maxLength: 255
        minLength: 1
        type: string
      secretKeyTwo:
        type: string
      timeoutSeconds:
        default: 30
        maximum: 300
        minimum: 5
        type: integer
      withdrawSplit:
        default: false
        type: boolean
    required:
    - accountName
    - baseUrl
    - code
    - maximumSplitWithdrawPerTransaction
    - maximumWithdraw
    - maximumWithdrawPerTransaction
    - merchantCode
    - minimumWithdraw
    - provider
    - secretKey
    type: object
  permission.UserRolePermissionUpdate:
    properties:
      can_create:
        type: boolean
      can_delete:
        type: boolean
      can_edit:
        type: boolean
      can_view:
        type: boolean
      permission_key:
        type: string
    required:
    - permission_key
    type: object
  platform.PlatformResponse:
    properties:
      id:
        type: string
      name:
        type: string
      type:
        $ref: '#/definitions/platform.PlatformType'
    type: object
  platform.PlatformType:
    enum:
    - Facebook
    - Youtube
    - Google
    - Line
    - Tiktok
    - Banner
    - Twitter
    type: string
    x-enum-varnames:
    - PlatformTypeFacebook
    - PlatformTypeYoutube
    - PlatformTypeGoogle
    - PlatformTypeLine
    - PlatformTypeTiktok
    - PlatformTypeBanner
    - PlatformTypeTwitter
  promotion_web.CancelPromotionWebRequest:
    properties:
      id:
        type: integer
    type: object
  promotion_web.CollectPromotionRequest:
    properties:
      promotionWebId:
        type: integer
    required:
    - promotionWebId
    type: object
  promotion_web.DragSortRequest:
    properties:
      fromItemId:
        type: integer
      toItemId:
        type: integer
    required:
    - fromItemId
    - toItemId
    type: object
  promotion_web.LockCreditPromotionCreateRequest:
    properties:
      bonusAmount:
        type: number
      isLocked:
        type: boolean
      promotionId:
        type: integer
      promotionWebUserId:
        type: integer
      userId:
        type: integer
    required:
    - bonusAmount
    - promotionId
    - promotionWebUserId
    - userId
    type: object
  promotion_web.PromotionWebCreateRequest:
    properties:
      ableWithdrawMorethan:
        type: number
      ableWithdrawPertime:
        type: number
      bonusConditionAmount:
        type: number
      bonusTypeAmount:
        type: number
      bonusTypeAmountMax:
        type: number
      conditionDetail:
        type: string
      description:
        type: string
      endDate:
        type: string
      freeBonusAmount:
        type: number
      friday:
        type: boolean
      hiddenUrlLink:
        type: string
      imageUrl:
        type: string
      monday:
        type: boolean
      name:
        type: string
      privilegePerDay:
        type: integer
      promotionWebBonusConditionId:
        type: integer
      promotionWebBonusTypeId:
        type: integer
      promotionWebDateTypeId:
        type: integer
      promotionWebStatusId:
        type: integer
      promotionWebTurnoverTypeId:
        type: integer
      promotionWebTypeId:
        type: integer
      saturday:
        type: boolean
      shortDescription:
        type: string
      startDate:
        type: string
      sunday:
        type: boolean
      thursday:
        type: boolean
      timeEnd:
        type: string
      timeStart:
        type: string
      tuesday:
        type: boolean
      turnoverAmount:
        type: number
      wednesday:
        type: boolean
    required:
    - description
    - name
    - promotionWebDateTypeId
    - promotionWebStatusId
    - promotionWebTypeId
    - shortDescription
    type: object
  promotion_web.PromotionWebUpdateRequest:
    properties:
      ableWithdrawMorethan:
        type: number
      ableWithdrawPertime:
        type: number
      bonusConditionAmount:
        type: number
      bonusTypeAmount:
        type: number
      bonusTypeAmountMax:
        type: number
      conditionDetail:
        type: string
      description:
        type: string
      endDate:
        type: string
      freeBonusAmount:
        type: number
      friday:
        type: boolean
      hiddenUrlLink:
        type: string
      imageUrl:
        type: string
      monday:
        type: boolean
      name:
        type: string
      privilegePerDay:
        type: integer
      promotionWebBonusConditionId:
        type: integer
      promotionWebBonusTypeId:
        type: integer
      promotionWebDateTypeId:
        type: integer
      promotionWebStatusId:
        type: integer
      promotionWebTurnoverTypeId:
        type: integer
      promotionWebTypeId:
        type: integer
      saturday:
        type: boolean
      shortDescription:
        type: string
      startDate:
        type: string
      sunday:
        type: boolean
      thursday:
        type: boolean
      timeEnd:
        type: string
      timeStart:
        type: string
      tuesday:
        type: boolean
      turnoverAmount:
        type: number
      wednesday:
        type: boolean
    type: object
  referral.Dataset:
    properties:
      backgroundColor:
        type: string
      borderColor:
        type: string
      data:
        items:
          type: number
        type: array
      label:
        type: string
    type: object
  referral.FAQ:
    properties:
      answer:
        type: string
      id:
        type: integer
      position:
        type: integer
      status:
        type: string
      title:
        type: string
    type: object
  referral.GameCategoryStat:
    properties:
      category:
        description: '"Slot", "Casino", etc.'
        type: string
      commission:
        description: total_winloss * commission_percent
        type: number
      total_winloss:
        description: sum(upline_winloss) for this category
        type: number
    type: object
  referral.HourlyChartData:
    properties:
      datasets:
        description: Chart.js dataset format
        items:
          $ref: '#/definitions/referral.Dataset'
        type: array
      labels:
        description: '["00:00", "01:00", "02:00", ...]'
        items:
          type: string
        type: array
    type: object
  referral.IncomeResponse:
    properties:
      commission_growth_percentage:
        type: number
      pagination:
        $ref: '#/definitions/referral.PaginationMeta'
      recent_transactions:
        items:
          $ref: '#/definitions/referral.IncomeTransaction'
        type: array
      total_income:
        type: number
    type: object
  referral.IncomeTransaction:
    properties:
      amount:
        description: Transaction amount (positive for income, negative for expense)
        type: number
      created_by_admin:
        description: NULL if not created by admin
        type: integer
      created_by_member:
        description: NULL if not created by member
        type: integer
      creator_type:
        description: '"system", "admin", "member"'
        type: string
      id:
        type: integer
      member_username:
        type: string
      refer_code:
        type: string
      remark:
        description: Optional remark
        type: string
      status:
        type: string
      transaction_date:
        type: string
      type:
        description: 'Transaction type: commission, withdraw, adjustment, bonus'
        type: string
    type: object
  referral.OverviewResponse:
    properties:
      commission_balance:
        description: member -> commission_balance
        type: number
      commission_growth_percentage:
        description: '% growth month over month from referral_transactions'
        type: number
      referral_view_count:
        description: member -> referral_view_count
        type: integer
      today_downline_register_count:
        description: today's new registrations
        type: integer
      total_referrals:
        description: Required fields based on new requirements
        type: integer
    type: object
  referral.PaginationMeta:
    properties:
      has_next:
        type: boolean
      has_prev:
        type: boolean
      limit:
        type: integer
      page:
        type: integer
      total_count:
        type: integer
      total_pages:
        type: integer
    type: object
  referral.ReferralMember:
    properties:
      downline_count:
        type: integer
      joined_at:
        type: string
      member_id:
        type: integer
      phone:
        type: string
      status:
        type: string
      username:
        type: string
    type: object
  referral.ReferralMembersResponse:
    properties:
      downlines:
        items:
          $ref: '#/definitions/referral.ReferralMember'
        type: array
      game_category_stats:
        items:
          $ref: '#/definitions/referral.GameCategoryStat'
        type: array
      growth_percent:
        type: number
      hourly_chart:
        $ref: '#/definitions/referral.HourlyChartData'
      pagination:
        $ref: '#/definitions/referral.PaginationMeta'
      today_commission:
        type: number
    type: object
  referral.TutorialFAQResponse:
    properties:
      faqs:
        items:
          $ref: '#/definitions/referral.FAQ'
        type: array
      network_tutorial_image:
        type: string
      network_tutorial_make_money_image:
        type: string
      network_tutorial_text:
        type: string
    type: object
  referral.WithdrawCommissionRequest:
    properties:
      amount:
        type: number
    required:
    - amount
    type: object
  referral.WithdrawCommissionResponse:
    properties:
      balance_after:
        type: number
      balance_before:
        type: number
      created_at:
        type: string
      member_id:
        type: integer
      message:
        type: string
      remark:
        type: string
      status:
        type: string
      transaction_id:
        type: integer
      withdraw_amount:
        type: number
    type: object
  report.DetailReportSummary:
    properties:
      commission_count:
        type: integer
      net_amount:
        type: number
      total_commission_amount:
        type: number
      total_records:
        type: integer
      total_withdraw_amount:
        type: number
      withdraw_count:
        type: integer
    type: object
  report.PaginationResponse:
    properties:
      has_next:
        type: boolean
      has_prev:
        type: boolean
      limit:
        type: integer
      page:
        type: integer
      total_count:
        type: integer
      total_pages:
        type: integer
    type: object
  report.ReferralReportDetailGrouped:
    properties:
      adjustment_count:
        type: integer
      bonus_count:
        type: integer
      commission_count:
        type: integer
      downline_member_id:
        type: integer
      downline_member_phone:
        type: string
      downline_member_username:
        type: string
      first_transaction_date:
        type: string
      last_transaction_date:
        type: string
      member_id:
        type: integer
      member_phone:
        type: string
      member_username:
        type: string
      net_amount:
        type: number
      total_adjustment_amount:
        type: number
      total_bonus_amount:
        type: number
      total_commission_amount:
        type: number
      total_withdraw_amount:
        type: number
      transaction_count:
        type: integer
      withdraw_count:
        type: integer
    type: object
  report.ReferralReportDetailGroupedResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/report.ReferralReportDetailGrouped'
        type: array
      pagination:
        $ref: '#/definitions/report.PaginationResponse'
      summary:
        $ref: '#/definitions/report.DetailReportSummary'
    type: object
  report.ReferralReportSummary:
    properties:
      current_commission_balance:
        description: from members table
        type: number
      downline_count:
        description: distinct count of downline_member_id
        type: integer
      first_transaction_date:
        type: string
      last_transaction_date:
        type: string
      member_first_name:
        type: string
      member_id:
        type: integer
      member_last_name:
        type: string
      member_phone:
        type: string
      member_username:
        type: string
      net_amount:
        description: commission - withdraw
        type: number
      refer_code:
        type: string
      total_commission_amount:
        type: number
      total_withdraw_amount:
        type: number
      transaction_count:
        type: integer
    type: object
  report.ReferralReportSummaryResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/report.ReferralReportSummary'
        type: array
      pagination:
        $ref: '#/definitions/report.PaginationResponse'
      summary:
        $ref: '#/definitions/report.ReportSummary'
    type: object
  report.ReportSummary:
    properties:
      average_commission_per_member:
        type: number
      net_amount:
        type: number
      total_commission_amount:
        type: number
      total_downline_count:
        type: integer
      total_members:
        type: integer
      total_transaction_count:
        type: integer
      total_withdraw_amount:
        type: number
    type: object
  response.Error:
    properties:
      code:
        type: integer
      error:
        type: string
      message:
        type: string
    type: object
  response.Success:
    properties:
      data: {}
      message:
        maxLength: 255
        minLength: 1
        type: string
    required:
    - message
    type: object
  response.SuccessWithPagination:
    properties:
      content: {}
      limit:
        type: integer
      message:
        maxLength: 255
        minLength: 1
        type: string
      page:
        type: integer
      totalItems:
        type: integer
      totalPages:
        type: integer
    required:
    - message
    type: object
  service.BulkUpdateRequest:
    properties:
      permissions:
        items:
          $ref: '#/definitions/permission.UserRolePermissionUpdate'
        type: array
    required:
    - permissions
    type: object
  summary_bet_by_member.Pagination:
    properties:
      currentPage:
        type: string
      from:
        type: integer
      lastPage:
        type: integer
      nextPage:
        type: string
      perPage:
        type: string
      prevPage:
        type: string
      to:
        type: integer
      total:
        type: integer
    type: object
  summary_bet_by_member.SummaryBetByMember:
    properties:
      data:
        items:
          $ref: '#/definitions/summary_bet_by_member.SummaryBetByMemberItem'
        type: array
      pagination:
        $ref: '#/definitions/summary_bet_by_member.Pagination'
    type: object
  summary_bet_by_member.SummaryBetByMemberDetail:
    properties:
      data:
        items:
          $ref: '#/definitions/summary_bet_by_member.SummaryBetByMemberDetailItem'
        type: array
      pagination:
        $ref: '#/definitions/summary_bet_by_member.Pagination'
    type: object
  summary_bet_by_member.SummaryBetByMemberDetailItem:
    properties:
      _id:
        properties:
          game_type_id:
            type: integer
          gaming_provider_code:
            type: string
          phone:
            type: string
          user_id:
            type: integer
        type: object
      commission_amount:
        type: number
      commission_net_turnover:
        type: number
      jackpot:
        type: number
      net_turn_over:
        type: number
      turn_over:
        type: number
      win_lose:
        type: number
      win_lose_not_include_jackpot:
        type: number
    type: object
  summary_bet_by_member.SummaryBetByMemberDetailResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/summary_bet_by_member.SummaryBetByMemberDetail'
      message:
        type: string
      success:
        type: boolean
    type: object
  summary_bet_by_member.SummaryBetByMemberItem:
    properties:
      _id:
        properties:
          phone:
            type: string
          user_id:
            type: integer
        type: object
      balance:
        type: string
      commission_amount:
        type: number
      commission_net_turnover:
        type: number
      count_user:
        type: integer
      jackpot:
        type: number
      net_turn_over:
        type: number
      turn_over:
        type: number
      user_code:
        type: string
      win_lose:
        type: number
      win_lose_not_include_jackpot:
        type: number
    type: object
  summary_bet_by_member.SummaryBetByMemberResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/summary_bet_by_member.SummaryBetByMember'
      message:
        type: string
      success:
        type: boolean
    type: object
  summary_report.Provider:
    properties:
      code:
        type: string
      id:
        type: integer
      logo:
        type: string
      name:
        type: string
    type: object
  summary_report.SummaryReport:
    properties:
      data:
        items:
          $ref: '#/definitions/summary_report.SummaryReportItem'
        type: array
    type: object
  summary_report.SummaryReportDetail:
    properties:
      data:
        items:
          $ref: '#/definitions/summary_report.SummaryReportDetailItem'
        type: array
    type: object
  summary_report.SummaryReportDetailItem:
    properties:
      _id:
        properties:
          game_code:
            type: string
          game_name:
            type: string
          gaming_provider_code:
            type: string
        type: object
      commission_amount:
        type: number
      commission_net_turnover:
        type: number
      jackpot:
        type: number
      net_turn_over:
        type: number
      provider:
        allOf:
        - $ref: '#/definitions/summary_report.Provider'
        description: Provider information (joined)
      turn_over:
        type: number
      win_lose:
        type: number
      win_lose_not_include_jackpot:
        type: number
    type: object
  summary_report.SummaryReportDetailResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/summary_report.SummaryReportDetail'
      message:
        type: string
      success:
        type: boolean
    type: object
  summary_report.SummaryReportItem:
    properties:
      _id:
        properties:
          game_type_id:
            type: integer
          gaming_provider_code:
            type: string
        type: object
      commission_amount:
        type: number
      commission_net_turnover:
        type: number
      count_user:
        type: integer
      jackpot:
        type: number
      net_turn_over:
        type: number
      provider:
        allOf:
        - $ref: '#/definitions/summary_report.Provider'
        description: Provider information (joined)
      turn_over:
        type: number
      win_lose:
        type: number
      win_lose_not_include_jackpot:
        type: number
    type: object
  summary_report.SummaryReportResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/summary_report.SummaryReport'
      message:
        type: string
      success:
        type: boolean
    type: object
  system_setting.SettingOption:
    properties:
      is_default:
        type: boolean
      label:
        type: string
      value:
        type: string
    type: object
  system_setting.SettingOptionsResponse:
    properties:
      options:
        items:
          $ref: '#/definitions/system_setting.SettingOption'
        type: array
    type: object
  system_setting.UpdateLoginAttemptLimitRequest:
    properties:
      max_attempts:
        minimum: 0
        type: integer
    type: object
  theme_setting.SaveThemeRequest:
    properties:
      theme_value:
        type: string
    required:
    - theme_value
    type: object
  user.CreateUserRequest:
    properties:
      first_name:
        maxLength: 50
        minLength: 2
        type: string
      is_enable:
        type: boolean
      last_name:
        maxLength: 50
        minLength: 2
        type: string
      password:
        minLength: 8
        type: string
      user_role_id:
        type: integer
      username:
        type: string
    required:
    - first_name
    - is_enable
    - last_name
    - password
    - user_role_id
    - username
    type: object
  user.Status:
    enum:
    - active
    - inactive
    - suspended
    type: string
    x-enum-varnames:
    - StatusActive
    - StatusInactive
    - StatusSuspended
  user.UpdateUserRequest:
    properties:
      first_name:
        maxLength: 50
        minLength: 2
        type: string
      is_enable:
        type: boolean
      last_name:
        maxLength: 50
        minLength: 2
        type: string
      user_role_id:
        description: Only validates if value changes
        type: integer
      username:
        maxLength: 50
        minLength: 3
        type: string
    type: object
  user.UserDropdownItem:
    properties:
      id:
        type: integer
      name:
        description: first_name + last_name
        type: string
    type: object
  user.UserDropdownResponse:
    properties:
      users:
        items:
          $ref: '#/definitions/user.UserDropdownItem'
        type: array
    type: object
  user.UserResponse:
    properties:
      created_at:
        type: string
      first_name:
        type: string
      id:
        type: integer
      is_2fa_enabled:
        type: boolean
      is_enable:
        type: boolean
      last_name:
        type: string
      status:
        $ref: '#/definitions/user.Status'
      updated_at:
        type: string
      user_role_id:
        type: integer
      user_role_name:
        type: string
      username:
        type: string
    type: object
  user_transaction.CreateUserTransactionAdminDepositRequest:
    properties:
      amount:
        type: number
      date:
        type: string
      depositAccountId:
        type: integer
      description:
        type: string
      phoneOrUsername:
        type: string
      promotionId:
        type: integer
      slipUrl:
        description: Deposit slip image URL
        type: string
      time:
        type: string
    required:
    - amount
    - date
    - depositAccountId
    - phoneOrUsername
    - slipUrl
    - time
    type: object
  user_transaction.CreateUserTransactionTransferRequest:
    properties:
      banking_id:
        type: integer
      credit_amount:
        type: number
      description:
        type: string
      transfer_banking_id:
        type: integer
    required:
    - banking_id
    - credit_amount
    - transfer_banking_id
    type: object
  user_transaction.CreateUserTransactionWebDepositRequest:
    properties:
      amount:
        type: number
      date:
        type: string
      depositAccountId:
        type: integer
      description:
        type: string
      promotionId:
        type: integer
      slipUrl:
        type: string
      time:
        type: string
    required:
    - amount
    - date
    - depositAccountId
    - slipUrl
    - time
    type: object
  user_transaction.CreateUserTransactionWebWithdrawRequest:
    properties:
      accountName:
        type: string
      accountNumber:
        type: string
      amount:
        type: number
      description:
        type: string
      withdrawBankId:
        type: integer
    required:
    - accountName
    - accountNumber
    - amount
    - withdrawBankId
    type: object
  user_transaction.CreateUserTransactionWithdrawRequest:
    properties:
      credit_amount:
        type: number
      description:
        type: string
      phoneOrUsername:
        type: string
    required:
    - credit_amount
    - phoneOrUsername
    type: object
  user_transaction.UpdateUserTransactionStatusRequest:
    properties:
      confirm_admin_id:
        type: integer
      detail:
        type: string
      status_id:
        type: integer
    required:
    - status_id
    type: object
  user_transaction.UserTransactionResponse:
    properties:
      bankingId:
        type: integer
      bankingName:
        type: string
      bonusAmount:
        type: number
      confirmAdminId:
        type: integer
      confirmAdminName:
        type: string
      createdAt:
        type: string
      creditAfter:
        type: number
      creditAmount:
        type: number
      creditBack:
        type: number
      creditBefore:
        type: number
      date:
        type: string
      detail:
        type: string
      directionId:
        type: integer
      directionName:
        type: string
      id:
        type: integer
      memberCode:
        type: string
      memberId:
        type: integer
      promotionId:
        type: integer
      refId:
        type: string
      statusDetail:
        type: string
      statusId:
        type: integer
      statusName:
        type: string
      transferAt:
        type: string
      transferBankingId:
        type: integer
      transferBankingName:
        type: string
      typeId:
        type: integer
      typeName:
        type: string
      updatedAt:
        type: string
      username:
        type: string
    type: object
  userrole.CreateUserRoleRequest:
    properties:
      is_2fa:
        type: boolean
      is_enable:
        type: boolean
      is_lock_ip:
        type: boolean
      name:
        type: string
    required:
    - is_enable
    - name
    type: object
  userrole.Status:
    enum:
    - active
    - inactive
    - suspended
    type: string
    x-enum-varnames:
    - StatusActive
    - StatusInactive
    - StatusSuspended
  userrole.UpdateUserRoleRequest:
    properties:
      is_enable:
        type: boolean
      name:
        type: string
    required:
    - is_enable
    - name
    type: object
  userrole.UserRoleResponse:
    properties:
      created_at:
        type: string
      id:
        type: integer
      is_2fa:
        type: boolean
      is_enable:
        type: boolean
      is_lock_ip:
        type: boolean
      name:
        type: string
      position:
        type: integer
      status:
        $ref: '#/definitions/userrole.Status'
      updated_at:
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: Backend API service for Blacking platform with user management, member
    system, banking operations, and system configurations.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Blacking API
  version: "1.0"
paths:
  /admin/customer-followup:
    get:
      consumes:
      - application/json
      description: Get list of customers for follow-up with various filters including
        tag, status, date range, etc.
      parameters:
      - description: Limit (default 10, max 100)
        in: query
        name: limit
        type: integer
      - description: Offset (default 0)
        in: query
        name: offset
        type: integer
      - description: Search in member info
        in: query
        name: search
        type: string
      - description: Follow-up tag filter
        in: query
        name: tag
        type: string
      - description: Follow-up status filter
        in: query
        name: status
        type: string
      - description: Start date filter (YYYY-MM-DD HH:MM:SS)
        in: query
        name: start_datetime
        type: string
      - description: End date filter (YYYY-MM-DD HH:MM:SS)
        in: query
        name: end_datetime
        type: string
      - description: Partner ID filter
        in: query
        name: partner_id
        type: integer
      - description: Channel ID filter
        in: query
        name: channel_id
        type: integer
      - description: Platform ID filter
        in: query
        name: platform_id
        type: string
      - description: Admin who contacted filter
        in: query
        name: contacted_by
        type: integer
      - description: Member status filter (active, inactive, suspended, banned)
        in: query
        name: member_status
        type: string
      - description: 'Sort field: id, created_at, last_contact_at (default: id)'
        in: query
        name: order_by
        type: string
      - description: 'Sort direction: asc, desc (default: desc)'
        in: query
        name: order_dir
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Customers retrieved successfully
          schema:
            properties:
              data:
                properties:
                  customers:
                    items:
                      $ref: '#/definitions/customer_followup.CustomerFollowUpResponse'
                    type: array
                  limit:
                    type: integer
                  offset:
                    type: integer
                  total:
                    format: int64
                    type: integer
                type: object
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: List customers for follow-up
      tags:
      - Customer Follow-Up
  /admin/customer-followup/export-csv:
    get:
      description: Download CSV file of customer follow-up list with current filters
      parameters:
      - description: Search in member info
        in: query
        name: search
        type: string
      - description: Follow-up tag filter
        in: query
        name: tag
        type: string
      - description: Follow-up status filter
        in: query
        name: status
        type: string
      - description: Start date filter (YYYY-MM-DD HH:MM:SS)
        in: query
        name: start_datetime
        type: string
      - description: End date filter (YYYY-MM-DD HH:MM:SS)
        in: query
        name: end_datetime
        type: string
      - description: Partner ID filter
        in: query
        name: partner_id
        type: integer
      - description: Channel ID filter
        in: query
        name: channel_id
        type: integer
      - description: Platform ID filter
        in: query
        name: platform_id
        type: string
      - description: Admin who contacted filter
        in: query
        name: contacted_by
        type: integer
      produces:
      - text/csv
      responses:
        "200":
          description: CSV file download
          schema:
            type: file
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Export customer follow-up as CSV
      tags:
      - Customer Follow-Up
  /admin/customer-followup/export-old-customers-csv:
    get:
      description: Download CSV file of old customer follow-up list with current filters
        (based on last_online)
      parameters:
      - description: Search in member info
        in: query
        name: search
        type: string
      - description: Follow-up tag filter
        in: query
        name: tag
        type: string
      - description: Follow-up status filter
        in: query
        name: status
        type: string
      - description: Last online start date filter (YYYY-MM-DD HH:MM:SS)
        in: query
        name: start_datetime
        type: string
      - description: Last online end date filter (YYYY-MM-DD HH:MM:SS)
        in: query
        name: end_datetime
        type: string
      - description: Partner ID filter
        in: query
        name: partner_id
        type: integer
      - description: Channel ID filter
        in: query
        name: channel_id
        type: integer
      - description: Platform ID filter
        in: query
        name: platform_id
        type: string
      - description: Admin who contacted filter
        in: query
        name: contacted_by
        type: integer
      - description: Member status filter (active, inactive, suspended, banned)
        in: query
        name: member_status
        type: string
      produces:
      - text/csv
      responses:
        "200":
          description: CSV file download
          schema:
            type: file
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Export old customer follow-up as CSV
      tags:
      - Customer Follow-Up
  /admin/customer-followup/options:
    get:
      description: Get available follow-up tags and statuses for dropdown/select options
      produces:
      - application/json
      responses:
        "200":
          description: Options retrieved successfully
          schema:
            properties:
              data:
                properties:
                  statuses:
                    items:
                      type: object
                    type: array
                  tags:
                    items:
                      type: object
                    type: array
                type: object
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get follow-up options
      tags:
      - Customer Follow-Up
  /admin/customer-followup/search-old-customers:
    get:
      consumes:
      - application/json
      description: Get list of old customers for follow-up with various filters including
        tag, status, last_online date range, etc.
      parameters:
      - description: Limit (default 10, max 100)
        in: query
        name: limit
        type: integer
      - description: Offset (default 0)
        in: query
        name: offset
        type: integer
      - description: Search in member info
        in: query
        name: search
        type: string
      - description: Follow-up tag filter
        in: query
        name: tag
        type: string
      - description: Follow-up status filter
        in: query
        name: status
        type: string
      - description: Last online start date filter (YYYY-MM-DD HH:MM:SS)
        in: query
        name: start_datetime
        type: string
      - description: Last online end date filter (YYYY-MM-DD HH:MM:SS)
        in: query
        name: end_datetime
        type: string
      - description: Partner ID filter
        in: query
        name: partner_id
        type: integer
      - description: Channel ID filter
        in: query
        name: channel_id
        type: integer
      - description: Platform ID filter
        in: query
        name: platform_id
        type: string
      - description: Admin who contacted filter
        in: query
        name: contacted_by
        type: integer
      - description: Member status filter (active, inactive, suspended, banned)
        in: query
        name: member_status
        type: string
      - description: 'Sort field: id, last_online, last_contact_at (default: id)'
        in: query
        name: order_by
        type: string
      - description: 'Sort direction: asc, desc (default: desc)'
        in: query
        name: order_dir
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Old customers retrieved successfully
          schema:
            properties:
              data:
                properties:
                  customers:
                    items:
                      $ref: '#/definitions/customer_followup.CustomerFollowUpResponse'
                    type: array
                  limit:
                    type: integer
                  offset:
                    type: integer
                  total:
                    format: int64
                    type: integer
                type: object
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: List old customers for follow-up
      tags:
      - Customer Follow-Up
  /admin/customer-followup/track-call:
    post:
      consumes:
      - application/json
      description: Record a phone call to track customer follow-up
      parameters:
      - description: Track customer by call request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/customer_followup.TrackCustomerByCallRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Call tracked successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Track customer by call
      tags:
      - Customer Follow-Up
  /admin/customer-followup/track-sms:
    post:
      consumes:
      - application/json
      description: Record an SMS to track customer follow-up
      parameters:
      - description: Track customer by SMS request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/customer_followup.TrackCustomerBySMSRequest'
      produces:
      - application/json
      responses:
        "200":
          description: SMS tracked successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Track customer by SMS
      tags:
      - Customer Follow-Up
  /admin/customer-followup/update-remark:
    patch:
      consumes:
      - application/json
      description: Update member remark field
      parameters:
      - description: Update member remark request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/customer_followup.UpdateMemberRemarkRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Member remark updated successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Update member remark
      tags:
      - Customer Follow-Up
  /admin/customer-followup/update-status:
    patch:
      consumes:
      - application/json
      description: Update member follow-up status (contacted, unreachable, not_contacted)
      parameters:
      - description: Update follow-up status request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/customer_followup.UpdateFollowUpStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Follow-up status updated successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Update follow-up status
      tags:
      - Customer Follow-Up
  /admin/customer-followup/update-tag:
    patch:
      consumes:
      - application/json
      description: Set follow-up tag for member (cut_off, call_later, etc.)
      parameters:
      - description: Update follow-up tag request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/customer_followup.UpdateFollowUpTagRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Follow-up tag updated successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Set follow-up tag
      tags:
      - Customer Follow-Up
  /algorithm:
    get:
      description: Get a list of all available algorithms for banking operations
      produces:
      - application/json
      responses:
        "200":
          description: Algorithms retrieved successfully
          schema:
            properties:
              data:
                items:
                  type: object
                type: array
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: List algorithms
      tags:
      - Banking Management
  /api/admin/reports/referral/detail/{member_id}:
    get:
      consumes:
      - application/json
      description: Get detailed referral transactions for a specific member grouped
        by downline_member_id with sum
      parameters:
      - description: Member ID
        in: path
        name: member_id
        required: true
        type: integer
      - description: Start date (YYYY-MM-DD)
        format: date
        in: query
        name: start_date
        type: string
      - description: End date (YYYY-MM-DD)
        format: date
        in: query
        name: end_date
        type: string
      - description: Search by username or phone
        in: query
        name: search
        type: string
      - description: Exact phone match
        in: query
        name: phone
        type: string
      - description: Exact username match
        in: query
        name: username
        type: string
      - collectionFormat: multi
        description: Status filter
        in: query
        items:
          enum:
          - pending
          - success
          - cancel
          type: string
        name: status
        type: array
      - collectionFormat: multi
        description: Type filter
        in: query
        items:
          enum:
          - commission
          - withdraw
          - adjustment
          - bonus
          type: string
        name: type
        type: array
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 20
        description: Records per page
        in: query
        name: limit
        type: integer
      - default: desc
        description: Sort direction
        enum:
        - asc
        - desc
        in: query
        name: order_dir
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/report.ReferralReportDetailGroupedResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Get referral report detail grouped by downline
      tags:
      - Referral Reports
  /api/admin/reports/referral/detail/{member_id}/export:
    get:
      consumes:
      - application/json
      description: Export detailed referral transactions grouped by downline for a
        specific member to CSV file
      parameters:
      - description: Member ID
        in: path
        name: member_id
        required: true
        type: integer
      - description: Start date (YYYY-MM-DD)
        format: date
        in: query
        name: start_date
        type: string
      - description: End date (YYYY-MM-DD)
        format: date
        in: query
        name: end_date
        type: string
      - description: Search by username or phone
        in: query
        name: search
        type: string
      - description: Exact phone match
        in: query
        name: phone
        type: string
      - description: Exact username match
        in: query
        name: username
        type: string
      - collectionFormat: multi
        description: Status filter
        in: query
        items:
          enum:
          - pending
          - success
          - cancel
          type: string
        name: status
        type: array
      - collectionFormat: multi
        description: Type filter
        in: query
        items:
          enum:
          - commission
          - withdraw
          - adjustment
          - bonus
          type: string
        name: type
        type: array
      produces:
      - text/csv
      responses:
        "200":
          description: OK
          schema:
            type: file
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Export referral report grouped detail as CSV
      tags:
      - Referral Reports
  /api/admin/reports/referral/summary:
    get:
      consumes:
      - application/json
      description: Get referral report summary grouped by member with date filtering,
        search, and pagination
      parameters:
      - description: Start date (YYYY-MM-DD)
        format: date
        in: query
        name: start_date
        type: string
      - description: End date (YYYY-MM-DD)
        format: date
        in: query
        name: end_date
        type: string
      - description: Search by username or phone
        in: query
        name: search
        type: string
      - description: Exact phone match
        in: query
        name: phone
        type: string
      - description: Exact username match
        in: query
        name: username
        type: string
      - collectionFormat: multi
        description: Status filter
        in: query
        items:
          enum:
          - pending
          - success
          - cancel
          type: string
        name: status
        type: array
      - collectionFormat: multi
        description: Type filter
        in: query
        items:
          enum:
          - commission
          - withdraw
          - adjustment
          - bonus
          type: string
        name: type
        type: array
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 20
        description: Records per page
        in: query
        name: limit
        type: integer
      - default: member_id
        description: Sort field
        enum:
        - member_id
        - total_commission_amount
        - total_withdraw_amount
        - net_amount
        - transaction_count
        - created_at
        in: query
        name: order_by
        type: string
      - default: desc
        description: Sort direction
        enum:
        - asc
        - desc
        in: query
        name: order_dir
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/report.ReferralReportSummaryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Get referral report summary
      tags:
      - Referral Reports
  /api/admin/reports/referral/summary/export:
    get:
      consumes:
      - application/json
      description: Export referral report summary to CSV file with date filtering
        and search
      parameters:
      - description: Start date (YYYY-MM-DD)
        format: date
        in: query
        name: start_date
        type: string
      - description: End date (YYYY-MM-DD)
        format: date
        in: query
        name: end_date
        type: string
      - description: Search by username or phone
        in: query
        name: search
        type: string
      - description: Exact phone match
        in: query
        name: phone
        type: string
      - description: Exact username match
        in: query
        name: username
        type: string
      - collectionFormat: multi
        description: Status filter
        in: query
        items:
          enum:
          - pending
          - success
          - cancel
          type: string
        name: status
        type: array
      - collectionFormat: multi
        description: Type filter
        in: query
        items:
          enum:
          - commission
          - withdraw
          - adjustment
          - bonus
          type: string
        name: type
        type: array
      produces:
      - text/csv
      responses:
        "200":
          description: OK
          schema:
            type: file
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Export referral report summary as CSV
      tags:
      - Referral Reports
  /api/v1/channels/dropdown:
    get:
      description: Get list of active channels for dropdown/select options
      produces:
      - application/json
      responses:
        "200":
          description: Channels dropdown retrieved successfully
          schema:
            properties:
              data:
                properties:
                  channels:
                    items:
                      $ref: '#/definitions/channel.ChannelDropdownResponse'
                    type: array
                type: object
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get channels dropdown
      tags:
      - Channels
  /api/v1/dashboard:
    get:
      consumes:
      - application/json
      description: รวมข้อมูลทั้งหมดสำหรับหน้า dashboard พร้อม join ข้อมูล game types
        และ providers
      parameters:
      - description: Start date (YYYY-MM-DD)
        example: "2025-08-01"
        in: query
        name: date_from
        required: true
        type: string
      - description: End date (YYYY-MM-DD)
        example: "2025-08-19"
        in: query
        name: date_to
        required: true
        type: string
      - description: Dashboard type
        enum:
        - daily
        example: daily
        in: query
        name: type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dashboard.DashboardResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.ErrorResponse'
      summary: Get dashboard data
      tags:
      - Dashboard
  /api/v1/dashboard/bot:
    get:
      consumes:
      - application/json
      description: ข้อมูลความสำเร็จของ bot สำหรับหน้า dashboard
      parameters:
      - description: Round date (YYYY-MM-DD)
        example: "2025-07-16"
        in: query
        name: round_date
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dashboard.BotSuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.ErrorResponse'
      summary: Get bot success data
      tags:
      - Dashboard
  /api/v1/game-provider-groups:
    get:
      consumes:
      - application/json
      description: Get available game provider groups from game API
      produces:
      - application/json
      responses:
        "200":
          description: Game provider groups retrieved successfully
          schema:
            properties:
              data: {}
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get game provider groups
      tags:
      - Member Management
  /api/v1/jaijai/deposits:
    get:
      consumes:
      - application/json
      description: Get a paginated list of JaiJaiPay deposit transactions with optional
        filters
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Filter by status
        in: query
        name: status
        type: string
      - description: Start date filter (YYYY-MM-DD)
        in: query
        name: startDate
        type: string
      - description: End date filter (YYYY-MM-DD)
        in: query
        name: endDate
        type: string
      - description: Filter by currency
        in: query
        name: currency
        type: string
      - description: Filter by order ID
        in: query
        name: orderId
        type: string
      - description: Filter by customer reference ID
        in: query
        name: customerReferenceId
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Deposits retrieved successfully
          schema:
            properties:
              data:
                items:
                  type: object
                type: array
              metadata:
                type: object
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
      security:
      - BearerAuth: []
      summary: List JaiJaiPay deposits
      tags:
      - JaiJaiPay
    post:
      consumes:
      - application/json
      description: Create a new deposit transaction through JaiJaiPay payment gateway
      parameters:
      - description: Create deposit request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/jaijaipay.CreateDepositRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Deposit created successfully
          schema:
            properties:
              transaction_id:
                type: string
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
      security:
      - BearerAuth: []
      summary: Create JaiJaiPay deposit
      tags:
      - JaiJaiPay
  /api/v1/jaijai/deposits/{transactionId}:
    get:
      consumes:
      - application/json
      description: Get a specific JaiJaiPay deposit transaction by transaction ID
      parameters:
      - description: Transaction ID
        in: path
        name: transactionId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Deposit retrieved successfully
          schema:
            properties:
              amount:
                type: number
              status:
                type: string
              transaction_id:
                type: string
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
            type: object
        "404":
          description: Deposit not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get JaiJaiPay deposit by ID
      tags:
      - JaiJaiPay
  /api/v1/jaijai/deposits/cancel:
    post:
      consumes:
      - application/json
      description: Cancel a JaiJaiPay deposit transaction
      parameters:
      - description: Cancel deposit request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/jaijaipay.CancelDepositRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Deposit cancelled successfully
          schema:
            properties:
              status:
                type: string
              transaction_id:
                type: string
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
      security:
      - BearerAuth: []
      summary: Cancel JaiJaiPay deposit
      tags:
      - JaiJaiPay
  /api/v1/jaijai/withdrawals:
    post:
      consumes:
      - application/json
      description: Create a new withdrawal transaction through JaiJaiPay payment gateway
      parameters:
      - description: Create withdrawal request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/jaijaipay.CreateWithdrawalRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Withdrawal created successfully
          schema:
            properties:
              amount:
                type: number
              transaction_id:
                type: string
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
      security:
      - BearerAuth: []
      summary: Create JaiJaiPay withdrawal
      tags:
      - JaiJaiPay
  /api/v1/member-auth/invalidate-tokens:
    post:
      consumes:
      - application/json
      description: Invalidate all refresh tokens for the authenticated user, effectively
        logging them out from all devices
      produces:
      - application/json
      responses:
        "200":
          description: All tokens invalidated successfully
          schema:
            properties:
              message:
                type: string
              status:
                type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              message:
                type: string
              status:
                type: string
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              message:
                type: string
              status:
                type: string
            type: object
      security:
      - BearerAuth: []
      summary: Invalidate all tokens (logout)
      tags:
      - Authentication
  /api/v1/member-auth/refresh-token:
    post:
      consumes:
      - application/json
      description: Refresh expired JWT access token using refresh token for both admin
        and member users
      parameters:
      - description: Refresh token request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/http.RefreshTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Token refreshed successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/http.RefreshTokenResponse'
              status:
                type: string
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              status:
                type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              message:
                type: string
              status:
                type: string
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              message:
                type: string
              status:
                type: string
            type: object
      summary: Refresh authentication token
      tags:
      - Authentication
  /api/v1/member-auth/register/mode:
    get:
      consumes:
      - application/json
      description: Get current registration mode (OTP required or direct registration)
      produces:
      - application/json
      responses:
        "200":
          description: Registration mode retrieved successfully
          schema:
            properties:
              data:
                properties:
                  current_mode:
                    type: string
                type: object
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      summary: Get registration mode
      tags:
      - Member Registration
  /api/v1/member-auth/register/send-otp:
    post:
      consumes:
      - application/json
      description: Send OTP code to phone for member registration verification
      parameters:
      - description: OTP request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/member.VerifySendOTPRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OTP sent successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/member.RegistrationOTPResponse'
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      summary: Send registration OTP
      tags:
      - Member Registration
  /api/v1/member-auth/register/verify-otp:
    post:
      consumes:
      - application/json
      description: Verify OTP code for member registration
      parameters:
      - description: OTP verification request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/member.VerifyRegistrationOTPRequest'
      produces:
      - application/json
      responses:
        "201":
          description: OTP verified successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      summary: Verify registration OTP
      tags:
      - Member Registration
  /api/v1/member-groups:
    post:
      consumes:
      - multipart/form-data
      description: Create a new member group with optional image upload
      parameters:
      - description: Member group name
        in: formData
        name: name
        required: true
        type: string
      - description: Member group description
        in: formData
        name: description
        type: string
      - description: Member group image
        in: formData
        name: image_file
        type: file
      produces:
      - application/json
      responses:
        "201":
          description: Member group created successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/member_group.MemberGroupResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Create member group
      tags:
      - Member Group Management
  /api/v1/members/{id}:
    put:
      consumes:
      - application/json
      description: Update an existing member account by ID
      parameters:
      - description: Member ID
        in: path
        name: id
        required: true
        type: string
      - description: Update member request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/member.UpdateMemberRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Member updated successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/member.MemberResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Member not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Update member
      tags:
      - Member Management
  /api/v1/members/{id}/backfill-game-username:
    post:
      consumes:
      - application/json
      description: Backfills game_username by calling AG API to create user and get
        game_username
      parameters:
      - description: Member ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Game username backfilled successfully
          schema:
            properties:
              data:
                properties:
                  game_username:
                    type: string
                type: object
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Member not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Backfill game_username for member
      tags:
      - Member
  /api/v1/members/{id}/delete:
    post:
      consumes:
      - application/json
      description: Delete a member account by ID (soft delete)
      parameters:
      - description: Member ID
        in: path
        name: id
        required: true
        type: string
      - description: Delete member request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/member.DeleteMemberRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Member deleted successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Member not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Delete member
      tags:
      - Member Management
  /api/v1/members/{id}/game-lists/{provider}:
    get:
      consumes:
      - application/json
      description: Get available game lists for a specific member and provider from
        Game API
      parameters:
      - description: Member ID
        in: path
        name: id
        required: true
        type: string
      - description: Provider code (e.g., JILI, PRAGMATIC)
        in: path
        name: provider
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Game lists retrieved successfully
          schema:
            properties:
              data: {}
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Member not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get game lists for member by provider
      tags:
      - Member
  /api/v1/members/{id}/game-providers:
    get:
      consumes:
      - application/json
      description: Get available game providers for a specific member from Game API
      parameters:
      - description: Member ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Game providers retrieved successfully
          schema:
            properties:
              data: {}
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Member not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get game providers for member
      tags:
      - Member
  /api/v1/members/{id}/game-providers/{provider}:
    get:
      consumes:
      - application/json
      description: Get specific game provider information for a member from Game API
      parameters:
      - description: Member ID
        in: path
        name: id
        required: true
        type: string
      - description: Provider code (e.g., JILI, PRAGMATIC)
        in: path
        name: provider
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Game provider info retrieved successfully
          schema:
            properties:
              data: {}
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Member not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get game provider info for member
      tags:
      - Member
  /api/v1/members/game-username/{game_username}:
    get:
      description: Get a member by their game username
      parameters:
      - description: Member game username
        in: path
        name: game_username
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Member retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/member.MemberResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Member not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get member by game username
      tags:
      - Member Management
  /api/v1/members/username/{username}:
    get:
      description: Get a member by their username
      parameters:
      - description: Member username
        in: path
        name: username
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Member retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/member.MemberResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Member not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get member by username
      tags:
      - Member Management
  /api/v1/partners/dropdown:
    get:
      description: Get list of active partners for dropdown/select options with page-specific
        logic
      parameters:
      - description: Page name (e.g., customer-followup) - adds special ID=0 option
          for customer-followup
        in: query
        name: page
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Partners dropdown retrieved successfully
          schema:
            properties:
              data:
                properties:
                  partners:
                    items:
                      $ref: '#/definitions/member.PartnerDropdownResponse'
                    type: array
                type: object
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get partners dropdown
      tags:
      - Partners
  /api/v1/payment-gateway/crypto/{transactionId}:
    get:
      consumes:
      - application/json
      description: Retrieves current status and details of a crypto deposit transaction
      parameters:
      - description: Transaction ID from initiation
        in: path
        name: transactionId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/crypto_deposit.CryptoDepositResponse'
        "404":
          description: Not Found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
      security:
      - Bearer: []
      summary: Get deposit status
      tags:
      - CryptoDeposits
  /api/v1/payment-gateway/crypto/{transactionId}/logs:
    get:
      consumes:
      - application/json
      description: Retrieves detailed logs for a specific crypto deposit transaction
      parameters:
      - description: Transaction ID
        in: path
        name: transactionId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/crypto_deposit.CryptoDepositLog'
            type: array
        "404":
          description: Not Found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
      security:
      - Bearer: []
      summary: Get deposit logs
      tags:
      - CryptoDeposits
  /api/v1/payment-gateway/crypto/{transactionId}/step1:
    put:
      consumes:
      - application/json
      description: Reports Step 1 transaction results (User wallet → Backend wallet)
      parameters:
      - description: Transaction ID from initiation
        in: path
        name: transactionId
        required: true
        type: string
      - description: Step 1 update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/crypto_deposit.UpdateStep1Request'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/crypto_deposit.UpdateStepResponse'
        "400":
          description: Bad Request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "404":
          description: Not Found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "409":
          description: Conflict
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
      security:
      - Bearer: []
      summary: Update Step 1 status
      tags:
      - CryptoDeposits
  /api/v1/payment-gateway/crypto/{transactionId}/step2:
    put:
      consumes:
      - application/json
      description: Reports Step 2 transaction results (Backend wallet → Final recipient)
      parameters:
      - description: Transaction ID from initiation
        in: path
        name: transactionId
        required: true
        type: string
      - description: Step 2 update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/crypto_deposit.UpdateStep2Request'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/crypto_deposit.UpdateStepResponse'
        "400":
          description: Bad Request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "404":
          description: Not Found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "409":
          description: Conflict
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
      security:
      - Bearer: []
      summary: Update Step 2 status
      tags:
      - CryptoDeposits
  /api/v1/payment-gateway/crypto/config:
    get:
      consumes:
      - application/json
      description: Retrieves configuration needed for crypto deposits including wallet
        addresses and supported networks
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/crypto_deposit.CryptoDepositConfigResponse'
        "500":
          description: Internal Server Error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
      security:
      - Bearer: []
      summary: Get crypto deposit configuration
      tags:
      - CryptoDeposits
  /api/v1/payment-gateway/crypto/convert:
    get:
      consumes:
      - application/json
      description: Calculate how much THB for given token amount using stored conversion
        rates
      parameters:
      - description: Chain ID
        example: 84532
        in: query
        name: chainId
        required: true
        type: integer
      - description: Token contract address
        example: '"******************************************"'
        in: query
        name: tokenContract
        required: true
        type: string
      - description: Token amount
        example: 10
        in: query
        name: amount
        required: true
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/crypto_deposit.ConversionCalculationResponse'
        "400":
          description: Bad Request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "404":
          description: Not Found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
      security:
      - Bearer: []
      summary: Calculate token to THB conversion
      tags:
      - CryptoDeposits
  /api/v1/payment-gateway/crypto/deposits:
    get:
      consumes:
      - application/json
      description: Retrieves a paginated list of crypto deposit transactions
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 20
        description: Items per page
        in: query
        name: page_size
        type: integer
      - description: Filter by overall status
        in: query
        name: status
        type: string
      - description: Filter by Step 1 status
        in: query
        name: step1Status
        type: string
      - description: Filter by Step 2 status
        in: query
        name: step2Status
        type: string
      - description: Filter by blockchain network
        in: query
        name: chainId
        type: integer
      - description: Filter by token currency
        in: query
        name: currency
        type: string
      - description: Filter by customer username
        in: query
        name: customerUsername
        type: string
      - description: Start date filter (YYYY-MM-DD)
        in: query
        name: startDate
        type: string
      - description: End date filter (YYYY-MM-DD)
        in: query
        name: endDate
        type: string
      - default: created_at
        description: Sort field
        in: query
        name: sort_by
        type: string
      - default: desc
        description: Sort order (asc/desc)
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/crypto_deposit.CryptoDepositListResponse'
        "400":
          description: Bad Request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
      security:
      - Bearer: []
      summary: List crypto deposits
      tags:
      - CryptoDeposits
  /api/v1/payment-gateway/crypto/initiate:
    post:
      consumes:
      - application/json
      description: Creates a new crypto deposit transaction record and returns transaction
        ID for tracking
      parameters:
      - description: Initiate deposit request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/crypto_deposit.InitiateCryptoDepositRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/crypto_deposit.InitiateDepositResponse'
        "400":
          description: Bad Request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "409":
          description: Conflict
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
      security:
      - Bearer: []
      summary: Initiate crypto deposit
      tags:
      - CryptoDeposits
  /api/v1/payment-gateway/crypto/tokens:
    get:
      consumes:
      - application/json
      description: Retrieves all available tokens with their conversion rates
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/crypto_deposit.ChainToken'
            type: array
        "500":
          description: Internal Server Error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
      security:
      - Bearer: []
      summary: List available chain tokens
      tags:
      - CryptoDeposits
  /api/v1/payment-gateway/crypto/tokens/{tokenId}/rate:
    put:
      consumes:
      - application/json
      description: Updates the conversion rate for a specific token (admin only)
      parameters:
      - description: Token ID
        in: path
        name: tokenId
        required: true
        type: integer
      - description: New rate
        in: body
        name: request
        required: true
        schema:
          properties:
            rate:
              type: number
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            properties:
              message:
                type: string
            type: object
        "400":
          description: Bad Request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "404":
          description: Not Found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
      security:
      - Bearer: []
      summary: Update token conversion rate
      tags:
      - CryptoDeposits
  /api/v1/payment-gateway/crypto/wallets/{walletAddress}/balance:
    put:
      consumes:
      - application/json
      description: Updates the balance of a backend wallet for monitoring purposes
      parameters:
      - description: Wallet address
        in: path
        name: walletAddress
        required: true
        type: string
      - description: Balance update
        in: body
        name: request
        required: true
        schema:
          properties:
            ethBalance:
              type: number
            tokenBalance:
              type: number
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            properties:
              message:
                type: string
            type: object
        "400":
          description: Bad Request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
            type: object
      security:
      - Bearer: []
      summary: Update backend wallet balance
      tags:
      - CryptoDeposits
  /api/v1/permissions:
    get:
      consumes:
      - application/json
      description: Get all permissions with optional search
      parameters:
      - description: Search term
        in: query
        name: search
        type: string
      - default: 50
        description: Limit
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get all permissions
      tags:
      - permissions
  /api/v1/permissions/check/{permission_key}:
    get:
      consumes:
      - application/json
      description: Check what actions the current user's role can perform on a specific
        permission
      parameters:
      - description: Permission Key
        in: path
        name: permission_key
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Check user permission
      tags:
      - permissions
  /api/v1/permissions/groups/{group_id}/permissions:
    get:
      consumes:
      - application/json
      description: Get all permissions for a specific permission group
      parameters:
      - description: Permission Group ID
        in: path
        name: group_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get permissions by group
      tags:
      - permissions
  /api/v1/permissions/matrix:
    get:
      consumes:
      - application/json
      description: Get all permissions grouped by permission groups for UI display,
        optionally with user role permissions
      parameters:
      - description: User Role ID to include permission status
        in: query
        name: user_role_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get permission matrix
      tags:
      - permissions
  /api/v1/permissions/my-permissions:
    get:
      consumes:
      - application/json
      description: Get all permissions for the current user's role
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Check my permissions
      tags:
      - permissions
  /api/v1/permissions/user-roles/{user_role_id}:
    get:
      consumes:
      - application/json
      description: Get permissions matrix with user role permission data
      parameters:
      - description: User Role ID
        in: path
        name: user_role_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get user role permissions
      tags:
      - permissions
  /api/v1/permissions/user-roles/{user_role_id}/bulk-update:
    put:
      consumes:
      - application/json
      description: Update  multiple permissions for a user role
      parameters:
      - description: User Role ID
        in: path
        name: user_role_id
        required: true
        type: integer
      - description: Bulk update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service.BulkUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Bulk update user role permissions
      tags:
      - permissions
  /api/v1/platforms/{id}:
    get:
      description: Get a specific platform by its ID
      parameters:
      - description: Platform ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Platform retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/platform.PlatformResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Platform not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get platform by ID
      tags:
      - Platform Management
  /api/v1/platforms/types:
    get:
      description: Get all available platform types
      produces:
      - application/json
      responses:
        "200":
          description: Platform types retrieved successfully
          schema:
            properties:
              data:
                properties:
                  types:
                    items:
                      type: string
                    type: array
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get platform types
      tags:
      - Platform Management
  /api/v1/reports/summary-bet:
    get:
      consumes:
      - application/json
      description: ข้อมูลรายงานสรุปพร้อมฟิลเตอร์ (รองรับทุก parameters)
      parameters:
      - description: Registration date (YYYY-MM-DD)
        example: "2025-01-01"
        in: query
        name: date_register
        type: string
      - description: Start date (YYYY-MM-DD)
        example: "2025-01-01"
        in: query
        name: date_from
        type: string
      - description: End date (YYYY-MM-DD)
        example: "2025-01-31"
        in: query
        name: date_to
        type: string
      - description: End time (HH:MM:SS)
        example: "23:59:59"
        in: query
        name: end_time
        type: string
      - description: First deposit filter
        example: "yes"
        in: query
        name: first_deposit
        type: string
      - description: Game type ID
        example: 1
        in: query
        name: game_type_id
        type: integer
      - description: Include partner data
        example: true
        in: query
        name: is_include_partner
        type: boolean
      - description: Partner ID
        example: 1
        in: query
        name: partner_id
        type: integer
      - description: Phone number
        example: "**********"
        in: query
        name: phone
        type: string
      - description: Provider code
        example: PG
        in: query
        name: provider_code
        type: string
      - description: Start time (HH:MM:SS)
        example: "00:00:00"
        in: query
        name: start_time
        type: string
      - description: User code
        example: USER001
        in: query
        name: user_code
        type: string
      - description: Username
        example: testuser
        in: query
        name: username
        type: string
      - description: Sort order
        example: asc
        in: query
        name: sort
        type: string
      - description: Sort by field
        example: name
        in: query
        name: sortBy
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/summary_report.SummaryReportResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.ErrorResponse'
      summary: Get summary reports with filters
      tags:
      - Reports
  /api/v1/reports/summary-bet-by-member:
    get:
      consumes:
      - application/json
      description: ข้อมูลรายงานสรุปเดิมพันแยกตาม Member พร้อมฟิลเตอร์ (รองรับทุก parameters)
      parameters:
      - description: Registration date
        example: "2025-01-01"
        in: query
        name: date_register
        type: string
      - description: Date search range
        example: 2025-08-01 to 2025-08-18
        in: query
        name: date_search
        type: string
      - description: First deposit filter
        example: "yes"
        in: query
        name: first_deposit
        type: string
      - description: Game type ID
        example: 1
        in: query
        name: game_type_id
        type: integer
      - description: Partner ID
        example: 1
        in: query
        name: partner_id
        type: integer
      - description: Phone number
        example: "**********"
        in: query
        name: phone
        type: string
      - description: Provider code
        example: PGT
        in: query
        name: provider_code
        type: string
      - description: Show all records
        example: 1
        in: query
        name: showAll
        type: integer
      - description: User code
        example: ZAB1E1PM15362
        in: query
        name: user_code
        type: string
      - description: User groups
        example: vip
        in: query
        name: user_groups
        type: string
      - description: Username
        example: testuser
        in: query
        name: username
        type: string
      - description: Sort order
        example: desc
        in: query
        name: sort
        type: string
      - description: Sort by field
        example: created_at
        in: query
        name: sortBy
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/summary_bet_by_member.SummaryBetByMemberResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.ErrorResponse'
      summary: Get summary bet by member reports with filters
      tags:
      - Reports
  /api/v1/reports/summary-bet-by-member/{member_id}:
    get:
      consumes:
      - application/json
      description: ข้อมูลรายละเอียดรายงานสรุปเดิมพันแยกตาม Member
      parameters:
      - description: Member ID
        example: 15362
        in: path
        name: member_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/summary_bet_by_member.SummaryBetByMemberDetailResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.ErrorResponse'
      summary: Get summary bet by member report detail
      tags:
      - Reports
  /api/v1/reports/summary-bet/{id}:
    get:
      consumes:
      - application/json
      description: ข้อมูลรายละเอียดรายงานสรุป
      parameters:
      - description: Report ID
        example: 1
        in: path
        name: id
        required: true
        type: integer
      - description: Start date (YYYY-MM-DD)
        example: "2025-01-01"
        in: query
        name: date_from
        required: true
        type: string
      - description: End date (YYYY-MM-DD)
        example: "2025-01-31"
        in: query
        name: date_to
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/summary_report.SummaryReportDetailResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.ErrorResponse'
      summary: Get summary report detail
      tags:
      - Reports
  /api/v1/reports/summary-bet/legacy:
    get:
      consumes:
      - application/json
      description: ใช้ GetSummaryReportsWithFilter แทน - ข้อมูลรายงานสรุปหน้าหลัก
      parameters:
      - description: Start date (YYYY-MM-DD)
        example: "2025-01-01"
        in: query
        name: date_from
        required: true
        type: string
      - description: End date (YYYY-MM-DD)
        example: "2025-01-31"
        in: query
        name: date_to
        required: true
        type: string
      - description: Report category
        enum:
        - deposit
        - withdraw
        - transfer
        - commission
        in: query
        name: category
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/summary_report.SummaryReportResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.ErrorResponse'
      summary: Get summary reports (deprecated)
      tags:
      - Reports
  /api/v1/users:
    get:
      description: Get a paginated list of users with optional filtering
      parameters:
      - default: 10
        description: Number of items per page
        in: query
        name: limit
        type: integer
      - default: 0
        description: Number of items to skip
        in: query
        name: offset
        type: integer
      - description: Search term for filtering users
        in: query
        name: search
        type: string
      - description: Filter by user role ID
        in: query
        name: user_role_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Users retrieved successfully
          schema:
            properties:
              data:
                properties:
                  pagination:
                    properties:
                      limit:
                        type: integer
                      offset:
                        type: integer
                      total:
                        type: integer
                    type: object
                  users:
                    items:
                      $ref: '#/definitions/user.UserResponse'
                    type: array
                type: object
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: List users
      tags:
      - User Management
  /api/v1/users/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a user account by ID
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User deleted successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: User not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Delete user
      tags:
      - User Management
    put:
      consumes:
      - application/json
      description: Update an existing user account by ID
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      - description: Update user request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user.UpdateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: User updated successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/user.UserResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: User not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Update user
      tags:
      - User Management
  /api/v1/users/{id}/activate:
    patch:
      consumes:
      - application/json
      description: Activate a user account by ID
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User activated successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: User not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Activate user
      tags:
      - User Management
  /api/v1/users/{id}/deactivate:
    patch:
      consumes:
      - application/json
      description: Deactivate a user account by ID
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User deactivated successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: User not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Deactivate user
      tags:
      - User Management
  /api/v1/users/{id}/password:
    patch:
      consumes:
      - application/json
      description: Change a user's password by ID
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      - description: Change password request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/http.ChangePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Password changed successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: User not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Change user password
      tags:
      - User Management
  /api/v1/users/{id}/suspend:
    patch:
      consumes:
      - application/json
      description: Suspend a user account by ID
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User suspended successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: User not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Suspend user
      tags:
      - User Management
  /auth/2fa-setup/verify:
    post:
      consumes:
      - application/json
      description: Verify the 2FA code during setup process
      parameters:
      - description: 2FA verification request
        in: body
        name: request
        required: true
        schema:
          properties:
            code:
              type: string
            token:
              type: string
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: 2FA code verified successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Invalid token or code
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      summary: Verify 2FA setup code
      tags:
      - Two-Factor Authentication
  /auth/2fa/disable:
    post:
      consumes:
      - application/json
      description: Disable two-factor authentication for a user
      parameters:
      - description: 2FA disable request
        in: body
        name: request
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: 2FA disabled successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Disable 2FA
      tags:
      - Two-Factor Authentication
  /auth/2fa/status/{user_id}:
    get:
      description: Get the 2FA status for a specific user
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 2FA status retrieved successfully
          schema:
            properties:
              data:
                type: object
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get 2FA status
      tags:
      - Two-Factor Authentication
  /auth/login-2fa:
    post:
      consumes:
      - application/json
      description: Complete login process using 2FA verification code
      parameters:
      - description: 2FA login request
        in: body
        name: request
        required: true
        schema:
          properties:
            code:
              type: string
            token:
              type: string
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Login successful with JWT token
          schema:
            properties:
              data:
                type: object
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Invalid token or code
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      summary: Login with 2FA
      tags:
      - Two-Factor Authentication
  /auto-bot:
    get:
      description: Get a list of all available auto bots for banking operations
      produces:
      - application/json
      responses:
        "200":
          description: Auto bots retrieved successfully
          schema:
            properties:
              data:
                items:
                  type: object
                type: array
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: List auto bots
      tags:
      - Banking Management
  /bank-transaction-slip:
    get:
      consumes:
      - application/json
      description: Get a paginated list of bank transaction slips with filters
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Filter by member ID
        in: query
        name: memberId
        type: integer
      - description: Filter by status (1=Pending, 2=Approved, 3=Rejected, 4=Cancelled)
        in: query
        name: status
        type: integer
      - description: Filter by transaction ID
        in: query
        name: transactionId
        type: integer
      - description: Filter from date (YYYY-MM-DD)
        in: query
        name: fromDate
        type: string
      - description: Filter to date (YYYY-MM-DD)
        in: query
        name: toDate
        type: string
      - description: Search in member username, fullname, account names
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.SuccessWithPagination'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/bank_transaction_slip.Response'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Error'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Error'
      summary: Get paginated list of bank transaction slips
      tags:
      - Bank Transaction Slip
    post:
      consumes:
      - application/json
      description: Create a new bank transaction slip for a member
      parameters:
      - description: Create request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/bank_transaction_slip.CreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/response.Success'
            - properties:
                data:
                  $ref: '#/definitions/bank_transaction_slip.Response'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Error'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Error'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Error'
      summary: Create a new bank transaction slip
      tags:
      - Bank Transaction Slip
  /bank-transaction-slip/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a bank transaction slip (only if pending or cancelled)
      parameters:
      - description: Slip ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Success'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Error'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Error'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Error'
      summary: Delete a bank transaction slip
      tags:
      - Bank Transaction Slip
    get:
      consumes:
      - application/json
      description: Get a bank transaction slip by its ID
      parameters:
      - description: Slip ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Success'
            - properties:
                data:
                  $ref: '#/definitions/bank_transaction_slip.Response'
              type: object
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Error'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Error'
      summary: Get bank transaction slip by ID
      tags:
      - Bank Transaction Slip
    put:
      consumes:
      - application/json
      description: Update a bank transaction slip (only if status is pending)
      parameters:
      - description: Slip ID
        in: path
        name: id
        required: true
        type: integer
      - description: Update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/bank_transaction_slip.UpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Success'
            - properties:
                data:
                  $ref: '#/definitions/bank_transaction_slip.Response'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Error'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Error'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Error'
      summary: Update a bank transaction slip
      tags:
      - Bank Transaction Slip
  /bank-transaction-slip/{id}/status:
    patch:
      consumes:
      - application/json
      description: Update the status of a bank transaction slip
      parameters:
      - description: Slip ID
        in: path
        name: id
        required: true
        type: integer
      - description: Status update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/bank_transaction_slip.UpdateStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Success'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Error'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Error'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Error'
      summary: Update bank transaction slip status
      tags:
      - Bank Transaction Slip
  /bank-transaction-slip/member/{memberId}:
    get:
      consumes:
      - application/json
      description: Get bank transaction slips for a specific member
      parameters:
      - description: Member ID
        in: path
        name: memberId
        required: true
        type: integer
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Filter by status
        in: query
        name: status
        type: integer
      - description: Filter from date (YYYY-MM-DD)
        in: query
        name: fromDate
        type: string
      - description: Filter to date (YYYY-MM-DD)
        in: query
        name: toDate
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.SuccessWithPagination'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/bank_transaction_slip.Response'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Error'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Error'
      summary: Get bank transaction slips by member ID
      tags:
      - Bank Transaction Slip
  /bank-transaction-slip/status-counts:
    get:
      consumes:
      - application/json
      description: Get count of bank transaction slips by status
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Error'
      summary: Get bank transaction slip status counts
      tags:
      - Bank Transaction Slip
  /bank-transaction-slip/transaction/{transactionId}:
    get:
      consumes:
      - application/json
      description: Get a bank transaction slip by its transaction ID
      parameters:
      - description: Transaction ID
        in: path
        name: transactionId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Success'
            - properties:
                data:
                  $ref: '#/definitions/bank_transaction_slip.Response'
              type: object
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Error'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Error'
      summary: Get bank transaction slip by transaction ID
      tags:
      - Bank Transaction Slip
  /banking:
    get:
      description: Get a list of banking accounts with optional search and pagination
      parameters:
      - description: Search term for bank name or code
        in: query
        name: search
        type: string
      - default: 50
        description: Limit
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Banking accounts retrieved successfully
          schema:
            properties:
              data:
                items:
                  type: object
                type: array
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: List banking accounts
      tags:
      - Banking Management
  /commission-groups:
    post:
      consumes:
      - application/json
      description: Create a new commission group for managing commission rates
      parameters:
      - description: Commission group creation request
        in: body
        name: request
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "201":
          description: Commission group created successfully
          schema:
            properties:
              data:
                type: object
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Create commission group
      tags:
      - Commission Management
  /commission-groups/{id}:
    get:
      description: Get a specific commission group by its ID
      parameters:
      - description: Commission Group ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Commission group retrieved successfully
          schema:
            properties:
              data:
                type: object
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Commission group not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get commission group by ID
      tags:
      - Commission Management
  /commission-groups/default:
    get:
      description: Get the default commission group configuration
      produces:
      - application/json
      responses:
        "200":
          description: Default commission group retrieved successfully
          schema:
            properties:
              data:
                type: object
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get default commission group
      tags:
      - Commission Management
  /deposit-account:
    get:
      description: Get a list of deposit accounts with optional search and pagination
      parameters:
      - description: Limit
        in: query
        name: limit
        type: integer
      - description: Offset
        in: query
        name: offset
        type: integer
      - description: Search term
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Deposit accounts retrieved successfully
          schema:
            properties:
              data:
                items:
                  type: object
                type: array
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: List deposit accounts
      tags:
      - Account Management
    post:
      consumes:
      - application/json
      description: Create a new deposit account for processing deposits
      parameters:
      - description: Deposit account creation request
        in: body
        name: request
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "201":
          description: Deposit account created successfully
          schema:
            properties:
              Message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Create deposit account
      tags:
      - Account Management
  /deposit-account/{id}:
    delete:
      description: Delete a deposit account by ID
      parameters:
      - description: Deposit Account ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Deposit account deleted successfully
          schema:
            properties:
              Message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Deposit account not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Delete deposit account
      tags:
      - Account Management
    get:
      description: Get a specific deposit account by its ID
      parameters:
      - description: Deposit Account ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Deposit account retrieved successfully
          schema:
            properties:
              data:
                type: object
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Deposit account not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get deposit account by ID
      tags:
      - Account Management
    put:
      consumes:
      - application/json
      description: Update an existing deposit account
      parameters:
      - description: Deposit Account ID
        in: path
        name: id
        required: true
        type: string
      - description: Deposit account update request
        in: body
        name: request
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Deposit account updated successfully
          schema:
            properties:
              Message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Deposit account not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Update deposit account
      tags:
      - Account Management
  /health:
    get:
      description: Get the health status of the API service
      produces:
      - application/json
      responses:
        "200":
          description: Service is healthy
          schema:
            properties:
              service:
                type: string
              status:
                type: string
              timestamp:
                type: string
              version:
                type: string
            type: object
      summary: Health check
      tags:
      - Health
  /health/live:
    get:
      description: Check if the service is alive and running
      produces:
      - application/json
      responses:
        "200":
          description: Service is alive
          schema:
            properties:
              status:
                type: string
              timestamp:
                type: string
            type: object
      summary: Liveness check
      tags:
      - Health
  /health/ready:
    get:
      description: Check if the service is ready to handle requests
      produces:
      - application/json
      responses:
        "200":
          description: Service is ready
          schema:
            properties:
              status:
                type: string
              timestamp:
                type: string
            type: object
      summary: Readiness check
      tags:
      - Health
  /holding-account:
    get:
      description: Get a list of holding accounts with optional search and pagination
      parameters:
      - description: Limit
        in: query
        name: limit
        type: integer
      - description: Offset
        in: query
        name: offset
        type: integer
      - description: Search term
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Holding accounts retrieved successfully
          schema:
            properties:
              holding_accounts:
                items:
                  type: object
                type: array
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: List holding accounts
      tags:
      - Account Management
    post:
      consumes:
      - application/json
      description: Create a new holding account for managing funds
      parameters:
      - description: Holding account creation request
        in: body
        name: request
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "201":
          description: Holding account created successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Create holding account
      tags:
      - Account Management
  /holding-account/{id}:
    get:
      description: Get a specific holding account by its ID
      parameters:
      - description: Holding Account ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Holding account retrieved successfully
          schema:
            properties:
              holding_account:
                type: object
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Holding account not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get holding account by ID
      tags:
      - Account Management
  /languages:
    get:
      description: Get a list of all supported languages in the system
      produces:
      - application/json
      responses:
        "200":
          description: Languages retrieved successfully
          schema:
            properties:
              data:
                items:
                  type: object
                type: array
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      summary: Get supported languages
      tags:
      - Language Management
  /languages/{code}:
    get:
      description: Get language information by language code
      parameters:
      - description: Language Code
        in: path
        name: code
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Language retrieved successfully
          schema:
            properties:
              data:
                type: object
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Language not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      summary: Get language by code
      tags:
      - Language Management
  /languages/active:
    get:
      description: Get a list of currently active languages
      produces:
      - application/json
      responses:
        "200":
          description: Active languages retrieved successfully
          schema:
            properties:
              data:
                items:
                  type: object
                type: array
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      summary: Get active languages
      tags:
      - Language Management
  /lock-credit:
    post:
      consumes:
      - application/json
      description: Create a new credit lock for a user's promotion
      parameters:
      - description: Lock credit data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/promotion_web.LockCreditPromotionCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Lock credit created successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request data
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create lock credit
      tags:
      - Admin - Lock Credit
  /lock-credit/withdraw-list:
    get:
      consumes:
      - application/json
      description: Get a paginated list of credit withdraw locks with filtering options
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Filter by user ID
        in: query
        name: userId
        type: integer
      - description: Search by member code, name, or phone
        in: query
        name: search
        type: string
      - description: Start date filter (YYYY-MM-DD)
        in: query
        name: startDate
        type: string
      - description: End date filter (YYYY-MM-DD)
        in: query
        name: endDate
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Lock credit withdraw list retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid query parameters
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get lock credit withdraw list
      tags:
      - Admin - Lock Credit
  /member-auth/game:
    post:
      consumes:
      - application/json
      description: Get provider member code by fetching member data from external
        API and filtering by provider
      parameters:
      - description: Game provider request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/member.GameProviderRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Provider member code retrieved successfully
          schema:
            properties:
              data:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Provider not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      summary: Get game provider data
      tags:
      - Member Auth
  /member-auth/register:
    post:
      consumes:
      - application/json
      description: Register a new member account (supports both direct and OTP-based
        registration based on system settings)
      parameters:
      - description: Registration request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/member.CompleteRegistrationRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Registration completed successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/member.MemberResponse'
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      summary: Register member
      tags:
      - Member Registration
  /member-auth/register/complete:
    post:
      consumes:
      - application/json
      deprecated: true
      description: Complete member registration after OTP verification - use /register
        instead
      parameters:
      - description: Registration request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/member.CompleteRegistrationRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Registration completed successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/member.MemberResponse'
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      summary: Complete OTP-based registration (deprecated)
      tags:
      - Member Registration
  /member-auth/register/direct:
    post:
      consumes:
      - application/json
      deprecated: true
      description: Direct member registration without OTP - use /register instead
      parameters:
      - description: Registration request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/member.CompleteRegistrationRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Registration completed successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/member.MemberResponse'
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      summary: Direct registration (deprecated)
      tags:
      - Member Registration
  /member/me:
    get:
      description: Get current authenticated member information from JWT token in
        cookie
      produces:
      - application/json
      responses:
        "200":
          description: Current user retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/member.MemberResponse'
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - MemberAuth: []
      summary: Get current user
      tags:
      - Member Area
  /members:
    post:
      consumes:
      - application/json
      description: Create a new member account by admin
      parameters:
      - description: Create member request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/member.CreateMemberRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Member created successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/member.MemberResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Create member
      tags:
      - Member Management
  /members/{id}:
    get:
      description: Get a specific member by their ID
      parameters:
      - description: Member ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Member retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/member.MemberResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Member not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get member by ID
      tags:
      - Member Management
  /members/{id}/change-partner:
    post:
      consumes:
      - application/json
      description: Change member partner (refer_user_id) by admin with audit logging
        and action remark
      parameters:
      - description: Member ID
        in: path
        name: id
        required: true
        type: string
      - description: Change partner request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/member.ChangeMemberPartnerRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Partner changed successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Member not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Change member partner by admin
      tags:
      - Member Management
  /members/{id}/change-password:
    post:
      consumes:
      - application/json
      description: Change member password by admin with audit logging
      parameters:
      - description: Member ID
        in: path
        name: id
        required: true
        type: string
      - description: Change password request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/member.ChangeMemberPasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Password changed successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Member not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Change member password by admin
      tags:
      - Member Management
  /members/{id}/suspend:
    patch:
      consumes:
      - application/json
      description: Toggle member suspension status (suspend if active, unsuspend if
        suspended) by admin with audit logging and action remark preservation
      parameters:
      - description: Member ID
        in: path
        name: id
        required: true
        type: string
      - description: Suspend/Unsuspend member request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/member.SuspendMemberRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Member status toggled successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Member not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Toggle member suspension status by admin
      tags:
      - Member Management
  /members/{id}/update-bank-info:
    post:
      consumes:
      - application/json
      description: Update member's bank information (bank code, bank number, names,
        TrueMoney username) by admin with audit logging
      parameters:
      - description: Member ID
        in: path
        name: id
        required: true
        type: string
      - description: Bank info request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/member.UpdateBankInfoRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Bank info updated successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Member not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Update member bank information by admin
      tags:
      - Member Management
  /members/status-counts:
    get:
      description: Get count of members grouped by status (active, suspended, banned)
        and total count
      produces:
      - application/json
      responses:
        "200":
          description: Member status counts retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/member.MemberStatusCountsResponse'
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get member status counts
      tags:
      - Member Management
  /payment-gateway/jaijaipay/balance:
    get:
      consumes:
      - application/json
      description: Get balance from JaiJaiPay using database configuration
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get JaiJaiPay balance
      tags:
      - Payment Gateway
  /payment-gateway/jaijaipay/deposits:
    post:
      consumes:
      - application/json
      description: Create a deposit transaction with JaiJaiPay using database configuration
      parameters:
      - description: Deposit request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Create JaiJaiPay deposit
      tags:
      - Payment Gateway
  /payment-gateway/jaijaipay/test:
    post:
      consumes:
      - application/json
      description: Test JaiJaiPay connection using database configuration
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Create JaiJaiPay client
      tags:
      - Payment Gateway
  /payment-gateway/jaijaipay/webhooks/resend:
    post:
      consumes:
      - application/json
      description: Resend webhook for a specific transaction using database configuration
      parameters:
      - description: Resend webhook request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Resend webhook
      tags:
      - Payment Gateway
  /payment-gateway/providers:
    get:
      consumes:
      - application/json
      description: Get list of all payment gateway providers
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get payment gateway providers
      tags:
      - Payment Gateway
    post:
      consumes:
      - application/json
      description: Create a new payment gateway provider
      parameters:
      - description: Create provider request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/payment_gateway_account.PaymentGatewayAccountRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Create payment gateway provider
      tags:
      - Payment Gateway
  /payment-gateway/providers/{id}/status:
    put:
      consumes:
      - application/json
      description: Update the active status of a payment gateway provider
      parameters:
      - description: Provider ID
        in: path
        name: id
        required: true
        type: integer
      - description: Status update request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update payment gateway provider status
      tags:
      - Payment Gateway
  /payment-gateway/providers/active:
    get:
      consumes:
      - application/json
      description: Get list of active payment gateway providers
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get active payment gateway providers
      tags:
      - Payment Gateway
  /payment-gateway/transactions:
    get:
      consumes:
      - application/json
      description: Get list of payment gateway transactions with filtering and pagination
      parameters:
      - description: Transaction ID
        in: query
        name: transaction_id
        type: string
      - description: Internal Reference
        in: query
        name: internal_reference
        type: string
      - description: Provider
        in: query
        name: provider
        type: string
      - description: Status
        in: query
        name: status
        type: string
      - description: Transaction Type
        in: query
        name: transaction_type
        type: string
      - description: Customer Username
        in: query
        name: customer_username
        type: string
      - description: Customer Reference
        in: query
        name: customer_reference
        type: string
      - description: Customer Bank Account
        in: query
        name: customer_bank_account
        type: string
      - description: Customer Bank Name
        in: query
        name: customer_bank_name
        type: string
      - description: Payment Gateway Account ID
        in: query
        name: payment_gateway_account_id
        type: integer
      - description: Minimum Amount
        in: query
        name: min_amount
        type: number
      - description: Maximum Amount
        in: query
        name: max_amount
        type: number
      - description: Created From Date (YYYY-MM-DD)
        in: query
        name: created_from_date
        type: string
      - description: Created To Date (YYYY-MM-DD)
        in: query
        name: created_to_date
        type: string
      - description: Initiated From Date (YYYY-MM-DD)
        in: query
        name: initiated_from_date
        type: string
      - description: Initiated To Date (YYYY-MM-DD)
        in: query
        name: initiated_to_date
        type: string
      - description: Completed From Date (YYYY-MM-DD)
        in: query
        name: completed_from_date
        type: string
      - description: Completed To Date (YYYY-MM-DD)
        in: query
        name: completed_to_date
        type: string
      - description: Has Error
        in: query
        name: has_error
        type: boolean
      - description: Sort By (id, transaction_id, provider, status, transaction_type,
          amount, created_at, updated_at, initiated_at, completed_at, customer_username)
        in: query
        name: sort_by
        type: string
      - description: Sort Order (asc, desc)
        in: query
        name: sort_order
        type: string
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Page size (default: 10, max: 100)'
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get payment gateway transactions
      tags:
      - Payment Gateway
  /payment-gateway/transactions/{id}:
    get:
      consumes:
      - application/json
      description: Get a specific payment gateway transaction by transaction ID
      parameters:
      - description: Transaction ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get payment gateway transaction by ID
      tags:
      - Payment Gateway
  /payment-method:
    get:
      description: Get a list of all available payment methods
      produces:
      - application/json
      responses:
        "200":
          description: Payment methods retrieved successfully
          schema:
            properties:
              data:
                items:
                  type: object
                type: array
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: List payment methods
      tags:
      - Payment Management
    post:
      consumes:
      - application/json
      description: Create a new payment method for processing transactions
      parameters:
      - description: Payment method creation request
        in: body
        name: request
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "201":
          description: Payment method created successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Create payment method
      tags:
      - Payment Management
  /payment-method/{id}:
    delete:
      description: Delete a payment method by ID
      parameters:
      - description: Payment Method ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Payment method deleted successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Payment method not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Delete payment method
      tags:
      - Payment Management
    get:
      description: Get a specific payment method by its ID
      parameters:
      - description: Payment Method ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Payment method retrieved successfully
          schema:
            properties:
              data:
                type: object
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Payment method not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get payment method by ID
      tags:
      - Payment Management
    put:
      consumes:
      - application/json
      description: Update an existing payment method
      parameters:
      - description: Payment Method ID
        in: path
        name: id
        required: true
        type: string
      - description: Payment method update request
        in: body
        name: request
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Payment method updated successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Payment method not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Update payment method
      tags:
      - Payment Management
  /payment-method/{id}/status:
    put:
      consumes:
      - application/json
      description: Update the status of a payment method (enable/disable)
      parameters:
      - description: Payment Method ID
        in: path
        name: id
        required: true
        type: string
      - description: Status update request
        in: body
        name: request
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Payment method status updated successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Payment method not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Update payment method status
      tags:
      - Payment Management
  /promotion-web:
    get:
      consumes:
      - application/json
      description: Get a paginated list of promotions with optional filtering
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Search term
        in: query
        name: search
        type: string
      - description: Filter by status ID
        in: query
        name: promotionWebStatusId
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Promotion list retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid query parameters
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get promotion list
      tags:
      - Admin - Promotion Management
    post:
      consumes:
      - application/json
      description: Create a new promotion web campaign with specified parameters
      parameters:
      - description: Promotion creation data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/promotion_web.PromotionWebCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Promotion created successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request data
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create a new promotion
      tags:
      - Admin - Promotion Management
  /promotion-web/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a promotion by ID
      parameters:
      - description: Promotion ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Promotion deleted successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid promotion ID
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Promotion not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Delete promotion
      tags:
      - Admin - Promotion Management
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific promotion
      parameters:
      - description: Promotion ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Promotion retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid promotion ID
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Promotion not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get promotion by ID
      tags:
      - Admin - Promotion Management
    put:
      consumes:
      - application/json
      description: Update an existing promotion with new data
      parameters:
      - description: Promotion ID
        in: path
        name: id
        required: true
        type: integer
      - description: Promotion update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/promotion_web.PromotionWebUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Promotion updated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request data
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Promotion not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update promotion
      tags:
      - Admin - Promotion Management
  /promotion-web/{id}/cancel:
    post:
      consumes:
      - application/json
      description: Cancel an existing promotion web campaign
      parameters:
      - description: Promotion ID
        in: path
        name: id
        required: true
        type: integer
      - description: Cancel promotion data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/promotion_web.CancelPromotionWebRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Promotion canceled successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request data
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Promotion not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Cancel a promotion
      tags:
      - Admin - Promotion Management
  /promotion-web/options/bonus-conditions:
    get:
      consumes:
      - application/json
      description: Get all available bonus conditions for promotion configuration
      produces:
      - application/json
      responses:
        "200":
          description: Promotion bonus conditions retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get promotion bonus conditions
      tags:
      - Admin - Options & Lookups
  /promotion-web/options/bonus-types:
    get:
      consumes:
      - application/json
      description: Get all available bonus types for promotion configuration
      produces:
      - application/json
      responses:
        "200":
          description: Promotion bonus types retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get promotion bonus types
      tags:
      - Admin - Options & Lookups
  /promotion-web/options/date-types:
    get:
      consumes:
      - application/json
      description: Get all available date types for promotion configuration
      produces:
      - application/json
      responses:
        "200":
          description: Promotion date types retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get promotion date types
      tags:
      - Admin - Options & Lookups
  /promotion-web/options/statuses:
    get:
      consumes:
      - application/json
      description: Get all available promotion statuses for configuration
      produces:
      - application/json
      responses:
        "200":
          description: Promotion statuses retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get promotion statuses
      tags:
      - Admin - Options & Lookups
  /promotion-web/options/turnover-types:
    get:
      consumes:
      - application/json
      description: Get all available turnover types for promotion configuration
      produces:
      - application/json
      responses:
        "200":
          description: Promotion turnover types retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get promotion turnover types
      tags:
      - Admin - Options & Lookups
  /promotion-web/options/types:
    get:
      consumes:
      - application/json
      description: Get all available promotion types for configuration
      produces:
      - application/json
      responses:
        "200":
          description: Promotion types retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get promotion types
      tags:
      - Admin - Options & Lookups
  /promotion-web/public:
    get:
      consumes:
      - application/json
      description: Get a list of all publicly available promotions (no authentication
        required)
      produces:
      - application/json
      responses:
        "200":
          description: Public promotions retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Get public promotions
      tags:
      - Public - Promotions
  /promotion-web/public/{id}:
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific public promotion
      parameters:
      - description: Promotion ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Public promotion retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid promotion ID
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Promotion not found or not publicly available
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Get public promotion by ID
      tags:
      - Public - Promotions
  /promotion-web/public/slides/active:
    get:
      consumes:
      - application/json
      description: Get a list of active promotions for display as slides (public access)
      produces:
      - application/json
      responses:
        "200":
          description: Active promotion slides retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Get active promotion slides
      tags:
      - Public - Promotions
  /promotion-web/slide-list:
    get:
      consumes:
      - application/json
      description: Get a list of active promotions for display as slides
      produces:
      - application/json
      responses:
        "200":
          description: Promotion slide list retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get promotion slide list
      tags:
      - Admin - Promotion Management
  /promotion-web/sort-priority-order:
    put:
      consumes:
      - application/json
      description: Update the priority order of promotions by dragging and dropping
      parameters:
      - description: Sort request data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/promotion_web.DragSortRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Priority order updated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request data
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Sort promotion priority order
      tags:
      - Admin - Promotion Management
  /promotion-web/upload/cover:
    post:
      consumes:
      - multipart/form-data
      description: Upload a cover image for a promotion
      parameters:
      - description: Cover image file
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: Promotion cover uploaded successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid file or file validation failed
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Upload promotion cover
      tags:
      - Admin - Promotion Management
  /promotion-web/user/collect:
    post:
      consumes:
      - application/json
      description: Allow a user to collect/claim a promotion
      parameters:
      - description: Promotion collection data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/promotion_web.CollectPromotionRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Promotion collected successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request data or promotion not eligible
          schema:
            additionalProperties: true
            type: object
        "401":
          description: User authentication required
          schema:
            additionalProperties: true
            type: object
        "403":
          description: User access required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Promotion not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Collect a promotion
      tags:
      - User - Promotion Collection
  /promotion-web/user/my-promotions:
    get:
      consumes:
      - application/json
      description: Get all promotions available to the authenticated user with their
        status
      produces:
      - application/json
      responses:
        "200":
          description: User promotions retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "401":
          description: User authentication required
          schema:
            additionalProperties: true
            type: object
        "403":
          description: User access required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get user's promotions
      tags:
      - User - Promotion Collection
  /promotion-web/users:
    get:
      consumes:
      - application/json
      description: Get a paginated list of user promotions with filtering options
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Filter by promotion ID
        in: query
        name: promotionWebId
        type: integer
      - description: Filter by user promotion status
        in: query
        name: promotionWebUserStatusId
        type: integer
      - description: Search by user details
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User promotion list retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid query parameters
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Admin access required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get user promotion list
      tags:
      - Admin - User Management
  /referrals/income:
    get:
      consumes:
      - application/json
      description: Get referral income statistics and history for the current user
        with pagination for recent transactions
      parameters:
      - description: 'Number of months for history (default: 12, max: 24)'
        in: query
        maximum: 24
        minimum: 1
        name: months
        type: integer
      - description: 'Page number for recent transactions (default: 1)'
        in: query
        minimum: 1
        name: page
        type: integer
      - description: 'Records per page for recent transactions (default: 10, max:
          100)'
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Income data retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/referral.IncomeResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get referral income
      tags:
      - Referral
  /referrals/members:
    get:
      consumes:
      - application/json
      description: Get downlines list with today's commission analytics and chart
        data with pagination support
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        minimum: 1
        name: page
        type: integer
      - description: 'Records per page (default: 20, max: 100)'
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Members data retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/referral.ReferralMembersResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get referral members data
      tags:
      - Referral
  /referrals/overview:
    get:
      consumes:
      - application/json
      description: Get referral overview statistics for the current user
      produces:
      - application/json
      responses:
        "200":
          description: Overview retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/referral.OverviewResponse'
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get referral overview
      tags:
      - Referral
  /referrals/tutorial-faq:
    get:
      consumes:
      - application/json
      description: Get tutorials and FAQs for the referral system
      produces:
      - application/json
      responses:
        "200":
          description: Tutorial and FAQ data retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/referral.TutorialFAQResponse'
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get referral tutorials and FAQs
      tags:
      - Referral
  /referrals/view/{referral_code}:
    post:
      consumes:
      - application/json
      description: Increment referral view count when someone visits referral link
      parameters:
      - description: Referral code to increment view count
        in: path
        name: referral_code
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: View count incremented successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Referral code not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      summary: Increment referral view count
      tags:
      - Referral
  /referrals/withdraw:
    post:
      consumes:
      - application/json
      description: Withdraw commission balance for the current member
      parameters:
      - description: Withdraw request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/referral.WithdrawCommissionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Withdraw request processed successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/referral.WithdrawCommissionResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Withdraw commission balance
      tags:
      - Referral
  /sms-provider:
    get:
      description: Get a list of SMS providers with optional search and pagination
      parameters:
      - description: Limit
        in: query
        name: limit
        type: integer
      - description: Offset
        in: query
        name: offset
        type: integer
      - description: Search term
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: SMS providers retrieved successfully
          schema:
            properties:
              data:
                items:
                  type: object
                type: array
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: List SMS providers
      tags:
      - Communication Management
    post:
      consumes:
      - application/json
      description: Create a new SMS provider for sending SMS messages
      parameters:
      - description: SMS provider creation request
        in: body
        name: request
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "201":
          description: SMS provider created successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Create SMS provider
      tags:
      - Communication Management
  /sms-provider/{id}:
    get:
      description: Get a specific SMS provider by its ID
      parameters:
      - description: SMS Provider ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: SMS provider retrieved successfully
          schema:
            properties:
              data:
                type: object
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: SMS provider not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get SMS provider by ID
      tags:
      - Communication Management
  /system/login-attempt-limit:
    get:
      description: Get the current login attempt limit setting
      produces:
      - application/json
      responses:
        "200":
          description: Login attempt limit retrieved successfully
          schema:
            properties:
              data:
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get login attempt limit
      tags:
      - System Settings
    put:
      consumes:
      - application/json
      description: Update the login attempt limit setting
      parameters:
      - description: Update login attempt limit request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/system_setting.UpdateLoginAttemptLimitRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Login attempt limit updated successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Update login attempt limit
      tags:
      - System Settings
  /system/referral-commission-options:
    get:
      description: Get available options for referral commission frequency (day/week/month)
      produces:
      - application/json
      responses:
        "200":
          description: Referral commission options retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/system_setting.SettingOptionsResponse'
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      summary: Get referral commission frequency options
      tags:
      - System Settings
  /system/settings:
    get:
      description: Get a list of all system settings
      produces:
      - application/json
      responses:
        "200":
          description: System settings retrieved successfully
          schema:
            properties:
              data:
                items:
                  type: object
                type: array
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: List all system settings
      tags:
      - System Settings
  /system/theme:
    get:
      description: Get the current theme settings configuration
      produces:
      - application/json
      responses:
        "200":
          description: Theme settings retrieved successfully
          schema:
            properties:
              data:
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get theme settings
      tags:
      - System Settings
    post:
      consumes:
      - application/json
      description: Save or update theme settings configuration
      parameters:
      - description: Save theme request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/theme_setting.SaveThemeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Theme settings saved successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Save theme settings
      tags:
      - System Settings
  /user-roles:
    get:
      description: Get a paginated list of user roles with optional search
      parameters:
      - default: 10
        description: Limit
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset
        in: query
        name: offset
        type: integer
      - description: Search term
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User roles retrieved successfully
          schema:
            properties:
              data:
                type: object
              pagination:
                type: object
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: List user roles
      tags:
      - User Role Management
    post:
      consumes:
      - application/json
      description: Create a new user role with permissions
      parameters:
      - description: Create user role request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/userrole.CreateUserRoleRequest'
      produces:
      - application/json
      responses:
        "201":
          description: User role created successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/userrole.UserRoleResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Create user role
      tags:
      - User Role Management
  /user-roles/{id}:
    delete:
      description: Delete a user role by ID
      parameters:
      - description: User Role ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User role deleted successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: User role not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Delete user role
      tags:
      - User Role Management
    get:
      description: Get a specific user role by its ID
      parameters:
      - description: User Role ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User role retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/userrole.UserRoleResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: User role not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get user role by ID
      tags:
      - User Role Management
    put:
      consumes:
      - application/json
      description: Update an existing user role
      parameters:
      - description: User Role ID
        in: path
        name: id
        required: true
        type: string
      - description: Update user role request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/userrole.UpdateUserRoleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: User role updated successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/userrole.UserRoleResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: User role not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Update user role
      tags:
      - User Role Management
  /user-roles/dropdown:
    get:
      description: Get a list of user roles formatted for dropdown selection
      produces:
      - application/json
      responses:
        "200":
          description: User roles for dropdown retrieved successfully
          schema:
            properties:
              data:
                items:
                  type: object
                type: array
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: List user roles for dropdown
      tags:
      - User Role Management
  /user-transaction/deposit:
    post:
      consumes:
      - application/json
      description: Create a new deposit transaction
      parameters:
      - description: Deposit request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user_transaction.CreateUserTransactionAdminDepositRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Deposit created successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
      summary: Create deposit transaction
      tags:
      - UserTransaction
  /user-transaction/deposit/{id}:
    get:
      consumes:
      - application/json
      description: Retrieve a specific deposit transaction by its ID
      parameters:
      - description: Transaction ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Deposit transaction retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/user_transaction.UserTransactionResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "404":
          description: Transaction not found
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
      summary: Get deposit transaction by ID
      tags:
      - UserTransaction
  /user-transaction/deposit/member/{memberId}/page:
    get:
      consumes:
      - application/json
      description: Retrieve paginated deposit transactions for a specific user
      parameters:
      - description: User ID
        in: path
        name: userId
        required: true
        type: integer
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Status ID filter
        in: query
        name: statusId
        type: integer
      - description: Date filter (YYYY-MM-DD)
        in: query
        name: date
        type: string
      - description: Banking ID filter
        in: query
        name: bankingId
        type: integer
      - description: Start amount filter
        in: query
        name: startAmount
        type: number
      - description: End amount filter
        in: query
        name: endAmount
        type: number
      - description: Exact amount filter
        in: query
        name: amount
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: User deposit transactions retrieved successfully
          schema:
            $ref: '#/definitions/response.SuccessWithPagination'
        "400":
          description: Bad request
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "404":
          description: User not found
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
      summary: Get deposit transactions by user ID
      tags:
      - UserTransaction
  /user-transaction/deposit/page:
    get:
      consumes:
      - application/json
      description: Retrieve paginated deposit transactions with filters
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Status ID filter
        in: query
        name: statusId
        type: integer
      - description: Date filter (YYYY-MM-DD)
        in: query
        name: date
        type: string
      - description: Username filter
        in: query
        name: username
        type: string
      - description: Member code filter
        in: query
        name: membercode
        type: string
      - description: User bank ID filter
        in: query
        name: userbankid
        type: integer
      - description: Banking ID filter
        in: query
        name: bankingid
        type: integer
      - description: Start amount filter
        in: query
        name: startamount
        type: number
      - description: End amount filter
        in: query
        name: endamount
        type: number
      - description: Exact amount filter
        in: query
        name: amount
        type: number
      - description: Admin filter
        in: query
        name: admin
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Deposit transactions retrieved successfully
          schema:
            $ref: '#/definitions/response.SuccessWithPagination'
        "400":
          description: Bad request
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
      summary: Get paginated deposit transactions
      tags:
      - UserTransaction
  /user-transaction/deposit/status/{id}:
    put:
      consumes:
      - application/json
      description: Update the status of a deposit transaction
      parameters:
      - description: Transaction ID
        in: path
        name: id
        required: true
        type: integer
      - description: Status update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user_transaction.UpdateUserTransactionStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Status updated successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "404":
          description: Transaction not found
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
      summary: Update deposit transaction status
      tags:
      - UserTransaction
  /user-transaction/transfer:
    post:
      consumes:
      - application/json
      description: Create a new transfer transaction
      parameters:
      - description: Transfer request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user_transaction.CreateUserTransactionTransferRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Transfer created successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/user_transaction.UserTransactionResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
      summary: Create transfer transaction
      tags:
      - UserTransaction
  /user-transaction/transfer/{id}:
    get:
      consumes:
      - application/json
      description: Retrieve a specific transfer transaction by its ID
      parameters:
      - description: Transaction ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Transfer transaction retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/user_transaction.UserTransactionResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "404":
          description: Transaction not found
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
      summary: Get transfer transaction by ID
      tags:
      - UserTransaction
  /user-transaction/transfer/page:
    get:
      consumes:
      - application/json
      description: Retrieve paginated transfer transactions with filters
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Status ID filter
        in: query
        name: statusId
        type: integer
      - description: Date filter (YYYY-MM-DD)
        in: query
        name: date
        type: string
      - description: Exact amount filter
        in: query
        name: amount
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: Transfer transactions retrieved successfully
          schema:
            $ref: '#/definitions/response.SuccessWithPagination'
        "400":
          description: Bad request
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
      summary: Get paginated transfer transactions
      tags:
      - UserTransaction
  /user-transaction/transfer/status/{id}:
    put:
      consumes:
      - application/json
      description: Update the status of a transfer transaction
      parameters:
      - description: Transaction ID
        in: path
        name: id
        required: true
        type: integer
      - description: Status update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user_transaction.UpdateUserTransactionStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Status updated successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "404":
          description: Transaction not found
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
      summary: Update transfer transaction status
      tags:
      - UserTransaction
  /user-transaction/upload-slip:
    post:
      consumes:
      - multipart/form-data
      description: Upload a transaction slip image file
      parameters:
      - description: Slip image file
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: Slip uploaded successfully
          schema:
            properties:
              data:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Upload transaction slip
      tags:
      - UserTransaction
  /user-transaction/web/deposit:
    post:
      consumes:
      - application/json
      description: Create a new web deposit transaction
      parameters:
      - description: Web deposit request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user_transaction.CreateUserTransactionWebDepositRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Web deposit created successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/user_transaction.UserTransactionResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
      summary: Create web deposit transaction
      tags:
      - UserTransaction
  /user-transaction/web/withdraw:
    post:
      consumes:
      - application/json
      description: Create a new web withdraw transaction
      parameters:
      - description: Web withdraw request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user_transaction.CreateUserTransactionWebWithdrawRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Web withdraw created successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/user_transaction.UserTransactionResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
      summary: Create web withdraw transaction
      tags:
      - UserTransaction
  /user-transaction/withdraw:
    post:
      consumes:
      - application/json
      description: Create a new withdraw transaction
      parameters:
      - description: Withdraw request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user_transaction.CreateUserTransactionWithdrawRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Withdraw created successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/user_transaction.UserTransactionResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
      summary: Create withdraw transaction
      tags:
      - UserTransaction
  /user-transaction/withdraw/{id}:
    get:
      consumes:
      - application/json
      description: Retrieve a specific withdraw transaction by its ID
      parameters:
      - description: Transaction ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Withdraw transaction retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/user_transaction.UserTransactionResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "404":
          description: Transaction not found
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
      summary: Get withdraw transaction by ID
      tags:
      - UserTransaction
  /user-transaction/withdraw/member/{memberId}/page:
    get:
      consumes:
      - application/json
      description: Retrieve paginated withdraw transactions for a specific user
      parameters:
      - description: User ID
        in: path
        name: userId
        required: true
        type: integer
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Status ID filter
        in: query
        name: statusId
        type: integer
      - description: Date filter (YYYY-MM-DD)
        in: query
        name: date
        type: string
      - description: Banking ID filter
        in: query
        name: bankingId
        type: integer
      - description: Start amount filter
        in: query
        name: startAmount
        type: number
      - description: End amount filter
        in: query
        name: endAmount
        type: number
      - description: Exact amount filter
        in: query
        name: amount
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: User withdraw transactions retrieved successfully
          schema:
            $ref: '#/definitions/response.SuccessWithPagination'
        "400":
          description: Bad request
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "404":
          description: User not found
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
      summary: Get withdraw transactions by user ID
      tags:
      - UserTransaction
  /user-transaction/withdraw/page:
    get:
      consumes:
      - application/json
      description: Retrieve paginated withdraw transactions with filters
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Status ID filter
        in: query
        name: statusId
        type: integer
      - description: Date filter (YYYY-MM-DD)
        in: query
        name: date
        type: string
      - description: Username filter
        in: query
        name: username
        type: string
      - description: Member code filter
        in: query
        name: membercode
        type: string
      - description: User bank ID filter
        in: query
        name: userbankid
        type: integer
      - description: Banking ID filter
        in: query
        name: bankingid
        type: integer
      - description: Start amount filter
        in: query
        name: startamount
        type: number
      - description: End amount filter
        in: query
        name: endamount
        type: number
      - description: Exact amount filter
        in: query
        name: amount
        type: number
      - description: IP address filter
        in: query
        name: ip
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Withdraw transactions retrieved successfully
          schema:
            $ref: '#/definitions/response.SuccessWithPagination'
        "400":
          description: Bad request
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
      summary: Get paginated withdraw transactions
      tags:
      - UserTransaction
  /user-transaction/withdraw/status/{id}:
    put:
      consumes:
      - application/json
      description: Update the status of a withdraw transaction
      parameters:
      - description: Transaction ID
        in: path
        name: id
        required: true
        type: integer
      - description: Status update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user_transaction.UpdateUserTransactionStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Status updated successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "404":
          description: Transaction not found
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                properties:
                  code:
                    type: string
                  message:
                    type: string
                type: object
              success:
                type: boolean
            type: object
      summary: Update withdraw transaction status
      tags:
      - UserTransaction
  /user/lock-credit/withdraw-check:
    get:
      consumes:
      - application/json
      description: Check if the authenticated user has any locked credit that prevents
        withdrawal
      produces:
      - application/json
      responses:
        "200":
          description: Lock credit status checked successfully
          schema:
            additionalProperties: true
            type: object
        "401":
          description: User authentication required
          schema:
            additionalProperties: true
            type: object
        "403":
          description: User access required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Check lock credit withdraw
      tags:
      - User - Lock Credit
  /users:
    post:
      consumes:
      - application/json
      description: Create a new user account with admin privileges
      parameters:
      - description: Create user request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user.CreateUserRequest'
      produces:
      - application/json
      responses:
        "201":
          description: User created successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/user.UserResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Create user
      tags:
      - User Management
  /users/{id}:
    get:
      description: Get a specific user by their ID
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/user.UserResponse'
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: User not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get user by ID
      tags:
      - User Management
  /users/dropdown:
    get:
      description: Get list of users formatted for dropdown selection (id, name).
        If page=member, adds special entry "ลูกค้าสมัครเอง" with id=0
      parameters:
      - description: Page context (e.g., 'member' to add special entry)
        in: query
        name: page
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Users for dropdown retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/user.UserDropdownResponse'
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get users for dropdown
      tags:
      - User Management
  /users/me:
    get:
      description: Get the currently authenticated user information
      produces:
      - application/json
      responses:
        "200":
          description: Current user retrieved successfully
          schema:
            properties:
              data:
                $ref: '#/definitions/user.UserResponse'
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get current user
      tags:
      - User Management
  /withdraw-account:
    get:
      description: Get a list of withdraw accounts with optional search and pagination
      parameters:
      - description: Limit
        in: query
        name: limit
        type: integer
      - description: Offset
        in: query
        name: offset
        type: integer
      - description: Search term
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Withdraw accounts retrieved successfully
          schema:
            properties:
              data:
                items:
                  type: object
                type: array
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: List withdraw accounts
      tags:
      - Account Management
    post:
      consumes:
      - application/json
      description: Create a new withdraw account for processing withdrawals
      parameters:
      - description: Withdraw account creation request
        in: body
        name: request
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "201":
          description: Withdraw account created successfully
          schema:
            properties:
              message:
                type: string
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Create withdraw account
      tags:
      - Account Management
  /withdraw-account/{id}:
    get:
      description: Get a specific withdraw account by its ID
      parameters:
      - description: Withdraw Account ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Withdraw account retrieved successfully
          schema:
            properties:
              data:
                type: object
              success:
                type: boolean
            type: object
        "400":
          description: Bad request
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "401":
          description: Unauthorized
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "404":
          description: Withdraw account not found
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
        "500":
          description: Internal server error
          schema:
            properties:
              error:
                type: string
              message:
                type: string
              success:
                type: boolean
            type: object
      security:
      - BearerAuth: []
      summary: Get withdraw account by ID
      tags:
      - Account Management
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
