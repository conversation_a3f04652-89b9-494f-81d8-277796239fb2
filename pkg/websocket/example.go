package websocket

import (
	"log"
	"time"

	"blacking-api/internal/config"
)

// Example demonstrates how to use the WebSocket client
func Example() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// Create WebSocket client
	client := NewWSClient(cfg.WebSocket.URL)

	// Set up event handlers
	client.OnConnect(func() {
		log.Println("Connected to socket.irich.info")
	})

	client.OnDisconnect(func(err error) {
		if err != nil {
			log.Printf("Disconnected with error: %v", err)
		} else {
			log.Println("Disconnected")
		}
	})

	client.OnMessage(func(message []byte) {
		log.Printf("Received: %s", string(message))
	})

	client.OnError(func(err error) {
		log.Printf("WebSocket error: %v", err)
	})

	// Connect
	if err := client.Connect(); err != nil {
		log.Fatal("Connection failed:", err)
	}

	// Wait for connection to establish
	time.Sleep(2 * time.Second)

	// Example usage of events
	channelID := 123

	// Join a channel
	if err := client.EmitJoin(channelID); err != nil {
		log.Printf("Failed to emit join: %v", err)
	}

	// Send deposit status
	if err := client.EmitDeposit(channelID, true); err != nil {
		log.Printf("Failed to emit deposit: %v", err)
	}

	// Send withdraw status
	if err := client.EmitWithdraw(channelID, false); err != nil {
		log.Printf("Failed to emit withdraw: %v", err)
	}

	// Leave channel
	if err := client.EmitLeave(channelID); err != nil {
		log.Printf("Failed to emit leave: %v", err)
	}

	// Close connection when done
	defer client.Close()
}

// ExampleInService shows how to integrate WebSocket client in a service
func ExampleInService() {
	// This example shows how you might use the WebSocket client
	// within a service to notify about banking transactions

	cfg, _ := config.Load()
	wsClient := NewWSClient(cfg.WebSocket.URL)

	// Set up handlers
	wsClient.OnConnect(func() {
		log.Println("Banking WebSocket service connected")
	})

	// Connect
	if err := wsClient.Connect(); err != nil {
		log.Printf("WebSocket connection failed: %v", err)
		return
	}

	// Example: Notify about a successful deposit
	channelID := 123 // This would come from your business logic

	// In your deposit processing logic:
	// if deposit.Status == "completed" {
	//     wsClient.EmitDeposit(channelID, true)
	// }

	// In your withdrawal processing logic:
	// if withdrawal.Status == "completed" {
	//     wsClient.EmitWithdraw(channelID, true)
	// } else if withdrawal.Status == "failed" {
	//     wsClient.EmitWithdraw(channelID, false)
	// }

	wsClient.EmitDeposit(channelID, true)
	wsClient.EmitWithdraw(channelID, true)
}
