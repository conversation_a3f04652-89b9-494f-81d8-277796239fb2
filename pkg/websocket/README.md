# WebSocket Client for socket.irich.info

This package provides a Go WebSocket client for connecting to `wss://socket.irich.info` and emitting Socket.IO events.

## Features

- Socket.IO compatible WebSocket client
- Automatic connection handling and reconnection
- Event emission for `join`, `leave`, `deposit`, and `withdraw` events
- Configurable via environment variables
- Thread-safe operations
- Connection health monitoring

## Configuration

The WebSocket URL is configured via the `WEBSOCKET_URL` environment variable:

```bash
WEBSOCKET_URL=wss://socket.irich.info
```

## Usage

### Basic Usage

```go
package main

import (
    "log"
    "blacking-api/internal/config"
    "blacking-api/pkg/websocket"
)

func main() {
    // Load configuration
    cfg, err := config.Load()
    if err != nil {
        log.Fatal("Failed to load config:", err)
    }

    // Create WebSocket client
    client := websocket.NewWSClient(cfg.WebSocket.URL)

    // Set up event handlers
    client.OnConnect(func() {
        log.Println("Connected!")
    })

    client.OnMessage(func(message []byte) {
        log.Printf("Received: %s", string(message))
    })

    // Connect to server
    if err := client.Connect(); err != nil {
        log.Fatal("Connection failed:", err)
    }

    // Emit events
    channelID := 123
    
    // Join a channel
    client.EmitJoin(channelID)
    
    // Notify about deposit
    client.EmitDeposit(channelID, true)  // true = success
    
    // Notify about withdrawal
    client.EmitWithdraw(channelID, false) // false = failed
    
    // Leave channel
    client.EmitLeave(channelID)

    // Close when done
    defer client.Close()
}
```

### Integration with Services

```go
type BankingService struct {
    wsClient *websocket.WSClient
    // other fields...
}

func NewBankingService(cfg *config.Config) *BankingService {
    wsClient := websocket.NewWSClient(cfg.WebSocket.URL)
    
    // Set up connection
    wsClient.OnConnect(func() {
        log.Println("Banking WebSocket connected")
    })
    
    wsClient.Connect()
    
    return &BankingService{
        wsClient: wsClient,
    }
}

func (s *BankingService) ProcessDeposit(memberID int, amount float64) error {
    // Your deposit processing logic...
    
    // Notify WebSocket about successful deposit
    if err := s.wsClient.EmitDeposit(memberID, true); err != nil {
        log.Printf("Failed to notify deposit: %v", err)
    }
    
    return nil
}

func (s *BankingService) ProcessWithdrawal(memberID int, amount float64) error {
    // Your withdrawal processing logic...
    
    success := true // Based on your business logic
    
    // Notify WebSocket about withdrawal result
    if err := s.wsClient.EmitWithdraw(memberID, success); err != nil {
        log.Printf("Failed to notify withdrawal: %v", err)
    }
    
    return nil
}
```

## API Reference

### Client Creation

```go
client := websocket.NewWSClient("wss://socket.irich.info")
```

### Event Handlers

```go
// Connection established
client.OnConnect(func() {
    // Handle connection
})

// Connection lost
client.OnDisconnect(func(err error) {
    // Handle disconnection
})

// Message received
client.OnMessage(func(message []byte) {
    // Handle incoming messages
})

// Error occurred
client.OnError(func(err error) {
    // Handle errors
})
```

### Event Emission

```go
// Join a channel
client.EmitJoin(channelID int) error

// Leave a channel
client.EmitLeave(channelID int) error

// Notify about deposit transaction
client.EmitDeposit(channelID int, status bool) error

// Notify about withdrawal transaction
client.EmitWithdraw(channelID int, status bool) error
```

### Connection Management

```go
// Connect to server
client.Connect() error

// Check connection status
client.IsConnected() bool

// Close connection
client.Close()

// Configure reconnection
client.SetReconnectConfig(delay time.Duration, maxRetries int)
```

## Event Data Format

All events are sent with the following data structure:

### Join/Leave Events
```json
{
    "channel_id": 123
}
```

### Deposit/Withdraw Events
```json
{
    "channel_id": 123,
    "status": true
}
```

## Testing

Build and run the test client:

```bash
# Build the test client
go build -o bin/websocket-test cmd/websocket-test/main.go

# Run the test client
./bin/websocket-test
```

The test client will:
1. Connect to the WebSocket server
2. Emit join, deposit, withdraw, and leave events
3. Display received messages
4. Monitor connection health

## Error Handling

The client includes automatic reconnection with configurable retry logic:

```go
client.SetReconnectConfig(5*time.Second, 10) // 5s delay, max 10 retries
```

Common errors are handled gracefully:
- Connection failures trigger automatic reconnection
- Write errors are logged and trigger reconnection
- Network timeouts are handled with proper cleanup

## Socket.IO Protocol

The client implements Socket.IO Engine.IO v4 protocol:
- Connects to `/socket.io/?EIO=4&transport=websocket`
- Handles protocol handshake automatically
- Formats messages according to Socket.IO specification
- Maintains connection with periodic pings

## Production Considerations

1. **Connection Monitoring**: Use the health check functionality to monitor connection status
2. **Error Logging**: Implement proper error logging for production environments
3. **Graceful Shutdown**: Always call `Close()` when shutting down
4. **Resource Management**: The client manages goroutines and channels automatically
5. **Thread Safety**: All operations are thread-safe for concurrent usage

## Environment Variables

```bash
# Required
WEBSOCKET_URL=wss://socket.irich.info

# Optional configuration can be added to config.yaml files
```