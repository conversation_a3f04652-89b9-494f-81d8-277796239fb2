package websocket

import (
	"encoding/json"
	"fmt"
	"log"
	"net/url"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// EventData represents common data structure for events
type EventData struct {
	ChannelID int  `json:"channel_id"`
	Status    bool `json:"status,omitempty"`
}

// WSClient represents a WebSocket client for socket.irich.info
type WSClient struct {
	conn           *websocket.Conn
	url            string
	connected      bool
	reconnectDelay time.Duration
	maxRetries     int
	retries        int
	mutex          sync.RWMutex
	done           chan struct{}
	send           chan []byte
	pingTicker     *time.Ticker

	// Event callbacks
	onConnect    func()
	onDisconnect func(error)
	onMessage    func([]byte)
	onError      func(error)
}

// NewWSClient creates a new WebSocket client
func NewWSClient(serverURL string) *WSClient {
	return &WSClient{
		url:            serverURL,
		reconnectDelay: 5 * time.Second,
		maxRetries:     10,
		done:           make(chan struct{}),
		send:           make(chan []byte, 256),
	}
}

// OnConnect sets callback for connection events
func (c *WSClient) OnConnect(callback func()) {
	c.onConnect = callback
}

// OnDisconnect sets callback for disconnection events
func (c *WSClient) OnDisconnect(callback func(error)) {
	c.onDisconnect = callback
}

// OnMessage sets callback for message events
func (c *WSClient) OnMessage(callback func([]byte)) {
	c.onMessage = callback
}

// OnError sets callback for error events
func (c *WSClient) OnError(callback func(error)) {
	c.onError = callback
}

// Connect establishes WebSocket connection to socket.irich.info
func (c *WSClient) Connect() error {
	// Convert to Socket.IO compatible URL
	u, err := url.Parse(c.url)
	if err != nil {
		return fmt.Errorf("invalid URL: %v", err)
	}

	// Add Socket.IO path and query parameters
	socketIOURL := fmt.Sprintf("%s://%s/socket.io/?EIO=4&transport=websocket", u.Scheme, u.Host)

	dialer := websocket.DefaultDialer
	dialer.HandshakeTimeout = 10 * time.Second

	conn, _, err := dialer.Dial(socketIOURL, nil)
	if err != nil {
		if c.retries < c.maxRetries {
			c.retries++
			log.Printf("Connection failed, retrying in %v... (attempt %d/%d)",
				c.reconnectDelay, c.retries, c.maxRetries)
			time.Sleep(c.reconnectDelay)
			return c.Connect()
		}
		return fmt.Errorf("connection failed after %d attempts: %v", c.maxRetries, err)
	}

	c.mutex.Lock()
	c.conn = conn
	c.connected = true
	c.retries = 0
	c.done = make(chan struct{})
	c.send = make(chan []byte, 256)
	c.mutex.Unlock()

	// Start goroutines
	go c.writePump()
	go c.readPump()
	go c.pingPump()
	go c.handleSocketIOHandshake()

	if c.onConnect != nil {
		c.onConnect()
	}

	log.Printf("Connected to Socket.IO server: %s", socketIOURL)
	return nil
}

// handleSocketIOHandshake handles Socket.IO protocol handshake
func (c *WSClient) handleSocketIOHandshake() {
	time.Sleep(100 * time.Millisecond) // Give connection time to establish

	c.mutex.RLock()
	connected := c.connected
	c.mutex.RUnlock()

	if !connected {
		return
	}

	// Send namespace connect message
	select {
	case c.send <- []byte("40"):
		log.Println("Sent Socket.IO namespace connect")
	default:
		log.Println("Failed to send namespace connect - channel full")
	}
}

// readPump handles incoming messages
func (c *WSClient) readPump() {
	defer func() {
		c.disconnect(nil)
	}()

	c.conn.SetReadLimit(8192)
	c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		select {
		case <-c.done:
			return
		default:
			_, message, err := c.conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					log.Printf("WebSocket error: %v", err)
					if c.onError != nil {
						c.onError(err)
					}
				}
				return
			}

			messageStr := string(message)
			log.Printf("Received: %s", messageStr)

			// Handle Socket.IO protocol messages
			if messageStr == "0" {
				// Server connect - send namespace connect
				select {
				case c.send <- []byte("40"):
					log.Println("Sent namespace connect response")
				default:
					log.Println("Failed to send namespace connect response")
				}
			} else if messageStr == "40" {
				log.Println("Namespace connected successfully")
			}

			if c.onMessage != nil {
				c.onMessage(message)
			}
		}
	}
}

// writePump handles outgoing messages
func (c *WSClient) writePump() {
	defer func() {
		if c.conn != nil {
			c.conn.Close()
		}
	}()

	for {
		select {
		case message, ok := <-c.send:
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.conn.WriteMessage(websocket.TextMessage, message); err != nil {
				log.Printf("Write error: %v", err)
				if c.onError != nil {
					c.onError(err)
				}
				return
			}

		case <-c.done:
			return
		}
	}
}

// pingPump maintains connection with periodic pings
func (c *WSClient) pingPump() {
	c.pingTicker = time.NewTicker(54 * time.Second)
	defer c.pingTicker.Stop()

	for {
		select {
		case <-c.pingTicker.C:
			c.mutex.RLock()
			connected := c.connected
			c.mutex.RUnlock()

			if connected {
				c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
				if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
					log.Printf("Ping error: %v", err)
					if c.onError != nil {
						c.onError(err)
					}
					return
				}
			}

		case <-c.done:
			return
		}
	}
}

// disconnect closes the WebSocket connection
func (c *WSClient) disconnect(err error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.connected {
		return
	}

	c.connected = false
	close(c.done)

	if c.pingTicker != nil {
		c.pingTicker.Stop()
	}

	if c.conn != nil {
		c.conn.Close()
	}

	if c.onDisconnect != nil {
		c.onDisconnect(err)
	}

	log.Println("Disconnected from WebSocket server")
}

// emitEvent sends a Socket.IO event
func (c *WSClient) emitEvent(event string, data interface{}) error {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.connected {
		return fmt.Errorf("not connected")
	}

	// Create Socket.IO event format: ["event_name", data]
	socketIOData := []interface{}{event, data}
	dataJSON, err := json.Marshal(socketIOData)
	if err != nil {
		return fmt.Errorf("marshal error: %v", err)
	}

	// Socket.IO event message format: "42" + JSON
	socketIOMessage := "42" + string(dataJSON)

	log.Printf("Emitting event '%s': %s", event, socketIOMessage)

	select {
	case c.send <- []byte(socketIOMessage):
		return nil
	default:
		return fmt.Errorf("send channel is full")
	}
}

// EmitJoin emits a 'join' event with channel_id
func (c *WSClient) EmitJoin(channelID int) error {
	data := EventData{ChannelID: channelID}
	return c.emitEvent("join", data)
}

// EmitLeave emits a 'leave' event with channel_id
func (c *WSClient) EmitLeave(channelID int) error {
	data := EventData{ChannelID: channelID}
	return c.emitEvent("leave", data)
}

// EmitDeposit emits a 'deposit' event with channel_id and status
func (c *WSClient) EmitDeposit(channelID int, status bool) error {
	data := EventData{ChannelID: channelID, Status: status}
	return c.emitEvent("deposit", data)
}

// EmitWithdraw emits a 'withdraw' event with channel_id and status
func (c *WSClient) EmitWithdraw(channelID int, status bool) error {
	data := EventData{ChannelID: channelID, Status: status}
	return c.emitEvent("withdraw", data)
}

// Close closes the WebSocket connection
func (c *WSClient) Close() {
	c.disconnect(nil)
}

// IsConnected returns connection status
func (c *WSClient) IsConnected() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.connected
}

// SetReconnectConfig sets reconnection parameters
func (c *WSClient) SetReconnectConfig(delay time.Duration, maxRetries int) {
	c.reconnectDelay = delay
	c.maxRetries = maxRetries
}
