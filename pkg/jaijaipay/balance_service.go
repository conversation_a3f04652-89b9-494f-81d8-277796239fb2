package jaijaipay

import (
	"context"
	"fmt"
	"net/http"
)

// BalanceService handles balance-related API operations
type BalanceService struct {
	client *Client
}

// Get retrieves current account balance and financial summary
func (s *BalanceService) Get(ctx context.Context) (*BalanceResponse, error) {
	resp, err := s.client.makeRequest(ctx, "GET", "/payments/client/balance", nil, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get balance: %w", err)
	}

	var result BalanceResponse
	if err := s.handleResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// handleResponse handles HTTP response for balance service
func (s *BalanceService) handleResponse(resp *http.Response, target interface{}) error {
	serviceBase := &serviceBase{client: s.client}
	return serviceBase.handleResponse(resp, target)
}
