package jaijaipay

import (
	"context"
	"fmt"
	"net/http"
)

// FeesService handles fee-related API operations
type FeesService struct {
	client *Client
}

// GetPreview calculates estimated fees for a transaction before creation
func (s *FeesService) GetPreview(ctx context.Context, req *FeePreviewRequest) (*FeePreviewResponse, error) {
	// Build query parameters
	params := map[string]interface{}{
		"amount":          req.Amount,
		"transactionType": req.TransactionType,
	}

	if req.Currency != "" {
		params["currency"] = req.Currency
	}

	queryString := s.client.buildQueryString(params)
	endpoint := "/payments/client/fees/preview" + queryString

	businessContext := map[string]interface{}{
		"amount":           req.Amount,
		"transaction_type": req.TransactionType,
		"currency":         req.Currency,
	}

	resp, err := s.client.makeRequest(ctx, "GET", endpoint, nil, businessContext)
	if err != nil {
		return nil, fmt.Errorf("failed to get fee preview: %w", err)
	}

	var result FeePreviewResponse
	if err := s.handleResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// handleResponse handles HTTP response for fees service
func (s *FeesService) handleResponse(resp *http.Response, target interface{}) error {
	serviceBase := &serviceBase{client: s.client}
	return serviceBase.handleResponse(resp, target)
}
