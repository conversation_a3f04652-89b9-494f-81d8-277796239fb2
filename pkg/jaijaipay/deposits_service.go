package jaijaipay

import (
	"context"
	"fmt"
	"net/http"
)

// DepositsService handles all deposit-related API operations
type DepositsService struct {
	client *Client
}

// Create creates a new deposit transaction
func (s *DepositsService) Create(ctx context.Context, req *CreateDepositRequest) (*DepositResponse, error) {
	// Auto-generate unique orderId
	orderId := GenerateDepositOrderID()

	// Debug: Log generated orderId and full request
	s.client.logger.WithFields(map[string]interface{}{
		"generated_order_id":  orderId,
		"order_id_length":     len(orderId),
		"customer_reference":  req.CustomerReference,
		"amount":              req.Amount,
		"currency":            req.Currency,
		"bank_code":           req.BankCode,
		"bank_account_number": req.BankAccountNumber,
		"account_holder_name": req.AccountHolderName,
		"webhook_url":         req.WebhookURL,
	}).Info("generated orderId and preparing deposit request")

	// Create internal request with auto-generated orderId
	internalReq := &jaiJaiPayCreateDepositRequest{
		OrderID:           orderId,
		CustomerReference: req.CustomerReference,
		Amount:            req.Amount,
		Currency:          req.Currency,
		AssetType:         req.AssetType,
		BankCode:          req.BankCode,
		BankAccountNumber: req.BankAccountNumber,
		AccountHolderName: req.AccountHolderName,
		Description:       req.Description,
		WebhookURL:        req.WebhookURL,
		Metadata:          req.Metadata,
		ClientConfig:      req.ClientConfig,
	}

	businessContext := map[string]interface{}{
		"order_id":           orderId,
		"customer_reference": req.CustomerReference,
		"amount":             req.Amount,
		"currency":           req.Currency,
		"bank_code":          req.BankCode,
		"bank_account":       req.BankAccountNumber,
		"account_holder":     req.AccountHolderName,
	}

	resp, err := s.client.makeRequest(ctx, "POST", "/payments/client/deposits", internalReq, businessContext)
	if err != nil {
		return nil, fmt.Errorf("failed to create deposit: %w", err)
	}

	var result DepositResponse
	if err := s.handleResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// List retrieves a paginated list of deposit transactions
func (s *DepositsService) List(ctx context.Context, req *ListDepositsRequest) (*ListDepositsResponse, error) {
	// Build query parameters
	params := map[string]interface{}{}
	if req.Page > 0 {
		params["page"] = req.Page
	}
	if req.Limit > 0 {
		params["limit"] = req.Limit
	}
	if req.Status != "" {
		params["status"] = req.Status
	}
	if req.StartDate != "" {
		params["startDate"] = req.StartDate
	}
	if req.EndDate != "" {
		params["endDate"] = req.EndDate
	}
	if req.Currency != "" {
		params["currency"] = req.Currency
	}
	if req.OrderID != "" {
		params["orderId"] = req.OrderID
	}
	if req.CustomerReferenceID != "" {
		params["customerReferenceId"] = req.CustomerReferenceID
	}

	queryString := s.client.buildQueryString(params)
	endpoint := "/payments/client/deposits" + queryString

	businessContext := map[string]interface{}{
		"currency": req.Currency,
	}

	resp, err := s.client.makeRequest(ctx, "GET", endpoint, nil, businessContext)
	if err != nil {
		return nil, fmt.Errorf("failed to list deposits: %w", err)
	}

	var result ListDepositsResponse
	if err := s.handleResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// GetByID retrieves a specific deposit transaction by ID
func (s *DepositsService) GetByID(ctx context.Context, transactionID string) (*DepositResponse, error) {
	endpoint := fmt.Sprintf("/payments/client/deposits/%s", transactionID)

	businessContext := map[string]interface{}{
		"transaction_id": transactionID,
	}

	resp, err := s.client.makeRequest(ctx, "GET", endpoint, nil, businessContext)
	if err != nil {
		return nil, fmt.Errorf("failed to get deposit by ID: %w", err)
	}

	var result DepositResponse
	if err := s.handleResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// Cancel cancels a pending deposit transaction
func (s *DepositsService) Cancel(ctx context.Context, req *CancelDepositRequest) (*CancelDepositResponse, error) {
	businessContext := map[string]interface{}{
		"transaction_id": req.TransactionID,
	}

	resp, err := s.client.makeRequest(ctx, "POST", "/payments/client/deposits/cancel", req, businessContext)
	if err != nil {
		return nil, fmt.Errorf("failed to cancel deposit: %w", err)
	}

	var result CancelDepositResponse
	if err := s.handleResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// handleResponse handles HTTP response for deposits service
func (s *DepositsService) handleResponse(resp *http.Response, target interface{}) error {
	serviceBase := &serviceBase{client: s.client}
	return serviceBase.handleResponse(resp, target)
}
