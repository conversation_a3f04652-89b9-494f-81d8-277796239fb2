package jaijaipay

import "time"

// Config holds the JaiJaiPay client configuration
type Config struct {
	BaseURL          string        `json:"base_url" yaml:"base_url" mapstructure:"base_url"`
	APIKey           string        `json:"api_key" yaml:"api_key" mapstructure:"api_key"`
	Secret<PERSON><PERSON>        string        `json:"secret_key" yaml:"secret_key" mapstructure:"secret_key"`
	Timeout          time.Duration `json:"timeout" yaml:"timeout" mapstructure:"timeout"`
	MaxRetries       int           `json:"max_retries" yaml:"max_retries" mapstructure:"max_retries"`
	RetryDelay       time.Duration `json:"retry_delay" yaml:"retry_delay" mapstructure:"retry_delay"`
	EnableRequestLog bool          `json:"enable_request_log" yaml:"enable_request_log" mapstructure:"enable_request_log"`
	LogResponseBody  bool          `json:"log_response_body" yaml:"log_response_body" mapstructure:"log_response_body"`
	EnableDebug      bool          `json:"enable_debug" yaml:"enable_debug" mapstructure:"enable_debug"`
}

// DefaultConfig returns default configuration
func DefaultConfig() Config {
	return Config{
		BaseURL:          "https://api.jaijaipay.com/api/v1",
		Timeout:          30 * time.Second,
		MaxRetries:       3,
		RetryDelay:       1 * time.Second,
		EnableRequestLog: true,
		LogResponseBody:  false, // Security: don't log sensitive response data by default
		EnableDebug:      false,
	}
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.BaseURL == "" {
		return ErrInvalidConfig{Field: "base_url", Message: "base_url is required"}
	}

	if c.APIKey == "" {
		return ErrInvalidConfig{Field: "api_key", Message: "api_key is required"}
	}

	if c.SecretKey == "" {
		return ErrInvalidConfig{Field: "secret_key", Message: "secret_key is required"}
	}

	if c.Timeout <= 0 {
		return ErrInvalidConfig{Field: "timeout", Message: "timeout must be greater than 0"}
	}

	if c.MaxRetries < 0 {
		return ErrInvalidConfig{Field: "max_retries", Message: "max_retries must be >= 0"}
	}

	return nil
}

// ErrInvalidConfig represents configuration validation errors
type ErrInvalidConfig struct {
	Field   string
	Message string
}

func (e ErrInvalidConfig) Error() string {
	return "invalid config - " + e.Field + ": " + e.Message
}
