package jaijaipay

import (
	"fmt"
	"time"

	"github.com/google/uuid"
)

// GenerateOrderID creates a unique order ID for JaiJaiPay transactions
func GenerateOrderID(prefix string) string {
	// Very short format: prefix + last 6 digits of timestamp + 4 random chars
	timestamp := time.Now().Unix() % 1000000 // Last 6 digits only
	shortUUID := uuid.New().String()[:4]     // 4 chars only
	return fmt.Sprintf("%s%d%s", prefix, timestamp, shortUUID)
}

// GenerateDepositOrderID creates a unique order ID for deposit transactions
func GenerateDepositOrderID() string {
	return GenerateOrderID("DEP")
}

// GenerateWithdrawalOrderID creates a unique order ID for withdrawal transactions
func GenerateWithdrawalOrderID() string {
	return GenerateOrderID("WDL")
}

// GenerateCustomerReference creates a unique customer reference if not provided
func GenerateCustomerReference(userID string) string {
	if userID == "" {
		userID = "unknown"
	}
	timestamp := time.Now().Unix()
	return fmt.Sprintf("CUST-%s-%d", userID, timestamp)
}
