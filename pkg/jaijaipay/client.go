package jaijaipay

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"

	"blacking-api/internal/domain"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/logger"
)

// Client represents the JaiJaiPay API client
type Client struct {
	config             Config
	httpClient         *http.Client
	signatureGenerator *SignatureGenerator
	logger             logger.Logger
	apiLogRepo         interfaces.JaiJaiPayAPILogRepository

	// Service categories
	Deposits    *DepositsService
	Withdrawals *WithdrawalsService
	Balance     *BalanceService
	Fees        *FeesService
	Analytics   *AnalyticsService
	Webhooks    *WebhooksService
}

// NewClient creates a new JaiJaiPay API client
func NewClient(config Config, logger logger.Logger, apiLogRepo interfaces.JaiJaiPayAPILogRepository) (*Client, error) {
	// Debug: Log config before validation
	logger.WithFields(map[string]interface{}{
		"base_url":          config.BaseURL,
		"api_key_set":       config.APIKey != "",
		"api_key_length":    len(config.APIKey),
		"secret_key_set":    config.SecretKey != "",
		"secret_key_length": len(config.SecretKey),
		"timeout":           config.Timeout,
	}).Info("NewClient called with config")

	if err := config.Validate(); err != nil {
		logger.WithError(err).Error("JaiJaiPay config validation failed")
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	logger.Info("JaiJaiPay config validation passed")

	client := &Client{
		config:             config,
		httpClient:         &http.Client{Timeout: config.Timeout},
		signatureGenerator: NewSignatureGenerator(config.SecretKey),
		logger:             logger,
		apiLogRepo:         apiLogRepo,
	}

	// Initialize service categories
	client.Deposits = &DepositsService{client: client}
	client.Withdrawals = &WithdrawalsService{client: client}
	client.Balance = &BalanceService{client: client}
	client.Fees = &FeesService{client: client}
	client.Analytics = &AnalyticsService{client: client}
	client.Webhooks = &WebhooksService{client: client}

	return client, nil
}

// makeRequest handles HTTP requests with authentication and logging
func (c *Client) makeRequest(ctx context.Context, method, endpoint string, requestBody interface{}, businessContext map[string]interface{}) (*http.Response, error) {
	requestID := uuid.New().String()
	startTime := time.Now()
	fullURL := c.config.BaseURL + endpoint

	// Marshal request body if provided
	var bodyBytes []byte
	var err error
	if requestBody != nil && (method == "POST" || method == "PUT" || method == "PATCH") {
		bodyBytes, err = json.Marshal(requestBody)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
	}

	// Debug: Log config before generating auth headers
	c.logger.WithFields(map[string]interface{}{
		"base_url":          c.config.BaseURL,
		"api_key_length":    len(c.config.APIKey),
		"secret_key_length": len(c.config.SecretKey),
		"api_key_set":       c.config.APIKey != "",
		"secret_key_set":    c.config.SecretKey != "",
	}).Info("JaiJaiPay client config before auth header generation")

	// Generate auth headers
	authHeaders, err := c.signatureGenerator.GenerateAuthHeaders(c.config.APIKey, requestBody, method)
	if err != nil {
		c.logger.WithError(err).WithFields(map[string]interface{}{
			"method":         method,
			"api_key_set":    c.config.APIKey != "",
			"secret_key_set": c.config.SecretKey != "",
		}).Error("failed to generate auth headers")
		return nil, fmt.Errorf("failed to generate auth headers: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, method, fullURL, bytes.NewBuffer(bodyBytes))
	if err != nil {
		c.logger.WithError(err).WithFields(map[string]interface{}{
			"method":    method,
			"full_url":  fullURL,
			"body_size": len(bodyBytes),
		}).Error("failed to create HTTP request")
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-api-key", authHeaders.APIKey)
	req.Header.Set("x-signature", authHeaders.Signature)
	req.Header.Set("x-timestamp", authHeaders.Timestamp)

	// Prepare business context for logging
	businessData := domain.BusinessContext{}
	if businessContext != nil {
		if orderID, ok := businessContext["order_id"].(string); ok {
			businessData.OrderID = &orderID
		}
		if transactionID, ok := businessContext["transaction_id"].(string); ok {
			businessData.TransactionID = &transactionID
		}
		if customerRef, ok := businessContext["customer_reference"].(string); ok {
			businessData.CustomerReference = &customerRef
		}
		if amount, ok := businessContext["amount"].(float64); ok {
			businessData.Amount = &amount
		}
		if currency, ok := businessContext["currency"].(string); ok {
			businessData.Currency = &currency
		}
		if bankCode, ok := businessContext["bank_code"].(string); ok {
			businessData.BankCode = &bankCode
		}
		if bankAccount, ok := businessContext["bank_account"].(string); ok {
			businessData.BankAccount = &bankAccount
		}
		if accountHolder, ok := businessContext["account_holder"].(string); ok {
			businessData.AccountHolder = &accountHolder
		}
	}

	// Determine API category
	apiCategory := c.determineAPICategory(endpoint)

	// Create initial API log entry
	apiLog := &domain.JaiJaiPayAPILog{
		RequestID:   requestID,
		APICategory: apiCategory,
		Method:      method,
		Endpoint:    endpoint,
		RequestBody: bodyBytes,
		Success:     false, // Will be updated after response
	}

	// Set business context
	if err := apiLog.SetBusinessData(businessData); err != nil {
		c.logger.WithError(err).Warn("failed to set business data for API log")
	}

	// Set request headers for logging (without sensitive data)
	requestHeaders := map[string]string{
		"Content-Type": req.Header.Get("Content-Type"),
		"x-api-key":    "***MASKED***",
		"x-signature":  "***MASKED***",
		"x-timestamp":  authHeaders.Timestamp,
	}
	requestHeadersBytes, _ := json.Marshal(requestHeaders)
	apiLog.RequestHeaders = requestHeadersBytes

	// Debug: Log the actual request being sent to JaiJaiPay
	c.logger.WithFields(map[string]interface{}{
		"request_id":   requestID,
		"method":       method,
		"endpoint":     endpoint,
		"full_url":     fullURL,
		"timestamp":    authHeaders.Timestamp,
		"request_body": string(bodyBytes),
		"api_category": apiCategory,
	}).Info("sending request to JaiJaiPay API")

	// Save initial log entry
	if c.config.EnableRequestLog {
		if err := c.apiLogRepo.Create(ctx, apiLog); err != nil {
			c.logger.WithError(err).Error("failed to create API log entry")
		}
	}

	// Log just before making HTTP request
	c.logger.WithFields(map[string]interface{}{
		"request_id": requestID,
		"method":     method,
		"full_url":   fullURL,
		"headers_set": map[string]bool{
			"content_type": req.Header.Get("Content-Type") != "",
			"x_api_key":    req.Header.Get("x-api-key") != "",
			"x_signature":  req.Header.Get("x-signature") != "",
			"x_timestamp":  req.Header.Get("x-timestamp") != "",
		},
		"body_length": len(bodyBytes),
	}).Info("making HTTP request to JaiJaiPay")

	// Make the HTTP request
	resp, err := c.httpClient.Do(req)
	responseTime := int(time.Since(startTime).Milliseconds())

	// Update log with response data
	apiLog.ResponseTimeMs = &responseTime

	if err != nil {
		// Handle request error
		errorMsg := err.Error()
		apiLog.ErrorMessage = &errorMsg
		apiLog.Success = false

		if c.config.EnableRequestLog {
			if updateErr := c.apiLogRepo.Update(ctx, apiLog); updateErr != nil {
				c.logger.WithError(updateErr).Error("failed to update API log with error")
			}
		}

		c.logger.WithError(err).WithFields(map[string]interface{}{
			"request_id": requestID,
			"method":     method,
			"endpoint":   endpoint,
			"full_url":   fullURL,
			"duration":   responseTime,
		}).Error("HTTP request to JaiJaiPay failed")

		return nil, fmt.Errorf("request failed: %w", err)
	}

	// Read response body
	var responseBody []byte
	if resp.Body != nil {
		responseBody, err = io.ReadAll(resp.Body)
		if err != nil {
			c.logger.WithError(err).Warn("failed to read response body")
		}
		resp.Body.Close()

		// Create new body reader for return
		resp.Body = io.NopCloser(bytes.NewReader(responseBody))
	}

	// Update log with response
	apiLog.ResponseStatusCode = &resp.StatusCode
	apiLog.Success = resp.StatusCode < 400

	if c.config.LogResponseBody && len(responseBody) > 0 {
		apiLog.ResponseBody = responseBody
	}

	if c.config.EnableRequestLog {
		if err := c.apiLogRepo.Update(ctx, apiLog); err != nil {
			c.logger.WithError(err).Error("failed to update API log with response")
		}
	}

	// Log the request
	logFields := map[string]interface{}{
		"request_id":    requestID,
		"method":        method,
		"endpoint":      endpoint,
		"status_code":   resp.StatusCode,
		"duration_ms":   responseTime,
		"success":       apiLog.Success,
		"api_category":  apiCategory,
		"response_body": string(responseBody),
	}

	if apiLog.Success {
		c.logger.WithFields(logFields).Info("jaijaipay api request completed successfully")
	} else {
		// Always log detailed error response for debugging
		c.logger.WithFields(map[string]interface{}{
			"request_id":       requestID,
			"method":           method,
			"endpoint":         endpoint,
			"status_code":      resp.StatusCode,
			"duration_ms":      responseTime,
			"success":          apiLog.Success,
			"api_category":     apiCategory,
			"request_body":     string(bodyBytes),
			"response_body":    string(responseBody),
			"response_headers": resp.Header,
		}).Error("jaijaipay api request failed with detailed error response")
	}

	return resp, nil
}

// determineAPICategory determines the API category based on endpoint
func (c *Client) determineAPICategory(endpoint string) string {
	switch {
	case strings.Contains(endpoint, "/deposits"):
		return domain.APICategories.Deposits
	case strings.Contains(endpoint, "/withdrawals"):
		return domain.APICategories.Withdrawals
	case strings.Contains(endpoint, "/balance"):
		return "balance"
	case strings.Contains(endpoint, "/fees"):
		return "fees"
	case strings.Contains(endpoint, "/analytics"):
		return "analytics"
	case strings.Contains(endpoint, "/webhooks"):
		return "webhooks"
	default:
		return "unknown"
	}
}

// buildQueryString builds query string from parameters
func (c *Client) buildQueryString(params map[string]interface{}) string {
	if len(params) == 0 {
		return ""
	}

	values := url.Values{}
	for key, value := range params {
		switch v := value.(type) {
		case string:
			if v != "" {
				values.Add(key, v)
			}
		case int:
			if v != 0 {
				values.Add(key, strconv.Itoa(v))
			}
		case float64:
			if v != 0 {
				values.Add(key, strconv.FormatFloat(v, 'f', -1, 64))
			}
		}
	}

	if len(values) == 0 {
		return ""
	}

	return "?" + values.Encode()
}

// handleErrorResponse handles API error responses
func (c *Client) handleErrorResponse(resp *http.Response) error {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read error response: %w", err)
	}

	var errorResp ErrorResponse
	if err := json.Unmarshal(body, &errorResp); err != nil {
		return fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(body))
	}

	return fmt.Errorf("API error %d: %s (request_id: %s)", errorResp.ErrorCode, errorResp.Message, errorResp.RequestID)
}

// Service base struct for all service categories
type serviceBase struct {
	client *Client
}

func (s *serviceBase) makeRequest(ctx context.Context, method, endpoint string, requestBody interface{}, businessContext map[string]interface{}) (*http.Response, error) {
	return s.client.makeRequest(ctx, method, endpoint, requestBody, businessContext)
}

func (s *serviceBase) handleResponse(resp *http.Response, target interface{}) error {
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		return s.client.handleErrorResponse(resp)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	if target != nil {
		if err := json.Unmarshal(body, target); err != nil {
			return fmt.Errorf("failed to unmarshal response: %w", err)
		}
	}

	return nil
}
