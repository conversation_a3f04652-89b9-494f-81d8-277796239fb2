package jaijaipay

import (
	"context"
	"fmt"
	"net/http"
)

// WebhooksService handles webhook-related API operations
type WebhooksService struct {
	client *Client
}

// Resend resends webhook notifications for specific transactions
func (s *WebhooksService) Resend(ctx context.Context, req *ResendWebhookRequest) (*ResendWebhookResponse, error) {
	businessContext := map[string]interface{}{
		"transaction_id": req.TransactionID,
	}

	resp, err := s.client.makeRequest(ctx, "POST", "/payments/client/webhooks/resend", req, businessContext)
	if err != nil {
		return nil, fmt.Errorf("failed to resend webhook: %w", err)
	}

	var result ResendWebhookResponse
	if err := s.handleResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// ValidateWebhookPayload validates incoming webhook payload signature
func (s *WebhooksService) ValidateWebhookPayload(payload string, signature string) bool {
	return s.client.signatureGenerator.ValidateSignature(payload, signature)
}

// handleResponse handles HTTP response for webhooks service
func (s *WebhooksService) handleResponse(resp *http.Response, target interface{}) error {
	serviceBase := &serviceBase{client: s.client}
	return serviceBase.handleResponse(resp, target)
}
