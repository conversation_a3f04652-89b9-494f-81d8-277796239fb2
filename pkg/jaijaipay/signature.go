package jaijaipay

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"strconv"
	"time"
)

// SignatureGenerator handles HMAC-SHA256 signature generation for JaiJaiPay API
type SignatureGenerator struct {
	secretKey string
}

// NewSignatureGenerator creates a new signature generator
func NewSignatureGenerator(secretKey string) *SignatureGenerator {
	return &SignatureGenerator{
		secretKey: secretKey,
	}
}

// GenerateTimestamp creates a Unix timestamp string
func GenerateTimestamp() string {
	return strconv.FormatInt(time.Now().Unix(), 10)
}

// GenerateSignatureForPOST generates HMAC-SHA256 signature for POST requests with body
func (sg *SignatureGenerator) GenerateSignatureForPOST(requestBody interface{}, timestamp string) (string, error) {
	// Convert request body to JSON string
	var bodyString string
	if requestBody != nil {
		bodyBytes, err := json.Marshal(requestBody)
		if err != nil {
			return "", fmt.Errorf("failed to marshal request body: %w", err)
		}
		bodyString = string(bodyBytes)
	}

	// Create payload: timestamp + JSON body
	payload := timestamp + bodyString

	// Generate HMAC-SHA256 signature
	signature := sg.generateHMAC(payload)
	return signature, nil
}

// GenerateSignatureForGET generates HMAC-SHA256 signature for GET requests (timestamp only)
func (sg *SignatureGenerator) GenerateSignatureForGET(timestamp string) string {
	// For GET requests, payload is just timestamp
	payload := timestamp
	return sg.generateHMAC(payload)
}

// generateHMAC creates HMAC-SHA256 hash
func (sg *SignatureGenerator) generateHMAC(payload string) string {
	h := hmac.New(sha256.New, []byte(sg.secretKey))
	h.Write([]byte(payload))
	return hex.EncodeToString(h.Sum(nil))
}

// ValidateSignature validates incoming webhook signatures
func (sg *SignatureGenerator) ValidateSignature(payload string, receivedSignature string) bool {
	expectedSignature := sg.generateHMAC(payload)
	return hmac.Equal([]byte(expectedSignature), []byte(receivedSignature))
}

// AuthHeaders represents the required authentication headers for JaiJaiPay API
type AuthHeaders struct {
	APIKey    string `json:"x-api-key"`
	Signature string `json:"x-signature"`
	Timestamp string `json:"x-timestamp"`
}

// GenerateAuthHeaders creates all required authentication headers
func (sg *SignatureGenerator) GenerateAuthHeaders(apiKey string, requestBody interface{}, method string) (*AuthHeaders, error) {
	timestamp := GenerateTimestamp()

	var signature string
	var err error

	switch method {
	case "POST", "PUT", "PATCH":
		signature, err = sg.GenerateSignatureForPOST(requestBody, timestamp)
		if err != nil {
			return nil, err
		}
	case "GET", "DELETE":
		signature = sg.GenerateSignatureForGET(timestamp)
	default:
		return nil, fmt.Errorf("unsupported HTTP method: %s", method)
	}

	return &AuthHeaders{
		APIKey:    apiKey,
		Signature: signature,
		Timestamp: timestamp,
	}, nil
}
