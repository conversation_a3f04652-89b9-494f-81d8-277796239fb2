package jaijaipay

import (
	"context"
	"fmt"
	"net/http"
)

// WithdrawalsService handles all withdrawal-related API operations
type WithdrawalsService struct {
	client *Client
}

// Create creates a new withdrawal transaction
func (s *WithdrawalsService) Create(ctx context.Context, req *CreateWithdrawalRequest) (*WithdrawalResponse, error) {
	// Auto-generate unique orderId
	orderId := GenerateWithdrawalOrderID()

	// Create internal request with auto-generated orderId
	internalReq := &jaiJaiPayCreateWithdrawalRequest{
		OrderID:           orderId,
		CustomerReference: req.CustomerReference,
		Amount:            req.Amount,
		Currency:          req.Currency,
		AssetType:         req.AssetType,
		BankCode:          req.BankCode,
		BankAccountNumber: req.BankAccountNumber,
		AccountHolderName: req.AccountHolderName,
		Description:       req.Description,
		WebhookURL:        req.WebhookURL,
		Metadata:          req.Metadata,
	}

	businessContext := map[string]interface{}{
		"order_id":           orderId,
		"customer_reference": req.CustomerReference,
		"amount":             req.Amount,
		"currency":           req.Currency,
		"bank_code":          req.BankCode,
		"bank_account":       req.BankAccountNumber,
		"account_holder":     req.AccountHolderName,
	}

	resp, err := s.client.makeRequest(ctx, "POST", "/payments/client/withdrawals", internalReq, businessContext)
	if err != nil {
		return nil, fmt.Errorf("failed to create withdrawal: %w", err)
	}

	var result WithdrawalResponse
	if err := s.handleResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// List retrieves a paginated list of withdrawal transactions
func (s *WithdrawalsService) List(ctx context.Context, req *ListWithdrawalsRequest) (*ListWithdrawalsResponse, error) {
	// Build query parameters
	params := map[string]interface{}{}
	if req.Page > 0 {
		params["page"] = req.Page
	}
	if req.Limit > 0 {
		params["limit"] = req.Limit
	}
	if req.Status != "" {
		params["status"] = req.Status
	}
	if req.StartDate != "" {
		params["startDate"] = req.StartDate
	}
	if req.EndDate != "" {
		params["endDate"] = req.EndDate
	}
	if req.Currency != "" {
		params["currency"] = req.Currency
	}
	if req.CustomerReferenceID != "" {
		params["customerReferenceId"] = req.CustomerReferenceID
	}

	queryString := s.client.buildQueryString(params)
	endpoint := "/payments/client/withdrawals" + queryString

	businessContext := map[string]interface{}{
		"currency": req.Currency,
	}

	resp, err := s.client.makeRequest(ctx, "GET", endpoint, nil, businessContext)
	if err != nil {
		return nil, fmt.Errorf("failed to list withdrawals: %w", err)
	}

	var result ListWithdrawalsResponse
	if err := s.handleResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// GetByID retrieves a specific withdrawal transaction by ID
func (s *WithdrawalsService) GetByID(ctx context.Context, transactionID string) (*WithdrawalResponse, error) {
	endpoint := fmt.Sprintf("/payments/client/withdrawals/%s", transactionID)

	businessContext := map[string]interface{}{
		"transaction_id": transactionID,
	}

	resp, err := s.client.makeRequest(ctx, "GET", endpoint, nil, businessContext)
	if err != nil {
		return nil, fmt.Errorf("failed to get withdrawal by ID: %w", err)
	}

	var result WithdrawalResponse
	if err := s.handleResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// handleResponse handles HTTP response for withdrawals service
func (s *WithdrawalsService) handleResponse(resp *http.Response, target interface{}) error {
	serviceBase := &serviceBase{client: s.client}
	return serviceBase.handleResponse(resp, target)
}
