package jaijaipay

import (
	"context"
	"fmt"
	"net/http"
)

// AnalyticsService handles analytics-related API operations
type AnalyticsService struct {
	client *Client
}

// GetTransactionAnalytics retrieves comprehensive transaction analytics for specified time period
func (s *AnalyticsService) GetTransactionAnalytics(ctx context.Context, req *AnalyticsRequest) (*AnalyticsResponse, error) {
	// Build query parameters
	params := map[string]interface{}{
		"startDate": req.StartDate,
		"endDate":   req.EndDate,
	}

	if req.Currency != "" {
		params["currency"] = req.Currency
	}
	if req.TransactionType != "" {
		params["transactionType"] = req.TransactionType
	}
	if req.Status != "" {
		params["status"] = req.Status
	}

	queryString := s.client.buildQueryString(params)
	endpoint := "/payments/client/analytics/transactions" + queryString

	businessContext := map[string]interface{}{
		"start_date":       req.StartDate,
		"end_date":         req.EndDate,
		"currency":         req.Currency,
		"transaction_type": req.TransactionType,
		"status":           req.Status,
	}

	resp, err := s.client.makeRequest(ctx, "GET", endpoint, nil, businessContext)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction analytics: %w", err)
	}

	var result AnalyticsResponse
	if err := s.handleResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// handleResponse handles HTTP response for analytics service
func (s *AnalyticsService) handleResponse(resp *http.Response, target interface{}) error {
	serviceBase := &serviceBase{client: s.client}
	return serviceBase.handleResponse(resp, target)
}
