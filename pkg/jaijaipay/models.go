package jaijaipay

import (
	"encoding/json"
	"time"
)

// Common structures
type ClientConfig struct {
	SuccessURL string `json:"successUrl,omitempty"`
	FailedURL  string `json:"failedUrl,omitempty"`
	ReturnURL  string `json:"returnUrl,omitempty"`
}

type QRPromptPay struct {
	QRCodeImage                string          `json:"qrCodeImage,omitempty"`
	QRDataString               string          `json:"qrDataString,omitempty"`
	BusinessRegistrationNumber string          `json:"businessRegistrationNumber,omitempty"`
	Metadata                   json.RawMessage `json:"metadata,omitempty"`
	UsageStatistics            json.RawMessage `json:"usageStatistics,omitempty"`
	AmountTracking             AmountTracking  `json:"amountTracking,omitempty"`
}

type AmountTracking struct {
	OriginalAmount   float64 `json:"originalAmount"`
	QRAmount         float64 `json:"qrAmount"`
	AddedCents       int     `json:"addedCents"`
	PrecisionApplied bool    `json:"precisionApplied"`
	Currency         string  `json:"currency"`
}

type Pagination struct {
	Page       int `json:"page"`
	Limit      int `json:"limit"`
	Total      int `json:"total"`
	TotalPages int `json:"totalPages"`
}

// Deposits API Models
type CreateDepositRequest struct {
	// OrderID will be auto-generated by Blacking API
	CustomerReference string          `json:"customerReference"`
	Amount            float64         `json:"amount"`
	Currency          string          `json:"currency"`
	AssetType         string          `json:"assetType"`
	BankCode          string          `json:"bankCode"`
	BankAccountNumber string          `json:"bankAccountNumber"`
	AccountHolderName string          `json:"accountHolderName"`
	Description       string          `json:"description"`
	WebhookURL        string          `json:"webhookUrl"`
	Metadata          json.RawMessage `json:"metadata,omitempty"`
	ClientConfig      *ClientConfig   `json:"clientConfig,omitempty"`
}

// Internal request to JaiJaiPay (with auto-generated OrderID)
type jaiJaiPayCreateDepositRequest struct {
	OrderID           string          `json:"orderId"`
	CustomerReference string          `json:"customerReference"`
	Amount            float64         `json:"amount"`
	Currency          string          `json:"currency"`
	AssetType         string          `json:"assetType"`
	BankCode          string          `json:"bankCode"`
	BankAccountNumber string          `json:"bankAccountNumber"`
	AccountHolderName string          `json:"accountHolderName"`
	Description       string          `json:"description"`
	WebhookURL        string          `json:"webhookUrl"`
	Metadata          json.RawMessage `json:"metadata,omitempty"`
	ClientConfig      *ClientConfig   `json:"clientConfig,omitempty"`
}

type DepositResponse struct {
	ID                       int64           `json:"id"`
	TransactionID            string          `json:"transactionId"`
	TransactionReference     string          `json:"transactionReference"`
	Amount                   string          `json:"amount"`
	OriginalAmount           string          `json:"originalAmount"`
	Status                   string          `json:"status"`
	QRCode                   string          `json:"qrCode,omitempty"`
	QRText                   string          `json:"qrText,omitempty"`
	QRImageURL               string          `json:"qrImageUrl,omitempty"`
	PaymentURL               string          `json:"paymentUrl,omitempty"`
	ExpiresAt                *time.Time      `json:"expiresAt,omitempty"`
	OrderID                  string          `json:"orderId"`
	CustomerReference        string          `json:"customerReference"`
	BankCode                 string          `json:"bankCode"`
	BankAccountNumber        string          `json:"bankAccountNumber"`
	AccountHolderName        string          `json:"accountHolderName"`
	Currency                 string          `json:"currency"`
	AssetType                string          `json:"assetType"`
	Description              string          `json:"description"`
	WebhookURL               string          `json:"webhookUrl"`
	MerchantID               string          `json:"merchantId"`
	CompanyID                string          `json:"companyId"`
	Metadata                 json.RawMessage `json:"metadata,omitempty"`
	WebsiteConfig            json.RawMessage `json:"websiteConfig,omitempty"`
	CreatedAt                time.Time       `json:"createdAt"`
	FeeAmount                string          `json:"feeAmount,omitempty"`
	TotalAmount              float64         `json:"totalAmount,omitempty"`
	PercentageCommissionRate string          `json:"percentageCommissionRate,omitempty"`
	FixedCommissionAmount    string          `json:"fixedCommissionAmount,omitempty"`
	TotalCommissionAmount    string          `json:"totalCommissionAmount,omitempty"`
	CompletedAt              *time.Time      `json:"completedAt,omitempty"`
	QRPromptPay              *QRPromptPay    `json:"qrPromptPay,omitempty"`
}

type ListDepositsRequest struct {
	Page                int    `json:"page,omitempty"`
	Limit               int    `json:"limit,omitempty"`
	Status              string `json:"status,omitempty"`
	StartDate           string `json:"startDate,omitempty"`
	EndDate             string `json:"endDate,omitempty"`
	Currency            string `json:"currency,omitempty"`
	OrderID             string `json:"orderId,omitempty"`
	CustomerReferenceID string `json:"customerReferenceId,omitempty"`
}

type ListDepositsResponse struct {
	Data       []DepositResponse `json:"data"`
	Pagination Pagination        `json:"pagination"`
	Filters    json.RawMessage   `json:"filters,omitempty"`
}

type CancelDepositRequest struct {
	TransactionID string `json:"transactionId"`
}

type CancelDepositResponse struct {
	TransactionID string    `json:"transactionId"`
	Status        string    `json:"status"`
	Message       string    `json:"message"`
	CancelledAt   time.Time `json:"cancelledAt"`
}

// Withdrawals API Models
type CreateWithdrawalRequest struct {
	// OrderID will be auto-generated by Blacking API
	CustomerReference string          `json:"customerReference"`
	Amount            float64         `json:"amount"`
	Currency          string          `json:"currency"`
	AssetType         string          `json:"assetType"`
	BankCode          string          `json:"bankCode"`
	BankAccountNumber string          `json:"bankAccountNumber"`
	AccountHolderName string          `json:"accountHolderName"`
	Description       string          `json:"description"`
	WebhookURL        string          `json:"webhookUrl"`
	Metadata          json.RawMessage `json:"metadata,omitempty"`
}

// Internal request to JaiJaiPay (with auto-generated OrderID)
type jaiJaiPayCreateWithdrawalRequest struct {
	OrderID           string          `json:"orderId"`
	CustomerReference string          `json:"customerReference"`
	Amount            float64         `json:"amount"`
	Currency          string          `json:"currency"`
	AssetType         string          `json:"assetType"`
	BankCode          string          `json:"bankCode"`
	BankAccountNumber string          `json:"bankAccountNumber"`
	AccountHolderName string          `json:"accountHolderName"`
	Description       string          `json:"description"`
	WebhookURL        string          `json:"webhookUrl"`
	Metadata          json.RawMessage `json:"metadata,omitempty"`
}

type WithdrawalResponse struct {
	TransactionID            string          `json:"transactionId"`
	TransactionReference     string          `json:"transactionReference"`
	Amount                   string          `json:"amount"`
	Status                   string          `json:"status"`
	BankCode                 string          `json:"bankCode"`
	BankAccountNumber        string          `json:"bankAccountNumber"`
	AccountHolderName        string          `json:"accountHolderName"`
	CreatedAt                time.Time       `json:"createdAt"`
	OrderID                  string          `json:"orderId"`
	CustomerReference        string          `json:"customerReference"`
	Currency                 string          `json:"currency"`
	AssetType                string          `json:"assetType"`
	Description              string          `json:"description,omitempty"`
	WebhookURL               string          `json:"webhookUrl,omitempty"`
	MerchantID               string          `json:"merchantId,omitempty"`
	CompanyID                string          `json:"companyId,omitempty"`
	Metadata                 json.RawMessage `json:"metadata,omitempty"`
	PercentageCommissionRate string          `json:"percentageCommissionRate,omitempty"`
	FixedCommissionAmount    string          `json:"fixedCommissionAmount,omitempty"`
	TotalCommissionAmount    string          `json:"totalCommissionAmount,omitempty"`
	CompletedAt              *time.Time      `json:"completedAt,omitempty"`
}

type ListWithdrawalsRequest struct {
	Page                int    `json:"page,omitempty"`
	Limit               int    `json:"limit,omitempty"`
	Status              string `json:"status,omitempty"`
	StartDate           string `json:"startDate,omitempty"`
	EndDate             string `json:"endDate,omitempty"`
	Currency            string `json:"currency,omitempty"`
	CustomerReferenceID string `json:"customerReferenceId,omitempty"`
}

type ListWithdrawalsResponse struct {
	Data       []WithdrawalResponse `json:"data"`
	Pagination Pagination           `json:"pagination"`
}

// Balance API Models
type BalanceResponse struct {
	MerchantID string          `json:"merchantId"`
	CompanyID  string          `json:"companyId"`
	Results    []BalanceResult `json:"results"`
}

type BalanceResult struct {
	Currency         string             `json:"currency"`
	AvailableBalance float64            `json:"availableBalance"`
	Deposit          BalanceTransaction `json:"deposit"`
	Withdrawal       BalanceTransaction `json:"withdrawal"`
}

type BalanceTransaction struct {
	Fees         BalanceFees `json:"fees"`
	TotalAmount  float64     `json:"totalAmount"`
	TotalFees    float64     `json:"totalFees"`
	TotalBalance float64     `json:"totalBalance"`
}

type BalanceFees struct {
	Percentage float64 `json:"percentage"`
	Fixed      float64 `json:"fixed"`
}

// Fees API Models
type FeePreviewRequest struct {
	Amount          float64 `json:"amount"`
	TransactionType string  `json:"transactionType"` // DEPOSIT or WITHDRAWAL
	Currency        string  `json:"currency,omitempty"`
}

type FeePreviewResponse struct {
	Amount                  float64         `json:"amount"`
	Currency                string          `json:"currency"`
	TransactionType         string          `json:"transactionType"`
	FeeCalculation          FeeCalculation  `json:"feeCalculation"`
	AmountBreakdown         AmountBreakdown `json:"amountBreakdown"`
	EstimatedProcessingTime string          `json:"estimatedProcessingTime"`
	CalculatedAt            time.Time       `json:"calculatedAt"`
}

type FeeCalculation struct {
	PercentageFeeRate   float64 `json:"percentageFeeRate"`
	PercentageFeeAmount float64 `json:"percentageFeeAmount"`
	FixedFeeAmount      float64 `json:"fixedFeeAmount"`
	TotalFeeAmount      float64 `json:"totalFeeAmount"`
	FeeBearer           string  `json:"feeBearer"`
}

type AmountBreakdown struct {
	CustomerPays     float64 `json:"customerPays"`
	MerchantReceives float64 `json:"merchantReceives"`
	PlatformFee      float64 `json:"platformFee"`
}

// Analytics API Models
type AnalyticsRequest struct {
	StartDate       string `json:"startDate"` // YYYY-MM-DD
	EndDate         string `json:"endDate"`   // YYYY-MM-DD
	Currency        string `json:"currency,omitempty"`
	TransactionType string `json:"transactionType,omitempty"` // DEPOSIT or WITHDRAWAL
	Status          string `json:"status,omitempty"`
}

type AnalyticsResponse struct {
	ReportPeriod  ReportPeriod           `json:"reportPeriod"`
	CompanyID     string                 `json:"companyId"`
	MerchantID    string                 `json:"merchantId"`
	GeneratedAt   time.Time              `json:"generatedAt"`
	Summary       AnalyticsSummary       `json:"summary"`
	Breakdown     AnalyticsBreakdown     `json:"breakdown"`
	Trends        AnalyticsTrends        `json:"trends"`
	Performance   AnalyticsPerformance   `json:"performance"`
	Profitability AnalyticsProfitability `json:"profitability"`
	Insights      AnalyticsInsights      `json:"insights"`
}

type ReportPeriod struct {
	StartDate string `json:"startDate"`
	EndDate   string `json:"endDate"`
	TotalDays int    `json:"totalDays"`
}

type AnalyticsSummary struct {
	TotalTransactions      int     `json:"totalTransactions"`
	TotalVolume            float64 `json:"totalVolume"`
	TotalFees              float64 `json:"totalFees"`
	NetAmount              float64 `json:"netAmount"`
	AverageTransactionSize float64 `json:"averageTransactionSize"`
	UniqueCustomers        int     `json:"uniqueCustomers"`
}

type AnalyticsBreakdown struct {
	Deposits    TransactionBreakdown `json:"deposits"`
	Withdrawals TransactionBreakdown `json:"withdrawals"`
}

type TransactionBreakdown struct {
	Count             int     `json:"count"`
	TotalAmount       float64 `json:"totalAmount"`
	TotalFees         float64 `json:"totalFees"`
	AverageAmount     float64 `json:"averageAmount"`
	SuccessRate       float64 `json:"successRate"`
	PercentageOfTotal float64 `json:"percentageOfTotal"`
}

type AnalyticsTrends struct {
	VolumeGrowth           float64 `json:"volumeGrowth"`
	TransactionGrowth      float64 `json:"transactionGrowth"`
	AverageTransactionSize float64 `json:"averageTransactionSize"`
	FeeGrowth              float64 `json:"feeGrowth"`
	SuccessRateChange      float64 `json:"successRateChange"`
	PeakTransactionPeriod  string  `json:"peakTransactionPeriod"`
}

type AnalyticsPerformance struct {
	AverageProcessingTime float64 `json:"averageProcessingTime"`
	FastestTransaction    float64 `json:"fastestTransaction"`
	SlowestTransaction    float64 `json:"slowestTransaction"`
	OnTimeCompletionRate  float64 `json:"onTimeCompletionRate"`
	SLABreaches           int     `json:"slaBreaches"`
}

type AnalyticsProfitability struct {
	TotalRevenue           float64 `json:"totalRevenue"`
	TotalCosts             float64 `json:"totalCosts"`
	GrossProfit            float64 `json:"grossProfit"`
	NetProfit              float64 `json:"netProfit"`
	ProfitMarginPercentage float64 `json:"profitMarginPercentage"`
	FeeIncomePercentage    float64 `json:"feeIncomePercentage"`
	OperationalEfficiency  float64 `json:"operationalEfficiency"`
}

type AnalyticsInsights struct {
	PeakHour           string             `json:"peakHour"`
	PeakDay            string             `json:"peakDay"`
	MostUsedCurrency   string             `json:"mostUsedCurrency"`
	LargestTransaction LargestTransaction `json:"largestTransaction"`
	RecommendedActions []string           `json:"recommendedActions"`
}

type LargestTransaction struct {
	Amount        string `json:"amount"`
	TransactionID string `json:"transactionId"`
	Date          string `json:"date"`
}

// Webhooks API Models
type ResendWebhookRequest struct {
	TransactionID string `json:"transactionId"`
}

type ResendWebhookResponse struct {
	Success             bool      `json:"success"`
	Message             string    `json:"message"`
	TransactionID       string    `json:"transactionId"`
	ResentAt            time.Time `json:"resentAt"`
	Warning             string    `json:"warning,omitempty"`
	PreviousResendCount int       `json:"previousResendCount"`
}

type WebhookPayload struct {
	Event                string          `json:"event"`
	TransactionID        string          `json:"transactionId"`
	TransactionReference string          `json:"transactionReference"`
	Status               string          `json:"status"`
	Amount               string          `json:"amount"`
	Currency             string          `json:"currency"`
	TransactionType      string          `json:"transactionType"`
	CompanyID            string          `json:"companyId"`
	MerchantID           string          `json:"merchantId"`
	OrderID              string          `json:"orderId"`
	CustomerReference    string          `json:"customerReference"`
	Metadata             json.RawMessage `json:"metadata,omitempty"`
	Timestamp            time.Time       `json:"timestamp"`
}

// Error Response Model
type ErrorResponse struct {
	ErrorCode        int             `json:"errorCode"`
	Message          string          `json:"message"`
	Details          json.RawMessage `json:"details,omitempty"`
	RequestID        string          `json:"requestId,omitempty"`
	DocumentationURL string          `json:"documentationUrl,omitempty"`
	Timestamp        time.Time       `json:"timestamp"`
}
