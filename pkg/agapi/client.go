package agapi

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	"strings"
	"syscall"
	"time"

	"blacking-api/internal/config"
	"blacking-api/pkg/logger"
)

type Client struct {
	baseURL    string
	lineCode   string
	secretKey  string
	httpClient *http.Client
	logger     logger.Logger
}

type MemberRegisterRequest struct {
	Username  string `json:"username"`
	Email     string `json:"email"`
	FirstName string `json:"firstName"`
	LastName  string `json:"lastName"`
	Phone     string `json:"phone"`
	Password  string `json:"password"`
}

type MemberRegisterResponse struct {
	Success bool `json:"success"`
	Data    struct {
		MemberCode string     `json:"memberCode"`
		Username   string     `json:"username"`
		Email      string     `json:"email"`
		FirstName  string     `json:"firstName"`
		LastName   string     `json:"lastName"`
		Phone      string     `json:"phone"`
		AgentCode  string     `json:"agentCode"`
		Status     string     `json:"status"`
		Providers  []Provider `json:"providers"`
		CreatedAt  string     `json:"createdAt"`
		UpdatedAt  string     `json:"updatedAt"`
	} `json:"data"`
	Message string `json:"message"`
}

type Provider struct {
	ProviderCode       string `json:"providerCode"`
	ProviderMemberCode string `json:"providerMemberCode"`
	Status             string `json:"status"`
	RegistrationDate   string `json:"registrationDate"`
}

type ErrorResponse struct {
	Success bool `json:"success"`
	Error   struct {
		Code    string                 `json:"code"`
		Message string                 `json:"message"`
		Details map[string]interface{} `json:"details,omitempty"`
	} `json:"error"`
}

type GamesDepositRequest struct {
	Username    string `json:"username"`
	Amount      string `json:"amount"`
	ReferenceID string `json:"referenceId"`
}

type GamesDepositResponse struct {
	Amount       float64 `json:"amount"`
	TxnID        string  `json:"txnId"`
	Username     string  `json:"username"`
	WalletAfter  float64 `json:"walletAfter"`
	WalletBefore float64 `json:"walletBefore"`
}

type GamesDepositAPIResponse struct {
	Data GamesDepositResponse `json:"data"`
}

type GamesWithdrawRequest struct {
	Username    string `json:"username"`
	Amount      string `json:"amount"`
	ReferenceID string `json:"referenceId"`
}

type GamesWithdrawResponse struct {
	Amount       float64 `json:"amount"`
	TxnID        string  `json:"txnId"`
	Username     string  `json:"username"`
	WalletAfter  float64 `json:"walletAfter"`
	WalletBefore float64 `json:"walletBefore"`
}

type GamesWithdrawAPIResponse struct {
	Data GamesWithdrawResponse `json:"data"`
}

func NewClient(cfg *config.Config, logger logger.Logger) *Client {
	return &Client{
		baseURL:   cfg.Agent.BaseURL,
		lineCode:  cfg.Agent.LineCode,
		secretKey: cfg.Agent.SecretKey,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
	}
}

// GetBaseURL returns the configured base URL
func (c *Client) GetBaseURL() string {
	return c.baseURL
}

// CreateAuthHeader returns the Basic Auth header for API calls
func (c *Client) CreateAuthHeader() string {
	return c.createBasicAuth()
}

func (c *Client) RegisterMember(ctx context.Context, req MemberRegisterRequest) (*MemberRegisterResponse, error) {
	log := c.logger.WithContext(ctx).WithField("operation", "RegisterMember")

	// Log configuration status
	log.WithField("baseURL", c.baseURL).WithField("lineCode", c.lineCode).WithField("hasSecretKey", c.secretKey != "").Info("AG API client configuration")

	if c.lineCode == "" || c.secretKey == "" {
		log.Error("AG API credentials not configured")
		return nil, fmt.Errorf("AG API credentials not configured")
	}

	// Prepare request body
	reqBody, err := json.Marshal(req)
	if err != nil {
		log.WithError(err).Error("failed to marshal request body")
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	url := fmt.Sprintf("%s/members", c.baseURL)
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		log.WithError(err).Error("failed to create HTTP request")
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	authHeader := c.createBasicAuth()
	httpReq.Header.Set("Authorization", authHeader)

	// Log request details (without sensitive data)
	log.WithField("url", url).WithField("username", req.Username).WithField("method", "POST").WithField("requestBody", string(reqBody)).Info("calling AG API to register member")

	// Make the request
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		// Check if it's a connection refused error (AG API server not running)
		if isConnectionRefused(err) {
			log.WithError(err).WithField("url", url).Warn("AG API server is not available (connection refused)")
			return nil, fmt.Errorf("AG API server is not available at %s: %w", url, err)
		}
		log.WithError(err).WithField("url", url).Error("failed to call AG API")
		return nil, fmt.Errorf("failed to call AG API: %w", err)
	}
	defer resp.Body.Close()

	// Read response body first for debugging
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.WithError(err).WithField("status_code", resp.StatusCode).Error("failed to read response body")
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Log response details
	log.WithField("status_code", resp.StatusCode).WithField("response_body", string(bodyBytes)).Info("AG API response received")

	// Handle response based on status code
	if resp.StatusCode == http.StatusCreated {
		var memberResp MemberRegisterResponse
		if err := json.Unmarshal(bodyBytes, &memberResp); err != nil {
			log.WithError(err).WithField("response_body", string(bodyBytes)).Error("failed to decode success response")
			return nil, fmt.Errorf("failed to decode response: %w", err)
		}
		log.WithField("member_code", memberResp.Data.MemberCode).Info("member registered successfully in AG API")
		return &memberResp, nil
	}

	// Handle error response
	var errResp ErrorResponse
	if err := json.Unmarshal(bodyBytes, &errResp); err != nil {
		log.WithError(err).WithField("status_code", resp.StatusCode).WithField("response_body", string(bodyBytes)).Error("failed to decode error response")
		return nil, fmt.Errorf("AG API returned status %d with body: %s", resp.StatusCode, string(bodyBytes))
	}

	log.WithField("error_code", errResp.Error.Code).WithField("error_message", errResp.Error.Message).WithField("error_details", errResp.Error.Details).Error("AG API returned error")
	return nil, fmt.Errorf("AG API error (%s): %s", errResp.Error.Code, errResp.Error.Message)
}

// GamesDeposit calls the games deposit API endpoint
func (c *Client) GamesDeposit(ctx context.Context, username string, amount float64, referenceID string) (*GamesDepositResponse, error) {
	log := c.logger.WithContext(ctx).WithField("operation", "GamesDeposit")

	if c.baseURL == "" {
		log.Error("Agent API URL not configured")
		return nil, fmt.Errorf("Agent API URL not configured")
	}

	// Prepare request body
	req := GamesDepositRequest{
		Username:    username,
		Amount:      fmt.Sprintf("%.0f", amount),
		ReferenceID: referenceID,
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		log.WithError(err).Error("failed to marshal request body")
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	url := fmt.Sprintf("%s/games/deposit", strings.TrimSuffix(c.baseURL, "/"))
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		log.WithError(err).Error("failed to create HTTP request")
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers - use Basic Auth with username:secretKey instead of lineCode:secretKey
	httpReq.Header.Set("Content-Type", "application/json")
	credentials := fmt.Sprintf("%s:%s", username, c.secretKey)
	encoded := base64.StdEncoding.EncodeToString([]byte(credentials))
	httpReq.Header.Set("Authorization", fmt.Sprintf("Basic %s", encoded))

	// Log request details
	log.WithFields(map[string]interface{}{
		"url":          url,
		"username":     username,
		"amount":       amount,
		"reference_id": referenceID,
	}).Info("calling Agent API for games deposit")

	// Make the request
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		if isConnectionRefused(err) {
			log.WithError(err).WithField("url", url).Warn("Agent API server is not available (connection refused)")
			return nil, fmt.Errorf("Agent API server is not available at %s: %w", url, err)
		}
		log.WithError(err).WithField("url", url).Error("failed to call Agent API")
		return nil, fmt.Errorf("failed to call Agent API: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.WithError(err).WithField("status_code", resp.StatusCode).Error("failed to read response body")
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Log response details
	log.WithFields(map[string]interface{}{
		"status_code":   resp.StatusCode,
		"response_body": string(bodyBytes),
		"username":      username,
		"amount":        amount,
		"reference_id":  referenceID,
	}).Info("Agent API response received")

	// Check if request was successful
	if resp.StatusCode != http.StatusOK {
		log.WithFields(map[string]interface{}{
			"status_code":   resp.StatusCode,
			"response_body": string(bodyBytes),
		}).Error("Agent API returned non-OK status")
		return nil, fmt.Errorf("Agent API returned status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse response - expecting wrapped data object
	var apiResp GamesDepositAPIResponse
	if err := json.Unmarshal(bodyBytes, &apiResp); err != nil {
		log.WithError(err).Error("failed to parse Agent API response")
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"username":        username,
		"amount":          amount,
		"reference_id":    referenceID,
		"wallet_before":   apiResp.Data.WalletBefore,
		"wallet_after":    apiResp.Data.WalletAfter,
		"txn_id":          apiResp.Data.TxnID,
		"response_amount": apiResp.Data.Amount,
	}).Info("games deposit processed successfully via Agent API")

	return &apiResp.Data, nil
}

// GamesWithdraw calls the games withdraw API endpoint
func (c *Client) GamesWithdraw(ctx context.Context, username string, amount float64, referenceID string) (*GamesWithdrawResponse, error) {
	log := c.logger.WithContext(ctx).WithField("operation", "GamesWithdraw")

	if c.baseURL == "" {
		log.Error("Agent API URL not configured")
		return nil, fmt.Errorf("Agent API URL not configured")
	}

	// Prepare request body
	req := GamesWithdrawRequest{
		Username:    username,
		Amount:      fmt.Sprintf("%.0f", amount),
		ReferenceID: referenceID,
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		log.WithError(err).Error("failed to marshal request body")
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	url := fmt.Sprintf("%s/games/withdraw", strings.TrimSuffix(c.baseURL, "/"))
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		log.WithError(err).Error("failed to create HTTP request")
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers - use Basic Auth with username:secretKey instead of lineCode:secretKey
	httpReq.Header.Set("Content-Type", "application/json")
	credentials := fmt.Sprintf("%s:%s", username, c.secretKey)
	encoded := base64.StdEncoding.EncodeToString([]byte(credentials))
	httpReq.Header.Set("Authorization", fmt.Sprintf("Basic %s", encoded))

	// Log request details
	log.WithFields(map[string]interface{}{
		"url":          url,
		"username":     username,
		"amount":       amount,
		"reference_id": referenceID,
	}).Info("calling Agent API for games withdraw")

	// Make the request
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		if isConnectionRefused(err) {
			log.WithError(err).WithField("url", url).Warn("Agent API server is not available (connection refused)")
			return nil, fmt.Errorf("Agent API server is not available at %s: %w", url, err)
		}
		log.WithError(err).WithField("url", url).Error("failed to call Agent API")
		return nil, fmt.Errorf("failed to call Agent API: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.WithError(err).WithField("status_code", resp.StatusCode).Error("failed to read response body")
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Log response details
	log.WithFields(map[string]interface{}{
		"status_code":   resp.StatusCode,
		"response_body": string(bodyBytes),
		"username":      username,
		"amount":        amount,
		"reference_id":  referenceID,
	}).Info("Agent API response received")

	// Check if request was successful
	if resp.StatusCode != http.StatusOK {
		log.WithFields(map[string]interface{}{
			"status_code":   resp.StatusCode,
			"response_body": string(bodyBytes),
		}).Error("Agent API returned non-OK status")
		return nil, fmt.Errorf("Agent API returned status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse response - expecting wrapped data object
	var apiResp GamesWithdrawAPIResponse
	if err := json.Unmarshal(bodyBytes, &apiResp); err != nil {
		log.WithError(err).Error("failed to parse Agent API response")
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	log.WithFields(map[string]interface{}{
		"username":        username,
		"amount":          amount,
		"reference_id":    referenceID,
		"wallet_before":   apiResp.Data.WalletBefore,
		"wallet_after":    apiResp.Data.WalletAfter,
		"txn_id":          apiResp.Data.TxnID,
		"response_amount": apiResp.Data.Amount,
	}).Info("games withdraw processed successfully via Agent API")

	return &apiResp.Data, nil
}

func (c *Client) createBasicAuth() string {
	credentials := fmt.Sprintf("%s:%s", c.lineCode, c.secretKey)
	encoded := base64.StdEncoding.EncodeToString([]byte(credentials))
	return fmt.Sprintf("Basic %s", encoded)
}

// isConnectionRefused checks if the error is a connection refused error
func isConnectionRefused(err error) bool {
	if err == nil {
		return false
	}

	// Check for connection refused error
	if strings.Contains(err.Error(), "connection refused") {
		return true
	}

	// Check for syscall.ECONNREFUSED error
	var netErr *net.OpError
	if errors.As(err, &netErr) {
		if errors.Is(netErr.Err, syscall.ECONNREFUSED) {
			return true
		}
	}

	return false
}
