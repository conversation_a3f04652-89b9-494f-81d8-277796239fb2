package auth

// LoginClaims represents user JWT claims
type <PERSON><PERSON><PERSON><PERSON><PERSON> struct {
	ID            int
	Username      string
	AdminRoleName string
	AdminRoleID   int
	IsEnable      bool
	LoginTime     int64 // Unix timestamp of login
}

// MemberLoginClaims represents member JWT claims
type MemberLoginClaims struct {
	ID             int
	Phone          *string // Use phone as primary identifier for login
	Username       *string // Optional username field
	GameUsername   *string // Game username for gaming platform
	IsEnable       bool
	LoginTime      int64  // Unix timestamp of login
	MemberRoleID   int    // Member role ID for permission checking
	MemberRoleName string // Member role name
}
