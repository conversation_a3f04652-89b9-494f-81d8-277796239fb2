package gameapi

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// Client represents the game API client
type Client struct {
	baseURL                   string
	httpClient                *http.Client
	upline                    string
	winLossMemberInfoEndpoint string
	userDepositEndpoint       string
}

// NewClient creates a new game API client
func NewClient(baseURL, upline, winLossMemberInfoEndpoint, userDepositEndpoint string) *Client {
	return &Client{
		baseURL:                   baseURL,
		upline:                    upline,
		winLossMemberInfoEndpoint: winLossMemberInfoEndpoint,
		userDepositEndpoint:       userDepositEndpoint,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// WinLossRequest represents the request for win/loss data
type WinLossRequest struct {
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
	Upline    string `json:"upline"`
	Page      int    `json:"page"`
	Limit     int    `json:"limit"`
}

// WinLossResponse represents the response from the API
type WinLossResponse struct {
	Data struct {
		Data []WinLossItem `json:"data"`
		Meta struct {
			Limit     int `json:"limit"`
			Page      int `json:"page"`
			Total     int `json:"total"`
			TotalPage int `json:"total_page"`
		} `json:"meta"`
		Sum struct {
			BetAmount       float64 `json:"bet_amount"`
			BetCount        int     `json:"bet_count"`
			BetWinloss      float64 `json:"bet_winloss"`
			ProviderWinloss float64 `json:"provider_winloss"`
			UplineWinloss   float64 `json:"upline_winloss"`
		} `json:"sum"`
	} `json:"data"`
	Message string `json:"message"`
	Status  string `json:"status"`
}

// WinLossItem represents a single win/loss record
type WinLossItem struct {
	ActionType      string  `json:"action_type"`
	AmountDeposit   float64 `json:"amount_deposit"`
	AmountWithdraw  float64 `json:"amount_withdraw"`
	BalanceAfter    float64 `json:"balance_after"`
	BalanceBefore   float64 `json:"balance_before"`
	BetAmount       float64 `json:"bet_amount"`
	BetWinloss      float64 `json:"bet_winloss"`
	Created         string  `json:"created"`
	CreatedAt       string  `json:"created_at"`
	CreatedISO      string  `json:"created_iso"`
	GameCategory    string  `json:"game_category"`
	GameName        string  `json:"game_name"`
	PlayDate        string  `json:"play_date"`
	ProductID       string  `json:"product_id"`
	Provider        string  `json:"provider"`
	ProviderWinloss float64 `json:"provider_winloss"`
	RoundID         string  `json:"round_id"`
	TxnID           string  `json:"txn_id"`
	Upline          string  `json:"upline"`
	UplineWinloss   float64 `json:"upline_winloss"`
	Username        string  `json:"username"`
}

// GameProviderGroup represents a game provider group from the API
type GameProviderGroup struct {
	GameID          string `json:"game_id"`
	Upline          string `json:"upline"`
	Category        string `json:"category"`
	GameProvider    string `json:"game_provider"`
	GameName        string `json:"game_name"`
	GameCallbackURL string `json:"game_callback_url"`
	GameOrder       int    `json:"game_order"`
	Image           string `json:"image"`
	ImageMobile     string `json:"image_mobile"`
	Provider        string `json:"provider"`
	IsRedirectGame  int    `json:"is_redirect_game"`
}

// GameProviderGroupsResponse represents the response from game-provider-groups API
type GameProviderGroupsResponse struct {
	Data    []GameProviderGroup `json:"data"`
	Message string              `json:"message"`
	Status  string              `json:"status"`
}

// UserDepositRequest represents the request for user deposit
type UserDepositRequest struct {
	Username    string  `json:"username"`
	Amount      float64 `json:"amount"`
	ReferenceID string  `json:"reference_id"`
}

// UserDepositResponse represents the response from user deposit API
type UserDepositResponse struct {
	Data struct {
		Amount       float64 `json:"amount"`
		TxnID        string  `json:"txn_id"`
		Username     string  `json:"username"`
		WalletAfter  float64 `json:"wallet_after"`
		WalletBefore float64 `json:"wallet_before"`
	} `json:"data"`
	Message string `json:"message"`
	Status  string `json:"status"`
}

// GetWinLossMemberInfo fetches win/loss data for members
func (c *Client) GetWinLossMemberInfo(ctx context.Context, req WinLossRequest) (*WinLossResponse, error) {
	url := fmt.Sprintf("%s%s", c.baseURL, c.winLossMemberInfoEndpoint)

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("upline", c.upline)

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned non-OK status: %d, body: %s", resp.StatusCode, string(body))
	}

	var winLossResp WinLossResponse
	if err := json.Unmarshal(body, &winLossResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if winLossResp.Status != "success" {
		return nil, fmt.Errorf("API returned error status: %s, message: %s", winLossResp.Status, winLossResp.Message)
	}

	return &winLossResp, nil
}

// GetGameProviderGroups fetches game provider groups
func (c *Client) GetGameProviderGroups(ctx context.Context) (*GameProviderGroupsResponse, error) {
	url := fmt.Sprintf("%s/api/v1/transfer/game-provider-groups", c.baseURL)

	httpReq, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("upline", c.upline)

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned non-OK status: %d, body: %s", resp.StatusCode, string(body))
	}

	var gameProviderResp GameProviderGroupsResponse
	if err := json.Unmarshal(body, &gameProviderResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if gameProviderResp.Status != "success" {
		return nil, fmt.Errorf("API returned error status: %s, message: %s", gameProviderResp.Status, gameProviderResp.Message)
	}

	return &gameProviderResp, nil
}

// UserDeposit performs user deposit via form data
func (c *Client) UserDeposit(ctx context.Context, req UserDepositRequest) (*UserDepositResponse, error) {
	apiURL := fmt.Sprintf("%s%s", c.baseURL, c.userDepositEndpoint)

	// Prepare form data
	formData := url.Values{}
	formData.Set("username", req.Username)
	formData.Set("amount", fmt.Sprintf("%.2f", req.Amount))
	formData.Set("reference_id", req.ReferenceID)

	httpReq, err := http.NewRequestWithContext(ctx, "POST", apiURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	httpReq.Header.Set("upline", c.upline)

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned non-OK status: %d, body: %s", resp.StatusCode, string(body))
	}

	var depositResp UserDepositResponse
	if err := json.Unmarshal(body, &depositResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if depositResp.Status != "success" {
		return nil, fmt.Errorf("API returned error status: %s, message: %s", depositResp.Status, depositResp.Message)
	}

	return &depositResp, nil
}
