package dbutil

import (
	"context"
	"fmt"
	"os"
	"strings"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
)

// TableName returns the fully qualified table name with schema prefix
// This replaces the problematic schema path approach for DigitalOcean PostgreSQL
func TableName(tableName string) string {
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" || schemaName == "public" {
		return tableName
	}
	return fmt.Sprintf("%s.%s", schemaName, tableName)
}

// processQueryWithSchema automatically adds schema prefixes to table names in SQL queries
// This is a helper function for backward compatibility with existing code
func processQueryWithSchema(query string) string {
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" || schemaName == "public" {
		return query
	}

	// List of all tables found in the database - complete coverage based on actual schema
	tables := []string{
		// Core tables - exact match with database
		"admin_audit_log", "algorithm", "allowed_ips", "auto_bot", "banners", "channels", "contact",
		"faqs", "languages", "members", "migrations", "otps", "permissions",
		"referral_groups", "refresh_tokens", "system_settings", "theme_setting",
		"user_2fa", "user_audit_logs", "user_role_permissions", "user_roles", "users",

		// Banking system
		"bank_statement", "bank_transaction_slip", "banking", "deposit_account",
		"holding_account", "payment_gateway_account", "payment_method",
		"statement_status", "statement_type", "user_transaction",
		"user_transaction_direction", "user_transaction_status", "user_transaction_type",
		"withdraw_account",

		// Member system
		"commission_groups", "login_attempts", "member_audit_logs",
		"member_group_deposit_accounts", "member_group_types",
		"member_group_withdrawal_approvals", "member_groups",

		// Communication system
		"customer_call_logs", "customer_sms_logs", "sms_provider", "sms_provider_name",

		// Promotion system - complete list from database
		"promotion_web", "promotion_web_bonus_condition", "promotion_web_bonus_type",
		"promotion_web_date_type", "promotion_web_lock_credit", "promotion_web_pg_hard_game",
		"promotion_web_pg_hard_game_user", "promotion_web_register_member",
		"promotion_web_register_member_status", "promotion_web_status", "promotion_web_turnover_type",
		"promotion_web_type", "promotion_web_user", "promotion_web_user_confirm",
		"promotion_web_user_log", "promotion_web_user_status",

		// Return turn system
		"return_turn_cut_type", "return_turn_loser", "return_turn_loser_status",
		"return_turn_loser_type", "return_turn_setting", "return_turn_calculate_type",
		"calculate_play_type",

		// External API and transaction logs
		"jaijaipay_api_logs", "referral_transactions",

		// Payment Gateway Transaction System (New)
		"payment_gateway_transactions", "payment_gateway_webhooks",
		"payment_gateway_api_logs", "payment_gateway_transaction_status_history",

		// Crypto Deposit System (New)
		"blockchain_networks", "chain_tokens", "backend_wallets", "crypto_deposit_logs",
	}

	// Replace table names with schema-qualified versions
	for _, table := range tables {
		// Handle FROM clause
		query = strings.ReplaceAll(query, fmt.Sprintf("FROM %s ", table), fmt.Sprintf("FROM %s.%s ", schemaName, table))
		query = strings.ReplaceAll(query, fmt.Sprintf("FROM %s\n", table), fmt.Sprintf("FROM %s.%s\n", schemaName, table))
		query = strings.ReplaceAll(query, fmt.Sprintf("FROM %s\t", table), fmt.Sprintf("FROM %s.%s\t", schemaName, table))

		// Handle INSERT INTO clause
		query = strings.ReplaceAll(query, fmt.Sprintf("INTO %s ", table), fmt.Sprintf("INTO %s.%s ", schemaName, table))
		query = strings.ReplaceAll(query, fmt.Sprintf("INTO %s (", table), fmt.Sprintf("INTO %s.%s (", schemaName, table))

		// Handle UPDATE clause
		query = strings.ReplaceAll(query, fmt.Sprintf("UPDATE %s ", table), fmt.Sprintf("UPDATE %s.%s ", schemaName, table))
		query = strings.ReplaceAll(query, fmt.Sprintf("UPDATE %s\n", table), fmt.Sprintf("UPDATE %s.%s\n", schemaName, table))
		query = strings.ReplaceAll(query, fmt.Sprintf("UPDATE %s\t", table), fmt.Sprintf("UPDATE %s.%s\t", schemaName, table))

		// Handle JOIN clause
		query = strings.ReplaceAll(query, fmt.Sprintf("JOIN %s ", table), fmt.Sprintf("JOIN %s.%s ", schemaName, table))
		query = strings.ReplaceAll(query, fmt.Sprintf("JOIN %s ON", table), fmt.Sprintf("JOIN %s.%s ON", schemaName, table))

		// Handle LEFT JOIN clause
		query = strings.ReplaceAll(query, fmt.Sprintf("LEFT JOIN %s ", table), fmt.Sprintf("LEFT JOIN %s.%s ", schemaName, table))
		query = strings.ReplaceAll(query, fmt.Sprintf("LEFT JOIN %s ON", table), fmt.Sprintf("LEFT JOIN %s.%s ON", schemaName, table))

		// Handle RIGHT JOIN clause
		query = strings.ReplaceAll(query, fmt.Sprintf("RIGHT JOIN %s ", table), fmt.Sprintf("RIGHT JOIN %s.%s ", schemaName, table))
		query = strings.ReplaceAll(query, fmt.Sprintf("RIGHT JOIN %s ON", table), fmt.Sprintf("RIGHT JOIN %s.%s ON", schemaName, table))

		// Handle INNER JOIN clause
		query = strings.ReplaceAll(query, fmt.Sprintf("INNER JOIN %s ", table), fmt.Sprintf("INNER JOIN %s.%s ", schemaName, table))
		query = strings.ReplaceAll(query, fmt.Sprintf("INNER JOIN %s ON", table), fmt.Sprintf("INNER JOIN %s.%s ON", schemaName, table))
	}

	return query
}

// ExecWithSchema executes a query with automatic schema handling
// Now uses fully qualified table names instead of setting search_path
func ExecWithSchema(ctx context.Context, pool *pgxpool.Pool, query string, args ...interface{}) (pgconn.CommandTag, error) {
	processedQuery := processQueryWithSchema(query)
	return pool.Exec(ctx, processedQuery, args...)
}

// QueryRowWithSchema queries a single row with automatic schema handling
// Now uses fully qualified table names instead of setting search_path
func QueryRowWithSchema(ctx context.Context, pool *pgxpool.Pool, query string, args ...interface{}) pgx.Row {
	processedQuery := processQueryWithSchema(query)
	return pool.QueryRow(ctx, processedQuery, args...)
}

// QueryWithSchema queries multiple rows with automatic schema handling
// Now uses fully qualified table names instead of setting search_path
func QueryWithSchema(ctx context.Context, pool *pgxpool.Pool, query string, args ...interface{}) (pgx.Rows, error) {
	processedQuery := processQueryWithSchema(query)
	return pool.Query(ctx, processedQuery, args...)
}

// Transaction-aware functions for handling schema in transactions

// TxExecWithSchema executes a query within a transaction with automatic schema handling
func TxExecWithSchema(ctx context.Context, tx pgx.Tx, query string, args ...interface{}) (pgconn.CommandTag, error) {
	processedQuery := processQueryWithSchema(query)
	return tx.Exec(ctx, processedQuery, args...)
}

// TxQueryRowWithSchema queries a single row within a transaction with automatic schema handling
func TxQueryRowWithSchema(ctx context.Context, tx pgx.Tx, query string, args ...interface{}) pgx.Row {
	processedQuery := processQueryWithSchema(query)
	return tx.QueryRow(ctx, processedQuery, args...)
}

// TxQueryWithSchema queries multiple rows within a transaction with automatic schema handling
func TxQueryWithSchema(ctx context.Context, tx pgx.Tx, query string, args ...interface{}) (pgx.Rows, error) {
	processedQuery := processQueryWithSchema(query)
	return tx.Query(ctx, processedQuery, args...)
}
