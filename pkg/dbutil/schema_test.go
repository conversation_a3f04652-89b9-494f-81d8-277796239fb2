package dbutil

import (
	"os"
	"testing"
)

func TestTableName(t *testing.T) {
	tests := []struct {
		name      string
		schema    string
		tableName string
		expected  string
	}{
		{
			name:      "public schema should return table name as is",
			schema:    "public",
			tableName: "users",
			expected:  "users",
		},
		{
			name:      "empty schema should return table name as is",
			schema:    "",
			tableName: "users",
			expected:  "users",
		},
		{
			name:      "custom schema should prepend schema name",
			schema:    "sbobet789",
			tableName: "users",
			expected:  "sbobet789.users",
		},
		{
			name:      "custom schema with system_settings table",
			schema:    "testschema",
			tableName: "system_settings",
			expected:  "testschema.system_settings",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variable for test
			original := os.Getenv("DATABASE_SCHEMA")
			defer os.Setenv("DATABASE_SCHEMA", original)

			os.Setenv("DATABASE_SCHEMA", tt.schema)

			result := TableName(tt.tableName)
			if result != tt.expected {
				t.<PERSON>rf("TableName(%q) = %q, want %q", tt.tableName, result, tt.expected)
			}
		})
	}
}

func TestProcessQueryWithSchema(t *testing.T) {
	tests := []struct {
		name     string
		schema   string
		query    string
		expected string
	}{
		{
			name:     "public schema should not modify query",
			schema:   "public",
			query:    "SELECT * FROM users WHERE id = $1",
			expected: "SELECT * FROM users WHERE id = $1",
		},
		{
			name:     "empty schema should not modify query",
			schema:   "",
			query:    "SELECT * FROM users WHERE id = $1",
			expected: "SELECT * FROM users WHERE id = $1",
		},
		{
			name:     "custom schema should modify FROM clause",
			schema:   "testschema",
			query:    "SELECT * FROM users WHERE id = $1",
			expected: "SELECT * FROM testschema.users WHERE id = $1",
		},
		{
			name:     "custom schema should modify INSERT INTO clause",
			schema:   "testschema",
			query:    "INSERT INTO users (name) VALUES ($1)",
			expected: "INSERT INTO testschema.users (name) VALUES ($1)",
		},
		{
			name:     "custom schema should modify UPDATE clause",
			schema:   "testschema",
			query:    "UPDATE users SET name = $1 WHERE id = $2",
			expected: "UPDATE testschema.users SET name = $1 WHERE id = $2",
		},
		{
			name:     "custom schema should modify JOIN clause",
			schema:   "testschema",
			query:    "SELECT * FROM users u LEFT JOIN members m ON u.id = m.user_id",
			expected: "SELECT * FROM testschema.users u LEFT JOIN testschema.members m ON u.id = m.user_id",
		},
		{
			name:     "system_settings table should be modified",
			schema:   "testschema",
			query:    "SELECT value FROM system_settings WHERE key = $1",
			expected: "SELECT value FROM testschema.system_settings WHERE key = $1",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variable for test
			original := os.Getenv("DATABASE_SCHEMA")
			defer os.Setenv("DATABASE_SCHEMA", original)

			os.Setenv("DATABASE_SCHEMA", tt.schema)

			result := processQueryWithSchema(tt.query)
			if result != tt.expected {
				t.Errorf("processQueryWithSchema(%q) = %q, want %q", tt.query, result, tt.expected)
			}
		})
	}
}
