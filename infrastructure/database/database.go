package database

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"blacking-api/internal/config"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/jackc/pgx/v5/stdlib"
)

type Database struct {
	PGXPool  *pgxpool.Pool
	logger   logger.Logger
	stdlibDB *sql.DB // Backward compatibility for old repositories
}

// NewDatabase creates a new database connection using PGX pool
func NewDatabase(cfg *config.Config) (*Database, error) {
	dsn := cfg.GetDatabaseDSN()

	// Parse the connection string
	poolConfig, err := pgxpool.ParseConfig(dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to parse database config: %w", err)
	}

	// Auto-detect DigitalOcean - use default settings for compatibility
	if strings.Contains(cfg.Database.Host, ".ondigitalocean.com") {
		// DigitalOcean PostgreSQL doesn't support custom runtime parameters
		// Use default PGX settings for maximum compatibility
	}

	// Configure connection pool settings
	poolConfig.MaxConns = int32(cfg.Database.MaxOpenConns)
	poolConfig.MinConns = int32(cfg.Database.MaxIdleConns / 2) // Set min to half of max idle
	poolConfig.MaxConnLifetime = time.Hour
	poolConfig.MaxConnIdleTime = 30 * time.Minute
	poolConfig.HealthCheckPeriod = 5 * time.Minute

	// Create the connection pool
	pool, err := pgxpool.NewWithConfig(context.Background(), poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create database pool: %w", err)
	}

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := pool.Ping(ctx); err != nil {
		pool.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// Create default logger if not provided
	var log logger.Logger

	if log != nil {
		log.Info("PGX database connection pool established successfully")
	}

	// Create stdlib DB for backward compatibility
	stdlibDB := stdlib.OpenDBFromPool(pool)

	return &Database{
		PGXPool:  pool,
		logger:   log,
		stdlibDB: stdlibDB,
	}, nil
}

// New creates a new database connection (backward compatibility)
func New(cfg *config.Config, log logger.Logger) (*Database, error) {
	db, err := NewDatabase(cfg)
	if err != nil {
		return nil, err
	}
	db.logger = log
	return db, nil
}

// Close closes the database connection pool
func (d *Database) Close() {
	if d.stdlibDB != nil {
		d.stdlibDB.Close()
	}
	if d.PGXPool != nil {
		d.PGXPool.Close()
	}
}

// BeginTx begins a new transaction
func (d *Database) BeginTx(ctx context.Context) (pgx.Tx, error) {
	return d.PGXPool.Begin(ctx)
}

// ExecContext executes a query without returning rows
func (d *Database) ExecContext(ctx context.Context, query string, args ...interface{}) error {
	_, err := d.PGXPool.Exec(ctx, query, args...)
	return err
}

// QueryContext executes a query that returns rows
func (d *Database) QueryContext(ctx context.Context, query string, args ...interface{}) (pgx.Rows, error) {
	return d.PGXPool.Query(ctx, query, args...)
}

// QueryRowContext executes a query that returns at most one row
func (d *Database) QueryRowContext(ctx context.Context, query string, args ...interface{}) pgx.Row {
	return d.PGXPool.QueryRow(ctx, query, args...)
}

// Transactional interface for PGX transactions
type Transactional interface {
	BeginTx(ctx context.Context) (pgx.Tx, error)
}

// WithTransaction executes a function within a database transaction
func WithTransaction(ctx context.Context, db Transactional, fn func(pgx.Tx) error) error {
	tx, err := db.BeginTx(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if p := recover(); p != nil {
			_ = tx.Rollback(ctx)
			panic(p)
		}
	}()

	if err := fn(tx); err != nil {
		if rbErr := tx.Rollback(ctx); rbErr != nil {
			return fmt.Errorf("transaction error: %v, rollback error: %v", err, rbErr)
		}
		return err
	}

	if err := tx.Commit(ctx); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// GetPool returns the underlying PGX pool for direct access
func (d *Database) GetPool() *pgxpool.Pool {
	return d.PGXPool
}

// Stats returns connection pool statistics
func (d *Database) Stats() *pgxpool.Stat {
	return d.PGXPool.Stat()
}

// Health checks the health of the database connection
func (d *Database) Health(ctx context.Context) error {
	return d.PGXPool.Ping(ctx)
}

// ===== BACKWARD COMPATIBILITY METHODS =====

// GetStdlibDB returns the stdlib database connection for backward compatibility
func (d *Database) GetStdlibDB() *sql.DB {
	return d.stdlibDB
}

// DB returns the stdlib database connection (alias for GetStdlibDB)
func (d *Database) DB() *sql.DB {
	return d.stdlibDB
}

// GetDB returns the stdlib database connection (another alias)
func (d *Database) GetDB() *sql.DB {
	return d.stdlibDB
}
