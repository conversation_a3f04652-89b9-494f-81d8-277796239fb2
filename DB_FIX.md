# PostgreSQL Schema Management Fix Documentation

## 🚨 Problem Analysis

### Current Issues
1. **Schema Path Not Working Correctly**: The `SET search_path` command only affects the current connection session. In connection pool environments (especially with PgBouncer), connections are reused and the schema path gets reset.

2. **Connection Pool Mode Conflict**: DigitalOcean uses PgBouncer with Transaction mode by default, where each transaction may use different connections, causing schema settings to be lost.

3. **Prepared Statement Conflicts**: Error `"FATAL: prepared statement name is already in use"` occurs when connections are reused without proper cleanup.

### Root Cause
The current implementation in `dbutil` package tries to set schema path per connection, but this doesn't work reliably with connection pooling systems like PgBouncer.

```go
// ❌ PROBLEMATIC CODE
func EnsureSchemaPath(ctx context.Context, pool *pgxpool.Pool) error {
    schemaName := os.Getenv("DATABASE_SCHEMA")
    if schemaName == "" || schemaName == "public" {
        return nil
    }
    
    // This only affects current connection/session
    _, err := pool.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
    return err
}
```

## 🛠️ Recommended Solution

### Primary Solution: Fully Qualified Table Names

**Why this approach:**
- ✅ Works with all connection pool modes
- ✅ No performance overhead
- ✅ Explicit and clear schema usage
- ✅ No connection state management needed

### Implementation Requirements

#### 1. Update Database Utility Functions

Replace the current schema path approach with fully qualified table names:

```go
// ✅ NEW APPROACH
func getFullTableName(tableName string) string {
    schemaName := os.Getenv("DATABASE_SCHEMA")
    if schemaName == "" || schemaName == "public" {
        return tableName
    }
    return fmt.Sprintf("%s.%s", schemaName, tableName)
}

// Updated utility functions
func ExecWithSchema(ctx context.Context, pool *pgxpool.Pool, query string, args ...interface{}) (pgconn.CommandTag, error) {
    // Process query to add schema prefix to table names
    processedQuery := processQueryWithSchema(query)
    return pool.Exec(ctx, processedQuery, args...)
}
```

#### 2. Query Processing Function

Create a function to automatically add schema prefixes to table names in SQL queries:

```go
func processQueryWithSchema(query string) string {
    schemaName := os.Getenv("DATABASE_SCHEMA")
    if schemaName == "" || schemaName == "public" {
        return query
    }
    
    // Add logic to identify table names and prefix them with schema
    // This can be a simple string replacement or more sophisticated parsing
    return addSchemaToQuery(query, schemaName)
}
```

#### 3. Alternative: Helper Function for Table Names

```go
func TableName(name string) string {
    schema := os.Getenv("DATABASE_SCHEMA")
    if schema == "" || schema == "public" {
        return name
    }
    return fmt.Sprintf("%s.%s", schema, name)
}

// Usage in queries:
// SELECT * FROM system_settings WHERE key = $1
// becomes:
// SELECT * FROM " + TableName("system_settings") + " WHERE key = $1
```

### Code Changes Required

#### Files to Update:
1. **`dbutil` package**: Remove schema path setting functions, add table name helpers
2. **All repository/service files**: Update SQL queries to use fully qualified table names
3. **Database queries**: Replace table names with schema-prefixed versions

#### Migration Steps:

1. **Phase 1**: Update `dbutil` package
    - Remove `EnsureSchemaPath`, `WithSchemaPath` functions
    - Add `TableName()` helper function
    - Update `ExecWithSchema`, `QueryWithSchema`, `QueryRowWithSchema` functions

2. **Phase 2**: Update all SQL queries
    - Find all raw SQL queries in the codebase
    - Replace table names with `TableName("table_name")` calls
    - Test each query change

3. **Phase 3**: Testing
    - Test with different schema configurations
    - Verify connection pool behavior
    - Check performance impact

### Example Implementation

#### Before:
```go
func GetSystemSetting(ctx context.Context, pool *pgxpool.Pool, key string) (*SystemSetting, error) {
    query := "SELECT key, value, created_at, updated_at FROM system_settings WHERE key = $1"
    
    row := QueryRowWithSchema(ctx, pool, query, key)
    // ... rest of the code
}
```

#### After:
```go
func GetSystemSetting(ctx context.Context, pool *pgxpool.Pool, key string) (*SystemSetting, error) {
    query := fmt.Sprintf("SELECT key, value, created_at, updated_at FROM %s WHERE key = $1", 
        TableName("system_settings"))
    
    row := pool.QueryRow(ctx, query, key)
    // ... rest of the code
}
```

### Alternative Solutions (If Primary Solution Not Suitable)

#### Option 2: Connection String Configuration
```env
DATABASE_OPTIONS=options=-csearch_path=sbobet789
```

#### Option 3: Transaction-Scoped Schema Setting
```go
func ExecWithSchemaTransaction(ctx context.Context, pool *pgxpool.Pool, query string, args ...interface{}) (pgconn.CommandTag, error) {
    tx, err := pool.Begin(ctx)
    if err != nil {
        return pgconn.CommandTag{}, err
    }
    defer tx.Rollback(ctx)

    schemaName := os.Getenv("DATABASE_SCHEMA")
    if schemaName != "" && schemaName != "public" {
        _, err = tx.Exec(ctx, fmt.Sprintf("SET LOCAL search_path TO %s", schemaName))
        if err != nil {
            return pgconn.CommandTag{}, err
        }
    }

    result, err := tx.Exec(ctx, query, args...)
    if err != nil {
        return pgconn.CommandTag{}, err
    }

    return result, tx.Commit(ctx)
}
```

## 🎯 Action Items for Claude Code

1. **Analyze Current Codebase**
    - Find all usages of the current `dbutil` schema functions
    - Identify all SQL queries that reference table names
    - List all files that need updates

2. **Implement Primary Solution**
    - Create `TableName()` helper function
    - Update `dbutil` package to remove schema path functions
    - Replace all table name references with fully qualified names

3. **Update Environment Configuration**
    - Ensure `DATABASE_SCHEMA` environment variable is properly configured
    - Update connection configuration if needed

4. **Testing Strategy**
    - Create test cases for different schema configurations
    - Verify connection pool behavior doesn't cause issues
    - Test error scenarios

## 📋 Checklist

- [ ] Remove `EnsureSchemaPath` function
- [ ] Remove `WithSchemaPath` function
- [ ] Add `TableName()` helper function
- [ ] Update all `ExecWithSchema` calls
- [ ] Update all `QueryWithSchema` calls
- [ ] Update all `QueryRowWithSchema` calls
- [ ] Find and update all raw SQL queries
- [ ] Test with actual DigitalOcean PostgreSQL cluster
- [ ] Verify no table/relation not found errors
- [ ] Check connection pool performance

## 🔍 Files Likely to Need Updates

Based on the error logs, these areas probably need attention:
- System settings queries (`system_settings` table)
- Member authentication related queries
- Banking/financial data queries
- Any repository files with direct SQL queries

## ⚠️ Important Notes

1. **Schema Name Validation**: Ensure the schema name from environment variables is properly escaped to prevent SQL injection
2. **Case Sensitivity**: PostgreSQL schema and table names are case-sensitive when quoted
3. **Connection Pool Settings**: Current settings show `DATABASE_MAX_OPEN_CONNS=25` and `DATABASE_MAX_IDLE_CONNS=25` which should work fine with the new approach
4. **SSL Mode**: Currently set to `disable` but DigitalOcean typically requires SSL in production

This documentation should provide Claude Code with enough context to implement the necessary fixes for the PostgreSQL schema management issues.