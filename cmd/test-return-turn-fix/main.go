package main

import (
	"context"
	"fmt"
	"log"

	"blacking-api/infrastructure/database"
	"blacking-api/internal/config"
	"blacking-api/internal/domain/return_turn"
	repository_postgres "blacking-api/internal/repository/postgres"
	"blacking-api/pkg/logger"
)

func main() {
	log.Println("Testing return turn repository fix...")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logger
	loggerInstance := logger.New(logger.Config{
		Level:  cfg.Log.Level,
		Format: cfg.Log.Format,
	})

	// Initialize database connection
	db, err := database.New(cfg, loggerInstance)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Initialize repository
	repo := repository_postgres.NewReturnTurnRepository(db.PGXPool, loggerInstance)

	// Test the method that was fixed
	req := return_turn.ReturnTurnHistoryListRequest{
		Page:     1,
		Limit:    10,
		MemberID: func() *int64 { id := int64(33); return &id }(),
		StatusID: func() *int64 { id := int64(0); return &id }(), // This was the problematic value
	}

	ctx := context.Background()
	logs, total, err := repo.GetReturnHistoryLogList(ctx, req)

	if err != nil {
		log.Printf("Error: %v", err)
		return
	}

	fmt.Printf("✅ Success! Repository method returned %d records out of %d total\n", len(logs), total)

	if total > 0 && len(logs) > 0 {
		log := logs[0]
		fmt.Printf("First record: ID=%d, MemberCode=%s, Status=%d\n", log.ID, log.MemberCode, log.StatusID)
		fmt.Printf("✅ Fix verified: statusId=0 no longer filters out records\n")
	} else if total > 0 && len(logs) == 0 {
		fmt.Printf("❌ Issue: Total shows %d but no records returned - scanning issue still exists\n", total)
	} else {
		fmt.Printf("ℹ️ No records found for member ID 33\n")
	}
}
