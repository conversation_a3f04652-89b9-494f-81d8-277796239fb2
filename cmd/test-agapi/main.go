package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"blacking-api/internal/config"
	"blacking-api/pkg/agapi"
	"blacking-api/pkg/logger"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logger
	lgr := logger.New(logger.Config{
		Level:  "debug",
		Format: "text",
	})

	// Test AG API connectivity
	testURL := fmt.Sprintf("%s/health", cfg.Agent.BaseURL)

	lgr.Infof("Testing AG API connectivity to: %s", testURL)
	lgr.Infof("LineCode: %s", cfg.Agent.LineCode)
	lgr.Infof("SecretKey: %s", maskS<PERSON>ret(cfg.Agent.SecretKey))

	// Simple HTTP GET test
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Get(testURL)
	if err != nil {
		lgr.<PERSON><PERSON><PERSON>("Failed to connect to AG API: %v", err)
	} else {
		defer resp.Body.Close()
		lgr.Infof("AG API responded with status: %d", resp.StatusCode)
	}

	// Test AG API client
	agClient := agapi.NewClient(cfg, lgr)

	// Test member registration
	testReq := agapi.MemberRegisterRequest{
		Username:  "testuser001",
		Email:     "<EMAIL>",
		FirstName: "Test",
		LastName:  "User",
		Phone:     "**********",
		Password:  "testPassword123",
	}

	ctx := context.Background()
	lgr.Info("Testing member registration...")

	_, err = agClient.RegisterMember(ctx, testReq)
	if err != nil {
		lgr.Errorf("Member registration failed: %v", err)
	} else {
		lgr.Info("Member registration succeeded")
	}
}

func maskSecret(secret string) string {
	if len(secret) <= 8 {
		return "***"
	}
	return secret[:4] + "****" + secret[len(secret)-4:]
}
