package main

import (
	"log"
	"os"
	"os/signal"
	"time"

	"blacking-api/internal/config"
	"blacking-api/pkg/websocket"
)

func main() {
	// Load configuration to get WebSocket URL
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	if cfg.WebSocket.URL == "" {
		log.Fatal("WEBSOCKET_URL not configured")
	}

	log.Printf("Connecting to WebSocket server: %s", cfg.WebSocket.URL)

	// Create WebSocket client
	client := websocket.NewWSClient(cfg.WebSocket.URL)

	// Set up event handlers
	client.OnConnect(func() {
		log.Println("✅ Connected to Socket.IO server!")
	})

	client.OnDisconnect(func(err error) {
		if err != nil {
			log.Printf("❌ Disconnected with error: %v", err)
		} else {
			log.Println("📴 Disconnected from server")
		}
	})

	client.OnMessage(func(message []byte) {
		log.Printf("📨 Received: %s", string(message))
	})

	client.OnError(func(err error) {
		log.Printf("🚨 Error: %v", err)
	})

	// Connect to server
	if err := client.Connect(); err != nil {
		log.Fatal("Connection failed:", err)
	}

	// Test emission of events
	go func() {
		// Wait for connection to stabilize
		time.Sleep(2 * time.Second)

		channelID := 33

		// Test join event
		log.Printf("🚪 Testing join event (channel_id: %d)", channelID)
		if err := client.EmitJoin(channelID); err != nil {
			log.Printf("Error emitting join: %v", err)
		} else {
			log.Println("✅ Join event sent successfully")
		}

		time.Sleep(2 * time.Second)

		// Test deposit event
		log.Printf("💰 Testing deposit event (channel_id: %d, status: true)", channelID)
		if err := client.EmitDeposit(channelID, true); err != nil {
			log.Printf("Error emitting deposit: %v", err)
		} else {
			log.Println("✅ Deposit event sent successfully")
		}

		time.Sleep(2 * time.Second)

		// Test withdraw event
		log.Printf("💸 Testing withdraw event (channel_id: %d, status: true)", channelID)
		if err := client.EmitWithdraw(channelID, true); err != nil {
			log.Printf("Error emitting withdraw: %v", err)
		} else {
			log.Println("✅ Withdraw event sent successfully")
		}

		time.Sleep(2 * time.Second)

		// Test leave event
		log.Printf("🚪 Testing leave event (channel_id: %d)", channelID)
		if err := client.EmitLeave(channelID); err != nil {
			log.Printf("Error emitting leave: %v", err)
		} else {
			log.Println("✅ Leave event sent successfully")
		}

		// Test with different channel and status
		time.Sleep(3 * time.Second)

		otherChannelID := 456
		log.Printf("🔄 Testing with different channel_id: %d", otherChannelID)

		if err := client.EmitJoin(otherChannelID); err != nil {
			log.Printf("Error emitting join: %v", err)
		} else {
			log.Println("✅ Join event sent for channel 456")
		}

		time.Sleep(1 * time.Second)

		// Test deposit with false status
		if err := client.EmitDeposit(otherChannelID, false); err != nil {
			log.Printf("Error emitting deposit: %v", err)
		} else {
			log.Println("✅ Deposit event (status: false) sent successfully")
		}

		time.Sleep(1 * time.Second)

		if err := client.EmitLeave(otherChannelID); err != nil {
			log.Printf("Error emitting leave: %v", err)
		} else {
			log.Println("✅ Leave event sent for channel 456")
		}
	}()

	// Connection health check
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				if client.IsConnected() {
					log.Println("💚 Connection health check: OK")
				} else {
					log.Println("💔 Connection health check: FAILED")
				}
			}
		}
	}()

	// Wait for interrupt signal
	interrupt := make(chan os.Signal, 1)
	signal.Notify(interrupt, os.Interrupt)

	log.Println("🔄 WebSocket client is running. Press Ctrl+C to exit...")
	<-interrupt
	log.Println("🛑 Shutting down WebSocket client...")
	client.Close()
	log.Println("👋 Goodbye!")
}
