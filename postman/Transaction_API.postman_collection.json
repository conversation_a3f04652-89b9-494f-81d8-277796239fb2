{"info": {"_postman_id": "transaction-api-collection-v1", "name": "Transaction API", "description": "Blacking API - Transaction Management System\n\nThis collection contains all transaction-related endpoints including:\n- File upload for transaction slips\n- Deposit transactions\n- Withdraw transactions\n- Transfer transactions\n- Transaction status management\n- Member-specific transactions\n\n## Status Codes\n\n### Transaction Status (status_id)\n- 1: Website Deposit (ฝากเว็บ)\n- 2: Waiting for Transfer (รอโอน)\n- 3: Waiting for Verification (รอตรวจสอบ)\n- 4: Slip Pending (สลิปรอดำเนินการ)\n- 5: In Progress (กำลังดำเนินการ)\n- 6: Over Limit (เกินวงเงิน)\n- 7: VIP Approved (อนุมัติ VIP)\n- 8: Waiting for Matching (รอจับคู่)\n- 9: In Queue (รอคิว)\n- 10: Duplicate (ทำซ้ำ)\n- 11: Success (สำเร็จ)\n- 12: Cancelled (ยกเลิก)\n\n### Transaction Type (type_id)\n- 1: Deposit (ฝาก)\n- 2: Withdraw (ถอน)\n- 3: Bonus (โบนัส)\n- 4: Promotion Return Loss (คืนยอดเสีย)\n- 5: Affiliate Income (รายได้แนะนำเพื่อน)\n- 6: Alliance Income (รายได้พันธมิตร)\n- 7: Take Credit Back (ดึงเครดิตกลับ)\n- 8: Daily Activity Bonus (โบนัสกิจกรรมรายวัน)\n- 9: Lucky Wheel (กงล้อนำโชค)\n- 10: Web Promotion (โปรโมชั่นเว็บ)\n- 11: Cash Coupon (คูปองเงินสด)\n- 12: Lottery Credit (เครดิตลอตเตอรี่)\n- 13: Return Commission (คืนยอด commission)\n- 14: Cancel Credit (ยกเลิกเติมเครดิต)\n\n### Direction (direction_id)\n- 1: Deposit (ฝาก)\n- 2: Withdraw (ถอน)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Common - Admin/Member", "item": [{"name": "Upload Transaction Slip", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text", "description": "Admin or Member JWT token"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "description": "Transaction slip image file (JPEG, JPG, PNG, GIF, WEBP)"}]}, "url": {"raw": "{{base_url}}/api/v1/transaction/upload-slip", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "upload-slip"]}, "description": "Upload a transaction slip image to AWS S3.\n\n**Authentication**: Admin OR Member\n\n**Allowed file types**: JPEG, JPG, PNG, GIF, WEBP\n\n**Response**:\n```json\n{\n    \"success\": true,\n    \"message\": \"Slip uploaded successfully\",\n    \"data\": \"https://s3.amazonaws.com/bucket/backoffice/transaction-slips/**********-slip.jpg\"\n}\n```"}, "response": [{"name": "Success", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "slip.jpg"}]}, "url": {"raw": "{{base_url}}/api/v1/transaction/upload-slip", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "upload-slip"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Slip uploaded successfully\",\n    \"data\": \"https://s3.amazonaws.com/bucket/backoffice/transaction-slips/**********-slip.jpg\"\n}"}]}]}, {"name": "Admin - Transactions", "item": [{"name": "Create", "item": [{"name": "C<PERSON> De<PERSON>sit", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"phoneOrUsername\": \"username123\",\n    \"amount\": 1000.00,\n    \"depositAccountId\": 1,\n    \"date\": \"2025-01-23\",\n    \"time\": \"14:30:00\",\n    \"promotionId\": null,\n    \"description\": \"Deposit via bank transfer\",\n    \"slipUrl\": \"https://s3.amazonaws.com/bucket/backoffice/transaction-slips/slip.jpg\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/transaction/admin/deposit", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "admin", "deposit"]}, "description": "Create a new deposit transaction.\n\n**Authentication**: Admin only"}, "response": []}, {"name": "Create Withdraw", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"phoneOrUsername\": \"username123\",\n    \"credit_amount\": 500.00,\n    \"description\": \"Withdraw request\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/transaction/admin/withdraw", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "admin", "withdraw"]}, "description": "Create a new withdraw transaction.\n\n**Authentication**: Admin only"}, "response": []}, {"name": "Create Transfer", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"banking_id\": 1,\n    \"transfer_banking_id\": 2,\n    \"credit_amount\": 250.00,\n    \"description\": \"Transfer between accounts\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/transaction/admin/transfer", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "admin", "transfer"]}, "description": "Create a new transfer transaction between members.\n\n**Authentication**: Admin only"}, "response": []}]}, {"name": "List", "item": [{"name": "Get Deposit Page", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/transaction/admin/deposit/page?page=1&limit=10&status=pending", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "admin", "deposit", "page"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "10", "description": "Items per page"}, {"key": "status", "value": "pending", "description": "Filter by status (pending, approved, rejected)"}, {"key": "startDate", "value": "2025-01-01", "disabled": true, "description": "Start date (YYYY-MM-DD)"}, {"key": "endDate", "value": "2025-01-31", "disabled": true, "description": "End date (YYYY-MM-DD)"}]}, "description": "Get paginated list of deposit transactions.\n\n**Authentication**: Admin only"}, "response": []}, {"name": "Get Withdraw Page", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/transaction/admin/withdraw/page?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "admin", "withdraw", "page"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get paginated list of withdraw transactions.\n\n**Authentication**: Admin only"}, "response": []}, {"name": "Get Transfer Page", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/transaction/admin/transfer/page?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "admin", "transfer", "page"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get paginated list of transfer transactions.\n\n**Authentication**: Admin only"}, "response": []}]}, {"name": "Get By ID", "item": [{"name": "Get Deposit By ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/transaction/admin/deposit/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "admin", "deposit", ":id"], "variable": [{"key": "id", "value": "1", "description": "Deposit transaction ID"}]}, "description": "Get specific deposit transaction by ID.\n\n**Authentication**: Admin only"}, "response": []}, {"name": "Get Withdraw By ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/transaction/admin/withdraw/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "admin", "withdraw", ":id"], "variable": [{"key": "id", "value": "1", "description": "Withdraw transaction ID"}]}, "description": "Get specific withdraw transaction by ID.\n\n**Authentication**: Admin only"}, "response": []}, {"name": "Get Transfer By ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/transaction/admin/transfer/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "admin", "transfer", ":id"], "variable": [{"key": "id", "value": "1", "description": "Transfer transaction ID"}]}, "description": "Get specific transfer transaction by ID.\n\n**Authentication**: Admin only"}, "response": []}]}, {"name": "Member Transactions", "item": [{"name": "Get Member Deposits", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/transaction/admin/deposit/member/:memberId/page?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "admin", "deposit", "member", ":memberId", "page"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}], "variable": [{"key": "memberId", "value": "123", "description": "Member ID"}]}, "description": "Get deposit transactions for specific member.\n\n**Authentication**: Admin only"}, "response": []}, {"name": "Get Member Withdraws", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/transaction/admin/withdraw/member/:memberId/page?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "admin", "withdraw", "member", ":memberId", "page"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}], "variable": [{"key": "memberId", "value": "123", "description": "Member ID"}]}, "description": "Get withdraw transactions for specific member.\n\n**Authentication**: Admin only"}, "response": []}]}, {"name": "Update Status", "item": [{"name": "Update Deposit Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"status_id\": 11,\n    \"confirm_admin_id\": 1,\n    \"detail\": \"Verified and approved\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/transaction/admin/deposit/status/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "admin", "deposit", "status", ":id"], "variable": [{"key": "id", "value": "1", "description": "Deposit transaction ID"}]}, "description": "Update deposit transaction status.\n\n**Authentication**: Admin only\n\n**Status values**: pending, approved, rejected"}, "response": []}, {"name": "Update Withdraw Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"status_id\": 11,\n    \"confirm_admin_id\": 1,\n    \"detail\": \"Processed successfully\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/transaction/admin/withdraw/status/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "admin", "withdraw", "status", ":id"], "variable": [{"key": "id", "value": "1", "description": "Withdraw transaction ID"}]}, "description": "Update withdraw transaction status.\n\n**Authentication**: Admin only\n\n**Status values**: pending, approved, rejected"}, "response": []}, {"name": "Update Transfer Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"status_id\": 11,\n    \"confirm_admin_id\": 1,\n    \"detail\": \"Transfer completed\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/transaction/admin/transfer/status/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "admin", "transfer", "status", ":id"], "variable": [{"key": "id", "value": "1", "description": "Transfer transaction ID"}]}, "description": "Update transfer transaction status.\n\n**Authentication**: Admin only\n\n**Status values**: pending, completed, cancelled"}, "response": []}]}]}, {"name": "Member - Transactions", "item": [{"name": "Create Web Deposit", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{member_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"amount\": 1000.00,\n    \"depositAccountId\": 1,\n    \"date\": \"2025-01-23\",\n    \"time\": \"14:30:00\",\n    \"promotionId\": null,\n    \"description\": \"Web deposit\",\n    \"slipUrl\": \"https://s3.amazonaws.com/bucket/backoffice/transaction-slips/slip.jpg\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/transaction/member/web/deposit", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "member", "web", "deposit"]}, "description": "Create a web deposit transaction for the authenticated member.\n\n**Authentication**: Member only"}, "response": []}, {"name": "Create Web Withdraw", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{member_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"amount\": 500.00,\n    \"withdrawBankId\": 1,\n    \"accountNumber\": \"**********\",\n    \"accountName\": \"<PERSON>\",\n    \"description\": \"Web withdraw request\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/transaction/member/web/withdraw", "host": ["{{base_url}}"], "path": ["api", "v1", "transaction", "member", "web", "withdraw"]}, "description": "Create a web withdraw transaction for the authenticated member.\n\n**Authentication**: Member only"}, "response": []}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Pre-request Script", "// You can add any pre-request logic here"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// <PERSON>", "// Common test for all requests", "pm.test(\"Status code is successful\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test(\"Response has success field\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "});"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "admin_token", "value": "", "type": "string"}, {"key": "member_token", "value": "", "type": "string"}, {"key": "token", "value": "", "type": "string", "description": "Generic token for Admin OR Member endpoints"}]}