{"id": "transaction-api-env", "name": "Transaction API Environment", "values": [{"key": "base_url", "value": "http://localhost:8080", "enabled": true, "type": "default", "description": "Base URL for the API server"}, {"key": "admin_token", "value": "", "enabled": true, "type": "secret", "description": "JWT token for admin authentication"}, {"key": "member_token", "value": "", "enabled": true, "type": "secret", "description": "JWT token for member authentication"}, {"key": "token", "value": "", "enabled": true, "type": "secret", "description": "Generic token for endpoints that accept both admin and member"}, {"key": "admin_username", "value": "admin", "enabled": true, "type": "default", "description": "Admin username for login"}, {"key": "admin_password", "value": "password", "enabled": true, "type": "secret", "description": "Admin password for login"}, {"key": "member_username", "value": "member123", "enabled": true, "type": "default", "description": "Member username for login"}, {"key": "member_password", "value": "password", "enabled": true, "type": "secret", "description": "Member password for login"}, {"key": "test_member_id", "value": "123", "enabled": true, "type": "default", "description": "Test member ID for transactions"}, {"key": "test_deposit_id", "value": "1", "enabled": true, "type": "default", "description": "Test deposit transaction ID"}, {"key": "test_withdraw_id", "value": "1", "enabled": true, "type": "default", "description": "Test withdraw transaction ID"}, {"key": "test_transfer_id", "value": "1", "enabled": true, "type": "default", "description": "Test transfer transaction ID"}, {"key": "s3_bucket_url", "value": "https://s3.amazonaws.com/your-bucket", "enabled": true, "type": "default", "description": "S3 bucket URL for file uploads"}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-08-23T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}