{"info": {"_postman_id": "return-turn-api-collection", "name": "Return Turn API", "description": "API Collection for Return Turn (คืนยอดเทิร์น) feature - manages return/cashback for losing players", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "item": [{"name": "Admin APIs", "item": [{"name": "Settings", "item": [{"name": "Get Return Setting", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/admin/return-turn/setting", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "return-turn", "setting"]}, "description": "Get current return turn settings configuration"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/admin/return-turn/setting", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "return-turn", "setting"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 1,\n        \"returnPercent\": 5.00,\n        \"returnTypeId\": 1,\n        \"cutTypeId\": 1,\n        \"minLossPrice\": 500.00,\n        \"maxReturnPrice\": 1000.00,\n        \"detail\": \"<p>คืนยอดเทิร์น</p>\",\n        \"isEnabled\": true,\n        \"calculateTypes\": [1, 2, 3],\n        \"createdAt\": \"2025-08-26T00:00:00Z\",\n        \"updatedAt\": \"2025-08-26T00:00:00Z\"\n    }\n}"}]}, {"name": "Update Return Setting", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{admin_jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"returnPercent\": 5.00,\n    \"returnTypeId\": 1,\n    \"cutTypeId\": 1,\n    \"minLossPrice\": 500.00,\n    \"maxReturnPrice\": 1000.00,\n    \"detail\": \"<p>คืนยอดเทิร์น 5%</p>\",\n    \"isEnabled\": true,\n    \"calculateTypes\": [1, 2, 3]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/admin/return-turn/setting", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "return-turn", "setting"]}, "description": "Update return turn settings. All fields are optional."}, "response": [{"name": "Success Response", "originalRequest": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n    \"returnPercent\": 5.00,\n    \"isEnabled\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/admin/return-turn/setting", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "return-turn", "setting"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Return setting updated successfully\"\n}"}]}]}, {"name": "History Reports", "item": [{"name": "Get Return History Member List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/admin/return-turn/history/member-list?page=1&limit=10&dateType=all", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "return-turn", "history", "member-list"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "10", "description": "Items per page"}, {"key": "dateType", "value": "all", "description": "Date filter: all, today, yesterday, last_week, this_month, last_month"}, {"key": "fromDate", "value": "2024-01-01", "description": "Start date (YYYY-MM-DD)", "disabled": true}, {"key": "toDate", "value": "2024-12-31", "description": "End date (YYYY-MM-DD)", "disabled": true}, {"key": "search", "value": "", "description": "Search by member code, username, or fullname", "disabled": true}]}, "description": "Get list of members with return history"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/admin/return-turn/history/member-list?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "return-turn", "history", "member-list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": [\n        {\n            \"id\": 123,\n            \"memberCode\": \"MB001\",\n            \"username\": \"testuser\",\n            \"fullname\": \"Test User\",\n            \"totalLossAmount\": 5000.00,\n            \"totalTakenPrice\": 250.00\n        }\n    ],\n    \"pagination\": {\n        \"page\": 1,\n        \"limit\": 10,\n        \"total\": 100,\n        \"totalPages\": 10\n    }\n}"}]}, {"name": "Get Return History Member Su<PERSON>ry", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/admin/return-turn/history/member-summary?dateType=all", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "return-turn", "history", "member-summary"], "query": [{"key": "dateType", "value": "all", "description": "Date filter: all, today, yesterday, last_week, this_month, last_month"}, {"key": "fromDate", "value": "2024-01-01", "disabled": true}, {"key": "toDate", "value": "2024-12-31", "disabled": true}]}, "description": "Get summary statistics for return history"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/admin/return-turn/history/member-summary", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "return-turn", "history", "member-summary"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"dateType\": \"all\",\n        \"fromDate\": \"\",\n        \"toDate\": \"\",\n        \"totalLossAmount\": 50000.00,\n        \"totalTakenPrice\": 2500.00\n    }\n}"}]}, {"name": "Get Return History Log List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/admin/return-turn/history/log-list?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "return-turn", "history", "log-list"], "query": [{"key": "memberId", "value": "123", "description": "Filter by member ID", "disabled": true}, {"key": "fromDate", "value": "2024-01-01", "disabled": true}, {"key": "toDate", "value": "2024-12-31", "disabled": true}, {"key": "statusId", "value": "2", "description": "1=PENDING, 2=READY, 3=TAKEN, 4=EXPIRED", "disabled": true}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get detailed log list of all return transactions"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/admin/return-turn/history/log-list", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "return-turn", "history", "log-list"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": [\n        {\n            \"id\": 1,\n            \"userId\": 123,\n            \"username\": \"testuser\",\n            \"fullname\": \"Test User\",\n            \"statusId\": 2,\n            \"statusName\": \"READY\",\n            \"dailyKey\": \"123_2024-01-15\",\n            \"ofDate\": \"2024-01-15\",\n            \"totalLossAmount\": 1000.00,\n            \"totalLossLiveCasino\": 400.00,\n            \"totalLossSlot\": 300.00,\n            \"totalLossSport\": 300.00,\n            \"returnPercent\": 5.00,\n            \"returnPrice\": 50.00,\n            \"creditExpireAt\": \"2024-01-22 00:00:00\",\n            \"logStatus\": \"READY\",\n            \"createdAt\": \"2024-01-15T10:00:00Z\",\n            \"updatedAt\": \"2024-01-15T10:00:00Z\"\n        }\n    ],\n    \"pagination\": {\n        \"page\": 1,\n        \"limit\": 10,\n        \"total\": 100,\n        \"totalPages\": 10\n    }\n}"}]}]}]}, {"name": "Web/Member APIs", "item": [{"name": "Get Member Current Return Detail", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{member_jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/web/return-turn-loser/current", "host": ["{{base_url}}"], "path": ["api", "v1", "web", "return-turn-loser", "current"]}, "description": "Get current member's available return detail and status"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/web/return-turn-loser/current", "host": ["{{base_url}}"], "path": ["api", "v1", "web", "return-turn-loser", "current"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"userId\": 123,\n        \"statusId\": 2,\n        \"statusName\": \"READY\",\n        \"returnPercent\": 5.00,\n        \"returnPrice\": 250.00,\n        \"detail\": \"<p>คืนยอดเทิร์น</p>\",\n        \"relatedItemList\": [\n            {\n                \"id\": 1,\n                \"userId\": 123,\n                \"username\": \"testuser\",\n                \"statusId\": 2,\n                \"statusName\": \"READY\",\n                \"dailyKey\": \"123_2024-01-15\",\n                \"ofDate\": \"2024-01-15\",\n                \"totalLossAmount\": 5000.00,\n                \"returnPercent\": 5.00,\n                \"returnPrice\": 250.00,\n                \"creditExpireAt\": \"2024-01-22 00:00:00\",\n                \"logStatus\": \"READY\",\n                \"createdAt\": \"2024-01-15T10:00:00Z\"\n            }\n        ]\n    }\n}"}, {"name": "No Return Available", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/web/return-turn-loser/current", "host": ["{{base_url}}"], "path": ["api", "v1", "web", "return-turn-loser", "current"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"userId\": 123,\n        \"statusId\": 1,\n        \"statusName\": \"PENDING\",\n        \"returnPercent\": 0,\n        \"returnPrice\": 0,\n        \"detail\": \"\",\n        \"relatedItemList\": []\n    }\n}"}]}, {"name": "Take Member Return Amount", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{member_jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/web/return-turn-loser/take", "host": ["{{base_url}}"], "path": ["api", "v1", "web", "return-turn-loser", "take"]}, "description": "Take/claim available return amount (adds to member's credit balance)"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/web/return-turn-loser/take", "host": ["{{base_url}}"], "path": ["api", "v1", "web", "return-turn-loser", "take"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Return amount taken successfully\",\n    \"data\": {\n        \"takenAmount\": 250.00,\n        \"newBalance\": 1250.00\n    }\n}"}, {"name": "No Return Available", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/web/return-turn-loser/take", "host": ["{{base_url}}"], "path": ["api", "v1", "web", "return-turn-loser", "take"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"error\": \"No return available to take\"\n}"}]}, {"name": "Get Member Return History List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{member_jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/web/return-turn-loser/list?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "web", "return-turn-loser", "list"], "query": [{"key": "statusId", "value": "3", "description": "Filter by status: 1=PENDING, 2=READY, 3=TAKEN, 4=EXPIRED", "disabled": true}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get member's return transaction history"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/web/return-turn-loser/list?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "web", "return-turn-loser", "list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": [\n        {\n            \"id\": 1,\n            \"userId\": 123,\n            \"username\": \"testuser\",\n            \"statusId\": 3,\n            \"statusName\": \"TAKEN\",\n            \"dailyKey\": \"123_2024-01-15\",\n            \"ofDate\": \"2024-01-15\",\n            \"totalLossAmount\": 5000.00,\n            \"totalLossLiveCasino\": 2000.00,\n            \"totalLossSlot\": 1500.00,\n            \"totalLossSport\": 1500.00,\n            \"returnPercent\": 5.00,\n            \"returnPrice\": 250.00,\n            \"takenPrice\": 250.00,\n            \"takeAt\": \"2024-01-16T09:00:00Z\",\n            \"logStatus\": \"TAKEN\",\n            \"createdAt\": \"2024-01-15T10:00:00Z\",\n            \"updatedAt\": \"2024-01-16T09:00:00Z\"\n        }\n    ],\n    \"pagination\": {\n        \"page\": 1,\n        \"limit\": 10,\n        \"total\": 50,\n        \"totalPages\": 5\n    }\n}"}]}]}, {"name": "Cron APIs", "item": [{"name": "Cron Cut Return Loss Daily", "request": {"method": "GET", "header": [{"key": "X-Cron-Secret", "value": "{{cron_secret}}", "type": "text", "description": "Cron authentication secret"}], "url": {"raw": "{{base_url}}/api/v1/cron/return-turn-loser/cut-daily", "host": ["{{base_url}}"], "path": ["api", "v1", "cron", "return-turn-loser", "cut-daily"]}, "description": "Calculate and create return transactions for yesterday's losses. Should be run daily after midnight."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/cron/return-turn-loser/cut-daily", "host": ["{{base_url}}"], "path": ["api", "v1", "cron", "return-turn-loser", "cut-daily"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Daily return loss calculation completed\",\n    \"data\": {\n        \"processedDate\": \"2024-01-14\",\n        \"membersProcessed\": 25,\n        \"totalReturnAmount\": 1250.00\n    }\n}"}]}, {"name": "Cron Cut Return Loss By Date", "request": {"method": "GET", "header": [{"key": "X-Cron-Secret", "value": "{{cron_secret}}", "type": "text", "description": "Cron authentication secret"}], "url": {"raw": "{{base_url}}/api/v1/cron/return-turn-loser/cut-date?date=2024-01-15", "host": ["{{base_url}}"], "path": ["api", "v1", "cron", "return-turn-loser", "cut-date"], "query": [{"key": "date", "value": "2024-01-15", "description": "Date to calculate returns for (YYYY-MM-DD)"}]}, "description": "Calculate and create return transactions for a specific date. Used for manual recalculation or fixing missed days."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/cron/return-turn-loser/cut-date?date=2024-01-15", "host": ["{{base_url}}"], "path": ["api", "v1", "cron", "return-turn-loser", "cut-date"], "query": [{"key": "date", "value": "2024-01-15"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Return loss calculation completed for date\",\n    \"data\": {\n        \"processedDate\": \"2024-01-15\",\n        \"membersProcessed\": 30,\n        \"totalReturnAmount\": 1500.00\n    }\n}"}, {"name": "Invalid Date", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/cron/return-turn-loser/cut-date?date=invalid", "host": ["{{base_url}}"], "path": ["api", "v1", "cron", "return-turn-loser", "cut-date"], "query": [{"key": "date", "value": "invalid"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"error\": \"Invalid date format. Use YYYY-MM-DD\"\n}"}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "admin_jwt_token", "value": "", "type": "string"}, {"key": "member_jwt_token", "value": "", "type": "string"}, {"key": "cron_secret", "value": "", "type": "string"}]}