package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"blacking-api/internal/domain/promotion_web"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/internal/repository/postgres"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(".env"); err != nil {
		log.Printf("Warning: .env file not found: %v", err)
	}

	// Initialize logger
	loggerInstance := logger.New(logger.Config{
		Level:  "info",
		Format: "json",
	})

	// Build connection string
	host := os.Getenv("DATABASE_HOST")
	port := os.Getenv("DATABASE_PORT")
	user := os.Getenv("DATABASE_USERNAME")
	password := os.Getenv("DATABASE_PASSWORD")
	dbname := os.Getenv("DATABASE_DBNAME")
	sslmode := os.Getenv("DATABASE_SSLMODE")

	connString := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		host, port, user, password, dbname, sslmode)

	// Create pool config
	config, err := pgxpool.ParseConfig(connString)
	if err != nil {
		log.Fatalf("Failed to parse config: %v", err)
	}

	// Create pool
	pool, err := pgxpool.New(context.Background(), config.ConnString())
	if err != nil {
		log.Fatalf("Failed to create pool: %v", err)
	}
	defer pool.Close()

	// Test connection
	if err := pool.Ping(context.Background()); err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}

	// Create datetime repository (required dependency)
	var datetimeRepo interfaces.DateTimeRepository

	// Create promotion web repository
	promotionWebRepo := postgres.NewPromotionWebRepository(pool, loggerInstance, datetimeRepo)

	// Test GetPromotionWebList with pagination and status filter
	fmt.Println("Testing GetPromotionWebList with status filter 2...")

	statusId := int64(2)
	req := promotion_web.PromotionWebGetListRequest{
		Page:                 1,
		Limit:                10,
		Search:               "",
		PromotionWebStatusId: &statusId,
		StartDate:            "",
		EndDate:              "",
	}

	result, total, err := promotionWebRepo.GetPromotionWebList(context.Background(), req)
	if err != nil {
		log.Fatalf("Error: %v", err)
	}

	fmt.Printf("Success! Retrieved promotion web list:\n")
	fmt.Printf("- Total count: %d\n", total)
	fmt.Printf("- Results count: %d\n", len(result))

	for i, item := range result {
		fmt.Printf("- Item %d:\n", i+1)
		fmt.Printf("  - ID: %d\n", item.Id)
		fmt.Printf("  - Name: %s\n", item.Name)
		fmt.Printf("  - Type ID: %d\n", item.PromotionWebTypeId)
		fmt.Printf("  - Type TH: '%s'\n", item.PromotionWebTypeTh)
		fmt.Printf("  - Status ID: %d\n", item.PromotionWebStatusId)
		fmt.Printf("  - Status TH: '%s'\n", item.PromotionWebStatusTh)
		fmt.Printf("  - Created By Admin Name: '%s'\n", item.CreatedByAdminName)
		fmt.Printf("  - Updated By Admin Name: '%s'\n", item.UpdatedByAdminName)
		if item.CanceledByAdminName != nil {
			fmt.Printf("  - Canceled By Admin Name: '%s'\n", *item.CanceledByAdminName)
		} else {
			fmt.Printf("  - Canceled By Admin Name: <NULL>\n")
		}
		fmt.Printf("  - Date Type TH: '%s'\n", item.PromotionWebDateTypeTh)
		fmt.Println()
	}
}
